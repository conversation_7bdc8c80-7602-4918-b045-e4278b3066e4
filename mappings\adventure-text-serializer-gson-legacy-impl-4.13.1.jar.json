{"md5": "d52a43c888757b956861149d212976c8", "sha2": "ab7232cacee5cad54713b6ffea48fbafff4e5d0d", "sha256": "e9b7bcb548388b9ead4c4dc9936040654a2ec3f2489eb0bed5ba30db7f3a13dd", "contents": {"classes": {"net/kyori/adventure/text/serializer/gson/legacyimpl/NBTLegacyHoverEventSerializer.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/text/serializer/gson/legacyimpl/NBTLegacyHoverEventSerializer", "super": "java/lang/Object", "mthds": [{"nme": "get", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/serializer/gson/LegacyHoverEventSerializer;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": []}, "net/kyori/adventure/text/serializer/gson/legacyimpl/NBTLegacyHoverEventSerializerImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/serializer/gson/legacyimpl/NBTLegacyHoverEventSerializerImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "deserializeShowItem", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;)Lnet/kyori/adventure/text/event/HoverEvent$ShowItem;", "exs": ["java/io/IOException"]}, {"nme": "deserializeShowEntity", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;Lnet/kyori/adventure/util/Codec$Decoder;)Lnet/kyori/adventure/text/event/HoverEvent$ShowEntity;", "sig": "(Lnet/kyori/adventure/text/Component;Lnet/kyori/adventure/util/Codec$Decoder<Lnet/kyori/adventure/text/Component;Ljava/lang/String;+Ljava/lang/RuntimeException;>;)Lnet/kyori/adventure/text/event/HoverEvent$ShowEntity;", "exs": ["java/io/IOException"]}, {"nme": "assertTextComponent", "acc": 10, "dsc": "(Lnet/kyori/adventure/text/Component;)V"}, {"nme": "serializeShowItem", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/event/HoverEvent$ShowItem;)Lnet/kyori/adventure/text/Component;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "serializeShowEntity", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/event/HoverEvent$ShowEntity;Lnet/kyori/adventure/util/Codec$Encoder;)Lnet/kyori/adventure/text/Component;", "sig": "(Lnet/kyori/adventure/text/event/HoverEvent$ShowEntity;Lnet/kyori/adventure/util/Codec$Encoder<Lnet/kyori/adventure/text/Component;Ljava/lang/String;+Ljava/lang/RuntimeException;>;)Lnet/kyori/adventure/text/Component;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "INSTANCE", "dsc": "Lnet/kyori/adventure/text/serializer/gson/legacyimpl/NBTLegacyHoverEventSerializerImpl;"}, {"acc": 26, "nme": "SNBT_IO", "dsc": "Lnet/kyori/adventure/nbt/TagStringIO;"}, {"acc": 26, "nme": "SNBT_CODEC", "dsc": "Lnet/kyori/adventure/util/Codec;", "sig": "Lnet/kyori/adventure/util/Codec<Lnet/kyori/adventure/nbt/CompoundBinaryTag;Ljava/lang/String;Ljava/io/IOException;Ljava/io/IOException;>;"}, {"acc": 24, "nme": "ITEM_TYPE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "id"}, {"acc": 24, "nme": "ITEM_COUNT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "Count"}, {"acc": 24, "nme": "ITEM_TAG", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "tag"}, {"acc": 24, "nme": "ENTITY_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "name"}, {"acc": 24, "nme": "ENTITY_TYPE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "type"}, {"acc": 24, "nme": "ENTITY_ID", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "id"}]}}}}