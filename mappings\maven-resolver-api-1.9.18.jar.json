{"md5": "43a87208b9585a61f268843f6a0931ec", "sha2": "0cd5174d6e80175398debe4869d484169c0abbf8", "sha256": "ebfb9e1dfeea3c2017905184581e007874b4eaac9d28bfffcfe5133d70ac6339", "contents": {"classes": {"org/eclipse/aether/SessionData.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/SessionData", "super": "java/lang/Object", "mthds": [{"nme": "set", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "set", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "get", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "computeIfAbsent", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/util/function/Supplier;)Ljava/lang/Object;", "sig": "(L<PERSON><PERSON>/lang/Object;Lja<PERSON>/util/function/Supplier<Ljava/lang/Object;>;)Ljava/lang/Object;"}], "flds": []}, "org/eclipse/aether/repository/ProxySelector.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/repository/ProxySelector", "super": "java/lang/Object", "mthds": [{"nme": "getProxy", "acc": 1025, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/repository/Proxy;"}], "flds": []}, "org/eclipse/aether/transform/FileTransformer.class": {"ver": 52, "acc": 132609, "nme": "org/eclipse/aether/transform/FileTransformer", "super": "java/lang/Object", "mthds": [{"nme": "transformArtifact", "acc": 1025, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "transformData", "acc": 1025, "dsc": "(L<PERSON><PERSON>/io/File;)Ljava/io/InputStream;", "exs": ["java/io/IOException", "org/eclipse/aether/transform/TransformException"]}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "org/eclipse/aether/transfer/TransferResource.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/transfer/TransferResource", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/io/File;Lorg/eclipse/aether/RequestTrace;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;Ljava/io/File;Lorg/eclipse/aether/RequestTrace;)V"}, {"nme": "getRepositoryId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getRepositoryUrl", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getResourceName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getFile", "acc": 1, "dsc": "()Ljava/io/File;"}, {"nme": "getContentLength", "acc": 1, "dsc": "()J"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(J)Lorg/eclipse/aether/transfer/TransferResource;"}, {"nme": "getResumeOffset", "acc": 1, "dsc": "()J"}, {"nme": "setResumeOffset", "acc": 1, "dsc": "(J)Lorg/eclipse/aether/transfer/TransferResource;"}, {"nme": "getTransferStartTime", "acc": 1, "dsc": "()J"}, {"nme": "getTrace", "acc": 1, "dsc": "()Lorg/eclipse/aether/RequestTrace;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "repositoryId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "repositoryUrl", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "resourceName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "file", "dsc": "Ljava/io/File;"}, {"acc": 18, "nme": "startTime", "dsc": "J"}, {"acc": 18, "nme": "trace", "dsc": "Lorg/eclipse/aether/RequestTrace;"}, {"acc": 2, "nme": "contentLength", "dsc": "J"}, {"acc": 2, "nme": "resumeOffset", "dsc": "J"}]}, "org/eclipse/aether/transfer/TransferListener.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/transfer/TransferListener", "super": "java/lang/Object", "mthds": [{"nme": "transferInitiated", "acc": 1025, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V", "exs": ["org/eclipse/aether/transfer/TransferCancelledException"]}, {"nme": "transferStarted", "acc": 1025, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V", "exs": ["org/eclipse/aether/transfer/TransferCancelledException"]}, {"nme": "transferProgressed", "acc": 1025, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V", "exs": ["org/eclipse/aether/transfer/TransferCancelledException"]}, {"nme": "transferCorrupted", "acc": 1025, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V", "exs": ["org/eclipse/aether/transfer/TransferCancelledException"]}, {"nme": "transferSucceeded", "acc": 1025, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V"}, {"nme": "transferFailed", "acc": 1025, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V"}], "flds": []}, "org/eclipse/aether/MultiRuntimeException.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/MultiRuntimeException", "super": "java/lang/RuntimeException", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/util/List<+Ljava/lang/Throwable;>;)V"}, {"nme": "getThrowables", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Ljava/lang/Throwable;>;"}, {"nme": "mayThrow", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/util/List<+Ljava/lang/Throwable;>;)V"}], "flds": [{"acc": 18, "nme": "throwables", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<+Ljava/lang/Throwable;>;"}]}, "org/eclipse/aether/metadata/MergeableMetadata.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/metadata/MergeableMetadata", "super": "java/lang/Object", "mthds": [{"nme": "merge", "acc": 1025, "dsc": "(Ljava/io/File;Ljava/io/File;)V", "exs": ["org/eclipse/aether/RepositoryException"]}, {"nme": "isMerged", "acc": 1025, "dsc": "()Z"}], "flds": []}, "org/eclipse/aether/DefaultRepositorySystemSession$NullMirrorSelector.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/DefaultRepositorySystemSession$NullMirrorSelector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "getMirror", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/repository/RemoteRepository;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "INSTANCE", "dsc": "Lorg/eclipse/aether/repository/MirrorSelector;"}]}, "org/eclipse/aether/repository/LocalMetadataRequest.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/repository/LocalMetadataRequest", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;)V"}, {"nme": "getMetadata", "acc": 1, "dsc": "()Lorg/eclipse/aether/metadata/Metadata;"}, {"nme": "setMetadata", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;)Lorg/eclipse/aether/repository/LocalMetadataRequest;"}, {"nme": "getContext", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setContext", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/repository/LocalMetadataRequest;"}, {"nme": "getRepository", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/RemoteRepository;"}, {"nme": "setRepository", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/repository/LocalMetadataRequest;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "metadata", "dsc": "Lorg/eclipse/aether/metadata/Metadata;"}, {"acc": 2, "nme": "context", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "repository", "dsc": "Lorg/eclipse/aether/repository/RemoteRepository;"}]}, "org/eclipse/aether/resolution/DependencyResult.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/resolution/DependencyResult", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/resolution/DependencyRequest;)V"}, {"nme": "getRequest", "acc": 1, "dsc": "()Lorg/eclipse/aether/resolution/DependencyRequest;"}, {"nme": "getRoot", "acc": 1, "dsc": "()Lorg/eclipse/aether/graph/DependencyNode;"}, {"nme": "setRoot", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Lorg/eclipse/aether/resolution/DependencyResult;"}, {"nme": "getCycles", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/graph/DependencyCycle;>;"}, {"nme": "setCycles", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/eclipse/aether/resolution/DependencyResult;", "sig": "(<PERSON><PERSON><PERSON>/util/List<Lorg/eclipse/aether/graph/DependencyCycle;>;)Lorg/eclipse/aether/resolution/DependencyResult;"}, {"nme": "getCollectExceptions", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/Exception;>;"}, {"nme": "setCollectExceptions", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/eclipse/aether/resolution/DependencyResult;", "sig": "(<PERSON><PERSON><PERSON>/util/List<Ljava/lang/Exception;>;)Lorg/eclipse/aether/resolution/DependencyResult;"}, {"nme": "getArtifactResults", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/resolution/ArtifactResult;>;"}, {"nme": "setArtifactResults", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/eclipse/aether/resolution/DependencyResult;", "sig": "(<PERSON><PERSON><PERSON>/util/List<Lorg/eclipse/aether/resolution/ArtifactResult;>;)Lorg/eclipse/aether/resolution/DependencyResult;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "request", "dsc": "Lorg/eclipse/aether/resolution/DependencyRequest;"}, {"acc": 2, "nme": "root", "dsc": "Lorg/eclipse/aether/graph/DependencyNode;"}, {"acc": 2, "nme": "cycles", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/graph/DependencyCycle;>;"}, {"acc": 2, "nme": "collectExceptions", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/Exception;>;"}, {"acc": 2, "nme": "artifactResults", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/resolution/ArtifactResult;>;"}]}, "org/eclipse/aether/transfer/NoRepositoryConnectorException.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/transfer/NoRepositoryConnectorException", "super": "org/eclipse/aether/RepositoryException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;<PERSON>ja<PERSON>/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;Ljava/lang/Throwable;)V"}, {"nme": "toMessage", "acc": 10, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Ljava/lang/String;"}, {"nme": "getRepository", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/RemoteRepository;"}], "flds": [{"acc": 146, "nme": "repository", "dsc": "Lorg/eclipse/aether/repository/RemoteRepository;"}]}, "org/eclipse/aether/SyncContext.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/SyncContext", "super": "java/lang/Object", "mthds": [{"nme": "acquire", "acc": 1025, "dsc": "(Lja<PERSON>/util/Collection;Ljava/util/Collection;)V", "sig": "(Ljava/util/Collection<+Lorg/eclipse/aether/artifact/Artifact;>;Ljava/util/Collection<+Lorg/eclipse/aether/metadata/Metadata;>;)V"}, {"nme": "close", "acc": 1025, "dsc": "()V"}], "flds": []}, "org/eclipse/aether/metadata/Metadata$Nature.class": {"ver": 52, "acc": 16433, "nme": "org/eclipse/aether/metadata/Metadata$Nature", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lorg/eclipse/aether/metadata/Metadata$Nature;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/metadata/Metadata$Nature;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Lorg/eclipse/aether/metadata/Metadata$Nature;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "RELEASE", "dsc": "Lorg/eclipse/aether/metadata/Metadata$Nature;"}, {"acc": 16409, "nme": "SNAPSHOT", "dsc": "Lorg/eclipse/aether/metadata/Metadata$Nature;"}, {"acc": 16409, "nme": "RELEASE_OR_SNAPSHOT", "dsc": "Lorg/eclipse/aether/metadata/Metadata$Nature;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lorg/eclipse/aether/metadata/Metadata$Nature;"}]}, "org/eclipse/aether/version/Version.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/version/Version", "super": "java/lang/Object", "mthds": [{"nme": "toString", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "org/eclipse/aether/graph/DependencyFilter.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/graph/DependencyFilter", "super": "java/lang/Object", "mthds": [{"nme": "accept", "acc": 1025, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;<PERSON>ja<PERSON>/util/List;)Z", "sig": "(Lorg/eclipse/aether/graph/DependencyNode;Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;)Z"}], "flds": []}, "org/eclipse/aether/resolution/ArtifactDescriptorRequest.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/resolution/ArtifactDescriptorRequest", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/util/List;Ljava/lang/String;)V", "sig": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;Ljava/lang/String;)V"}, {"nme": "getArtifact", "acc": 1, "dsc": "()Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "setArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/resolution/ArtifactDescriptorRequest;"}, {"nme": "getRepositories", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"nme": "setRepositories", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/eclipse/aether/resolution/ArtifactDescriptorRequest;", "sig": "(Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;)Lorg/eclipse/aether/resolution/ArtifactDescriptorRequest;"}, {"nme": "addRepository", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/resolution/ArtifactDescriptorRequest;"}, {"nme": "getRequestContext", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setRequestContext", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/resolution/ArtifactDescriptorRequest;"}, {"nme": "getTrace", "acc": 1, "dsc": "()Lorg/eclipse/aether/RequestTrace;"}, {"nme": "setTrace", "acc": 1, "dsc": "(Lorg/eclipse/aether/RequestTrace;)Lorg/eclipse/aether/resolution/ArtifactDescriptorRequest;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "artifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}, {"acc": 2, "nme": "repositories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"acc": 2, "nme": "context", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "trace", "dsc": "Lorg/eclipse/aether/RequestTrace;"}]}, "org/eclipse/aether/artifact/ArtifactType.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/artifact/ArtifactType", "super": "java/lang/Object", "mthds": [{"nme": "getId", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getExtension", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getClassifier", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getProperties", "acc": 1025, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}], "flds": []}, "org/eclipse/aether/repository/NoLocalRepositoryManagerException.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/repository/NoLocalRepositoryManagerException", "super": "org/eclipse/aether/RepositoryException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/LocalRepository;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/LocalRepository;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/LocalRepository;Ljava/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/LocalRepository;Ljava/lang/String;Ljava/lang/Throwable;)V"}, {"nme": "toMessage", "acc": 10, "dsc": "(Lorg/eclipse/aether/repository/LocalRepository;)Ljava/lang/String;"}, {"nme": "getRepository", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/LocalRepository;"}], "flds": [{"acc": 146, "nme": "repository", "dsc": "Lorg/eclipse/aether/repository/LocalRepository;"}]}, "org/eclipse/aether/resolution/ResolutionErrorPolicy.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/resolution/ResolutionErrorPolicy", "super": "java/lang/Object", "mthds": [{"nme": "getArtifactPolicy", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/ResolutionErrorPolicyRequest;)I", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/ResolutionErrorPolicyRequest<Lorg/eclipse/aether/artifact/Artifact;>;)I"}, {"nme": "getMetadataPolicy", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/ResolutionErrorPolicyRequest;)I", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/ResolutionErrorPolicyRequest<Lorg/eclipse/aether/metadata/Metadata;>;)I"}], "flds": [{"acc": 25, "nme": "CACHE_DISABLED", "dsc": "I", "val": 0}, {"acc": 25, "nme": "CACHE_NOT_FOUND", "dsc": "I", "val": 1}, {"acc": 25, "nme": "CACHE_TRANSFER_ERROR", "dsc": "I", "val": 2}, {"acc": 25, "nme": "CACHE_ALL", "dsc": "I", "val": 3}]}, "org/eclipse/aether/DefaultRepositoryCache.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/DefaultRepositoryCache", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "get", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON>ja<PERSON>/lang/Object;"}, {"nme": "put", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}], "flds": [{"acc": 18, "nme": "cache", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;"}]}, "org/eclipse/aether/version/VersionConstraint.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/version/VersionConstraint", "super": "java/lang/Object", "mthds": [{"nme": "getRange", "acc": 1025, "dsc": "()Lorg/eclipse/aether/version/VersionRange;"}, {"nme": "getVersion", "acc": 1025, "dsc": "()Lorg/eclipse/aether/version/Version;"}, {"nme": "containsVersion", "acc": 1025, "dsc": "(Lorg/eclipse/aether/version/Version;)Z"}], "flds": []}, "org/eclipse/aether/resolution/MetadataResult.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/resolution/MetadataResult", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/resolution/MetadataRequest;)V"}, {"nme": "getRequest", "acc": 1, "dsc": "()Lorg/eclipse/aether/resolution/MetadataRequest;"}, {"nme": "getMetadata", "acc": 1, "dsc": "()Lorg/eclipse/aether/metadata/Metadata;"}, {"nme": "setMetadata", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;)Lorg/eclipse/aether/resolution/MetadataResult;"}, {"nme": "setException", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Exception;)Lorg/eclipse/aether/resolution/MetadataResult;"}, {"nme": "getException", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Exception;"}, {"nme": "setUpdated", "acc": 1, "dsc": "(Z)Lorg/eclipse/aether/resolution/MetadataResult;"}, {"nme": "isUpdated", "acc": 1, "dsc": "()Z"}, {"nme": "isResolved", "acc": 1, "dsc": "()Z"}, {"nme": "isMissing", "acc": 1, "dsc": "()Z"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "request", "dsc": "Lorg/eclipse/aether/resolution/MetadataRequest;"}, {"acc": 2, "nme": "exception", "dsc": "<PERSON><PERSON><PERSON>/lang/Exception;"}, {"acc": 2, "nme": "updated", "dsc": "Z"}, {"acc": 2, "nme": "metadata", "dsc": "Lorg/eclipse/aether/metadata/Metadata;"}]}, "org/eclipse/aether/graph/DefaultDependencyNode.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/graph/DefaultDependencyNode", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/Dependency;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(<PERSON><PERSON><PERSON>/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;)V"}, {"nme": "getDependency", "acc": 1, "dsc": "()Lorg/eclipse/aether/graph/Dependency;"}, {"nme": "getArtifact", "acc": 1, "dsc": "()Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "setArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)V"}, {"nme": "getRelocations", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Lorg/eclipse/aether/artifact/Artifact;>;"}, {"nme": "setRelocations", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(<PERSON><PERSON><PERSON>/util/List<+Lorg/eclipse/aether/artifact/Artifact;>;)V"}, {"nme": "getAliases", "acc": 1, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<+Lorg/eclipse/aether/artifact/Artifact;>;"}, {"nme": "set<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)V", "sig": "(L<PERSON><PERSON>/util/Collection<+Lorg/eclipse/aether/artifact/Artifact;>;)V"}, {"nme": "getVersionConstraint", "acc": 1, "dsc": "()Lorg/eclipse/aether/version/VersionConstraint;"}, {"nme": "setVersionConstraint", "acc": 1, "dsc": "(Lorg/eclipse/aether/version/VersionConstraint;)V"}, {"nme": "getVersion", "acc": 1, "dsc": "()Lorg/eclipse/aether/version/Version;"}, {"nme": "setVersion", "acc": 1, "dsc": "(Lorg/eclipse/aether/version/Version;)V"}, {"nme": "setScope", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setOptional", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/<PERSON>;)V"}, {"nme": "getManagedBits", "acc": 1, "dsc": "()I"}, {"nme": "setManagedBits", "acc": 1, "dsc": "(I)V"}, {"nme": "getRepositories", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"nme": "setRepositories", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;)V"}, {"nme": "getRequestContext", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setRequestContext", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getData", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;"}, {"nme": "setData", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Ljava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "setData", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "accept", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyVisitor;)Z"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "children", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;"}, {"acc": 2, "nme": "dependency", "dsc": "Lorg/eclipse/aether/graph/Dependency;"}, {"acc": 2, "nme": "artifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}, {"acc": 2, "nme": "relocations", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<+Lorg/eclipse/aether/artifact/Artifact;>;"}, {"acc": 2, "nme": "aliases", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<+Lorg/eclipse/aether/artifact/Artifact;>;"}, {"acc": 2, "nme": "versionConstraint", "dsc": "Lorg/eclipse/aether/version/VersionConstraint;"}, {"acc": 2, "nme": "version", "dsc": "Lorg/eclipse/aether/version/Version;"}, {"acc": 2, "nme": "managedBits", "dsc": "B"}, {"acc": 2, "nme": "repositories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"acc": 2, "nme": "context", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "data", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;"}]}, "org/eclipse/aether/collection/DependencyTraverser.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/collection/DependencyTraverser", "super": "java/lang/Object", "mthds": [{"nme": "traverseDependency", "acc": 1025, "dsc": "(Lorg/eclipse/aether/graph/Dependency;)Z"}, {"nme": "deriveChildTraverser", "acc": 1025, "dsc": "(Lorg/eclipse/aether/collection/DependencyCollectionContext;)Lorg/eclipse/aether/collection/DependencyTraverser;"}], "flds": []}, "org/eclipse/aether/transfer/TransferEvent.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/transfer/TransferEvent", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent$Builder;)V"}, {"nme": "getType", "acc": 1, "dsc": "()Lorg/eclipse/aether/transfer/TransferEvent$EventType;"}, {"nme": "getRequestType", "acc": 1, "dsc": "()Lorg/eclipse/aether/transfer/TransferEvent$RequestType;"}, {"nme": "getSession", "acc": 1, "dsc": "()Lorg/eclipse/aether/RepositorySystemSession;"}, {"nme": "getResource", "acc": 1, "dsc": "()Lorg/eclipse/aether/transfer/TransferResource;"}, {"nme": "getTransferredBytes", "acc": 1, "dsc": "()J"}, {"nme": "getDataBuffer", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/nio/<PERSON>;"}, {"nme": "getDataLength", "acc": 1, "dsc": "()I"}, {"nme": "getException", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Exception;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "type", "dsc": "Lorg/eclipse/aether/transfer/TransferEvent$EventType;"}, {"acc": 18, "nme": "requestType", "dsc": "Lorg/eclipse/aether/transfer/TransferEvent$RequestType;"}, {"acc": 18, "nme": "session", "dsc": "Lorg/eclipse/aether/RepositorySystemSession;"}, {"acc": 18, "nme": "resource", "dsc": "Lorg/eclipse/aether/transfer/TransferResource;"}, {"acc": 18, "nme": "dataBuffer", "dsc": "<PERSON><PERSON><PERSON>/nio/<PERSON>te<PERSON>er;"}, {"acc": 18, "nme": "transferredBytes", "dsc": "J"}, {"acc": 18, "nme": "exception", "dsc": "<PERSON><PERSON><PERSON>/lang/Exception;"}]}, "org/eclipse/aether/resolution/ArtifactDescriptorResult.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/resolution/ArtifactDescriptorResult", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/resolution/ArtifactDescriptorRequest;)V"}, {"nme": "getRequest", "acc": 1, "dsc": "()Lorg/eclipse/aether/resolution/ArtifactDescriptorRequest;"}, {"nme": "getExceptions", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/Exception;>;"}, {"nme": "setExceptions", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;", "sig": "(Lja<PERSON>/util/List<Ljava/lang/Exception;>;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;"}, {"nme": "addException", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Exception;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;"}, {"nme": "getRelocations", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/artifact/Artifact;>;"}, {"nme": "setRelocations", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;", "sig": "(L<PERSON><PERSON>/util/List<Lorg/eclipse/aether/artifact/Artifact;>;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;"}, {"nme": "addRelocation", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;"}, {"nme": "getAliases", "acc": 1, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Lorg/eclipse/aether/artifact/Artifact;>;"}, {"nme": "set<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;", "sig": "(Lja<PERSON>/util/Collection<Lorg/eclipse/aether/artifact/Artifact;>;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;"}, {"nme": "add<PERSON><PERSON>s", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;"}, {"nme": "getArtifact", "acc": 1, "dsc": "()Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "setArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;"}, {"nme": "getRepository", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/ArtifactRepository;"}, {"nme": "setRepository", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/ArtifactRepository;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;"}, {"nme": "getDependencies", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;"}, {"nme": "setDependencies", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;", "sig": "(<PERSON><PERSON><PERSON>/util/List<Lorg/eclipse/aether/graph/Dependency;>;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;"}, {"nme": "addDependency", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/Dependency;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;"}, {"nme": "getManagedDependencies", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;"}, {"nme": "setManagedDependencies", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;", "sig": "(<PERSON><PERSON><PERSON>/util/List<Lorg/eclipse/aether/graph/Dependency;>;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;"}, {"nme": "addManagedDependency", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/Dependency;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;"}, {"nme": "getRepositories", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"nme": "setRepositories", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;", "sig": "(Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;"}, {"nme": "addRepository", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;"}, {"nme": "getProperties", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;"}, {"nme": "setProperties", "acc": 1, "dsc": "(Ljava/util/Map;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "request", "dsc": "Lorg/eclipse/aether/resolution/ArtifactDescriptorRequest;"}, {"acc": 2, "nme": "exceptions", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/Exception;>;"}, {"acc": 2, "nme": "relocations", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/artifact/Artifact;>;"}, {"acc": 2, "nme": "aliases", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lorg/eclipse/aether/artifact/Artifact;>;"}, {"acc": 2, "nme": "artifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}, {"acc": 2, "nme": "repository", "dsc": "Lorg/eclipse/aether/repository/ArtifactRepository;"}, {"acc": 2, "nme": "dependencies", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;"}, {"acc": 2, "nme": "managedDependencies", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;"}, {"acc": 2, "nme": "repositories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"acc": 2, "nme": "properties", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;"}]}, "org/eclipse/aether/repository/WorkspaceReader.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/repository/WorkspaceReader", "super": "java/lang/Object", "mthds": [{"nme": "getRepository", "acc": 1025, "dsc": "()Lorg/eclipse/aether/repository/WorkspaceRepository;"}, {"nme": "findArtifact", "acc": 1025, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Ljava/io/File;"}, {"nme": "findVersions", "acc": 1025, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Ljava/util/List;", "sig": "(Lorg/eclipse/aether/artifact/Artifact;)Ljava/util/List<Ljava/lang/String;>;"}], "flds": []}, "org/eclipse/aether/graph/Dependency.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/graph/Dependency", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/lang/String;<PERSON><PERSON><PERSON>/lang/Boolean;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/lang/String;<PERSON><PERSON><PERSON>/lang/Boolean;<PERSON><PERSON><PERSON>/util/Collection;)V", "sig": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/lang/String;Ljava/lang/Boolean;Ljava/util/Collection<Lorg/eclipse/aether/graph/Exclusion;>;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/lang/String;<PERSON><PERSON><PERSON>/util/Set;<PERSON><PERSON><PERSON>/lang/<PERSON><PERSON>an;)V", "sig": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/lang/String;Ljava/util/Set<Lorg/eclipse/aether/graph/Exclusion;>;Ljava/lang/<PERSON>an;)V"}, {"nme": "getArtifact", "acc": 1, "dsc": "()Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "setArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/graph/Dependency;"}, {"nme": "getScope", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setScope", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/graph/Dependency;"}, {"nme": "isOptional", "acc": 1, "dsc": "()Z"}, {"nme": "getOptional", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/<PERSON>an;"}, {"nme": "setOptional", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Boolean;)Lorg/eclipse/aether/graph/Dependency;"}, {"nme": "getExclusions", "acc": 1, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Lorg/eclipse/aether/graph/Exclusion;>;"}, {"nme": "setExclusions", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Lorg/eclipse/aether/graph/Dependency;", "sig": "(<PERSON><PERSON><PERSON>/util/Collection<Lorg/eclipse/aether/graph/Exclusion;>;)Lorg/eclipse/aether/graph/Dependency;"}, {"nme": "hasEquivalentExclusions", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Z", "sig": "(Ljava/util/Collection<Lorg/eclipse/aether/graph/Exclusion;>;)Z"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "artifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}, {"acc": 18, "nme": "scope", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "optional", "dsc": "<PERSON><PERSON><PERSON>/lang/Boolean;"}, {"acc": 18, "nme": "exclusions", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Lorg/eclipse/aether/graph/Exclusion;>;"}]}, "org/eclipse/aether/repository/LocalMetadataResult.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/repository/LocalMetadataResult", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/LocalMetadataRequest;)V"}, {"nme": "getRequest", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/LocalMetadataRequest;"}, {"nme": "getFile", "acc": 1, "dsc": "()Ljava/io/File;"}, {"nme": "setFile", "acc": 1, "dsc": "(Ljava/io/File;)Lorg/eclipse/aether/repository/LocalMetadataResult;"}, {"nme": "isStale", "acc": 1, "dsc": "()Z"}, {"nme": "setStale", "acc": 1, "dsc": "(Z)Lorg/eclipse/aether/repository/LocalMetadataResult;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "request", "dsc": "Lorg/eclipse/aether/repository/LocalMetadataRequest;"}, {"acc": 2, "nme": "file", "dsc": "Ljava/io/File;"}, {"acc": 2, "nme": "stale", "dsc": "Z"}]}, "org/eclipse/aether/collection/DependencyManager.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/collection/DependencyManager", "super": "java/lang/Object", "mthds": [{"nme": "manageDependency", "acc": 1025, "dsc": "(Lorg/eclipse/aether/graph/Dependency;)Lorg/eclipse/aether/collection/DependencyManagement;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(Lorg/eclipse/aether/collection/DependencyCollectionContext;)Lorg/eclipse/aether/collection/DependencyManager;"}], "flds": []}, "org/eclipse/aether/resolution/MetadataRequest.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/resolution/MetadataRequest", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;)V"}, {"nme": "getMetadata", "acc": 1, "dsc": "()Lorg/eclipse/aether/metadata/Metadata;"}, {"nme": "setMetadata", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;)Lorg/eclipse/aether/resolution/MetadataRequest;"}, {"nme": "getRepository", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/RemoteRepository;"}, {"nme": "setRepository", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/resolution/MetadataRequest;"}, {"nme": "getRequestContext", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setRequestContext", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/resolution/MetadataRequest;"}, {"nme": "isDeleteLocalCopyIfMissing", "acc": 1, "dsc": "()Z"}, {"nme": "setDeleteLocalCopyIfMissing", "acc": 1, "dsc": "(Z)Lorg/eclipse/aether/resolution/MetadataRequest;"}, {"nme": "isFavorLocalRepository", "acc": 1, "dsc": "()Z"}, {"nme": "setFavorLocalRepository", "acc": 1, "dsc": "(Z)Lorg/eclipse/aether/resolution/MetadataRequest;"}, {"nme": "getTrace", "acc": 1, "dsc": "()Lorg/eclipse/aether/RequestTrace;"}, {"nme": "setTrace", "acc": 1, "dsc": "(Lorg/eclipse/aether/RequestTrace;)Lorg/eclipse/aether/resolution/MetadataRequest;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "metadata", "dsc": "Lorg/eclipse/aether/metadata/Metadata;"}, {"acc": 2, "nme": "repository", "dsc": "Lorg/eclipse/aether/repository/RemoteRepository;"}, {"acc": 2, "nme": "context", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "deleteLocalCopyIfMissing", "dsc": "Z"}, {"acc": 2, "nme": "favorLocalRepository", "dsc": "Z"}, {"acc": 2, "nme": "trace", "dsc": "Lorg/eclipse/aether/RequestTrace;"}]}, "org/eclipse/aether/metadata/Metadata.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/metadata/Metadata", "super": "java/lang/Object", "mthds": [{"nme": "getGroupId", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getArtifactId", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getVersion", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getType", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getNature", "acc": 1025, "dsc": "()Lorg/eclipse/aether/metadata/Metadata$Nature;"}, {"nme": "getFile", "acc": 1025, "dsc": "()Ljava/io/File;"}, {"nme": "setFile", "acc": 1025, "dsc": "(Ljava/io/File;)Lorg/eclipse/aether/metadata/Metadata;"}, {"nme": "getProperty", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getProperties", "acc": 1025, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "setProperties", "acc": 1025, "dsc": "(Ljava/util/Map;)Lorg/eclipse/aether/metadata/Metadata;", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)Lorg/eclipse/aether/metadata/Metadata;"}], "flds": []}, "org/eclipse/aether/transfer/RepositoryOfflineException.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/transfer/RepositoryOfflineException", "super": "org/eclipse/aether/RepositoryException", "mthds": [{"nme": "getMessage", "acc": 10, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Ljava/lang/String;"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;)V"}, {"nme": "getRepository", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/RemoteRepository;"}], "flds": [{"acc": 146, "nme": "repository", "dsc": "Lorg/eclipse/aether/repository/RemoteRepository;"}]}, "org/eclipse/aether/artifact/DefaultArtifactType.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/artifact/DefaultArtifactType", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;ZZ)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;Ljava/util/Map;)V", "sig": "(Lja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "emptify", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getExtension", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getClassifier", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getProperties", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}], "flds": [{"acc": 18, "nme": "id", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "extension", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "classifier", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "properties", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}]}, "org/eclipse/aether/repository/RemoteRepository$Builder.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/repository/RemoteRepository$Builder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)V"}, {"nme": "build", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/RemoteRepository;"}, {"nme": "delta", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "<T:Ljava/lang/Object;>(ITT;TT;)V"}, {"nme": "setId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/repository/RemoteRepository$Builder;"}, {"nme": "setContentType", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/repository/RemoteRepository$Builder;"}, {"nme": "setUrl", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/repository/RemoteRepository$Builder;"}, {"nme": "setPolicy", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RepositoryPolicy;)Lorg/eclipse/aether/repository/RemoteRepository$Builder;"}, {"nme": "setReleasePolicy", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RepositoryPolicy;)Lorg/eclipse/aether/repository/RemoteRepository$Builder;"}, {"nme": "setSnapshotPolicy", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RepositoryPolicy;)Lorg/eclipse/aether/repository/RemoteRepository$Builder;"}, {"nme": "setProxy", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/Proxy;)Lorg/eclipse/aether/repository/RemoteRepository$Builder;"}, {"nme": "setAuthentication", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/Authentication;)Lorg/eclipse/aether/repository/RemoteRepository$Builder;"}, {"nme": "setMirroredRepositories", "acc": 1, "dsc": "(<PERSON>ja<PERSON>/util/List;)Lorg/eclipse/aether/repository/RemoteRepository$Builder;", "sig": "(Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;)Lorg/eclipse/aether/repository/RemoteRepository$Builder;"}, {"nme": "addMirroredRepository", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/repository/RemoteRepository$Builder;"}, {"nme": "setRepositoryManager", "acc": 1, "dsc": "(Z)Lorg/eclipse/aether/repository/RemoteRepository$Builder;"}, {"nme": "setBlocked", "acc": 1, "dsc": "(Z)Lorg/eclipse/aether/repository/RemoteRepository$Builder;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "DEFAULT_POLICY", "dsc": "Lorg/eclipse/aether/repository/RepositoryPolicy;"}, {"acc": 24, "nme": "ID", "dsc": "I", "val": 1}, {"acc": 24, "nme": "TYPE", "dsc": "I", "val": 2}, {"acc": 24, "nme": "URL", "dsc": "I", "val": 4}, {"acc": 24, "nme": "RELEASES", "dsc": "I", "val": 8}, {"acc": 24, "nme": "SNAPSHOTS", "dsc": "I", "val": 16}, {"acc": 24, "nme": "PROXY", "dsc": "I", "val": 32}, {"acc": 24, "nme": "AUTH", "dsc": "I", "val": 64}, {"acc": 24, "nme": "MIRRORED", "dsc": "I", "val": 128}, {"acc": 24, "nme": "REPOMAN", "dsc": "I", "val": 256}, {"acc": 24, "nme": "BLOCKED", "dsc": "I", "val": 512}, {"acc": 0, "nme": "delta", "dsc": "I"}, {"acc": 0, "nme": "prototype", "dsc": "Lorg/eclipse/aether/repository/RemoteRepository;"}, {"acc": 0, "nme": "id", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "type", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "url", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "releasePolicy", "dsc": "Lorg/eclipse/aether/repository/RepositoryPolicy;"}, {"acc": 0, "nme": "snapshotPolicy", "dsc": "Lorg/eclipse/aether/repository/RepositoryPolicy;"}, {"acc": 0, "nme": "proxy", "dsc": "Lorg/eclipse/aether/repository/Proxy;"}, {"acc": 0, "nme": "authentication", "dsc": "Lorg/eclipse/aether/repository/Authentication;"}, {"acc": 0, "nme": "mirroredRepositories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"acc": 0, "nme": "repositoryManager", "dsc": "Z"}, {"acc": 0, "nme": "blocked", "dsc": "Z"}]}, "org/eclipse/aether/resolution/VersionRangeResolutionException.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/resolution/VersionRangeResolutionException", "super": "org/eclipse/aether/RepositoryException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/resolution/VersionRangeResult;)V"}, {"nme": "getMessage", "acc": 10, "dsc": "(Lorg/eclipse/aether/resolution/VersionRangeResult;)Ljava/lang/String;"}, {"nme": "getCause", "acc": 10, "dsc": "(Lorg/eclipse/aether/resolution/VersionRangeResult;)Ljava/lang/Throwable;"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/resolution/VersionRangeResult;Lja<PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/resolution/VersionRangeResult;Ljava/lang/String;Lja<PERSON>/lang/Throwable;)V"}, {"nme": "getResult", "acc": 1, "dsc": "()Lorg/eclipse/aether/resolution/VersionRangeResult;"}], "flds": [{"acc": 146, "nme": "result", "dsc": "Lorg/eclipse/aether/resolution/VersionRangeResult;"}]}, "org/eclipse/aether/repository/ArtifactRepository.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/repository/ArtifactRepository", "super": "java/lang/Object", "mthds": [{"nme": "getContentType", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getId", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "org/eclipse/aether/RepositoryListener.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/RepositoryListener", "super": "java/lang/Object", "mthds": [{"nme": "artifactDescriptorInvalid", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "artifactDescriptorMissing", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "metadataInvalid", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "artifactResolving", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "artifactResolved", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "metadataResolving", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "metadataResolved", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "artifactDownloading", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "artifactDownloaded", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "metadataDownloading", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "metadataDownloaded", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "artifactInstalling", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "artifactInstalled", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "metadataInstalling", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "metadataInstalled", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "artifactDeploying", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "artifactDeployed", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "metadataDeploying", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "metadataDeployed", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}], "flds": []}, "org/eclipse/aether/repository/AuthenticationDigest.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/repository/AuthenticationDigest", "super": "java/lang/Object", "mthds": [{"nme": "forRepository", "acc": 9, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;)Ljava/lang/String;"}, {"nme": "forProxy", "acc": 9, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;)Ljava/lang/String;"}, {"nme": "<init>", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/repository/Proxy;)V"}, {"nme": "newDigest", "acc": 10, "dsc": "()Ljava/security/MessageDigest;"}, {"nme": "getSession", "acc": 1, "dsc": "()Lorg/eclipse/aether/RepositorySystemSession;"}, {"nme": "getRepository", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/RemoteRepository;"}, {"nme": "getProxy", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/Proxy;"}, {"nme": "update", "acc": 129, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "update", "acc": 129, "dsc": "([C)V"}, {"nme": "update", "acc": 129, "dsc": "([B)V"}, {"nme": "digest", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "digest", "dsc": "Ljava/security/MessageDigest;"}, {"acc": 18, "nme": "session", "dsc": "Lorg/eclipse/aether/RepositorySystemSession;"}, {"acc": 18, "nme": "repository", "dsc": "Lorg/eclipse/aether/repository/RemoteRepository;"}, {"acc": 18, "nme": "proxy", "dsc": "Lorg/eclipse/aether/repository/Proxy;"}]}, "org/eclipse/aether/AbstractForwardingRepositorySystemSession.class": {"ver": 52, "acc": 1057, "nme": "org/eclipse/aether/AbstractForwardingRepositorySystemSession", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "getSession", "acc": 1028, "dsc": "()Lorg/eclipse/aether/RepositorySystemSession;"}, {"nme": "isOffline", "acc": 1, "dsc": "()Z"}, {"nme": "isIgnoreArtifactDescriptorRepositories", "acc": 1, "dsc": "()Z"}, {"nme": "getResolutionErrorPolicy", "acc": 1, "dsc": "()Lorg/eclipse/aether/resolution/ResolutionErrorPolicy;"}, {"nme": "getArtifactDescriptorPolicy", "acc": 1, "dsc": "()Lorg/eclipse/aether/resolution/ArtifactDescriptorPolicy;"}, {"nme": "getChecksumPolicy", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getUpdatePolicy", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLocalRepository", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/LocalRepository;"}, {"nme": "getLocalRepositoryManager", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/LocalRepositoryManager;"}, {"nme": "getWorkspaceReader", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/WorkspaceReader;"}, {"nme": "getRepositoryListener", "acc": 1, "dsc": "()Lorg/eclipse/aether/RepositoryListener;"}, {"nme": "getTransferListener", "acc": 1, "dsc": "()Lorg/eclipse/aether/transfer/TransferListener;"}, {"nme": "getSystemProperties", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "getUserProperties", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "getConfigProperties", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;"}, {"nme": "getMirrorSelector", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/MirrorSelector;"}, {"nme": "getProxySelector", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/ProxySelector;"}, {"nme": "getAuthenticationSelector", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/AuthenticationSelector;"}, {"nme": "getArtifactTypeRegistry", "acc": 1, "dsc": "()Lorg/eclipse/aether/artifact/ArtifactTypeRegistry;"}, {"nme": "getDependencyTraverser", "acc": 1, "dsc": "()Lorg/eclipse/aether/collection/DependencyTraverser;"}, {"nme": "getDependencyManager", "acc": 1, "dsc": "()Lorg/eclipse/aether/collection/DependencyManager;"}, {"nme": "getDependencySelector", "acc": 1, "dsc": "()Lorg/eclipse/aether/collection/DependencySelector;"}, {"nme": "getVersionFilter", "acc": 1, "dsc": "()Lorg/eclipse/aether/collection/VersionFilter;"}, {"nme": "getDependencyGraphTransformer", "acc": 1, "dsc": "()Lorg/eclipse/aether/collection/DependencyGraphTransformer;"}, {"nme": "getData", "acc": 1, "dsc": "()Lorg/eclipse/aether/SessionData;"}, {"nme": "getCache", "acc": 1, "dsc": "()Lorg/eclipse/aether/RepositoryCache;"}, {"nme": "getFileTransformerManager", "acc": 1, "dsc": "()Lorg/eclipse/aether/transform/FileTransformerManager;"}], "flds": []}, "org/eclipse/aether/collection/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/collection/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/repository/LocalRepositoryManager.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/repository/LocalRepositoryManager", "super": "java/lang/Object", "mthds": [{"nme": "getRepository", "acc": 1025, "dsc": "()Lorg/eclipse/aether/repository/LocalRepository;"}, {"nme": "getPathForLocalArtifact", "acc": 1025, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lja<PERSON>/lang/String;"}, {"nme": "getPathForRemoteArtifact", "acc": 1025, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "getPathForLocalMetadata", "acc": 1025, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;)Ljava/lang/String;"}, {"nme": "getPathForRemoteMetadata", "acc": 1025, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "find", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/LocalArtifactRequest;)Lorg/eclipse/aether/repository/LocalArtifactResult;"}, {"nme": "add", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/LocalArtifactRegistration;)V"}, {"nme": "find", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/LocalMetadataRequest;)Lorg/eclipse/aether/repository/LocalMetadataResult;"}, {"nme": "add", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/LocalMetadataRegistration;)V"}], "flds": []}, "org/eclipse/aether/artifact/ArtifactProperties.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/artifact/ArtifactProperties", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "TYPE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "type"}, {"acc": 25, "nme": "LANGUAGE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "language"}, {"acc": 25, "nme": "LOCAL_PATH", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "localPath"}, {"acc": 25, "nme": "INCLUDES_DEPENDENCIES", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "includesDependencies"}, {"acc": 25, "nme": "CONSTITUTES_BUILD_PATH", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "constitutesBuildPath"}, {"acc": 25, "nme": "DOWNLOAD_URL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "downloadUrl"}]}, "org/eclipse/aether/version/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/version/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/resolution/VersionResult.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/resolution/VersionResult", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/resolution/VersionRequest;)V"}, {"nme": "getRequest", "acc": 1, "dsc": "()Lorg/eclipse/aether/resolution/VersionRequest;"}, {"nme": "getExceptions", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/Exception;>;"}, {"nme": "addException", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Exception;)Lorg/eclipse/aether/resolution/VersionResult;"}, {"nme": "getVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setVersion", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/resolution/VersionResult;"}, {"nme": "getRepository", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/ArtifactRepository;"}, {"nme": "setRepository", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/ArtifactRepository;)Lorg/eclipse/aether/resolution/VersionResult;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "request", "dsc": "Lorg/eclipse/aether/resolution/VersionRequest;"}, {"acc": 2, "nme": "exceptions", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/Exception;>;"}, {"acc": 2, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "repository", "dsc": "Lorg/eclipse/aether/repository/ArtifactRepository;"}]}, "org/eclipse/aether/DefaultRepositorySystemSession$NullFileTransformerManager.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/DefaultRepositorySystemSession$NullFileTransformerManager", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "getTransformersForArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Ljava/util/Collection;", "sig": "(Lorg/eclipse/aether/artifact/Artifact;)Ljava/util/Collection<Lorg/eclipse/aether/transform/FileTransformer;>;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "INSTANCE", "dsc": "Lorg/eclipse/aether/transform/FileTransformerManager;"}]}, "org/eclipse/aether/DefaultRepositorySystemSession$NullArtifactTypeRegistry.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/DefaultRepositorySystemSession$NullArtifactTypeRegistry", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "get", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/artifact/ArtifactType;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "INSTANCE", "dsc": "Lorg/eclipse/aether/artifact/ArtifactTypeRegistry;"}]}, "org/eclipse/aether/graph/Dependency$Exclusions$1.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/graph/Dependency$Exclusions$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/graph/Dependency$Exclusions;)V"}, {"nme": "hasNext", "acc": 1, "dsc": "()Z"}, {"nme": "next", "acc": 1, "dsc": "()Lorg/eclipse/aether/graph/Exclusion;"}, {"nme": "remove", "acc": 1, "dsc": "()V"}, {"nme": "next", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 2, "nme": "cursor", "dsc": "I"}, {"acc": 4112, "nme": "this$0", "dsc": "Lorg/eclipse/aether/graph/Dependency$Exclusions;"}]}, "org/eclipse/aether/resolution/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/resolution/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/installation/InstallRequest.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/installation/InstallRequest", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getArtifacts", "acc": 1, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Lorg/eclipse/aether/artifact/Artifact;>;"}, {"nme": "setArtifacts", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Lorg/eclipse/aether/installation/InstallRequest;", "sig": "(Ljava/util/Collection<Lorg/eclipse/aether/artifact/Artifact;>;)Lorg/eclipse/aether/installation/InstallRequest;"}, {"nme": "addArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/installation/InstallRequest;"}, {"nme": "getMetadata", "acc": 1, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Lorg/eclipse/aether/metadata/Metadata;>;"}, {"nme": "setMetadata", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Lorg/eclipse/aether/installation/InstallRequest;", "sig": "(Ljava/util/Collection<Lorg/eclipse/aether/metadata/Metadata;>;)Lorg/eclipse/aether/installation/InstallRequest;"}, {"nme": "addMetadata", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;)Lorg/eclipse/aether/installation/InstallRequest;"}, {"nme": "getTrace", "acc": 1, "dsc": "()Lorg/eclipse/aether/RequestTrace;"}, {"nme": "setTrace", "acc": 1, "dsc": "(Lorg/eclipse/aether/RequestTrace;)Lorg/eclipse/aether/installation/InstallRequest;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "artifacts", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lorg/eclipse/aether/artifact/Artifact;>;"}, {"acc": 2, "nme": "metadata", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lorg/eclipse/aether/metadata/Metadata;>;"}, {"acc": 2, "nme": "trace", "dsc": "Lorg/eclipse/aether/RequestTrace;"}]}, "org/eclipse/aether/DefaultRepositorySystemSession$NullProxySelector.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/DefaultRepositorySystemSession$NullProxySelector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "getProxy", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/repository/Proxy;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "INSTANCE", "dsc": "Lorg/eclipse/aether/repository/ProxySelector;"}]}, "org/eclipse/aether/collection/CollectStepData.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/collection/CollectStepData", "super": "java/lang/Object", "mthds": [{"nme": "getContext", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;"}, {"nme": "getNode", "acc": 1025, "dsc": "()Lorg/eclipse/aether/graph/Dependency;"}], "flds": []}, "org/eclipse/aether/RepositoryException.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/RepositoryException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "getMessage", "acc": 12, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)Ljava/lang/String;"}], "flds": []}, "org/eclipse/aether/RepositoryEvent$EventType.class": {"ver": 52, "acc": 16433, "nme": "org/eclipse/aether/RepositoryEvent$EventType", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lorg/eclipse/aether/RepositoryEvent$EventType;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/RepositoryEvent$EventType;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Lorg/eclipse/aether/RepositoryEvent$EventType;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "ARTIFACT_DESCRIPTOR_INVALID", "dsc": "Lorg/eclipse/aether/RepositoryEvent$EventType;"}, {"acc": 16409, "nme": "ARTIFACT_DESCRIPTOR_MISSING", "dsc": "Lorg/eclipse/aether/RepositoryEvent$EventType;"}, {"acc": 16409, "nme": "METADATA_INVALID", "dsc": "Lorg/eclipse/aether/RepositoryEvent$EventType;"}, {"acc": 16409, "nme": "ARTIFACT_RESOLVING", "dsc": "Lorg/eclipse/aether/RepositoryEvent$EventType;"}, {"acc": 16409, "nme": "ARTIFACT_RESOLVED", "dsc": "Lorg/eclipse/aether/RepositoryEvent$EventType;"}, {"acc": 16409, "nme": "METADATA_RESOLVING", "dsc": "Lorg/eclipse/aether/RepositoryEvent$EventType;"}, {"acc": 16409, "nme": "METADATA_RESOLVED", "dsc": "Lorg/eclipse/aether/RepositoryEvent$EventType;"}, {"acc": 16409, "nme": "ARTIFACT_DOWNLOADING", "dsc": "Lorg/eclipse/aether/RepositoryEvent$EventType;"}, {"acc": 16409, "nme": "ARTIFACT_DOWNLOADED", "dsc": "Lorg/eclipse/aether/RepositoryEvent$EventType;"}, {"acc": 16409, "nme": "METADATA_DOWNLOADING", "dsc": "Lorg/eclipse/aether/RepositoryEvent$EventType;"}, {"acc": 16409, "nme": "METADATA_DOWNLOADED", "dsc": "Lorg/eclipse/aether/RepositoryEvent$EventType;"}, {"acc": 16409, "nme": "ARTIFACT_INSTALLING", "dsc": "Lorg/eclipse/aether/RepositoryEvent$EventType;"}, {"acc": 16409, "nme": "ARTIFACT_INSTALLED", "dsc": "Lorg/eclipse/aether/RepositoryEvent$EventType;"}, {"acc": 16409, "nme": "METADATA_INSTALLING", "dsc": "Lorg/eclipse/aether/RepositoryEvent$EventType;"}, {"acc": 16409, "nme": "METADATA_INSTALLED", "dsc": "Lorg/eclipse/aether/RepositoryEvent$EventType;"}, {"acc": 16409, "nme": "ARTIFACT_DEPLOYING", "dsc": "Lorg/eclipse/aether/RepositoryEvent$EventType;"}, {"acc": 16409, "nme": "ARTIFACT_DEPLOYED", "dsc": "Lorg/eclipse/aether/RepositoryEvent$EventType;"}, {"acc": 16409, "nme": "METADATA_DEPLOYING", "dsc": "Lorg/eclipse/aether/RepositoryEvent$EventType;"}, {"acc": 16409, "nme": "METADATA_DEPLOYED", "dsc": "Lorg/eclipse/aether/RepositoryEvent$EventType;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lorg/eclipse/aether/RepositoryEvent$EventType;"}]}, "org/eclipse/aether/resolution/VersionRangeResult.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/resolution/VersionRangeResult", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/resolution/VersionRangeRequest;)V"}, {"nme": "getRequest", "acc": 1, "dsc": "()Lorg/eclipse/aether/resolution/VersionRangeRequest;"}, {"nme": "getExceptions", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/Exception;>;"}, {"nme": "addException", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Exception;)Lorg/eclipse/aether/resolution/VersionRangeResult;"}, {"nme": "getVersions", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/version/Version;>;"}, {"nme": "addVersion", "acc": 1, "dsc": "(Lorg/eclipse/aether/version/Version;)Lorg/eclipse/aether/resolution/VersionRangeResult;"}, {"nme": "setVersions", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/eclipse/aether/resolution/VersionRangeResult;", "sig": "(L<PERSON><PERSON>/util/List<Lorg/eclipse/aether/version/Version;>;)Lorg/eclipse/aether/resolution/VersionRangeResult;"}, {"nme": "getLowestVersion", "acc": 1, "dsc": "()Lorg/eclipse/aether/version/Version;"}, {"nme": "getHighestVersion", "acc": 1, "dsc": "()Lorg/eclipse/aether/version/Version;"}, {"nme": "getRepository", "acc": 1, "dsc": "(Lorg/eclipse/aether/version/Version;)Lorg/eclipse/aether/repository/ArtifactRepository;"}, {"nme": "setRepository", "acc": 1, "dsc": "(Lorg/eclipse/aether/version/Version;Lorg/eclipse/aether/repository/ArtifactRepository;)Lorg/eclipse/aether/resolution/VersionRangeResult;"}, {"nme": "getVersionConstraint", "acc": 1, "dsc": "()Lorg/eclipse/aether/version/VersionConstraint;"}, {"nme": "setVersionConstraint", "acc": 1, "dsc": "(Lorg/eclipse/aether/version/VersionConstraint;)Lorg/eclipse/aether/resolution/VersionRangeResult;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "request", "dsc": "Lorg/eclipse/aether/resolution/VersionRangeRequest;"}, {"acc": 2, "nme": "exceptions", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/Exception;>;"}, {"acc": 2, "nme": "versions", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/version/Version;>;"}, {"acc": 2, "nme": "repositories", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Lorg/eclipse/aether/version/Version;Lorg/eclipse/aether/repository/ArtifactRepository;>;"}, {"acc": 2, "nme": "versionConstraint", "dsc": "Lorg/eclipse/aether/version/VersionConstraint;"}]}, "org/eclipse/aether/transfer/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/transfer/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/repository/AuthenticationSelector.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/repository/AuthenticationSelector", "super": "java/lang/Object", "mthds": [{"nme": "getAuthentication", "acc": 1025, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/repository/Authentication;"}], "flds": []}, "org/eclipse/aether/version/VersionRange$Bound.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/version/VersionRange$Bound", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/version/Version;Z)V"}, {"nme": "getVersion", "acc": 1, "dsc": "()Lorg/eclipse/aether/version/Version;"}, {"nme": "isInclusive", "acc": 1, "dsc": "()Z"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "version", "dsc": "Lorg/eclipse/aether/version/Version;"}, {"acc": 18, "nme": "inclusive", "dsc": "Z"}]}, "org/eclipse/aether/repository/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/repository/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/collection/DependencyCollectionContext.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/collection/DependencyCollectionContext", "super": "java/lang/Object", "mthds": [{"nme": "getSession", "acc": 1025, "dsc": "()Lorg/eclipse/aether/RepositorySystemSession;"}, {"nme": "getArtifact", "acc": 1025, "dsc": "()Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "getDependency", "acc": 1025, "dsc": "()Lorg/eclipse/aether/graph/Dependency;"}, {"nme": "getManagedDependencies", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;"}], "flds": []}, "org/eclipse/aether/graph/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/graph/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/resolution/ResolutionErrorPolicyRequest.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/resolution/ResolutionErrorPolicyRequest", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/eclipse/aether/repository/RemoteRepository;)V", "sig": "(TT;Lorg/eclipse/aether/repository/RemoteRepository;)V"}, {"nme": "getItem", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}, {"nme": "setItem", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/eclipse/aether/resolution/ResolutionErrorPolicyRequest;", "sig": "(TT;)Lorg/eclipse/aether/resolution/ResolutionErrorPolicyRequest<TT;>;"}, {"nme": "getRepository", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/RemoteRepository;"}, {"nme": "setRepository", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/resolution/ResolutionErrorPolicyRequest;", "sig": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/resolution/ResolutionErrorPolicyRequest<TT;>;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "item", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;", "sig": "TT;"}, {"acc": 2, "nme": "repository", "dsc": "Lorg/eclipse/aether/repository/RemoteRepository;"}]}, "org/eclipse/aether/RepositoryCache.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/RepositoryCache", "super": "java/lang/Object", "mthds": [{"nme": "put", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "get", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON>ja<PERSON>/lang/Object;"}], "flds": []}, "org/eclipse/aether/resolution/ArtifactDescriptorPolicy.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/resolution/ArtifactDescriptorPolicy", "super": "java/lang/Object", "mthds": [{"nme": "getPolicy", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/ArtifactDescriptorPolicyRequest;)I"}], "flds": [{"acc": 25, "nme": "STRICT", "dsc": "I", "val": 0}, {"acc": 25, "nme": "IGNORE_MISSING", "dsc": "I", "val": 1}, {"acc": 25, "nme": "IGNORE_INVALID", "dsc": "I", "val": 2}, {"acc": 25, "nme": "IGNORE_ERRORS", "dsc": "I", "val": 3}]}, "org/eclipse/aether/repository/LocalMetadataRegistration.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/repository/LocalMetadataRegistration", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/util/Collection;)V", "sig": "(Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/util/Collection<Ljava/lang/String;>;)V"}, {"nme": "getMetadata", "acc": 1, "dsc": "()Lorg/eclipse/aether/metadata/Metadata;"}, {"nme": "setMetadata", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;)Lorg/eclipse/aether/repository/LocalMetadataRegistration;"}, {"nme": "getRepository", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/RemoteRepository;"}, {"nme": "setRepository", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/repository/LocalMetadataRegistration;"}, {"nme": "getContexts", "acc": 1, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Ljava/lang/String;>;"}, {"nme": "setContexts", "acc": 1, "dsc": "(Lja<PERSON>/util/Collection;)Lorg/eclipse/aether/repository/LocalMetadataRegistration;", "sig": "(Ljava/util/Collection<Ljava/lang/String;>;)Lorg/eclipse/aether/repository/LocalMetadataRegistration;"}], "flds": [{"acc": 2, "nme": "metadata", "dsc": "Lorg/eclipse/aether/metadata/Metadata;"}, {"acc": 2, "nme": "repository", "dsc": "Lorg/eclipse/aether/repository/RemoteRepository;"}, {"acc": 2, "nme": "contexts", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Ljava/lang/String;>;"}]}, "org/eclipse/aether/resolution/ArtifactResult.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/resolution/ArtifactResult", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/resolution/ArtifactRequest;)V"}, {"nme": "getRequest", "acc": 1, "dsc": "()Lorg/eclipse/aether/resolution/ArtifactRequest;"}, {"nme": "getArtifact", "acc": 1, "dsc": "()Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "setArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/resolution/ArtifactResult;"}, {"nme": "getExceptions", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/Exception;>;"}, {"nme": "addException", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Exception;)Lorg/eclipse/aether/resolution/ArtifactResult;"}, {"nme": "getRepository", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/ArtifactRepository;"}, {"nme": "setRepository", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/ArtifactRepository;)Lorg/eclipse/aether/resolution/ArtifactResult;"}, {"nme": "getLocalArtifactResult", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/LocalArtifactResult;"}, {"nme": "setLocalArtifactResult", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/LocalArtifactResult;)V"}, {"nme": "isResolved", "acc": 1, "dsc": "()Z"}, {"nme": "isMissing", "acc": 1, "dsc": "()Z"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "request", "dsc": "Lorg/eclipse/aether/resolution/ArtifactRequest;"}, {"acc": 2, "nme": "exceptions", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/Exception;>;"}, {"acc": 2, "nme": "artifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}, {"acc": 2, "nme": "repository", "dsc": "Lorg/eclipse/aether/repository/ArtifactRepository;"}, {"acc": 2, "nme": "localArtifactResult", "dsc": "Lorg/eclipse/aether/repository/LocalArtifactResult;"}]}, "org/eclipse/aether/metadata/DefaultMetadata.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/metadata/DefaultMetadata", "super": "org/eclipse/aether/metadata/AbstractMetadata", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/eclipse/aether/metadata/Metadata$Nature;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;Lorg/eclipse/aether/metadata/Metadata$Nature;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/eclipse/aether/metadata/Metadata$Nature;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/eclipse/aether/metadata/Metadata$Nature;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/eclipse/aether/metadata/Metadata$Nature;Ljava/io/File;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/eclipse/aether/metadata/Metadata$Nature;Ljava/util/Map;Ljava/io/File;)V", "sig": "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/eclipse/aether/metadata/Metadata$Nature;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;Ljava/io/File;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/eclipse/aether/metadata/Metadata$Nature;Ljava/io/File;Ljava/util/Map;)V", "sig": "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/eclipse/aether/metadata/Metadata$Nature;Ljava/io/File;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "emptify", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getGroupId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getArtifactId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getType", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getNature", "acc": 1, "dsc": "()Lorg/eclipse/aether/metadata/Metadata$Nature;"}, {"nme": "getFile", "acc": 1, "dsc": "()Ljava/io/File;"}, {"nme": "getProperties", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}], "flds": [{"acc": 18, "nme": "groupId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "artifactId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "type", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "nature", "dsc": "Lorg/eclipse/aether/metadata/Metadata$Nature;"}, {"acc": 18, "nme": "file", "dsc": "Ljava/io/File;"}, {"acc": 18, "nme": "properties", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}]}, "org/eclipse/aether/deployment/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/deployment/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/version/VersionScheme.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/version/VersionScheme", "super": "java/lang/Object", "mthds": [{"nme": "parseVersion", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/version/Version;", "exs": ["org/eclipse/aether/version/InvalidVersionSpecificationException"]}, {"nme": "parseVersionRange", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/version/VersionRange;", "exs": ["org/eclipse/aether/version/InvalidVersionSpecificationException"]}, {"nme": "parseVersionConstraint", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/version/VersionConstraint;", "exs": ["org/eclipse/aether/version/InvalidVersionSpecificationException"]}], "flds": []}, "org/eclipse/aether/collection/DependencyCollectionException.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/collection/DependencyCollectionException", "super": "org/eclipse/aether/RepositoryException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/CollectResult;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/CollectResult;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/CollectResult;Lja<PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "getResult", "acc": 1, "dsc": "()Lorg/eclipse/aether/collection/CollectResult;"}, {"nme": "getSource", "acc": 10, "dsc": "(Lorg/eclipse/aether/collection/CollectResult;)Lja<PERSON>/lang/String;"}, {"nme": "getCause", "acc": 10, "dsc": "(Lorg/eclipse/aether/collection/CollectResult;)<PERSON><PERSON><PERSON>/lang/Throwable;"}], "flds": [{"acc": 146, "nme": "result", "dsc": "Lorg/eclipse/aether/collection/CollectResult;"}]}, "org/eclipse/aether/repository/MirrorSelector.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/repository/MirrorSelector", "super": "java/lang/Object", "mthds": [{"nme": "getMirror", "acc": 1025, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/repository/RemoteRepository;"}], "flds": []}, "org/eclipse/aether/transfer/TransferCancelledException.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/transfer/TransferCancelledException", "super": "org/eclipse/aether/RepositoryException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}], "flds": []}, "org/eclipse/aether/repository/RemoteRepository.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/repository/RemoteRepository", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository$Builder;)V"}, {"nme": "copy", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Ljava/util/List;", "sig": "(Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;)Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"nme": "getId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getContentType", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getUrl", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getProtocol", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getHost", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getPolicy", "acc": 1, "dsc": "(Z)Lorg/eclipse/aether/repository/RepositoryPolicy;"}, {"nme": "getProxy", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/Proxy;"}, {"nme": "getAuthentication", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/Authentication;"}, {"nme": "getMirroredRepositories", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"nme": "isRepositoryManager", "acc": 1, "dsc": "()Z"}, {"nme": "isBlocked", "acc": 1, "dsc": "()Z"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "hash", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "URL_PATTERN", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 18, "nme": "id", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "type", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "url", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "host", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "protocol", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "releasePolicy", "dsc": "Lorg/eclipse/aether/repository/RepositoryPolicy;"}, {"acc": 18, "nme": "snapshotPolicy", "dsc": "Lorg/eclipse/aether/repository/RepositoryPolicy;"}, {"acc": 18, "nme": "proxy", "dsc": "Lorg/eclipse/aether/repository/Proxy;"}, {"acc": 18, "nme": "authentication", "dsc": "Lorg/eclipse/aether/repository/Authentication;"}, {"acc": 18, "nme": "mirroredRepositories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"acc": 18, "nme": "repositoryManager", "dsc": "Z"}, {"acc": 18, "nme": "blocked", "dsc": "Z"}]}, "org/eclipse/aether/transform/FileTransformerManager.class": {"ver": 52, "acc": 132609, "nme": "org/eclipse/aether/transform/FileTransformerManager", "super": "java/lang/Object", "mthds": [{"nme": "getTransformersForArtifact", "acc": 1025, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Ljava/util/Collection;", "sig": "(Lorg/eclipse/aether/artifact/Artifact;)Ljava/util/Collection<Lorg/eclipse/aether/transform/FileTransformer;>;"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "org/eclipse/aether/graph/DependencyCycle.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/graph/DependencyCycle", "super": "java/lang/Object", "mthds": [{"nme": "getPrecedingDependencies", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;"}, {"nme": "getCyclicDependencies", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;"}], "flds": []}, "org/eclipse/aether/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/transfer/AbstractTransferListener.class": {"ver": 52, "acc": 1057, "nme": "org/eclipse/aether/transfer/AbstractTransferListener", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "transferInitiated", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V", "exs": ["org/eclipse/aether/transfer/TransferCancelledException"]}, {"nme": "transferStarted", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V", "exs": ["org/eclipse/aether/transfer/TransferCancelledException"]}, {"nme": "transferProgressed", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V", "exs": ["org/eclipse/aether/transfer/TransferCancelledException"]}, {"nme": "transferCorrupted", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V", "exs": ["org/eclipse/aether/transfer/TransferCancelledException"]}, {"nme": "transferSucceeded", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V"}, {"nme": "transferFailed", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V"}], "flds": []}, "org/eclipse/aether/installation/InstallResult.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/installation/InstallResult", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/installation/InstallRequest;)V"}, {"nme": "getRequest", "acc": 1, "dsc": "()Lorg/eclipse/aether/installation/InstallRequest;"}, {"nme": "getArtifacts", "acc": 1, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Lorg/eclipse/aether/artifact/Artifact;>;"}, {"nme": "setArtifacts", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Lorg/eclipse/aether/installation/InstallResult;", "sig": "(Ljava/util/Collection<Lorg/eclipse/aether/artifact/Artifact;>;)Lorg/eclipse/aether/installation/InstallResult;"}, {"nme": "addArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/installation/InstallResult;"}, {"nme": "getMetadata", "acc": 1, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Lorg/eclipse/aether/metadata/Metadata;>;"}, {"nme": "setMetadata", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Lorg/eclipse/aether/installation/InstallResult;", "sig": "(Ljava/util/Collection<Lorg/eclipse/aether/metadata/Metadata;>;)Lorg/eclipse/aether/installation/InstallResult;"}, {"nme": "addMetadata", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;)Lorg/eclipse/aether/installation/InstallResult;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "request", "dsc": "Lorg/eclipse/aether/installation/InstallRequest;"}, {"acc": 2, "nme": "artifacts", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lorg/eclipse/aether/artifact/Artifact;>;"}, {"acc": 2, "nme": "metadata", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lorg/eclipse/aether/metadata/Metadata;>;"}]}, "org/eclipse/aether/ConfigurationProperties.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/ConfigurationProperties", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "PREFIX_AETHER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether."}, {"acc": 26, "nme": "PREFIX_CONNECTOR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.connector."}, {"acc": 25, "nme": "PREFIX_PRIORITY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.priority."}, {"acc": 25, "nme": "IMPLICIT_PRIORITIES", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.priority.implicit"}, {"acc": 25, "nme": "DEFAULT_IMPLICIT_PRIORITIES", "dsc": "Z", "val": 0}, {"acc": 25, "nme": "INTERACTIVE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.interactive"}, {"acc": 25, "nme": "DEFAULT_INTERACTIVE", "dsc": "Z", "val": 0}, {"acc": 25, "nme": "USER_AGENT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.connector.userAgent"}, {"acc": 25, "nme": "DEFAULT_USER_AGENT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "Aether"}, {"acc": 25, "nme": "CONNECT_TIMEOUT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.connector.connectTimeout"}, {"acc": 25, "nme": "DEFAULT_CONNECT_TIMEOUT", "dsc": "I", "val": 10000}, {"acc": 25, "nme": "REQUEST_TIMEOUT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.connector.requestTimeout"}, {"acc": 25, "nme": "DEFAULT_REQUEST_TIMEOUT", "dsc": "I", "val": 1800000}, {"acc": 25, "nme": "HTTP_HEADERS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.connector.http.headers"}, {"acc": 25, "nme": "HTTP_CREDENTIAL_ENCODING", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.connector.http.credentialEncoding"}, {"acc": 25, "nme": "DEFAULT_HTTP_CREDENTIAL_ENCODING", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "ISO-8859-1"}, {"acc": 25, "nme": "HTTP_RETRY_HANDLER_COUNT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.connector.http.retryHandler.count"}, {"acc": 25, "nme": "DEFAULT_HTTP_RETRY_HANDLER_COUNT", "dsc": "I", "val": 3}, {"acc": 25, "nme": "HTTP_RETRY_HANDLER_INTERVAL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.connector.http.retryHandler.interval"}, {"acc": 25, "nme": "DEFAULT_HTTP_RETRY_HANDLER_INTERVAL", "dsc": "J", "val": 5000}, {"acc": 25, "nme": "HTTP_RETRY_HANDLER_INTERVAL_MAX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.connector.http.retryHandler.intervalMax"}, {"acc": 25, "nme": "DEFAULT_HTTP_RETRY_HANDLER_INTERVAL_MAX", "dsc": "J", "val": 300000}, {"acc": 25, "nme": "HTTP_RETRY_HANDLER_SERVICE_UNAVAILABLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.connector.http.retryHandler.serviceUnavailable"}, {"acc": 25, "nme": "DEFAULT_HTTP_RETRY_HANDLER_SERVICE_UNAVAILABLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "429,503"}, {"acc": 25, "nme": "HTTP_PREEMPTIVE_AUTH", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.connector.http.preemptiveAuth"}, {"acc": 25, "nme": "DEFAULT_HTTP_PREEMPTIVE_AUTH", "dsc": "Z", "val": 0}, {"acc": 25, "nme": "HTTP_REUSE_CONNECTIONS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.connector.http.reuseConnections"}, {"acc": 25, "nme": "DEFAULT_HTTP_REUSE_CONNECTIONS", "dsc": "Z", "val": 1}, {"acc": 25, "nme": "HTTP_CONNECTION_MAX_TTL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.connector.http.connectionMaxTtl"}, {"acc": 25, "nme": "DEFAULT_HTTP_CONNECTION_MAX_TTL", "dsc": "I", "val": 300}, {"acc": 25, "nme": "HTTP_MAX_CONNECTIONS_PER_ROUTE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.connector.http.maxConnectionsPerRoute"}, {"acc": 25, "nme": "DEFAULT_HTTP_MAX_CONNECTIONS_PER_ROUTE", "dsc": "I", "val": 50}, {"acc": 25, "nme": "HTTP_EXPECT_CONTINUE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.connector.http.expectContinue"}, {"acc": 25, "nme": "HTTPS_SECURITY_MODE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.connector.https.securityMode"}, {"acc": 25, "nme": "HTTPS_SECURITY_MODE_DEFAULT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "default"}, {"acc": 25, "nme": "HTTPS_SECURITY_MODE_INSECURE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "insecure"}, {"acc": 25, "nme": "PERSISTED_CHECKSUMS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.connector.persistedChecksums"}, {"acc": 25, "nme": "DEFAULT_PERSISTED_CHECKSUMS", "dsc": "Z", "val": 1}]}, "org/eclipse/aether/collection/DependencyManagement.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/collection/DependencyManagement", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setVersion", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/collection/DependencyManagement;"}, {"nme": "getScope", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setScope", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/collection/DependencyManagement;"}, {"nme": "getOptional", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/<PERSON>an;"}, {"nme": "setOptional", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Boolean;)Lorg/eclipse/aether/collection/DependencyManagement;"}, {"nme": "getExclusions", "acc": 1, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Lorg/eclipse/aether/graph/Exclusion;>;"}, {"nme": "setExclusions", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Lorg/eclipse/aether/collection/DependencyManagement;", "sig": "(Lja<PERSON>/util/Collection<Lorg/eclipse/aether/graph/Exclusion;>;)Lorg/eclipse/aether/collection/DependencyManagement;"}, {"nme": "getProperties", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "setProperties", "acc": 1, "dsc": "(<PERSON>java/util/Map;)Lorg/eclipse/aether/collection/DependencyManagement;", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)Lorg/eclipse/aether/collection/DependencyManagement;"}], "flds": [{"acc": 2, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "scope", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "optional", "dsc": "<PERSON><PERSON><PERSON>/lang/Boolean;"}, {"acc": 2, "nme": "exclusions", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lorg/eclipse/aether/graph/Exclusion;>;"}, {"acc": 2, "nme": "properties", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}]}, "org/eclipse/aether/DefaultSessionData.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/DefaultSessionData", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "set", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "set", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "get", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "computeIfAbsent", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/util/function/Supplier;)Ljava/lang/Object;", "sig": "(L<PERSON><PERSON>/lang/Object;Lja<PERSON>/util/function/Supplier<Ljava/lang/Object;>;)Ljava/lang/Object;"}, {"nme": "lambda$computeIfAbsent$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Supplier;L<PERSON><PERSON>/lang/Object;)Ljava/lang/Object;"}], "flds": [{"acc": 18, "nme": "data", "dsc": "Ljava/util/concurrent/ConcurrentMap;", "sig": "Ljava/util/concurrent/ConcurrentMap<Ljava/lang/Object;Ljava/lang/Object;>;"}]}, "org/eclipse/aether/deployment/DeploymentException.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/deployment/DeploymentException", "super": "org/eclipse/aether/RepositoryException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}], "flds": []}, "org/eclipse/aether/resolution/DependencyResolutionException.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/resolution/DependencyResolutionException", "super": "org/eclipse/aether/RepositoryException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/resolution/DependencyResult;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/resolution/DependencyResult;<PERSON>ja<PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "getMessage", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getResult", "acc": 1, "dsc": "()Lorg/eclipse/aether/resolution/DependencyResult;"}], "flds": [{"acc": 146, "nme": "result", "dsc": "Lorg/eclipse/aether/resolution/DependencyResult;"}]}, "org/eclipse/aether/artifact/ArtifactTypeRegistry.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/artifact/ArtifactTypeRegistry", "super": "java/lang/Object", "mthds": [{"nme": "get", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/artifact/ArtifactType;"}], "flds": []}, "org/eclipse/aether/resolution/ArtifactResolutionException.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/resolution/ArtifactResolutionException", "super": "org/eclipse/aether/RepositoryException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(<PERSON><PERSON><PERSON>/util/List<Lorg/eclipse/aether/resolution/ArtifactResult;>;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/lang/String;)V", "sig": "(Lja<PERSON>/util/List<Lorg/eclipse/aether/resolution/ArtifactResult;>;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V", "sig": "(L<PERSON><PERSON>/util/List<Lorg/eclipse/aether/resolution/ArtifactResult;>;Ljava/lang/String;Ljava/lang/Throwable;)V"}, {"nme": "getResults", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/resolution/ArtifactResult;>;"}, {"nme": "getResult", "acc": 1, "dsc": "()Lorg/eclipse/aether/resolution/ArtifactResult;"}, {"nme": "getMessage", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Ljava/lang/String;", "sig": "(Lja<PERSON>/util/List<+Lorg/eclipse/aether/resolution/ArtifactResult;>;)Ljava/lang/String;"}, {"nme": "getCause", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)<PERSON><PERSON><PERSON>/lang/Throwable;", "sig": "(Lja<PERSON>/util/List<+Lorg/eclipse/aether/resolution/ArtifactResult;>;)Ljava/lang/Throwable;"}], "flds": [{"acc": 146, "nme": "results", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/resolution/ArtifactResult;>;"}]}, "org/eclipse/aether/resolution/ArtifactRequest.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/resolution/ArtifactRequest", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/util/List;Ljava/lang/String;)V", "sig": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)V"}, {"nme": "getArtifact", "acc": 1, "dsc": "()Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "setArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/resolution/ArtifactRequest;"}, {"nme": "getDependencyNode", "acc": 1, "dsc": "()Lorg/eclipse/aether/graph/DependencyNode;"}, {"nme": "setDependencyNode", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Lorg/eclipse/aether/resolution/ArtifactRequest;"}, {"nme": "getRepositories", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"nme": "setRepositories", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/eclipse/aether/resolution/ArtifactRequest;", "sig": "(Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;)Lorg/eclipse/aether/resolution/ArtifactRequest;"}, {"nme": "addRepository", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/resolution/ArtifactRequest;"}, {"nme": "getRequestContext", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setRequestContext", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/resolution/ArtifactRequest;"}, {"nme": "getTrace", "acc": 1, "dsc": "()Lorg/eclipse/aether/RequestTrace;"}, {"nme": "setTrace", "acc": 1, "dsc": "(Lorg/eclipse/aether/RequestTrace;)Lorg/eclipse/aether/resolution/ArtifactRequest;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "artifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}, {"acc": 2, "nme": "node", "dsc": "Lorg/eclipse/aether/graph/DependencyNode;"}, {"acc": 2, "nme": "repositories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"acc": 2, "nme": "context", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "trace", "dsc": "Lorg/eclipse/aether/RequestTrace;"}]}, "org/eclipse/aether/transfer/NoTransporterException.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/transfer/NoTransporterException", "super": "org/eclipse/aether/RepositoryException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;<PERSON>ja<PERSON>/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;Ljava/lang/Throwable;)V"}, {"nme": "toMessage", "acc": 10, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Ljava/lang/String;"}, {"nme": "getRepository", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/RemoteRepository;"}], "flds": [{"acc": 146, "nme": "repository", "dsc": "Lorg/eclipse/aether/repository/RemoteRepository;"}]}, "org/eclipse/aether/RequestTrace.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/RequestTrace", "super": "java/lang/Object", "mthds": [{"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 9, "dsc": "(Lorg/eclipse/aether/RequestTrace;<PERSON><PERSON><PERSON>/lang/Object;)Lorg/eclipse/aether/RequestTrace;"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "<init>", "acc": 4, "dsc": "(Lorg/eclipse/aether/RequestTrace;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "getData", "acc": 17, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getParent", "acc": 17, "dsc": "()Lorg/eclipse/aether/RequestTrace;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/eclipse/aether/RequestTrace;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "parent", "dsc": "Lorg/eclipse/aether/RequestTrace;"}, {"acc": 18, "nme": "data", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "org/eclipse/aether/installation/InstallationException.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/installation/InstallationException", "super": "org/eclipse/aether/RepositoryException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}], "flds": []}, "org/eclipse/aether/RepositoryEvent.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/RepositoryEvent", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/RepositoryEvent$Builder;)V"}, {"nme": "getType", "acc": 1, "dsc": "()Lorg/eclipse/aether/RepositoryEvent$EventType;"}, {"nme": "getSession", "acc": 1, "dsc": "()Lorg/eclipse/aether/RepositorySystemSession;"}, {"nme": "getArtifact", "acc": 1, "dsc": "()Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "getMetadata", "acc": 1, "dsc": "()Lorg/eclipse/aether/metadata/Metadata;"}, {"nme": "getFile", "acc": 1, "dsc": "()Ljava/io/File;"}, {"nme": "getRepository", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/ArtifactRepository;"}, {"nme": "getException", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Exception;"}, {"nme": "getExceptions", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/Exception;>;"}, {"nme": "getTrace", "acc": 1, "dsc": "()Lorg/eclipse/aether/RequestTrace;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "type", "dsc": "Lorg/eclipse/aether/RepositoryEvent$EventType;"}, {"acc": 18, "nme": "session", "dsc": "Lorg/eclipse/aether/RepositorySystemSession;"}, {"acc": 18, "nme": "artifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}, {"acc": 18, "nme": "metadata", "dsc": "Lorg/eclipse/aether/metadata/Metadata;"}, {"acc": 18, "nme": "repository", "dsc": "Lorg/eclipse/aether/repository/ArtifactRepository;"}, {"acc": 18, "nme": "file", "dsc": "Ljava/io/File;"}, {"acc": 18, "nme": "exceptions", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/Exception;>;"}, {"acc": 18, "nme": "trace", "dsc": "Lorg/eclipse/aether/RequestTrace;"}]}, "org/eclipse/aether/resolution/ArtifactDescriptorException.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/resolution/ArtifactDescriptorException", "super": "org/eclipse/aether/RepositoryException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;Lja<PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;Lja<PERSON>/lang/String;Ljava/lang/Throwable;)V"}, {"nme": "getResult", "acc": 1, "dsc": "()Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;"}, {"nme": "getCause", "acc": 10, "dsc": "(Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;)Ljava/lang/Throwable;"}], "flds": [{"acc": 146, "nme": "result", "dsc": "Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;"}]}, "org/eclipse/aether/artifact/AbstractArtifact.class": {"ver": 52, "acc": 1057, "nme": "org/eclipse/aether/artifact/AbstractArtifact", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "isSnapshot", "acc": 1, "dsc": "()Z"}, {"nme": "isSnapshot", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "getBaseVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toBaseVersion", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "newInstance", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/util/Map;Ljava/io/File;)Lorg/eclipse/aether/artifact/Artifact;", "sig": "(Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;Ljava/io/File;)Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "setVersion", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "setFile", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "setProperties", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Lorg/eclipse/aether/artifact/Artifact;", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "getProperty", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "copyProperties", "acc": 12, "dsc": "(Ljava/util/Map;)Ljava/util/Map;", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "hash", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "SNAPSHOT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "SNAPSHOT"}, {"acc": 26, "nme": "SNAPSHOT_TIMESTAMP", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}]}, "org/eclipse/aether/deployment/DeployRequest.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/deployment/DeployRequest", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getArtifacts", "acc": 1, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Lorg/eclipse/aether/artifact/Artifact;>;"}, {"nme": "setArtifacts", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Lorg/eclipse/aether/deployment/DeployRequest;", "sig": "(Ljava/util/Collection<Lorg/eclipse/aether/artifact/Artifact;>;)Lorg/eclipse/aether/deployment/DeployRequest;"}, {"nme": "addArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/deployment/DeployRequest;"}, {"nme": "getMetadata", "acc": 1, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Lorg/eclipse/aether/metadata/Metadata;>;"}, {"nme": "setMetadata", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Lorg/eclipse/aether/deployment/DeployRequest;", "sig": "(Ljava/util/Collection<Lorg/eclipse/aether/metadata/Metadata;>;)Lorg/eclipse/aether/deployment/DeployRequest;"}, {"nme": "addMetadata", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;)Lorg/eclipse/aether/deployment/DeployRequest;"}, {"nme": "getRepository", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/RemoteRepository;"}, {"nme": "setRepository", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/deployment/DeployRequest;"}, {"nme": "getTrace", "acc": 1, "dsc": "()Lorg/eclipse/aether/RequestTrace;"}, {"nme": "setTrace", "acc": 1, "dsc": "(Lorg/eclipse/aether/RequestTrace;)Lorg/eclipse/aether/deployment/DeployRequest;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "artifacts", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lorg/eclipse/aether/artifact/Artifact;>;"}, {"acc": 2, "nme": "metadata", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lorg/eclipse/aether/metadata/Metadata;>;"}, {"acc": 2, "nme": "repository", "dsc": "Lorg/eclipse/aether/repository/RemoteRepository;"}, {"acc": 2, "nme": "trace", "dsc": "Lorg/eclipse/aether/RequestTrace;"}]}, "org/eclipse/aether/collection/CollectResult.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/collection/CollectResult", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/CollectRequest;)V"}, {"nme": "getRequest", "acc": 1, "dsc": "()Lorg/eclipse/aether/collection/CollectRequest;"}, {"nme": "getExceptions", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/Exception;>;"}, {"nme": "addException", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Exception;)Lorg/eclipse/aether/collection/CollectResult;"}, {"nme": "getCycles", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/graph/DependencyCycle;>;"}, {"nme": "addCycle", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyCycle;)Lorg/eclipse/aether/collection/CollectResult;"}, {"nme": "getRoot", "acc": 1, "dsc": "()Lorg/eclipse/aether/graph/DependencyNode;"}, {"nme": "setRoot", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Lorg/eclipse/aether/collection/CollectResult;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "request", "dsc": "Lorg/eclipse/aether/collection/CollectRequest;"}, {"acc": 2, "nme": "exceptions", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/Exception;>;"}, {"acc": 2, "nme": "cycles", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/graph/DependencyCycle;>;"}, {"acc": 2, "nme": "root", "dsc": "Lorg/eclipse/aether/graph/DependencyNode;"}]}, "org/eclipse/aether/graph/Exclusion.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/graph/Exclusion", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getGroupId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getArtifactId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getClassifier", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getExtension", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "groupId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "artifactId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "classifier", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "extension", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/eclipse/aether/AbstractRepositoryListener.class": {"ver": 52, "acc": 1057, "nme": "org/eclipse/aether/AbstractRepositoryListener", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "artifactDeployed", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "artifactDeploying", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "artifactDescriptorInvalid", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "artifactDescriptorMissing", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "artifactDownloaded", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "artifactDownloading", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "artifactInstalled", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "artifactInstalling", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "artifactResolved", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "artifactResolving", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "metadataDeployed", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "metadataDeploying", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "metadataDownloaded", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "metadataDownloading", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "metadataInstalled", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "metadataInstalling", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "metadataInvalid", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "metadataResolved", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "metadataResolving", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}], "flds": []}, "org/eclipse/aether/metadata/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/metadata/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/repository/LocalRepository.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/repository/LocalRepository", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;Ljava/lang/String;)V"}, {"nme": "getContentType", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getBasedir", "acc": 1, "dsc": "()Ljava/io/File;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "hash", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}], "flds": [{"acc": 18, "nme": "basedir", "dsc": "Ljava/io/File;"}, {"acc": 18, "nme": "type", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/eclipse/aether/transfer/ArtifactNotFoundException.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/transfer/ArtifactNotFoundException", "super": "org/eclipse/aether/transfer/ArtifactTransferException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/RemoteRepository;)V"}, {"nme": "getMessage", "acc": 10, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/RemoteRepository;)Ljava/lang/String;"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/RemoteRepository;Lja<PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/RemoteRepository;<PERSON>ja<PERSON>/lang/String;Z)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;Ljava/lang/Throwable;)V"}], "flds": []}, "org/eclipse/aether/artifact/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/artifact/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/collection/DependencyGraphTransformationContext.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/collection/DependencyGraphTransformationContext", "super": "java/lang/Object", "mthds": [{"nme": "getSession", "acc": 1025, "dsc": "()Lorg/eclipse/aether/RepositorySystemSession;"}, {"nme": "get", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "put", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "org/eclipse/aether/RepositorySystemSession.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/RepositorySystemSession", "super": "java/lang/Object", "mthds": [{"nme": "isOffline", "acc": 1025, "dsc": "()Z"}, {"nme": "isIgnoreArtifactDescriptorRepositories", "acc": 1025, "dsc": "()Z"}, {"nme": "getResolutionErrorPolicy", "acc": 1025, "dsc": "()Lorg/eclipse/aether/resolution/ResolutionErrorPolicy;"}, {"nme": "getArtifactDescriptorPolicy", "acc": 1025, "dsc": "()Lorg/eclipse/aether/resolution/ArtifactDescriptorPolicy;"}, {"nme": "getChecksumPolicy", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getUpdatePolicy", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLocalRepository", "acc": 1025, "dsc": "()Lorg/eclipse/aether/repository/LocalRepository;"}, {"nme": "getLocalRepositoryManager", "acc": 1025, "dsc": "()Lorg/eclipse/aether/repository/LocalRepositoryManager;"}, {"nme": "getWorkspaceReader", "acc": 1025, "dsc": "()Lorg/eclipse/aether/repository/WorkspaceReader;"}, {"nme": "getRepositoryListener", "acc": 1025, "dsc": "()Lorg/eclipse/aether/RepositoryListener;"}, {"nme": "getTransferListener", "acc": 1025, "dsc": "()Lorg/eclipse/aether/transfer/TransferListener;"}, {"nme": "getSystemProperties", "acc": 1025, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "getUserProperties", "acc": 1025, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "getConfigProperties", "acc": 1025, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;"}, {"nme": "getMirrorSelector", "acc": 1025, "dsc": "()Lorg/eclipse/aether/repository/MirrorSelector;"}, {"nme": "getProxySelector", "acc": 1025, "dsc": "()Lorg/eclipse/aether/repository/ProxySelector;"}, {"nme": "getAuthenticationSelector", "acc": 1025, "dsc": "()Lorg/eclipse/aether/repository/AuthenticationSelector;"}, {"nme": "getArtifactTypeRegistry", "acc": 1025, "dsc": "()Lorg/eclipse/aether/artifact/ArtifactTypeRegistry;"}, {"nme": "getDependencyTraverser", "acc": 1025, "dsc": "()Lorg/eclipse/aether/collection/DependencyTraverser;"}, {"nme": "getDependencyManager", "acc": 1025, "dsc": "()Lorg/eclipse/aether/collection/DependencyManager;"}, {"nme": "getDependencySelector", "acc": 1025, "dsc": "()Lorg/eclipse/aether/collection/DependencySelector;"}, {"nme": "getVersionFilter", "acc": 1025, "dsc": "()Lorg/eclipse/aether/collection/VersionFilter;"}, {"nme": "getDependencyGraphTransformer", "acc": 1025, "dsc": "()Lorg/eclipse/aether/collection/DependencyGraphTransformer;"}, {"nme": "getData", "acc": 1025, "dsc": "()Lorg/eclipse/aether/SessionData;"}, {"nme": "getCache", "acc": 1025, "dsc": "()Lorg/eclipse/aether/RepositoryCache;"}, {"nme": "getFileTransformerManager", "acc": 132097, "dsc": "()Lorg/eclipse/aether/transform/FileTransformerManager;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}], "flds": []}, "org/eclipse/aether/repository/Proxy.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/repository/Proxy", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;ILorg/eclipse/aether/repository/Authentication;)V"}, {"nme": "getType", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getHost", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getPort", "acc": 1, "dsc": "()I"}, {"nme": "getAuthentication", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/Authentication;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "hash", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}], "flds": [{"acc": 25, "nme": "TYPE_HTTP", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "http"}, {"acc": 25, "nme": "TYPE_HTTPS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "https"}, {"acc": 18, "nme": "type", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "host", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "port", "dsc": "I"}, {"acc": 18, "nme": "auth", "dsc": "Lorg/eclipse/aether/repository/Authentication;"}]}, "org/eclipse/aether/resolution/ArtifactDescriptorPolicyRequest.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/resolution/ArtifactDescriptorPolicyRequest", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/lang/String;)V"}, {"nme": "getArtifact", "acc": 1, "dsc": "()Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "setArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/resolution/ArtifactDescriptorPolicyRequest;"}, {"nme": "getRequestContext", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setRequestContext", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/resolution/ArtifactDescriptorPolicyRequest;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "artifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}, {"acc": 2, "nme": "context", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/eclipse/aether/collection/CollectRequest.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/collection/CollectRequest", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/Dependency;Lja<PERSON>/util/List;)V", "sig": "(Lorg/eclipse/aether/graph/Dependency;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/Dependency;Ljava/util/List;Ljava/util/List;)V", "sig": "(Lorg/eclipse/aether/graph/Dependency;Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;)V"}, {"nme": "getRootArtifact", "acc": 1, "dsc": "()Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "setRootArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/collection/CollectRequest;"}, {"nme": "getRoot", "acc": 1, "dsc": "()Lorg/eclipse/aether/graph/Dependency;"}, {"nme": "setRoot", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/Dependency;)Lorg/eclipse/aether/collection/CollectRequest;"}, {"nme": "getDependencies", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;"}, {"nme": "setDependencies", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/eclipse/aether/collection/CollectRequest;", "sig": "(<PERSON><PERSON><PERSON>/util/List<Lorg/eclipse/aether/graph/Dependency;>;)Lorg/eclipse/aether/collection/CollectRequest;"}, {"nme": "addDependency", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/Dependency;)Lorg/eclipse/aether/collection/CollectRequest;"}, {"nme": "getManagedDependencies", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;"}, {"nme": "setManagedDependencies", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/eclipse/aether/collection/CollectRequest;", "sig": "(<PERSON><PERSON><PERSON>/util/List<Lorg/eclipse/aether/graph/Dependency;>;)Lorg/eclipse/aether/collection/CollectRequest;"}, {"nme": "addManagedDependency", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/Dependency;)Lorg/eclipse/aether/collection/CollectRequest;"}, {"nme": "getRepositories", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"nme": "setRepositories", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/eclipse/aether/collection/CollectRequest;", "sig": "(Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;)Lorg/eclipse/aether/collection/CollectRequest;"}, {"nme": "addRepository", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/collection/CollectRequest;"}, {"nme": "getRequestContext", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setRequestContext", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/collection/CollectRequest;"}, {"nme": "getTrace", "acc": 1, "dsc": "()Lorg/eclipse/aether/RequestTrace;"}, {"nme": "setTrace", "acc": 1, "dsc": "(Lorg/eclipse/aether/RequestTrace;)Lorg/eclipse/aether/collection/CollectRequest;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "rootArtifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}, {"acc": 2, "nme": "root", "dsc": "Lorg/eclipse/aether/graph/Dependency;"}, {"acc": 2, "nme": "dependencies", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;"}, {"acc": 2, "nme": "managedDependencies", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;"}, {"acc": 2, "nme": "repositories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"acc": 2, "nme": "context", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "trace", "dsc": "Lorg/eclipse/aether/RequestTrace;"}]}, "org/eclipse/aether/repository/RepositoryPolicy.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/repository/RepositoryPolicy", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "isEnabled", "acc": 1, "dsc": "()Z"}, {"nme": "getUpdatePolicy", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getChecksumPolicy", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 25, "nme": "UPDATE_POLICY_NEVER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "never"}, {"acc": 25, "nme": "UPDATE_POLICY_ALWAYS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "always"}, {"acc": 25, "nme": "UPDATE_POLICY_DAILY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "daily"}, {"acc": 25, "nme": "UPDATE_POLICY_INTERVAL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "interval"}, {"acc": 25, "nme": "CHECKSUM_POLICY_FAIL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "fail"}, {"acc": 25, "nme": "CHECKSUM_POLICY_WARN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "warn"}, {"acc": 25, "nme": "CHECKSUM_POLICY_IGNORE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "ignore"}, {"acc": 18, "nme": "enabled", "dsc": "Z"}, {"acc": 18, "nme": "updatePolicy", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "checksumPolicy", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/eclipse/aether/DefaultRepositorySystemSession.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/DefaultRepositorySystemSession", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)V"}, {"nme": "isOffline", "acc": 1, "dsc": "()Z"}, {"nme": "setOffline", "acc": 1, "dsc": "(Z)Lorg/eclipse/aether/DefaultRepositorySystemSession;"}, {"nme": "isIgnoreArtifactDescriptorRepositories", "acc": 1, "dsc": "()Z"}, {"nme": "setIgnoreArtifactDescriptorRepositories", "acc": 1, "dsc": "(Z)Lorg/eclipse/aether/DefaultRepositorySystemSession;"}, {"nme": "getResolutionErrorPolicy", "acc": 1, "dsc": "()Lorg/eclipse/aether/resolution/ResolutionErrorPolicy;"}, {"nme": "setResolutionErrorPolicy", "acc": 1, "dsc": "(Lorg/eclipse/aether/resolution/ResolutionErrorPolicy;)Lorg/eclipse/aether/DefaultRepositorySystemSession;"}, {"nme": "getArtifactDescriptorPolicy", "acc": 1, "dsc": "()Lorg/eclipse/aether/resolution/ArtifactDescriptorPolicy;"}, {"nme": "setArtifactDescriptorPolicy", "acc": 1, "dsc": "(Lorg/eclipse/aether/resolution/ArtifactDescriptorPolicy;)Lorg/eclipse/aether/DefaultRepositorySystemSession;"}, {"nme": "getChecksumPolicy", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setChecksumPolicy", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/DefaultRepositorySystemSession;"}, {"nme": "getUpdatePolicy", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setUpdatePolicy", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/DefaultRepositorySystemSession;"}, {"nme": "getLocalRepository", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/LocalRepository;"}, {"nme": "getLocalRepositoryManager", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/LocalRepositoryManager;"}, {"nme": "setLocalRepositoryManager", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/LocalRepositoryManager;)Lorg/eclipse/aether/DefaultRepositorySystemSession;"}, {"nme": "getFileTransformerManager", "acc": 1, "dsc": "()Lorg/eclipse/aether/transform/FileTransformerManager;"}, {"nme": "setFileTransformerManager", "acc": 1, "dsc": "(Lorg/eclipse/aether/transform/FileTransformerManager;)Lorg/eclipse/aether/DefaultRepositorySystemSession;"}, {"nme": "getWorkspaceReader", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/WorkspaceReader;"}, {"nme": "setWorkspaceReader", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/WorkspaceReader;)Lorg/eclipse/aether/DefaultRepositorySystemSession;"}, {"nme": "getRepositoryListener", "acc": 1, "dsc": "()Lorg/eclipse/aether/RepositoryListener;"}, {"nme": "setRepositoryListener", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryListener;)Lorg/eclipse/aether/DefaultRepositorySystemSession;"}, {"nme": "getTransferListener", "acc": 1, "dsc": "()Lorg/eclipse/aether/transfer/TransferListener;"}, {"nme": "setTransferListener", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferListener;)Lorg/eclipse/aether/DefaultRepositorySystemSession;"}, {"nme": "copySafe", "acc": 2, "dsc": "(Ljava/util/Map;Ljava/lang/Class;)Ljava/util/Map;", "sig": "<T:Ljava/lang/Object;>(Ljava/util/Map<**>;Ljava/lang/Class<TT;>;)Ljava/util/Map<Ljava/lang/String;TT;>;"}, {"nme": "getSystemProperties", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "setSystemProperties", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Lorg/eclipse/aether/DefaultRepositorySystemSession;", "sig": "(<PERSON><PERSON><PERSON>/util/Map<**>;)Lorg/eclipse/aether/DefaultRepositorySystemSession;"}, {"nme": "setSystemProperty", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;)Lorg/eclipse/aether/DefaultRepositorySystemSession;"}, {"nme": "getUserProperties", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "setUserProperties", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Lorg/eclipse/aether/DefaultRepositorySystemSession;", "sig": "(<PERSON><PERSON><PERSON>/util/Map<**>;)Lorg/eclipse/aether/DefaultRepositorySystemSession;"}, {"nme": "setUserProperty", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;)Lorg/eclipse/aether/DefaultRepositorySystemSession;"}, {"nme": "getConfigProperties", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;"}, {"nme": "setConfigProperties", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Lorg/eclipse/aether/DefaultRepositorySystemSession;", "sig": "(<PERSON><PERSON><PERSON>/util/Map<**>;)Lorg/eclipse/aether/DefaultRepositorySystemSession;"}, {"nme": "setConfigProperty", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)Lorg/eclipse/aether/DefaultRepositorySystemSession;"}, {"nme": "getMirrorSelector", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/MirrorSelector;"}, {"nme": "setMirrorSelector", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/MirrorSelector;)Lorg/eclipse/aether/DefaultRepositorySystemSession;"}, {"nme": "getProxySelector", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/ProxySelector;"}, {"nme": "setProxySelector", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/ProxySelector;)Lorg/eclipse/aether/DefaultRepositorySystemSession;"}, {"nme": "getAuthenticationSelector", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/AuthenticationSelector;"}, {"nme": "setAuthenticationSelector", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/AuthenticationSelector;)Lorg/eclipse/aether/DefaultRepositorySystemSession;"}, {"nme": "getArtifactTypeRegistry", "acc": 1, "dsc": "()Lorg/eclipse/aether/artifact/ArtifactTypeRegistry;"}, {"nme": "setArtifactTypeRegistry", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/ArtifactTypeRegistry;)Lorg/eclipse/aether/DefaultRepositorySystemSession;"}, {"nme": "getDependencyTraverser", "acc": 1, "dsc": "()Lorg/eclipse/aether/collection/DependencyTraverser;"}, {"nme": "setDependencyTraverser", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/DependencyTraverser;)Lorg/eclipse/aether/DefaultRepositorySystemSession;"}, {"nme": "getDependencyManager", "acc": 1, "dsc": "()Lorg/eclipse/aether/collection/DependencyManager;"}, {"nme": "setDependencyManager", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/DependencyManager;)Lorg/eclipse/aether/DefaultRepositorySystemSession;"}, {"nme": "getDependencySelector", "acc": 1, "dsc": "()Lorg/eclipse/aether/collection/DependencySelector;"}, {"nme": "setDependencySelector", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/DependencySelector;)Lorg/eclipse/aether/DefaultRepositorySystemSession;"}, {"nme": "getVersionFilter", "acc": 1, "dsc": "()Lorg/eclipse/aether/collection/VersionFilter;"}, {"nme": "setVersionFilter", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/VersionFilter;)Lorg/eclipse/aether/DefaultRepositorySystemSession;"}, {"nme": "getDependencyGraphTransformer", "acc": 1, "dsc": "()Lorg/eclipse/aether/collection/DependencyGraphTransformer;"}, {"nme": "setDependencyGraphTransformer", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/DependencyGraphTransformer;)Lorg/eclipse/aether/DefaultRepositorySystemSession;"}, {"nme": "getData", "acc": 1, "dsc": "()Lorg/eclipse/aether/SessionData;"}, {"nme": "setData", "acc": 1, "dsc": "(Lorg/eclipse/aether/SessionData;)Lorg/eclipse/aether/DefaultRepositorySystemSession;"}, {"nme": "getCache", "acc": 1, "dsc": "()Lorg/eclipse/aether/RepositoryCache;"}, {"nme": "setCache", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryCache;)Lorg/eclipse/aether/DefaultRepositorySystemSession;"}, {"nme": "setReadOnly", "acc": 1, "dsc": "()V"}, {"nme": "verifyStateForMutation", "acc": 2, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "readOnly", "dsc": "Z"}, {"acc": 2, "nme": "offline", "dsc": "Z"}, {"acc": 2, "nme": "ignoreArtifactDescriptorRepositories", "dsc": "Z"}, {"acc": 2, "nme": "resolutionErrorPolicy", "dsc": "Lorg/eclipse/aether/resolution/ResolutionErrorPolicy;"}, {"acc": 2, "nme": "artifactDescriptorPolicy", "dsc": "Lorg/eclipse/aether/resolution/ArtifactDescriptorPolicy;"}, {"acc": 2, "nme": "checksumPolicy", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "updatePolicy", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "localRepositoryManager", "dsc": "Lorg/eclipse/aether/repository/LocalRepositoryManager;"}, {"acc": 2, "nme": "fileTransformerManager", "dsc": "Lorg/eclipse/aether/transform/FileTransformerManager;"}, {"acc": 2, "nme": "workspaceReader", "dsc": "Lorg/eclipse/aether/repository/WorkspaceReader;"}, {"acc": 2, "nme": "repositoryListener", "dsc": "Lorg/eclipse/aether/RepositoryListener;"}, {"acc": 2, "nme": "transferListener", "dsc": "Lorg/eclipse/aether/transfer/TransferListener;"}, {"acc": 2, "nme": "systemProperties", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"acc": 2, "nme": "systemPropertiesView", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"acc": 2, "nme": "userProperties", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"acc": 2, "nme": "userPropertiesView", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"acc": 2, "nme": "configProperties", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;"}, {"acc": 2, "nme": "configProper<PERSON>View", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;"}, {"acc": 2, "nme": "mirrorSelector", "dsc": "Lorg/eclipse/aether/repository/MirrorSelector;"}, {"acc": 2, "nme": "proxySelector", "dsc": "Lorg/eclipse/aether/repository/ProxySelector;"}, {"acc": 2, "nme": "authenticationSelector", "dsc": "Lorg/eclipse/aether/repository/AuthenticationSelector;"}, {"acc": 2, "nme": "artifactTypeRegistry", "dsc": "Lorg/eclipse/aether/artifact/ArtifactTypeRegistry;"}, {"acc": 2, "nme": "dependencyTraverser", "dsc": "Lorg/eclipse/aether/collection/DependencyTraverser;"}, {"acc": 2, "nme": "dependencyManager", "dsc": "Lorg/eclipse/aether/collection/DependencyManager;"}, {"acc": 2, "nme": "dependencySelector", "dsc": "Lorg/eclipse/aether/collection/DependencySelector;"}, {"acc": 2, "nme": "versionFilter", "dsc": "Lorg/eclipse/aether/collection/VersionFilter;"}, {"acc": 2, "nme": "dependencyGraphTransformer", "dsc": "Lorg/eclipse/aether/collection/DependencyGraphTransformer;"}, {"acc": 2, "nme": "data", "dsc": "Lorg/eclipse/aether/SessionData;"}, {"acc": 2, "nme": "cache", "dsc": "Lorg/eclipse/aether/RepositoryCache;"}]}, "org/eclipse/aether/metadata/AbstractMetadata.class": {"ver": 52, "acc": 1057, "nme": "org/eclipse/aether/metadata/AbstractMetadata", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "newInstance", "acc": 2, "dsc": "(Ljava/util/Map;Ljava/io/File;)Lorg/eclipse/aether/metadata/Metadata;", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;Ljava/io/File;)Lorg/eclipse/aether/metadata/Metadata;"}, {"nme": "setFile", "acc": 1, "dsc": "(Ljava/io/File;)Lorg/eclipse/aether/metadata/Metadata;"}, {"nme": "setProperties", "acc": 1, "dsc": "(Ljava/util/Map;)Lorg/eclipse/aether/metadata/Metadata;", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)Lorg/eclipse/aether/metadata/Metadata;"}, {"nme": "getProperty", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "copyProperties", "acc": 12, "dsc": "(Ljava/util/Map;)Ljava/util/Map;", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "hash", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}], "flds": []}, "org/eclipse/aether/graph/DependencyVisitor.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/graph/DependencyVisitor", "super": "java/lang/Object", "mthds": [{"nme": "visitEnter", "acc": 1025, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Z"}, {"nme": "visitLeave", "acc": 1025, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Z"}], "flds": []}, "org/eclipse/aether/RepositorySystem.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/RepositorySystem", "super": "java/lang/Object", "mthds": [{"nme": "resolveVersionRange", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/VersionRangeRequest;)Lorg/eclipse/aether/resolution/VersionRangeResult;", "exs": ["org/eclipse/aether/resolution/VersionRangeResolutionException"]}, {"nme": "resolveVersion", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/VersionRequest;)Lorg/eclipse/aether/resolution/VersionResult;", "exs": ["org/eclipse/aether/resolution/VersionResolutionException"]}, {"nme": "readArtifactDescriptor", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/ArtifactDescriptorRequest;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;", "exs": ["org/eclipse/aether/resolution/ArtifactDescriptorException"]}, {"nme": "collectDependencies", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/collection/CollectRequest;)Lorg/eclipse/aether/collection/CollectResult;", "exs": ["org/eclipse/aether/collection/DependencyCollectionException"]}, {"nme": "resolveDependencies", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/DependencyRequest;)Lorg/eclipse/aether/resolution/DependencyResult;", "exs": ["org/eclipse/aether/resolution/DependencyResolutionException"]}, {"nme": "resolveArtifact", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/ArtifactRequest;)Lorg/eclipse/aether/resolution/ArtifactResult;", "exs": ["org/eclipse/aether/resolution/ArtifactResolutionException"]}, {"nme": "resolveArtifacts", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection;)Ljava/util/List;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection<+Lorg/eclipse/aether/resolution/ArtifactRequest;>;)Ljava/util/List<Lorg/eclipse/aether/resolution/ArtifactResult;>;", "exs": ["org/eclipse/aether/resolution/ArtifactResolutionException"]}, {"nme": "resolveMetadata", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection;)Ljava/util/List;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection<+Lorg/eclipse/aether/resolution/MetadataRequest;>;)Ljava/util/List<Lorg/eclipse/aether/resolution/MetadataResult;>;"}, {"nme": "install", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/installation/InstallRequest;)Lorg/eclipse/aether/installation/InstallResult;", "exs": ["org/eclipse/aether/installation/InstallationException"]}, {"nme": "deploy", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/deployment/DeployRequest;)Lorg/eclipse/aether/deployment/DeployResult;", "exs": ["org/eclipse/aether/deployment/DeploymentException"]}, {"nme": "newLocalRepositoryManager", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/LocalRepository;)Lorg/eclipse/aether/repository/LocalRepositoryManager;"}, {"nme": "newSyncContext", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Z)Lorg/eclipse/aether/SyncContext;"}, {"nme": "newResolutionRepositories", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lja<PERSON>/util/List;)Ljava/util/List;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;)Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"nme": "newDeploymentRepository", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/repository/RemoteRepository;"}, {"nme": "addOnSystemEndedHandler", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Runnable;)V"}, {"nme": "shutdown", "acc": 1025, "dsc": "()V"}], "flds": []}, "org/eclipse/aether/repository/LocalArtifactRequest.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/repository/LocalArtifactRequest", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/util/List;Ljava/lang/String;)V", "sig": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;Ljava/lang/String;)V"}, {"nme": "getArtifact", "acc": 1, "dsc": "()Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "setArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/repository/LocalArtifactRequest;"}, {"nme": "getContext", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setContext", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/repository/LocalArtifactRequest;"}, {"nme": "getRepositories", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"nme": "setRepositories", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/eclipse/aether/repository/LocalArtifactRequest;", "sig": "(Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;)Lorg/eclipse/aether/repository/LocalArtifactRequest;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "artifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}, {"acc": 2, "nme": "context", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "repositories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}]}, "org/eclipse/aether/transfer/MetadataNotFoundException.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/transfer/MetadataNotFoundException", "super": "org/eclipse/aether/transfer/MetadataTransferException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/LocalRepository;)V"}, {"nme": "getString", "acc": 10, "dsc": "(Ljava/lang/String;Lorg/eclipse/aether/repository/LocalRepository;)Ljava/lang/String;"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/RemoteRepository;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;Z)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;Ljava/lang/Throwable;)V"}], "flds": []}, "org/eclipse/aether/collection/VersionFilter.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/collection/VersionFilter", "super": "java/lang/Object", "mthds": [{"nme": "filterVersions", "acc": 1025, "dsc": "(Lorg/eclipse/aether/collection/VersionFilter$VersionFilterContext;)V", "exs": ["org/eclipse/aether/RepositoryException"]}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(Lorg/eclipse/aether/collection/DependencyCollectionContext;)Lorg/eclipse/aether/collection/VersionFilter;"}], "flds": []}, "org/eclipse/aether/resolution/VersionRequest.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/resolution/VersionRequest", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/util/List;Ljava/lang/String;)V", "sig": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;Ljava/lang/String;)V"}, {"nme": "getArtifact", "acc": 1, "dsc": "()Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "setArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/resolution/VersionRequest;"}, {"nme": "getRepositories", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"nme": "setRepositories", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/eclipse/aether/resolution/VersionRequest;", "sig": "(Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;)Lorg/eclipse/aether/resolution/VersionRequest;"}, {"nme": "addRepository", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/resolution/VersionRequest;"}, {"nme": "getRequestContext", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setRequestContext", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/resolution/VersionRequest;"}, {"nme": "getTrace", "acc": 1, "dsc": "()Lorg/eclipse/aether/RequestTrace;"}, {"nme": "setTrace", "acc": 1, "dsc": "(Lorg/eclipse/aether/RequestTrace;)Lorg/eclipse/aether/resolution/VersionRequest;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "artifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}, {"acc": 2, "nme": "repositories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"acc": 2, "nme": "context", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "trace", "dsc": "Lorg/eclipse/aether/RequestTrace;"}]}, "org/eclipse/aether/artifact/DefaultArtifact.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/artifact/DefaultArtifact", "super": "org/eclipse/aether/artifact/AbstractArtifact", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/util/Map;)V", "sig": "(Lja<PERSON>/lang/String;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "get", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/eclipse/aether/artifact/ArtifactType;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>java/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/Map;Lorg/eclipse/aether/artifact/ArtifactType;)V", "sig": "(Lja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;Lorg/eclipse/aether/artifact/ArtifactType;)V"}, {"nme": "merge", "acc": 10, "dsc": "(Ljava/util/Map;Ljava/util/Map;)Ljava/util/Map;", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/Map;Ljava/io/File;)V", "sig": "(Lja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;Ljava/io/File;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/io/File;Ljava/util/Map;)V", "sig": "(Lja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/io/File;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "emptify", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getGroupId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getArtifactId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getClassifier", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getExtension", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getFile", "acc": 1, "dsc": "()Ljava/io/File;"}, {"nme": "getProperties", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "COORDINATE_PATTERN", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 18, "nme": "groupId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "artifactId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "classifier", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "extension", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "file", "dsc": "Ljava/io/File;"}, {"acc": 18, "nme": "properties", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}]}, "org/eclipse/aether/transfer/TransferEvent$EventType.class": {"ver": 52, "acc": 16433, "nme": "org/eclipse/aether/transfer/TransferEvent$EventType", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lorg/eclipse/aether/transfer/TransferEvent$EventType;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/transfer/TransferEvent$EventType;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Lorg/eclipse/aether/transfer/TransferEvent$EventType;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "INITIATED", "dsc": "Lorg/eclipse/aether/transfer/TransferEvent$EventType;"}, {"acc": 16409, "nme": "STARTED", "dsc": "Lorg/eclipse/aether/transfer/TransferEvent$EventType;"}, {"acc": 16409, "nme": "PROGRESSED", "dsc": "Lorg/eclipse/aether/transfer/TransferEvent$EventType;"}, {"acc": 16409, "nme": "CORRUPTED", "dsc": "Lorg/eclipse/aether/transfer/TransferEvent$EventType;"}, {"acc": 16409, "nme": "SUCCEEDED", "dsc": "Lorg/eclipse/aether/transfer/TransferEvent$EventType;"}, {"acc": 16409, "nme": "FAILED", "dsc": "Lorg/eclipse/aether/transfer/TransferEvent$EventType;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lorg/eclipse/aether/transfer/TransferEvent$EventType;"}]}, "org/eclipse/aether/repository/LocalArtifactRegistration.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/repository/LocalArtifactRegistration", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/util/Collection;)V", "sig": "(Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/util/Collection<Ljava/lang/String;>;)V"}, {"nme": "getArtifact", "acc": 1, "dsc": "()Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "setArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/repository/LocalArtifactRegistration;"}, {"nme": "getRepository", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/RemoteRepository;"}, {"nme": "setRepository", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/repository/LocalArtifactRegistration;"}, {"nme": "getContexts", "acc": 1, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Ljava/lang/String;>;"}, {"nme": "setContexts", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Lorg/eclipse/aether/repository/LocalArtifactRegistration;", "sig": "(Ljava/util/Collection<Ljava/lang/String;>;)Lorg/eclipse/aether/repository/LocalArtifactRegistration;"}], "flds": [{"acc": 2, "nme": "artifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}, {"acc": 2, "nme": "repository", "dsc": "Lorg/eclipse/aether/repository/RemoteRepository;"}, {"acc": 2, "nme": "contexts", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Ljava/lang/String;>;"}]}, "org/eclipse/aether/transfer/TransferEvent$RequestType.class": {"ver": 52, "acc": 16433, "nme": "org/eclipse/aether/transfer/TransferEvent$RequestType", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lorg/eclipse/aether/transfer/TransferEvent$RequestType;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/transfer/TransferEvent$RequestType;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Lorg/eclipse/aether/transfer/TransferEvent$RequestType;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "GET", "dsc": "Lorg/eclipse/aether/transfer/TransferEvent$RequestType;"}, {"acc": 16409, "nme": "GET_EXISTENCE", "dsc": "Lorg/eclipse/aether/transfer/TransferEvent$RequestType;"}, {"acc": 16409, "nme": "PUT", "dsc": "Lorg/eclipse/aether/transfer/TransferEvent$RequestType;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lorg/eclipse/aether/transfer/TransferEvent$RequestType;"}]}, "org/eclipse/aether/artifact/Artifact.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/artifact/Artifact", "super": "java/lang/Object", "mthds": [{"nme": "getGroupId", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getArtifactId", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getVersion", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setVersion", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "getBaseVersion", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isSnapshot", "acc": 1025, "dsc": "()Z"}, {"nme": "getClassifier", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getExtension", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getFile", "acc": 1025, "dsc": "()Ljava/io/File;"}, {"nme": "setFile", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "getProperty", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getProperties", "acc": 1025, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "setProperties", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Lorg/eclipse/aether/artifact/Artifact;", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)Lorg/eclipse/aether/artifact/Artifact;"}], "flds": []}, "org/eclipse/aether/repository/LocalArtifactResult.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/repository/LocalArtifactResult", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/LocalArtifactRequest;)V"}, {"nme": "getRequest", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/LocalArtifactRequest;"}, {"nme": "getFile", "acc": 1, "dsc": "()Ljava/io/File;"}, {"nme": "setFile", "acc": 1, "dsc": "(Ljava/io/File;)Lorg/eclipse/aether/repository/LocalArtifactResult;"}, {"nme": "isAvailable", "acc": 1, "dsc": "()Z"}, {"nme": "setAvailable", "acc": 1, "dsc": "(Z)Lorg/eclipse/aether/repository/LocalArtifactResult;"}, {"nme": "getRepository", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/RemoteRepository;"}, {"nme": "setRepository", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/repository/LocalArtifactResult;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "request", "dsc": "Lorg/eclipse/aether/repository/LocalArtifactRequest;"}, {"acc": 2, "nme": "file", "dsc": "Ljava/io/File;"}, {"acc": 2, "nme": "available", "dsc": "Z"}, {"acc": 2, "nme": "repository", "dsc": "Lorg/eclipse/aether/repository/RemoteRepository;"}]}, "org/eclipse/aether/repository/WorkspaceRepository.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/repository/WorkspaceRepository", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "getContentType", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "type", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "key", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "org/eclipse/aether/repository/Authentication.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/repository/Authentication", "super": "java/lang/Object", "mthds": [{"nme": "fill", "acc": 1025, "dsc": "(Lorg/eclipse/aether/repository/AuthenticationContext;Ljava/lang/String;Ljava/util/Map;)V", "sig": "(Lorg/eclipse/aether/repository/AuthenticationContext;Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "digest", "acc": 1025, "dsc": "(Lorg/eclipse/aether/repository/AuthenticationDigest;)V"}], "flds": []}, "org/eclipse/aether/resolution/VersionRangeRequest.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/resolution/VersionRangeRequest", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/util/List;Ljava/lang/String;)V", "sig": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;Ljava/lang/String;)V"}, {"nme": "getArtifact", "acc": 1, "dsc": "()Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "setArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/resolution/VersionRangeRequest;"}, {"nme": "getRepositories", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"nme": "setRepositories", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/eclipse/aether/resolution/VersionRangeRequest;", "sig": "(Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;)Lorg/eclipse/aether/resolution/VersionRangeRequest;"}, {"nme": "addRepository", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/resolution/VersionRangeRequest;"}, {"nme": "getRequestContext", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setRequestContext", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/resolution/VersionRangeRequest;"}, {"nme": "getTrace", "acc": 1, "dsc": "()Lorg/eclipse/aether/RequestTrace;"}, {"nme": "setTrace", "acc": 1, "dsc": "(Lorg/eclipse/aether/RequestTrace;)Lorg/eclipse/aether/resolution/VersionRangeRequest;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "artifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}, {"acc": 2, "nme": "repositories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"acc": 2, "nme": "context", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "trace", "dsc": "Lorg/eclipse/aether/RequestTrace;"}]}, "org/eclipse/aether/transfer/NoRepositoryLayoutException.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/transfer/NoRepositoryLayoutException", "super": "org/eclipse/aether/RepositoryException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;<PERSON>ja<PERSON>/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;Ljava/lang/Throwable;)V"}, {"nme": "toMessage", "acc": 10, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Ljava/lang/String;"}, {"nme": "getRepository", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/RemoteRepository;"}], "flds": [{"acc": 146, "nme": "repository", "dsc": "Lorg/eclipse/aether/repository/RemoteRepository;"}]}, "org/eclipse/aether/transfer/TransferEvent$Builder.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/transfer/TransferEvent$Builder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/transfer/TransferResource;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent$Builder;)V"}, {"nme": "copy", "acc": 1, "dsc": "()Lorg/eclipse/aether/transfer/TransferEvent$Builder;"}, {"nme": "resetType", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent$EventType;)Lorg/eclipse/aether/transfer/TransferEvent$Builder;"}, {"nme": "setType", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent$EventType;)Lorg/eclipse/aether/transfer/TransferEvent$Builder;"}, {"nme": "setRequestType", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent$RequestType;)Lorg/eclipse/aether/transfer/TransferEvent$Builder;"}, {"nme": "setTransferredBytes", "acc": 1, "dsc": "(J)Lorg/eclipse/aether/transfer/TransferEvent$Builder;"}, {"nme": "addTransferredBytes", "acc": 1, "dsc": "(J)Lorg/eclipse/aether/transfer/TransferEvent$Builder;"}, {"nme": "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "([BII)Lorg/eclipse/aether/transfer/TransferEvent$Builder;"}, {"nme": "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/nio/ByteBuffer;)Lorg/eclipse/aether/transfer/TransferEvent$Builder;"}, {"nme": "setException", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Exception;)Lorg/eclipse/aether/transfer/TransferEvent$Builder;"}, {"nme": "build", "acc": 1, "dsc": "()Lorg/eclipse/aether/transfer/TransferEvent;"}], "flds": [{"acc": 0, "nme": "type", "dsc": "Lorg/eclipse/aether/transfer/TransferEvent$EventType;"}, {"acc": 0, "nme": "requestType", "dsc": "Lorg/eclipse/aether/transfer/TransferEvent$RequestType;"}, {"acc": 16, "nme": "session", "dsc": "Lorg/eclipse/aether/RepositorySystemSession;"}, {"acc": 16, "nme": "resource", "dsc": "Lorg/eclipse/aether/transfer/TransferResource;"}, {"acc": 0, "nme": "dataBuffer", "dsc": "<PERSON><PERSON><PERSON>/nio/<PERSON>te<PERSON>er;"}, {"acc": 0, "nme": "transferredBytes", "dsc": "J"}, {"acc": 0, "nme": "exception", "dsc": "<PERSON><PERSON><PERSON>/lang/Exception;"}]}, "org/eclipse/aether/installation/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/installation/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/transfer/ArtifactFilteredOutException.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/transfer/ArtifactFilteredOutException", "super": "org/eclipse/aether/transfer/ArtifactNotFoundException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/RemoteRepository;Lja<PERSON>/lang/String;)V"}], "flds": []}, "org/eclipse/aether/collection/DependencyGraphTransformer.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/collection/DependencyGraphTransformer", "super": "java/lang/Object", "mthds": [{"nme": "transformGraph", "acc": 1025, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;Lorg/eclipse/aether/collection/DependencyGraphTransformationContext;)Lorg/eclipse/aether/graph/DependencyNode;", "exs": ["org/eclipse/aether/RepositoryException"]}], "flds": []}, "org/eclipse/aether/resolution/VersionResolutionException.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/resolution/VersionResolutionException", "super": "org/eclipse/aether/RepositoryException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/resolution/VersionResult;)V"}, {"nme": "getMessage", "acc": 10, "dsc": "(Lorg/eclipse/aether/resolution/VersionResult;)Ljava/lang/String;"}, {"nme": "getCause", "acc": 10, "dsc": "(Lorg/eclipse/aether/resolution/VersionResult;)Lja<PERSON>/lang/Throwable;"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/resolution/VersionResult;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/resolution/VersionResult;Ljava/lang/String;Lja<PERSON>/lang/Throwable;)V"}, {"nme": "getResult", "acc": 1, "dsc": "()Lorg/eclipse/aether/resolution/VersionResult;"}], "flds": [{"acc": 146, "nme": "result", "dsc": "Lorg/eclipse/aether/resolution/VersionResult;"}]}, "org/eclipse/aether/deployment/DeployResult.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/deployment/DeployResult", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/deployment/DeployRequest;)V"}, {"nme": "getRequest", "acc": 1, "dsc": "()Lorg/eclipse/aether/deployment/DeployRequest;"}, {"nme": "getArtifacts", "acc": 1, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Lorg/eclipse/aether/artifact/Artifact;>;"}, {"nme": "setArtifacts", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Lorg/eclipse/aether/deployment/DeployResult;", "sig": "(Ljava/util/Collection<Lorg/eclipse/aether/artifact/Artifact;>;)Lorg/eclipse/aether/deployment/DeployResult;"}, {"nme": "addArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/deployment/DeployResult;"}, {"nme": "getMetadata", "acc": 1, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Lorg/eclipse/aether/metadata/Metadata;>;"}, {"nme": "setMetadata", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Lorg/eclipse/aether/deployment/DeployResult;", "sig": "(Ljava/util/Collection<Lorg/eclipse/aether/metadata/Metadata;>;)Lorg/eclipse/aether/deployment/DeployResult;"}, {"nme": "addMetadata", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;)Lorg/eclipse/aether/deployment/DeployResult;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "request", "dsc": "Lorg/eclipse/aether/deployment/DeployRequest;"}, {"acc": 2, "nme": "artifacts", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lorg/eclipse/aether/artifact/Artifact;>;"}, {"acc": 2, "nme": "metadata", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lorg/eclipse/aether/metadata/Metadata;>;"}]}, "org/eclipse/aether/resolution/DependencyRequest.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/resolution/DependencyRequest", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;Lorg/eclipse/aether/graph/DependencyFilter;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/CollectRequest;Lorg/eclipse/aether/graph/DependencyFilter;)V"}, {"nme": "getRoot", "acc": 1, "dsc": "()Lorg/eclipse/aether/graph/DependencyNode;"}, {"nme": "setRoot", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Lorg/eclipse/aether/resolution/DependencyRequest;"}, {"nme": "getCollectRequest", "acc": 1, "dsc": "()Lorg/eclipse/aether/collection/CollectRequest;"}, {"nme": "setCollectRequest", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/CollectRequest;)Lorg/eclipse/aether/resolution/DependencyRequest;"}, {"nme": "getFilter", "acc": 1, "dsc": "()Lorg/eclipse/aether/graph/DependencyFilter;"}, {"nme": "setFilter", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyFilter;)Lorg/eclipse/aether/resolution/DependencyRequest;"}, {"nme": "getTrace", "acc": 1, "dsc": "()Lorg/eclipse/aether/RequestTrace;"}, {"nme": "setTrace", "acc": 1, "dsc": "(Lorg/eclipse/aether/RequestTrace;)Lorg/eclipse/aether/resolution/DependencyRequest;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "root", "dsc": "Lorg/eclipse/aether/graph/DependencyNode;"}, {"acc": 2, "nme": "collectRequest", "dsc": "Lorg/eclipse/aether/collection/CollectRequest;"}, {"acc": 2, "nme": "filter", "dsc": "Lorg/eclipse/aether/graph/DependencyFilter;"}, {"acc": 2, "nme": "trace", "dsc": "Lorg/eclipse/aether/RequestTrace;"}]}, "org/eclipse/aether/graph/DependencyNode.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/graph/DependencyNode", "super": "java/lang/Object", "mthds": [{"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(<PERSON><PERSON><PERSON>/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;)V"}, {"nme": "getDependency", "acc": 1025, "dsc": "()Lorg/eclipse/aether/graph/Dependency;"}, {"nme": "getArtifact", "acc": 1025, "dsc": "()Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "setArtifact", "acc": 1025, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)V"}, {"nme": "getRelocations", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Lorg/eclipse/aether/artifact/Artifact;>;"}, {"nme": "getAliases", "acc": 1025, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<+Lorg/eclipse/aether/artifact/Artifact;>;"}, {"nme": "getVersionConstraint", "acc": 1025, "dsc": "()Lorg/eclipse/aether/version/VersionConstraint;"}, {"nme": "getVersion", "acc": 1025, "dsc": "()Lorg/eclipse/aether/version/Version;"}, {"nme": "setScope", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setOptional", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/<PERSON>;)V"}, {"nme": "getManagedBits", "acc": 1025, "dsc": "()I"}, {"nme": "getRepositories", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"nme": "getRequestContext", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setRequestContext", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getData", "acc": 1025, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<**>;"}, {"nme": "setData", "acc": 1025, "dsc": "(Ljava/util/Map;)V", "sig": "(Ljava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "setData", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "accept", "acc": 1025, "dsc": "(Lorg/eclipse/aether/graph/DependencyVisitor;)Z"}], "flds": [{"acc": 25, "nme": "MANAGED_VERSION", "dsc": "I", "val": 1}, {"acc": 25, "nme": "MANAGED_SCOPE", "dsc": "I", "val": 2}, {"acc": 25, "nme": "MANAGED_OPTIONAL", "dsc": "I", "val": 4}, {"acc": 25, "nme": "MANAGED_PROPERTIES", "dsc": "I", "val": 8}, {"acc": 25, "nme": "MANAGED_EXCLUSIONS", "dsc": "I", "val": 16}]}, "org/eclipse/aether/repository/AuthenticationContext.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/repository/AuthenticationContext", "super": "java/lang/Object", "mthds": [{"nme": "forRepository", "acc": 9, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/repository/AuthenticationContext;"}, {"nme": "forProxy", "acc": 9, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/repository/AuthenticationContext;"}, {"nme": "newInstance", "acc": 10, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/repository/Proxy;Lorg/eclipse/aether/repository/Authentication;)Lorg/eclipse/aether/repository/AuthenticationContext;"}, {"nme": "<init>", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/repository/Proxy;Lorg/eclipse/aether/repository/Authentication;)V"}, {"nme": "getSession", "acc": 1, "dsc": "()Lorg/eclipse/aether/RepositorySystemSession;"}, {"nme": "getRepository", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/RemoteRepository;"}, {"nme": "getProxy", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/Proxy;"}, {"nme": "get", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "get", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Class;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/String;Ljava/lang/Class<TT;>;)TT;"}, {"nme": "get", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;Ljava/lang/Class;)<PERSON>java/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;Ljava/lang/Class<TT;>;)TT;"}, {"nme": "convert", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Class;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Object;Ljava/lang/Class<TT;>;)TT;"}, {"nme": "put", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "close", "acc": 1, "dsc": "()V"}, {"nme": "close", "acc": 9, "dsc": "(Lorg/eclipse/aether/repository/AuthenticationContext;)V"}], "flds": [{"acc": 25, "nme": "USERNAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "username"}, {"acc": 25, "nme": "PASSWORD", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "password"}, {"acc": 25, "nme": "NTLM_DOMAIN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "ntlm.domain"}, {"acc": 25, "nme": "NTLM_WORKSTATION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "ntlm.workstation"}, {"acc": 25, "nme": "PRIVATE_KEY_PATH", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "privateKey.path"}, {"acc": 25, "nme": "PRIVATE_KEY_PASSPHRASE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "privateKey.passphrase"}, {"acc": 25, "nme": "HOST_KEY_ACCEPTANCE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "hostKey.acceptance"}, {"acc": 25, "nme": "HOST_KEY_REMOTE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "hostKey.remote"}, {"acc": 25, "nme": "HOST_KEY_LOCAL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "hostKey.local"}, {"acc": 25, "nme": "SSL_CONTEXT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "ssl.context"}, {"acc": 25, "nme": "SSL_HOSTNAME_VERIFIER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "ssl.hostnameVerifier"}, {"acc": 18, "nme": "session", "dsc": "Lorg/eclipse/aether/RepositorySystemSession;"}, {"acc": 18, "nme": "repository", "dsc": "Lorg/eclipse/aether/repository/RemoteRepository;"}, {"acc": 18, "nme": "proxy", "dsc": "Lorg/eclipse/aether/repository/Proxy;"}, {"acc": 18, "nme": "auth", "dsc": "Lorg/eclipse/aether/repository/Authentication;"}, {"acc": 18, "nme": "authData", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;"}, {"acc": 2, "nme": "fillingAuthData", "dsc": "Z"}]}, "org/eclipse/aether/version/InvalidVersionSpecificationException.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/version/InvalidVersionSpecificationException", "super": "org/eclipse/aether/RepositoryException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "getVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/eclipse/aether/transfer/ArtifactTransferException.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/transfer/ArtifactTransferException", "super": "org/eclipse/aether/RepositoryException", "mthds": [{"nme": "getString", "acc": 8, "dsc": "(Ljava/lang/String;Lorg/eclipse/aether/repository/RemoteRepository;)Ljava/lang/String;"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/RemoteRepository;Lja<PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/RemoteRepository;<PERSON>ja<PERSON>/lang/String;Z)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/RemoteRepository;<PERSON>ja<PERSON>/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;Ljava/lang/Throwable;)V"}, {"nme": "getArtifact", "acc": 1, "dsc": "()Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "getRepository", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/RemoteRepository;"}, {"nme": "isFromCache", "acc": 1, "dsc": "()Z"}], "flds": [{"acc": 146, "nme": "artifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}, {"acc": 146, "nme": "repository", "dsc": "Lorg/eclipse/aether/repository/RemoteRepository;"}, {"acc": 18, "nme": "fromCache", "dsc": "Z"}]}, "org/eclipse/aether/DefaultRepositorySystemSession$NullAuthenticationSelector.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/DefaultRepositorySystemSession$NullAuthenticationSelector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "getAuthentication", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/repository/Authentication;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "INSTANCE", "dsc": "Lorg/eclipse/aether/repository/AuthenticationSelector;"}]}, "org/eclipse/aether/collection/VersionFilter$VersionFilterContext.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/collection/VersionFilter$VersionFilterContext", "super": "java/lang/Object", "mthds": [{"nme": "getSession", "acc": 1025, "dsc": "()Lorg/eclipse/aether/RepositorySystemSession;"}, {"nme": "getDependency", "acc": 1025, "dsc": "()Lorg/eclipse/aether/graph/Dependency;"}, {"nme": "getCount", "acc": 1025, "dsc": "()I"}, {"nme": "iterator", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/Iterator;", "sig": "()Ljava/util/Iterator<Lorg/eclipse/aether/version/Version;>;"}, {"nme": "getVersionConstraint", "acc": 1025, "dsc": "()Lorg/eclipse/aether/version/VersionConstraint;"}, {"nme": "getRepository", "acc": 1025, "dsc": "(Lorg/eclipse/aether/version/Version;)Lorg/eclipse/aether/repository/ArtifactRepository;"}, {"nme": "getRepositories", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}], "flds": []}, "org/eclipse/aether/transfer/MetadataTransferException.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/transfer/MetadataTransferException", "super": "org/eclipse/aether/RepositoryException", "mthds": [{"nme": "getString", "acc": 8, "dsc": "(Ljava/lang/String;Lorg/eclipse/aether/repository/RemoteRepository;)Ljava/lang/String;"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;Z)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;Ljava/lang/Throwable;)V"}, {"nme": "getMetadata", "acc": 1, "dsc": "()Lorg/eclipse/aether/metadata/Metadata;"}, {"nme": "getRepository", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/RemoteRepository;"}, {"nme": "isFromCache", "acc": 1, "dsc": "()Z"}], "flds": [{"acc": 146, "nme": "metadata", "dsc": "Lorg/eclipse/aether/metadata/Metadata;"}, {"acc": 146, "nme": "repository", "dsc": "Lorg/eclipse/aether/repository/RemoteRepository;"}, {"acc": 18, "nme": "fromCache", "dsc": "Z"}]}, "org/eclipse/aether/transform/TransformException.class": {"ver": 52, "acc": 131105, "nme": "org/eclipse/aether/transform/TransformException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "org/eclipse/aether/collection/UnsolvableVersionConflictException.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/collection/UnsolvableVersionConflictException", "super": "org/eclipse/aether/RepositoryException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)V", "sig": "(Ljava/util/Collection<+Ljava/util/List<+Lorg/eclipse/aether/graph/DependencyNode;>;>;)V"}, {"nme": "toPaths", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Ljava/lang/String;", "sig": "(Ljava/util/Collection<+Ljava/util/List<+Lorg/eclipse/aether/graph/DependencyNode;>;>;)Ljava/lang/String;"}, {"nme": "to<PERSON><PERSON>", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Ljava/lang/String;", "sig": "(Lja<PERSON>/util/List<+Lorg/eclipse/aether/graph/DependencyNode;>;)Ljava/lang/String;"}, {"nme": "getPaths", "acc": 1, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<+Ljava/util/List<+Lorg/eclipse/aether/graph/DependencyNode;>;>;"}, {"nme": "getVersions", "acc": 1, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Ljava/lang/String;>;"}], "flds": [{"acc": 146, "nme": "versions", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Ljava/lang/String;>;"}, {"acc": 146, "nme": "paths", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<+Ljava/util/List<+Lorg/eclipse/aether/graph/DependencyNode;>;>;"}]}, "org/eclipse/aether/version/VersionRange.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/version/VersionRange", "super": "java/lang/Object", "mthds": [{"nme": "containsVersion", "acc": 1025, "dsc": "(Lorg/eclipse/aether/version/Version;)Z"}, {"nme": "getLowerBound", "acc": 1025, "dsc": "()Lorg/eclipse/aether/version/VersionRange$Bound;"}, {"nme": "getUpperBound", "acc": 1025, "dsc": "()Lorg/eclipse/aether/version/VersionRange$Bound;"}], "flds": []}, "org/eclipse/aether/transfer/ChecksumFailureException.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/transfer/ChecksumFailureException", "super": "org/eclipse/aether/RepositoryException", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "getExpected", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getExpectedKind", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getActual", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isRetryWorthy", "acc": 1, "dsc": "()Z"}], "flds": [{"acc": 18, "nme": "expected", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "expectedKind", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "actual", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "retry<PERSON>orthy", "dsc": "Z"}]}, "org/eclipse/aether/RepositoryEvent$Builder.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/RepositoryEvent$Builder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RepositoryEvent$EventType;)V"}, {"nme": "setArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/RepositoryEvent$Builder;"}, {"nme": "setMetadata", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;)Lorg/eclipse/aether/RepositoryEvent$Builder;"}, {"nme": "setRepository", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/ArtifactRepository;)Lorg/eclipse/aether/RepositoryEvent$Builder;"}, {"nme": "setFile", "acc": 1, "dsc": "(Ljava/io/File;)Lorg/eclipse/aether/RepositoryEvent$Builder;"}, {"nme": "setException", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Exception;)Lorg/eclipse/aether/RepositoryEvent$Builder;"}, {"nme": "setExceptions", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/eclipse/aether/RepositoryEvent$Builder;", "sig": "(Lja<PERSON>/util/List<Ljava/lang/Exception;>;)Lorg/eclipse/aether/RepositoryEvent$Builder;"}, {"nme": "setTrace", "acc": 1, "dsc": "(Lorg/eclipse/aether/RequestTrace;)Lorg/eclipse/aether/RepositoryEvent$Builder;"}, {"nme": "build", "acc": 1, "dsc": "()Lorg/eclipse/aether/RepositoryEvent;"}], "flds": [{"acc": 16, "nme": "type", "dsc": "Lorg/eclipse/aether/RepositoryEvent$EventType;"}, {"acc": 16, "nme": "session", "dsc": "Lorg/eclipse/aether/RepositorySystemSession;"}, {"acc": 0, "nme": "artifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}, {"acc": 0, "nme": "metadata", "dsc": "Lorg/eclipse/aether/metadata/Metadata;"}, {"acc": 0, "nme": "repository", "dsc": "Lorg/eclipse/aether/repository/ArtifactRepository;"}, {"acc": 0, "nme": "file", "dsc": "Ljava/io/File;"}, {"acc": 0, "nme": "exceptions", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/Exception;>;"}, {"acc": 0, "nme": "trace", "dsc": "Lorg/eclipse/aether/RequestTrace;"}]}, "org/eclipse/aether/graph/Dependency$Exclusions.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/graph/Dependency$Exclusions", "super": "java/util/AbstractSet", "mthds": [{"nme": "copy", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)<PERSON>java/util/Set;", "sig": "(Ljava/util/Collection<Lorg/eclipse/aether/graph/Exclusion;>;)Ljava/util/Set<Lorg/eclipse/aether/graph/Exclusion;>;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)V", "sig": "(Ljava/util/Collection<Lorg/eclipse/aether/graph/Exclusion;>;)V"}, {"nme": "iterator", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Iterator;", "sig": "()Ljava/util/Iterator<Lorg/eclipse/aether/graph/Exclusion;>;"}, {"nme": "size", "acc": 1, "dsc": "()I"}, {"nme": "access$000", "acc": 4104, "dsc": "(Lorg/eclipse/aether/graph/Dependency$Exclusions;)[Lorg/eclipse/aether/graph/Exclusion;"}], "flds": [{"acc": 18, "nme": "exclusions", "dsc": "[Lorg/eclipse/aether/graph/Exclusion;"}]}, "org/eclipse/aether/collection/DependencySelector.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/collection/DependencySelector", "super": "java/lang/Object", "mthds": [{"nme": "selectDependency", "acc": 1025, "dsc": "(Lorg/eclipse/aether/graph/Dependency;)Z"}, {"nme": "deriveChildSelector", "acc": 1025, "dsc": "(Lorg/eclipse/aether/collection/DependencyCollectionContext;)Lorg/eclipse/aether/collection/DependencySelector;"}], "flds": []}}}}