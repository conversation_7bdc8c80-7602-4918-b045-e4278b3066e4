{"md5": "1e6595d56938174a4edf8fba5cfe8d52", "sha2": "7baa7f742f288ae2a76b2b98909adaa0a5d28b0e", "sha256": "149eaf7bc0b1f7b25391c73ee4113d1a3e3f272868f8b8e2659b3150cb737c9a", "contents": {"classes": {"net/kyori/adventure/text/serializer/commons/ComponentTreeConstants.class": {"ver": 52, "acc": 49, "nme": "net/kyori/adventure/text/serializer/commons/ComponentTreeConstants", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "TEXT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "text"}, {"acc": 25, "nme": "TRANSLATE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "translate"}, {"acc": 25, "nme": "TRANSLATE_FALLBACK", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "fallback"}, {"acc": 25, "nme": "TRANSLATE_WITH", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "with"}, {"acc": 25, "nme": "SCORE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "score"}, {"acc": 25, "nme": "SCORE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "name"}, {"acc": 25, "nme": "SCORE_OBJECTIVE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "objective"}, {"acc": 131097, "nme": "SCORE_VALUE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "value"}, {"acc": 25, "nme": "SELECTOR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "selector"}, {"acc": 25, "nme": "KEYBIND", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "keybind"}, {"acc": 25, "nme": "EXTRA", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "extra"}, {"acc": 25, "nme": "NBT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "nbt"}, {"acc": 25, "nme": "NBT_INTERPRET", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "interpret"}, {"acc": 25, "nme": "NBT_BLOCK", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "block"}, {"acc": 25, "nme": "NBT_ENTITY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "entity"}, {"acc": 25, "nme": "NBT_STORAGE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "storage"}, {"acc": 25, "nme": "SEPARATOR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "separator"}, {"acc": 25, "nme": "FONT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "font"}, {"acc": 25, "nme": "COLOR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "color"}, {"acc": 25, "nme": "SHADOW_COLOR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "shadow_color"}, {"acc": 25, "nme": "INSERTION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "insertion"}, {"acc": 25, "nme": "CLICK_EVENT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "clickEvent"}, {"acc": 25, "nme": "CLICK_EVENT_ACTION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "action"}, {"acc": 25, "nme": "CLICK_EVENT_VALUE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "value"}, {"acc": 25, "nme": "HOVER_EVENT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "hoverEvent"}, {"acc": 25, "nme": "HOVER_EVENT_ACTION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "action"}, {"acc": 25, "nme": "HOVER_EVENT_CONTENTS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "contents"}, {"acc": 131097, "nme": "HOVER_EVENT_VALUE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "value"}, {"acc": 25, "nme": "SHOW_ENTITY_TYPE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "type"}, {"acc": 25, "nme": "SHOW_ENTITY_ID", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "id"}, {"acc": 25, "nme": "SHOW_ENTITY_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "name"}, {"acc": 25, "nme": "SHOW_ITEM_ID", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "id"}, {"acc": 25, "nme": "SHOW_ITEM_COUNT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "count"}, {"acc": 131097, "nme": "SHOW_ITEM_TAG", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "tag"}, {"acc": 25, "nme": "SHOW_ITEM_COMPONENTS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "components"}]}}}}