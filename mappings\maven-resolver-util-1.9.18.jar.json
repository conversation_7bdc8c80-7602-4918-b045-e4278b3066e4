{"md5": "0e6aa5f372550f4239361df35a53b4a1", "sha2": "5ae9406f188ae4a999c353fce3fd77273797a216", "sha256": "2eb0ea667bc489384478231dda7516407d4b5b22a138077229871de9362a7ae2", "contents": {"classes": {"org/eclipse/aether/util/concurrency/ExecutorUtils.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/concurrency/ExecutorUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "threadPool", "acc": 9, "dsc": "(ILjava/lang/String;)Ljava/util/concurrent/ExecutorService;"}, {"nme": "executor", "acc": 9, "dsc": "(ILjava/lang/String;)Ljava/util/concurrent/Executor;"}, {"nme": "shutdown", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/concurrent/Executor;)V"}, {"nme": "threadCount", "acc": 137, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;I[<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "DIRECT_EXECUTOR", "dsc": "<PERSON><PERSON><PERSON>/util/concurrent/Executor;"}]}, "org/eclipse/aether/util/graph/transformer/ConflictResolver$OptionalitySelector.class": {"ver": 52, "acc": 1057, "nme": "org/eclipse/aether/util/graph/transformer/ConflictResolver$OptionalitySelector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getInstance", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;Lorg/eclipse/aether/collection/DependencyGraphTransformationContext;)Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$OptionalitySelector;", "exs": ["org/eclipse/aether/RepositoryException"]}, {"nme": "selectOptionality", "acc": 1025, "dsc": "(Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictContext;)V", "exs": ["org/eclipse/aether/RepositoryException"]}], "flds": []}, "org/eclipse/aether/util/repository/ChainedLocalRepositoryManager.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/repository/ChainedLocalRepositoryManager", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/LocalRepositoryManager;Ljava/util/List;Z)V", "sig": "(Lorg/eclipse/aether/repository/LocalRepositoryManager;Ljava/util/List<Lorg/eclipse/aether/repository/LocalRepositoryManager;>;Z)V"}, {"nme": "getRepository", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/LocalRepository;"}, {"nme": "getPathForLocalArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lja<PERSON>/lang/String;"}, {"nme": "getPathForRemoteArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "getPathForLocalMetadata", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;)Ljava/lang/String;"}, {"nme": "getPathForRemoteMetadata", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "find", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/LocalArtifactRequest;)Lorg/eclipse/aether/repository/LocalArtifactResult;"}, {"nme": "add", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/LocalArtifactRegistration;)V"}, {"nme": "find", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/LocalMetadataRequest;)Lorg/eclipse/aether/repository/LocalMetadataResult;"}, {"nme": "add", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/LocalMetadataRegistration;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "head", "dsc": "Lorg/eclipse/aether/repository/LocalRepositoryManager;"}, {"acc": 18, "nme": "tail", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/repository/LocalRepositoryManager;>;"}, {"acc": 18, "nme": "ignoreTailAvailability", "dsc": "Z"}]}, "org/eclipse/aether/util/graph/selector/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/util/graph/selector/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/util/filter/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/util/filter/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/util/version/UnionVersionRange.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/version/UnionVersionRange", "super": "java/lang/Object", "mthds": [{"nme": "from", "acc": 137, "dsc": "([Lorg/eclipse/aether/version/VersionRange;)Lorg/eclipse/aether/version/VersionRange;"}, {"nme": "from", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Lorg/eclipse/aether/version/VersionRange;", "sig": "(Lja<PERSON>/util/Collection<+Lorg/eclipse/aether/version/VersionRange;>;)Lorg/eclipse/aether/version/VersionRange;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)V", "sig": "(Ljava/util/Collection<+Lorg/eclipse/aether/version/VersionRange;>;)V"}, {"nme": "containsVersion", "acc": 1, "dsc": "(Lorg/eclipse/aether/version/Version;)Z"}, {"nme": "getLowerBound", "acc": 1, "dsc": "()Lorg/eclipse/aether/version/VersionRange$Bound;"}, {"nme": "getUpperBound", "acc": 1, "dsc": "()Lorg/eclipse/aether/version/VersionRange$Bound;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "ranges", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Lorg/eclipse/aether/version/VersionRange;>;"}, {"acc": 18, "nme": "lowerBound", "dsc": "Lorg/eclipse/aether/version/VersionRange$Bound;"}, {"acc": 18, "nme": "upperBound", "dsc": "Lorg/eclipse/aether/version/VersionRange$Bound;"}]}, "org/eclipse/aether/util/repository/JreProxySelector.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/repository/JreProxySelector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getProxy", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/repository/Proxy;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 10, "dsc": "(Ljava/net/SocketAddress;)Z"}], "flds": []}, "org/eclipse/aether/util/graph/selector/StaticDependencySelector.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/selector/StaticDependencySelector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Z)V"}, {"nme": "selectDependency", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/Dependency;)Z"}, {"nme": "deriveChildSelector", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/DependencyCollectionContext;)Lorg/eclipse/aether/collection/DependencySelector;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "select", "dsc": "Z"}]}, "org/eclipse/aether/util/StringUtils.class": {"ver": 52, "acc": 131121, "nme": "org/eclipse/aether/util/StringUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "isEmpty", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "org/eclipse/aether/util/graph/version/HighestVersionFilter.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/version/HighestVersionFilter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "filterVersions", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/VersionFilter$VersionFilterContext;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/DependencyCollectionContext;)Lorg/eclipse/aether/collection/VersionFilter;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": []}, "org/eclipse/aether/util/repository/ConservativeProxySelector.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/repository/ConservativeProxySelector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/ProxySelector;)V"}, {"nme": "getProxy", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/repository/Proxy;"}], "flds": [{"acc": 18, "nme": "selector", "dsc": "Lorg/eclipse/aether/repository/ProxySelector;"}]}, "org/eclipse/aether/util/graph/transformer/ConflictResolver$State.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/util/graph/transformer/ConflictResolver$State", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/util/graph/transformer/ConflictResolver;Lorg/eclipse/aether/graph/DependencyNode;Ljava/util/Map;ILorg/eclipse/aether/collection/DependencyGraphTransformationContext;)V", "sig": "(Lorg/eclipse/aether/graph/DependencyNode;Ljava/util/Map<**>;ILorg/eclipse/aether/collection/DependencyGraphTransformationContext;)V", "exs": ["org/eclipse/aether/RepositoryException"]}, {"nme": "prepare", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Ljava/util/Collection;)V", "sig": "(L<PERSON><PERSON>/lang/Object;Ljava/util/Collection<Ljava/lang/Object;>;)V"}, {"nme": "finish", "acc": 0, "dsc": "()V"}, {"nme": "winner", "acc": 0, "dsc": "()V"}, {"nme": "loser", "acc": 0, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "push", "acc": 0, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;<PERSON><PERSON><PERSON>/lang/Object;)Z", "exs": ["org/eclipse/aether/RepositoryException"]}, {"nme": "pop", "acc": 0, "dsc": "()V"}, {"nme": "add", "acc": 0, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)V", "exs": ["org/eclipse/aether/RepositoryException"]}, {"nme": "newConflictItem", "acc": 2, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;Lorg/eclipse/aether/graph/DependencyNode;)Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictItem;", "exs": ["org/eclipse/aether/RepositoryException"]}, {"nme": "depth", "acc": 2, "dsc": "()I"}, {"nme": "parent", "acc": 2, "dsc": "()Lorg/eclipse/aether/graph/DependencyNode;"}, {"nme": "deriveScope", "acc": 2, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;Ljava/lang/Object;)Ljava/lang/String;", "exs": ["org/eclipse/aether/RepositoryException"]}, {"nme": "scopes", "acc": 2, "dsc": "(ILorg/eclipse/aether/graph/Dependency;)V"}, {"nme": "scope", "acc": 2, "dsc": "(Lorg/eclipse/aether/graph/Dependency;)L<PERSON><PERSON>/lang/String;"}, {"nme": "deriveOptional", "acc": 2, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "flds": [{"acc": 0, "nme": "currentId", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 0, "nme": "totalConflictItems", "dsc": "I"}, {"acc": 16, "nme": "verbosity", "dsc": "Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$Verbosity;"}, {"acc": 16, "nme": "resolvedIds", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/eclipse/aether/graph/DependencyNode;>;"}, {"acc": 16, "nme": "potentialAncestorIds", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Ljava/lang/Object;>;"}, {"acc": 16, "nme": "conflictIds", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<**>;"}, {"acc": 16, "nme": "items", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictItem;>;"}, {"acc": 16, "nme": "infos", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$NodeInfo;>;"}, {"acc": 16, "nme": "stack", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;Ljava/lang/Object;>;"}, {"acc": 16, "nme": "parentNodes", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;"}, {"acc": 16, "nme": "parentScopes", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 16, "nme": "parentOptionals", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/Boolean;>;"}, {"acc": 16, "nme": "parentInfos", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$NodeInfo;>;"}, {"acc": 16, "nme": "conflictCtx", "dsc": "Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictContext;"}, {"acc": 16, "nme": "scopeCtx", "dsc": "Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ScopeContext;"}, {"acc": 16, "nme": "versionSelector", "dsc": "Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$VersionSelector;"}, {"acc": 16, "nme": "scopeSelector", "dsc": "Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ScopeSelector;"}, {"acc": 16, "nme": "scopeDeriver", "dsc": "Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ScopeDeriver;"}, {"acc": 16, "nme": "optionalitySelector", "dsc": "Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$OptionalitySelector;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lorg/eclipse/aether/util/graph/transformer/ConflictResolver;"}]}, "org/eclipse/aether/util/repository/DefaultProxySelector$ProxyDef.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/util/repository/DefaultProxySelector$ProxyDef", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/repository/Proxy;Ljava/lang/String;)V"}], "flds": [{"acc": 16, "nme": "proxy", "dsc": "Lorg/eclipse/aether/repository/Proxy;"}, {"acc": 16, "nme": "nonProxyHosts", "dsc": "Lorg/eclipse/aether/util/repository/DefaultProxySelector$NonProxyHosts;"}]}, "org/eclipse/aether/util/artifact/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/util/artifact/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/util/filter/PatternExclusionsDependencyFilter.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/filter/PatternExclusionsDependencyFilter", "super": "org/eclipse/aether/util/filter/AbstractPatternDependencyFilter", "mthds": [{"nme": "<init>", "acc": 129, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 129, "dsc": "(Lorg/eclipse/aether/version/VersionScheme;[<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)V", "sig": "(Lja<PERSON>/util/Collection<Ljava/lang/String;>;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/version/VersionScheme;Ljava/util/Collection;)V", "sig": "(Lorg/eclipse/aether/version/VersionScheme;Ljava/util/Collection<Ljava/lang/String;>;)V"}, {"nme": "accept", "acc": 4, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Z"}, {"nme": "hashCode", "acc": 4161, "dsc": "()I"}, {"nme": "equals", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "accept", "acc": 4161, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;<PERSON>ja<PERSON>/util/List;)Z"}], "flds": []}, "org/eclipse/aether/util/graph/transformer/ConflictIdSorter.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/transformer/ConflictIdSorter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "transformGraph", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;Lorg/eclipse/aether/collection/DependencyGraphTransformationContext;)Lorg/eclipse/aether/graph/DependencyNode;", "exs": ["org/eclipse/aether/RepositoryException"]}, {"nme": "buildConflitIdDAG", "acc": 2, "dsc": "(Ljava/util/Map;Lorg/eclipse/aether/graph/DependencyNode;Lorg/eclipse/aether/util/graph/transformer/ConflictIdSorter$ConflictId;ILjava/util/Map;Ljava/util/Map;)V", "sig": "(Ljava/util/Map<Ljava/lang/Object;Lorg/eclipse/aether/util/graph/transformer/ConflictIdSorter$ConflictId;>;Lorg/eclipse/aether/graph/DependencyNode;Lorg/eclipse/aether/util/graph/transformer/ConflictIdSorter$ConflictId;ILjava/util/Map<Lorg/eclipse/aether/graph/DependencyNode;Ljava/lang/Object;>;Ljava/util/Map<**>;)V"}, {"nme": "topsortConflictIds", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;Lorg/eclipse/aether/collection/DependencyGraphTransformationContext;)I", "sig": "(Ljava/util/Collection<Lorg/eclipse/aether/util/graph/transformer/ConflictIdSorter$ConflictId;>;Lorg/eclipse/aether/collection/DependencyGraphTransformationContext;)I"}, {"nme": "processRoots", "acc": 2, "dsc": "(Ljava/util/List;Lorg/eclipse/aether/util/graph/transformer/ConflictIdSorter$RootQueue;)V", "sig": "(Ljava/util/List<Ljava/lang/Object;>;Lorg/eclipse/aether/util/graph/transformer/ConflictIdSorter$RootQueue;)V"}, {"nme": "findCycles", "acc": 2, "dsc": "(L<PERSON><PERSON>/util/Collection;)Ljava/util/Collection;", "sig": "(Ljava/util/Collection<Lorg/eclipse/aether/util/graph/transformer/ConflictIdSorter$ConflictId;>;)Ljava/util/Collection<Ljava/util/Collection<Ljava/lang/Object;>;>;"}, {"nme": "findCycles", "acc": 2, "dsc": "(Lorg/eclipse/aether/util/graph/transformer/ConflictIdSorter$ConflictId;Ljava/util/Map;Ljava/util/Map;Ljava/util/Collection;)V", "sig": "(Lorg/eclipse/aether/util/graph/transformer/ConflictIdSorter$ConflictId;Ljava/util/Map<Lorg/eclipse/aether/util/graph/transformer/ConflictIdSorter$ConflictId;Ljava/lang/Object;>;Ljava/util/Map<Ljava/lang/Object;Ljava/lang/Integer;>;Ljava/util/Collection<Ljava/util/Collection<Ljava/lang/Object;>;>;)V"}], "flds": []}, "org/eclipse/aether/util/FileUtils$FileWriter.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/util/FileUtils$FileWriter", "super": "java/lang/Object", "mthds": [{"nme": "write", "acc": 1025, "dsc": "(Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/FunctionalInterface;"}]}, "org/eclipse/aether/util/graph/selector/ExclusionDependencySelector.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/selector/ExclusionDependencySelector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)V", "sig": "(Ljava/util/Collection<Lorg/eclipse/aether/graph/Exclusion;>;)V"}, {"nme": "<init>", "acc": 2, "dsc": "([Lorg/eclipse/aether/graph/Exclusion;)V"}, {"nme": "selectDependency", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/Dependency;)Z"}, {"nme": "matches", "acc": 2, "dsc": "(Lorg/eclipse/aether/graph/Exclusion;Lorg/eclipse/aether/artifact/Artifact;)Z"}, {"nme": "matches", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "deriveChildSelector", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/DependencyCollectionContext;)Lorg/eclipse/aether/collection/DependencySelector;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "exclusions", "dsc": "[Lorg/eclipse/aether/graph/Exclusion;"}, {"acc": 2, "nme": "hashCode", "dsc": "I"}]}, "org/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictContext.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictContext", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;Ljava/util/Map;Ljava/util/Collection;)V", "sig": "(Lorg/eclipse/aether/graph/DependencyNode;Ljava/util/Map<**>;Ljava/util/Collection<Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictItem;>;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;Ljava/lang/Object;Ljava/util/Map;Ljava/util/Collection;)V", "sig": "(Lorg/eclipse/aether/graph/DependencyNode;Ljava/lang/Object;Ljava/util/Map<Lorg/eclipse/aether/graph/DependencyNode;Ljava/lang/Object;>;Ljava/util/Collection<Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictItem;>;)V"}, {"nme": "getRoot", "acc": 1, "dsc": "()Lorg/eclipse/aether/graph/DependencyNode;"}, {"nme": "isIncluded", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Z"}, {"nme": "getItems", "acc": 1, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictItem;>;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictItem;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictItem;)V"}, {"nme": "getScope", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setScope", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getOptional", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/<PERSON>an;"}, {"nme": "setOptional", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/<PERSON>;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 16, "nme": "root", "dsc": "Lorg/eclipse/aether/graph/DependencyNode;"}, {"acc": 16, "nme": "conflictIds", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<**>;"}, {"acc": 16, "nme": "items", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictItem;>;"}, {"acc": 0, "nme": "conflictId", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 0, "nme": "winner", "dsc": "Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictItem;"}, {"acc": 0, "nme": "scope", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "optional", "dsc": "<PERSON><PERSON><PERSON>/lang/Boolean;"}]}, "org/eclipse/aether/util/DirectoryUtils.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/DirectoryUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "resolveDirectory", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;Ljava/nio/file/Path;Z)Ljava/nio/file/Path;", "exs": ["java/io/IOException"]}, {"nme": "resolveDirectory", "acc": 9, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/lang/String;Ljava/lang/String;Z)Ljava/nio/file/Path;", "exs": ["java/io/IOException"]}], "flds": []}, "org/eclipse/aether/util/filter/AndDependencyFilter.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/filter/AndDependencyFilter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 129, "dsc": "([Lorg/eclipse/aether/graph/DependencyFilter;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)V", "sig": "(<PERSON><PERSON><PERSON>/util/Collection<Lorg/eclipse/aether/graph/DependencyFilter;>;)V"}, {"nme": "newInstance", "acc": 9, "dsc": "(Lorg/eclipse/aether/graph/DependencyFilter;Lorg/eclipse/aether/graph/DependencyFilter;)Lorg/eclipse/aether/graph/DependencyFilter;"}, {"nme": "accept", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;<PERSON>ja<PERSON>/util/List;)Z", "sig": "(Lorg/eclipse/aether/graph/DependencyNode;Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;)Z"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "filters", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Lorg/eclipse/aether/graph/DependencyFilter;>;"}]}, "org/eclipse/aether/util/graph/transformer/ConflictMarker$Key.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/util/graph/transformer/ConflictMarker$Key", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)V"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "artifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}]}, "org/eclipse/aether/util/version/GenericVersionRange.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/util/version/GenericVersionRange", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/eclipse/aether/version/InvalidVersionSpecificationException"]}, {"nme": "parse", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/version/Version;"}, {"nme": "getLowerBound", "acc": 1, "dsc": "()Lorg/eclipse/aether/version/VersionRange$Bound;"}, {"nme": "getUpperBound", "acc": 1, "dsc": "()Lorg/eclipse/aether/version/VersionRange$Bound;"}, {"nme": "containsVersion", "acc": 1, "dsc": "(Lorg/eclipse/aether/version/Version;)Z"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "hash", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "lowerBound", "dsc": "Lorg/eclipse/aether/version/VersionRange$Bound;"}, {"acc": 18, "nme": "upperBound", "dsc": "Lorg/eclipse/aether/version/VersionRange$Bound;"}]}, "org/eclipse/aether/util/graph/transformer/ConflictResolver$ScopeContext.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/transformer/ConflictResolver$ScopeContext", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getParentScope", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getChildScope", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDerivedScope", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setDerivedScope", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 0, "nme": "parentScope", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "childScope", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "derivedScope", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/eclipse/aether/util/repository/AuthenticationBuilder.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/repository/AuthenticationBuilder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "build", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/Authentication;"}, {"nme": "addUsername", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/util/repository/AuthenticationBuilder;"}, {"nme": "addPassword", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/util/repository/AuthenticationBuilder;"}, {"nme": "addPassword", "acc": 1, "dsc": "([C)Lorg/eclipse/aether/util/repository/AuthenticationBuilder;"}, {"nme": "addNtlm", "acc": 1, "dsc": "(<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;)Lorg/eclipse/aether/util/repository/AuthenticationBuilder;"}, {"nme": "addPrivateKey", "acc": 1, "dsc": "(<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;)Lorg/eclipse/aether/util/repository/AuthenticationBuilder;"}, {"nme": "addPrivateKey", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[C)Lorg/eclipse/aether/util/repository/AuthenticationBuilder;"}, {"nme": "addHostnameVerifier", "acc": 1, "dsc": "(Ljavax/net/ssl/HostnameVerifier;)Lorg/eclipse/aether/util/repository/AuthenticationBuilder;"}, {"nme": "addString", "acc": 1, "dsc": "(<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;)Lorg/eclipse/aether/util/repository/AuthenticationBuilder;"}, {"nme": "addSecret", "acc": 1, "dsc": "(<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;)Lorg/eclipse/aether/util/repository/AuthenticationBuilder;"}, {"nme": "addSecret", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[C)Lorg/eclipse/aether/util/repository/AuthenticationBuilder;"}, {"nme": "addCustom", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/Authentication;)Lorg/eclipse/aether/util/repository/AuthenticationBuilder;"}], "flds": [{"acc": 18, "nme": "authentications", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/repository/Authentication;>;"}]}, "org/eclipse/aether/util/graph/version/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/util/graph/version/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/util/graph/visitor/FilteringDependencyVisitor.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/visitor/FilteringDependencyVisitor", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyVisitor;Lorg/eclipse/aether/graph/DependencyFilter;)V"}, {"nme": "getVisitor", "acc": 1, "dsc": "()Lorg/eclipse/aether/graph/DependencyVisitor;"}, {"nme": "getFilter", "acc": 1, "dsc": "()Lorg/eclipse/aether/graph/DependencyFilter;"}, {"nme": "visitEnter", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Z"}, {"nme": "visitLeave", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Z"}], "flds": [{"acc": 18, "nme": "filter", "dsc": "Lorg/eclipse/aether/graph/DependencyFilter;"}, {"acc": 18, "nme": "visitor", "dsc": "Lorg/eclipse/aether/graph/DependencyVisitor;"}, {"acc": 18, "nme": "accepts", "dsc": "Lorg/eclipse/aether/util/graph/visitor/Stack;", "sig": "Lorg/eclipse/aether/util/graph/visitor/Stack<Ljava/lang/Boolean;>;"}, {"acc": 18, "nme": "parents", "dsc": "Lorg/eclipse/aether/util/graph/visitor/Stack;", "sig": "Lorg/eclipse/aether/util/graph/visitor/Stack<Lorg/eclipse/aether/graph/DependencyNode;>;"}]}, "org/eclipse/aether/util/graph/visitor/PostorderNodeListGenerator.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/visitor/PostorderNodeListGenerator", "super": "org/eclipse/aether/util/graph/visitor/AbstractDepthFirstNodeListGenerator", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "visitEnter", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Z"}, {"nme": "visitLeave", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Z"}, {"nme": "getClassPath", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getFiles", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/util/List;"}, {"nme": "getArtifacts", "acc": 4161, "dsc": "(Z)Ljava/util/List;"}, {"nme": "getDependencies", "acc": 4161, "dsc": "(Z)Ljava/util/List;"}, {"nme": "getNodes", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/util/List;"}], "flds": [{"acc": 18, "nme": "visits", "dsc": "Lorg/eclipse/aether/util/graph/visitor/Stack;", "sig": "Lorg/eclipse/aether/util/graph/visitor/Stack<Ljava/lang/Boolean;>;"}]}, "org/eclipse/aether/util/artifact/OverlayArtifactTypeRegistry.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/artifact/OverlayArtifactTypeRegistry", "super": "org/eclipse/aether/util/artifact/SimpleArtifactTypeRegistry", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/ArtifactTypeRegistry;)V"}, {"nme": "add", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/ArtifactType;)Lorg/eclipse/aether/util/artifact/OverlayArtifactTypeRegistry;"}, {"nme": "get", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/artifact/ArtifactType;"}, {"nme": "toString", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "add", "acc": 4161, "dsc": "(Lorg/eclipse/aether/artifact/ArtifactType;)Lorg/eclipse/aether/util/artifact/SimpleArtifactTypeRegistry;"}], "flds": [{"acc": 18, "nme": "delegate", "dsc": "Lorg/eclipse/aether/artifact/ArtifactTypeRegistry;"}]}, "org/eclipse/aether/util/artifact/SimpleArtifactTypeRegistry.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/util/artifact/SimpleArtifactTypeRegistry", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "add", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/ArtifactType;)Lorg/eclipse/aether/util/artifact/SimpleArtifactTypeRegistry;"}, {"nme": "get", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/artifact/ArtifactType;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "types", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/artifact/ArtifactType;>;"}]}, "org/eclipse/aether/util/version/GenericVersion$Tokenizer.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/util/version/GenericVersion$Tokenizer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "next", "acc": 1, "dsc": "()Z"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toItem", "acc": 1, "dsc": "()Lorg/eclipse/aether/util/version/GenericVersion$Item;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "QUALIFIER_ALPHA", "dsc": "<PERSON><PERSON><PERSON>/lang/Integer;"}, {"acc": 26, "nme": "QUALIFIER_BETA", "dsc": "<PERSON><PERSON><PERSON>/lang/Integer;"}, {"acc": 26, "nme": "QUALIFIER_MILESTONE", "dsc": "<PERSON><PERSON><PERSON>/lang/Integer;"}, {"acc": 26, "nme": "QUALIFIERS", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Integer;>;"}, {"acc": 18, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "versionLength", "dsc": "I"}, {"acc": 2, "nme": "index", "dsc": "I"}, {"acc": 2, "nme": "token", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "number", "dsc": "Z"}, {"acc": 2, "nme": "terminatedByNumber", "dsc": "Z"}]}, "org/eclipse/aether/util/repository/SimpleArtifactDescriptorPolicy.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/repository/SimpleArtifactDescriptorPolicy", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(ZZ)V"}, {"nme": "<init>", "acc": 1, "dsc": "(I)V"}, {"nme": "getPolicy", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/ArtifactDescriptorPolicyRequest;)I"}], "flds": [{"acc": 18, "nme": "policy", "dsc": "I"}]}, "org/eclipse/aether/util/graph/traverser/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/util/graph/traverser/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/util/FileUtils$1.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/util/FileUtils$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/nio/file/Path;)V", "sig": "()V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 4112, "nme": "val$tempFile", "dsc": "Ljava/nio/file/Path;"}]}, "org/eclipse/aether/util/version/GenericVersionConstraint.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/util/version/GenericVersionConstraint", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/version/VersionRange;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/version/Version;)V"}, {"nme": "getRange", "acc": 1, "dsc": "()Lorg/eclipse/aether/version/VersionRange;"}, {"nme": "getVersion", "acc": 1, "dsc": "()Lorg/eclipse/aether/version/Version;"}, {"nme": "containsVersion", "acc": 1, "dsc": "(Lorg/eclipse/aether/version/Version;)Z"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "hash", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}], "flds": [{"acc": 18, "nme": "range", "dsc": "Lorg/eclipse/aether/version/VersionRange;"}, {"acc": 18, "nme": "version", "dsc": "Lorg/eclipse/aether/version/Version;"}]}, "org/eclipse/aether/util/graph/visitor/PathRecordingDependencyVisitor.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/visitor/PathRecordingDependencyVisitor", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyFilter;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyFilter;Z)V"}, {"nme": "getFilter", "acc": 1, "dsc": "()Lorg/eclipse/aether/graph/DependencyFilter;"}, {"nme": "getPaths", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;>;"}, {"nme": "visitEnter", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Z"}, {"nme": "visitLeave", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Z"}], "flds": [{"acc": 18, "nme": "filter", "dsc": "Lorg/eclipse/aether/graph/DependencyFilter;"}, {"acc": 18, "nme": "paths", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;>;"}, {"acc": 18, "nme": "parents", "dsc": "Lorg/eclipse/aether/util/graph/visitor/Stack;", "sig": "Lorg/eclipse/aether/util/graph/visitor/Stack<Lorg/eclipse/aether/graph/DependencyNode;>;"}, {"acc": 18, "nme": "excludeChildrenOfMatches", "dsc": "Z"}]}, "org/eclipse/aether/util/StringDigestUtil.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/StringDigestUtil", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "update", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/util/StringDigestUtil;"}, {"nme": "digest", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "sha1", "acc": 9, "dsc": "()Lorg/eclipse/aether/util/StringDigestUtil;"}, {"nme": "sha1", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": [{"acc": 18, "nme": "digest", "dsc": "Ljava/security/MessageDigest;"}]}, "org/eclipse/aether/util/graph/transformer/NoopDependencyGraphTransformer.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/transformer/NoopDependencyGraphTransformer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "transformGraph", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;Lorg/eclipse/aether/collection/DependencyGraphTransformationContext;)Lorg/eclipse/aether/graph/DependencyNode;", "exs": ["org/eclipse/aether/RepositoryException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "INSTANCE", "dsc": "Lorg/eclipse/aether/collection/DependencyGraphTransformer;"}]}, "org/eclipse/aether/util/graph/transformer/JavaScopeDeriver.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/transformer/JavaScopeDeriver", "super": "org/eclipse/aether/util/graph/transformer/ConflictResolver$ScopeDeriver", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "deriveScope", "acc": 1, "dsc": "(Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ScopeContext;)V", "exs": ["org/eclipse/aether/RepositoryException"]}, {"nme": "getDerivedScope", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": []}, "org/eclipse/aether/util/repository/DefaultAuthenticationSelector.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/repository/DefaultAuthenticationSelector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "add", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/eclipse/aether/repository/Authentication;)Lorg/eclipse/aether/util/repository/DefaultAuthenticationSelector;"}, {"nme": "getAuthentication", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/repository/Authentication;"}], "flds": [{"acc": 18, "nme": "repos", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/repository/Authentication;>;"}]}, "org/eclipse/aether/util/repository/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/util/repository/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/util/graph/transformer/ConflictMarker$ConflictGroup.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/util/graph/transformer/ConflictMarker$ConflictGroup", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;I)V", "sig": "(<PERSON><PERSON><PERSON>/util/Set<Ljava/lang/Object;>;I)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 16, "nme": "keys", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/Object;>;"}, {"acc": 16, "nme": "index", "dsc": "I"}]}, "org/eclipse/aether/util/filter/PatternInclusionsDependencyFilter.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/filter/PatternInclusionsDependencyFilter", "super": "org/eclipse/aether/util/filter/AbstractPatternDependencyFilter", "mthds": [{"nme": "<init>", "acc": 129, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 129, "dsc": "(Lorg/eclipse/aether/version/VersionScheme;[<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)V", "sig": "(Lja<PERSON>/util/Collection<Ljava/lang/String;>;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/version/VersionScheme;Ljava/util/Collection;)V", "sig": "(Lorg/eclipse/aether/version/VersionScheme;Ljava/util/Collection<Ljava/lang/String;>;)V"}, {"nme": "hashCode", "acc": 4161, "dsc": "()I"}, {"nme": "equals", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "accept", "acc": 4161, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;<PERSON>ja<PERSON>/util/List;)Z"}], "flds": []}, "org/eclipse/aether/util/concurrency/WorkerThreadFactory.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/concurrency/WorkerThreadFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getCallerSimpleClassName", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "newThread", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Runnable;)<PERSON><PERSON><PERSON>/lang/Thread;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "factory", "dsc": "Ljava/util/concurrent/ThreadFactory;"}, {"acc": 18, "nme": "namePrefix", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "threadIndex", "dsc": "Ljava/util/concurrent/atomic/AtomicInteger;"}, {"acc": 26, "nme": "POOL_INDEX", "dsc": "Ljava/util/concurrent/atomic/AtomicInteger;"}]}, "org/eclipse/aether/util/ConfigUtils.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/ConfigUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getObject", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;<PERSON><PERSON><PERSON>/lang/Object;[Lja<PERSON>/lang/String;)Ljava/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/util/Map<**>;Ljava/lang/Object;[Ljava/lang/String;)Ljava/lang/Object;"}, {"nme": "getObject", "acc": 137, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lja<PERSON>/lang/Object;[Ljava/lang/String;)Ljava/lang/Object;"}, {"nme": "getString", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;Ljava/lang/String;[Ljava/lang/String;)Ljava/lang/String;", "sig": "(<PERSON><PERSON><PERSON>/util/Map<**>;Ljava/lang/String;[Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "getString", "acc": 137, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/lang/String;[Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "getInteger", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;I[<PERSON><PERSON><PERSON>/lang/String;)I", "sig": "(<PERSON><PERSON><PERSON>/util/Map<**>;I[<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "getInteger", "acc": 137, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;I[<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "getLong", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;J[Ljava/lang/String;)J", "sig": "(<PERSON><PERSON><PERSON>/util/Map<**>;J[Ljava/lang/String;)J"}, {"nme": "getLong", "acc": 137, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;J[<PERSON>ja<PERSON>/lang/String;)J"}, {"nme": "getFloat", "acc": 137, "dsc": "(Lja<PERSON>/util/Map;F[Ljava/lang/String;)F", "sig": "(Lja<PERSON>/util/Map<**>;F[Ljava/lang/String;)F"}, {"nme": "getFloat", "acc": 137, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;F[Ljava/lang/String;)F"}, {"nme": "getBoolean", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;Z[Ljava/lang/String;)Z", "sig": "(<PERSON><PERSON><PERSON>/util/Map<**>;Z[Ljava/lang/String;)Z"}, {"nme": "getBoolean", "acc": 137, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Z[<PERSON>java/lang/String;)Z"}, {"nme": "getList", "acc": 137, "dsc": "(Lja<PERSON>/util/Map;Ljava/util/List;[Lja<PERSON>/lang/String;)Ljava/util/List;", "sig": "(Ljava/util/Map<**>;Ljava/util/List<*>;[Ljava/lang/String;)Ljava/util/List<*>;"}, {"nme": "getList", "acc": 137, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/List;[Lja<PERSON>/lang/String;)Ljava/util/List;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/List<*>;[Lja<PERSON>/lang/String;)Ljava/util/List<*>;"}, {"nme": "getMap", "acc": 137, "dsc": "(Ljava/util/Map;Ljava/util/Map;[Ljava/lang/String;)Ljava/util/Map;", "sig": "(Ljava/util/Map<**>;Ljava/util/Map<**>;[Ljava/lang/String;)Ljava/util/Map<**>;"}, {"nme": "getMap", "acc": 137, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Map;[Ljava/lang/String;)Ljava/util/Map;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Map<**>;[Lja<PERSON>/lang/String;)Ljava/util/Map<**>;"}, {"nme": "parseCommaSeparatedNames", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "parseCommaSeparatedUniqueNames", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "lambda$parseCommaSeparatedNames$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}], "flds": []}, "org/eclipse/aether/util/artifact/ArtifactIdUtils.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/artifact/ArtifactIdUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "toId", "acc": 9, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lja<PERSON>/lang/String;"}, {"nme": "toId", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "toBaseId", "acc": 9, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lja<PERSON>/lang/String;"}, {"nme": "toVersionlessId", "acc": 9, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lja<PERSON>/lang/String;"}, {"nme": "toVersionlessId", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;L<PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "concat", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;)Ljava/lang/StringBuilder;"}, {"nme": "equalsId", "acc": 9, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/artifact/Artifact;)Z"}, {"nme": "equalsBaseId", "acc": 9, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/artifact/Artifact;)Z"}, {"nme": "equalsVersionlessId", "acc": 9, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/artifact/Artifact;)Z"}], "flds": [{"acc": 26, "nme": "SEP", "dsc": "C", "val": 58}]}, "org/eclipse/aether/util/repository/StringAuthentication.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/util/repository/StringAuthentication", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "fill", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/AuthenticationContext;Ljava/lang/String;Ljava/util/Map;)V", "sig": "(Lorg/eclipse/aether/repository/AuthenticationContext;Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "digest", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/AuthenticationDigest;)V"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "key", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "value", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/eclipse/aether/util/graph/traverser/StaticDependencyTraverser.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/traverser/StaticDependencyTraverser", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Z)V"}, {"nme": "traverseDependency", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/Dependency;)Z"}, {"nme": "deriveChildTraverser", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/DependencyCollectionContext;)Lorg/eclipse/aether/collection/DependencyTraverser;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "traverse", "dsc": "Z"}]}, "org/eclipse/aether/util/graph/visitor/AbstractDepthFirstNodeListGenerator.class": {"ver": 52, "acc": 1056, "nme": "org/eclipse/aether/util/graph/visitor/AbstractDepthFirstNodeListGenerator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "getNodes", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;"}, {"nme": "getDependencies", "acc": 1, "dsc": "(Z)Ljava/util/List;", "sig": "(Z)Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;"}, {"nme": "getArtifacts", "acc": 1, "dsc": "(Z)Ljava/util/List;", "sig": "(Z)Ljava/util/List<Lorg/eclipse/aether/artifact/Artifact;>;"}, {"nme": "getFiles", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/io/File;>;"}, {"nme": "getClassPath", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setVisited", "acc": 4, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Z"}, {"nme": "visitEnter", "acc": 1025, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Z"}, {"nme": "visitLeave", "acc": 1025, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Z"}], "flds": [{"acc": 18, "nme": "visitedNodes", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Lorg/eclipse/aether/graph/DependencyNode;Ljava/lang/Object;>;"}, {"acc": 20, "nme": "nodes", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;"}]}, "org/eclipse/aether/util/graph/manager/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/util/graph/manager/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/util/repository/DefaultMirrorSelector$MirrorDef.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/util/repository/DefaultMirrorSelector$MirrorDef", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/String;ZZ<PERSON>java/lang/String;Ljava/lang/String;)V"}], "flds": [{"acc": 16, "nme": "id", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 16, "nme": "url", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 16, "nme": "type", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 16, "nme": "repositoryManager", "dsc": "Z"}, {"acc": 16, "nme": "blocked", "dsc": "Z"}, {"acc": 16, "nme": "mirrorOfIds", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 16, "nme": "mirrorOfTypes", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/eclipse/aether/util/artifact/SubArtifact.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/artifact/SubArtifact", "super": "org/eclipse/aether/artifact/AbstractArtifact", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/lang/String;Ljava/lang/String;Ljava/io/File;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/lang/String;Ljava/lang/String;Ljava/util/Map;)V", "sig": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/lang/String;Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/lang/String;Ljava/lang/String;Ljava/util/Map;Ljava/io/File;)V", "sig": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/lang/String;Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;Ljava/io/File;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/lang/String;Ljava/lang/String;Ljava/io/File;Ljava/util/Map;)V", "sig": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/lang/String;Ljava/lang/String;Ljava/io/File;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "getGroupId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getArtifactId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getBaseVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isSnapshot", "acc": 1, "dsc": "()Z"}, {"nme": "getClassifier", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getExtension", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getFile", "acc": 1, "dsc": "()Ljava/io/File;"}, {"nme": "setFile", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "getProperties", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "setProperties", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Lorg/eclipse/aether/artifact/Artifact;", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "expand", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": [{"acc": 18, "nme": "mainArtifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}, {"acc": 18, "nme": "classifier", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "extension", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "file", "dsc": "Ljava/io/File;"}, {"acc": 18, "nme": "properties", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}]}, "org/eclipse/aether/util/graph/transformer/ConflictMarker.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/transformer/ConflictMarker", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "transformGraph", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;Lorg/eclipse/aether/collection/DependencyGraphTransformationContext;)Lorg/eclipse/aether/graph/DependencyNode;", "exs": ["org/eclipse/aether/RepositoryException"]}, {"nme": "analyze", "acc": 2, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;Ljava/util/Map;Ljava/util/Map;[I)V", "sig": "(Lorg/eclipse/aether/graph/DependencyNode;Ljava/util/Map<Lorg/eclipse/aether/graph/DependencyNode;Ljava/lang/Object;>;Ljava/util/Map<Ljava/lang/Object;Lorg/eclipse/aether/util/graph/transformer/ConflictMarker$ConflictGroup;>;[I)V"}, {"nme": "merge", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;<PERSON><PERSON><PERSON>/util/Set;)<PERSON><PERSON><PERSON>/util/Set;", "sig": "(L<PERSON><PERSON>/util/Set<Ljava/lang/Object;>;Ljava/util/Set<Ljava/lang/Object;>;)Ljava/util/Set<Ljava/lang/Object;>;"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Ljava/util/Set;", "sig": "(Lorg/eclipse/aether/graph/DependencyNode;)Ljava/util/Set<Ljava/lang/Object;>;"}, {"nme": "mark", "acc": 2, "dsc": "(Ljava/util/Collection;Ljava/util/Map;)Ljava/util/Map;", "sig": "(Ljava/util/Collection<Lorg/eclipse/aether/graph/DependencyNode;>;Ljava/util/Map<Ljava/lang/Object;Lorg/eclipse/aether/util/graph/transformer/ConflictMarker$ConflictGroup;>;)Ljava/util/Map<Lorg/eclipse/aether/graph/DependencyNode;Ljava/lang/Object;>;"}, {"nme": "to<PERSON><PERSON>", "acc": 10, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "org/eclipse/aether/util/graph/traverser/AndDependencyTraverser.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/traverser/AndDependencyTraverser", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 129, "dsc": "([Lorg/eclipse/aether/collection/DependencyTraverser;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)V", "sig": "(<PERSON><PERSON><PERSON>/util/Collection<+Lorg/eclipse/aether/collection/DependencyTraverser;>;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(<PERSON><PERSON><PERSON>/util/Set<Lorg/eclipse/aether/collection/DependencyTraverser;>;)V"}, {"nme": "newInstance", "acc": 9, "dsc": "(Lorg/eclipse/aether/collection/DependencyTraverser;Lorg/eclipse/aether/collection/DependencyTraverser;)Lorg/eclipse/aether/collection/DependencyTraverser;"}, {"nme": "traverseDependency", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/Dependency;)Z"}, {"nme": "deriveChildTraverser", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/DependencyCollectionContext;)Lorg/eclipse/aether/collection/DependencyTraverser;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "traversers", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<+Lorg/eclipse/aether/collection/DependencyTraverser;>;"}, {"acc": 2, "nme": "hashCode", "dsc": "I"}]}, "org/eclipse/aether/util/repository/DefaultProxySelector$NonProxyHosts.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/util/repository/DefaultProxySelector$NonProxyHosts", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "isNonProxyHost", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}], "flds": [{"acc": 18, "nme": "patterns", "dsc": "[<PERSON><PERSON><PERSON>/util/regex/Pattern;"}]}, "org/eclipse/aether/util/graph/visitor/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/util/graph/visitor/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/util/filter/AbstractPatternDependencyFilter.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/util/filter/AbstractPatternDependencyFilter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 128, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 128, "dsc": "(Lorg/eclipse/aether/version/VersionScheme;[<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)V", "sig": "(Lja<PERSON>/util/Collection<Ljava/lang/String;>;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/version/VersionScheme;Ljava/util/Collection;)V", "sig": "(Lorg/eclipse/aether/version/VersionScheme;Ljava/util/Collection<Ljava/lang/String;>;)V"}, {"nme": "accept", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;<PERSON>ja<PERSON>/util/List;)Z", "sig": "(Lorg/eclipse/aether/graph/DependencyNode;Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;)Z"}, {"nme": "accept", "acc": 4, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Z"}, {"nme": "accept", "acc": 2, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/lang/String;)Z"}, {"nme": "matches", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isVersionIncludedInRange", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "patterns", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 18, "nme": "versionScheme", "dsc": "Lorg/eclipse/aether/version/VersionScheme;"}]}, "org/eclipse/aether/util/graph/selector/ExclusionDependencySelector$ExclusionComparator.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/util/graph/selector/ExclusionDependencySelector$ExclusionComparator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "compare", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/Exclusion;Lorg/eclipse/aether/graph/Exclusion;)I"}, {"nme": "compare", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)I"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "INSTANCE", "dsc": "Lorg/eclipse/aether/util/graph/selector/ExclusionDependencySelector$ExclusionComparator;"}]}, "org/eclipse/aether/util/ChecksumUtils.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/ChecksumUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "read", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)Ljava/lang/String;", "exs": ["java/io/IOException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "calc", "acc": 131081, "dsc": "(Ljava/io/File;Ljava/util/Collection;)Ljava/util/Map;", "sig": "(Ljava/io/File;Ljava/util/Collection<Ljava/lang/String;>;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;", "exs": ["java/io/IOException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "calc", "acc": 131081, "dsc": "([BLjava/util/Collection;)Ljava/util/Map;", "sig": "([BLjava/util/Collection<Ljava/lang/String;>;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;", "exs": ["java/io/IOException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "calc", "acc": 10, "dsc": "(L<PERSON><PERSON>/io/InputStream;Ljava/util/Collection;)Ljava/util/Map;", "sig": "(Ljava/io/InputStream;Ljava/util/Collection<Ljava/lang/String;>;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;", "exs": ["java/io/IOException"]}, {"nme": "toHexString", "acc": 9, "dsc": "([B)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "fromHexString", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[B"}], "flds": []}, "org/eclipse/aether/util/repository/DefaultProxySelector.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/repository/DefaultProxySelector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "add", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/Proxy;Ljava/lang/String;)Lorg/eclipse/aether/util/repository/DefaultProxySelector;"}, {"nme": "getProxy", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/repository/Proxy;"}], "flds": [{"acc": 18, "nme": "proxies", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/util/repository/DefaultProxySelector$ProxyDef;>;"}]}, "org/eclipse/aether/util/graph/manager/DependencyManagerUtils.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/manager/DependencyManagerUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getPremanagedVersion", "acc": 9, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Ljava/lang/String;"}, {"nme": "getPremanagedScope", "acc": 9, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Ljava/lang/String;"}, {"nme": "getPremanagedOptional", "acc": 9, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)<PERSON><PERSON><PERSON>/lang/<PERSON>an;"}, {"nme": "getPremanagedExclusions", "acc": 9, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Ljava/util/Collection;", "sig": "(Lorg/eclipse/aether/graph/DependencyNode;)Ljava/util/Collection<Lorg/eclipse/aether/graph/Exclusion;>;"}, {"nme": "getPremanagedProperties", "acc": 9, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Ljava/util/Map;", "sig": "(Lorg/eclipse/aether/graph/DependencyNode;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "cast", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Class;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Object;Ljava/lang/Class<TT;>;)TT;"}], "flds": [{"acc": 25, "nme": "CONFIG_PROP_VERBOSE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.dependencyManager.verbose"}, {"acc": 25, "nme": "NODE_DATA_PREMANAGED_VERSION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "premanaged.version"}, {"acc": 25, "nme": "NODE_DATA_PREMANAGED_SCOPE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "premanaged.scope"}, {"acc": 25, "nme": "NODE_DATA_PREMANAGED_OPTIONAL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "premanaged.optional"}, {"acc": 25, "nme": "NODE_DATA_PREMANAGED_EXCLUSIONS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "premanaged.exclusions"}, {"acc": 25, "nme": "NODE_DATA_PREMANAGED_PROPERTIES", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "premanaged.properties"}]}, "org/eclipse/aether/util/graph/visitor/DependencyGraphDumper.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/util/graph/visitor/DependencyGraphDumper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/util/function/Consumer;)V", "sig": "(Ljava/util/function/Consumer<Ljava/lang/String;>;)V"}, {"nme": "visitEnter", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Z"}, {"nme": "formatIndentation", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "formatNode", "acc": 2, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Ljava/lang/String;"}, {"nme": "visitLeave", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Z"}], "flds": [{"acc": 18, "nme": "consumer", "dsc": "Ljava/util/function/Consumer;", "sig": "Ljava/util/function/Consumer<Ljava/lang/String;>;"}, {"acc": 18, "nme": "childInfos", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/util/graph/visitor/DependencyGraphDumper$ChildInfo;>;"}]}, "org/eclipse/aether/util/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/util/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/util/graph/visitor/Stack.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/util/graph/visitor/Stack", "super": "java/util/AbstractList", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "push", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TE;)V"}, {"nme": "pop", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TE;"}, {"nme": "peek", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TE;"}, {"nme": "get", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(I)TE;"}, {"nme": "size", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 2, "nme": "elements", "dsc": "[<PERSON><PERSON><PERSON>/lang/Object;", "sig": "[TE;"}, {"acc": 2, "nme": "size", "dsc": "I"}]}, "org/eclipse/aether/util/FileUtils$TempFile.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/util/FileUtils$TempFile", "super": "java/lang/Object", "mthds": [{"nme": "<PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()Ljava/nio/file/Path;"}], "flds": []}, "org/eclipse/aether/util/repository/ConservativeAuthenticationSelector.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/repository/ConservativeAuthenticationSelector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/AuthenticationSelector;)V"}, {"nme": "getAuthentication", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/repository/Authentication;"}], "flds": [{"acc": 18, "nme": "selector", "dsc": "Lorg/eclipse/aether/repository/AuthenticationSelector;"}]}, "org/eclipse/aether/util/filter/NotDependencyFilter.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/filter/NotDependencyFilter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyFilter;)V"}, {"nme": "accept", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;<PERSON>ja<PERSON>/util/List;)Z", "sig": "(Lorg/eclipse/aether/graph/DependencyNode;Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;)Z"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "filter", "dsc": "Lorg/eclipse/aether/graph/DependencyFilter;"}]}, "org/eclipse/aether/util/graph/transformer/JavaDependencyContextRefiner.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/transformer/JavaDependencyContextRefiner", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "transformGraph", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;Lorg/eclipse/aether/collection/DependencyGraphTransformationContext;)Lorg/eclipse/aether/graph/DependencyNode;", "exs": ["org/eclipse/aether/RepositoryException"]}, {"nme": "getClasspathScope", "acc": 2, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Ljava/lang/String;"}], "flds": []}, "org/eclipse/aether/util/version/GenericVersion.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/util/version/GenericVersion", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "asString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "asItems", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/util/version/GenericVersion$Item;>;"}, {"nme": "parse", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;", "sig": "(Ljava/lang/String;)Ljava/util/List<Lorg/eclipse/aether/util/version/GenericVersion$Item;>;"}, {"nme": "trimPadding", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Lorg/eclipse/aether/util/version/GenericVersion$Item;>;)V"}, {"nme": "compareTo", "acc": 1, "dsc": "(Lorg/eclipse/aether/version/Version;)I"}, {"nme": "comparePadding", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON>java/lang/Boolean;)I", "sig": "(Ljava/util/List<Lorg/eclipse/aether/util/version/GenericVersion$Item;>;ILjava/lang/<PERSON>;)I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "compareTo", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}], "flds": [{"acc": 18, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "items", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/util/version/GenericVersion$Item;>;"}, {"acc": 18, "nme": "hash", "dsc": "I"}]}, "org/eclipse/aether/util/repository/SecretAuthentication.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/util/repository/SecretAuthentication", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[C)V"}, {"nme": "<init>", "acc": 2, "dsc": "([<PERSON><PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "copy", "acc": 10, "dsc": "([C)[C"}, {"nme": "xor", "acc": 2, "dsc": "([C)[C"}, {"nme": "clear", "acc": 10, "dsc": "([C)V"}, {"nme": "fill", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/AuthenticationContext;Ljava/lang/String;Ljava/util/Map;)V", "sig": "(Lorg/eclipse/aether/repository/AuthenticationContext;Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "digest", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/AuthenticationDigest;)V"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "KEYS", "dsc": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 18, "nme": "key", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "value", "dsc": "[C"}, {"acc": 18, "nme": "secretHash", "dsc": "I"}]}, "org/eclipse/aether/util/repository/ChainedAuthentication.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/util/repository/ChainedAuthentication", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 128, "dsc": "([Lorg/eclipse/aether/repository/Authentication;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)V", "sig": "(Lja<PERSON>/util/Collection<+Lorg/eclipse/aether/repository/Authentication;>;)V"}, {"nme": "fill", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/AuthenticationContext;Ljava/lang/String;Ljava/util/Map;)V", "sig": "(Lorg/eclipse/aether/repository/AuthenticationContext;Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "digest", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/AuthenticationDigest;)V"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "authentications", "dsc": "[Lorg/eclipse/aether/repository/Authentication;"}]}, "org/eclipse/aether/util/version/GenericVersionScheme.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/version/GenericVersionScheme", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "parseVersion", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/util/version/GenericVersion;", "exs": ["org/eclipse/aether/version/InvalidVersionSpecificationException"]}, {"nme": "parseVersionRange", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/util/version/GenericVersionRange;", "exs": ["org/eclipse/aether/version/InvalidVersionSpecificationException"]}, {"nme": "parseVersionConstraint", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/util/version/GenericVersionConstraint;", "exs": ["org/eclipse/aether/version/InvalidVersionSpecificationException"]}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "main", "acc": 137, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "parseVersionConstraint", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/version/VersionConstraint;", "exs": ["org/eclipse/aether/version/InvalidVersionSpecificationException"]}, {"nme": "parseVersionRange", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/version/VersionRange;", "exs": ["org/eclipse/aether/version/InvalidVersionSpecificationException"]}, {"nme": "parseVersion", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/version/Version;", "exs": ["org/eclipse/aether/version/InvalidVersionSpecificationException"]}], "flds": []}, "org/eclipse/aether/util/artifact/DefaultArtifactTypeRegistry.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/artifact/DefaultArtifactTypeRegistry", "super": "org/eclipse/aether/util/artifact/SimpleArtifactTypeRegistry", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "add", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/ArtifactType;)Lorg/eclipse/aether/util/artifact/DefaultArtifactTypeRegistry;"}, {"nme": "toString", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "get", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/artifact/ArtifactType;"}, {"nme": "add", "acc": 4161, "dsc": "(Lorg/eclipse/aether/artifact/ArtifactType;)Lorg/eclipse/aether/util/artifact/SimpleArtifactTypeRegistry;"}], "flds": []}, "org/eclipse/aether/util/graph/transformer/ConflictResolver$NodeInfo.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/util/graph/transformer/ConflictResolver$NodeInfo", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON>ja<PERSON>/lang/String;Z)V"}, {"nme": "update", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)I"}, {"nme": "add", "acc": 0, "dsc": "(Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictItem;)V"}], "flds": [{"acc": 0, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "I"}, {"acc": 0, "nme": "derivedScopes", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 0, "nme": "derivedOptionalities", "dsc": "I"}, {"acc": 0, "nme": "children", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictItem;>;"}, {"acc": 24, "nme": "CHANGE_SCOPE", "dsc": "I", "val": 1}, {"acc": 24, "nme": "CHANGE_OPTIONAL", "dsc": "I", "val": 2}, {"acc": 26, "nme": "OPT_FALSE", "dsc": "I", "val": 1}, {"acc": 26, "nme": "OPT_TRUE", "dsc": "I", "val": 2}]}, "org/eclipse/aether/util/repository/ChainedWorkspaceReader.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/repository/ChainedWorkspaceReader", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 129, "dsc": "([Lorg/eclipse/aether/repository/WorkspaceReader;)V"}, {"nme": "newInstance", "acc": 9, "dsc": "(Lorg/eclipse/aether/repository/WorkspaceReader;Lorg/eclipse/aether/repository/WorkspaceReader;)Lorg/eclipse/aether/repository/WorkspaceReader;"}, {"nme": "findArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Ljava/io/File;"}, {"nme": "findVersions", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Ljava/util/List;", "sig": "(Lorg/eclipse/aether/artifact/Artifact;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "getRepository", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/WorkspaceRepository;"}], "flds": [{"acc": 18, "nme": "readers", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/repository/WorkspaceReader;>;"}, {"acc": 2, "nme": "repository", "dsc": "Lorg/eclipse/aether/repository/WorkspaceRepository;"}]}, "org/eclipse/aether/util/graph/transformer/ConflictResolver$ScopeDeriver.class": {"ver": 52, "acc": 1057, "nme": "org/eclipse/aether/util/graph/transformer/ConflictResolver$ScopeDeriver", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getInstance", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;Lorg/eclipse/aether/collection/DependencyGraphTransformationContext;)Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ScopeDeriver;", "exs": ["org/eclipse/aether/RepositoryException"]}, {"nme": "deriveScope", "acc": 1025, "dsc": "(Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ScopeContext;)V", "exs": ["org/eclipse/aether/RepositoryException"]}], "flds": []}, "org/eclipse/aether/util/repository/DefaultMirrorSelector.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/repository/DefaultMirrorSelector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "add", "acc": 131073, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;Z<PERSON><PERSON>va/lang/String;Ljava/lang/String;)Lorg/eclipse/aether/util/repository/DefaultMirrorSelector;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "add", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;ZZLjava/lang/String;Ljava/lang/String;)Lorg/eclipse/aether/util/repository/DefaultMirrorSelector;"}, {"nme": "getMirror", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/repository/RemoteRepository;"}, {"nme": "find<PERSON><PERSON><PERSON>r", "acc": 2, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/util/repository/DefaultMirrorSelector$MirrorDef;"}, {"nme": "matchPattern", "acc": 8, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;Lja<PERSON>/lang/String;)Z"}, {"nme": "isExternalRepo", "acc": 8, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Z"}, {"nme": "isLocal", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isExternalHttpRepo", "acc": 8, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Z"}, {"nme": "matchesType", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}], "flds": [{"acc": 26, "nme": "WILDCARD", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "*"}, {"acc": 26, "nme": "EXTERNAL_WILDCARD", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "external:*"}, {"acc": 26, "nme": "EXTERNAL_HTTP_WILDCARD", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "external:http:*"}, {"acc": 18, "nme": "mirrors", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/util/repository/DefaultMirrorSelector$MirrorDef;>;"}]}, "org/eclipse/aether/util/graph/traverser/FatArtifactTraverser.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/traverser/FatArtifactTraverser", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "traverseDependency", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/Dependency;)Z"}, {"nme": "deriveChildTraverser", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/DependencyCollectionContext;)Lorg/eclipse/aether/collection/DependencyTraverser;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": []}, "org/eclipse/aether/util/graph/visitor/TreeDependencyVisitor.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/visitor/TreeDependencyVisitor", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyVisitor;)V"}, {"nme": "visitEnter", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Z"}, {"nme": "visitLeave", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Z"}], "flds": [{"acc": 18, "nme": "visitedNodes", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Lorg/eclipse/aether/graph/DependencyNode;Ljava/lang/Object;>;"}, {"acc": 18, "nme": "visitor", "dsc": "Lorg/eclipse/aether/graph/DependencyVisitor;"}, {"acc": 18, "nme": "visits", "dsc": "Lorg/eclipse/aether/util/graph/visitor/Stack;", "sig": "Lorg/eclipse/aether/util/graph/visitor/Stack<Ljava/lang/Boolean;>;"}]}, "org/eclipse/aether/util/graph/transformer/ChainedDependencyGraphTransformer.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/transformer/ChainedDependencyGraphTransformer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 129, "dsc": "([Lorg/eclipse/aether/collection/DependencyGraphTransformer;)V"}, {"nme": "newInstance", "acc": 9, "dsc": "(Lorg/eclipse/aether/collection/DependencyGraphTransformer;Lorg/eclipse/aether/collection/DependencyGraphTransformer;)Lorg/eclipse/aether/collection/DependencyGraphTransformer;"}, {"nme": "transformGraph", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;Lorg/eclipse/aether/collection/DependencyGraphTransformationContext;)Lorg/eclipse/aether/graph/DependencyNode;", "exs": ["org/eclipse/aether/RepositoryException"]}], "flds": [{"acc": 18, "nme": "transformers", "dsc": "[Lorg/eclipse/aether/collection/DependencyGraphTransformer;"}]}, "org/eclipse/aether/util/concurrency/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/util/concurrency/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/util/graph/manager/ClassicDependencyManager.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/manager/ClassicDependencyManager", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 2, "dsc": "(ILjava/util/Map;Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V", "sig": "(ILjava/util/Map<Ljava/lang/Object;Ljava/lang/String;>;Ljava/util/Map<Ljava/lang/Object;Ljava/lang/String;>;Ljava/util/Map<Ljava/lang/Object;Ljava/lang/Boolean;>;Ljava/util/Map<Ljava/lang/Object;Ljava/lang/String;>;Ljava/util/Map<Ljava/lang/Object;Ljava/util/Collection<Lorg/eclipse/aether/graph/Exclusion;>;>;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/DependencyCollectionContext;)Lorg/eclipse/aether/collection/DependencyManager;"}, {"nme": "manageDependency", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/Dependency;)Lorg/eclipse/aether/collection/DependencyManagement;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "lambda$deriveChildManager$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Ljava/util/Collection;"}], "flds": [{"acc": 18, "nme": "depth", "dsc": "I"}, {"acc": 18, "nme": "managedVersions", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Ljava/lang/String;>;"}, {"acc": 18, "nme": "managedScopes", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Ljava/lang/String;>;"}, {"acc": 18, "nme": "managedOptionals", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Ljava/lang/Boolean;>;"}, {"acc": 18, "nme": "managedLocalPaths", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Ljava/lang/String;>;"}, {"acc": 18, "nme": "managedExclusions", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Ljava/util/Collection<Lorg/eclipse/aether/graph/Exclusion;>;>;"}, {"acc": 2, "nme": "hashCode", "dsc": "I"}]}, "org/eclipse/aether/util/graph/visitor/CloningDependencyVisitor.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/util/graph/visitor/CloningDependencyVisitor", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getRootNode", "acc": 17, "dsc": "()Lorg/eclipse/aether/graph/DependencyNode;"}, {"nme": "clone", "acc": 4, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Lorg/eclipse/aether/graph/DependencyNode;"}, {"nme": "visitEnter", "acc": 17, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Z"}, {"nme": "visitLeave", "acc": 17, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Z"}], "flds": [{"acc": 18, "nme": "clones", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Lorg/eclipse/aether/graph/DependencyNode;Lorg/eclipse/aether/graph/DependencyNode;>;"}, {"acc": 18, "nme": "parents", "dsc": "Lorg/eclipse/aether/util/graph/visitor/Stack;", "sig": "Lorg/eclipse/aether/util/graph/visitor/Stack<Lorg/eclipse/aether/graph/DependencyNode;>;"}, {"acc": 2, "nme": "root", "dsc": "Lorg/eclipse/aether/graph/DependencyNode;"}]}, "org/eclipse/aether/util/graph/transformer/ConflictIdSorter$RootQueue.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/util/graph/transformer/ConflictIdSorter$RootQueue", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(I)V"}, {"nme": "isEmpty", "acc": 0, "dsc": "()Z"}, {"nme": "add", "acc": 0, "dsc": "(Lorg/eclipse/aether/util/graph/transformer/ConflictIdSorter$ConflictId;)V"}, {"nme": "remove", "acc": 0, "dsc": "()Lorg/eclipse/aether/util/graph/transformer/ConflictIdSorter$ConflictId;"}], "flds": [{"acc": 2, "nme": "nextOut", "dsc": "I"}, {"acc": 2, "nme": "nextIn", "dsc": "I"}, {"acc": 2, "nme": "ids", "dsc": "[Lorg/eclipse/aether/util/graph/transformer/ConflictIdSorter$ConflictId;"}]}, "org/eclipse/aether/util/listener/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/util/listener/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/util/graph/manager/NoopDependencyManager.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/manager/NoopDependencyManager", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/DependencyCollectionContext;)Lorg/eclipse/aether/collection/DependencyManager;"}, {"nme": "manageDependency", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/Dependency;)Lorg/eclipse/aether/collection/DependencyManagement;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "INSTANCE", "dsc": "Lorg/eclipse/aether/collection/DependencyManager;"}]}, "org/eclipse/aether/util/listener/ChainedRepositoryListener.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/listener/ChainedRepositoryListener", "super": "org/eclipse/aether/AbstractRepositoryListener", "mthds": [{"nme": "newInstance", "acc": 9, "dsc": "(Lorg/eclipse/aether/RepositoryListener;Lorg/eclipse/aether/RepositoryListener;)Lorg/eclipse/aether/RepositoryListener;"}, {"nme": "<init>", "acc": 129, "dsc": "([Lorg/eclipse/aether/RepositoryListener;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)V", "sig": "(Lja<PERSON>/util/Collection<+Lorg/eclipse/aether/RepositoryListener;>;)V"}, {"nme": "add", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)V", "sig": "(Lja<PERSON>/util/Collection<+Lorg/eclipse/aether/RepositoryListener;>;)V"}, {"nme": "add", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryListener;)V"}, {"nme": "remove", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryListener;)V"}, {"nme": "handleError", "acc": 4, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;Lorg/eclipse/aether/RepositoryListener;<PERSON><PERSON><PERSON>/lang/RuntimeException;)V"}, {"nme": "artifactDeployed", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "artifactDeploying", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "artifactDescriptorInvalid", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "artifactDescriptorMissing", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "artifactDownloaded", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "artifactDownloading", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "artifactInstalled", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "artifactInstalling", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "artifactResolved", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "artifactResolving", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "metadataDeployed", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "metadataDeploying", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "metadataDownloaded", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "metadataDownloading", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "metadataInstalled", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "metadataInstalling", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "metadataInvalid", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "metadataResolved", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "metadataResolving", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}], "flds": [{"acc": 18, "nme": "listeners", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/RepositoryListener;>;"}]}, "org/eclipse/aether/util/version/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/util/version/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/util/graph/visitor/PreorderNodeListGenerator.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/visitor/PreorderNodeListGenerator", "super": "org/eclipse/aether/util/graph/visitor/AbstractDepthFirstNodeListGenerator", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "visitEnter", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Z"}, {"nme": "visitLeave", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Z"}, {"nme": "getClassPath", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getFiles", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/util/List;"}, {"nme": "getArtifacts", "acc": 4161, "dsc": "(Z)Ljava/util/List;"}, {"nme": "getDependencies", "acc": 4161, "dsc": "(Z)Ljava/util/List;"}, {"nme": "getNodes", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/util/List;"}], "flds": []}, "org/eclipse/aether/util/repository/JreProxySelector$JreProxyAuthentication.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/util/repository/JreProxySelector$JreProxyAuthentication", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "fill", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/AuthenticationContext;Ljava/lang/String;Ljava/util/Map;)V", "sig": "(Lorg/eclipse/aether/repository/AuthenticationContext;Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "digest", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/AuthenticationDigest;)V"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "INSTANCE", "dsc": "Lorg/eclipse/aether/repository/Authentication;"}]}, "org/eclipse/aether/util/graph/transformer/ConflictResolver$VersionSelector.class": {"ver": 52, "acc": 1057, "nme": "org/eclipse/aether/util/graph/transformer/ConflictResolver$VersionSelector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getInstance", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;Lorg/eclipse/aether/collection/DependencyGraphTransformationContext;)Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$VersionSelector;", "exs": ["org/eclipse/aether/RepositoryException"]}, {"nme": "selectVersion", "acc": 1025, "dsc": "(Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictContext;)V", "exs": ["org/eclipse/aether/RepositoryException"]}], "flds": []}, "org/eclipse/aether/util/FileUtils.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/FileUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "newTempFile", "acc": 9, "dsc": "()Lorg/eclipse/aether/util/FileUtils$TempFile;", "exs": ["java/io/IOException"]}, {"nme": "newTempFile", "acc": 9, "dsc": "(Ljava/nio/file/Path;)Lorg/eclipse/aether/util/FileUtils$CollocatedTempFile;", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 10, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "writeFile", "acc": 9, "dsc": "(Ljava/nio/file/Path;Lorg/eclipse/aether/util/FileUtils$FileWriter;)V", "exs": ["java/io/IOException"]}, {"nme": "writeFileWithBackup", "acc": 9, "dsc": "(Ljava/nio/file/Path;Lorg/eclipse/aether/util/FileUtils$FileWriter;)V", "exs": ["java/io/IOException"]}, {"nme": "writeFile", "acc": 10, "dsc": "(Ljava/nio/file/Path;Lorg/eclipse/aether/util/FileUtils$FileWriter;Z)V", "exs": ["java/io/IOException"]}, {"nme": "access$000", "acc": 4104, "dsc": "()Z"}, {"nme": "access$100", "acc": 4104, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "IS_WINDOWS", "dsc": "Z"}]}, "org/eclipse/aether/util/concurrency/RunnableErrorForwarder.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/concurrency/RunnableErrorForwarder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "wrap", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Runnable;)<PERSON><PERSON><PERSON>/lang/Runnable;"}, {"nme": "await", "acc": 1, "dsc": "()V"}, {"nme": "awaitTerminationOfAllRunnables", "acc": 2, "dsc": "()V"}, {"nme": "lambda$wrap$0", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/Runnable;)V"}], "flds": [{"acc": 18, "nme": "thread", "dsc": "<PERSON><PERSON><PERSON>/lang/Thread;"}, {"acc": 18, "nme": "counter", "dsc": "Ljava/util/concurrent/atomic/AtomicInteger;"}, {"acc": 18, "nme": "error", "dsc": "Ljava/util/concurrent/atomic/AtomicReference;", "sig": "Ljava/util/concurrent/atomic/AtomicReference<Ljava/lang/Throwable;>;"}]}, "org/eclipse/aether/util/graph/transformer/JavaScopeSelector.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/transformer/JavaScopeSelector", "super": "org/eclipse/aether/util/graph/transformer/ConflictResolver$ScopeSelector", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "selectScope", "acc": 1, "dsc": "(Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictContext;)V", "exs": ["org/eclipse/aether/RepositoryException"]}, {"nme": "chooseEffectiveScope", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Ljava/lang/String;", "sig": "(Ljava/util/Collection<Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictItem;>;)Ljava/lang/String;"}, {"nme": "chooseEffectiveScope", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)<PERSON>ja<PERSON>/lang/String;", "sig": "(<PERSON><PERSON><PERSON>/util/Set<Ljava/lang/String;>;)Ljava/lang/String;"}], "flds": []}, "org/eclipse/aether/util/graph/transformer/ConflictResolver$ScopeSelector.class": {"ver": 52, "acc": 1057, "nme": "org/eclipse/aether/util/graph/transformer/ConflictResolver$ScopeSelector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getInstance", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;Lorg/eclipse/aether/collection/DependencyGraphTransformationContext;)Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ScopeSelector;", "exs": ["org/eclipse/aether/RepositoryException"]}, {"nme": "selectScope", "acc": 1025, "dsc": "(Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictContext;)V", "exs": ["org/eclipse/aether/RepositoryException"]}], "flds": []}, "org/eclipse/aether/util/repository/ChainedWorkspaceReader$Key.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/util/repository/ChainedWorkspaceReader$Key", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Lorg/eclipse/aether/repository/WorkspaceReader;>;)V"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "keys", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/Object;>;"}]}, "org/eclipse/aether/util/graph/manager/DefaultDependencyManager$Key.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/util/graph/manager/DefaultDependencyManager$Key", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)V"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "artifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}, {"acc": 18, "nme": "hashCode", "dsc": "I"}]}, "org/eclipse/aether/util/graph/transformer/TransformationContextKeys.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/transformer/TransformationContextKeys", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "CONFLICT_IDS", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 25, "nme": "SORTED_CONFLICT_IDS", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 25, "nme": "CYCLIC_CONFLICT_IDS", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 25, "nme": "STATS", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "org/eclipse/aether/util/graph/manager/TransitiveDependencyManager$Key.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/util/graph/manager/TransitiveDependencyManager$Key", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)V"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "artifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}, {"acc": 18, "nme": "hashCode", "dsc": "I"}]}, "org/eclipse/aether/util/graph/selector/AndDependencySelector.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/selector/AndDependencySelector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 129, "dsc": "([Lorg/eclipse/aether/collection/DependencySelector;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)V", "sig": "(<PERSON><PERSON><PERSON>/util/Collection<+Lorg/eclipse/aether/collection/DependencySelector;>;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(<PERSON><PERSON><PERSON>/util/Set<Lorg/eclipse/aether/collection/DependencySelector;>;)V"}, {"nme": "newInstance", "acc": 9, "dsc": "(Lorg/eclipse/aether/collection/DependencySelector;Lorg/eclipse/aether/collection/DependencySelector;)Lorg/eclipse/aether/collection/DependencySelector;"}, {"nme": "selectDependency", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/Dependency;)Z"}, {"nme": "deriveChildSelector", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/DependencyCollectionContext;)Lorg/eclipse/aether/collection/DependencySelector;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "selectors", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<+Lorg/eclipse/aether/collection/DependencySelector;>;"}, {"acc": 2, "nme": "hashCode", "dsc": "I"}]}, "org/eclipse/aether/util/FileUtils$CollocatedTempFile.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/util/FileUtils$CollocatedTempFile", "super": "java/lang/Object", "mthds": [{"nme": "move", "acc": 1025, "dsc": "()V", "exs": ["java/io/IOException"]}], "flds": []}, "org/eclipse/aether/util/artifact/JavaScopes.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/artifact/JavaScopes", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "COMPILE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "compile"}, {"acc": 25, "nme": "PROVIDED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "provided"}, {"acc": 25, "nme": "SYSTEM", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "system"}, {"acc": 25, "nme": "RUNTIME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "runtime"}, {"acc": 25, "nme": "TEST", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "test"}]}, "org/eclipse/aether/util/filter/ScopeDependencyFilter.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/filter/ScopeDependencyFilter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lja<PERSON>/util/Collection;Ljava/util/Collection;)V", "sig": "(Ljava/util/Collection<Ljava/lang/String;>;Ljava/util/Collection<Ljava/lang/String;>;)V"}, {"nme": "<init>", "acc": 129, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "accept", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;<PERSON>ja<PERSON>/util/List;)Z", "sig": "(Lorg/eclipse/aether/graph/DependencyNode;Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;)Z"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "included", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 18, "nme": "excluded", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}]}, "org/eclipse/aether/util/graph/transformer/SimpleOptionalitySelector.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/transformer/SimpleOptionalitySelector", "super": "org/eclipse/aether/util/graph/transformer/ConflictResolver$OptionalitySelector", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "selectOptionality", "acc": 1, "dsc": "(Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictContext;)V", "exs": ["org/eclipse/aether/RepositoryException"]}, {"nme": "chooseEffectiveOptionality", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Z", "sig": "(Ljava/util/Collection<Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictItem;>;)Z"}], "flds": []}, "org/eclipse/aether/util/filter/OrDependencyFilter.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/filter/OrDependencyFilter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 129, "dsc": "([Lorg/eclipse/aether/graph/DependencyFilter;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)V", "sig": "(<PERSON><PERSON><PERSON>/util/Collection<Lorg/eclipse/aether/graph/DependencyFilter;>;)V"}, {"nme": "newInstance", "acc": 9, "dsc": "(Lorg/eclipse/aether/graph/DependencyFilter;Lorg/eclipse/aether/graph/DependencyFilter;)Lorg/eclipse/aether/graph/DependencyFilter;"}, {"nme": "accept", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;<PERSON>ja<PERSON>/util/List;)Z", "sig": "(Lorg/eclipse/aether/graph/DependencyNode;Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;)Z"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "filters", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Lorg/eclipse/aether/graph/DependencyFilter;>;"}]}, "org/eclipse/aether/util/FileUtils$2.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/util/FileUtils$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/Path;)V", "sig": "()V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "move", "acc": 1, "dsc": "()V"}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 18, "nme": "wantsMove", "dsc": "Ljava/util/concurrent/atomic/AtomicBoolean;"}, {"acc": 4112, "nme": "val$tempFile", "dsc": "Ljava/nio/file/Path;"}, {"acc": 4112, "nme": "val$file", "dsc": "Ljava/nio/file/Path;"}]}, "org/eclipse/aether/util/version/GenericVersion$Item.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/util/version/GenericVersion$Item", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "isNumber", "acc": 1, "dsc": "()Z"}, {"nme": "compareTo", "acc": 1, "dsc": "(Lorg/eclipse/aether/util/version/GenericVersion$Item;)I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "KIND_MAX", "dsc": "I", "val": 8}, {"acc": 24, "nme": "KIND_BIGINT", "dsc": "I", "val": 5}, {"acc": 24, "nme": "KIND_INT", "dsc": "I", "val": 4}, {"acc": 24, "nme": "KIND_STRING", "dsc": "I", "val": 3}, {"acc": 24, "nme": "KIND_QUALIFIER", "dsc": "I", "val": 2}, {"acc": 24, "nme": "KIND_MIN", "dsc": "I", "val": 0}, {"acc": 24, "nme": "MAX", "dsc": "Lorg/eclipse/aether/util/version/GenericVersion$Item;"}, {"acc": 24, "nme": "MIN", "dsc": "Lorg/eclipse/aether/util/version/GenericVersion$Item;"}, {"acc": 18, "nme": "kind", "dsc": "I"}, {"acc": 18, "nme": "value", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "org/eclipse/aether/util/graph/visitor/DependencyGraphDumper$ChildInfo.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/util/graph/visitor/DependencyGraphDumper$ChildInfo", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(I)V"}, {"nme": "formatIndentation", "acc": 1, "dsc": "(Z)<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 16, "nme": "count", "dsc": "I"}, {"acc": 0, "nme": "index", "dsc": "I"}]}, "org/eclipse/aether/util/filter/ExclusionsDependencyFilter.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/filter/ExclusionsDependencyFilter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)V", "sig": "(Lja<PERSON>/util/Collection<Ljava/lang/String;>;)V"}, {"nme": "accept", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;<PERSON>ja<PERSON>/util/List;)Z", "sig": "(Lorg/eclipse/aether/graph/DependencyNode;Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;)Z"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "excludes", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}]}, "org/eclipse/aether/util/graph/transformer/NearestVersionSelector$ConflictGroup.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/util/graph/transformer/NearestVersionSelector$ConflictGroup", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 16, "nme": "constraints", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lorg/eclipse/aether/version/VersionConstraint;>;"}, {"acc": 16, "nme": "candidates", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictItem;>;"}, {"acc": 0, "nme": "winner", "dsc": "Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictItem;"}]}, "org/eclipse/aether/util/graph/version/ContextualSnapshotVersionFilter.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/version/ContextualSnapshotVersionFilter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "isEnabled", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Z"}, {"nme": "filterVersions", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/VersionFilter$VersionFilterContext;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/DependencyCollectionContext;)Lorg/eclipse/aether/collection/VersionFilter;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 25, "nme": "CONFIG_PROP_ENABLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.snapshotFilter"}, {"acc": 18, "nme": "filter", "dsc": "Lorg/eclipse/aether/util/graph/version/SnapshotVersionFilter;"}]}, "org/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictItem.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictItem", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;Lorg/eclipse/aether/graph/DependencyNode;<PERSON><PERSON><PERSON>/lang/String;Z)V"}, {"nme": "<init>", "acc": 129, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;Lorg/eclipse/aether/graph/DependencyNode;II[<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "is<PERSON><PERSON>ling", "acc": 1, "dsc": "(Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictItem;)Z"}, {"nme": "getNode", "acc": 1, "dsc": "()Lorg/eclipse/aether/graph/DependencyNode;"}, {"nme": "getDependency", "acc": 1, "dsc": "()Lorg/eclipse/aether/graph/Dependency;"}, {"nme": "<PERSON><PERSON><PERSON>h", "acc": 1, "dsc": "()I"}, {"nme": "getScopes", "acc": 1, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Ljava/lang/String;>;"}, {"nme": "addScope", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getOptionalities", "acc": 1, "dsc": "()I"}, {"nme": "addOptional", "acc": 0, "dsc": "(Z)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 16, "nme": "parent", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;"}, {"acc": 16, "nme": "artifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}, {"acc": 0, "nme": "node", "dsc": "Lorg/eclipse/aether/graph/DependencyNode;"}, {"acc": 0, "nme": "depth", "dsc": "I"}, {"acc": 0, "nme": "scopes", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 0, "nme": "optionalities", "dsc": "I"}, {"acc": 25, "nme": "OPTIONAL_FALSE", "dsc": "I", "val": 1}, {"acc": 25, "nme": "OPTIONAL_TRUE", "dsc": "I", "val": 2}]}, "org/eclipse/aether/util/graph/transformer/ConflictResolver.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/transformer/ConflictResolver", "super": "java/lang/Object", "mthds": [{"nme": "getVerbosity", "acc": 10, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$Verbosity;"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$VersionSelector;Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ScopeSelector;Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$OptionalitySelector;Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ScopeDeriver;)V"}, {"nme": "transformGraph", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;Lorg/eclipse/aether/collection/DependencyGraphTransformationContext;)Lorg/eclipse/aether/graph/DependencyNode;", "exs": ["org/eclipse/aether/RepositoryException"]}, {"nme": "gatherConflictItems", "acc": 2, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$State;)Z", "exs": ["org/eclipse/aether/RepositoryException"]}, {"nme": "removeLosers", "acc": 10, "dsc": "(Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$State;)V"}, {"nme": "relatedSiblingsCount", "acc": 10, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/util/List;)J", "sig": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;)J"}, {"nme": "lambda$relatedSiblingsCount$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/eclipse/aether/artifact/Artifact;)Z"}, {"nme": "lambda$transformGraph$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Ljava/util/Collection;"}, {"nme": "access$000", "acc": 4104, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$Verbosity;"}, {"nme": "access$100", "acc": 4104, "dsc": "(Lorg/eclipse/aether/util/graph/transformer/ConflictResolver;)Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$VersionSelector;"}, {"nme": "access$200", "acc": 4104, "dsc": "(Lorg/eclipse/aether/util/graph/transformer/ConflictResolver;)Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ScopeSelector;"}, {"nme": "access$300", "acc": 4104, "dsc": "(Lorg/eclipse/aether/util/graph/transformer/ConflictResolver;)Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ScopeDeriver;"}, {"nme": "access$400", "acc": 4104, "dsc": "(Lorg/eclipse/aether/util/graph/transformer/ConflictResolver;)Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$OptionalitySelector;"}], "flds": [{"acc": 25, "nme": "CONFIG_PROP_VERBOSE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.conflictResolver.verbose"}, {"acc": 25, "nme": "NODE_DATA_WINNER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "conflict.winner"}, {"acc": 25, "nme": "NODE_DATA_ORIGINAL_SCOPE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "conflict.originalScope"}, {"acc": 25, "nme": "NODE_DATA_ORIGINAL_OPTIONALITY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "conflict.originalOptionality"}, {"acc": 18, "nme": "versionSelector", "dsc": "Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$VersionSelector;"}, {"acc": 18, "nme": "scopeSelector", "dsc": "Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ScopeSelector;"}, {"acc": 18, "nme": "scopeDeriver", "dsc": "Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ScopeDeriver;"}, {"acc": 18, "nme": "optionalitySelector", "dsc": "Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$OptionalitySelector;"}]}, "org/eclipse/aether/util/graph/transformer/ConflictIdSorter$ConflictId.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/util/graph/transformer/ConflictIdSorter$ConflictId", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;I)V"}, {"nme": "add", "acc": 1, "dsc": "(Lorg/eclipse/aether/util/graph/transformer/ConflictIdSorter$ConflictId;)V"}, {"nme": "pullup", "acc": 1, "dsc": "(I)V"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 16, "nme": "key", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 0, "nme": "children", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lorg/eclipse/aether/util/graph/transformer/ConflictIdSorter$ConflictId;>;"}, {"acc": 0, "nme": "inDegree", "dsc": "I"}, {"acc": 0, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "I"}]}, "org/eclipse/aether/util/graph/manager/DefaultDependencyManager.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/manager/DefaultDependencyManager", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 2, "dsc": "(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V", "sig": "(Ljava/util/Map<Ljava/lang/Object;Ljava/lang/String;>;Ljava/util/Map<Ljava/lang/Object;Ljava/lang/String;>;Ljava/util/Map<Ljava/lang/Object;Ljava/lang/Boolean;>;Ljava/util/Map<Ljava/lang/Object;Ljava/lang/String;>;Ljava/util/Map<Ljava/lang/Object;Ljava/util/Collection<Lorg/eclipse/aether/graph/Exclusion;>;>;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/DependencyCollectionContext;)Lorg/eclipse/aether/collection/DependencyManager;"}, {"nme": "manageDependency", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/Dependency;)Lorg/eclipse/aether/collection/DependencyManagement;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "lambda$deriveChildManager$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Ljava/util/Collection;"}], "flds": [{"acc": 18, "nme": "managedVersions", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Ljava/lang/String;>;"}, {"acc": 18, "nme": "managedScopes", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Ljava/lang/String;>;"}, {"acc": 18, "nme": "managedOptionals", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Ljava/lang/Boolean;>;"}, {"acc": 18, "nme": "managedLocalPaths", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Ljava/lang/String;>;"}, {"acc": 18, "nme": "managedExclusions", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Ljava/util/Collection<Lorg/eclipse/aether/graph/Exclusion;>;>;"}, {"acc": 2, "nme": "hashCode", "dsc": "I"}]}, "org/eclipse/aether/util/graph/transformer/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/util/graph/transformer/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/util/artifact/DelegatingArtifact.class": {"ver": 52, "acc": 1057, "nme": "org/eclipse/aether/util/artifact/DelegatingArtifact", "super": "org/eclipse/aether/artifact/AbstractArtifact", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)V"}, {"nme": "newInstance", "acc": 1028, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/util/artifact/DelegatingArtifact;"}, {"nme": "getGroupId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getArtifactId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setVersion", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "getBaseVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isSnapshot", "acc": 1, "dsc": "()Z"}, {"nme": "getClassifier", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getExtension", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getFile", "acc": 1, "dsc": "()Ljava/io/File;"}, {"nme": "setFile", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "getProperty", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getProperties", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "setProperties", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Lorg/eclipse/aether/artifact/Artifact;", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "delegate", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}]}, "org/eclipse/aether/util/graph/selector/OptionalDependencySelector.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/selector/OptionalDependencySelector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 2, "dsc": "(I)V"}, {"nme": "selectDependency", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/Dependency;)Z"}, {"nme": "deriveChildSelector", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/DependencyCollectionContext;)Lorg/eclipse/aether/collection/DependencySelector;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "depth", "dsc": "I"}]}, "org/eclipse/aether/util/repository/SimpleResolutionErrorPolicy.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/repository/SimpleResolutionErrorPolicy", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(ZZ)V"}, {"nme": "<init>", "acc": 1, "dsc": "(I)V"}, {"nme": "<init>", "acc": 1, "dsc": "(II)V"}, {"nme": "getArtifactPolicy", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/ResolutionErrorPolicyRequest;)I", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/ResolutionErrorPolicyRequest<Lorg/eclipse/aether/artifact/Artifact;>;)I"}, {"nme": "getMetadataPolicy", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/ResolutionErrorPolicyRequest;)I", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/ResolutionErrorPolicyRequest<Lorg/eclipse/aether/metadata/Metadata;>;)I"}], "flds": [{"acc": 18, "nme": "artifactPolicy", "dsc": "I"}, {"acc": 18, "nme": "metadataPolicy", "dsc": "I"}]}, "org/eclipse/aether/util/filter/DependencyFilterUtils.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/filter/DependencyFilterUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "notFilter", "acc": 9, "dsc": "(Lorg/eclipse/aether/graph/DependencyFilter;)Lorg/eclipse/aether/graph/DependencyFilter;"}, {"nme": "and<PERSON><PERSON><PERSON>", "acc": 137, "dsc": "([Lorg/eclipse/aether/graph/DependencyFilter;)Lorg/eclipse/aether/graph/DependencyFilter;"}, {"nme": "and<PERSON><PERSON><PERSON>", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Lorg/eclipse/aether/graph/DependencyFilter;", "sig": "(<PERSON><PERSON><PERSON>/util/Collection<Lorg/eclipse/aether/graph/DependencyFilter;>;)Lorg/eclipse/aether/graph/DependencyFilter;"}, {"nme": "orFilter", "acc": 137, "dsc": "([Lorg/eclipse/aether/graph/DependencyFilter;)Lorg/eclipse/aether/graph/DependencyFilter;"}, {"nme": "orFilter", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Lorg/eclipse/aether/graph/DependencyFilter;", "sig": "(<PERSON><PERSON><PERSON>/util/Collection<Lorg/eclipse/aether/graph/DependencyFilter;>;)Lorg/eclipse/aether/graph/DependencyFilter;"}, {"nme": "classpathFilter", "acc": 137, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/graph/DependencyFilter;"}, {"nme": "classpathFilter", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Lorg/eclipse/aether/graph/DependencyFilter;", "sig": "(Ljava/util/Collection<Ljava/lang/String;>;)Lorg/eclipse/aether/graph/DependencyFilter;"}], "flds": []}, "org/eclipse/aether/util/graph/version/ChainedVersionFilter.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/version/ChainedVersionFilter", "super": "java/lang/Object", "mthds": [{"nme": "newInstance", "acc": 9, "dsc": "(Lorg/eclipse/aether/collection/VersionFilter;Lorg/eclipse/aether/collection/VersionFilter;)Lorg/eclipse/aether/collection/VersionFilter;"}, {"nme": "newInstance", "acc": 137, "dsc": "([Lorg/eclipse/aether/collection/VersionFilter;)Lorg/eclipse/aether/collection/VersionFilter;"}, {"nme": "newInstance", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Lorg/eclipse/aether/collection/VersionFilter;", "sig": "(Lja<PERSON>/util/Collection<+Lorg/eclipse/aether/collection/VersionFilter;>;)Lorg/eclipse/aether/collection/VersionFilter;"}, {"nme": "<init>", "acc": 2, "dsc": "([Lorg/eclipse/aether/collection/VersionFilter;)V"}, {"nme": "filterVersions", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/VersionFilter$VersionFilterContext;)V", "exs": ["org/eclipse/aether/RepositoryException"]}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/DependencyCollectionContext;)Lorg/eclipse/aether/collection/VersionFilter;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "filters", "dsc": "[Lorg/eclipse/aether/collection/VersionFilter;"}, {"acc": 2, "nme": "hashCode", "dsc": "I"}]}, "org/eclipse/aether/util/graph/version/SnapshotVersionFilter.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/version/SnapshotVersionFilter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "filterVersions", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/VersionFilter$VersionFilterContext;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/DependencyCollectionContext;)Lorg/eclipse/aether/collection/VersionFilter;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": []}, "org/eclipse/aether/util/graph/manager/ClassicDependencyManager$Key.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/util/graph/manager/ClassicDependencyManager$Key", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)V"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "artifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}, {"acc": 18, "nme": "hashCode", "dsc": "I"}]}, "org/eclipse/aether/util/graph/transformer/NearestVersionSelector.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/transformer/NearestVersionSelector", "super": "org/eclipse/aether/util/graph/transformer/ConflictResolver$VersionSelector", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "selectVersion", "acc": 1, "dsc": "(Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictContext;)V", "exs": ["org/eclipse/aether/RepositoryException"]}, {"nme": "backtrack", "acc": 2, "dsc": "(Lorg/eclipse/aether/util/graph/transformer/NearestVersionSelector$ConflictGroup;Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictContext;)V", "exs": ["org/eclipse/aether/collection/UnsolvableVersionConflictException"]}, {"nme": "isAcceptable", "acc": 2, "dsc": "(Lorg/eclipse/aether/util/graph/transformer/NearestVersionSelector$ConflictGroup;Lorg/eclipse/aether/version/Version;)Z"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictItem;Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictItem;)Z"}, {"nme": "newFailure", "acc": 2, "dsc": "(Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictContext;)Lorg/eclipse/aether/collection/UnsolvableVersionConflictException;"}, {"nme": "lambda$newFailure$0", "acc": 4106, "dsc": "(Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictContext;Lorg/eclipse/aether/graph/DependencyNode;Ljava/util/List;)Z"}], "flds": []}, "org/eclipse/aether/util/graph/transformer/ConflictResolver$Verbosity.class": {"ver": 52, "acc": 16433, "nme": "org/eclipse/aether/util/graph/transformer/ConflictResolver$Verbosity", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$Verbosity;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$Verbosity;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$Verbosity;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "NONE", "dsc": "Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$Verbosity;"}, {"acc": 16409, "nme": "STANDARD", "dsc": "Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$Verbosity;"}, {"acc": 16409, "nme": "FULL", "dsc": "Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$Verbosity;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lorg/eclipse/aether/util/graph/transformer/ConflictResolver$Verbosity;"}]}, "org/eclipse/aether/util/repository/ComponentAuthentication.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/util/repository/ComponentAuthentication", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "fill", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/AuthenticationContext;Ljava/lang/String;Ljava/util/Map;)V", "sig": "(Lorg/eclipse/aether/repository/AuthenticationContext;Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "digest", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/AuthenticationDigest;)V"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "eqClass", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)Z", "sig": "<T:Ljava/lang/Object;>(TT;TT;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "key", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "value", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "org/eclipse/aether/util/graph/manager/TransitiveDependencyManager.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/manager/TransitiveDependencyManager", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 2, "dsc": "(ILjava/util/Map;Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V", "sig": "(ILjava/util/Map<Ljava/lang/Object;Ljava/lang/String;>;Ljava/util/Map<Ljava/lang/Object;Ljava/lang/String;>;Ljava/util/Map<Ljava/lang/Object;Ljava/lang/Boolean;>;Ljava/util/Map<Ljava/lang/Object;Ljava/lang/String;>;Ljava/util/Map<Ljava/lang/Object;Ljava/util/Collection<Lorg/eclipse/aether/graph/Exclusion;>;>;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/DependencyCollectionContext;)Lorg/eclipse/aether/collection/DependencyManager;"}, {"nme": "manageDependency", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/Dependency;)Lorg/eclipse/aether/collection/DependencyManagement;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "lambda$deriveChildManager$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Ljava/util/Collection;"}], "flds": [{"acc": 18, "nme": "managedVersions", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Ljava/lang/String;>;"}, {"acc": 18, "nme": "managedScopes", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Ljava/lang/String;>;"}, {"acc": 18, "nme": "managedOptionals", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Ljava/lang/Boolean;>;"}, {"acc": 18, "nme": "managedLocalPaths", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Ljava/lang/String;>;"}, {"acc": 18, "nme": "managedExclusions", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Ljava/util/Collection<Lorg/eclipse/aether/graph/Exclusion;>;>;"}, {"acc": 18, "nme": "depth", "dsc": "I"}, {"acc": 2, "nme": "hashCode", "dsc": "I"}]}, "org/eclipse/aether/util/listener/ChainedTransferListener.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/listener/ChainedTransferListener", "super": "org/eclipse/aether/transfer/AbstractTransferListener", "mthds": [{"nme": "newInstance", "acc": 9, "dsc": "(Lorg/eclipse/aether/transfer/TransferListener;Lorg/eclipse/aether/transfer/TransferListener;)Lorg/eclipse/aether/transfer/TransferListener;"}, {"nme": "<init>", "acc": 129, "dsc": "([Lorg/eclipse/aether/transfer/TransferListener;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)V", "sig": "(Ljava/util/Collection<+Lorg/eclipse/aether/transfer/TransferListener;>;)V"}, {"nme": "add", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)V", "sig": "(Ljava/util/Collection<+Lorg/eclipse/aether/transfer/TransferListener;>;)V"}, {"nme": "add", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferListener;)V"}, {"nme": "remove", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferListener;)V"}, {"nme": "handleError", "acc": 4, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;Lorg/eclipse/aether/transfer/TransferListener;Ljava/lang/RuntimeException;)V"}, {"nme": "transferInitiated", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V", "exs": ["org/eclipse/aether/transfer/TransferCancelledException"]}, {"nme": "transferStarted", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V", "exs": ["org/eclipse/aether/transfer/TransferCancelledException"]}, {"nme": "transferProgressed", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V", "exs": ["org/eclipse/aether/transfer/TransferCancelledException"]}, {"nme": "transferCorrupted", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V", "exs": ["org/eclipse/aether/transfer/TransferCancelledException"]}, {"nme": "transferSucceeded", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V"}, {"nme": "transferFailed", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V"}], "flds": [{"acc": 18, "nme": "listeners", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/transfer/TransferListener;>;"}]}, "org/eclipse/aether/util/graph/selector/ScopeDependencySelector.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/util/graph/selector/ScopeDependencySelector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lja<PERSON>/util/Collection;Ljava/util/Collection;)V", "sig": "(Ljava/util/Collection<Ljava/lang/String;>;Ljava/util/Collection<Ljava/lang/String;>;)V"}, {"nme": "clone", "acc": 10, "dsc": "(L<PERSON><PERSON>/util/Collection;)Ljava/util/Collection;", "sig": "(Ljava/util/Collection<Ljava/lang/String;>;)Ljava/util/Collection<Ljava/lang/String;>;"}, {"nme": "<init>", "acc": 129, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(Z<PERSON>java/util/Collection;Ljava/util/Collection;)V", "sig": "(ZLjava/util/Collection<Ljava/lang/String;>;Ljava/util/Collection<Ljava/lang/String;>;)V"}, {"nme": "selectDependency", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/Dependency;)Z"}, {"nme": "deriveChildSelector", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/DependencyCollectionContext;)Lorg/eclipse/aether/collection/DependencySelector;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "transitive", "dsc": "Z"}, {"acc": 18, "nme": "included", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Ljava/lang/String;>;"}, {"acc": 18, "nme": "excluded", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Ljava/lang/String;>;"}]}}}}