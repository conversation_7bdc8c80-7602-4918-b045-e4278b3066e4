{"md5": "8b1aa0b78ffdb501dd4a6cbc0cbeff68", "sha2": "983ce00d50a9f78ad1b805e21e4fd71807fa6ebf", "sha256": "5f96dafbc411ee4b1e8426368d0d31d05ab5a4dace69808143142a0017598721", "contents": {"classes": {"org/apache/maven/model/building/DefaultModelBuilder.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/building/DefaultModelBuilder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "setModelProcessor", "acc": 1, "dsc": "(Lorg/apache/maven/model/building/ModelProcessor;)Lorg/apache/maven/model/building/DefaultModelBuilder;"}, {"nme": "setModelValidator", "acc": 1, "dsc": "(Lorg/apache/maven/model/validation/ModelValidator;)Lorg/apache/maven/model/building/DefaultModelBuilder;"}, {"nme": "setModelNormalizer", "acc": 1, "dsc": "(Lorg/apache/maven/model/normalization/ModelNormalizer;)Lorg/apache/maven/model/building/DefaultModelBuilder;"}, {"nme": "setModelInterpolator", "acc": 1, "dsc": "(Lorg/apache/maven/model/interpolation/ModelInterpolator;)Lorg/apache/maven/model/building/DefaultModelBuilder;"}, {"nme": "setModelPathTranslator", "acc": 1, "dsc": "(Lorg/apache/maven/model/path/ModelPathTranslator;)Lorg/apache/maven/model/building/DefaultModelBuilder;"}, {"nme": "setModelUrlNormalizer", "acc": 1, "dsc": "(Lorg/apache/maven/model/path/ModelUrlNormalizer;)Lorg/apache/maven/model/building/DefaultModelBuilder;"}, {"nme": "setSuperPomProvider", "acc": 1, "dsc": "(Lorg/apache/maven/model/superpom/SuperPomProvider;)Lorg/apache/maven/model/building/DefaultModelBuilder;"}, {"nme": "setProfileSelector", "acc": 1, "dsc": "(Lorg/apache/maven/model/profile/ProfileSelector;)Lorg/apache/maven/model/building/DefaultModelBuilder;"}, {"nme": "setProfileInjector", "acc": 1, "dsc": "(Lorg/apache/maven/model/profile/ProfileInjector;)Lorg/apache/maven/model/building/DefaultModelBuilder;"}, {"nme": "setInheritanceAssembler", "acc": 1, "dsc": "(Lorg/apache/maven/model/inheritance/InheritanceAssembler;)Lorg/apache/maven/model/building/DefaultModelBuilder;"}, {"nme": "setDependencyManagementImporter", "acc": 1, "dsc": "(Lorg/apache/maven/model/composition/DependencyManagementImporter;)Lorg/apache/maven/model/building/DefaultModelBuilder;"}, {"nme": "setDependencyManagementInjector", "acc": 1, "dsc": "(Lorg/apache/maven/model/management/DependencyManagementInjector;)Lorg/apache/maven/model/building/DefaultModelBuilder;"}, {"nme": "setLifecycleBindingsInjector", "acc": 1, "dsc": "(Lorg/apache/maven/model/plugin/LifecycleBindingsInjector;)Lorg/apache/maven/model/building/DefaultModelBuilder;"}, {"nme": "setPluginConfigurationExpander", "acc": 1, "dsc": "(Lorg/apache/maven/model/plugin/PluginConfigurationExpander;)Lorg/apache/maven/model/building/DefaultModelBuilder;"}, {"nme": "setPluginManagementInjector", "acc": 1, "dsc": "(Lorg/apache/maven/model/management/PluginManagementInjector;)Lorg/apache/maven/model/building/DefaultModelBuilder;"}, {"nme": "setReportConfigurationExpander", "acc": 1, "dsc": "(Lorg/apache/maven/model/plugin/ReportConfigurationExpander;)Lorg/apache/maven/model/building/DefaultModelBuilder;"}, {"nme": "setReportingConverter", "acc": 1, "dsc": "(Lorg/apache/maven/model/plugin/ReportingConverter;)Lorg/apache/maven/model/building/DefaultModelBuilder;"}, {"nme": "setProfileActivationFilePathInterpolator", "acc": 1, "dsc": "(Lorg/apache/maven/model/path/ProfileActivationFilePathInterpolator;)Lorg/apache/maven/model/building/DefaultModelBuilder;"}, {"nme": "build", "acc": 1, "dsc": "(Lorg/apache/maven/model/building/ModelBuildingRequest;)Lorg/apache/maven/model/building/ModelBuildingResult;", "exs": ["org/apache/maven/model/building/ModelBuildingException"]}, {"nme": "build", "acc": 4, "dsc": "(Lorg/apache/maven/model/building/ModelBuildingRequest;Ljava/util/Collection;)Lorg/apache/maven/model/building/ModelBuildingResult;", "sig": "(Lorg/apache/maven/model/building/ModelBuildingRequest;Ljava/util/Collection<Ljava/lang/String;>;)Lorg/apache/maven/model/building/ModelBuildingResult;", "exs": ["org/apache/maven/model/building/ModelBuildingException"]}, {"nme": "getInterpolatedActivations", "acc": 2, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/profile/DefaultProfileActivationContext;Lorg/apache/maven/model/building/DefaultModelProblemCollector;)Ljava/util/Map;", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/profile/DefaultProfileActivationContext;Lorg/apache/maven/model/building/DefaultModelProblemCollector;)Ljava/util/Map<Ljava/lang/String;Lorg/apache/maven/model/Activation;>;"}, {"nme": "replaceWithInterpolatedValue", "acc": 2, "dsc": "(Lorg/apache/maven/model/ActivationFile;Lorg/apache/maven/model/profile/ProfileActivationContext;Lorg/apache/maven/model/building/DefaultModelProblemCollector;)V"}, {"nme": "build", "acc": 1, "dsc": "(Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelBuildingResult;)Lorg/apache/maven/model/building/ModelBuildingResult;", "exs": ["org/apache/maven/model/building/ModelBuildingException"]}, {"nme": "build", "acc": 2, "dsc": "(Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelBuildingResult;Ljava/util/Collection;)Lorg/apache/maven/model/building/ModelBuildingResult;", "sig": "(Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelBuildingResult;Ljava/util/Collection<Ljava/lang/String;>;)Lorg/apache/maven/model/building/ModelBuildingResult;", "exs": ["org/apache/maven/model/building/ModelBuildingException"]}, {"nme": "buildRawModel", "acc": 1, "dsc": "(Ljava/io/File;IZ)Lorg/apache/maven/model/building/Result;", "sig": "(Ljava/io/File;IZ)Lorg/apache/maven/model/building/Result<+Lorg/apache/maven/model/Model;>;"}, {"nme": "readModel", "acc": 2, "dsc": "(Lorg/apache/maven/model/building/ModelSource;Ljava/io/File;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/DefaultModelProblemCollector;)Lorg/apache/maven/model/Model;", "exs": ["org/apache/maven/model/building/ModelBuildingException"]}, {"nme": "getProfileActivationContext", "acc": 2, "dsc": "(Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/Model;)Lorg/apache/maven/model/profile/DefaultProfileActivationContext;"}, {"nme": "configureResolver", "acc": 2, "dsc": "(Lorg/apache/maven/model/resolution/ModelResolver;Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/DefaultModelProblemCollector;)V"}, {"nme": "configureResolver", "acc": 2, "dsc": "(Lorg/apache/maven/model/resolution/ModelResolver;Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/DefaultModelProblemCollector;Z)V"}, {"nme": "checkPluginVersions", "acc": 2, "dsc": "(Ljava/util/List;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V", "sig": "(Ljava/util/List<Lorg/apache/maven/model/building/ModelData;>;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}, {"nme": "assembleInheritance", "acc": 2, "dsc": "(Ljava/util/List;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V", "sig": "(Ljava/util/List<Lorg/apache/maven/model/building/ModelData;>;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}, {"nme": "getProfileActivations", "acc": 2, "dsc": "(Lorg/apache/maven/model/Model;Z)Ljava/util/Map;", "sig": "(Lorg/apache/maven/model/Model;Z)Ljava/util/Map<Ljava/lang/String;Lorg/apache/maven/model/Activation;>;"}, {"nme": "injectProfileActivations", "acc": 2, "dsc": "(Lorg/apache/maven/model/Model;Ljava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Ljava/util/Map<Ljava/lang/String;Lorg/apache/maven/model/Activation;>;)V"}, {"nme": "interpolateModel", "acc": 2, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)Lorg/apache/maven/model/Model;"}, {"nme": "readParent", "acc": 2, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelSource;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/DefaultModelProblemCollector;)Lorg/apache/maven/model/building/ModelData;", "exs": ["org/apache/maven/model/building/ModelBuildingException"]}, {"nme": "readParentLocally", "acc": 2, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelSource;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/DefaultModelProblemCollector;)Lorg/apache/maven/model/building/ModelData;", "exs": ["org/apache/maven/model/building/ModelBuildingException"]}, {"nme": "rawChildVersionReferencesParent", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "getParentPomFile", "acc": 2, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelSource;)Lorg/apache/maven/model/building/ModelSource;"}, {"nme": "readParentExternally", "acc": 2, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/DefaultModelProblemCollector;)Lorg/apache/maven/model/building/ModelData;", "exs": ["org/apache/maven/model/building/ModelBuildingException"]}, {"nme": "getSuperModel", "acc": 2, "dsc": "()Lorg/apache/maven/model/Model;"}, {"nme": "importDependencyManagement", "acc": 2, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/DefaultModelProblemCollector;Ljava/util/Collection;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/DefaultModelProblemCollector;Ljava/util/Collection<Ljava/lang/String;>;)V"}, {"nme": "put<PERSON>ache", "acc": 2, "dsc": "(Lorg/apache/maven/model/building/ModelCache;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/building/ModelCacheTag;Ljava/lang/Object;)V", "sig": "<T:Ljava/lang/Object;>(Lorg/apache/maven/model/building/ModelCache;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/building/ModelCacheTag<TT;>;TT;)V"}, {"nme": "getCache", "acc": 2, "dsc": "(Lorg/apache/maven/model/building/ModelCache;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/building/ModelCacheTag;)Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Lorg/apache/maven/model/building/ModelCache;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/building/ModelCacheTag<TT;>;)TT;"}, {"nme": "fireEvent", "acc": 2, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;Lorg/apache/maven/model/building/ModelBuildingEventCatapult;)V", "exs": ["org/apache/maven/model/building/ModelBuildingException"]}, {"nme": "containsCoordinates", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "hasModelErrors", "acc": 4, "dsc": "(Lorg/apache/maven/model/building/ModelProblemCollectorExt;)Z"}, {"nme": "hasFatalErrors", "acc": 4, "dsc": "(Lorg/apache/maven/model/building/ModelProblemCollectorExt;)Z"}, {"nme": "lambda$getProfileActivationContext$0", "acc": 4106, "dsc": "(Lorg/apache/maven/model/Model;Ljava/lang/Object;)Ljava/lang/Object;"}], "flds": [{"acc": 2, "nme": "modelProcessor", "dsc": "Lorg/apache/maven/model/building/ModelProcessor;"}, {"acc": 2, "nme": "modelValidator", "dsc": "Lorg/apache/maven/model/validation/ModelValidator;"}, {"acc": 2, "nme": "modelNormalizer", "dsc": "Lorg/apache/maven/model/normalization/ModelNormalizer;"}, {"acc": 2, "nme": "modelInterpolator", "dsc": "Lorg/apache/maven/model/interpolation/ModelInterpolator;"}, {"acc": 2, "nme": "modelPathTranslator", "dsc": "Lorg/apache/maven/model/path/ModelPathTranslator;"}, {"acc": 2, "nme": "modelUrlNormalizer", "dsc": "Lorg/apache/maven/model/path/ModelUrlNormalizer;"}, {"acc": 2, "nme": "superPomProvider", "dsc": "Lorg/apache/maven/model/superpom/SuperPomProvider;"}, {"acc": 2, "nme": "inheritanceAssembler", "dsc": "Lorg/apache/maven/model/inheritance/InheritanceAssembler;"}, {"acc": 2, "nme": "profileSelector", "dsc": "Lorg/apache/maven/model/profile/ProfileSelector;"}, {"acc": 2, "nme": "profileInjector", "dsc": "Lorg/apache/maven/model/profile/ProfileInjector;"}, {"acc": 2, "nme": "pluginManagementInjector", "dsc": "Lorg/apache/maven/model/management/PluginManagementInjector;"}, {"acc": 2, "nme": "dependencyManagementInjector", "dsc": "Lorg/apache/maven/model/management/DependencyManagementInjector;"}, {"acc": 2, "nme": "dependencyManagementImporter", "dsc": "Lorg/apache/maven/model/composition/DependencyManagementImporter;"}, {"acc": 2, "nme": "lifecycleBindingsInjector", "dsc": "Lorg/apache/maven/model/plugin/LifecycleBindingsInjector;"}, {"acc": 2, "nme": "pluginConfigurationExpander", "dsc": "Lorg/apache/maven/model/plugin/PluginConfigurationExpander;"}, {"acc": 2, "nme": "reportConfigurationExpander", "dsc": "Lorg/apache/maven/model/plugin/ReportConfigurationExpander;"}, {"acc": 2, "nme": "reportingConverter", "dsc": "Lorg/apache/maven/model/plugin/ReportingConverter;"}, {"acc": 2, "nme": "profileActivationFilePathInterpolator", "dsc": "Lorg/apache/maven/model/path/ProfileActivationFilePathInterpolator;"}], "vanns": [{"dsc": "Ljavax/inject/Named;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/model/building/DefaultModelBuilderFactory$StubLifecycleBindingsInjector.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/building/DefaultModelBuilderFactory$StubLifecycleBindingsInjector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "injectLifecycleBindings", "acc": 1, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/apache/maven/model/building/DefaultModelBuilderFactory$1;)V"}], "flds": []}, "org/apache/maven/model/building/ModelCacheTag.class": {"ver": 52, "acc": 1536, "nme": "org/apache/maven/model/building/ModelCacheTag", "super": "java/lang/Object", "mthds": [{"nme": "getName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getType", "acc": 1025, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<TT;>;"}, {"nme": "intoCache", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(TT;)TT;"}, {"nme": "fromCache", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(TT;)TT;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "RAW", "dsc": "Lorg/apache/maven/model/building/ModelCacheTag;", "sig": "Lorg/apache/maven/model/building/ModelCacheTag<Lorg/apache/maven/model/building/ModelData;>;"}, {"acc": 25, "nme": "IMPORT", "dsc": "Lorg/apache/maven/model/building/ModelCacheTag;", "sig": "Lorg/apache/maven/model/building/ModelCacheTag<Lorg/apache/maven/model/DependencyManagement;>;"}]}, "org/apache/maven/model/management/PluginManagementInjector.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/management/PluginManagementInjector", "super": "java/lang/Object", "mthds": [{"nme": "injectManagement", "acc": 1025, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}], "flds": []}, "org/apache/maven/model/merge/MavenModelMerger.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/merge/MavenModelMerger", "super": "org/apache/maven/model/merge/ModelMerger", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "mergeModel", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_Name", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_Url", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_Organization", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_IssueManagement", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_CiManagement", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_ModelVersion", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_ArtifactId", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_Profiles", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_Prerequisites", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_Licenses", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_Developers", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_Contributors", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_MailingLists", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModelBase_Modules", "acc": 4, "dsc": "(Lorg/apache/maven/model/ModelBase;Lorg/apache/maven/model/ModelBase;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/ModelBase;Lorg/apache/maven/model/ModelBase;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModelBase_Repositories", "acc": 4, "dsc": "(Lorg/apache/maven/model/ModelBase;Lorg/apache/maven/model/ModelBase;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/ModelBase;Lorg/apache/maven/model/ModelBase;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModelBase_PluginRepositories", "acc": 4, "dsc": "(Lorg/apache/maven/model/ModelBase;Lorg/apache/maven/model/ModelBase;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/ModelBase;Lorg/apache/maven/model/ModelBase;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeBuildBase_Filters", "acc": 4, "dsc": "(Lorg/apache/maven/model/BuildBase;Lorg/apache/maven/model/BuildBase;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/BuildBase;Lorg/apache/maven/model/BuildBase;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeBuildBase_Resources", "acc": 4, "dsc": "(Lorg/apache/maven/model/BuildBase;Lorg/apache/maven/model/BuildBase;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/BuildBase;Lorg/apache/maven/model/BuildBase;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeBuildBase_TestResources", "acc": 4, "dsc": "(Lorg/apache/maven/model/BuildBase;Lorg/apache/maven/model/BuildBase;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/BuildBase;Lorg/apache/maven/model/BuildBase;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeDistributionManagement_Repository", "acc": 4, "dsc": "(Lorg/apache/maven/model/DistributionManagement;Lorg/apache/maven/model/DistributionManagement;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/DistributionManagement;Lorg/apache/maven/model/DistributionManagement;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeDistributionManagement_SnapshotRepository", "acc": 4, "dsc": "(Lorg/apache/maven/model/DistributionManagement;Lorg/apache/maven/model/DistributionManagement;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/DistributionManagement;Lorg/apache/maven/model/DistributionManagement;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeDistributionManagement_Site", "acc": 4, "dsc": "(Lorg/apache/maven/model/DistributionManagement;Lorg/apache/maven/model/DistributionManagement;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/DistributionManagement;Lorg/apache/maven/model/DistributionManagement;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeSite", "acc": 4, "dsc": "(Lorg/apache/maven/model/Site;Lorg/apache/maven/model/Site;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Site;Lorg/apache/maven/model/Site;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "isSiteEmpty", "acc": 4, "dsc": "(Lorg/apache/maven/model/Site;)Z"}, {"nme": "mergeSite_Url", "acc": 4, "dsc": "(Lorg/apache/maven/model/Site;Lorg/apache/maven/model/Site;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Site;Lorg/apache/maven/model/Site;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeScm_Url", "acc": 4, "dsc": "(Lorg/apache/maven/model/Scm;Lorg/apache/maven/model/Scm;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Scm;Lorg/apache/maven/model/Scm;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeScm_Connection", "acc": 4, "dsc": "(Lorg/apache/maven/model/Scm;Lorg/apache/maven/model/Scm;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Scm;Lorg/apache/maven/model/Scm;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeScm_DeveloperConnection", "acc": 4, "dsc": "(Lorg/apache/maven/model/Scm;Lorg/apache/maven/model/Scm;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Scm;Lorg/apache/maven/model/Scm;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergePlugin_Executions", "acc": 4, "dsc": "(Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergePluginExecution_Goals", "acc": 4, "dsc": "(Lorg/apache/maven/model/PluginExecution;Lorg/apache/maven/model/PluginExecution;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/PluginExecution;Lorg/apache/maven/model/PluginExecution;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeReportPlugin_ReportSets", "acc": 4, "dsc": "(Lorg/apache/maven/model/ReportPlugin;Lorg/apache/maven/model/ReportPlugin;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/ReportPlugin;Lorg/apache/maven/model/ReportPlugin;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "getDependency<PERSON>ey", "acc": 4, "dsc": "(Lorg/apache/maven/model/Dependency;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getPlugin<PERSON>ey", "acc": 4, "dsc": "(Lorg/apache/maven/model/Plugin;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getPluginExecutionKey", "acc": 4, "dsc": "(Lorg/apache/maven/model/PluginExecution;)Ljava/lang/Object;"}, {"nme": "getReportPluginKey", "acc": 4, "dsc": "(Lorg/apache/maven/model/ReportPlugin;)Ljava/lang/Object;"}, {"nme": "getReportSetKey", "acc": 4, "dsc": "(Lorg/apache/maven/model/ReportSet;)Ljava/lang/Object;"}, {"nme": "getRepositoryBaseKey", "acc": 4, "dsc": "(Lorg/apache/maven/model/RepositoryBase;)L<PERSON><PERSON>/lang/Object;"}, {"nme": "getExtensionKey", "acc": 4, "dsc": "(Lorg/apache/maven/model/Extension;)Ljava/lang/Object;"}, {"nme": "getExclusionKey", "acc": 4, "dsc": "(Lorg/apache/maven/model/Exclusion;)L<PERSON><PERSON>/lang/Object;"}, {"nme": "extrapolateChildUrl", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z<PERSON><PERSON>va/util/Map;)Ljava/lang/String;", "sig": "(Ljava/lang/String;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)Ljava/lang/String;"}], "flds": [{"acc": 25, "nme": "CHILD_PATH_ADJUSTMENT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "child-path-adjustment"}, {"acc": 25, "nme": "ARTIFACT_ID", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "artifact-id"}]}, "org/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction$CacheItem.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction$CacheItem", "super": "java/lang/Object", "mthds": [{"nme": "isQualifiedForInterpolation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "isQualifiedForInterpolation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;Ljava/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;Ljava/lang/Class<*>;)Z"}, {"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V"}, {"nme": "interpolate", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction;)V"}, {"nme": "isArray", "acc": 0, "dsc": "()Z"}, {"nme": "access$000", "acc": 4104, "dsc": "(Lorg/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction$CacheItem;)Z"}], "flds": [{"acc": 18, "nme": "isArray", "dsc": "Z"}, {"acc": 18, "nme": "isQualifiedForInterpolation", "dsc": "Z"}, {"acc": 18, "nme": "fields", "dsc": "[Lorg/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction$CacheField;"}]}, "org/apache/maven/model/management/DependencyManagementInjector.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/management/DependencyManagementInjector", "super": "java/lang/Object", "mthds": [{"nme": "injectManagement", "acc": 1025, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}], "flds": []}, "org/apache/maven/model/interpolation/ModelInterpolator.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/interpolation/ModelInterpolator", "super": "java/lang/Object", "mthds": [{"nme": "interpolateModel", "acc": 1025, "dsc": "(Lorg/apache/maven/model/Model;Ljava/io/File;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)Lorg/apache/maven/model/Model;"}], "flds": []}, "org/apache/maven/model/profile/ProfileActivationContext.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/profile/ProfileActivationContext", "super": "java/lang/Object", "mthds": [{"nme": "getActiveProfileIds", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "getInactiveProfileIds", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "getSystemProperties", "acc": 1025, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "getUserProperties", "acc": 1025, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "getProjectDirectory", "acc": 1025, "dsc": "()Ljava/io/File;"}, {"nme": "getProjectProperties", "acc": 1025, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}], "flds": [{"acc": 25, "nme": "PROPERTY_NAME_PACKAGING", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "packaging"}]}, "org/apache/maven/model/building/DefaultModelProblemCollector.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/building/DefaultModelProblemCollector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/apache/maven/model/building/ModelBuildingResult;)V"}, {"nme": "hasFatalErrors", "acc": 1, "dsc": "()Z"}, {"nme": "hasErrors", "acc": 1, "dsc": "()Z"}, {"nme": "getProblems", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/building/ModelProblem;>;"}, {"nme": "setSource", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setSource", "acc": 1, "dsc": "(Lorg/apache/maven/model/Model;)V"}, {"nme": "getSource", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getModelId", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setRootModel", "acc": 1, "dsc": "(Lorg/apache/maven/model/Model;)V"}, {"nme": "getRootModel", "acc": 1, "dsc": "()Lorg/apache/maven/model/Model;"}, {"nme": "getRootModelId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "add", "acc": 1, "dsc": "(Lorg/apache/maven/model/building/ModelProblem;)V"}, {"nme": "addAll", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Lorg/apache/maven/model/building/ModelProblem;>;)V"}, {"nme": "add", "acc": 1, "dsc": "(Lorg/apache/maven/model/building/ModelProblemCollectorRequest;)V"}, {"nme": "newModelBuildingException", "acc": 1, "dsc": "()Lorg/apache/maven/model/building/ModelBuildingException;"}], "flds": [{"acc": 18, "nme": "result", "dsc": "Lorg/apache/maven/model/building/ModelBuildingResult;"}, {"acc": 2, "nme": "problems", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/model/building/ModelProblem;>;"}, {"acc": 2, "nme": "source", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "sourceModel", "dsc": "Lorg/apache/maven/model/Model;"}, {"acc": 2, "nme": "rootModel", "dsc": "Lorg/apache/maven/model/Model;"}, {"acc": 2, "nme": "severities", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Lorg/apache/maven/model/building/ModelProblem$Severity;>;"}]}, "org/apache/maven/model/building/ModelProblemCollectorExt.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/building/ModelProblemCollectorExt", "super": "java/lang/Object", "mthds": [{"nme": "getProblems", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/building/ModelProblem;>;"}], "flds": []}, "org/apache/maven/model/management/DefaultDependencyManagementInjector.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/management/DefaultDependencyManagementInjector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "injectManagement", "acc": 1, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}], "flds": [{"acc": 2, "nme": "merger", "dsc": "Lorg/apache/maven/model/management/DefaultDependencyManagementInjector$ManagementModelMerger;"}], "vanns": [{"dsc": "Ljavax/inject/Named;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/model/interpolation/StringSearchModelInterpolator$InnerInterpolator.class": {"ver": 52, "acc": 1536, "nme": "org/apache/maven/model/interpolation/StringSearchModelInterpolator$InnerInterpolator", "super": "java/lang/Object", "mthds": [{"nme": "interpolate", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": []}, "org/apache/maven/model/normalization/ModelNormalizer.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/normalization/ModelNormalizer", "super": "java/lang/Object", "mthds": [{"nme": "mergeDuplicates", "acc": 1025, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}, {"nme": "injectDefaultValues", "acc": 1025, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}], "flds": []}, "org/apache/maven/model/interpolation/AbstractStringBasedModelInterpolator$2.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/interpolation/AbstractStringBasedModelInterpolator$2", "super": "org/codehaus/plexus/interpolation/AbstractValueSource", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/apache/maven/model/interpolation/AbstractStringBasedModelInterpolator;ZLjava/io/File;)V"}, {"nme": "getValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 4112, "nme": "val$projectDir", "dsc": "Ljava/io/File;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lorg/apache/maven/model/interpolation/AbstractStringBasedModelInterpolator;"}]}, "org/apache/maven/model/plugin/PluginConfigurationExpander.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/plugin/PluginConfigurationExpander", "super": "java/lang/Object", "mthds": [{"nme": "expandPluginConfiguration", "acc": 1025, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}], "flds": []}, "org/apache/maven/model/profile/activation/JdkVersionProfileActivator$RangeValue.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/profile/activation/JdkVersionProfileActivator$RangeValue", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "access$000", "acc": 4104, "dsc": "(Lorg/apache/maven/model/profile/activation/JdkVersionProfileActivator$RangeValue;)Ljava/lang/String;"}, {"nme": "access$100", "acc": 4104, "dsc": "(Lorg/apache/maven/model/profile/activation/JdkVersionProfileActivator$RangeValue;)Z"}], "flds": [{"acc": 2, "nme": "value", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "closed", "dsc": "Z"}]}, "org/apache/maven/model/path/UrlNormalizer.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/path/UrlNormalizer", "super": "java/lang/Object", "mthds": [{"nme": "normalize", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": []}, "org/apache/maven/model/plugin/DefaultPluginConfigurationExpander.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/plugin/DefaultPluginConfigurationExpander", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "expandPluginConfiguration", "acc": 1, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}, {"nme": "expand", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(<PERSON>ja<PERSON>/util/List<Lorg/apache/maven/model/Plugin;>;)V"}], "flds": [], "vanns": [{"dsc": "Ljavax/inject/Named;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/model/profile/activation/JdkVersionProfileActivator.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/profile/activation/JdkVersionProfileActivator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "isActive", "acc": 1, "dsc": "(Lorg/apache/maven/model/Profile;Lorg/apache/maven/model/profile/ProfileActivationContext;Lorg/apache/maven/model/building/ModelProblemCollector;)Z"}, {"nme": "presentInConfig", "acc": 1, "dsc": "(Lorg/apache/maven/model/Profile;Lorg/apache/maven/model/profile/ProfileActivationContext;Lorg/apache/maven/model/building/ModelProblemCollector;)Z"}, {"nme": "isInRange", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/List;)Z", "sig": "(Ljava/lang/String;Ljava/util/List<Lorg/apache/maven/model/profile/activation/JdkVersionProfileActivator$RangeValue;>;)Z"}, {"nme": "getRelationOrder", "acc": 10, "dsc": "(Lja<PERSON>/lang/String;Lorg/apache/maven/model/profile/activation/JdkVersionProfileActivator$RangeValue;Z)I"}, {"nme": "addZeroTokens", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/List;I)V", "sig": "(<PERSON><PERSON><PERSON>/util/List<Ljava/lang/String;>;I)V"}, {"nme": "isRange", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "getRange", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;", "sig": "(Ljava/lang/String;)Ljava/util/List<Lorg/apache/maven/model/profile/activation/JdkVersionProfileActivator$RangeValue;>;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "FILTER_1", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 26, "nme": "FILTER_2", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 26, "nme": "FILTER_3", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}], "vanns": [{"dsc": "Ljavax/inject/Named;", "vals": ["value", "jdk-version"]}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/model/interpolation/UrlNormalizingPostProcessor.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/interpolation/UrlNormalizingPostProcessor", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/apache/maven/model/path/UrlNormalizer;)V"}, {"nme": "execute", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "URL_EXPRESSIONS", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 2, "nme": "normalizer", "dsc": "Lorg/apache/maven/model/path/UrlNormalizer;"}]}, "org/apache/maven/model/building/Result.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/building/Result", "super": "java/lang/Object", "mthds": [{"nme": "success", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/building/Result;", "sig": "<T:Ljava/lang/Object;>(TT;)Lorg/apache/maven/model/building/Result<TT;>;"}, {"nme": "success", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Ljava/lang/Iterable;)Lorg/apache/maven/model/building/Result;", "sig": "<T:Ljava/lang/Object;>(TT;Ljava/lang/Iterable<+Lorg/apache/maven/model/building/ModelProblem;>;)Lorg/apache/maven/model/building/Result<TT;>;"}, {"nme": "success", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;[Lorg/apache/maven/model/building/Result;)Lorg/apache/maven/model/building/Result;", "sig": "<T:Ljava/lang/Object;>(TT;[Lorg/apache/maven/model/building/Result<*>;)Lorg/apache/maven/model/building/Result<TT;>;"}, {"nme": "error", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)Lorg/apache/maven/model/building/Result;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Iterable<+Lorg/apache/maven/model/building/ModelProblem;>;)Lorg/apache/maven/model/building/Result<TT;>;"}, {"nme": "error", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/building/Result;", "sig": "<T:Ljava/lang/Object;>(TT;)Lorg/apache/maven/model/building/Result<TT;>;"}, {"nme": "error", "acc": 9, "dsc": "(Lorg/apache/maven/model/building/Result;)Lorg/apache/maven/model/building/Result;", "sig": "<T:Ljava/lang/Object;>(Lorg/apache/maven/model/building/Result<*>;)Lorg/apache/maven/model/building/Result<TT;>;"}, {"nme": "error", "acc": 137, "dsc": "([Lorg/apache/maven/model/building/Result;)Lorg/apache/maven/model/building/Result;", "sig": "<T:Ljava/lang/Object;>([Lorg/apache/maven/model/building/Result<*>;)Lorg/apache/maven/model/building/Result<TT;>;"}, {"nme": "error", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Ljava/lang/Iterable;)Lorg/apache/maven/model/building/Result;", "sig": "<T:Ljava/lang/Object;>(TT;Ljava/lang/Iterable<+Lorg/apache/maven/model/building/ModelProblem;>;)Lorg/apache/maven/model/building/Result<TT;>;"}, {"nme": "newResult", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Ljava/lang/Iterable;)Lorg/apache/maven/model/building/Result;", "sig": "<T:Ljava/lang/Object;>(TT;Ljava/lang/Iterable<+Lorg/apache/maven/model/building/ModelProblem;>;)Lorg/apache/maven/model/building/Result<TT;>;"}, {"nme": "addProblem", "acc": 9, "dsc": "(Lorg/apache/maven/model/building/Result;Lorg/apache/maven/model/building/ModelProblem;)Lorg/apache/maven/model/building/Result;", "sig": "<T:Ljava/lang/Object;>(Lorg/apache/maven/model/building/Result<TT;>;Lorg/apache/maven/model/building/ModelProblem;)Lorg/apache/maven/model/building/Result<TT;>;"}, {"nme": "addProblems", "acc": 9, "dsc": "(Lorg/apache/maven/model/building/Result;<PERSON><PERSON><PERSON>/lang/Iterable;)Lorg/apache/maven/model/building/Result;", "sig": "<T:Ljava/lang/Object;>(Lorg/apache/maven/model/building/Result<TT;>;Ljava/lang/Iterable<+Lorg/apache/maven/model/building/ModelProblem;>;)Lorg/apache/maven/model/building/Result<TT;>;"}, {"nme": "addProblems", "acc": 137, "dsc": "(Lorg/apache/maven/model/building/Result;[Lorg/apache/maven/model/building/Result;)Lorg/apache/maven/model/building/Result;", "sig": "<T:Ljava/lang/Object;>(Lorg/apache/maven/model/building/Result<TT;>;[Lorg/apache/maven/model/building/Result<*>;)Lorg/apache/maven/model/building/Result<TT;>;"}, {"nme": "newResultSet", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)Lorg/apache/maven/model/building/Result;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Iterable<+Lorg/apache/maven/model/building/Result<+TT;>;>;)Lorg/apache/maven/model/building/Result<Ljava/lang/Iterable<TT;>;>;"}, {"nme": "hasErrors", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Iterable<+Lorg/apache/maven/model/building/ModelProblem;>;)Z"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Iterable;)V", "sig": "(ZTT;Ljava/lang/Iterable<+Lorg/apache/maven/model/building/ModelProblem;>;)V"}, {"nme": "getProblems", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Iterable;", "sig": "()Ljava/lang/Iterable<+Lorg/apache/maven/model/building/ModelProblem;>;"}, {"nme": "get", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}, {"nme": "hasErrors", "acc": 1, "dsc": "()Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "errors", "dsc": "Z"}, {"acc": 18, "nme": "value", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;", "sig": "TT;"}, {"acc": 18, "nme": "problems", "dsc": "<PERSON><PERSON><PERSON>/lang/Iterable;", "sig": "Ljava/lang/Iterable<+Lorg/apache/maven/model/building/ModelProblem;>;"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "org/apache/maven/model/interpolation/DefaultModelVersionProcessor.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/interpolation/DefaultModelVersionProcessor", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "isValidProperty", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "overwriteModelProperties", "acc": 1, "dsc": "(Ljava/util/Properties;Lorg/apache/maven/model/building/ModelBuildingRequest;)V"}], "flds": [{"acc": 26, "nme": "SHA1_PROPERTY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "sha1"}, {"acc": 26, "nme": "CHANGELIST_PROPERTY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "changelist"}, {"acc": 26, "nme": "REVISION_PROPERTY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "revision"}], "vanns": [{"dsc": "Ljavax/inject/Named;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/model/building/DefaultModelBuildingEvent.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/building/DefaultModelBuildingEvent", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}, {"nme": "getModel", "acc": 1, "dsc": "()Lorg/apache/maven/model/Model;"}, {"nme": "getRequest", "acc": 1, "dsc": "()Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "getProblems", "acc": 1, "dsc": "()Lorg/apache/maven/model/building/ModelProblemCollector;"}], "flds": [{"acc": 18, "nme": "model", "dsc": "Lorg/apache/maven/model/Model;"}, {"acc": 18, "nme": "request", "dsc": "Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"acc": 18, "nme": "problems", "dsc": "Lorg/apache/maven/model/building/ModelProblemCollector;"}]}, "org/apache/maven/model/building/ModelData.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/building/ModelData", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/apache/maven/model/building/ModelSource;Lorg/apache/maven/model/Model;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(Lorg/apache/maven/model/building/ModelSource;Lorg/apache/maven/model/Model;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "getSource", "acc": 1, "dsc": "()Lorg/apache/maven/model/building/ModelSource;"}, {"nme": "getModel", "acc": 1, "dsc": "()Lorg/apache/maven/model/Model;"}, {"nme": "setModel", "acc": 1, "dsc": "(Lorg/apache/maven/model/Model;)V"}, {"nme": "getRawModel", "acc": 1, "dsc": "()Lorg/apache/maven/model/Model;"}, {"nme": "setRawModel", "acc": 1, "dsc": "(Lorg/apache/maven/model/Model;)V"}, {"nme": "getActiveProfiles", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/Profile;>;"}, {"nme": "setActiveProfiles", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Lorg/apache/maven/model/Profile;>;)V"}, {"nme": "getGroupId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setGroupId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getArtifactId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setArtifactId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setVersion", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "source", "dsc": "Lorg/apache/maven/model/building/ModelSource;"}, {"acc": 2, "nme": "model", "dsc": "Lorg/apache/maven/model/Model;"}, {"acc": 2, "nme": "rawModel", "dsc": "Lorg/apache/maven/model/Model;"}, {"acc": 2, "nme": "activeProfiles", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/model/Profile;>;"}, {"acc": 2, "nme": "groupId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "artifactId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/apache/maven/model/management/DefaultPluginManagementInjector$ManagementModelMerger.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/management/DefaultPluginManagementInjector$ManagementModelMerger", "super": "org/apache/maven/model/merge/MavenModelMerger", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "mergeManagedBuildPlugins", "acc": 1, "dsc": "(Lorg/apache/maven/model/Model;)V"}, {"nme": "mergePluginContainerPlugins", "acc": 2, "dsc": "(Lorg/apache/maven/model/PluginContainer;Lorg/apache/maven/model/PluginContainer;)V"}, {"nme": "mergePlugin_Executions", "acc": 4, "dsc": "(Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}], "flds": []}, "org/apache/maven/model/building/ModelProblemCollectorRequest.class": {"ver": 52, "acc": 49, "nme": "org/apache/maven/model/building/ModelProblemCollectorRequest", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/apache/maven/model/building/ModelProblem$Severity;Lorg/apache/maven/model/building/ModelProblem$Version;)V"}, {"nme": "getSeverity", "acc": 1, "dsc": "()Lorg/apache/maven/model/building/ModelProblem$Severity;"}, {"nme": "getVersion", "acc": 1, "dsc": "()Lorg/apache/maven/model/building/ModelProblem$Version;"}, {"nme": "getException", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Exception;"}, {"nme": "setException", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Exception;)Lorg/apache/maven/model/building/ModelProblemCollectorRequest;"}, {"nme": "getMessage", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setMessage", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/apache/maven/model/building/ModelProblemCollectorRequest;"}, {"nme": "getLocation", "acc": 1, "dsc": "()Lorg/apache/maven/model/InputLocation;"}, {"nme": "setLocation", "acc": 1, "dsc": "(Lorg/apache/maven/model/InputLocation;)Lorg/apache/maven/model/building/ModelProblemCollectorRequest;"}], "flds": [{"acc": 18, "nme": "severity", "dsc": "Lorg/apache/maven/model/building/ModelProblem$Severity;"}, {"acc": 18, "nme": "version", "dsc": "Lorg/apache/maven/model/building/ModelProblem$Version;"}, {"acc": 2, "nme": "exception", "dsc": "<PERSON><PERSON><PERSON>/lang/Exception;"}, {"acc": 2, "nme": "message", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}]}, "org/apache/maven/model/building/DefaultModelProblem.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/building/DefaultModelProblem", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/lang/String;Lorg/apache/maven/model/building/ModelProblem$Severity;Lorg/apache/maven/model/building/ModelProblem$Version;Lorg/apache/maven/model/Model;IILjava/lang/Exception;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/lang/String;Lorg/apache/maven/model/building/ModelProblem$Severity;Lorg/apache/maven/model/building/ModelProblem$Version;Ljava/lang/String;IILjava/lang/String;Ljava/lang/Exception;)V"}, {"nme": "getSource", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLineNumber", "acc": 1, "dsc": "()I"}, {"nme": "getColumnNumber", "acc": 1, "dsc": "()I"}, {"nme": "getModelId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getException", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Exception;"}, {"nme": "getMessage", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getSeverity", "acc": 1, "dsc": "()Lorg/apache/maven/model/building/ModelProblem$Severity;"}, {"nme": "getVersion", "acc": 1, "dsc": "()Lorg/apache/maven/model/building/ModelProblem$Version;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "source", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "lineNumber", "dsc": "I"}, {"acc": 18, "nme": "columnNumber", "dsc": "I"}, {"acc": 18, "nme": "modelId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "message", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "exception", "dsc": "<PERSON><PERSON><PERSON>/lang/Exception;"}, {"acc": 18, "nme": "severity", "dsc": "Lorg/apache/maven/model/building/ModelProblem$Severity;"}, {"acc": 18, "nme": "version", "dsc": "Lorg/apache/maven/model/building/ModelProblem$Version;"}]}, "org/apache/maven/model/profile/activation/FileProfileActivator.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/profile/activation/FileProfileActivator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "setProfileActivationFilePathInterpolator", "acc": 1, "dsc": "(Lorg/apache/maven/model/path/ProfileActivationFilePathInterpolator;)Lorg/apache/maven/model/profile/activation/FileProfileActivator;"}, {"nme": "isActive", "acc": 1, "dsc": "(Lorg/apache/maven/model/Profile;Lorg/apache/maven/model/profile/ProfileActivationContext;Lorg/apache/maven/model/building/ModelProblemCollector;)Z"}, {"nme": "presentInConfig", "acc": 1, "dsc": "(Lorg/apache/maven/model/Profile;Lorg/apache/maven/model/profile/ProfileActivationContext;Lorg/apache/maven/model/building/ModelProblemCollector;)Z"}], "flds": [{"acc": 2, "nme": "profileActivationFilePathInterpolator", "dsc": "Lorg/apache/maven/model/path/ProfileActivationFilePathInterpolator;"}], "vanns": [{"dsc": "Ljavax/inject/Named;", "vals": ["value", "file"]}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/model/io/DefaultModelWriter.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/io/DefaultModelWriter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "write", "acc": 1, "dsc": "(Ljava/io/File;Ljava/util/Map;Lorg/apache/maven/model/Model;)V", "sig": "(Ljava/io/File;Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;Lorg/apache/maven/model/Model;)V", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Writer;<PERSON><PERSON><PERSON>/util/Map;Lorg/apache/maven/model/Model;)V", "sig": "(<PERSON>ja<PERSON>/io/Writer;Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;Lorg/apache/maven/model/Model;)V", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Ljava/io/OutputStream;Ljava/util/Map;Lorg/apache/maven/model/Model;)V", "sig": "(Ljava/io/OutputStream;Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;Lorg/apache/maven/model/Model;)V", "exs": ["java/io/IOException"]}], "flds": [], "vanns": [{"dsc": "Ljavax/inject/Named;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/model/building/ModelBuildingEvent.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/building/ModelBuildingEvent", "super": "java/lang/Object", "mthds": [{"nme": "getModel", "acc": 1025, "dsc": "()Lorg/apache/maven/model/Model;"}, {"nme": "getRequest", "acc": 1025, "dsc": "()Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "getProblems", "acc": 1025, "dsc": "()Lorg/apache/maven/model/building/ModelProblemCollector;"}], "flds": []}, "org/apache/maven/model/building/ModelProblem$Version.class": {"ver": 52, "acc": 16433, "nme": "org/apache/maven/model/building/ModelProblem$Version", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lorg/apache/maven/model/building/ModelProblem$Version;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/apache/maven/model/building/ModelProblem$Version;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Lorg/apache/maven/model/building/ModelProblem$Version;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "BASE", "dsc": "Lorg/apache/maven/model/building/ModelProblem$Version;"}, {"acc": 16409, "nme": "V20", "dsc": "Lorg/apache/maven/model/building/ModelProblem$Version;"}, {"acc": 16409, "nme": "V30", "dsc": "Lorg/apache/maven/model/building/ModelProblem$Version;"}, {"acc": 16409, "nme": "V31", "dsc": "Lorg/apache/maven/model/building/ModelProblem$Version;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lorg/apache/maven/model/building/ModelProblem$Version;"}]}, "org/apache/maven/model/resolution/UnresolvableModelException.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/resolution/UnresolvableModelException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getGroupId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getArtifactId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "groupId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "artifactId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/apache/maven/model/building/ModelBuildingResult.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/building/ModelBuildingResult", "super": "java/lang/Object", "mthds": [{"nme": "getModelIds", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "getEffectiveModel", "acc": 1025, "dsc": "()Lorg/apache/maven/model/Model;"}, {"nme": "getRawModel", "acc": 1025, "dsc": "()Lorg/apache/maven/model/Model;"}, {"nme": "getRawModel", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/apache/maven/model/Model;"}, {"nme": "getActivePomProfiles", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/List<Lorg/apache/maven/model/Profile;>;"}, {"nme": "getActiveExternalProfiles", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/Profile;>;"}, {"nme": "getProblems", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/building/ModelProblem;>;"}], "flds": []}, "org/apache/maven/model/profile/DefaultProfileSelector.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/profile/DefaultProfileSelector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "addProfileActivator", "acc": 1, "dsc": "(Lorg/apache/maven/model/profile/activation/ProfileActivator;)Lorg/apache/maven/model/profile/DefaultProfileSelector;"}, {"nme": "getActiveProfiles", "acc": 1, "dsc": "(Ljava/util/Collection;Lorg/apache/maven/model/profile/ProfileActivationContext;Lorg/apache/maven/model/building/ModelProblemCollector;)Ljava/util/List;", "sig": "(Ljava/util/Collection<Lorg/apache/maven/model/Profile;>;Lorg/apache/maven/model/profile/ProfileActivationContext;Lorg/apache/maven/model/building/ModelProblemCollector;)Ljava/util/List<Lorg/apache/maven/model/Profile;>;"}, {"nme": "isActive", "acc": 2, "dsc": "(Lorg/apache/maven/model/Profile;Lorg/apache/maven/model/profile/ProfileActivationContext;Lorg/apache/maven/model/building/ModelProblemCollector;)Z"}, {"nme": "isActiveByDefault", "acc": 2, "dsc": "(Lorg/apache/maven/model/Profile;)Z"}], "flds": [{"acc": 2, "nme": "activators", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/model/profile/activation/ProfileActivator;>;"}], "vanns": [{"dsc": "Ljavax/inject/Named;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/model/interpolation/PathTranslatingPostProcessor.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/interpolation/PathTranslatingPostProcessor", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lja<PERSON>/util/List;Ljava/util/Collection;Ljava/io/File;Lorg/apache/maven/model/path/PathTranslator;)V", "sig": "(Ljava/util/List<Ljava/lang/String;>;Ljava/util/Collection<Ljava/lang/String;>;Ljava/io/File;Lorg/apache/maven/model/path/PathTranslator;)V"}, {"nme": "execute", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 18, "nme": "unprefixedPathKeys", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Ljava/lang/String;>;"}, {"acc": 18, "nme": "projectDir", "dsc": "Ljava/io/File;"}, {"acc": 18, "nme": "pathTranslator", "dsc": "Lorg/apache/maven/model/path/PathTranslator;"}, {"acc": 18, "nme": "expressionPrefixes", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}]}, "org/apache/maven/model/path/ModelUrlNormalizer.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/path/ModelUrlNormalizer", "super": "java/lang/Object", "mthds": [{"nme": "normalize", "acc": 1025, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;)V"}], "flds": []}, "org/apache/maven/model/path/ModelPathTranslator.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/path/ModelPathTranslator", "super": "java/lang/Object", "mthds": [{"nme": "alignToBaseDirectory", "acc": 1025, "dsc": "(Lorg/apache/maven/model/Model;Ljava/io/File;Lorg/apache/maven/model/building/ModelBuildingRequest;)V"}], "flds": []}, "org/apache/maven/model/inheritance/DefaultInheritanceAssembler.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/inheritance/DefaultInheritanceAssembler", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "assembleModelInheritance", "acc": 1, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}, {"nme": "getChildPathAdjustment", "acc": 2, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;Ljava/lang/String;)Ljava/lang/String;"}], "flds": [{"acc": 2, "nme": "merger", "dsc": "Lorg/apache/maven/model/inheritance/DefaultInheritanceAssembler$InheritanceModelMerger;"}, {"acc": 26, "nme": "CHILD_DIRECTORY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "child-directory"}, {"acc": 26, "nme": "CHILD_DIRECTORY_PROPERTY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "project.directory"}], "vanns": [{"dsc": "Ljavax/inject/Named;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/model/interpolation/StringVisitorModelInterpolator$1.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/interpolation/StringVisitorModelInterpolator$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/apache/maven/model/interpolation/StringVisitorModelInterpolator;Ljava/util/Map;Lorg/codehaus/plexus/interpolation/StringSearchInterpolator;Lorg/codehaus/plexus/interpolation/RecursionInterceptor;Lorg/apache/maven/model/building/ModelProblemCollector;)V", "sig": "()V"}, {"nme": "interpolate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": [{"acc": 4112, "nme": "val$cache", "dsc": "Ljava/util/Map;"}, {"acc": 4112, "nme": "val$interpolator", "dsc": "Lorg/codehaus/plexus/interpolation/StringSearchInterpolator;"}, {"acc": 4112, "nme": "val$recursionInterceptor", "dsc": "Lorg/codehaus/plexus/interpolation/RecursionInterceptor;"}, {"acc": 4112, "nme": "val$problems", "dsc": "Lorg/apache/maven/model/building/ModelProblemCollector;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lorg/apache/maven/model/interpolation/StringVisitorModelInterpolator;"}]}, "org/apache/maven/model/superpom/SuperPomProvider.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/superpom/SuperPomProvider", "super": "java/lang/Object", "mthds": [{"nme": "getSuperModel", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/apache/maven/model/Model;"}], "flds": []}, "org/apache/maven/model/plugin/LifecycleBindingsInjector.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/plugin/LifecycleBindingsInjector", "super": "java/lang/Object", "mthds": [{"nme": "injectLifecycleBindings", "acc": 1025, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}], "flds": []}, "org/apache/maven/model/building/StringModelSource.class": {"ver": 52, "acc": 131105, "nme": "org/apache/maven/model/building/StringModelSource", "super": "org/apache/maven/building/StringSource", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;<PERSON>ja<PERSON>/lang/String;)V"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "org/apache/maven/model/io/ModelWriter.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/io/ModelWriter", "super": "java/lang/Object", "mthds": [{"nme": "write", "acc": 1025, "dsc": "(Ljava/io/File;Ljava/util/Map;Lorg/apache/maven/model/Model;)V", "sig": "(Ljava/io/File;Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;Lorg/apache/maven/model/Model;)V", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/io/Writer;<PERSON><PERSON><PERSON>/util/Map;Lorg/apache/maven/model/Model;)V", "sig": "(<PERSON>ja<PERSON>/io/Writer;Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;Lorg/apache/maven/model/Model;)V", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1025, "dsc": "(Ljava/io/OutputStream;Ljava/util/Map;Lorg/apache/maven/model/Model;)V", "sig": "(Ljava/io/OutputStream;Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;Lorg/apache/maven/model/Model;)V", "exs": ["java/io/IOException"]}], "flds": []}, "org/apache/maven/model/management/DefaultDependencyManagementInjector$ManagementModelMerger.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/management/DefaultDependencyManagementInjector$ManagementModelMerger", "super": "org/apache/maven/model/merge/MavenModelMerger", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "mergeManagedDependencies", "acc": 1, "dsc": "(Lorg/apache/maven/model/Model;)V"}, {"nme": "mergeDependency_Optional", "acc": 4, "dsc": "(Lorg/apache/maven/model/Dependency;Lorg/apache/maven/model/Dependency;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Dependency;Lorg/apache/maven/model/Dependency;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeDependency_Exclusions", "acc": 4, "dsc": "(Lorg/apache/maven/model/Dependency;Lorg/apache/maven/model/Dependency;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Dependency;Lorg/apache/maven/model/Dependency;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}], "flds": []}, "org/apache/maven/model/inheritance/DefaultInheritanceAssembler$InheritanceModelMerger.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/inheritance/DefaultInheritanceAssembler$InheritanceModelMerger", "super": "org/apache/maven/model/merge/MavenModelMerger", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "extrapolateChildUrl", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z<PERSON><PERSON>va/util/Map;)Ljava/lang/String;", "sig": "(Ljava/lang/String;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)Ljava/lang/String;"}, {"nme": "appendPath", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)<PERSON>java/lang/String;"}, {"nme": "concat<PERSON>ath", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "mergeModelBase_Properties", "acc": 4, "dsc": "(Lorg/apache/maven/model/ModelBase;Lorg/apache/maven/model/ModelBase;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/ModelBase;Lorg/apache/maven/model/ModelBase;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "putAll", "acc": 2, "dsc": "(Ljava/util/Map;Ljava/util/Map;Ljava/lang/Object;)V", "sig": "(Ljava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;Ljava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;Ljava/lang/Object;)V"}, {"nme": "mergePluginContainer_Plugins", "acc": 4, "dsc": "(Lorg/apache/maven/model/PluginContainer;Lorg/apache/maven/model/PluginContainer;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/PluginContainer;Lorg/apache/maven/model/PluginContainer;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergePlugin", "acc": 4, "dsc": "(Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeReporting_Plugins", "acc": 4, "dsc": "(Lorg/apache/maven/model/Reporting;Lorg/apache/maven/model/Reporting;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Reporting;Lorg/apache/maven/model/Reporting;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}], "flds": []}, "org/apache/maven/model/path/DefaultUrlNormalizer.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/path/DefaultUrlNormalizer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "normalize", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": [], "vanns": [{"dsc": "Ljavax/inject/Named;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/model/profile/activation/PropertyProfileActivator.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/profile/activation/PropertyProfileActivator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "isActive", "acc": 1, "dsc": "(Lorg/apache/maven/model/Profile;Lorg/apache/maven/model/profile/ProfileActivationContext;Lorg/apache/maven/model/building/ModelProblemCollector;)Z"}, {"nme": "presentInConfig", "acc": 1, "dsc": "(Lorg/apache/maven/model/Profile;Lorg/apache/maven/model/profile/ProfileActivationContext;Lorg/apache/maven/model/building/ModelProblemCollector;)Z"}], "flds": [], "vanns": [{"dsc": "Ljavax/inject/Named;", "vals": ["value", "property"]}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/model/building/DefaultModelBuilderFactory.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/building/DefaultModelBuilderFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "newModelProcessor", "acc": 4, "dsc": "()Lorg/apache/maven/model/building/ModelProcessor;"}, {"nme": "newModelLocator", "acc": 4, "dsc": "()Lorg/apache/maven/model/locator/ModelLocator;"}, {"nme": "newModelReader", "acc": 4, "dsc": "()Lorg/apache/maven/model/io/ModelReader;"}, {"nme": "newProfileSelector", "acc": 4, "dsc": "()Lorg/apache/maven/model/profile/ProfileSelector;"}, {"nme": "newProfileActivators", "acc": 4, "dsc": "()[Lorg/apache/maven/model/profile/activation/ProfileActivator;"}, {"nme": "newProfileActivationFilePathInterpolator", "acc": 4, "dsc": "()Lorg/apache/maven/model/path/ProfileActivationFilePathInterpolator;"}, {"nme": "newUrlNormalizer", "acc": 4, "dsc": "()Lorg/apache/maven/model/path/UrlNormalizer;"}, {"nme": "newPathTranslator", "acc": 4, "dsc": "()Lorg/apache/maven/model/path/PathTranslator;"}, {"nme": "newModelInterpolator", "acc": 4, "dsc": "()Lorg/apache/maven/model/interpolation/ModelInterpolator;"}, {"nme": "newModelVersionPropertiesProcessor", "acc": 4, "dsc": "()Lorg/apache/maven/model/interpolation/ModelVersionProcessor;"}, {"nme": "newModelValidator", "acc": 4, "dsc": "()Lorg/apache/maven/model/validation/ModelValidator;"}, {"nme": "newModelNormalizer", "acc": 4, "dsc": "()Lorg/apache/maven/model/normalization/ModelNormalizer;"}, {"nme": "newModelPathTranslator", "acc": 4, "dsc": "()Lorg/apache/maven/model/path/ModelPathTranslator;"}, {"nme": "newModelUrlNormalizer", "acc": 4, "dsc": "()Lorg/apache/maven/model/path/ModelUrlNormalizer;"}, {"nme": "newInheritanceAssembler", "acc": 4, "dsc": "()Lorg/apache/maven/model/inheritance/InheritanceAssembler;"}, {"nme": "newProfileInjector", "acc": 4, "dsc": "()Lorg/apache/maven/model/profile/ProfileInjector;"}, {"nme": "newSuperPomProvider", "acc": 4, "dsc": "()Lorg/apache/maven/model/superpom/SuperPomProvider;"}, {"nme": "newDependencyManagementImporter", "acc": 4, "dsc": "()Lorg/apache/maven/model/composition/DependencyManagementImporter;"}, {"nme": "newDependencyManagementInjector", "acc": 4, "dsc": "()Lorg/apache/maven/model/management/DependencyManagementInjector;"}, {"nme": "newLifecycleBindingsInjector", "acc": 4, "dsc": "()Lorg/apache/maven/model/plugin/LifecycleBindingsInjector;"}, {"nme": "newPluginManagementInjector", "acc": 4, "dsc": "()Lorg/apache/maven/model/management/PluginManagementInjector;"}, {"nme": "newPluginConfigurationExpander", "acc": 4, "dsc": "()Lorg/apache/maven/model/plugin/PluginConfigurationExpander;"}, {"nme": "newReportConfigurationExpander", "acc": 4, "dsc": "()Lorg/apache/maven/model/plugin/ReportConfigurationExpander;"}, {"nme": "newReportingConverter", "acc": 4, "dsc": "()Lorg/apache/maven/model/plugin/ReportingConverter;"}, {"nme": "newInstance", "acc": 1, "dsc": "()Lorg/apache/maven/model/building/DefaultModelBuilder;"}], "flds": []}, "org/apache/maven/model/building/UrlModelSource.class": {"ver": 52, "acc": 131105, "nme": "org/apache/maven/model/building/UrlModelSource", "super": "org/apache/maven/building/UrlSource", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/net/URL;)V"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "org/apache/maven/model/building/ModelBuildingException.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/building/ModelBuildingException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "(Lorg/apache/maven/model/Model;Ljava/lang/String;Ljava/util/List;)V", "sig": "(Lorg/apache/maven/model/Model;Ljava/lang/String;Ljava/util/List<Lorg/apache/maven/model/building/ModelProblem;>;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/apache/maven/model/building/ModelBuildingResult;)V"}, {"nme": "getResult", "acc": 1, "dsc": "()Lorg/apache/maven/model/building/ModelBuildingResult;"}, {"nme": "getModel", "acc": 1, "dsc": "()Lorg/apache/maven/model/Model;"}, {"nme": "getModelId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getProblems", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/building/ModelProblem;>;"}, {"nme": "toMessage", "acc": 10, "dsc": "(Lorg/apache/maven/model/building/ModelBuildingResult;)Ljava/lang/String;"}, {"nme": "toMessage", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/List;)Ljava/lang/String;", "sig": "(Ljava/lang/String;Ljava/util/List<Lorg/apache/maven/model/building/ModelProblem;>;)Ljava/lang/String;"}], "flds": [{"acc": 18, "nme": "result", "dsc": "Lorg/apache/maven/model/building/ModelBuildingResult;"}]}, "org/apache/maven/model/building/DefaultModelBuilder$1.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/building/DefaultModelBuilder$1", "super": "org/apache/maven/model/building/FilterModelBuildingRequest", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/apache/maven/model/building/DefaultModelBuilder;Lorg/apache/maven/model/building/ModelBuildingRequest;)V"}, {"nme": "getValidationLevel", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lorg/apache/maven/model/building/DefaultModelBuilder;"}]}, "org/apache/maven/model/building/ModelProblemUtils.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/building/ModelProblemUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "toSourceHint", "acc": 8, "dsc": "(Lorg/apache/maven/model/Model;)Ljava/lang/String;"}, {"nme": "to<PERSON><PERSON>", "acc": 8, "dsc": "(Lorg/apache/maven/model/Model;)Ljava/lang/String;"}, {"nme": "toId", "acc": 8, "dsc": "(Lorg/apache/maven/model/Model;)Ljava/lang/String;"}, {"nme": "toId", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)<PERSON>java/lang/String;"}, {"nme": "formatLocation", "acc": 9, "dsc": "(Lorg/apache/maven/model/building/ModelProblem;Ljava/lang/String;)Ljava/lang/String;"}], "flds": []}, "org/apache/maven/model/interpolation/AbstractStringBasedModelInterpolator$3.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/interpolation/AbstractStringBasedModelInterpolator$3", "super": "org/codehaus/plexus/interpolation/AbstractValueSource", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/apache/maven/model/interpolation/AbstractStringBasedModelInterpolator;ZLorg/apache/maven/model/building/ModelBuildingRequest;)V"}, {"nme": "getValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 4112, "nme": "val$config", "dsc": "Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lorg/apache/maven/model/interpolation/AbstractStringBasedModelInterpolator;"}]}, "org/apache/maven/model/resolution/WorkspaceModelResolver.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/resolution/WorkspaceModelResolver", "super": "java/lang/Object", "mthds": [{"nme": "resolveRawModel", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/String;)Lorg/apache/maven/model/Model;", "exs": ["org/apache/maven/model/resolution/UnresolvableModelException"]}, {"nme": "resolveEffectiveModel", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/String;)Lorg/apache/maven/model/Model;", "exs": ["org/apache/maven/model/resolution/UnresolvableModelException"]}], "flds": []}, "org/apache/maven/model/validation/DefaultModelValidator.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/validation/DefaultModelValidator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/apache/maven/model/interpolation/ModelVersionProcessor;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "validateRawModel", "acc": 1, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}, {"nme": "validate30RawProfileActivation", "acc": 2, "dsc": "(Lorg/apache/maven/model/building/ModelProblemCollector;Lorg/apache/maven/model/Activation;Ljava/lang/String;)V"}, {"nme": "validate20RawPlugins", "acc": 2, "dsc": "(Lorg/apache/maven/model/building/ModelProblemCollector;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/building/ModelBuildingRequest;)V", "sig": "(Lorg/apache/maven/model/building/ModelProblemCollector;Ljava/util/List<Lorg/apache/maven/model/Plugin;>;Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/building/ModelBuildingRequest;)V"}, {"nme": "validateEffectiveModel", "acc": 1, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}, {"nme": "validate20RawDependencies", "acc": 2, "dsc": "(Lorg/apache/maven/model/building/ModelProblemCollector;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/building/ModelBuildingRequest;)V", "sig": "(Lorg/apache/maven/model/building/ModelProblemCollector;Ljava/util/List<Lorg/apache/maven/model/Dependency;>;Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/building/ModelBuildingRequest;)V"}, {"nme": "validate20RawDependenciesSelfReferencing", "acc": 2, "dsc": "(Lorg/apache/maven/model/building/ModelProblemCollector;Lorg/apache/maven/model/Model;Ljava/util/List;Ljava/lang/String;Lorg/apache/maven/model/building/ModelBuildingRequest;)V", "sig": "(Lorg/apache/maven/model/building/ModelProblemCollector;Lorg/apache/maven/model/Model;Ljava/util/List<Lorg/apache/maven/model/Dependency;>;Ljava/lang/String;Lorg/apache/maven/model/building/ModelBuildingRequest;)V"}, {"nme": "validateEffectiveDependencies", "acc": 2, "dsc": "(Lorg/apache/maven/model/building/ModelProblemCollector;Lorg/apache/maven/model/Model;Ljava/util/List;ZLorg/apache/maven/model/building/ModelBuildingRequest;)V", "sig": "(Lorg/apache/maven/model/building/ModelProblemCollector;Lorg/apache/maven/model/Model;Ljava/util/List<Lorg/apache/maven/model/Dependency;>;ZLorg/apache/maven/model/building/ModelBuildingRequest;)V"}, {"nme": "validateEffectiveModelAgainstDependency", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/apache/maven/model/building/ModelProblemCollector;Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Dependency;Lorg/apache/maven/model/building/ModelBuildingRequest;)V"}, {"nme": "validate20EffectivePluginDependencies", "acc": 2, "dsc": "(Lorg/apache/maven/model/building/ModelProblemCollector;Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/building/ModelBuildingRequest;)V"}, {"nme": "validateEffectiveDependency", "acc": 2, "dsc": "(Lorg/apache/maven/model/building/ModelProblemCollector;Lorg/apache/maven/model/Dependency;ZLjava/lang/String;Lorg/apache/maven/model/building/ModelBuildingRequest;)V"}, {"nme": "validateDependencyVersion", "acc": 4, "dsc": "(Lorg/apache/maven/model/building/ModelProblemCollector;Lorg/apache/maven/model/Dependency;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "validateRawRepositories", "acc": 2, "dsc": "(Lorg/apache/maven/model/building/ModelProblemCollector;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/building/ModelBuildingRequest;)V", "sig": "(Lorg/apache/maven/model/building/ModelProblemCollector;Ljava/util/List<Lorg/apache/maven/model/Repository;>;Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/building/ModelBuildingRequest;)V"}, {"nme": "validate20EffectiveRepository", "acc": 2, "dsc": "(Lorg/apache/maven/model/building/ModelProblemCollector;Lorg/apache/maven/model/Repository;Ljava/lang/String;Lorg/apache/maven/model/building/ModelBuildingRequest;)V"}, {"nme": "validate20RawResources", "acc": 2, "dsc": "(Lorg/apache/maven/model/building/ModelProblemCollector;Ljava/util/List;Ljava/lang/String;Lorg/apache/maven/model/building/ModelBuildingRequest;)V", "sig": "(Lorg/apache/maven/model/building/ModelProblemCollector;Ljava/util/List<Lorg/apache/maven/model/Resource;>;Ljava/lang/String;Lorg/apache/maven/model/building/ModelBuildingRequest;)V"}, {"nme": "validateId", "acc": 2, "dsc": "(Ljava/lang/String;Lorg/apache/maven/model/building/ModelProblemCollector;Ljava/lang/String;Lorg/apache/maven/model/InputLocationTracker;)Z"}, {"nme": "validateId", "acc": 2, "dsc": "(Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/building/ModelProblemCollector;Lorg/apache/maven/model/building/ModelProblem$Severity;Lorg/apache/maven/model/building/ModelProblem$Version;Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/InputLocationTracker;)Z"}, {"nme": "isValidId", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isValidIdCharacter", "acc": 2, "dsc": "(C)Z"}, {"nme": "validateIdWithWildcards", "acc": 2, "dsc": "(Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/building/ModelProblemCollector;Lorg/apache/maven/model/building/ModelProblem$Severity;Lorg/apache/maven/model/building/ModelProblem$Version;Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/InputLocationTracker;)Z"}, {"nme": "isValidIdWithWildCards", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isValidIdWithWildCardCharacter", "acc": 2, "dsc": "(C)Z"}, {"nme": "validateStringNoExpression", "acc": 2, "dsc": "(Ljava/lang/String;Lorg/apache/maven/model/building/ModelProblemCollector;Lorg/apache/maven/model/building/ModelProblem$Severity;Lorg/apache/maven/model/building/ModelProblem$Version;Ljava/lang/String;Lorg/apache/maven/model/InputLocationTracker;)Z"}, {"nme": "validateVersionNoExpression", "acc": 2, "dsc": "(Ljava/lang/String;Lorg/apache/maven/model/building/ModelProblemCollector;Lorg/apache/maven/model/building/ModelProblem$Severity;Lorg/apache/maven/model/building/ModelProblem$Version;Ljava/lang/String;Lorg/apache/maven/model/InputLocationTracker;)Z"}, {"nme": "hasExpression", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "hasProjectExpression", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "validateStringNotEmpty", "acc": 2, "dsc": "(Ljava/lang/String;Lorg/apache/maven/model/building/ModelProblemCollector;Lorg/apache/maven/model/building/ModelProblem$Severity;Lorg/apache/maven/model/building/ModelProblem$Version;Ljava/lang/String;Lorg/apache/maven/model/InputLocationTracker;)Z"}, {"nme": "validateStringNotEmpty", "acc": 2, "dsc": "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/building/ModelProblemCollector;Lorg/apache/maven/model/building/ModelProblem$Severity;Lorg/apache/maven/model/building/ModelProblem$Version;Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/InputLocationTracker;)Z"}, {"nme": "validateStringNotEmpty", "acc": 2, "dsc": "(Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/building/ModelProblemCollector;Lorg/apache/maven/model/building/ModelProblem$Severity;Lorg/apache/maven/model/building/ModelProblem$Version;Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/InputLocationTracker;)Z"}, {"nme": "validateNotNull", "acc": 2, "dsc": "(Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/building/ModelProblemCollector;Lorg/apache/maven/model/building/ModelProblem$Severity;Lorg/apache/maven/model/building/ModelProblem$Version;Ljava/lang/Object;Ljava/lang/String;Lorg/apache/maven/model/InputLocationTracker;)Z"}, {"nme": "validateNotNull", "acc": 2, "dsc": "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/building/ModelProblemCollector;Lorg/apache/maven/model/building/ModelProblem$Severity;Lorg/apache/maven/model/building/ModelProblem$Version;Ljava/lang/Object;Ljava/lang/String;Lorg/apache/maven/model/InputLocationTracker;)Z"}, {"nme": "validateBoolean", "acc": 2, "dsc": "(Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/building/ModelProblemCollector;Lorg/apache/maven/model/building/ModelProblem$Severity;Lorg/apache/maven/model/building/ModelProblem$Version;Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/InputLocationTracker;)Z"}, {"nme": "validateEnum", "acc": 130, "dsc": "(Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/building/ModelProblemCollector;Lorg/apache/maven/model/building/ModelProblem$Severity;Lorg/apache/maven/model/building/ModelProblem$Version;Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/InputLocationTracker;[Ljava/lang/String;)Z"}, {"nme": "validateModelVersion", "acc": 130, "dsc": "(Lorg/apache/maven/model/building/ModelProblemCollector;Ljava/lang/String;Lorg/apache/maven/model/InputLocationTracker;[Ljava/lang/String;)Z"}, {"nme": "compareModelVersions", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "validateBannedCharacters", "acc": 2, "dsc": "(Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/building/ModelProblemCollector;Lorg/apache/maven/model/building/ModelProblem$Severity;Lorg/apache/maven/model/building/ModelProblem$Version;Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/InputLocationTracker;Ljava/lang/String;)Z"}, {"nme": "validateVersion", "acc": 2, "dsc": "(Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/building/ModelProblemCollector;Lorg/apache/maven/model/building/ModelProblem$Severity;Lorg/apache/maven/model/building/ModelProblem$Version;Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/InputLocationTracker;)Z"}, {"nme": "validate20ProperSnapshotVersion", "acc": 2, "dsc": "(Ljava/lang/String;Lorg/apache/maven/model/building/ModelProblemCollector;Lorg/apache/maven/model/building/ModelProblem$Severity;Lorg/apache/maven/model/building/ModelProblem$Version;Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/InputLocationTracker;)Z"}, {"nme": "validate20PluginVersion", "acc": 2, "dsc": "(Ljava/lang/String;Lorg/apache/maven/model/building/ModelProblemCollector;Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/InputLocationTracker;Lorg/apache/maven/model/building/ModelBuildingRequest;)Z"}, {"nme": "addViolation", "acc": 10, "dsc": "(Lorg/apache/maven/model/building/ModelProblemCollector;Lorg/apache/maven/model/building/ModelProblem$Severity;Lorg/apache/maven/model/building/ModelProblem$Version;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/InputLocationTracker;)V"}, {"nme": "getLocation", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/apache/maven/model/InputLocationTracker;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "equals", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "getSeverity", "acc": 10, "dsc": "(Lorg/apache/maven/model/building/ModelBuildingRequest;I)Lorg/apache/maven/model/building/ModelProblem$Severity;"}, {"nme": "getSeverity", "acc": 10, "dsc": "(II)Lorg/apache/maven/model/building/ModelProblem$Severity;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CI_FRIENDLY_EXPRESSION", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 26, "nme": "EXPRESSION_PROJECT_NAME_PATTERN", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 26, "nme": "ILLEGAL_FS_CHARS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "\\/:\"<>|?*"}, {"acc": 26, "nme": "ILLEGAL_VERSION_CHARS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "\\/:\"<>|?*"}, {"acc": 26, "nme": "ILLEGAL_REPO_ID_CHARS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "\\/:\"<>|?*"}, {"acc": 26, "nme": "EMPTY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": ""}, {"acc": 18, "nme": "validIds", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 2, "nme": "versionProcessor", "dsc": "Lorg/apache/maven/model/interpolation/ModelVersionProcessor;"}], "vanns": [{"dsc": "Ljavax/inject/Named;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/model/path/ProfileActivationFilePathInterpolator.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/path/ProfileActivationFilePathInterpolator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "setPathTranslator", "acc": 1, "dsc": "(Lorg/apache/maven/model/path/PathTranslator;)Lorg/apache/maven/model/path/ProfileActivationFilePathInterpolator;"}, {"nme": "interpolate", "acc": 1, "dsc": "(Ljava/lang/String;Lorg/apache/maven/model/profile/ProfileActivationContext;)Ljava/lang/String;", "exs": ["org/codehaus/plexus/interpolation/InterpolationException"]}], "flds": [{"acc": 2, "nme": "pathTranslator", "dsc": "Lorg/apache/maven/model/path/PathTranslator;"}], "vanns": [{"dsc": "Ljavax/inject/Named;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/model/interpolation/StringVisitorModelInterpolator$ModelVisitor.class": {"ver": 52, "acc": 48, "nme": "org/apache/maven/model/interpolation/StringVisitorModelInterpolator$ModelVisitor", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/apache/maven/model/interpolation/StringVisitorModelInterpolator$InnerInterpolator;)V"}, {"nme": "visit", "acc": 0, "dsc": "(Lorg/apache/maven/model/Model;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/Parent;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/Organization;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/License;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/Developer;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/Contributor;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/MailingList;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/Prerequisites;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/Scm;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/IssueManagement;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/CiManagement;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/Notifier;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/BuildBase;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/PluginManagement;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/Build;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/Resource;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/Plugin;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/PluginExecution;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/Xpp3Dom;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/Extension;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/Profile;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/Activation;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/ActivationOS;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/ActivationProperty;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/ActivationFile;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/ModelBase;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/DistributionManagement;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/Site;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/Relocation;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/DependencyManagement;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/Repository;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/RepositoryBase;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/RepositoryPolicy;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/Dependency;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/Exclusion;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/Reporting;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/ReportPlugin;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Lorg/apache/maven/model/ReportSet;)V"}, {"nme": "visit", "acc": 2, "dsc": "(Ljava/util/Properties;)V"}, {"nme": "visit", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/String;>;)V"}, {"nme": "interpolate", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": [{"acc": 18, "nme": "interpolator", "dsc": "Lorg/apache/maven/model/interpolation/StringVisitorModelInterpolator$InnerInterpolator;"}]}, "org/apache/maven/model/inheritance/InheritanceAssembler.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/inheritance/InheritanceAssembler", "super": "java/lang/Object", "mthds": [{"nme": "assembleModelInheritance", "acc": 1025, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}], "flds": []}, "org/apache/maven/model/building/ModelSource.class": {"ver": 52, "acc": 132609, "nme": "org/apache/maven/model/building/ModelSource", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "org/apache/maven/model/profile/DefaultProfileInjector.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/profile/DefaultProfileInjector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "injectProfile", "acc": 1, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Profile;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}], "flds": [{"acc": 2, "nme": "merger", "dsc": "Lorg/apache/maven/model/profile/DefaultProfileInjector$ProfileModelMerger;"}], "vanns": [{"dsc": "Ljavax/inject/Named;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/model/building/ModelBuildingRequest.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/building/ModelBuildingRequest", "super": "java/lang/Object", "mthds": [{"nme": "getRawModel", "acc": 1025, "dsc": "()Lorg/apache/maven/model/Model;"}, {"nme": "setRawModel", "acc": 1025, "dsc": "(Lorg/apache/maven/model/Model;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "getModelSource", "acc": 1025, "dsc": "()Lorg/apache/maven/model/building/ModelSource;"}, {"nme": "setModelSource", "acc": 1025, "dsc": "(Lorg/apache/maven/model/building/ModelSource;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "getPomFile", "acc": 1025, "dsc": "()Ljava/io/File;"}, {"nme": "setPomFile", "acc": 1025, "dsc": "(Ljava/io/File;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "getValidationLevel", "acc": 1025, "dsc": "()I"}, {"nme": "setValidationLevel", "acc": 1025, "dsc": "(I)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "isProcessPlugins", "acc": 1025, "dsc": "()Z"}, {"nme": "setProcessPlugins", "acc": 1025, "dsc": "(Z)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "isTwoPhaseBuilding", "acc": 1025, "dsc": "()Z"}, {"nme": "setTwoPhaseBuilding", "acc": 1025, "dsc": "(Z)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "isLocationTracking", "acc": 1025, "dsc": "()Z"}, {"nme": "setLocationTracking", "acc": 1025, "dsc": "(Z)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "getProfiles", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/Profile;>;"}, {"nme": "setProfiles", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/apache/maven/model/building/ModelBuildingRequest;", "sig": "(Ljava/util/List<Lorg/apache/maven/model/Profile;>;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "getActiveProfileIds", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "setActiveProfileIds", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/apache/maven/model/building/ModelBuildingRequest;", "sig": "(Ljava/util/List<Ljava/lang/String;>;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "getInactiveProfileIds", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "setInactiveProfileIds", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/apache/maven/model/building/ModelBuildingRequest;", "sig": "(Ljava/util/List<Ljava/lang/String;>;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "getSystemProperties", "acc": 1025, "dsc": "()Ljava/util/Properties;"}, {"nme": "setSystemProperties", "acc": 1025, "dsc": "(Ljava/util/Properties;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "getUserProperties", "acc": 1025, "dsc": "()Ljava/util/Properties;"}, {"nme": "setUserProperties", "acc": 1025, "dsc": "(Ljava/util/Properties;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "getBuildStartTime", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/Date;"}, {"nme": "setBuildStartTime", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/Date;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "getModelResolver", "acc": 1025, "dsc": "()Lorg/apache/maven/model/resolution/ModelResolver;"}, {"nme": "setModelResolver", "acc": 1025, "dsc": "(Lorg/apache/maven/model/resolution/ModelResolver;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "getModelBuildingListener", "acc": 1025, "dsc": "()Lorg/apache/maven/model/building/ModelBuildingListener;"}, {"nme": "setModelBuildingListener", "acc": 1025, "dsc": "(Lorg/apache/maven/model/building/ModelBuildingListener;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "getModelCache", "acc": 1025, "dsc": "()Lorg/apache/maven/model/building/ModelCache;"}, {"nme": "setModelCache", "acc": 1025, "dsc": "(Lorg/apache/maven/model/building/ModelCache;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "getWorkspaceModelResolver", "acc": 1025, "dsc": "()Lorg/apache/maven/model/resolution/WorkspaceModelResolver;"}, {"nme": "setWorkspaceModelResolver", "acc": 1025, "dsc": "(Lorg/apache/maven/model/resolution/WorkspaceModelResolver;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}], "flds": [{"acc": 25, "nme": "VALIDATION_LEVEL_MINIMAL", "dsc": "I", "val": 0}, {"acc": 25, "nme": "VALIDATION_LEVEL_MAVEN_2_0", "dsc": "I", "val": 20}, {"acc": 25, "nme": "VALIDATION_LEVEL_MAVEN_3_0", "dsc": "I", "val": 30}, {"acc": 25, "nme": "VALIDATION_LEVEL_MAVEN_3_1", "dsc": "I", "val": 31}, {"acc": 25, "nme": "VALIDATION_LEVEL_STRICT", "dsc": "I", "val": 30}]}, "org/apache/maven/model/plugin/DefaultReportConfigurationExpander.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/plugin/DefaultReportConfigurationExpander", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "expandPluginConfiguration", "acc": 1, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}], "flds": [], "vanns": [{"dsc": "Ljavax/inject/Named;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/model/locator/DefaultModelLocator.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/locator/DefaultModelLocator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "locatePom", "acc": 1, "dsc": "(Lja<PERSON>/io/File;)Ljava/io/File;"}], "flds": [], "vanns": [{"dsc": "Ljavax/inject/Named;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/model/building/ModelProblem$Severity.class": {"ver": 52, "acc": 16433, "nme": "org/apache/maven/model/building/ModelProblem$Severity", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lorg/apache/maven/model/building/ModelProblem$Severity;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/apache/maven/model/building/ModelProblem$Severity;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Lorg/apache/maven/model/building/ModelProblem$Severity;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "FATAL", "dsc": "Lorg/apache/maven/model/building/ModelProblem$Severity;"}, {"acc": 16409, "nme": "ERROR", "dsc": "Lorg/apache/maven/model/building/ModelProblem$Severity;"}, {"acc": 16409, "nme": "WARNING", "dsc": "Lorg/apache/maven/model/building/ModelProblem$Severity;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lorg/apache/maven/model/building/ModelProblem$Severity;"}]}, "org/apache/maven/model/building/ModelProblem.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/building/ModelProblem", "super": "java/lang/Object", "mthds": [{"nme": "getSource", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLineNumber", "acc": 1025, "dsc": "()I"}, {"nme": "getColumnNumber", "acc": 1025, "dsc": "()I"}, {"nme": "getModelId", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getException", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/Exception;"}, {"nme": "getMessage", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getSeverity", "acc": 1025, "dsc": "()Lorg/apache/maven/model/building/ModelProblem$Severity;"}, {"nme": "getVersion", "acc": 1025, "dsc": "()Lorg/apache/maven/model/building/ModelProblem$Version;"}], "flds": []}, "org/apache/maven/model/path/PathTranslator.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/path/PathTranslator", "super": "java/lang/Object", "mthds": [{"nme": "alignToBaseDirectory", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/io/File;)Ljava/lang/String;"}], "flds": []}, "org/apache/maven/model/interpolation/ProblemDetectingValueSource.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/interpolation/ProblemDetectingValueSource", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/codehaus/plexus/interpolation/ValueSource;Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}, {"nme": "getValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getFeedback", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;"}, {"nme": "clearFeedback", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "valueSource", "dsc": "Lorg/codehaus/plexus/interpolation/ValueSource;"}, {"acc": 18, "nme": "bannedPrefix", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "newPrefix", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "problems", "dsc": "Lorg/apache/maven/model/building/ModelProblemCollector;"}]}, "org/apache/maven/model/building/FileModelSource.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/building/FileModelSource", "super": "org/apache/maven/building/FileSource", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V"}, {"nme": "getPomFile", "acc": 131073, "dsc": "()Ljava/io/File;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getRelatedSource", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/apache/maven/model/building/ModelSource2;"}, {"nme": "getLocationURI", "acc": 1, "dsc": "()Ljava/net/URI;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": []}, "org/apache/maven/model/profile/ProfileInjector.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/profile/ProfileInjector", "super": "java/lang/Object", "mthds": [{"nme": "injectProfile", "acc": 1025, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Profile;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}], "flds": []}, "org/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction$ObjectField.class": {"ver": 52, "acc": 48, "nme": "org/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction$ObjectField", "super": "org/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction$CacheField", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)V"}, {"nme": "doInterpolate", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction;)V", "exs": ["java/lang/IllegalAccessException"]}], "flds": [{"acc": 18, "nme": "isArray", "dsc": "Z"}]}, "org/apache/maven/model/path/DefaultPathTranslator.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/path/DefaultPathTranslator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "alignToBaseDirectory", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/io/File;)Ljava/lang/String;"}], "flds": [], "vanns": [{"dsc": "Ljavax/inject/Named;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/model/plugin/ReportConfigurationExpander.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/plugin/ReportConfigurationExpander", "super": "java/lang/Object", "mthds": [{"nme": "expandPluginConfiguration", "acc": 1025, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}], "flds": []}, "org/apache/maven/model/composition/DependencyManagementImporter.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/composition/DependencyManagementImporter", "super": "java/lang/Object", "mthds": [{"nme": "importManagement", "acc": 1025, "dsc": "(Lorg/apache/maven/model/Model;Ljava/util/List;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V", "sig": "(Lorg/apache/maven/model/Model;Ljava/util/List<+Lorg/apache/maven/model/DependencyManagement;>;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}], "flds": []}, "org/apache/maven/model/interpolation/AbstractStringBasedModelInterpolator.class": {"ver": 52, "acc": 1057, "nme": "org/apache/maven/model/interpolation/AbstractStringBasedModelInterpolator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "setPathTranslator", "acc": 1, "dsc": "(Lorg/apache/maven/model/path/PathTranslator;)Lorg/apache/maven/model/interpolation/AbstractStringBasedModelInterpolator;"}, {"nme": "setUrlNormalizer", "acc": 1, "dsc": "(Lorg/apache/maven/model/path/UrlNormalizer;)Lorg/apache/maven/model/interpolation/AbstractStringBasedModelInterpolator;"}, {"nme": "setVersionPropertiesProcessor", "acc": 1, "dsc": "(Lorg/apache/maven/model/interpolation/ModelVersionProcessor;)Lorg/apache/maven/model/interpolation/AbstractStringBasedModelInterpolator;"}, {"nme": "createValueSources", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Ljava/io/File;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)Ljava/util/List;", "sig": "(Lorg/apache/maven/model/Model;Ljava/io/File;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)Ljava/util/List<Lorg/codehaus/plexus/interpolation/ValueSource;>;"}, {"nme": "createPostProcessors", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Ljava/io/File;Lorg/apache/maven/model/building/ModelBuildingRequest;)Ljava/util/List;", "sig": "(Lorg/apache/maven/model/Model;Ljava/io/File;Lorg/apache/maven/model/building/ModelBuildingRequest;)Ljava/util/List<+Lorg/codehaus/plexus/interpolation/InterpolationPostProcessor;>;"}, {"nme": "createRecursionInterceptor", "acc": 4, "dsc": "()Lorg/codehaus/plexus/interpolation/RecursionInterceptor;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "PROJECT_PREFIXES", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 26, "nme": "TRANSLATED_PATH_EXPRESSIONS", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Ljava/lang/String;>;"}, {"acc": 2, "nme": "pathTranslator", "dsc": "Lorg/apache/maven/model/path/PathTranslator;"}, {"acc": 2, "nme": "urlNormalizer", "dsc": "Lorg/apache/maven/model/path/UrlNormalizer;"}, {"acc": 2, "nme": "versionProcessor", "dsc": "Lorg/apache/maven/model/interpolation/ModelVersionProcessor;"}]}, "org/apache/maven/model/profile/activation/OperatingSystemProfileActivator.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/profile/activation/OperatingSystemProfileActivator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "isActive", "acc": 1, "dsc": "(Lorg/apache/maven/model/Profile;Lorg/apache/maven/model/profile/ProfileActivationContext;Lorg/apache/maven/model/building/ModelProblemCollector;)Z"}, {"nme": "presentInConfig", "acc": 1, "dsc": "(Lorg/apache/maven/model/Profile;Lorg/apache/maven/model/profile/ProfileActivationContext;Lorg/apache/maven/model/building/ModelProblemCollector;)Z"}, {"nme": "ensureAtLeastOneNonNull", "acc": 2, "dsc": "(Lorg/apache/maven/model/ActivationOS;)Z"}, {"nme": "determineVersionMatch", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "determineArchMatch", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "determineNameMatch", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "determineFamilyMatch", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}], "flds": [], "vanns": [{"dsc": "Ljavax/inject/Named;", "vals": ["value", "os"]}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/model/interpolation/BuildTimestampValueSource.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/interpolation/BuildTimestampValueSource", "super": "org/codehaus/plexus/interpolation/AbstractValueSource", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(L<PERSON><PERSON>/util/Date;Ljava/util/Properties;)V"}, {"nme": "getValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 18, "nme": "mavenBuildTimestamp", "dsc": "Lorg/apache/maven/model/interpolation/MavenBuildTimestamp;"}]}, "org/apache/maven/model/building/DefaultModelBuildingResult.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/building/DefaultModelBuildingResult", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "getEffectiveModel", "acc": 1, "dsc": "()Lorg/apache/maven/model/Model;"}, {"nme": "setEffectiveModel", "acc": 1, "dsc": "(Lorg/apache/maven/model/Model;)Lorg/apache/maven/model/building/DefaultModelBuildingResult;"}, {"nme": "getModelIds", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "addModelId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/apache/maven/model/building/DefaultModelBuildingResult;"}, {"nme": "getRawModel", "acc": 1, "dsc": "()Lorg/apache/maven/model/Model;"}, {"nme": "getRawModel", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/apache/maven/model/Model;"}, {"nme": "setRawModel", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/apache/maven/model/Model;)Lorg/apache/maven/model/building/DefaultModelBuildingResult;"}, {"nme": "getActivePomProfiles", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/List<Lorg/apache/maven/model/Profile;>;"}, {"nme": "setActivePomProfiles", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/util/List;)Lorg/apache/maven/model/building/DefaultModelBuildingResult;", "sig": "(Ljava/lang/String;Ljava/util/List<Lorg/apache/maven/model/Profile;>;)Lorg/apache/maven/model/building/DefaultModelBuildingResult;"}, {"nme": "getActiveExternalProfiles", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/Profile;>;"}, {"nme": "setActiveExternalProfiles", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/apache/maven/model/building/DefaultModelBuildingResult;", "sig": "(Ljava/util/List<Lorg/apache/maven/model/Profile;>;)Lorg/apache/maven/model/building/DefaultModelBuildingResult;"}, {"nme": "getProblems", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/building/ModelProblem;>;"}, {"nme": "setProblems", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/apache/maven/model/building/DefaultModelBuildingResult;", "sig": "(Ljava/util/List<Lorg/apache/maven/model/building/ModelProblem;>;)Lorg/apache/maven/model/building/DefaultModelBuildingResult;"}], "flds": [{"acc": 2, "nme": "effectiveModel", "dsc": "Lorg/apache/maven/model/Model;"}, {"acc": 2, "nme": "modelIds", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 2, "nme": "rawModels", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lorg/apache/maven/model/Model;>;"}, {"acc": 2, "nme": "activePomProfiles", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Lorg/apache/maven/model/Profile;>;>;"}, {"acc": 2, "nme": "activeExternalProfiles", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/model/Profile;>;"}, {"acc": 2, "nme": "problems", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/model/building/ModelProblem;>;"}]}, "org/apache/maven/model/building/DefaultModelProcessor.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/building/DefaultModelProcessor", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "setModelLocator", "acc": 1, "dsc": "(Lorg/apache/maven/model/locator/ModelLocator;)Lorg/apache/maven/model/building/DefaultModelProcessor;"}, {"nme": "setModelReader", "acc": 1, "dsc": "(Lorg/apache/maven/model/io/ModelReader;)Lorg/apache/maven/model/building/DefaultModelProcessor;"}, {"nme": "locatePom", "acc": 1, "dsc": "(Lja<PERSON>/io/File;)Ljava/io/File;"}, {"nme": "read", "acc": 1, "dsc": "(Ljava/io/File;Ljava/util/Map;)Lorg/apache/maven/model/Model;", "sig": "(Ljava/io/File;Ljava/util/Map<Ljava/lang/String;*>;)Lorg/apache/maven/model/Model;", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;Lja<PERSON>/util/Map;)Lorg/apache/maven/model/Model;", "sig": "(Ljava/io/Reader;Ljava/util/Map<Ljava/lang/String;*>;)Lorg/apache/maven/model/Model;", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 1, "dsc": "(Ljava/io/InputStream;Ljava/util/Map;)Lorg/apache/maven/model/Model;", "sig": "(Ljava/io/InputStream;Ljava/util/Map<Ljava/lang/String;*>;)Lorg/apache/maven/model/Model;", "exs": ["java/io/IOException"]}], "flds": [{"acc": 2, "nme": "locator", "dsc": "Lorg/apache/maven/model/locator/ModelLocator;"}, {"acc": 2, "nme": "reader", "dsc": "Lorg/apache/maven/model/io/ModelReader;"}], "vanns": [{"dsc": "Ljavax/inject/Named;", "vals": ["value", "core-default"]}, {"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Lorg/eclipse/sisu/Typed;", "vals": ["value", [{"itrlNme": "org/apache/maven/model/building/ModelProcessor"}]]}]}, "org/apache/maven/model/normalization/DefaultModelNormalizer.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/normalization/DefaultModelNormalizer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "mergeDuplicates", "acc": 1, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}, {"nme": "injectDefaultValues", "acc": 1, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}, {"nme": "injectDependencyDefaults", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(<PERSON><PERSON><PERSON>/util/List<Lorg/apache/maven/model/Dependency;>;)V"}], "flds": [{"acc": 2, "nme": "merger", "dsc": "Lorg/apache/maven/model/normalization/DefaultModelNormalizer$DuplicateMerger;"}], "vanns": [{"dsc": "Ljavax/inject/Named;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/model/building/ModelBuildingEventCatapult$1.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/building/ModelBuildingEventCatapult$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "fire", "acc": 1, "dsc": "(Lorg/apache/maven/model/building/ModelBuildingListener;Lorg/apache/maven/model/building/ModelBuildingEvent;)V"}], "flds": []}, "org/apache/maven/model/interpolation/StringVisitorModelInterpolator$InnerInterpolator.class": {"ver": 52, "acc": 1536, "nme": "org/apache/maven/model/interpolation/StringVisitorModelInterpolator$InnerInterpolator", "super": "java/lang/Object", "mthds": [{"nme": "interpolate", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": []}, "org/apache/maven/model/plugin/DefaultReportingConverter.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/plugin/DefaultReportingConverter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "convertReporting", "acc": 1, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}, {"nme": "findSitePlugin", "acc": 2, "dsc": "(Lorg/apache/maven/model/Build;)Lorg/apache/maven/model/Plugin;"}, {"nme": "isSitePlugin", "acc": 2, "dsc": "(Lorg/apache/maven/model/Plugin;)Z"}, {"nme": "convert", "acc": 2, "dsc": "(Lorg/apache/maven/model/ReportPlugin;)Lorg/codehaus/plexus/util/xml/Xpp3Dom;"}, {"nme": "convert", "acc": 2, "dsc": "(Lorg/apache/maven/model/ReportSet;)Lorg/codehaus/plexus/util/xml/Xpp3Dom;"}, {"nme": "addDom", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/Xpp3Dom;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "addDom", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/Xpp3Dom;Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "newDom", "acc": 2, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;Lorg/apache/maven/model/InputLocation;)Lorg/codehaus/plexus/util/xml/Xpp3Dom;"}], "flds": [{"acc": 18, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}], "vanns": [{"dsc": "Ljavax/inject/Named;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/model/profile/ProfileSelector.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/profile/ProfileSelector", "super": "java/lang/Object", "mthds": [{"nme": "getActiveProfiles", "acc": 1025, "dsc": "(Ljava/util/Collection;Lorg/apache/maven/model/profile/ProfileActivationContext;Lorg/apache/maven/model/building/ModelProblemCollector;)Ljava/util/List;", "sig": "(Ljava/util/Collection<Lorg/apache/maven/model/Profile;>;Lorg/apache/maven/model/profile/ProfileActivationContext;Lorg/apache/maven/model/building/ModelProblemCollector;)Ljava/util/List<Lorg/apache/maven/model/Profile;>;"}], "flds": []}, "org/apache/maven/model/building/DefaultModelBuilderFactory$1.class": {"ver": 52, "acc": 4128, "nme": "org/apache/maven/model/building/DefaultModelBuilderFactory$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/apache/maven/model/superpom/DefaultSuperPomProvider.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/superpom/DefaultSuperPomProvider", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "setModelProcessor", "acc": 1, "dsc": "(Lorg/apache/maven/model/building/ModelProcessor;)Lorg/apache/maven/model/superpom/DefaultSuperPomProvider;"}, {"nme": "getSuperModel", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/apache/maven/model/Model;"}], "flds": [{"acc": 2, "nme": "superModel", "dsc": "Lorg/apache/maven/model/Model;"}, {"acc": 2, "nme": "modelProcessor", "dsc": "Lorg/apache/maven/model/building/ModelProcessor;"}], "vanns": [{"dsc": "Ljavax/inject/Named;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/model/locator/ModelLocator.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/locator/ModelLocator", "super": "java/lang/Object", "mthds": [{"nme": "locatePom", "acc": 1025, "dsc": "(Lja<PERSON>/io/File;)Ljava/io/File;"}], "flds": []}, "org/apache/maven/model/building/ModelProcessor.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/building/ModelProcessor", "super": "java/lang/Object", "mthds": [], "flds": [{"acc": 25, "nme": "SOURCE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "org.apache.maven.model.building.source"}]}, "org/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction$ListField.class": {"ver": 52, "acc": 48, "nme": "org/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction$ListField", "super": "org/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction$CacheField", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)V"}, {"nme": "doInterpolate", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction;)V", "exs": ["java/lang/IllegalAccessException"]}], "flds": []}, "org/apache/maven/model/building/ModelSource2.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/building/ModelSource2", "super": "java/lang/Object", "mthds": [{"nme": "getRelatedSource", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/apache/maven/model/building/ModelSource2;"}, {"nme": "getLocationURI", "acc": 1025, "dsc": "()Ljava/net/URI;"}], "flds": []}, "org/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction$CacheField.class": {"ver": 52, "acc": 1056, "nme": "org/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction$CacheField", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)V"}, {"nme": "interpolate", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction;)V"}, {"nme": "doInterpolate", "acc": 1024, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction;)V", "exs": ["java/lang/IllegalAccessException"]}], "flds": [{"acc": 16, "nme": "field", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Field;"}]}, "org/apache/maven/model/resolution/ModelResolver.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/resolution/ModelResolver", "super": "java/lang/Object", "mthds": [{"nme": "resolveModel", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;)Lorg/apache/maven/model/building/ModelSource;", "exs": ["org/apache/maven/model/resolution/UnresolvableModelException"]}, {"nme": "resolveModel", "acc": 1025, "dsc": "(Lorg/apache/maven/model/Parent;)Lorg/apache/maven/model/building/ModelSource;", "exs": ["org/apache/maven/model/resolution/UnresolvableModelException"]}, {"nme": "resolveModel", "acc": 1025, "dsc": "(Lorg/apache/maven/model/Dependency;)Lorg/apache/maven/model/building/ModelSource;", "exs": ["org/apache/maven/model/resolution/UnresolvableModelException"]}, {"nme": "addRepository", "acc": 1025, "dsc": "(Lorg/apache/maven/model/Repository;)V", "exs": ["org/apache/maven/model/resolution/InvalidRepositoryException"]}, {"nme": "addRepository", "acc": 1025, "dsc": "(Lorg/apache/maven/model/Repository;Z)V", "exs": ["org/apache/maven/model/resolution/InvalidRepositoryException"]}, {"nme": "newCopy", "acc": 1025, "dsc": "()Lorg/apache/maven/model/resolution/ModelResolver;"}], "flds": []}, "org/apache/maven/model/normalization/DefaultModelNormalizer$DuplicateMerger.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/normalization/DefaultModelNormalizer$DuplicateMerger", "super": "org/apache/maven/model/merge/MavenModelMerger", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "mergePlugin", "acc": 1, "dsc": "(Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;)V"}], "flds": []}, "org/apache/maven/model/building/FilterModelBuildingRequest.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/building/FilterModelBuildingRequest", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/apache/maven/model/building/ModelBuildingRequest;)V"}, {"nme": "getPomFile", "acc": 1, "dsc": "()Ljava/io/File;"}, {"nme": "setPomFile", "acc": 1, "dsc": "(Ljava/io/File;)Lorg/apache/maven/model/building/FilterModelBuildingRequest;"}, {"nme": "getModelSource", "acc": 1, "dsc": "()Lorg/apache/maven/model/building/ModelSource;"}, {"nme": "setModelSource", "acc": 1, "dsc": "(Lorg/apache/maven/model/building/ModelSource;)Lorg/apache/maven/model/building/FilterModelBuildingRequest;"}, {"nme": "getValidationLevel", "acc": 1, "dsc": "()I"}, {"nme": "setValidationLevel", "acc": 1, "dsc": "(I)Lorg/apache/maven/model/building/FilterModelBuildingRequest;"}, {"nme": "isProcessPlugins", "acc": 1, "dsc": "()Z"}, {"nme": "setProcessPlugins", "acc": 1, "dsc": "(Z)Lorg/apache/maven/model/building/FilterModelBuildingRequest;"}, {"nme": "isTwoPhaseBuilding", "acc": 1, "dsc": "()Z"}, {"nme": "setTwoPhaseBuilding", "acc": 1, "dsc": "(Z)Lorg/apache/maven/model/building/FilterModelBuildingRequest;"}, {"nme": "isLocationTracking", "acc": 1, "dsc": "()Z"}, {"nme": "setLocationTracking", "acc": 1, "dsc": "(Z)Lorg/apache/maven/model/building/FilterModelBuildingRequest;"}, {"nme": "getProfiles", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/Profile;>;"}, {"nme": "setProfiles", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/apache/maven/model/building/FilterModelBuildingRequest;", "sig": "(Ljava/util/List<Lorg/apache/maven/model/Profile;>;)Lorg/apache/maven/model/building/FilterModelBuildingRequest;"}, {"nme": "getActiveProfileIds", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "setActiveProfileIds", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/apache/maven/model/building/FilterModelBuildingRequest;", "sig": "(Ljava/util/List<Ljava/lang/String;>;)Lorg/apache/maven/model/building/FilterModelBuildingRequest;"}, {"nme": "getInactiveProfileIds", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "setInactiveProfileIds", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/apache/maven/model/building/FilterModelBuildingRequest;", "sig": "(Ljava/util/List<Ljava/lang/String;>;)Lorg/apache/maven/model/building/FilterModelBuildingRequest;"}, {"nme": "getSystemProperties", "acc": 1, "dsc": "()Ljava/util/Properties;"}, {"nme": "setSystemProperties", "acc": 1, "dsc": "(Ljava/util/Properties;)Lorg/apache/maven/model/building/FilterModelBuildingRequest;"}, {"nme": "getUserProperties", "acc": 1, "dsc": "()Ljava/util/Properties;"}, {"nme": "setUserProperties", "acc": 1, "dsc": "(Ljava/util/Properties;)Lorg/apache/maven/model/building/FilterModelBuildingRequest;"}, {"nme": "getBuildStartTime", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Date;"}, {"nme": "setBuildStartTime", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Date;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "getModelResolver", "acc": 1, "dsc": "()Lorg/apache/maven/model/resolution/ModelResolver;"}, {"nme": "setModelResolver", "acc": 1, "dsc": "(Lorg/apache/maven/model/resolution/ModelResolver;)Lorg/apache/maven/model/building/FilterModelBuildingRequest;"}, {"nme": "getModelBuildingListener", "acc": 1, "dsc": "()Lorg/apache/maven/model/building/ModelBuildingListener;"}, {"nme": "setModelBuildingListener", "acc": 1, "dsc": "(Lorg/apache/maven/model/building/ModelBuildingListener;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "getModelCache", "acc": 1, "dsc": "()Lorg/apache/maven/model/building/ModelCache;"}, {"nme": "setModelCache", "acc": 1, "dsc": "(Lorg/apache/maven/model/building/ModelCache;)Lorg/apache/maven/model/building/FilterModelBuildingRequest;"}, {"nme": "getRawModel", "acc": 1, "dsc": "()Lorg/apache/maven/model/Model;"}, {"nme": "setRawModel", "acc": 1, "dsc": "(Lorg/apache/maven/model/Model;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "getWorkspaceModelResolver", "acc": 1, "dsc": "()Lorg/apache/maven/model/resolution/WorkspaceModelResolver;"}, {"nme": "setWorkspaceModelResolver", "acc": 1, "dsc": "(Lorg/apache/maven/model/resolution/WorkspaceModelResolver;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "setModelCache", "acc": 4161, "dsc": "(Lorg/apache/maven/model/building/ModelCache;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "setModelResolver", "acc": 4161, "dsc": "(Lorg/apache/maven/model/resolution/ModelResolver;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "setUserProperties", "acc": 4161, "dsc": "(Ljava/util/Properties;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "setSystemProperties", "acc": 4161, "dsc": "(Ljava/util/Properties;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "setInactiveProfileIds", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "setActiveProfileIds", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "setProfiles", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "setLocationTracking", "acc": 4161, "dsc": "(Z)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "setTwoPhaseBuilding", "acc": 4161, "dsc": "(Z)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "setProcessPlugins", "acc": 4161, "dsc": "(Z)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "setValidationLevel", "acc": 4161, "dsc": "(I)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "setPomFile", "acc": 4161, "dsc": "(Ljava/io/File;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "setModelSource", "acc": 4161, "dsc": "(Lorg/apache/maven/model/building/ModelSource;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}], "flds": [{"acc": 4, "nme": "request", "dsc": "Lorg/apache/maven/model/building/ModelBuildingRequest;"}]}, "org/apache/maven/model/building/ModelBuilder.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/building/ModelBuilder", "super": "java/lang/Object", "mthds": [{"nme": "build", "acc": 1025, "dsc": "(Lorg/apache/maven/model/building/ModelBuildingRequest;)Lorg/apache/maven/model/building/ModelBuildingResult;", "exs": ["org/apache/maven/model/building/ModelBuildingException"]}, {"nme": "build", "acc": 1025, "dsc": "(Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelBuildingResult;)Lorg/apache/maven/model/building/ModelBuildingResult;", "exs": ["org/apache/maven/model/building/ModelBuildingException"]}, {"nme": "buildRawModel", "acc": 1025, "dsc": "(Ljava/io/File;IZ)Lorg/apache/maven/model/building/Result;", "sig": "(Ljava/io/File;IZ)Lorg/apache/maven/model/building/Result<+Lorg/apache/maven/model/Model;>;"}], "flds": []}, "org/apache/maven/model/interpolation/AbstractStringBasedModelInterpolator$1.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/interpolation/AbstractStringBasedModelInterpolator$1", "super": "org/codehaus/plexus/interpolation/AbstractValueSource", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/apache/maven/model/interpolation/AbstractStringBasedModelInterpolator;ZLjava/io/File;)V"}, {"nme": "getValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 4112, "nme": "val$projectDir", "dsc": "Ljava/io/File;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lorg/apache/maven/model/interpolation/AbstractStringBasedModelInterpolator;"}]}, "org/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction$StringField.class": {"ver": 52, "acc": 48, "nme": "org/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction$StringField", "super": "org/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction$CacheField", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)V"}, {"nme": "doInterpolate", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction;)V", "exs": ["java/lang/IllegalAccessException"]}], "flds": []}, "org/apache/maven/model/interpolation/StringSearchModelInterpolator.class": {"ver": 52, "acc": 131105, "nme": "org/apache/maven/model/interpolation/StringSearchModelInterpolator", "super": "org/apache/maven/model/interpolation/AbstractStringBasedModelInterpolator", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "interpolateModel", "acc": 1, "dsc": "(Lorg/apache/maven/model/Model;Ljava/io/File;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)Lorg/apache/maven/model/Model;"}, {"nme": "interpolateObject", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/Model;Ljava/io/File;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}, {"nme": "createInterpolator", "acc": 2, "dsc": "(Ljava/util/List;Ljava/util/List;Lorg/apache/maven/model/building/ModelProblemCollector;)Lorg/apache/maven/model/interpolation/StringSearchModelInterpolator$InnerInterpolator;", "sig": "(Ljava/util/List<+Lorg/codehaus/plexus/interpolation/ValueSource;>;Ljava/util/List<+Lorg/codehaus/plexus/interpolation/InterpolationPostProcessor;>;Lorg/apache/maven/model/building/ModelProblemCollector;)Lorg/apache/maven/model/interpolation/StringSearchModelInterpolator$InnerInterpolator;"}, {"nme": "access$100", "acc": 4104, "dsc": "()Ljava/util/Map;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CACHED_ENTRIES", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Class<*>;Lorg/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction$CacheItem;>;"}], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "org/apache/maven/model/plugin/ReportingConverter.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/plugin/ReportingConverter", "super": "java/lang/Object", "mthds": [{"nme": "convertReporting", "acc": 1025, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}], "flds": []}, "org/apache/maven/model/building/AbstractModelBuildingListener.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/building/AbstractModelBuildingListener", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "buildExtensionsAssembled", "acc": 1, "dsc": "(Lorg/apache/maven/model/building/ModelBuildingEvent;)V"}], "flds": []}, "org/apache/maven/model/composition/DefaultDependencyManagementImporter.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/composition/DefaultDependencyManagementImporter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "importManagement", "acc": 1, "dsc": "(Lorg/apache/maven/model/Model;Ljava/util/List;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V", "sig": "(Lorg/apache/maven/model/Model;Ljava/util/List<+Lorg/apache/maven/model/DependencyManagement;>;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}], "flds": [], "vanns": [{"dsc": "Ljavax/inject/Named;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/model/io/ModelReader.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/io/ModelReader", "super": "java/lang/Object", "mthds": [{"nme": "read", "acc": 1025, "dsc": "(Ljava/io/File;Ljava/util/Map;)Lorg/apache/maven/model/Model;", "sig": "(Ljava/io/File;Ljava/util/Map<Ljava/lang/String;*>;)Lorg/apache/maven/model/Model;", "exs": ["java/io/IOException", "org/apache/maven/model/io/ModelParseException"]}, {"nme": "read", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;Lja<PERSON>/util/Map;)Lorg/apache/maven/model/Model;", "sig": "(Ljava/io/Reader;Ljava/util/Map<Ljava/lang/String;*>;)Lorg/apache/maven/model/Model;", "exs": ["java/io/IOException", "org/apache/maven/model/io/ModelParseException"]}, {"nme": "read", "acc": 1025, "dsc": "(Ljava/io/InputStream;Ljava/util/Map;)Lorg/apache/maven/model/Model;", "sig": "(Ljava/io/InputStream;Ljava/util/Map<Ljava/lang/String;*>;)Lorg/apache/maven/model/Model;", "exs": ["java/io/IOException", "org/apache/maven/model/io/ModelParseException"]}], "flds": [{"acc": 25, "nme": "IS_STRICT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "org.apache.maven.model.io.isStrict"}, {"acc": 25, "nme": "INPUT_SOURCE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "org.apache.maven.model.io.inputSource"}]}, "org/apache/maven/model/profile/activation/ProfileActivator.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/profile/activation/ProfileActivator", "super": "java/lang/Object", "mthds": [{"nme": "isActive", "acc": 1025, "dsc": "(Lorg/apache/maven/model/Profile;Lorg/apache/maven/model/profile/ProfileActivationContext;Lorg/apache/maven/model/building/ModelProblemCollector;)Z"}, {"nme": "presentInConfig", "acc": 1025, "dsc": "(Lorg/apache/maven/model/Profile;Lorg/apache/maven/model/profile/ProfileActivationContext;Lorg/apache/maven/model/building/ModelProblemCollector;)Z"}], "flds": []}, "org/apache/maven/model/profile/DefaultProfileInjector$ProfileModelMerger.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/profile/DefaultProfileInjector$ProfileModelMerger", "super": "org/apache/maven/model/merge/MavenModelMerger", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "mergeModelBase", "acc": 1, "dsc": "(Lorg/apache/maven/model/ModelBase;Lorg/apache/maven/model/ModelBase;)V"}, {"nme": "mergeBuildBase", "acc": 1, "dsc": "(Lorg/apache/maven/model/BuildBase;Lorg/apache/maven/model/BuildBase;)V"}, {"nme": "mergePluginContainer_Plugins", "acc": 4, "dsc": "(Lorg/apache/maven/model/PluginContainer;Lorg/apache/maven/model/PluginContainer;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/PluginContainer;Lorg/apache/maven/model/PluginContainer;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergePlugin_Executions", "acc": 4, "dsc": "(Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeReporting_Plugins", "acc": 4, "dsc": "(Lorg/apache/maven/model/Reporting;Lorg/apache/maven/model/Reporting;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Reporting;Lorg/apache/maven/model/Reporting;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeReportPlugin_ReportSets", "acc": 4, "dsc": "(Lorg/apache/maven/model/ReportPlugin;Lorg/apache/maven/model/ReportPlugin;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/ReportPlugin;Lorg/apache/maven/model/ReportPlugin;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}], "flds": []}, "org/apache/maven/model/building/ModelBuildingEventCatapult.class": {"ver": 52, "acc": 1536, "nme": "org/apache/maven/model/building/ModelBuildingEventCatapult", "super": "java/lang/Object", "mthds": [{"nme": "fire", "acc": 1025, "dsc": "(Lorg/apache/maven/model/building/ModelBuildingListener;Lorg/apache/maven/model/building/ModelBuildingEvent;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "BUILD_EXTENSIONS_ASSEMBLED", "dsc": "Lorg/apache/maven/model/building/ModelBuildingEventCatapult;"}]}, "org/apache/maven/model/building/ModelCache.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/building/ModelCache", "super": "java/lang/Object", "mthds": [{"nme": "put", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;L<PERSON><PERSON>/lang/Object;)V"}, {"nme": "get", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;L<PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "org/apache/maven/model/path/DefaultModelPathTranslator.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/path/DefaultModelPathTranslator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "setPathTranslator", "acc": 1, "dsc": "(Lorg/apache/maven/model/path/PathTranslator;)Lorg/apache/maven/model/path/DefaultModelPathTranslator;"}, {"nme": "alignToBaseDirectory", "acc": 1, "dsc": "(Lorg/apache/maven/model/Model;Ljava/io/File;Lorg/apache/maven/model/building/ModelBuildingRequest;)V"}, {"nme": "alignToBaseDirectory", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/io/File;)Ljava/lang/String;"}], "flds": [{"acc": 2, "nme": "pathTranslator", "dsc": "Lorg/apache/maven/model/path/PathTranslator;"}], "vanns": [{"dsc": "Ljavax/inject/Named;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/model/building/ModelCacheTag$2.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/building/ModelCacheTag$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getType", "acc": 1, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<Lorg/apache/maven/model/DependencyManagement;>;"}, {"nme": "intoCache", "acc": 1, "dsc": "(Lorg/apache/maven/model/DependencyManagement;)Lorg/apache/maven/model/DependencyManagement;"}, {"nme": "fromCache", "acc": 1, "dsc": "(Lorg/apache/maven/model/DependencyManagement;)Lorg/apache/maven/model/DependencyManagement;"}, {"nme": "fromCache", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "intoCache", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "org/apache/maven/model/building/ModelProblemCollector.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/building/ModelProblemCollector", "super": "java/lang/Object", "mthds": [{"nme": "add", "acc": 1025, "dsc": "(Lorg/apache/maven/model/building/ModelProblemCollectorRequest;)V"}], "flds": []}, "org/apache/maven/model/io/DefaultModelReader.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/io/DefaultModelReader", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Ljava/io/File;Ljava/util/Map;)Lorg/apache/maven/model/Model;", "sig": "(Ljava/io/File;Ljava/util/Map<Ljava/lang/String;*>;)Lorg/apache/maven/model/Model;", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;Lja<PERSON>/util/Map;)Lorg/apache/maven/model/Model;", "sig": "(Ljava/io/Reader;Ljava/util/Map<Ljava/lang/String;*>;)Lorg/apache/maven/model/Model;", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 1, "dsc": "(Ljava/io/InputStream;Ljava/util/Map;)Lorg/apache/maven/model/Model;", "sig": "(Ljava/io/InputStream;Ljava/util/Map<Ljava/lang/String;*>;)Lorg/apache/maven/model/Model;", "exs": ["java/io/IOException"]}, {"nme": "isStrict", "acc": 2, "dsc": "(Ljava/util/Map;)Z", "sig": "(Ljava/util/Map<Ljava/lang/String;*>;)Z"}, {"nme": "getSource", "acc": 2, "dsc": "(Ljava/util/Map;)Lorg/apache/maven/model/InputSource;", "sig": "(Ljava/util/Map<Ljava/lang/String;*>;)Lorg/apache/maven/model/InputSource;"}, {"nme": "read", "acc": 2, "dsc": "(Lja<PERSON>/io/Reader;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/Model;", "exs": ["java/io/IOException"]}], "flds": [], "vanns": [{"dsc": "Ljavax/inject/Named;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/model/building/DefaultModelBuildingRequest.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/building/DefaultModelBuildingRequest", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/apache/maven/model/building/ModelBuildingRequest;)V"}, {"nme": "getPomFile", "acc": 1, "dsc": "()Ljava/io/File;"}, {"nme": "setPomFile", "acc": 1, "dsc": "(Ljava/io/File;)Lorg/apache/maven/model/building/DefaultModelBuildingRequest;"}, {"nme": "getModelSource", "acc": 33, "dsc": "()Lorg/apache/maven/model/building/ModelSource;"}, {"nme": "setModelSource", "acc": 1, "dsc": "(Lorg/apache/maven/model/building/ModelSource;)Lorg/apache/maven/model/building/DefaultModelBuildingRequest;"}, {"nme": "getValidationLevel", "acc": 1, "dsc": "()I"}, {"nme": "setValidationLevel", "acc": 1, "dsc": "(I)Lorg/apache/maven/model/building/DefaultModelBuildingRequest;"}, {"nme": "isProcessPlugins", "acc": 1, "dsc": "()Z"}, {"nme": "setProcessPlugins", "acc": 1, "dsc": "(Z)Lorg/apache/maven/model/building/DefaultModelBuildingRequest;"}, {"nme": "isTwoPhaseBuilding", "acc": 1, "dsc": "()Z"}, {"nme": "setTwoPhaseBuilding", "acc": 1, "dsc": "(Z)Lorg/apache/maven/model/building/DefaultModelBuildingRequest;"}, {"nme": "isLocationTracking", "acc": 1, "dsc": "()Z"}, {"nme": "setLocationTracking", "acc": 1, "dsc": "(Z)Lorg/apache/maven/model/building/DefaultModelBuildingRequest;"}, {"nme": "getProfiles", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/Profile;>;"}, {"nme": "setProfiles", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/apache/maven/model/building/DefaultModelBuildingRequest;", "sig": "(Ljava/util/List<Lorg/apache/maven/model/Profile;>;)Lorg/apache/maven/model/building/DefaultModelBuildingRequest;"}, {"nme": "getActiveProfileIds", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "setActiveProfileIds", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/apache/maven/model/building/DefaultModelBuildingRequest;", "sig": "(Ljava/util/List<Ljava/lang/String;>;)Lorg/apache/maven/model/building/DefaultModelBuildingRequest;"}, {"nme": "getInactiveProfileIds", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "setInactiveProfileIds", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/apache/maven/model/building/DefaultModelBuildingRequest;", "sig": "(Ljava/util/List<Ljava/lang/String;>;)Lorg/apache/maven/model/building/DefaultModelBuildingRequest;"}, {"nme": "getSystemProperties", "acc": 1, "dsc": "()Ljava/util/Properties;"}, {"nme": "setSystemProperties", "acc": 1, "dsc": "(Ljava/util/Properties;)Lorg/apache/maven/model/building/DefaultModelBuildingRequest;"}, {"nme": "getUserProperties", "acc": 1, "dsc": "()Ljava/util/Properties;"}, {"nme": "setUserProperties", "acc": 1, "dsc": "(Ljava/util/Properties;)Lorg/apache/maven/model/building/DefaultModelBuildingRequest;"}, {"nme": "getBuildStartTime", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Date;"}, {"nme": "setBuildStartTime", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Date;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "getModelResolver", "acc": 1, "dsc": "()Lorg/apache/maven/model/resolution/ModelResolver;"}, {"nme": "setModelResolver", "acc": 1, "dsc": "(Lorg/apache/maven/model/resolution/ModelResolver;)Lorg/apache/maven/model/building/DefaultModelBuildingRequest;"}, {"nme": "getModelBuildingListener", "acc": 1, "dsc": "()Lorg/apache/maven/model/building/ModelBuildingListener;"}, {"nme": "setModelBuildingListener", "acc": 1, "dsc": "(Lorg/apache/maven/model/building/ModelBuildingListener;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "getModelCache", "acc": 1, "dsc": "()Lorg/apache/maven/model/building/ModelCache;"}, {"nme": "setModelCache", "acc": 1, "dsc": "(Lorg/apache/maven/model/building/ModelCache;)Lorg/apache/maven/model/building/DefaultModelBuildingRequest;"}, {"nme": "getRawModel", "acc": 1, "dsc": "()Lorg/apache/maven/model/Model;"}, {"nme": "setRawModel", "acc": 1, "dsc": "(Lorg/apache/maven/model/Model;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "getWorkspaceModelResolver", "acc": 1, "dsc": "()Lorg/apache/maven/model/resolution/WorkspaceModelResolver;"}, {"nme": "setWorkspaceModelResolver", "acc": 1, "dsc": "(Lorg/apache/maven/model/resolution/WorkspaceModelResolver;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "setModelCache", "acc": 4161, "dsc": "(Lorg/apache/maven/model/building/ModelCache;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "setModelResolver", "acc": 4161, "dsc": "(Lorg/apache/maven/model/resolution/ModelResolver;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "setUserProperties", "acc": 4161, "dsc": "(Ljava/util/Properties;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "setSystemProperties", "acc": 4161, "dsc": "(Ljava/util/Properties;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "setInactiveProfileIds", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "setActiveProfileIds", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "setProfiles", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "setLocationTracking", "acc": 4161, "dsc": "(Z)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "setTwoPhaseBuilding", "acc": 4161, "dsc": "(Z)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "setProcessPlugins", "acc": 4161, "dsc": "(Z)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "setValidationLevel", "acc": 4161, "dsc": "(I)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "setPomFile", "acc": 4161, "dsc": "(Ljava/io/File;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}, {"nme": "setModelSource", "acc": 4161, "dsc": "(Lorg/apache/maven/model/building/ModelSource;)Lorg/apache/maven/model/building/ModelBuildingRequest;"}], "flds": [{"acc": 2, "nme": "rawModel", "dsc": "Lorg/apache/maven/model/Model;"}, {"acc": 2, "nme": "pomFile", "dsc": "Ljava/io/File;"}, {"acc": 2, "nme": "modelSource", "dsc": "Lorg/apache/maven/model/building/ModelSource;"}, {"acc": 2, "nme": "validationLevel", "dsc": "I"}, {"acc": 2, "nme": "processPlugins", "dsc": "Z"}, {"acc": 2, "nme": "twoPhaseBuilding", "dsc": "Z"}, {"acc": 2, "nme": "locationTracking", "dsc": "Z"}, {"acc": 2, "nme": "profiles", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/model/Profile;>;"}, {"acc": 2, "nme": "activeProfileIds", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 2, "nme": "inactiveProfileIds", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 2, "nme": "systemProperties", "dsc": "Ljava/util/Properties;"}, {"acc": 2, "nme": "userProperties", "dsc": "Ljava/util/Properties;"}, {"acc": 2, "nme": "buildStartTime", "dsc": "<PERSON><PERSON><PERSON>/util/Date;"}, {"acc": 2, "nme": "modelResolver", "dsc": "Lorg/apache/maven/model/resolution/ModelResolver;"}, {"acc": 2, "nme": "modelBuildingListener", "dsc": "Lorg/apache/maven/model/building/ModelBuildingListener;"}, {"acc": 2, "nme": "modelCache", "dsc": "Lorg/apache/maven/model/building/ModelCache;"}, {"acc": 2, "nme": "workspaceResolver", "dsc": "Lorg/apache/maven/model/resolution/WorkspaceModelResolver;"}]}, "org/apache/maven/model/interpolation/StringSearchModelInterpolator$1.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/interpolation/StringSearchModelInterpolator$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/apache/maven/model/interpolation/StringSearchModelInterpolator;Ljava/util/Map;Lorg/codehaus/plexus/interpolation/StringSearchInterpolator;Lorg/codehaus/plexus/interpolation/RecursionInterceptor;Lorg/apache/maven/model/building/ModelProblemCollector;)V", "sig": "()V"}, {"nme": "interpolate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": [{"acc": 4112, "nme": "val$cache", "dsc": "Ljava/util/Map;"}, {"acc": 4112, "nme": "val$interpolator", "dsc": "Lorg/codehaus/plexus/interpolation/StringSearchInterpolator;"}, {"acc": 4112, "nme": "val$recursionInterceptor", "dsc": "Lorg/codehaus/plexus/interpolation/RecursionInterceptor;"}, {"acc": 4112, "nme": "val$problems", "dsc": "Lorg/apache/maven/model/building/ModelProblemCollector;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lorg/apache/maven/model/interpolation/StringSearchModelInterpolator;"}]}, "org/apache/maven/model/profile/DefaultProfileActivationContext.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/profile/DefaultProfileActivationContext", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getActiveProfileIds", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "setActiveProfileIds", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/apache/maven/model/profile/DefaultProfileActivationContext;", "sig": "(Ljava/util/List<Ljava/lang/String;>;)Lorg/apache/maven/model/profile/DefaultProfileActivationContext;"}, {"nme": "getInactiveProfileIds", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "setInactiveProfileIds", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/apache/maven/model/profile/DefaultProfileActivationContext;", "sig": "(Ljava/util/List<Ljava/lang/String;>;)Lorg/apache/maven/model/profile/DefaultProfileActivationContext;"}, {"nme": "getSystemProperties", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "setSystemProperties", "acc": 1, "dsc": "(Ljava/util/Properties;)Lorg/apache/maven/model/profile/DefaultProfileActivationContext;"}, {"nme": "setSystemProperties", "acc": 1, "dsc": "(Ljava/util/Map;)Lorg/apache/maven/model/profile/DefaultProfileActivationContext;", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)Lorg/apache/maven/model/profile/DefaultProfileActivationContext;"}, {"nme": "getUserProperties", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "setUserProperties", "acc": 1, "dsc": "(Ljava/util/Properties;)Lorg/apache/maven/model/profile/DefaultProfileActivationContext;"}, {"nme": "setUserProperties", "acc": 1, "dsc": "(Ljava/util/Map;)Lorg/apache/maven/model/profile/DefaultProfileActivationContext;", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)Lorg/apache/maven/model/profile/DefaultProfileActivationContext;"}, {"nme": "getProjectDirectory", "acc": 1, "dsc": "()Ljava/io/File;"}, {"nme": "setProjectDirectory", "acc": 1, "dsc": "(Ljava/io/File;)Lorg/apache/maven/model/profile/DefaultProfileActivationContext;"}, {"nme": "getProjectProperties", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "setProjectProperties", "acc": 1, "dsc": "(Ljava/util/Properties;)Lorg/apache/maven/model/profile/DefaultProfileActivationContext;"}, {"nme": "lambda$setProjectProperties$1", "acc": 4106, "dsc": "(Ljava/util/Map$Entry;)Ljava/lang/String;"}, {"nme": "lambda$setProjectProperties$0", "acc": 4106, "dsc": "(Ljava/util/Map$Entry;)Ljava/lang/String;"}], "flds": [{"acc": 2, "nme": "activeProfileIds", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 2, "nme": "inactiveProfileIds", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 2, "nme": "systemProperties", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"acc": 2, "nme": "userProperties", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"acc": 2, "nme": "projectProperties", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"acc": 2, "nme": "projectDirectory", "dsc": "Ljava/io/File;"}]}, "org/apache/maven/model/building/ModelBuildingListener.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/building/ModelBuildingListener", "super": "java/lang/Object", "mthds": [{"nme": "buildExtensionsAssembled", "acc": 1025, "dsc": "(Lorg/apache/maven/model/building/ModelBuildingEvent;)V"}], "flds": []}, "org/apache/maven/model/path/ProfileActivationFilePathInterpolator$1.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/path/ProfileActivationFilePathInterpolator$1", "super": "org/codehaus/plexus/interpolation/AbstractValueSource", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/apache/maven/model/path/ProfileActivationFilePathInterpolator;ZLjava/io/File;)V"}, {"nme": "getValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 4112, "nme": "val$basedir", "dsc": "Ljava/io/File;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lorg/apache/maven/model/path/ProfileActivationFilePathInterpolator;"}]}, "org/apache/maven/model/resolution/InvalidRepositoryException.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/resolution/InvalidRepositoryException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/apache/maven/model/Repository;<PERSON>ja<PERSON>/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/apache/maven/model/Repository;)V"}, {"nme": "getRepository", "acc": 1, "dsc": "()Lorg/apache/maven/model/Repository;"}], "flds": [{"acc": 2, "nme": "repository", "dsc": "Lorg/apache/maven/model/Repository;"}]}, "org/apache/maven/model/interpolation/ModelVersionProcessor.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/interpolation/ModelVersionProcessor", "super": "java/lang/Object", "mthds": [{"nme": "isValidProperty", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "overwriteModelProperties", "acc": 1025, "dsc": "(Ljava/util/Properties;Lorg/apache/maven/model/building/ModelBuildingRequest;)V"}], "flds": []}, "org/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction.class": {"ver": 52, "acc": 48, "nme": "org/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/interpolation/StringSearchModelInterpolator$InnerInterpolator;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}, {"nme": "run", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "interpolate", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "traverseObjectWithParents", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/Object;)V", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/Object;)V"}, {"nme": "getCacheEntry", "acc": 2, "dsc": "(Ljava/lang/Class;)Lorg/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction$CacheItem;", "sig": "(Ljava/lang/Class<*>;)Lorg/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction$CacheItem;"}, {"nme": "evaluateArray", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction;)V"}, {"nme": "access$200", "acc": 4104, "dsc": "(Lorg/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction;)Lorg/apache/maven/model/building/ModelProblemCollector;"}, {"nme": "access$300", "acc": 4104, "dsc": "(Lorg/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "access$400", "acc": 4104, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction;)V"}, {"nme": "access$500", "acc": 4104, "dsc": "(Lorg/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction;)Ljava/util/LinkedList;"}], "flds": [{"acc": 18, "nme": "interpolationTargets", "dsc": "<PERSON><PERSON><PERSON>/util/LinkedList;", "sig": "Ljava/util/LinkedList<Ljava/lang/Object;>;"}, {"acc": 18, "nme": "interpolator", "dsc": "Lorg/apache/maven/model/interpolation/StringSearchModelInterpolator$InnerInterpolator;"}, {"acc": 18, "nme": "problems", "dsc": "Lorg/apache/maven/model/building/ModelProblemCollector;"}]}, "org/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction$MapField.class": {"ver": 52, "acc": 48, "nme": "org/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction$MapField", "super": "org/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction$CacheField", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)V"}, {"nme": "doInterpolate", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/interpolation/StringSearchModelInterpolator$InterpolateObjectAction;)V", "exs": ["java/lang/IllegalAccessException"]}], "flds": []}, "org/apache/maven/model/path/DefaultModelUrlNormalizer.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/path/DefaultModelUrlNormalizer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "setUrlNormalizer", "acc": 1, "dsc": "(Lorg/apache/maven/model/path/UrlNormalizer;)Lorg/apache/maven/model/path/DefaultModelUrlNormalizer;"}, {"nme": "normalize", "acc": 1, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;)V"}, {"nme": "normalize", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": [{"acc": 2, "nme": "urlNormalizer", "dsc": "Lorg/apache/maven/model/path/UrlNormalizer;"}], "vanns": [{"dsc": "Ljavax/inject/Named;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/model/interpolation/StringVisitorModelInterpolator.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/interpolation/StringVisitorModelInterpolator", "super": "org/apache/maven/model/interpolation/AbstractStringBasedModelInterpolator", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "interpolateModel", "acc": 1, "dsc": "(Lorg/apache/maven/model/Model;Ljava/io/File;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)Lorg/apache/maven/model/Model;"}, {"nme": "createInterpolator", "acc": 2, "dsc": "(Ljava/util/List;Ljava/util/List;Lorg/apache/maven/model/building/ModelProblemCollector;)Lorg/apache/maven/model/interpolation/StringVisitorModelInterpolator$InnerInterpolator;", "sig": "(Ljava/util/List<+Lorg/codehaus/plexus/interpolation/ValueSource;>;Ljava/util/List<+Lorg/codehaus/plexus/interpolation/InterpolationPostProcessor;>;Lorg/apache/maven/model/building/ModelProblemCollector;)Lorg/apache/maven/model/interpolation/StringVisitorModelInterpolator$InnerInterpolator;"}], "flds": [], "vanns": [{"dsc": "Ljavax/inject/Named;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/model/interpolation/MavenBuildTimestamp.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/interpolation/MavenBuildTimestamp", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Date;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(L<PERSON><PERSON>/util/Date;Ljava/util/Properties;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Date;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "formattedTimestamp", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "DEFAULT_BUILD_TIMESTAMP_FORMAT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "yyyy-MM-dd'T'HH:mm:ss'Z'"}, {"acc": 25, "nme": "BUILD_TIMESTAMP_FORMAT_PROPERTY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "maven.build.timestamp.format"}, {"acc": 25, "nme": "DEFAULT_BUILD_TIME_ZONE", "dsc": "Ljava/util/TimeZone;"}, {"acc": 2, "nme": "formattedTimestamp", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/apache/maven/model/building/ModelCacheTag$1.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/building/ModelCacheTag$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getType", "acc": 1, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<Lorg/apache/maven/model/building/ModelData;>;"}, {"nme": "intoCache", "acc": 1, "dsc": "(Lorg/apache/maven/model/building/ModelData;)Lorg/apache/maven/model/building/ModelData;"}, {"nme": "fromCache", "acc": 1, "dsc": "(Lorg/apache/maven/model/building/ModelData;)Lorg/apache/maven/model/building/ModelData;"}, {"nme": "fromCache", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "intoCache", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "org/apache/maven/model/io/ModelParseException.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/io/ModelParseException", "super": "java/io/IOException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;II)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;II<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "getLineNumber", "acc": 1, "dsc": "()I"}, {"nme": "getColumnNumber", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "lineNumber", "dsc": "I"}, {"acc": 18, "nme": "columnNumber", "dsc": "I"}]}, "org/apache/maven/model/management/DefaultPluginManagementInjector.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/management/DefaultPluginManagementInjector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "injectManagement", "acc": 1, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}], "flds": [{"acc": 2, "nme": "merger", "dsc": "Lorg/apache/maven/model/management/DefaultPluginManagementInjector$ManagementModelMerger;"}], "vanns": [{"dsc": "Ljavax/inject/Named;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/model/validation/ModelValidator.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/validation/ModelValidator", "super": "java/lang/Object", "mthds": [{"nme": "validateRawModel", "acc": 1025, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}, {"nme": "validateEffectiveModel", "acc": 1025, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)V"}], "flds": []}}}}