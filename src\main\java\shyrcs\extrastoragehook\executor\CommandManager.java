package shyrcs.extrastoragehook.executor;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * Manager cho Discord commands
 */
public class CommandManager {
    
    private final Map<String, DiscordExecutor> commands;
    
    public CommandManager() {
        this.commands = new HashMap<>();
    }
    
    /**
     * Đăng ký command
     */
    public void registerCommand(String name, DiscordExecutor executor) {
        commands.put(name.toLowerCase(), executor);
    }
    
    /**
     * Lấy command executor
     */
    public DiscordExecutor getCommand(String name) {
        return commands.get(name.toLowerCase());
    }
    
    /**
     * <PERSON><PERSON><PERSON> tra command có tồn tại không
     */
    public boolean hasCommands(String name) {
        return commands.containsKey(name.toLowerCase());
    }
    
    /**
     * Xóa command
     */
    public void unregisterCommand(String name) {
        commands.remove(name.toLowerCase());
    }
    
    /**
     * <PERSON><PERSON>y tất cả tên commands
     */
    public Set<String> getCommandNames() {
        return commands.keySet();
    }
    
    /**
     * <PERSON><PERSON><PERSON> số lượng commands
     */
    public int getCommandCount() {
        return commands.size();
    }
    
    /**
     * Xóa tất cả commands
     */
    public void clearAll() {
        commands.clear();
    }
}
