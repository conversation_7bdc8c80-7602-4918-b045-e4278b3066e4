{"md5": "5c507ce56dd3fefef1f51c2b00d32148", "sha2": "b5e6779426eefe1b2325fa29549b11d5fc3e6646", "sha256": "80e9d581a43183c1034f1e3b29e063c80c3354892f753b11269afd6a0666fad2", "contents": {"classes": {"classes/module-info.class": {"ver": 68, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "classes/jdk/internal/editor/external/ExternalEditor$1.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/editor/external/ExternalEditor$1", "super": "java/nio/file/SimpleFileVisitor", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/internal/editor/external/ExternalEditor;)V"}, {"nme": "visitFile", "acc": 1, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/attribute/BasicFileAttributes;)Ljava/nio/file/FileVisitResult;", "exs": ["java/io/IOException"]}, {"nme": "postVisitDirectory", "acc": 1, "dsc": "(Ljava/nio/file/Path;Ljava/io/IOException;)Ljava/nio/file/FileVisitResult;", "exs": ["java/io/IOException"]}, {"nme": "postVisitDirectory", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Ljava/io/IOException;)Ljava/nio/file/FileVisitResult;", "exs": ["java/io/IOException"]}, {"nme": "visitFile", "acc": 4161, "dsc": "(Lja<PERSON>/lang/Object;Ljava/nio/file/attribute/BasicFileAttributes;)Ljava/nio/file/FileVisitResult;", "exs": ["java/io/IOException"]}], "flds": []}, "classes/jdk/internal/editor/external/ExternalEditor.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/editor/external/ExternalEditor", "super": "java/lang/Object", "mthds": [{"nme": "edit", "acc": 9, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;Ljava/util/function/Consumer;Ljava/util/function/Consumer;Ljava/lang/Runnable;<PERSON><PERSON><PERSON>/lang/Runnable;Z<PERSON><PERSON>va/lang/Runnable;)V", "sig": "([Ljava/lang/String;Ljava/lang/String;Ljava/util/function/Consumer<Ljava/lang/String;>;Ljava/util/function/Consumer<Ljava/lang/String;>;Ljava/lang/Runnable;Ljava/lang/Runnable;ZLjava/lang/Runnable;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(Ljava/util/function/Consumer;Ljava/util/function/Consumer;Ljava/lang/Runnable;<PERSON><PERSON><PERSON>/lang/Runnable;ZLjava/lang/Runnable;)V", "sig": "(Ljava/util/function/Consumer<Ljava/lang/String;>;Ljava/util/function/Consumer<Ljava/lang/String;>;Ljava/lang/Runnable;Ljava/lang/Runnable;ZLjava/lang/Runnable;)V"}, {"nme": "edit", "acc": 2, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;)V"}, {"nme": "setupWatch", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "launch", "acc": 2, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "saveFile", "acc": 2, "dsc": "()V"}, {"nme": "deleteDirectory", "acc": 2, "dsc": "()V"}, {"nme": "lambda$setupWatch$0", "acc": 4098, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "Ljava/util/function/Consumer;", "sig": "Ljava/util/function/Consumer<Ljava/lang/String;>;"}, {"acc": 18, "nme": "saveHandler", "dsc": "Ljava/util/function/Consumer;", "sig": "Ljava/util/function/Consumer<Ljava/lang/String;>;"}, {"acc": 18, "nme": "wait", "dsc": "Z"}, {"acc": 18, "nme": "suspendInteractiveInput", "dsc": "<PERSON><PERSON><PERSON>/lang/Runnable;"}, {"acc": 18, "nme": "resumeInteractiveInput", "dsc": "<PERSON><PERSON><PERSON>/lang/Runnable;"}, {"acc": 18, "nme": "promptForNewLineToEndWait", "dsc": "<PERSON><PERSON><PERSON>/lang/Runnable;"}, {"acc": 2, "nme": "watcher", "dsc": "Ljava/nio/file/WatchService;"}, {"acc": 2, "nme": "watched<PERSON><PERSON><PERSON>", "dsc": "<PERSON><PERSON><PERSON>/lang/Thread;"}, {"acc": 2, "nme": "dir", "dsc": "Ljava/nio/file/Path;"}, {"acc": 2, "nme": "tmpfile", "dsc": "Ljava/nio/file/Path;"}]}, "classes/jdk/internal/editor/spi/BuildInEditorProvider.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/editor/spi/BuildInEditorProvider", "super": "java/lang/Object", "mthds": [{"nme": "rank", "acc": 1025, "dsc": "()I"}, {"nme": "edit", "acc": 1025, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;Ljava/util/function/Consumer;Ljava/util/function/Consumer;)V", "sig": "(Ljava/lang/String;Ljava/lang/String;Ljava/util/function/Consumer<Ljava/lang/String;>;Ljava/util/function/Consumer<Ljava/lang/String;>;)V"}], "flds": []}}}}