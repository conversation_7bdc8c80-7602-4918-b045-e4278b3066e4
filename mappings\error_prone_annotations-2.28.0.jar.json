{"md5": "9e5d2181ddb6acb58fe08e86198c4232", "sha2": "59fc00087ce372de42e394d2c789295dff2d19f0", "sha256": "f3fc8a3a0a4020706a373b00e7f57c2512dd26d1f83d28c7d38768f8682b231e", "contents": {"classes": {"com/google/errorprone/annotations/DoNotCall.class": {"ver": 52, "acc": 9729, "nme": "com/google/errorprone/annotations/DoNotCall", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"]]]}]}, "com/google/errorprone/annotations/MustBeClosed.class": {"ver": 52, "acc": 9729, "nme": "com/google/errorprone/annotations/MustBeClosed", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "CONSTRUCTOR"], ["L<PERSON>va/lang/annotation/ElementType;", "METHOD"]]]}]}, "com/google/errorprone/annotations/SuppressPackageLocation.class": {"ver": 52, "acc": 9729, "nme": "com/google/errorprone/annotations/SuppressPackageLocation", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "PACKAGE"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}]}, "com/google/errorprone/annotations/FormatMethod.class": {"ver": 52, "acc": 9729, "nme": "com/google/errorprone/annotations/FormatMethod", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "CONSTRUCTOR"]]]}]}, "com/google/errorprone/annotations/CheckReturnValue.class": {"ver": 52, "acc": 9729, "nme": "com/google/errorprone/annotations/CheckReturnValue", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "CONSTRUCTOR"], ["L<PERSON>va/lang/annotation/ElementType;", "TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "PACKAGE"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "RUNTIME"]]}]}, "com/google/errorprone/annotations/DoNotMock.class": {"ver": 52, "acc": 9729, "nme": "com/google/errorprone/annotations/DoNotMock", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Inherited;"}, {"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "RUNTIME"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "ANNOTATION_TYPE"]]]}]}, "com/google/errorprone/annotations/RequiredModifiers.class": {"ver": 52, "acc": 9729, "nme": "com/google/errorprone/annotations/RequiredModifiers", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 132097, "dsc": "()[Ljavax/lang/model/element/Modifier;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "modifier", "acc": 1025, "dsc": "()[Lcom/google/errorprone/annotations/Modifier;"}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "ANNOTATION_TYPE"]]]}]}, "com/google/errorprone/annotations/OverridingMethodsMustInvokeSuper.class": {"ver": 52, "acc": 9729, "nme": "com/google/errorprone/annotations/OverridingMethodsMustInvokeSuper", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}]}, "com/google/errorprone/annotations/CanIgnoreReturnValue.class": {"ver": 52, "acc": 9729, "nme": "com/google/errorprone/annotations/CanIgnoreReturnValue", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "CONSTRUCTOR"], ["L<PERSON>va/lang/annotation/ElementType;", "TYPE"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}]}, "com/google/errorprone/annotations/concurrent/GuardedBy.class": {"ver": 52, "acc": 9729, "nme": "com/google/errorprone/annotations/concurrent/GuardedBy", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "FIELD"], ["L<PERSON>va/lang/annotation/ElementType;", "METHOD"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}]}, "com/google/errorprone/annotations/InlineMeValidationDisabled.class": {"ver": 52, "acc": 9729, "nme": "com/google/errorprone/annotations/InlineMeValidationDisabled", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "CONSTRUCTOR"]]]}]}, "com/google/errorprone/annotations/Var.class": {"ver": 52, "acc": 9729, "nme": "com/google/errorprone/annotations/Var", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "FIELD"], ["L<PERSON>va/lang/annotation/ElementType;", "PARAMETER"], ["L<PERSON>va/lang/annotation/ElementType;", "LOCAL_VARIABLE"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "RUNTIME"]]}], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/IncompatibleModifiers;", "vals": ["modifier", [["Lcom/google/errorprone/annotations/Modifier;", "FINAL"]]]}]}, "com/google/errorprone/annotations/NoAllocation.class": {"ver": 52, "acc": 9729, "nme": "com/google/errorprone/annotations/NoAllocation", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"]]]}]}, "com/google/errorprone/annotations/IncompatibleModifiers.class": {"ver": 52, "acc": 9729, "nme": "com/google/errorprone/annotations/IncompatibleModifiers", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 132097, "dsc": "()[Ljavax/lang/model/element/Modifier;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "modifier", "acc": 1025, "dsc": "()[Lcom/google/errorprone/annotations/Modifier;"}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "ANNOTATION_TYPE"]]]}]}, "com/google/errorprone/annotations/CompatibleWith.class": {"ver": 52, "acc": 9729, "nme": "com/google/errorprone/annotations/CompatibleWith", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "PARAMETER"]]]}]}, "com/google/errorprone/annotations/concurrent/LockMethod.class": {"ver": 52, "acc": 140801, "nme": "com/google/errorprone/annotations/concurrent/LockMethod", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "com/google/errorprone/annotations/ThreadSafe.class": {"ver": 52, "acc": 9729, "nme": "com/google/errorprone/annotations/ThreadSafe", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "TYPE"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "RUNTIME"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Inherited;"}, {"dsc": "Ljava/lang/annotation/Documented;"}]}, "com/google/errorprone/annotations/FormatString.class": {"ver": 52, "acc": 9729, "nme": "com/google/errorprone/annotations/FormatString", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "PARAMETER"]]]}]}, "com/google/errorprone/annotations/Keep.class": {"ver": 52, "acc": 9729, "nme": "com/google/errorprone/annotations/Keep", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "ANNOTATION_TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "CONSTRUCTOR"], ["L<PERSON>va/lang/annotation/ElementType;", "FIELD"], ["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "TYPE"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "RUNTIME"]]}]}, "META-INF/versions/9/module-info.class": {"ver": 53, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "com/google/errorprone/annotations/RestrictedApi.class": {"ver": 52, "acc": 9729, "nme": "com/google/errorprone/annotations/RestrictedApi", "super": "java/lang/Object", "mthds": [{"nme": "explanation", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "link", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "allowlistAnnotations", "acc": 1025, "dsc": "()[<PERSON><PERSON><PERSON>/lang/Class;", "sig": "()[<PERSON><PERSON><PERSON>/lang/Class<+Ljava/lang/annotation/Annotation;>;"}, {"nme": "allowlistWithWarningAnnotations", "acc": 1025, "dsc": "()[<PERSON><PERSON><PERSON>/lang/Class;", "sig": "()[<PERSON><PERSON><PERSON>/lang/Class<+Ljava/lang/annotation/Annotation;>;"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "CONSTRUCTOR"], ["L<PERSON>va/lang/annotation/ElementType;", "METHOD"]]]}]}, "com/google/errorprone/annotations/Immutable.class": {"ver": 52, "acc": 9729, "nme": "com/google/errorprone/annotations/Immutable", "super": "java/lang/Object", "mthds": [{"nme": "containerOf", "acc": 1025, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "TYPE"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "RUNTIME"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Inherited;"}]}, "com/google/errorprone/annotations/CompileTimeConstant.class": {"ver": 52, "acc": 9729, "nme": "com/google/errorprone/annotations/CompileTimeConstant", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "PARAMETER"], ["L<PERSON>va/lang/annotation/ElementType;", "FIELD"]]]}]}, "com/google/errorprone/annotations/ForOverride.class": {"ver": 52, "acc": 9729, "nme": "com/google/errorprone/annotations/ForOverride", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"]]]}], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/IncompatibleModifiers;", "vals": ["modifier", [["Lcom/google/errorprone/annotations/Modifier;", "PUBLIC"], ["Lcom/google/errorprone/annotations/Modifier;", "PRIVATE"], ["Lcom/google/errorprone/annotations/Modifier;", "STATIC"], ["Lcom/google/errorprone/annotations/Modifier;", "FINAL"]]]}]}, "com/google/errorprone/annotations/Modifier.class": {"ver": 52, "acc": 16433, "nme": "com/google/errorprone/annotations/Modifier", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lcom/google/errorprone/annotations/Modifier;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/google/errorprone/annotations/Modifier;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Lcom/google/errorprone/annotations/Modifier;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "PUBLIC", "dsc": "Lcom/google/errorprone/annotations/Modifier;"}, {"acc": 16409, "nme": "PROTECTED", "dsc": "Lcom/google/errorprone/annotations/Modifier;"}, {"acc": 16409, "nme": "PRIVATE", "dsc": "Lcom/google/errorprone/annotations/Modifier;"}, {"acc": 16409, "nme": "ABSTRACT", "dsc": "Lcom/google/errorprone/annotations/Modifier;"}, {"acc": 16409, "nme": "DEFAULT", "dsc": "Lcom/google/errorprone/annotations/Modifier;"}, {"acc": 16409, "nme": "STATIC", "dsc": "Lcom/google/errorprone/annotations/Modifier;"}, {"acc": 16409, "nme": "FINAL", "dsc": "Lcom/google/errorprone/annotations/Modifier;"}, {"acc": 16409, "nme": "TRANSIENT", "dsc": "Lcom/google/errorprone/annotations/Modifier;"}, {"acc": 16409, "nme": "VOLATILE", "dsc": "Lcom/google/errorprone/annotations/Modifier;"}, {"acc": 16409, "nme": "SYNCHRONIZED", "dsc": "Lcom/google/errorprone/annotations/Modifier;"}, {"acc": 16409, "nme": "NATIVE", "dsc": "Lcom/google/errorprone/annotations/Modifier;"}, {"acc": 16409, "nme": "STRICTFP", "dsc": "Lcom/google/errorprone/annotations/Modifier;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lcom/google/errorprone/annotations/Modifier;"}]}, "com/google/errorprone/annotations/concurrent/LazyInit.class": {"ver": 52, "acc": 9729, "nme": "com/google/errorprone/annotations/concurrent/LazyInit", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "RUNTIME"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "FIELD"]]]}], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/IncompatibleModifiers;", "vals": ["modifier", [["Lcom/google/errorprone/annotations/Modifier;", "FINAL"]]]}]}, "com/google/errorprone/annotations/concurrent/UnlockMethod.class": {"ver": 52, "acc": 140801, "nme": "com/google/errorprone/annotations/concurrent/UnlockMethod", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "com/google/errorprone/annotations/InlineMe.class": {"ver": 52, "acc": 9729, "nme": "com/google/errorprone/annotations/InlineMe", "super": "java/lang/Object", "mthds": [{"nme": "replacement", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "imports", "acc": 1025, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "staticImports", "acc": 1025, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "CONSTRUCTOR"]]]}]}}}}