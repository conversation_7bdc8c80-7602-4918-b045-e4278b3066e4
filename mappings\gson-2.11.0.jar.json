{"md5": "0c69b9199d3a4e6c34dc03619ff7feee", "sha2": "527175ca6d81050b53bdd4c457a6d6e017626b0e", "sha256": "57928d6e5a6edeb2abd3770a8f95ba44dce45f3b23b7a9dc2b309c581552a78b", "contents": {"classes": {"com/google/gson/internal/bind/JsonTreeWriter.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/internal/bind/JsonTreeWriter", "super": "com/google/gson/stream/JsonWriter", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "get", "acc": 1, "dsc": "()Lcom/google/gson/JsonElement;"}, {"nme": "peek", "acc": 2, "dsc": "()Lcom/google/gson/JsonElement;"}, {"nme": "put", "acc": 2, "dsc": "(Lcom/google/gson/JsonElement;)V"}, {"nme": "beginArray", "acc": 1, "dsc": "()Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "endArray", "acc": 1, "dsc": "()Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "beginObject", "acc": 1, "dsc": "()Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "endObject", "acc": 1, "dsc": "()Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "name", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "value", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "value", "acc": 1, "dsc": "(Z)Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "value", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Bo<PERSON>an;)Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "value", "acc": 1, "dsc": "(F)Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "value", "acc": 1, "dsc": "(D)Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "value", "acc": 1, "dsc": "(J)Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "value", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Number;)Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "nullValue", "acc": 1, "dsc": "()Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "jsonValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"]}, {"nme": "flush", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "UNWRITABLE_WRITER", "dsc": "<PERSON><PERSON><PERSON>/io/Writer;"}, {"acc": 26, "nme": "SENTINEL_CLOSED", "dsc": "Lcom/google/gson/JsonPrimitive;"}, {"acc": 18, "nme": "stack", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lcom/google/gson/JsonElement;>;"}, {"acc": 2, "nme": "pendingName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "product", "dsc": "Lcom/google/gson/JsonElement;"}]}, "com/google/gson/internal/sql/SqlTimeTypeAdapter$1.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/sql/SqlTimeTypeAdapter$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "create", "acc": 1, "dsc": "(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken;)Lcom/google/gson/TypeAdapter;", "sig": "<T:Ljava/lang/Object;>(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken<TT;>;)Lcom/google/gson/TypeAdapter<TT;>;"}], "flds": []}, "com/google/gson/internal/ConstructorConstructor$18.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/ConstructorConstructor$18", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "construct", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}], "flds": []}, "com/google/gson/internal/UnsafeAllocator$2.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/UnsafeAllocator$2", "super": "com/google/gson/internal/UnsafeAllocator", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;I)V"}, {"nme": "newInstance", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>ja<PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;)TT;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 4112, "nme": "val$newInstance", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 4112, "nme": "val$constructorId", "dsc": "I"}]}, "com/google/gson/internal/Primitives.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/internal/Primitives", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "isPrimitive", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Type;)Z"}, {"nme": "isWrapperType", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Type;)Z"}, {"nme": "wrap", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/Class;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;)Ljava/lang/Class<TT;>;"}, {"nme": "unwrap", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/Class;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;)Ljava/lang/Class<TT;>;"}], "flds": []}, "com/google/gson/internal/bind/JsonAdapterAnnotationTypeAdapterFactory$1.class": {"ver": 51, "acc": 4128, "nme": "com/google/gson/internal/bind/JsonAdapterAnnotationTypeAdapterFactory$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "com/google/gson/JsonNull.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/JsonNull", "super": "com/google/gson/JsonElement", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "deepCopy", "acc": 1, "dsc": "()Lcom/google/gson/JsonNull;"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "deepCopy", "acc": 4161, "dsc": "()Lcom/google/gson/JsonElement;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "INSTANCE", "dsc": "Lcom/google/gson/Json<PERSON>ull;"}]}, "com/google/gson/Gson$3.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/Gson$3", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Number;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/lang/Number;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/ReflectionAccessFilter$3.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/ReflectionAccessFilter$3", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "check", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Lcom/google/gson/ReflectionAccessFilter$FilterResult;", "sig": "(L<PERSON><PERSON>/lang/Class<*>;)Lcom/google/gson/ReflectionAccessFilter$FilterResult;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "com/google/gson/internal/bind/TypeAdapters$17.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$17", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/math/BigInteger;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/math/BigInteger;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/internal/bind/TypeAdapters$25.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$25", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/util/Currency;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/util/Currency;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/ToNumberPolicy.class": {"ver": 51, "acc": 17441, "nme": "com/google/gson/ToNumberPolicy", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lcom/google/gson/ToNumberPolicy;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/google/gson/ToNumberPolicy;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "<init>", "acc": 4096, "dsc": "(Ljava/lang/String;ILcom/google/gson/ToNumberPolicy$1;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "DOUBLE", "dsc": "Lcom/google/gson/ToNumberPolicy;"}, {"acc": 16409, "nme": "LAZILY_PARSED_NUMBER", "dsc": "Lcom/google/gson/ToNumberPolicy;"}, {"acc": 16409, "nme": "LONG_OR_DOUBLE", "dsc": "Lcom/google/gson/ToNumberPolicy;"}, {"acc": 16409, "nme": "BIG_DECIMAL", "dsc": "Lcom/google/gson/ToNumberPolicy;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lcom/google/gson/ToNumberPolicy;"}]}, "com/google/gson/internal/$Gson$Types$GenericArrayTypeImpl.class": {"ver": 51, "acc": 48, "nme": "com/google/gson/internal/$Gson$Types$GenericArrayTypeImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Type;)V"}, {"nme": "getGenericComponentType", "acc": 1, "dsc": "()Ljava/lang/reflect/Type;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "componentType", "dsc": "Ljava/lang/reflect/Type;"}, {"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 0}]}, "com/google/gson/JsonDeserializer.class": {"ver": 51, "acc": 1537, "nme": "com/google/gson/JsonDeserializer", "super": "java/lang/Object", "mthds": [{"nme": "deserialize", "acc": 1025, "dsc": "(Lcom/google/gson/JsonElement;Ljava/lang/reflect/Type;Lcom/google/gson/JsonDeserializationContext;)Ljava/lang/Object;", "sig": "(Lcom/google/gson/JsonElement;Ljava/lang/reflect/Type;Lcom/google/gson/JsonDeserializationContext;)TT;", "exs": ["com/google/gson/JsonParseException"]}], "flds": []}, "com/google/gson/internal/ConstructorConstructor$2.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/ConstructorConstructor$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/google/gson/internal/ConstructorConstructor;Lcom/google/gson/InstanceCreator;Ljava/lang/reflect/Type;)V"}, {"nme": "construct", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}], "flds": [{"acc": 4112, "nme": "val$rawTypeCreator", "dsc": "Lcom/google/gson/InstanceCreator;"}, {"acc": 4112, "nme": "val$type", "dsc": "Ljava/lang/reflect/Type;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lcom/google/gson/internal/ConstructorConstructor;"}]}, "com/google/gson/internal/ReflectionAccessFilterHelper$AccessChecker.class": {"ver": 51, "acc": 1056, "nme": "com/google/gson/internal/ReflectionAccessFilterHelper$AccessChecker", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "canAccess", "acc": 1025, "dsc": "(Lja<PERSON>/lang/reflect/AccessibleObject;Ljava/lang/Object;)Z"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lcom/google/gson/internal/ReflectionAccessFilterHelper$1;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "INSTANCE", "dsc": "Lcom/google/gson/internal/ReflectionAccessFilterHelper$AccessChecker;"}]}, "com/google/gson/internal/ConstructorConstructor$8.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/ConstructorConstructor$8", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "construct", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}], "flds": [{"acc": 4112, "nme": "val$exceptionMessage", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "com/google/gson/ExclusionStrategy.class": {"ver": 51, "acc": 1537, "nme": "com/google/gson/ExclusionStrategy", "super": "java/lang/Object", "mthds": [{"nme": "shouldSkipField", "acc": 1025, "dsc": "(Lcom/google/gson/FieldAttributes;)Z"}, {"nme": "shouldSkipClass", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}], "flds": []}, "com/google/gson/Gson.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/Gson", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 0, "dsc": "(Lcom/google/gson/internal/Excluder;Lcom/google/gson/FieldNamingStrategy;Ljava/util/Map;ZZZZLcom/google/gson/FormattingStyle;Lcom/google/gson/Strictness;ZZLcom/google/gson/LongSerializationPolicy;Ljava/lang/String;IILjava/util/List;Ljava/util/List;Ljava/util/List;Lcom/google/gson/ToNumberStrategy;Lcom/google/gson/ToNumberStrategy;Ljava/util/List;)V", "sig": "(Lcom/google/gson/internal/Excluder;Lcom/google/gson/FieldNamingStrategy;Ljava/util/Map<Ljava/lang/reflect/Type;Lcom/google/gson/InstanceCreator<*>;>;ZZZZLcom/google/gson/FormattingStyle;Lcom/google/gson/Strictness;ZZLcom/google/gson/LongSerializationPolicy;Ljava/lang/String;IILjava/util/List<Lcom/google/gson/TypeAdapterFactory;>;Ljava/util/List<Lcom/google/gson/TypeAdapterFactory;>;Ljava/util/List<Lcom/google/gson/TypeAdapterFactory;>;Lcom/google/gson/ToNumberStrategy;Lcom/google/gson/ToNumberStrategy;Ljava/util/List<Lcom/google/gson/ReflectionAccessFilter;>;)V"}, {"nme": "newBuilder", "acc": 1, "dsc": "()Lcom/google/gson/GsonBuilder;"}, {"nme": "excluder", "acc": 131073, "dsc": "()Lcom/google/gson/internal/Excluder;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "fieldNamingStrategy", "acc": 1, "dsc": "()Lcom/google/gson/FieldNamingStrategy;"}, {"nme": "serializeNulls", "acc": 1, "dsc": "()Z"}, {"nme": "htmlSafe", "acc": 1, "dsc": "()Z"}, {"nme": "doubleAdapter", "acc": 2, "dsc": "(Z)Lcom/google/gson/TypeAdapter;", "sig": "(Z)Lcom/google/gson/TypeAdapter<Ljava/lang/Number;>;"}, {"nme": "floatAdapter", "acc": 2, "dsc": "(Z)Lcom/google/gson/TypeAdapter;", "sig": "(Z)Lcom/google/gson/TypeAdapter<Ljava/lang/Number;>;"}, {"nme": "checkValidFloatingPoint", "acc": 8, "dsc": "(D)V"}, {"nme": "longAdapter", "acc": 10, "dsc": "(Lcom/google/gson/LongSerializationPolicy;)Lcom/google/gson/TypeAdapter;", "sig": "(Lcom/google/gson/LongSerializationPolicy;)Lcom/google/gson/TypeAdapter<Ljava/lang/Number;>;"}, {"nme": "atomicLongAdapter", "acc": 10, "dsc": "(Lcom/google/gson/TypeAdapter;)Lcom/google/gson/TypeAdapter;", "sig": "(Lcom/google/gson/TypeAdapter<Ljava/lang/Number;>;)Lcom/google/gson/TypeAdapter<Ljava/util/concurrent/atomic/AtomicLong;>;"}, {"nme": "atomicLongArrayAdapter", "acc": 10, "dsc": "(Lcom/google/gson/TypeAdapter;)Lcom/google/gson/TypeAdapter;", "sig": "(Lcom/google/gson/TypeAdapter<Ljava/lang/Number;>;)Lcom/google/gson/TypeAdapter<Ljava/util/concurrent/atomic/AtomicLongArray;>;"}, {"nme": "getAdapter", "acc": 1, "dsc": "(Lcom/google/gson/reflect/TypeToken;)Lcom/google/gson/TypeAdapter;", "sig": "<T:Ljava/lang/Object;>(Lcom/google/gson/reflect/TypeToken<TT;>;)Lcom/google/gson/TypeAdapter<TT;>;"}, {"nme": "getAdapter", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Lcom/google/gson/TypeAdapter;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;)Lcom/google/gson/TypeAdapter<TT;>;"}, {"nme": "getDelegateAdapter", "acc": 1, "dsc": "(Lcom/google/gson/TypeAdapterFactory;Lcom/google/gson/reflect/TypeToken;)Lcom/google/gson/TypeAdapter;", "sig": "<T:Ljava/lang/Object;>(Lcom/google/gson/TypeAdapterFactory;Lcom/google/gson/reflect/TypeToken<TT;>;)Lcom/google/gson/TypeAdapter<TT;>;"}, {"nme": "toJsonTree", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lcom/google/gson/JsonElement;"}, {"nme": "toJsonTree", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Ljava/lang/reflect/Type;)Lcom/google/gson/JsonElement;"}, {"nme": "to<PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON>ja<PERSON>/lang/String;"}, {"nme": "to<PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;L<PERSON><PERSON>/lang/reflect/Type;)Ljava/lang/String;"}, {"nme": "to<PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Appendable;)V", "exs": ["com/google/gson/JsonIOException"]}, {"nme": "to<PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/reflect/Type;Ljava/lang/Appendable;)V", "exs": ["com/google/gson/JsonIOException"]}, {"nme": "to<PERSON><PERSON>", "acc": 1, "dsc": "(Lja<PERSON>/lang/Object;Ljava/lang/reflect/Type;Lcom/google/gson/stream/JsonWriter;)V", "exs": ["com/google/gson/JsonIOException"]}, {"nme": "to<PERSON><PERSON>", "acc": 1, "dsc": "(Lcom/google/gson/JsonElement;)Ljava/lang/String;"}, {"nme": "to<PERSON><PERSON>", "acc": 1, "dsc": "(Lcom/google/gson/JsonElement;<PERSON><PERSON><PERSON>/lang/Appendable;)V", "exs": ["com/google/gson/JsonIOException"]}, {"nme": "to<PERSON><PERSON>", "acc": 1, "dsc": "(Lcom/google/gson/JsonElement;Lcom/google/gson/stream/JsonWriter;)V", "exs": ["com/google/gson/JsonIOException"]}, {"nme": "newJsonWriter", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Writer;)Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"]}, {"nme": "newJsonReader", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;)Lcom/google/gson/stream/JsonReader;"}, {"nme": "fromJson", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Class;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/String;Ljava/lang/Class<TT;>;)TT;", "exs": ["com/google/gson/JsonSyntaxException"]}, {"nme": "fromJson", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;L<PERSON><PERSON>/lang/reflect/Type;)Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/String;Ljava/lang/reflect/Type;)TT;", "exs": ["com/google/gson/JsonSyntaxException"]}, {"nme": "fromJson", "acc": 1, "dsc": "(Ljava/lang/String;Lcom/google/gson/reflect/TypeToken;)Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/String;Lcom/google/gson/reflect/TypeToken<TT;>;)TT;", "exs": ["com/google/gson/JsonSyntaxException"]}, {"nme": "fromJson", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;<PERSON><PERSON><PERSON>/lang/Class;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "<T:Lja<PERSON>/lang/Object;>(Lja<PERSON>/io/Reader;Ljava/lang/Class<TT;>;)TT;", "exs": ["com/google/gson/JsonSyntaxException", "com/google/gson/JsonIOException"]}, {"nme": "fromJson", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;<PERSON><PERSON><PERSON>/lang/reflect/Type;)L<PERSON><PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Lja<PERSON>/io/Reader;Ljava/lang/reflect/Type;)TT;", "exs": ["com/google/gson/JsonIOException", "com/google/gson/JsonSyntaxException"]}, {"nme": "fromJson", "acc": 1, "dsc": "(Ljava/io/Reader;Lcom/google/gson/reflect/TypeToken;)Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/io/Reader;Lcom/google/gson/reflect/TypeToken<TT;>;)TT;", "exs": ["com/google/gson/JsonIOException", "com/google/gson/JsonSyntaxException"]}, {"nme": "fromJson", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;Ljava/lang/reflect/Type;)Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Lcom/google/gson/stream/JsonReader;Ljava/lang/reflect/Type;)TT;", "exs": ["com/google/gson/JsonIOException", "com/google/gson/JsonSyntaxException"]}, {"nme": "fromJson", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;Lcom/google/gson/reflect/TypeToken;)Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Lcom/google/gson/stream/JsonReader;Lcom/google/gson/reflect/TypeToken<TT;>;)TT;", "exs": ["com/google/gson/JsonIOException", "com/google/gson/JsonSyntaxException"]}, {"nme": "fromJson", "acc": 1, "dsc": "(Lcom/google/gson/JsonElement;Ljava/lang/Class;)Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Lcom/google/gson/JsonElement;Ljava/lang/Class<TT;>;)TT;", "exs": ["com/google/gson/JsonSyntaxException"]}, {"nme": "fromJson", "acc": 1, "dsc": "(Lcom/google/gson/JsonElement;Ljava/lang/reflect/Type;)Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Lcom/google/gson/JsonElement;Ljava/lang/reflect/Type;)TT;", "exs": ["com/google/gson/JsonSyntaxException"]}, {"nme": "fromJson", "acc": 1, "dsc": "(Lcom/google/gson/JsonElement;Lcom/google/gson/reflect/TypeToken;)Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Lcom/google/gson/JsonElement;Lcom/google/gson/reflect/TypeToken<TT;>;)TT;", "exs": ["com/google/gson/JsonSyntaxException"]}, {"nme": "assertFullConsumption", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lcom/google/gson/stream/JsonReader;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "DEFAULT_JSON_NON_EXECUTABLE", "dsc": "Z", "val": 0}, {"acc": 24, "nme": "DEFAULT_STRICTNESS", "dsc": "Lcom/google/gson/Strictness;"}, {"acc": 24, "nme": "DEFAULT_FORMATTING_STYLE", "dsc": "Lcom/google/gson/FormattingStyle;"}, {"acc": 24, "nme": "DEFAULT_ESCAPE_HTML", "dsc": "Z", "val": 1}, {"acc": 24, "nme": "DEFAULT_SERIALIZE_NULLS", "dsc": "Z", "val": 0}, {"acc": 24, "nme": "DEFAULT_COMPLEX_MAP_KEYS", "dsc": "Z", "val": 0}, {"acc": 24, "nme": "DEFAULT_SPECIALIZE_FLOAT_VALUES", "dsc": "Z", "val": 0}, {"acc": 24, "nme": "DEFAULT_USE_JDK_UNSAFE", "dsc": "Z", "val": 1}, {"acc": 24, "nme": "DEFAULT_DATE_PATTERN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 24, "nme": "DEFAULT_FIELD_NAMING_STRATEGY", "dsc": "Lcom/google/gson/FieldNamingStrategy;"}, {"acc": 24, "nme": "DEFAULT_OBJECT_TO_NUMBER_STRATEGY", "dsc": "Lcom/google/gson/ToNumberStrategy;"}, {"acc": 24, "nme": "DEFAULT_NUMBER_TO_NUMBER_STRATEGY", "dsc": "Lcom/google/gson/ToNumberStrategy;"}, {"acc": 26, "nme": "JSON_NON_EXECUTABLE_PREFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": ")]}'\n"}, {"acc": 18, "nme": "threadLocalAdapterResults", "dsc": "<PERSON><PERSON><PERSON>/lang/ThreadLocal;", "sig": "Ljava/lang/ThreadLocal<Ljava/util/Map<Lcom/google/gson/reflect/TypeToken<*>;Lcom/google/gson/TypeAdapter<*>;>;>;"}, {"acc": 18, "nme": "typeTokenCache", "dsc": "Ljava/util/concurrent/ConcurrentMap;", "sig": "Ljava/util/concurrent/ConcurrentMap<Lcom/google/gson/reflect/TypeToken<*>;Lcom/google/gson/TypeAdapter<*>;>;"}, {"acc": 18, "nme": "constructorConstructor", "dsc": "Lcom/google/gson/internal/ConstructorConstructor;"}, {"acc": 18, "nme": "jsonAdapterFactory", "dsc": "Lcom/google/gson/internal/bind/JsonAdapterAnnotationTypeAdapterFactory;"}, {"acc": 16, "nme": "factories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lcom/google/gson/TypeAdapterFactory;>;"}, {"acc": 16, "nme": "excluder", "dsc": "Lcom/google/gson/internal/Excluder;"}, {"acc": 16, "nme": "fieldNamingStrategy", "dsc": "Lcom/google/gson/FieldNamingStrategy;"}, {"acc": 16, "nme": "instanceCreators", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/reflect/Type;Lcom/google/gson/InstanceCreator<*>;>;"}, {"acc": 16, "nme": "serializeNulls", "dsc": "Z"}, {"acc": 16, "nme": "complexMapKeySerialization", "dsc": "Z"}, {"acc": 16, "nme": "generateNonExecutableJson", "dsc": "Z"}, {"acc": 16, "nme": "htmlSafe", "dsc": "Z"}, {"acc": 16, "nme": "formattingStyle", "dsc": "Lcom/google/gson/FormattingStyle;"}, {"acc": 16, "nme": "strictness", "dsc": "Lcom/google/gson/Strictness;"}, {"acc": 16, "nme": "serializeSpecialFloatingPointValues", "dsc": "Z"}, {"acc": 16, "nme": "useJdkUnsafe", "dsc": "Z"}, {"acc": 16, "nme": "datePattern", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 16, "nme": "dateStyle", "dsc": "I"}, {"acc": 16, "nme": "timeStyle", "dsc": "I"}, {"acc": 16, "nme": "longSerializationPolicy", "dsc": "Lcom/google/gson/LongSerializationPolicy;"}, {"acc": 16, "nme": "builderFactories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lcom/google/gson/TypeAdapterFactory;>;"}, {"acc": 16, "nme": "builderHierarchyFactories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lcom/google/gson/TypeAdapterFactory;>;"}, {"acc": 16, "nme": "objectToNumberStrategy", "dsc": "Lcom/google/gson/ToNumberStrategy;"}, {"acc": 16, "nme": "numberToNumberStrategy", "dsc": "Lcom/google/gson/ToNumberStrategy;"}, {"acc": 16, "nme": "reflectionFilters", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lcom/google/gson/ReflectionAccessFilter;>;"}]}, "com/google/gson/FieldNamingPolicy$3.class": {"ver": 51, "acc": 16432, "nme": "com/google/gson/FieldNamingPolicy$3", "super": "com/google/gson/FieldNamingPolicy", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"nme": "translateName", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)Ljava/lang/String;"}], "flds": []}, "com/google/gson/Gson$4.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/Gson$4", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/google/gson/TypeAdapter;)V"}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/util/concurrent/atomic/AtomicLong;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/util/concurrent/atomic/AtomicLong;", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 4112, "nme": "val$longAdapter", "dsc": "Lcom/google/gson/TypeAdapter;"}]}, "com/google/gson/internal/bind/TypeAdapters$11.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$11", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Number;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/lang/Number;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/internal/ConstructorConstructor.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/internal/ConstructorConstructor", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/util/Map;ZLjava/util/List;)V", "sig": "(Ljava/util/Map<Ljava/lang/reflect/Type;Lcom/google/gson/InstanceCreator<*>;>;ZLjava/util/List<Lcom/google/gson/ReflectionAccessFilter;>;)V"}, {"nme": "checkInstantiable", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/String;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Ljava/lang/String;"}, {"nme": "get", "acc": 1, "dsc": "(Lcom/google/gson/reflect/TypeToken;)Lcom/google/gson/internal/ObjectConstructor;", "sig": "<T:Ljava/lang/Object;>(Lcom/google/gson/reflect/TypeToken<TT;>;)Lcom/google/gson/internal/ObjectConstructor<TT;>;"}, {"nme": "newSpecialCollectionConstructor", "acc": 10, "dsc": "(Ljava/lang/reflect/Type;Ljava/lang/Class;)Lcom/google/gson/internal/ObjectConstructor;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/reflect/Type;Ljava/lang/Class<-TT;>;)Lcom/google/gson/internal/ObjectConstructor<TT;>;"}, {"nme": "newDefaultConstructor", "acc": 10, "dsc": "(Ljava/lang/Class;Lcom/google/gson/ReflectionAccessFilter$FilterResult;)Lcom/google/gson/internal/ObjectConstructor;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<-TT;>;Lcom/google/gson/ReflectionAccessFilter$FilterResult;)Lcom/google/gson/internal/ObjectConstructor<TT;>;"}, {"nme": "newDefaultImplementationConstructor", "acc": 10, "dsc": "(Ljava/lang/reflect/Type;Ljava/lang/Class;)Lcom/google/gson/internal/ObjectConstructor;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/reflect/Type;Ljava/lang/Class<-TT;>;)Lcom/google/gson/internal/ObjectConstructor<TT;>;"}, {"nme": "newUnsafeAllocator", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Lcom/google/gson/internal/ObjectConstructor;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<-TT;>;)Lcom/google/gson/internal/ObjectConstructor<TT;>;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "instanceCreators", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/reflect/Type;Lcom/google/gson/InstanceCreator<*>;>;"}, {"acc": 18, "nme": "useJdkUnsafe", "dsc": "Z"}, {"acc": 18, "nme": "reflectionFilters", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lcom/google/gson/ReflectionAccessFilter;>;"}]}, "com/google/gson/internal/Streams$AppendableWriter.class": {"ver": 51, "acc": 48, "nme": "com/google/gson/internal/Streams$AppendableWriter", "super": "java/io/Writer", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Appendable;)V"}, {"nme": "write", "acc": 1, "dsc": "([CII)V", "exs": ["java/io/IOException"]}, {"nme": "flush", "acc": 1, "dsc": "()V"}, {"nme": "close", "acc": 1, "dsc": "()V"}, {"nme": "write", "acc": 1, "dsc": "(I)V", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;II)V", "exs": ["java/io/IOException"]}, {"nme": "append", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;)<PERSON><PERSON><PERSON>/io/Writer;", "exs": ["java/io/IOException"]}, {"nme": "append", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;II)<PERSON>ja<PERSON>/io/Writer;", "exs": ["java/io/IOException"]}, {"nme": "append", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;II)Ljava/lang/Appendable;", "exs": ["java/io/IOException"]}, {"nme": "append", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;)Ljava/lang/Appendable;", "exs": ["java/io/IOException"]}], "flds": [{"acc": 18, "nme": "appendable", "dsc": "<PERSON><PERSON><PERSON>/lang/Appendable;"}, {"acc": 18, "nme": "currentWrite", "dsc": "Lcom/google/gson/internal/Streams$AppendableWriter$CurrentWrite;"}]}, "com/google/gson/internal/bind/CollectionTypeAdapterFactory$Adapter.class": {"ver": 51, "acc": 48, "nme": "com/google/gson/internal/bind/CollectionTypeAdapterFactory$Adapter", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/google/gson/Gson;<PERSON><PERSON><PERSON>/lang/reflect/Type;Lcom/google/gson/TypeAdapter;Lcom/google/gson/internal/ObjectConstructor;)V", "sig": "(Lcom/google/gson/Gson;Ljava/lang/reflect/Type;Lcom/google/gson/TypeAdapter<TE;>;Lcom/google/gson/internal/ObjectConstructor<+Ljava/util/Collection<TE;>;>;)V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/util/Collection;", "sig": "(Lcom/google/gson/stream/JsonReader;)Ljava/util/Collection<TE;>;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/util/Collection;)V", "sig": "(Lcom/google/gson/stream/JsonWriter;Ljava/util/Collection<TE;>;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 18, "nme": "elementTypeAdapter", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<TE;>;"}, {"acc": 18, "nme": "constructor", "dsc": "Lcom/google/gson/internal/ObjectConstructor;", "sig": "Lcom/google/gson/internal/ObjectConstructor<+Ljava/util/Collection<TE;>;>;"}]}, "com/google/gson/ReflectionAccessFilter$2.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/ReflectionAccessFilter$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "check", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Lcom/google/gson/ReflectionAccessFilter$FilterResult;", "sig": "(L<PERSON><PERSON>/lang/Class<*>;)Lcom/google/gson/ReflectionAccessFilter$FilterResult;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "com/google/gson/internal/bind/MapTypeAdapterFactory$Adapter.class": {"ver": 51, "acc": 48, "nme": "com/google/gson/internal/bind/MapTypeAdapterFactory$Adapter", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/google/gson/internal/bind/MapTypeAdapterFactory;Lcom/google/gson/Gson;Ljava/lang/reflect/Type;Lcom/google/gson/TypeAdapter;Ljava/lang/reflect/Type;Lcom/google/gson/TypeAdapter;Lcom/google/gson/internal/ObjectConstructor;)V", "sig": "(Lcom/google/gson/Gson;Ljava/lang/reflect/Type;Lcom/google/gson/TypeAdapter<TK;>;Ljava/lang/reflect/Type;Lcom/google/gson/TypeAdapter<TV;>;Lcom/google/gson/internal/ObjectConstructor<+Ljava/util/Map<TK;TV;>;>;)V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/util/Map;", "sig": "(Lcom/google/gson/stream/JsonReader;)Ljava/util/Map<TK;TV;>;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/util/Map;)V", "sig": "(Lcom/google/gson/stream/JsonWriter;Ljava/util/Map<TK;TV;>;)V", "exs": ["java/io/IOException"]}, {"nme": "keyToString", "acc": 2, "dsc": "(Lcom/google/gson/JsonElement;)Ljava/lang/String;"}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 18, "nme": "keyTypeAdapter", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<TK;>;"}, {"acc": 18, "nme": "valueTypeAdapter", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<TV;>;"}, {"acc": 18, "nme": "constructor", "dsc": "Lcom/google/gson/internal/ObjectConstructor;", "sig": "Lcom/google/gson/internal/ObjectConstructor<+Ljava/util/Map<TK;TV;>;>;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lcom/google/gson/internal/bind/MapTypeAdapterFactory;"}]}, "com/google/gson/internal/bind/NumberTypeAdapter.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/internal/bind/NumberTypeAdapter", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lcom/google/gson/ToNumberStrategy;)V"}, {"nme": "newFactory", "acc": 10, "dsc": "(Lcom/google/gson/ToNumberStrategy;)Lcom/google/gson/TypeAdapterFactory;"}, {"nme": "getFactory", "acc": 9, "dsc": "(Lcom/google/gson/ToNumberStrategy;)Lcom/google/gson/TypeAdapterFactory;"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Number;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/lang/Number;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "LAZILY_PARSED_NUMBER_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 18, "nme": "toNumberStrategy", "dsc": "Lcom/google/gson/ToNumberStrategy;"}]}, "com/google/gson/internal/ConstructorConstructor$13.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/ConstructorConstructor$13", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "construct", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}], "flds": []}, "com/google/gson/internal/TroubleshootingGuide.class": {"ver": 51, "acc": 33, "nme": "com/google/gson/internal/TroubleshootingGuide", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "createUrl", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": []}, "com/google/gson/internal/sql/SqlTimestampTypeAdapter.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/sql/SqlTimestampTypeAdapter", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lcom/google/gson/TypeAdapter;)V", "sig": "(Lcom/google/gson/TypeAdapter<Ljava/util/Date;>;)V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/sql/Timestamp;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/sql/Timestamp;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 4096, "dsc": "(Lcom/google/gson/TypeAdapter;Lcom/google/gson/internal/sql/SqlTimestampTypeAdapter$1;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 18, "nme": "dateTypeAdapter", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<Ljava/util/Date;>;"}]}, "com/google/gson/internal/bind/TypeAdapters$4.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$4", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Bo<PERSON>an;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON><PERSON><PERSON>/lang/<PERSON>;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/google/gson/internal/ConstructorConstructor;Lcom/google/gson/FieldNamingStrategy;Lcom/google/gson/internal/Excluder;Lcom/google/gson/internal/bind/JsonAdapterAnnotationTypeAdapterFactory;Ljava/util/List;)V", "sig": "(Lcom/google/gson/internal/ConstructorConstructor;Lcom/google/gson/FieldNamingStrategy;Lcom/google/gson/internal/Excluder;Lcom/google/gson/internal/bind/JsonAdapterAnnotationTypeAdapterFactory;Ljava/util/List<Lcom/google/gson/ReflectionAccessFilter;>;)V"}, {"nme": "includeField", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;Z)Z"}, {"nme": "getFieldNames", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)Ljava/util/List;", "sig": "(Lja<PERSON>/lang/reflect/Field;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "create", "acc": 1, "dsc": "(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken;)Lcom/google/gson/TypeAdapter;", "sig": "<T:Ljava/lang/Object;>(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken<TT;>;)Lcom/google/gson/TypeAdapter<TT;>;"}, {"nme": "checkAccessible", "acc": 10, "dsc": "(L<PERSON><PERSON>/lang/Object;Ljava/lang/reflect/AccessibleObject;)V", "sig": "<M:Ljava/lang/reflect/AccessibleObject;:Ljava/lang/reflect/Member;>(Ljava/lang/Object;TM;)V"}, {"nme": "createBoundField", "acc": 2, "dsc": "(Lcom/google/gson/Gson;Ljava/lang/reflect/Field;Ljava/lang/reflect/Method;Ljava/lang/String;Lcom/google/gson/reflect/TypeToken;ZZ)Lcom/google/gson/internal/bind/ReflectiveTypeAdapterFactory$BoundField;", "sig": "(Lcom/google/gson/Gson;Ljava/lang/reflect/Field;Ljava/lang/reflect/Method;Ljava/lang/String;Lcom/google/gson/reflect/TypeToken<*>;ZZ)Lcom/google/gson/internal/bind/ReflectiveTypeAdapterFactory$BoundField;"}, {"nme": "createDuplicateFieldException", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/reflect/Field;Lja<PERSON>/lang/reflect/Field;)Ljava/lang/IllegalArgumentException;", "sig": "(L<PERSON><PERSON>/lang/Class<*>;Ljava/lang/String;Ljava/lang/reflect/Field;Ljava/lang/reflect/Field;)Ljava/lang/IllegalArgumentException;"}, {"nme": "getBoundFields", "acc": 2, "dsc": "(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken;Ljava/lang/Class;ZZ)Lcom/google/gson/internal/bind/ReflectiveTypeAdapterFactory$FieldsData;", "sig": "(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken<*>;Ljava/lang/Class<*>;ZZ)Lcom/google/gson/internal/bind/ReflectiveTypeAdapterFactory$FieldsData;"}, {"nme": "access$000", "acc": 4104, "dsc": "(L<PERSON><PERSON>/lang/Object;Ljava/lang/reflect/AccessibleObject;)V"}], "flds": [{"acc": 18, "nme": "constructorConstructor", "dsc": "Lcom/google/gson/internal/ConstructorConstructor;"}, {"acc": 18, "nme": "fieldNamingPolicy", "dsc": "Lcom/google/gson/FieldNamingStrategy;"}, {"acc": 18, "nme": "excluder", "dsc": "Lcom/google/gson/internal/Excluder;"}, {"acc": 18, "nme": "jsonAdapterFactory", "dsc": "Lcom/google/gson/internal/bind/JsonAdapterAnnotationTypeAdapterFactory;"}, {"acc": 18, "nme": "reflectionFilters", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lcom/google/gson/ReflectionAccessFilter;>;"}]}, "com/google/gson/ToNumberPolicy$2.class": {"ver": 51, "acc": 16432, "nme": "com/google/gson/ToNumberPolicy$2", "super": "com/google/gson/ToNumberPolicy", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"nme": "readNumber", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Number;", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/internal/GsonBuildConfig.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/internal/GsonBuildConfig", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "VERSION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "2.11.0"}]}, "com/google/gson/internal/bind/JsonTreeReader$1.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/JsonTreeReader$1", "super": "java/io/Reader", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "([CII)I"}, {"nme": "close", "acc": 1, "dsc": "()V"}], "flds": []}, "com/google/gson/internal/bind/TreeTypeAdapter.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/internal/bind/TreeTypeAdapter", "super": "com/google/gson/internal/bind/SerializationDelegatingTypeAdapter", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/google/gson/JsonSerializer;Lcom/google/gson/JsonDeserializer;Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken;Lcom/google/gson/TypeAdapterFactory;Z)V", "sig": "(Lcom/google/gson/JsonSerializer<TT;>;Lcom/google/gson/JsonDeserializer<TT;>;Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken<TT;>;Lcom/google/gson/TypeAdapterFactory;Z)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lcom/google/gson/JsonSerializer;Lcom/google/gson/JsonDeserializer;Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken;Lcom/google/gson/TypeAdapterFactory;)V", "sig": "(Lcom/google/gson/JsonSerializer<TT;>;Lcom/google/gson/JsonDeserializer<TT;>;Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken<TT;>;Lcom/google/gson/TypeAdapterFactory;)V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "sig": "(Lcom/google/gson/stream/JsonReader;)TT;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "sig": "(Lcom/google/gson/stream/JsonWriter;TT;)V", "exs": ["java/io/IOException"]}, {"nme": "delegate", "acc": 2, "dsc": "()Lcom/google/gson/TypeAdapter;", "sig": "()Lcom/google/gson/TypeAdapter<TT;>;"}, {"nme": "getSerializationDelegate", "acc": 1, "dsc": "()Lcom/google/gson/TypeAdapter;", "sig": "()Lcom/google/gson/TypeAdapter<TT;>;"}, {"nme": "newFactory", "acc": 9, "dsc": "(Lcom/google/gson/reflect/TypeToken;Ljava/lang/Object;)Lcom/google/gson/TypeAdapterFactory;", "sig": "(Lcom/google/gson/reflect/TypeToken<*>;Ljava/lang/Object;)Lcom/google/gson/TypeAdapterFactory;"}, {"nme": "newFactoryWithMatchRawType", "acc": 9, "dsc": "(Lcom/google/gson/reflect/TypeToken;Ljava/lang/Object;)Lcom/google/gson/TypeAdapterFactory;", "sig": "(Lcom/google/gson/reflect/TypeToken<*>;Ljava/lang/Object;)Lcom/google/gson/TypeAdapterFactory;"}, {"nme": "newTypeHierarchyFactory", "acc": 9, "dsc": "(Lja<PERSON>/lang/Class;Ljava/lang/Object;)Lcom/google/gson/TypeAdapterFactory;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Object;)Lcom/google/gson/TypeAdapterFactory;"}], "flds": [{"acc": 18, "nme": "serializer", "dsc": "Lcom/google/gson/JsonSerializer;", "sig": "Lcom/google/gson/JsonSerializer<TT;>;"}, {"acc": 18, "nme": "deserializer", "dsc": "Lcom/google/gson/JsonDeserializer;", "sig": "Lcom/google/gson/JsonDeserializer<TT;>;"}, {"acc": 16, "nme": "gson", "dsc": "Lcom/google/gson/Gson;"}, {"acc": 18, "nme": "typeToken", "dsc": "Lcom/google/gson/reflect/TypeToken;", "sig": "Lcom/google/gson/reflect/TypeToken<TT;>;"}, {"acc": 18, "nme": "skipPastForGetDelegateAdapter", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 18, "nme": "context", "dsc": "Lcom/google/gson/internal/bind/TreeTypeAdapter$GsonContextImpl;", "sig": "Lcom/google/gson/internal/bind/TreeTypeAdapter<TT;>.GsonContextImpl;"}, {"acc": 18, "nme": "nullSafe", "dsc": "Z"}, {"acc": 66, "nme": "delegate", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<TT;>;"}]}, "com/google/gson/internal/bind/TypeAdapters$24.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$24", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/util/UUID;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/util/UUID;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/JsonArray.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/JsonArray", "super": "com/google/gson/JsonElement", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(I)V"}, {"nme": "deepCopy", "acc": 1, "dsc": "()Lcom/google/gson/JsonArray;"}, {"nme": "add", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/<PERSON>;)V"}, {"nme": "add", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Character;)V"}, {"nme": "add", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Number;)V"}, {"nme": "add", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "add", "acc": 1, "dsc": "(Lcom/google/gson/JsonElement;)V"}, {"nme": "addAll", "acc": 1, "dsc": "(Lcom/google/gson/Json<PERSON>y;)V"}, {"nme": "set", "acc": 1, "dsc": "(ILcom/google/gson/JsonElement;)Lcom/google/gson/JsonElement;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "remove", "acc": 1, "dsc": "(Lcom/google/gson/JsonElement;)Z", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "remove", "acc": 1, "dsc": "(I)Lcom/google/gson/JsonElement;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "contains", "acc": 1, "dsc": "(Lcom/google/gson/JsonElement;)Z"}, {"nme": "size", "acc": 1, "dsc": "()I"}, {"nme": "isEmpty", "acc": 1, "dsc": "()Z"}, {"nme": "iterator", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Iterator;", "sig": "()Ljava/util/Iterator<Lcom/google/gson/JsonElement;>;"}, {"nme": "get", "acc": 1, "dsc": "(I)Lcom/google/gson/JsonElement;"}, {"nme": "getAsSingleElement", "acc": 2, "dsc": "()Lcom/google/gson/JsonElement;"}, {"nme": "getAsNumber", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Number;"}, {"nme": "getAsString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getAsDouble", "acc": 1, "dsc": "()D"}, {"nme": "getAsBigDecimal", "acc": 1, "dsc": "()Ljava/math/BigDecimal;"}, {"nme": "getAsBigInteger", "acc": 1, "dsc": "()Ljava/math/BigInteger;"}, {"nme": "getAsFloat", "acc": 1, "dsc": "()F"}, {"nme": "getAsLong", "acc": 1, "dsc": "()J"}, {"nme": "getAsInt", "acc": 1, "dsc": "()I"}, {"nme": "getAsByte", "acc": 1, "dsc": "()B"}, {"nme": "getAsCharacter", "acc": 131073, "dsc": "()C", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getAsShort", "acc": 1, "dsc": "()S"}, {"nme": "getAsBoolean", "acc": 1, "dsc": "()Z"}, {"nme": "asList", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lcom/google/gson/JsonElement;>;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "deepCopy", "acc": 4161, "dsc": "()Lcom/google/gson/JsonElement;"}], "flds": [{"acc": 18, "nme": "elements", "dsc": "<PERSON><PERSON><PERSON>/util/ArrayList;", "sig": "Ljava/util/ArrayList<Lcom/google/gson/JsonElement;>;"}]}, "com/google/gson/internal/JsonReaderInternalAccess.class": {"ver": 51, "acc": 1057, "nme": "com/google/gson/internal/JsonReaderInternalAccess", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "promoteNameToValue", "acc": 1025, "dsc": "(Lcom/google/gson/stream/JsonReader;)V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 73, "nme": "INSTANCE", "dsc": "Lcom/google/gson/internal/JsonReaderInternalAccess;"}]}, "com/google/gson/ToNumberPolicy$1.class": {"ver": 51, "acc": 16432, "nme": "com/google/gson/ToNumberPolicy$1", "super": "com/google/gson/ToNumberPolicy", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"nme": "readNumber", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Double;", "exs": ["java/io/IOException"]}, {"nme": "readNumber", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Number;", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/FieldNamingPolicy.class": {"ver": 51, "acc": 17441, "nme": "com/google/gson/FieldNamingPolicy", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lcom/google/gson/FieldNamingPolicy;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/google/gson/FieldNamingPolicy;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "separateCamelCase", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;C)Ljava/lang/String;"}, {"nme": "upperCaseFirstLetter", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Ljava/lang/String;ILcom/google/gson/FieldNamingPolicy$1;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "IDENTITY", "dsc": "Lcom/google/gson/FieldNamingPolicy;"}, {"acc": 16409, "nme": "UPPER_CAMEL_CASE", "dsc": "Lcom/google/gson/FieldNamingPolicy;"}, {"acc": 16409, "nme": "UPPER_CAMEL_CASE_WITH_SPACES", "dsc": "Lcom/google/gson/FieldNamingPolicy;"}, {"acc": 16409, "nme": "UPPER_CASE_WITH_UNDERSCORES", "dsc": "Lcom/google/gson/FieldNamingPolicy;"}, {"acc": 16409, "nme": "LOWER_CASE_WITH_UNDERSCORES", "dsc": "Lcom/google/gson/FieldNamingPolicy;"}, {"acc": 16409, "nme": "LOWER_CASE_WITH_DASHES", "dsc": "Lcom/google/gson/FieldNamingPolicy;"}, {"acc": 16409, "nme": "LOWER_CASE_WITH_DOTS", "dsc": "Lcom/google/gson/FieldNamingPolicy;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lcom/google/gson/FieldNamingPolicy;"}]}, "com/google/gson/internal/reflect/ReflectionHelper.class": {"ver": 51, "acc": 33, "nme": "com/google/gson/internal/reflect/ReflectionHelper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getInaccessibleTroubleshootingSuffix", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Exception;)<PERSON>ja<PERSON>/lang/String;"}, {"nme": "makeAccessible", "acc": 9, "dsc": "(Ljava/lang/reflect/AccessibleObject;)V", "exs": ["com/google/gson/JsonIOException"]}, {"nme": "getAccessibleObjectDescription", "acc": 9, "dsc": "(Lja<PERSON>/lang/reflect/AccessibleObject;Z)Ljava/lang/String;"}, {"nme": "fieldToString", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)Ljava/lang/String;"}, {"nme": "constructorToString", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Constructor;)Ljava/lang/String;", "sig": "(<PERSON><PERSON><PERSON>/lang/reflect/Constructor<*>;)Ljava/lang/String;"}, {"nme": "appendExecutableParameters", "acc": 10, "dsc": "(Ljava/lang/reflect/AccessibleObject;Ljava/lang/StringBuilder;)V"}, {"nme": "isStatic", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "isAnonymousOrNonStaticLocal", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "tryMakeAccessible", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Constructor;)Ljava/lang/String;", "sig": "(<PERSON><PERSON><PERSON>/lang/reflect/Constructor<*>;)Ljava/lang/String;"}, {"nme": "isRecord", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "getRecordComponentNames", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)[Lja<PERSON>/lang/String;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)[Ljava/lang/String;"}, {"nme": "getAccessor", "acc": 9, "dsc": "(L<PERSON><PERSON>/lang/Class;Ljava/lang/reflect/Field;)Ljava/lang/reflect/Method;", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/reflect/Field;)Ljava/lang/reflect/Method;"}, {"nme": "getCanonicalRecordConstructor", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/reflect/Constructor;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;)Ljava/lang/reflect/Constructor<TT;>;"}, {"nme": "createExceptionForUnexpectedIllegalAccess", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/IllegalAccessException;)Ljava/lang/RuntimeException;"}, {"nme": "createExceptionForRecordReflectionException", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/ReflectiveOperationException;)Lja<PERSON>/lang/RuntimeException;"}, {"nme": "access$300", "acc": 4104, "dsc": "(<PERSON><PERSON><PERSON>/lang/ReflectiveOperationException;)Lja<PERSON>/lang/RuntimeException;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "RECORD_HELPER", "dsc": "Lcom/google/gson/internal/reflect/ReflectionHelper$RecordHelper;"}]}, "com/google/gson/reflect/package-info.class": {"ver": 51, "acc": 5632, "nme": "com/google/gson/reflect/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "com/google/gson/stream/JsonToken.class": {"ver": 51, "acc": 16433, "nme": "com/google/gson/stream/JsonToken", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lcom/google/gson/stream/JsonToken;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/google/gson/stream/JsonToken;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "BEGIN_ARRAY", "dsc": "Lcom/google/gson/stream/JsonToken;"}, {"acc": 16409, "nme": "END_ARRAY", "dsc": "Lcom/google/gson/stream/JsonToken;"}, {"acc": 16409, "nme": "BEGIN_OBJECT", "dsc": "Lcom/google/gson/stream/JsonToken;"}, {"acc": 16409, "nme": "END_OBJECT", "dsc": "Lcom/google/gson/stream/JsonToken;"}, {"acc": 16409, "nme": "NAME", "dsc": "Lcom/google/gson/stream/JsonToken;"}, {"acc": 16409, "nme": "STRING", "dsc": "Lcom/google/gson/stream/JsonToken;"}, {"acc": 16409, "nme": "NUMBER", "dsc": "Lcom/google/gson/stream/JsonToken;"}, {"acc": 16409, "nme": "BOOLEAN", "dsc": "Lcom/google/gson/stream/JsonToken;"}, {"acc": 16409, "nme": "NULL", "dsc": "Lcom/google/gson/stream/JsonToken;"}, {"acc": 16409, "nme": "END_DOCUMENT", "dsc": "Lcom/google/gson/stream/JsonToken;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lcom/google/gson/stream/JsonToken;"}]}, "com/google/gson/JsonStreamParser.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/JsonStreamParser", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;)V"}, {"nme": "next", "acc": 1, "dsc": "()Lcom/google/gson/JsonElement;", "exs": ["com/google/gson/JsonParseException"]}, {"nme": "hasNext", "acc": 1, "dsc": "()Z"}, {"nme": "remove", "acc": 1, "dsc": "()V"}, {"nme": "next", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 18, "nme": "parser", "dsc": "Lcom/google/gson/stream/JsonReader;"}, {"acc": 18, "nme": "lock", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "com/google/gson/internal/bind/TreeTypeAdapter$1.class": {"ver": 51, "acc": 4128, "nme": "com/google/gson/internal/bind/TreeTypeAdapter$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "com/google/gson/internal/bind/TypeAdapters$12.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$12", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Number;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/lang/Number;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/reflect/TypeToken.class": {"ver": 51, "acc": 33, "nme": "com/google/gson/reflect/TypeToken", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Type;)V"}, {"nme": "isCapturingTypeVariablesForbidden", "acc": 10, "dsc": "()Z"}, {"nme": "getTypeTokenTypeArgument", "acc": 2, "dsc": "()Ljava/lang/reflect/Type;"}, {"nme": "verifyNoTypeVariable", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Type;)V"}, {"nme": "getRawType", "acc": 17, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<-TT;>;"}, {"nme": "getType", "acc": 17, "dsc": "()Ljava/lang/reflect/Type;"}, {"nme": "isAssignableFrom", "acc": 131073, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "isAssignableFrom", "acc": 131073, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Type;)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "isAssignableFrom", "acc": 131073, "dsc": "(Lcom/google/gson/reflect/TypeToken;)Z", "sig": "(Lcom/google/gson/reflect/TypeToken<*>;)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "isAssignableFrom", "acc": 10, "dsc": "(Ljava/lang/reflect/Type;Ljava/lang/reflect/GenericArrayType;)Z"}, {"nme": "isAssignableFrom", "acc": 10, "dsc": "(Ljava/lang/reflect/Type;Ljava/lang/reflect/ParameterizedType;Ljava/util/Map;)Z", "sig": "(Ljava/lang/reflect/Type;Ljava/lang/reflect/ParameterizedType;Ljava/util/Map<Ljava/lang/String;Ljava/lang/reflect/Type;>;)Z"}, {"nme": "typeEquals", "acc": 10, "dsc": "(Ljava/lang/reflect/ParameterizedType;Ljava/lang/reflect/ParameterizedType;Ljava/util/Map;)Z", "sig": "(Ljava/lang/reflect/ParameterizedType;Ljava/lang/reflect/ParameterizedType;Ljava/util/Map<Ljava/lang/String;Ljava/lang/reflect/Type;>;)Z"}, {"nme": "buildUnsupportedTypeException", "acc": 138, "dsc": "(L<PERSON><PERSON>/lang/reflect/Type;[Ljava/lang/Class;)Ljava/lang/IllegalArgumentException;", "sig": "(L<PERSON><PERSON>/lang/reflect/Type;[Ljava/lang/Class<*>;)Ljava/lang/IllegalArgumentException;"}, {"nme": "matches", "acc": 10, "dsc": "(Lja<PERSON>/lang/reflect/Type;Ljava/lang/reflect/Type;Ljava/util/Map;)Z", "sig": "(Lja<PERSON>/lang/reflect/Type;Ljava/lang/reflect/Type;Ljava/util/Map<Ljava/lang/String;Ljava/lang/reflect/Type;>;)Z"}, {"nme": "hashCode", "acc": 17, "dsc": "()I"}, {"nme": "equals", "acc": 17, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "toString", "acc": 17, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "get", "acc": 9, "dsc": "(L<PERSON><PERSON>/lang/reflect/Type;)Lcom/google/gson/reflect/TypeToken;", "sig": "(<PERSON><PERSON><PERSON>/lang/reflect/Type;)Lcom/google/gson/reflect/TypeToken<*>;"}, {"nme": "get", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Lcom/google/gson/reflect/TypeToken;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;)Lcom/google/gson/reflect/TypeToken<TT;>;"}, {"nme": "getParameterized", "acc": 137, "dsc": "(Ljava/lang/reflect/Type;[Ljava/lang/reflect/Type;)Lcom/google/gson/reflect/TypeToken;", "sig": "(Ljava/lang/reflect/Type;[Ljava/lang/reflect/Type;)Lcom/google/gson/reflect/TypeToken<*>;"}, {"nme": "getArray", "acc": 9, "dsc": "(L<PERSON><PERSON>/lang/reflect/Type;)Lcom/google/gson/reflect/TypeToken;", "sig": "(<PERSON><PERSON><PERSON>/lang/reflect/Type;)Lcom/google/gson/reflect/TypeToken<*>;"}], "flds": [{"acc": 18, "nme": "rawType", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<-TT;>;"}, {"acc": 18, "nme": "type", "dsc": "Ljava/lang/reflect/Type;"}, {"acc": 18, "nme": "hashCode", "dsc": "I"}]}, "com/google/gson/internal/ConstructorConstructor$3.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/ConstructorConstructor$3", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/google/gson/internal/ConstructorConstructor;Ljava/lang/String;)V"}, {"nme": "construct", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}], "flds": [{"acc": 4112, "nme": "val$exceptionMessage", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lcom/google/gson/internal/ConstructorConstructor;"}]}, "com/google/gson/annotations/SerializedName.class": {"ver": 51, "acc": 9729, "nme": "com/google/gson/annotations/SerializedName", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "alternate", "acc": 1025, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "RUNTIME"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "FIELD"], ["L<PERSON>va/lang/annotation/ElementType;", "METHOD"]]]}]}, "com/google/gson/internal/ConstructorConstructor$12.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/ConstructorConstructor$12", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "construct", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}], "flds": []}, "com/google/gson/internal/LinkedTreeMap$EntrySet.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/LinkedTreeMap$EntrySet", "super": "java/util/AbstractSet", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/google/gson/internal/LinkedTreeMap;)V"}, {"nme": "size", "acc": 1, "dsc": "()I"}, {"nme": "iterator", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Iterator;", "sig": "()Ljava/util/Iterator<Ljava/util/Map$Entry<TK;TV;>;>;"}, {"nme": "contains", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "remove", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "clear", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lcom/google/gson/internal/LinkedTreeMap;"}]}, "com/google/gson/internal/$Gson$Types$WildcardTypeImpl.class": {"ver": 51, "acc": 48, "nme": "com/google/gson/internal/$Gson$Types$WildcardTypeImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "([<PERSON>ja<PERSON>/lang/reflect/Type;[Ljava/lang/reflect/Type;)V"}, {"nme": "getUpperBounds", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/reflect/Type;"}, {"nme": "getLowerBounds", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/reflect/Type;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "upperBound", "dsc": "Ljava/lang/reflect/Type;"}, {"acc": 18, "nme": "lowerBound", "dsc": "Ljava/lang/reflect/Type;"}, {"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 0}]}, "com/google/gson/JsonElement.class": {"ver": 51, "acc": 1057, "nme": "com/google/gson/JsonElement", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "deepCopy", "acc": 1025, "dsc": "()Lcom/google/gson/JsonElement;"}, {"nme": "isJsonArray", "acc": 1, "dsc": "()Z"}, {"nme": "isJsonObject", "acc": 1, "dsc": "()Z"}, {"nme": "isJsonPrimitive", "acc": 1, "dsc": "()Z"}, {"nme": "isJsonNull", "acc": 1, "dsc": "()Z"}, {"nme": "getAsJsonObject", "acc": 1, "dsc": "()Lcom/google/gson/JsonObject;"}, {"nme": "getAsJsonArray", "acc": 1, "dsc": "()Lcom/google/gson/JsonArray;"}, {"nme": "getAsJsonPrimitive", "acc": 1, "dsc": "()Lcom/google/gson/JsonPrimitive;"}, {"nme": "getAsJsonNull", "acc": 1, "dsc": "()Lcom/google/gson/JsonNull;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "getAsBoolean", "acc": 1, "dsc": "()Z"}, {"nme": "getAsNumber", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Number;"}, {"nme": "getAsString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getAsDouble", "acc": 1, "dsc": "()D"}, {"nme": "getAsFloat", "acc": 1, "dsc": "()F"}, {"nme": "getAsLong", "acc": 1, "dsc": "()J"}, {"nme": "getAsInt", "acc": 1, "dsc": "()I"}, {"nme": "getAsByte", "acc": 1, "dsc": "()B"}, {"nme": "getAsCharacter", "acc": 131073, "dsc": "()C", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getAsBigDecimal", "acc": 1, "dsc": "()Ljava/math/BigDecimal;"}, {"nme": "getAsBigInteger", "acc": 1, "dsc": "()Ljava/math/BigInteger;"}, {"nme": "getAsShort", "acc": 1, "dsc": "()S"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "com/google/gson/internal/bind/TypeAdapters$18.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$18", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Lcom/google/gson/internal/LazilyParsedNumber;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Lcom/google/gson/internal/LazilyParsedNumber;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/FieldNamingPolicy$2.class": {"ver": 51, "acc": 16432, "nme": "com/google/gson/FieldNamingPolicy$2", "super": "com/google/gson/FieldNamingPolicy", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"nme": "translateName", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)Ljava/lang/String;"}], "flds": []}, "com/google/gson/internal/bind/TypeAdapters$5.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$5", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Number;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/lang/Number;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/JsonSerializationContext.class": {"ver": 51, "acc": 1537, "nme": "com/google/gson/JsonSerializationContext", "super": "java/lang/Object", "mthds": [{"nme": "serialize", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lcom/google/gson/JsonElement;"}, {"nme": "serialize", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Ljava/lang/reflect/Type;)Lcom/google/gson/JsonElement;"}], "flds": []}, "com/google/gson/internal/bind/TypeAdapters$6.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$6", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Number;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/lang/Number;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/Gson$2.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/Gson$2", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/google/gson/Gson;)V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Float;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/lang/Number;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lcom/google/gson/Gson;"}]}, "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$FieldsData.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$FieldsData", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/util/Map;Ljava/util/List;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;Lcom/google/gson/internal/bind/ReflectiveTypeAdapterFactory$BoundField;>;Ljava/util/List<Lcom/google/gson/internal/bind/ReflectiveTypeAdapterFactory$BoundField;>;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "EMPTY", "dsc": "Lcom/google/gson/internal/bind/ReflectiveTypeAdapterFactory$FieldsData;"}, {"acc": 17, "nme": "deserializedFields", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lcom/google/gson/internal/bind/ReflectiveTypeAdapterFactory$BoundField;>;"}, {"acc": 17, "nme": "serializedFields", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lcom/google/gson/internal/bind/ReflectiveTypeAdapterFactory$BoundField;>;"}]}, "com/google/gson/internal/bind/util/ISO8601Utils.class": {"ver": 51, "acc": 33, "nme": "com/google/gson/internal/bind/util/ISO8601Utils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "format", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Date;)L<PERSON><PERSON>/lang/String;"}, {"nme": "format", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Date;Z)Ljava/lang/String;"}, {"nme": "format", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Date;Z<PERSON><PERSON><PERSON>/util/TimeZone;)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "parse", "acc": 9, "dsc": "(L<PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/text/ParsePosition;)Ljava/util/Date;", "exs": ["java/text/ParseException"]}, {"nme": "checkOffset", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;IC)Z"}, {"nme": "parseInt", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;II)I", "exs": ["java/lang/NumberFormatException"]}, {"nme": "padInt", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;II)V"}, {"nme": "indexOfNonDigit", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)I"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "UTC_ID", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "UTC"}, {"acc": 26, "nme": "TIMEZONE_UTC", "dsc": "Ljava/util/TimeZone;"}]}, "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$2.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$2", "super": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$BoundField", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/google/gson/internal/bind/ReflectiveTypeAdapterFactory;Ljava/lang/String;Ljava/lang/reflect/Field;ZLjava/lang/reflect/Method;Lcom/google/gson/TypeAdapter;Lcom/google/gson/TypeAdapter;ZZ)V"}, {"nme": "write", "acc": 0, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException", "java/lang/IllegalAccessException"]}, {"nme": "readIntoArray", "acc": 0, "dsc": "(Lcom/google/gson/stream/JsonReader;I[<PERSON><PERSON><PERSON>/lang/Object;)V", "exs": ["java/io/IOException", "com/google/gson/JsonParseException"]}, {"nme": "readIntoField", "acc": 0, "dsc": "(Lcom/google/gson/stream/JsonReader;<PERSON><PERSON>va/lang/Object;)V", "exs": ["java/io/IOException", "java/lang/IllegalAccessException"]}], "flds": [{"acc": 4112, "nme": "val$blockInaccessible", "dsc": "Z"}, {"acc": 4112, "nme": "val$accessor", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 4112, "nme": "val$writeTypeAdapter", "dsc": "Lcom/google/gson/TypeAdapter;"}, {"acc": 4112, "nme": "val$typeAdapter", "dsc": "Lcom/google/gson/TypeAdapter;"}, {"acc": 4112, "nme": "val$isPrimitive", "dsc": "Z"}, {"acc": 4112, "nme": "val$isStaticFinalField", "dsc": "Z"}, {"acc": 4112, "nme": "this$0", "dsc": "Lcom/google/gson/internal/bind/ReflectiveTypeAdapterFactory;"}]}, "com/google/gson/internal/bind/TypeAdapters$16.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$16", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/math/BigDecimal;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/math/BigDecimal;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/internal/LinkedTreeMap$KeySet$1.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/LinkedTreeMap$KeySet$1", "super": "com/google/gson/internal/LinkedTreeMap$LinkedTreeMapIterator", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/google/gson/internal/LinkedTreeMap$KeySet;)V"}, {"nme": "next", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TK;"}], "flds": [{"acc": 4112, "nme": "this$1", "dsc": "Lcom/google/gson/internal/LinkedTreeMap$KeySet;"}]}, "com/google/gson/internal/sql/SqlDateTypeAdapter.class": {"ver": 51, "acc": 48, "nme": "com/google/gson/internal/sql/SqlDateTypeAdapter", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/sql/Date;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/sql/Date;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 4096, "dsc": "(Lcom/google/gson/internal/sql/SqlDateTypeAdapter$1;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 18, "nme": "format", "dsc": "Ljava/text/DateFormat;"}]}, "com/google/gson/internal/LinkedTreeMap.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/internal/LinkedTreeMap", "super": "java/util/AbstractMap", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Z)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Comparator;Z)V", "sig": "(<PERSON><PERSON><PERSON>/util/Comparator<-TK;>;Z)V"}, {"nme": "size", "acc": 1, "dsc": "()I"}, {"nme": "get", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/Object;)TV;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "put", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(TK;TV;)TV;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "clear", "acc": 1, "dsc": "()V"}, {"nme": "remove", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/Object;)TV;"}, {"nme": "find", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Z)Lcom/google/gson/internal/LinkedTreeMap$Node;", "sig": "(TK;Z)Lcom/google/gson/internal/LinkedTreeMap$Node<TK;TV;>;"}, {"nme": "findByObject", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lcom/google/gson/internal/LinkedTreeMap$Node;", "sig": "(<PERSON>ja<PERSON>/lang/Object;)Lcom/google/gson/internal/LinkedTreeMap$Node<TK;TV;>;"}, {"nme": "findByEntry", "acc": 0, "dsc": "(Ljava/util/Map$Entry;)Lcom/google/gson/internal/LinkedTreeMap$Node;", "sig": "(Ljava/util/Map$Entry<**>;)Lcom/google/gson/internal/LinkedTreeMap$Node<TK;TV;>;"}, {"nme": "equal", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "removeInternal", "acc": 0, "dsc": "(Lcom/google/gson/internal/LinkedTreeMap$Node;Z)V", "sig": "(Lcom/google/gson/internal/LinkedTreeMap$Node<TK;TV;>;Z)V"}, {"nme": "removeInternalByKey", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lcom/google/gson/internal/LinkedTreeMap$Node;", "sig": "(<PERSON>ja<PERSON>/lang/Object;)Lcom/google/gson/internal/LinkedTreeMap$Node<TK;TV;>;"}, {"nme": "replaceInParent", "acc": 2, "dsc": "(Lcom/google/gson/internal/LinkedTreeMap$Node;Lcom/google/gson/internal/LinkedTreeMap$Node;)V", "sig": "(Lcom/google/gson/internal/LinkedTreeMap$Node<TK;TV;>;Lcom/google/gson/internal/LinkedTreeMap$Node<TK;TV;>;)V"}, {"nme": "rebalance", "acc": 2, "dsc": "(Lcom/google/gson/internal/LinkedTreeMap$Node;Z)V", "sig": "(Lcom/google/gson/internal/LinkedTreeMap$Node<TK;TV;>;Z)V"}, {"nme": "rotateLeft", "acc": 2, "dsc": "(Lcom/google/gson/internal/LinkedTreeMap$Node;)V", "sig": "(Lcom/google/gson/internal/LinkedTreeMap$Node<TK;TV;>;)V"}, {"nme": "rotateRight", "acc": 2, "dsc": "(Lcom/google/gson/internal/LinkedTreeMap$Node;)V", "sig": "(Lcom/google/gson/internal/LinkedTreeMap$Node<TK;TV;>;)V"}, {"nme": "entrySet", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljava/util/Map$Entry<TK;TV;>;>;"}, {"nme": "keySet", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<TK;>;"}, {"nme": "writeReplace", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/io/ObjectStreamException"]}, {"nme": "readObject", "acc": 2, "dsc": "(Ljava/io/ObjectInputStream;)V", "exs": ["java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "NATURAL_ORDER", "dsc": "<PERSON><PERSON><PERSON>/util/Comparator;", "sig": "<PERSON><PERSON><PERSON>/util/Comparator<Ljava/lang/Comparable;>;"}, {"acc": 18, "nme": "comparator", "dsc": "<PERSON><PERSON><PERSON>/util/Comparator;", "sig": "Ljava/util/Comparator<-TK;>;"}, {"acc": 18, "nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dsc": "Z"}, {"acc": 0, "nme": "root", "dsc": "Lcom/google/gson/internal/LinkedTreeMap$Node;", "sig": "Lcom/google/gson/internal/LinkedTreeMap$Node<TK;TV;>;"}, {"acc": 0, "nme": "size", "dsc": "I"}, {"acc": 0, "nme": "modCount", "dsc": "I"}, {"acc": 16, "nme": "header", "dsc": "Lcom/google/gson/internal/LinkedTreeMap$Node;", "sig": "Lcom/google/gson/internal/LinkedTreeMap$Node<TK;TV;>;"}, {"acc": 2, "nme": "entrySet", "dsc": "Lcom/google/gson/internal/LinkedTreeMap$EntrySet;", "sig": "Lcom/google/gson/internal/LinkedTreeMap<TK;TV;>.EntrySet;"}, {"acc": 2, "nme": "keySet", "dsc": "Lcom/google/gson/internal/LinkedTreeMap$KeySet;", "sig": "Lcom/google/gson/internal/LinkedTreeMap<TK;TV;>.KeySet;"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "com/google/gson/internal/ConstructorConstructor$19.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/ConstructorConstructor$19", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/google/gson/internal/ConstructorConstructor;Ljava/lang/Class;)V"}, {"nme": "construct", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}], "flds": [{"acc": 4112, "nme": "val$rawType", "dsc": "L<PERSON>va/lang/Class;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lcom/google/gson/internal/ConstructorConstructor;"}]}, "com/google/gson/internal/bind/TypeAdapters$9.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$9", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/util/concurrent/atomic/AtomicBoolean;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/util/concurrent/atomic/AtomicBoolean;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/internal/ConstructorConstructor$11.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/ConstructorConstructor$11", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "construct", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}], "flds": []}, "com/google/gson/internal/bind/ArrayTypeAdapter.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/internal/bind/ArrayTypeAdapter", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/google/gson/Gson;Lcom/google/gson/TypeAdapter;<PERSON><PERSON>va/lang/Class;)V", "sig": "(Lcom/google/gson/Gson;Lcom/google/gson/TypeAdapter<TE;>;Ljava/lang/Class<TE;>;)V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 18, "nme": "componentType", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<TE;>;"}, {"acc": 18, "nme": "componentTypeAdapter", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<TE;>;"}]}, "com/google/gson/internal/bind/TypeAdapters$34$1.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$34$1", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/google/gson/internal/bind/TypeAdapters$34;Ljava/lang/Class;)V"}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "sig": "(Lcom/google/gson/stream/JsonWriter;TT1;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "sig": "(Lcom/google/gson/stream/JsonReader;)TT1;", "exs": ["java/io/IOException"]}], "flds": [{"acc": 4112, "nme": "val$requestedType", "dsc": "L<PERSON>va/lang/Class;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lcom/google/gson/internal/bind/TypeAdapters$34;"}]}, "com/google/gson/internal/sql/SqlTimeTypeAdapter.class": {"ver": 51, "acc": 48, "nme": "com/google/gson/internal/sql/SqlTimeTypeAdapter", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/sql/Time;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/sql/Time;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 4096, "dsc": "(Lcom/google/gson/internal/sql/SqlTimeTypeAdapter$1;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 18, "nme": "format", "dsc": "Ljava/text/DateFormat;"}]}, "com/google/gson/FieldNamingPolicy$4.class": {"ver": 51, "acc": 16432, "nme": "com/google/gson/FieldNamingPolicy$4", "super": "com/google/gson/FieldNamingPolicy", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"nme": "translateName", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)Ljava/lang/String;"}], "flds": []}, "com/google/gson/internal/reflect/ReflectionHelper$1.class": {"ver": 51, "acc": 4128, "nme": "com/google/gson/internal/reflect/ReflectionHelper$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "com/google/gson/internal/ObjectConstructor.class": {"ver": 51, "acc": 1537, "nme": "com/google/gson/internal/ObjectConstructor", "super": "java/lang/Object", "mthds": [{"nme": "construct", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}], "flds": []}, "com/google/gson/internal/bind/NumberTypeAdapter$2.class": {"ver": 51, "acc": 4128, "nme": "com/google/gson/internal/bind/NumberTypeAdapter$2", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$com$google$gson$stream$JsonToken", "dsc": "[I"}]}, "com/google/gson/ToNumberPolicy$3.class": {"ver": 51, "acc": 16432, "nme": "com/google/gson/ToNumberPolicy$3", "super": "com/google/gson/ToNumberPolicy", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"nme": "readNumber", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Number;", "exs": ["java/io/IOException", "com/google/gson/JsonParseException"]}, {"nme": "parseAsDouble", "acc": 2, "dsc": "(Ljava/lang/String;Lcom/google/gson/stream/JsonReader;)Ljava/lang/Number;", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/internal/bind/TypeAdapters$13.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$13", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Number;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/lang/Number;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/TypeAdapterFactory.class": {"ver": 51, "acc": 1537, "nme": "com/google/gson/TypeAdapterFactory", "super": "java/lang/Object", "mthds": [{"nme": "create", "acc": 1025, "dsc": "(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken;)Lcom/google/gson/TypeAdapter;", "sig": "<T:Ljava/lang/Object;>(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken<TT;>;)Lcom/google/gson/TypeAdapter<TT;>;"}], "flds": []}, "com/google/gson/stream/JsonReader.class": {"ver": 51, "acc": 33, "nme": "com/google/gson/stream/JsonReader", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;)V"}, {"nme": "setLenient", "acc": 131089, "dsc": "(Z)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "isLenient", "acc": 17, "dsc": "()Z"}, {"nme": "setStrictness", "acc": 17, "dsc": "(Lcom/google/gson/Strictness;)V"}, {"nme": "getStrictness", "acc": 17, "dsc": "()Lcom/google/gson/Strictness;"}, {"nme": "beginArray", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "endArray", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "beginObject", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "endObject", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "hasNext", "acc": 1, "dsc": "()Z", "exs": ["java/io/IOException"]}, {"nme": "peek", "acc": 1, "dsc": "()Lcom/google/gson/stream/JsonToken;", "exs": ["java/io/IOException"]}, {"nme": "doPeek", "acc": 0, "dsc": "()I", "exs": ["java/io/IOException"]}, {"nme": "peek<PERSON>ey<PERSON>", "acc": 2, "dsc": "()I", "exs": ["java/io/IOException"]}, {"nme": "peekNumber", "acc": 2, "dsc": "()I", "exs": ["java/io/IOException"]}, {"nme": "isLiteral", "acc": 2, "dsc": "(C)Z", "exs": ["java/io/IOException"]}, {"nme": "nextName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "nextString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "nextBoolean", "acc": 1, "dsc": "()Z", "exs": ["java/io/IOException"]}, {"nme": "nextNull", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "nextDouble", "acc": 1, "dsc": "()D", "exs": ["java/io/IOException"]}, {"nme": "nextLong", "acc": 1, "dsc": "()J", "exs": ["java/io/IOException"]}, {"nme": "nextQuoted<PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(C)<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "nextUnquoted<PERSON><PERSON>ue", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "skipQuoted<PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(C)V", "exs": ["java/io/IOException"]}, {"nme": "skipUnquoted<PERSON><PERSON><PERSON>", "acc": 2, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "nextInt", "acc": 1, "dsc": "()I", "exs": ["java/io/IOException"]}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "skip<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "push", "acc": 2, "dsc": "(I)V"}, {"nme": "fillBuffer", "acc": 2, "dsc": "(I)Z", "exs": ["java/io/IOException"]}, {"nme": "nextNonWhitespace", "acc": 2, "dsc": "(Z)I", "exs": ["java/io/IOException"]}, {"nme": "checkLenient", "acc": 2, "dsc": "()V", "exs": ["com/google/gson/stream/MalformedJsonException"]}, {"nme": "skipToEndOfLine", "acc": 2, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "skip<PERSON>o", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z", "exs": ["java/io/IOException"]}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "locationString", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(Z)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "readEscapeCharacter", "acc": 2, "dsc": "()C", "exs": ["java/io/IOException"]}, {"nme": "syntaxError", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/google/gson/stream/MalformedJsonException;", "exs": ["com/google/gson/stream/MalformedJsonException"]}, {"nme": "unexpectedTokenError", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/IllegalStateException;", "exs": ["java/io/IOException"]}, {"nme": "consumeNonExecutePrefix", "acc": 2, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "access$000", "acc": 4104, "dsc": "(Lcom/google/gson/stream/JsonReader;Ljava/lang/String;)Ljava/lang/IllegalStateException;", "exs": ["java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "MIN_INCOMPLETE_INTEGER", "dsc": "J", "val": -922337203685477580}, {"acc": 26, "nme": "PEEKED_NONE", "dsc": "I", "val": 0}, {"acc": 26, "nme": "PEEKED_BEGIN_OBJECT", "dsc": "I", "val": 1}, {"acc": 26, "nme": "PEEKED_END_OBJECT", "dsc": "I", "val": 2}, {"acc": 26, "nme": "PEEKED_BEGIN_ARRAY", "dsc": "I", "val": 3}, {"acc": 26, "nme": "PEEKED_END_ARRAY", "dsc": "I", "val": 4}, {"acc": 26, "nme": "PEEKED_TRUE", "dsc": "I", "val": 5}, {"acc": 26, "nme": "PEEKED_FALSE", "dsc": "I", "val": 6}, {"acc": 26, "nme": "PEEKED_NULL", "dsc": "I", "val": 7}, {"acc": 26, "nme": "PEEKED_SINGLE_QUOTED", "dsc": "I", "val": 8}, {"acc": 26, "nme": "PEEKED_DOUBLE_QUOTED", "dsc": "I", "val": 9}, {"acc": 26, "nme": "PEEKED_UNQUOTED", "dsc": "I", "val": 10}, {"acc": 26, "nme": "PEEKED_BUFFERED", "dsc": "I", "val": 11}, {"acc": 26, "nme": "PEEKED_SINGLE_QUOTED_NAME", "dsc": "I", "val": 12}, {"acc": 26, "nme": "PEEKED_DOUBLE_QUOTED_NAME", "dsc": "I", "val": 13}, {"acc": 26, "nme": "PEEKED_UNQUOTED_NAME", "dsc": "I", "val": 14}, {"acc": 26, "nme": "PEEKED_LONG", "dsc": "I", "val": 15}, {"acc": 26, "nme": "PEEKED_NUMBER", "dsc": "I", "val": 16}, {"acc": 26, "nme": "PEEKED_EOF", "dsc": "I", "val": 17}, {"acc": 26, "nme": "NUMBER_CHAR_NONE", "dsc": "I", "val": 0}, {"acc": 26, "nme": "NUMBER_CHAR_SIGN", "dsc": "I", "val": 1}, {"acc": 26, "nme": "NUMBER_CHAR_DIGIT", "dsc": "I", "val": 2}, {"acc": 26, "nme": "NUMBER_CHAR_DECIMAL", "dsc": "I", "val": 3}, {"acc": 26, "nme": "NUMBER_CHAR_FRACTION_DIGIT", "dsc": "I", "val": 4}, {"acc": 26, "nme": "NUMBER_CHAR_EXP_E", "dsc": "I", "val": 5}, {"acc": 26, "nme": "NUMBER_CHAR_EXP_SIGN", "dsc": "I", "val": 6}, {"acc": 26, "nme": "NUMBER_CHAR_EXP_DIGIT", "dsc": "I", "val": 7}, {"acc": 18, "nme": "in", "dsc": "<PERSON><PERSON><PERSON>/io/Reader;"}, {"acc": 2, "nme": "strictness", "dsc": "Lcom/google/gson/Strictness;"}, {"acc": 24, "nme": "BUFFER_SIZE", "dsc": "I", "val": 1024}, {"acc": 18, "nme": "buffer", "dsc": "[C"}, {"acc": 2, "nme": "pos", "dsc": "I"}, {"acc": 2, "nme": "limit", "dsc": "I"}, {"acc": 2, "nme": "lineNumber", "dsc": "I"}, {"acc": 2, "nme": "lineStart", "dsc": "I"}, {"acc": 0, "nme": "peeked", "dsc": "I"}, {"acc": 2, "nme": "peekedLong", "dsc": "J"}, {"acc": 2, "nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dsc": "I"}, {"acc": 2, "nme": "peekedString", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "stack", "dsc": "[I"}, {"acc": 2, "nme": "stackSize", "dsc": "I"}, {"acc": 2, "nme": "pathNames", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "pathIndices", "dsc": "[I"}]}, "com/google/gson/FieldNamingPolicy$7.class": {"ver": 51, "acc": 16432, "nme": "com/google/gson/FieldNamingPolicy$7", "super": "com/google/gson/FieldNamingPolicy", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"nme": "translateName", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)Ljava/lang/String;"}], "flds": []}, "com/google/gson/internal/bind/ArrayTypeAdapter$1.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/ArrayTypeAdapter$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "create", "acc": 1, "dsc": "(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken;)Lcom/google/gson/TypeAdapter;", "sig": "<T:Ljava/lang/Object;>(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken<TT;>;)Lcom/google/gson/TypeAdapter<TT;>;"}], "flds": []}, "com/google/gson/internal/bind/TypeAdapters$21.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$21", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/net/URL;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/net/URL;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/internal/bind/TypeAdapters$8.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$8", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/util/concurrent/atomic/AtomicInteger;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/util/concurrent/atomic/AtomicInteger;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/internal/ConstructorConstructor$10.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/ConstructorConstructor$10", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "construct", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}], "flds": []}, "com/google/gson/internal/bind/DefaultDateTypeAdapter.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/internal/bind/DefaultDateTypeAdapter", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lcom/google/gson/internal/bind/DefaultDateTypeAdapter$DateType;Ljava/lang/String;)V", "sig": "(Lcom/google/gson/internal/bind/DefaultDateTypeAdapter$DateType<TT;>;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(Lcom/google/gson/internal/bind/DefaultDateTypeAdapter$DateType;II)V", "sig": "(Lcom/google/gson/internal/bind/DefaultDateTypeAdapter$DateType<TT;>;II)V"}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/util/Date;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/util/Date;", "sig": "(Lcom/google/gson/stream/JsonReader;)TT;", "exs": ["java/io/IOException"]}, {"nme": "deserializeToDate", "acc": 2, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/util/Date;", "exs": ["java/io/IOException"]}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 4096, "dsc": "(Lcom/google/gson/internal/bind/DefaultDateTypeAdapter$DateType;IILcom/google/gson/internal/bind/DefaultDateTypeAdapter$1;)V"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lcom/google/gson/internal/bind/DefaultDateTypeAdapter$DateType;Ljava/lang/String;Lcom/google/gson/internal/bind/DefaultDateTypeAdapter$1;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "SIMPLE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "DefaultDateTypeAdapter"}, {"acc": 25, "nme": "DEFAULT_STYLE_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 18, "nme": "dateType", "dsc": "Lcom/google/gson/internal/bind/DefaultDateTypeAdapter$DateType;", "sig": "Lcom/google/gson/internal/bind/DefaultDateTypeAdapter$DateType<TT;>;"}, {"acc": 18, "nme": "dateFormats", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/text/DateFormat;>;"}]}, "com/google/gson/FieldNamingPolicy$6.class": {"ver": 51, "acc": 16432, "nme": "com/google/gson/FieldNamingPolicy$6", "super": "com/google/gson/FieldNamingPolicy", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"nme": "translateName", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)Ljava/lang/String;"}], "flds": []}, "com/google/gson/internal/bind/JsonTreeWriter$1.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/JsonTreeWriter$1", "super": "java/io/Writer", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "write", "acc": 1, "dsc": "([CII)V"}, {"nme": "flush", "acc": 1, "dsc": "()V"}, {"nme": "close", "acc": 1, "dsc": "()V"}], "flds": []}, "com/google/gson/annotations/Until.class": {"ver": 51, "acc": 9729, "nme": "com/google/gson/annotations/Until", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()D"}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "RUNTIME"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "FIELD"], ["L<PERSON>va/lang/annotation/ElementType;", "TYPE"]]]}]}, "com/google/gson/ReflectionAccessFilter$FilterResult.class": {"ver": 51, "acc": 16433, "nme": "com/google/gson/ReflectionAccessFilter$FilterResult", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lcom/google/gson/ReflectionAccessFilter$FilterResult;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/google/gson/ReflectionAccessFilter$FilterResult;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "ALLOW", "dsc": "Lcom/google/gson/ReflectionAccessFilter$FilterResult;"}, {"acc": 16409, "nme": "INDECISIVE", "dsc": "Lcom/google/gson/ReflectionAccessFilter$FilterResult;"}, {"acc": 16409, "nme": "BLOCK_INACCESSIBLE", "dsc": "Lcom/google/gson/ReflectionAccessFilter$FilterResult;"}, {"acc": 16409, "nme": "BLOCK_ALL", "dsc": "Lcom/google/gson/ReflectionAccessFilter$FilterResult;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lcom/google/gson/ReflectionAccessFilter$FilterResult;"}]}, "com/google/gson/internal/ConstructorConstructor$1.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/ConstructorConstructor$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/google/gson/internal/ConstructorConstructor;Lcom/google/gson/InstanceCreator;Ljava/lang/reflect/Type;)V"}, {"nme": "construct", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}], "flds": [{"acc": 4112, "nme": "val$typeCreator", "dsc": "Lcom/google/gson/InstanceCreator;"}, {"acc": 4112, "nme": "val$type", "dsc": "Ljava/lang/reflect/Type;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lcom/google/gson/internal/ConstructorConstructor;"}]}, "com/google/gson/FormattingStyle.class": {"ver": 51, "acc": 33, "nme": "com/google/gson/FormattingStyle", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Z)V"}, {"nme": "withNewline", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/google/gson/FormattingStyle;"}, {"nme": "with<PERSON>ndent", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/google/gson/FormattingStyle;"}, {"nme": "withSpaceAfterSeparators", "acc": 1, "dsc": "(Z)Lcom/google/gson/FormattingStyle;"}, {"nme": "getNewline", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getIndent", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "usesSpaceAfterSeparators", "acc": 1, "dsc": "()Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "newline", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "indent", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "spaceAfterSeparators", "dsc": "Z"}, {"acc": 25, "nme": "COMPACT", "dsc": "Lcom/google/gson/FormattingStyle;"}, {"acc": 25, "nme": "PRETTY", "dsc": "Lcom/google/gson/FormattingStyle;"}]}, "com/google/gson/internal/bind/TypeAdapters$14.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$14", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Character;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>ja<PERSON>/lang/Character;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/stream/MalformedJsonException.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/stream/MalformedJsonException", "super": "java/io/IOException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}]}, "com/google/gson/internal/bind/TypeAdapterRuntimeTypeWrapper.class": {"ver": 51, "acc": 48, "nme": "com/google/gson/internal/bind/TypeAdapterRuntimeTypeWrapper", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/google/gson/Gson;Lcom/google/gson/TypeAdapter;<PERSON><PERSON><PERSON>/lang/reflect/Type;)V", "sig": "(Lcom/google/gson/Gson;Lcom/google/gson/TypeAdapter<TT;>;Ljava/lang/reflect/Type;)V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "sig": "(Lcom/google/gson/stream/JsonReader;)TT;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "sig": "(Lcom/google/gson/stream/JsonWriter;TT;)V", "exs": ["java/io/IOException"]}, {"nme": "isReflective", "acc": 10, "dsc": "(Lcom/google/gson/TypeAdapter;)Z", "sig": "(Lcom/google/gson/TypeAdapter<*>;)Z"}, {"nme": "getRuntimeTypeIfMoreSpecific", "acc": 10, "dsc": "(L<PERSON><PERSON>/lang/reflect/Type;Ljava/lang/Object;)Ljava/lang/reflect/Type;"}], "flds": [{"acc": 18, "nme": "context", "dsc": "Lcom/google/gson/Gson;"}, {"acc": 18, "nme": "delegate", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<TT;>;"}, {"acc": 18, "nme": "type", "dsc": "Ljava/lang/reflect/Type;"}]}, "com/google/gson/stream/JsonWriter.class": {"ver": 51, "acc": 33, "nme": "com/google/gson/stream/JsonWriter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Writer;)V"}, {"nme": "setIndent", "acc": 17, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setFormattingStyle", "acc": 17, "dsc": "(Lcom/google/gson/FormattingStyle;)V"}, {"nme": "getFormattingStyle", "acc": 17, "dsc": "()Lcom/google/gson/FormattingStyle;"}, {"nme": "setLenient", "acc": 131089, "dsc": "(Z)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "isLenient", "acc": 1, "dsc": "()Z"}, {"nme": "setStrictness", "acc": 17, "dsc": "(Lcom/google/gson/Strictness;)V"}, {"nme": "getStrictness", "acc": 17, "dsc": "()Lcom/google/gson/Strictness;"}, {"nme": "setHtmlSafe", "acc": 17, "dsc": "(Z)V"}, {"nme": "isHtmlSafe", "acc": 17, "dsc": "()Z"}, {"nme": "setSerializeNulls", "acc": 17, "dsc": "(Z)V"}, {"nme": "getSerializeNulls", "acc": 17, "dsc": "()Z"}, {"nme": "beginArray", "acc": 1, "dsc": "()Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "endArray", "acc": 1, "dsc": "()Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "beginObject", "acc": 1, "dsc": "()Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "endObject", "acc": 1, "dsc": "()Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "openScope", "acc": 2, "dsc": "(IC)Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "closeScope", "acc": 2, "dsc": "(IIC)Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "push", "acc": 2, "dsc": "(I)V"}, {"nme": "peek", "acc": 2, "dsc": "()I"}, {"nme": "replaceTop", "acc": 2, "dsc": "(I)V"}, {"nme": "name", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "writeDeferredName", "acc": 2, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "value", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "value", "acc": 1, "dsc": "(Z)Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "value", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Bo<PERSON>an;)Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "value", "acc": 1, "dsc": "(F)Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "value", "acc": 1, "dsc": "(D)Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "value", "acc": 1, "dsc": "(J)Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "value", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Number;)Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "nullValue", "acc": 1, "dsc": "()Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "jsonValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/google/gson/stream/JsonWriter;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "flush", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "isTrustedNumberType", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(Lja<PERSON>/lang/Class<+Ljava/lang/Number;>;)Z"}, {"nme": "string", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "newline", "acc": 2, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "beforeName", "acc": 2, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "beforeValue", "acc": 2, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "VALID_JSON_NUMBER_PATTERN", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 26, "nme": "REPLACEMENT_CHARS", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "HTML_SAFE_REPLACEMENT_CHARS", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "out", "dsc": "<PERSON><PERSON><PERSON>/io/Writer;"}, {"acc": 2, "nme": "stack", "dsc": "[I"}, {"acc": 2, "nme": "stackSize", "dsc": "I"}, {"acc": 2, "nme": "formattingStyle", "dsc": "Lcom/google/gson/FormattingStyle;"}, {"acc": 2, "nme": "formattedColon", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "formattedComma", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "usesEmptyNewlineAndIndent", "dsc": "Z"}, {"acc": 2, "nme": "strictness", "dsc": "Lcom/google/gson/Strictness;"}, {"acc": 2, "nme": "htmlSafe", "dsc": "Z"}, {"acc": 2, "nme": "deferred<PERSON><PERSON>", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "serializeNulls", "dsc": "Z"}]}, "com/google/gson/internal/bind/TypeAdapters$20.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$20", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/StringBuffer;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON><PERSON>va/lang/StringBuffer;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/internal/JavaVersion.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/internal/JavaVersion", "super": "java/lang/Object", "mthds": [{"nme": "determineMajorJavaVersion", "acc": 10, "dsc": "()I"}, {"nme": "parseMajorJavaVersion", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "parseDotted", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "extractBeginningInt", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "getMajorJavaVersion", "acc": 9, "dsc": "()I"}, {"nme": "isJava9OrLater", "acc": 9, "dsc": "()Z"}, {"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "majorJavaVersion", "dsc": "I"}]}, "META-INF/versions/9/module-info.class": {"ver": 53, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "com/google/gson/FieldNamingStrategy.class": {"ver": 51, "acc": 1537, "nme": "com/google/gson/FieldNamingStrategy", "super": "java/lang/Object", "mthds": [{"nme": "translateName", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)Ljava/lang/String;"}], "flds": []}, "com/google/gson/ToNumberStrategy.class": {"ver": 51, "acc": 1537, "nme": "com/google/gson/ToNumberStrategy", "super": "java/lang/Object", "mthds": [{"nme": "readNumber", "acc": 1025, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Number;", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/internal/NonNullElementWrapperList.class": {"ver": 51, "acc": 33, "nme": "com/google/gson/internal/NonNullElementWrapperList", "super": "java/util/AbstractList", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/ArrayList;)V", "sig": "(<PERSON><PERSON><PERSON>/util/ArrayList<TE;>;)V"}, {"nme": "get", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(I)TE;"}, {"nme": "size", "acc": 1, "dsc": "()I"}, {"nme": "nonNull", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(TE;)TE;"}, {"nme": "set", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(ITE;)TE;"}, {"nme": "add", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(ITE;)V"}, {"nme": "remove", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(I)TE;"}, {"nme": "clear", "acc": 1, "dsc": "()V"}, {"nme": "remove", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "removeAll", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Z", "sig": "(<PERSON><PERSON><PERSON>/util/Collection<*>;)Z"}, {"nme": "retainAll", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Z", "sig": "(<PERSON><PERSON><PERSON>/util/Collection<*>;)Z"}, {"nme": "contains", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "indexOf", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}, {"nme": "lastIndexOf", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}, {"nme": "toArray", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "toArray", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;)[<PERSON>ja<PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>([TT;)[TT;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "delegate", "dsc": "<PERSON><PERSON><PERSON>/util/ArrayList;", "sig": "Ljava/util/ArrayList<TE;>;"}]}, "com/google/gson/internal/bind/TypeAdapters$15.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$15", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/ReflectionAccessFilter$4.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/ReflectionAccessFilter$4", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "check", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Lcom/google/gson/ReflectionAccessFilter$FilterResult;", "sig": "(L<PERSON><PERSON>/lang/Class<*>;)Lcom/google/gson/ReflectionAccessFilter$FilterResult;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "com/google/gson/stream/package-info.class": {"ver": 51, "acc": 5632, "nme": "com/google/gson/stream/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "com/google/gson/internal/bind/MapTypeAdapterFactory.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/internal/bind/MapTypeAdapterFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/google/gson/internal/ConstructorConstructor;Z)V"}, {"nme": "create", "acc": 1, "dsc": "(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken;)Lcom/google/gson/TypeAdapter;", "sig": "<T:Ljava/lang/Object;>(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken<TT;>;)Lcom/google/gson/TypeAdapter<TT;>;"}, {"nme": "getKeyAdapter", "acc": 2, "dsc": "(Lcom/google/gson/Gson;<PERSON><PERSON><PERSON>/lang/reflect/Type;)Lcom/google/gson/TypeAdapter;", "sig": "(Lcom/google/gson/Gson;Lja<PERSON>/lang/reflect/Type;)Lcom/google/gson/TypeAdapter<*>;"}], "flds": [{"acc": 18, "nme": "constructorConstructor", "dsc": "Lcom/google/gson/internal/ConstructorConstructor;"}, {"acc": 16, "nme": "complexMapKeySerialization", "dsc": "Z"}]}, "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$FieldReflectionAdapter.class": {"ver": 51, "acc": 48, "nme": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$FieldReflectionAdapter", "super": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$Adapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/google/gson/internal/ObjectConstructor;Lcom/google/gson/internal/bind/ReflectiveTypeAdapterFactory$FieldsData;)V", "sig": "(Lcom/google/gson/internal/ObjectConstructor<TT;>;Lcom/google/gson/internal/bind/ReflectiveTypeAdapterFactory$FieldsData;)V"}, {"nme": "createAccumulator", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}, {"nme": "readField", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lcom/google/gson/stream/JsonReader;Lcom/google/gson/internal/bind/ReflectiveTypeAdapterFactory$BoundField;)V", "sig": "(TT;Lcom/google/gson/stream/JsonReader;Lcom/google/gson/internal/bind/ReflectiveTypeAdapterFactory$BoundField;)V", "exs": ["java/lang/IllegalAccessException", "java/io/IOException"]}, {"nme": "finalize", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(TT;)TT;"}], "flds": [{"acc": 18, "nme": "constructor", "dsc": "Lcom/google/gson/internal/ObjectConstructor;", "sig": "Lcom/google/gson/internal/ObjectConstructor<TT;>;"}]}, "com/google/gson/internal/ConstructorConstructor$15.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/ConstructorConstructor$15", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "construct", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}], "flds": []}, "com/google/gson/internal/bind/TreeTypeAdapter$GsonContextImpl.class": {"ver": 51, "acc": 48, "nme": "com/google/gson/internal/bind/TreeTypeAdapter$GsonContextImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lcom/google/gson/internal/bind/TreeTypeAdapter;)V"}, {"nme": "serialize", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lcom/google/gson/JsonElement;"}, {"nme": "serialize", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Ljava/lang/reflect/Type;)Lcom/google/gson/JsonElement;"}, {"nme": "deserialize", "acc": 1, "dsc": "(Lcom/google/gson/JsonElement;Ljava/lang/reflect/Type;)Ljava/lang/Object;", "sig": "<R:Ljava/lang/Object;>(Lcom/google/gson/JsonElement;Ljava/lang/reflect/Type;)TR;", "exs": ["com/google/gson/JsonParseException"]}, {"nme": "<init>", "acc": 4096, "dsc": "(Lcom/google/gson/internal/bind/TreeTypeAdapter;Lcom/google/gson/internal/bind/TreeTypeAdapter$1;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lcom/google/gson/internal/bind/TreeTypeAdapter;"}]}, "com/google/gson/internal/Excluder.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/internal/Excluder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 4, "dsc": "()Lcom/google/gson/internal/Excluder;"}, {"nme": "withVersion", "acc": 1, "dsc": "(D)Lcom/google/gson/internal/Excluder;"}, {"nme": "withModifiers", "acc": 129, "dsc": "([I)Lcom/google/gson/internal/Excluder;"}, {"nme": "disableInnerClassSerialization", "acc": 1, "dsc": "()Lcom/google/gson/internal/Excluder;"}, {"nme": "excludeFieldsWithoutExposeAnnotation", "acc": 1, "dsc": "()Lcom/google/gson/internal/Excluder;"}, {"nme": "withExclusionStrategy", "acc": 1, "dsc": "(Lcom/google/gson/ExclusionStrategy;ZZ)Lcom/google/gson/internal/Excluder;"}, {"nme": "create", "acc": 1, "dsc": "(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken;)Lcom/google/gson/TypeAdapter;", "sig": "<T:Ljava/lang/Object;>(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken<TT;>;)Lcom/google/gson/TypeAdapter<TT;>;"}, {"nme": "excludeField", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;Z)Z"}, {"nme": "excludeClass", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Z)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;Z)Z"}, {"nme": "isInnerClass", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "isValidVersion", "acc": 2, "dsc": "(Lcom/google/gson/annotations/Since;Lcom/google/gson/annotations/Until;)Z"}, {"nme": "isValidSince", "acc": 2, "dsc": "(Lcom/google/gson/annotations/Since;)Z"}, {"nme": "isValidUntil", "acc": 2, "dsc": "(Lcom/google/gson/annotations/Until;)Z"}, {"nme": "clone", "acc": 4164, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "IGNORE_VERSIONS", "dsc": "D", "val": -1.0}, {"acc": 25, "nme": "DEFAULT", "dsc": "Lcom/google/gson/internal/Excluder;"}, {"acc": 2, "nme": "version", "dsc": "D"}, {"acc": 2, "nme": "modifiers", "dsc": "I"}, {"acc": 2, "nme": "serializeInnerClasses", "dsc": "Z"}, {"acc": 2, "nme": "requireExpose", "dsc": "Z"}, {"acc": 2, "nme": "serializationStrategies", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lcom/google/gson/ExclusionStrategy;>;"}, {"acc": 2, "nme": "deserializationStrategies", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lcom/google/gson/ExclusionStrategy;>;"}]}, "com/google/gson/JsonDeserializationContext.class": {"ver": 51, "acc": 1537, "nme": "com/google/gson/JsonDeserializationContext", "super": "java/lang/Object", "mthds": [{"nme": "deserialize", "acc": 1025, "dsc": "(Lcom/google/gson/JsonElement;Ljava/lang/reflect/Type;)Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Lcom/google/gson/JsonElement;Ljava/lang/reflect/Type;)TT;", "exs": ["com/google/gson/JsonParseException"]}], "flds": []}, "com/google/gson/FieldNamingPolicy$5.class": {"ver": 51, "acc": 16432, "nme": "com/google/gson/FieldNamingPolicy$5", "super": "com/google/gson/FieldNamingPolicy", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"nme": "translateName", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)Ljava/lang/String;"}], "flds": []}, "com/google/gson/ReflectionAccessFilter.class": {"ver": 51, "acc": 1537, "nme": "com/google/gson/ReflectionAccessFilter", "super": "java/lang/Object", "mthds": [{"nme": "check", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Lcom/google/gson/ReflectionAccessFilter$FilterResult;", "sig": "(L<PERSON><PERSON>/lang/Class<*>;)Lcom/google/gson/ReflectionAccessFilter$FilterResult;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "BLOCK_INACCESSIBLE_JAVA", "dsc": "Lcom/google/gson/ReflectionAccessFilter;"}, {"acc": 25, "nme": "BLOCK_ALL_JAVA", "dsc": "Lcom/google/gson/ReflectionAccessFilter;"}, {"acc": 25, "nme": "BLOCK_ALL_ANDROID", "dsc": "Lcom/google/gson/ReflectionAccessFilter;"}, {"acc": 25, "nme": "BLOCK_ALL_PLATFORM", "dsc": "Lcom/google/gson/ReflectionAccessFilter;"}]}, "com/google/gson/internal/$Gson$Types.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/internal/$Gson$Types", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "newParameterizedTypeWithOwner", "acc": 137, "dsc": "(Ljava/lang/reflect/Type;Ljava/lang/reflect/Type;[Ljava/lang/reflect/Type;)Ljava/lang/reflect/ParameterizedType;"}, {"nme": "arrayOf", "acc": 9, "dsc": "(Ljava/lang/reflect/Type;)Ljava/lang/reflect/GenericArrayType;"}, {"nme": "subtypeOf", "acc": 9, "dsc": "(Ljava/lang/reflect/Type;)Ljava/lang/reflect/WildcardType;"}, {"nme": "supertypeOf", "acc": 9, "dsc": "(Ljava/lang/reflect/Type;)Ljava/lang/reflect/WildcardType;"}, {"nme": "canonicalize", "acc": 9, "dsc": "(Ljava/lang/reflect/Type;)Ljava/lang/reflect/Type;"}, {"nme": "getRawType", "acc": 9, "dsc": "(Lja<PERSON>/lang/reflect/Type;)Ljava/lang/Class;", "sig": "(Lja<PERSON>/lang/reflect/Type;)Ljava/lang/Class<*>;"}, {"nme": "equal", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "equals", "acc": 9, "dsc": "(Lja<PERSON>/lang/reflect/Type;Ljava/lang/reflect/Type;)Z"}, {"nme": "typeToString", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Type;)Ljava/lang/String;"}, {"nme": "getGenericSupertype", "acc": 10, "dsc": "(Lja<PERSON>/lang/reflect/Type;Ljava/lang/Class;Ljava/lang/Class;)Ljava/lang/reflect/Type;", "sig": "(Ljava/lang/reflect/Type;Ljava/lang/Class<*>;Ljava/lang/Class<*>;)Ljava/lang/reflect/Type;"}, {"nme": "getSupertype", "acc": 10, "dsc": "(Lja<PERSON>/lang/reflect/Type;Ljava/lang/Class;Ljava/lang/Class;)Ljava/lang/reflect/Type;", "sig": "(Ljava/lang/reflect/Type;Ljava/lang/Class<*>;Ljava/lang/Class<*>;)Ljava/lang/reflect/Type;"}, {"nme": "getArrayComponentType", "acc": 9, "dsc": "(Ljava/lang/reflect/Type;)Ljava/lang/reflect/Type;"}, {"nme": "getCollectionElementType", "acc": 9, "dsc": "(Lja<PERSON>/lang/reflect/Type;Ljava/lang/Class;)Ljava/lang/reflect/Type;", "sig": "(Ljava/lang/reflect/Type;Ljava/lang/Class<*>;)Ljava/lang/reflect/Type;"}, {"nme": "getMapKeyAndValueTypes", "acc": 9, "dsc": "(Lja<PERSON>/lang/reflect/Type;Ljava/lang/Class;)[Ljava/lang/reflect/Type;", "sig": "(Lja<PERSON>/lang/reflect/Type;Ljava/lang/Class<*>;)[Ljava/lang/reflect/Type;"}, {"nme": "resolve", "acc": 9, "dsc": "(Lja<PERSON>/lang/reflect/Type;Ljava/lang/Class;Ljava/lang/reflect/Type;)Ljava/lang/reflect/Type;", "sig": "(Lja<PERSON>/lang/reflect/Type;Ljava/lang/Class<*>;Ljava/lang/reflect/Type;)Ljava/lang/reflect/Type;"}, {"nme": "resolve", "acc": 10, "dsc": "(Lja<PERSON>/lang/reflect/Type;Ljava/lang/Class;Ljava/lang/reflect/Type;Ljava/util/Map;)Ljava/lang/reflect/Type;", "sig": "(Ljava/lang/reflect/Type;Ljava/lang/Class<*>;Ljava/lang/reflect/Type;Ljava/util/Map<Ljava/lang/reflect/TypeVariable<*>;Ljava/lang/reflect/Type;>;)Ljava/lang/reflect/Type;"}, {"nme": "resolveTypeVariable", "acc": 10, "dsc": "(Lja<PERSON>/lang/reflect/Type;Ljava/lang/Class;Ljava/lang/reflect/TypeVariable;)Ljava/lang/reflect/Type;", "sig": "(Ljava/lang/reflect/Type;Ljava/lang/Class<*>;Ljava/lang/reflect/TypeVariable<*>;)Ljava/lang/reflect/Type;"}, {"nme": "indexOf", "acc": 10, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)I"}, {"nme": "declaringClassOf", "acc": 10, "dsc": "(Ljava/lang/reflect/TypeVariable;)Ljava/lang/Class;", "sig": "(Lja<PERSON>/lang/reflect/TypeVariable<*>;)Ljava/lang/Class<*>;"}, {"nme": "checkNotPrimitive", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Type;)V"}, {"nme": "requiresOwnerType", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Type;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "EMPTY_TYPE_ARRAY", "dsc": "[Ljava/lang/reflect/Type;"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "com/google/gson/internal/LinkedTreeMap$KeySet.class": {"ver": 51, "acc": 48, "nme": "com/google/gson/internal/LinkedTreeMap$KeySet", "super": "java/util/AbstractSet", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/google/gson/internal/LinkedTreeMap;)V"}, {"nme": "size", "acc": 1, "dsc": "()I"}, {"nme": "iterator", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Iterator;", "sig": "()Ljava/util/Iterator<TK;>;"}, {"nme": "contains", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "remove", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "clear", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lcom/google/gson/internal/LinkedTreeMap;"}]}, "com/google/gson/FieldAttributes.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/FieldAttributes", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)V"}, {"nme": "getDeclaringClass", "acc": 1, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<*>;"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDeclaredType", "acc": 1, "dsc": "()Ljava/lang/reflect/Type;"}, {"nme": "getDeclaredClass", "acc": 1, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<*>;"}, {"nme": "getAnnotation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/annotation/Annotation;", "sig": "<T::Ljava/lang/annotation/Annotation;>(Ljava/lang/Class<TT;>;)TT;"}, {"nme": "getAnnotations", "acc": 1, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Ljava/lang/annotation/Annotation;>;"}, {"nme": "hasModifier", "acc": 1, "dsc": "(I)Z"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "field", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Field;"}]}, "com/google/gson/ToNumberPolicy$4.class": {"ver": 51, "acc": 16432, "nme": "com/google/gson/ToNumberPolicy$4", "super": "com/google/gson/ToNumberPolicy", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"nme": "readNumber", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/math/BigDecimal;", "exs": ["java/io/IOException"]}, {"nme": "readNumber", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Number;", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/internal/$Gson$Types$ParameterizedTypeImpl.class": {"ver": 51, "acc": 48, "nme": "com/google/gson/internal/$Gson$Types$ParameterizedTypeImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 129, "dsc": "(Lja<PERSON>/lang/reflect/Type;Ljava/lang/reflect/Type;[Ljava/lang/reflect/Type;)V"}, {"nme": "getActualTypeArguments", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/reflect/Type;"}, {"nme": "getRawType", "acc": 1, "dsc": "()Ljava/lang/reflect/Type;"}, {"nme": "getOwnerType", "acc": 1, "dsc": "()Ljava/lang/reflect/Type;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCodeOrZero", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "ownerType", "dsc": "Ljava/lang/reflect/Type;"}, {"acc": 18, "nme": "rawType", "dsc": "Ljava/lang/reflect/Type;"}, {"acc": 18, "nme": "typeArguments", "dsc": "[Ljava/lang/reflect/Type;"}, {"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 0}]}, "com/google/gson/internal/LinkedTreeMap$1.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/LinkedTreeMap$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "compare", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Comparable;<PERSON><PERSON><PERSON>/lang/Comparable;)I"}, {"nme": "compare", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)I"}], "flds": []}, "com/google/gson/LongSerializationPolicy$2.class": {"ver": 51, "acc": 16432, "nme": "com/google/gson/LongSerializationPolicy$2", "super": "com/google/gson/LongSerializationPolicy", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"nme": "serialize", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Long;)Lcom/google/gson/JsonElement;"}], "flds": []}, "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$RecordAdapter.class": {"ver": 51, "acc": 48, "nme": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$RecordAdapter", "super": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$Adapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/lang/Class;Lcom/google/gson/internal/bind/ReflectiveTypeAdapterFactory$FieldsData;Z)V", "sig": "(Ljava/lang/Class<TT;>;Lcom/google/gson/internal/bind/ReflectiveTypeAdapterFactory$FieldsData;Z)V"}, {"nme": "primitiveDefaults", "acc": 10, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/Class<*>;Ljava/lang/Object;>;"}, {"nme": "createAccumulator", "acc": 0, "dsc": "()[<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "readField", "acc": 0, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;Lcom/google/gson/stream/JsonReader;Lcom/google/gson/internal/bind/ReflectiveTypeAdapterFactory$BoundField;)V", "exs": ["java/io/IOException"]}, {"nme": "finalize", "acc": 0, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "([<PERSON><PERSON><PERSON>/lang/Object;)TT;"}, {"nme": "finalize", "acc": 4160, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "readField", "acc": 4160, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lcom/google/gson/stream/JsonReader;Lcom/google/gson/internal/bind/ReflectiveTypeAdapterFactory$BoundField;)V", "exs": ["java/lang/IllegalAccessException", "java/io/IOException"]}, {"nme": "createAccumulator", "acc": 4160, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "PRIMITIVE_DEFAULTS", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Class<*>;Ljava/lang/Object;>;"}, {"acc": 18, "nme": "constructor", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Constructor;", "sig": "<PERSON>ja<PERSON>/lang/reflect/Constructor<TT;>;"}, {"acc": 18, "nme": "constructorArgsDefaults", "dsc": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 18, "nme": "componentIndices", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Integer;>;"}]}, "com/google/gson/internal/bind/JsonTreeReader.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/internal/bind/JsonTreeReader", "super": "com/google/gson/stream/JsonReader", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/google/gson/JsonElement;)V"}, {"nme": "beginArray", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "endArray", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "beginObject", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "endObject", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "hasNext", "acc": 1, "dsc": "()Z", "exs": ["java/io/IOException"]}, {"nme": "peek", "acc": 1, "dsc": "()Lcom/google/gson/stream/JsonToken;", "exs": ["java/io/IOException"]}, {"nme": "peekStack", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "popStack", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "expect", "acc": 2, "dsc": "(Lcom/google/gson/stream/JsonToken;)V", "exs": ["java/io/IOException"]}, {"nme": "nextName", "acc": 2, "dsc": "(Z)<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "nextName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "nextString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "nextBoolean", "acc": 1, "dsc": "()Z", "exs": ["java/io/IOException"]}, {"nme": "nextNull", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "nextDouble", "acc": 1, "dsc": "()D", "exs": ["java/io/IOException"]}, {"nme": "nextLong", "acc": 1, "dsc": "()J", "exs": ["java/io/IOException"]}, {"nme": "nextInt", "acc": 1, "dsc": "()I", "exs": ["java/io/IOException"]}, {"nme": "nextJsonElement", "acc": 0, "dsc": "()Lcom/google/gson/JsonElement;", "exs": ["java/io/IOException"]}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "skip<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "promoteNameToValue", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "push", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(Z)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "locationString", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "UNREADABLE_READER", "dsc": "<PERSON><PERSON><PERSON>/io/Reader;"}, {"acc": 26, "nme": "SENTINEL_CLOSED", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 2, "nme": "stack", "dsc": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 2, "nme": "stackSize", "dsc": "I"}, {"acc": 2, "nme": "pathNames", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "pathIndices", "dsc": "[I"}]}, "com/google/gson/internal/bind/SerializationDelegatingTypeAdapter.class": {"ver": 51, "acc": 1057, "nme": "com/google/gson/internal/bind/SerializationDelegatingTypeAdapter", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getSerializationDelegate", "acc": 1025, "dsc": "()Lcom/google/gson/TypeAdapter;", "sig": "()Lcom/google/gson/TypeAdapter<TT;>;"}], "flds": []}, "com/google/gson/stream/JsonScope.class": {"ver": 51, "acc": 48, "nme": "com/google/gson/stream/JsonScope", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "EMPTY_ARRAY", "dsc": "I", "val": 1}, {"acc": 24, "nme": "NONEMPTY_ARRAY", "dsc": "I", "val": 2}, {"acc": 24, "nme": "EMPTY_OBJECT", "dsc": "I", "val": 3}, {"acc": 24, "nme": "DANGLING_NAME", "dsc": "I", "val": 4}, {"acc": 24, "nme": "NONEMPTY_OBJECT", "dsc": "I", "val": 5}, {"acc": 24, "nme": "EMPTY_DOCUMENT", "dsc": "I", "val": 6}, {"acc": 24, "nme": "NONEMPTY_DOCUMENT", "dsc": "I", "val": 7}, {"acc": 24, "nme": "CLOSED", "dsc": "I", "val": 8}]}, "com/google/gson/GsonBuilder.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/GsonBuilder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 0, "dsc": "(Lcom/google/gson/Gson;)V"}, {"nme": "setVersion", "acc": 1, "dsc": "(D)Lcom/google/gson/GsonBuilder;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "excludeFieldsWithModifiers", "acc": 129, "dsc": "([I)Lcom/google/gson/GsonBuilder;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "generateNonExecutableJson", "acc": 1, "dsc": "()Lcom/google/gson/GsonBuilder;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "excludeFieldsWithoutExposeAnnotation", "acc": 1, "dsc": "()Lcom/google/gson/GsonBuilder;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "serializeNulls", "acc": 1, "dsc": "()Lcom/google/gson/GsonBuilder;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "enableComplexMapKeySerialization", "acc": 1, "dsc": "()Lcom/google/gson/GsonBuilder;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "disableInnerClassSerialization", "acc": 1, "dsc": "()Lcom/google/gson/GsonBuilder;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "setLongSerializationPolicy", "acc": 1, "dsc": "(Lcom/google/gson/LongSerializationPolicy;)Lcom/google/gson/GsonBuilder;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "setFieldNamingPolicy", "acc": 1, "dsc": "(Lcom/google/gson/FieldNamingPolicy;)Lcom/google/gson/GsonBuilder;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "setFieldNamingStrategy", "acc": 1, "dsc": "(Lcom/google/gson/FieldNamingStrategy;)Lcom/google/gson/GsonBuilder;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "setObjectToNumberStrategy", "acc": 1, "dsc": "(Lcom/google/gson/ToNumberStrategy;)Lcom/google/gson/GsonBuilder;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "setNumberToNumberStrategy", "acc": 1, "dsc": "(Lcom/google/gson/ToNumberStrategy;)Lcom/google/gson/GsonBuilder;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "setExclusionStrategies", "acc": 129, "dsc": "([Lcom/google/gson/ExclusionStrategy;)Lcom/google/gson/GsonBuilder;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "addSerializationExclusionStrategy", "acc": 1, "dsc": "(Lcom/google/gson/ExclusionStrategy;)Lcom/google/gson/GsonBuilder;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "addDeserializationExclusionStrategy", "acc": 1, "dsc": "(Lcom/google/gson/ExclusionStrategy;)Lcom/google/gson/GsonBuilder;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "setPrettyPrinting", "acc": 1, "dsc": "()Lcom/google/gson/GsonBuilder;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "setFormattingStyle", "acc": 1, "dsc": "(Lcom/google/gson/FormattingStyle;)Lcom/google/gson/GsonBuilder;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "setLenient", "acc": 131073, "dsc": "()Lcom/google/gson/GsonBuilder;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/InlineMe;", "vals": ["replacement", "this.setStrictness(Strictness.LENIENT)", "imports", ["com.google.gson.Strictness"]]}, {"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "setStrictness", "acc": 1, "dsc": "(Lcom/google/gson/Strictness;)Lcom/google/gson/GsonBuilder;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "disableHtmlEscaping", "acc": 1, "dsc": "()Lcom/google/gson/GsonBuilder;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "setDateFormat", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/google/gson/GsonBuilder;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "setDateFormat", "acc": 131073, "dsc": "(I)Lcom/google/gson/GsonBuilder;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "setDateFormat", "acc": 1, "dsc": "(II)Lcom/google/gson/GsonBuilder;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "checkDateFormatStyle", "acc": 10, "dsc": "(I)I"}, {"nme": "registerTypeAdapter", "acc": 1, "dsc": "(Lja<PERSON>/lang/reflect/Type;Ljava/lang/Object;)Lcom/google/gson/GsonBuilder;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "isTypeObjectOrJsonElement", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Type;)Z"}, {"nme": "registerTypeAdapterFactory", "acc": 1, "dsc": "(Lcom/google/gson/TypeAdapterFactory;)Lcom/google/gson/GsonBuilder;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "registerTypeHierarchyAdapter", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/Object;)Lcom/google/gson/GsonBuilder;", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/Object;)Lcom/google/gson/GsonBuilder;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "serializeSpecialFloatingPointValues", "acc": 1, "dsc": "()Lcom/google/gson/GsonBuilder;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "disableJdkUnsafe", "acc": 1, "dsc": "()Lcom/google/gson/GsonBuilder;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "addReflectionAccessFilter", "acc": 1, "dsc": "(Lcom/google/gson/ReflectionAccessFilter;)Lcom/google/gson/GsonBuilder;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "create", "acc": 1, "dsc": "()Lcom/google/gson/Gson;"}, {"nme": "addTypeAdaptersForDate", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;II<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/lang/String;IILjava/util/List<Lcom/google/gson/TypeAdapterFactory;>;)V"}], "flds": [{"acc": 2, "nme": "excluder", "dsc": "Lcom/google/gson/internal/Excluder;"}, {"acc": 2, "nme": "longSerializationPolicy", "dsc": "Lcom/google/gson/LongSerializationPolicy;"}, {"acc": 2, "nme": "fieldNamingPolicy", "dsc": "Lcom/google/gson/FieldNamingStrategy;"}, {"acc": 18, "nme": "instanceCreators", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/reflect/Type;Lcom/google/gson/InstanceCreator<*>;>;"}, {"acc": 18, "nme": "factories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lcom/google/gson/TypeAdapterFactory;>;"}, {"acc": 18, "nme": "hierarchyFactories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lcom/google/gson/TypeAdapterFactory;>;"}, {"acc": 2, "nme": "serializeNulls", "dsc": "Z"}, {"acc": 2, "nme": "datePattern", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "dateStyle", "dsc": "I"}, {"acc": 2, "nme": "timeStyle", "dsc": "I"}, {"acc": 2, "nme": "complexMapKeySerialization", "dsc": "Z"}, {"acc": 2, "nme": "serializeSpecialFloatingPointValues", "dsc": "Z"}, {"acc": 2, "nme": "escapeHtmlChars", "dsc": "Z"}, {"acc": 2, "nme": "formattingStyle", "dsc": "Lcom/google/gson/FormattingStyle;"}, {"acc": 2, "nme": "generateNonExecutableJson", "dsc": "Z"}, {"acc": 2, "nme": "strictness", "dsc": "Lcom/google/gson/Strictness;"}, {"acc": 2, "nme": "useJdkUnsafe", "dsc": "Z"}, {"acc": 2, "nme": "objectToNumberStrategy", "dsc": "Lcom/google/gson/ToNumberStrategy;"}, {"acc": 2, "nme": "numberToNumberStrategy", "dsc": "Lcom/google/gson/ToNumberStrategy;"}, {"acc": 18, "nme": "reflectionFilters", "dsc": "Ljava/util/<PERSON>;", "sig": "Ljava/util/ArrayDeque<Lcom/google/gson/ReflectionAccessFilter;>;"}]}, "com/google/gson/internal/LazilyParsedNumber.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/internal/LazilyParsedNumber", "super": "java/lang/Number", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "asBigDecimal", "acc": 2, "dsc": "()Ljava/math/BigDecimal;"}, {"nme": "intValue", "acc": 1, "dsc": "()I"}, {"nme": "longValue", "acc": 1, "dsc": "()J"}, {"nme": "floatValue", "acc": 1, "dsc": "()F"}, {"nme": "doubleValue", "acc": 1, "dsc": "()D"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "writeReplace", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/io/ObjectStreamException"]}, {"nme": "readObject", "acc": 2, "dsc": "(Ljava/io/ObjectInputStream;)V", "exs": ["java/io/IOException"]}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "flds": [{"acc": 18, "nme": "value", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "com/google/gson/internal/bind/TypeAdapters$7.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$7", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Number;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/lang/Number;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/internal/NumberLimits.class": {"ver": 51, "acc": 33, "nme": "com/google/gson/internal/NumberLimits", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "checkNumberStringLength", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "parseBigDecimal", "acc": 9, "dsc": "(<PERSON>ja<PERSON>/lang/String;)Ljava/math/BigDecimal;", "exs": ["java/lang/NumberFormatException"]}, {"nme": "parseBigInteger", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/math/BigInteger;", "exs": ["java/lang/NumberFormatException"]}], "flds": [{"acc": 26, "nme": "MAX_NUMBER_STRING_LENGTH", "dsc": "I", "val": 10000}]}, "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$1.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$1", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/google/gson/internal/bind/ReflectiveTypeAdapterFactory;)V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "sig": "(Lcom/google/gson/stream/JsonReader;)TT;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "sig": "(Lcom/google/gson/stream/JsonWriter;TT;)V", "exs": ["java/io/IOException"]}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lcom/google/gson/internal/bind/ReflectiveTypeAdapterFactory;"}]}, "com/google/gson/stream/JsonReader$1.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/stream/JsonReader$1", "super": "com/google/gson/internal/JsonReaderInternalAccess", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "promoteNameToValue", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/internal/bind/TypeAdapters$22.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$22", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/net/URI;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/net/URI;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/internal/bind/DefaultDateTypeAdapter$DateType$1.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/DefaultDateTypeAdapter$DateType$1", "super": "com/google/gson/internal/bind/DefaultDateTypeAdapter$DateType", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V"}, {"nme": "deserialize", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/util/Date;)<PERSON><PERSON><PERSON>/util/Date;"}], "flds": []}, "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$BoundField.class": {"ver": 51, "acc": 1056, "nme": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$BoundField", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/reflect/Field;)V"}, {"nme": "write", "acc": 1024, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException", "java/lang/IllegalAccessException"]}, {"nme": "readIntoArray", "acc": 1024, "dsc": "(Lcom/google/gson/stream/JsonReader;I[<PERSON><PERSON><PERSON>/lang/Object;)V", "exs": ["java/io/IOException", "com/google/gson/JsonParseException"]}, {"nme": "readIntoField", "acc": 1024, "dsc": "(Lcom/google/gson/stream/JsonReader;<PERSON><PERSON>va/lang/Object;)V", "exs": ["java/io/IOException", "java/lang/IllegalAccessException"]}], "flds": [{"acc": 16, "nme": "serializedName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 16, "nme": "field", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Field;"}, {"acc": 16, "nme": "fieldName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "com/google/gson/internal/PreJava9DateFormatProvider.class": {"ver": 51, "acc": 33, "nme": "com/google/gson/internal/PreJava9DateFormatProvider", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getUsDateTimeFormat", "acc": 9, "dsc": "(II)Ljava/text/DateFormat;"}, {"nme": "getDatePartOfDateTimePattern", "acc": 10, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getTimePartOfDateTimePattern", "acc": 10, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "com/google/gson/internal/bind/TypeAdapters$42.class": {"ver": 51, "acc": 4128, "nme": "com/google/gson/internal/bind/TypeAdapters$42", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$com$google$gson$stream$JsonToken", "dsc": "[I"}]}, "com/google/gson/Gson$1.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/Gson$1", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/google/gson/Gson;)V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Double;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/lang/Number;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lcom/google/gson/Gson;"}]}, "com/google/gson/annotations/JsonAdapter.class": {"ver": 51, "acc": 9729, "nme": "com/google/gson/annotations/JsonAdapter", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<*>;"}, {"nme": "nullSafe", "acc": 1025, "dsc": "()Z"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "RUNTIME"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "FIELD"]]]}]}, "com/google/gson/internal/bind/TypeAdapters$28.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$28", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "tryBeginNesting", "acc": 2, "dsc": "(Lcom/google/gson/stream/JsonReader;Lcom/google/gson/stream/JsonToken;)Lcom/google/gson/JsonElement;", "exs": ["java/io/IOException"]}, {"nme": "readTerminal", "acc": 2, "dsc": "(Lcom/google/gson/stream/JsonReader;Lcom/google/gson/stream/JsonToken;)Lcom/google/gson/JsonElement;", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Lcom/google/gson/JsonElement;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Lcom/google/gson/JsonElement;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/annotations/package-info.class": {"ver": 51, "acc": 5632, "nme": "com/google/gson/annotations/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "com/google/gson/internal/ConstructorConstructor$5.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/ConstructorConstructor$5", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Type;)V"}, {"nme": "construct", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}], "flds": [{"acc": 4112, "nme": "val$type", "dsc": "Ljava/lang/reflect/Type;"}]}, "com/google/gson/internal/Streams$1.class": {"ver": 51, "acc": 4128, "nme": "com/google/gson/internal/Streams$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "com/google/gson/internal/bind/NumberTypeAdapter$1.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/NumberTypeAdapter$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/google/gson/internal/bind/NumberTypeAdapter;)V"}, {"nme": "create", "acc": 1, "dsc": "(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken;)Lcom/google/gson/TypeAdapter;", "sig": "<T:Ljava/lang/Object;>(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken<TT;>;)Lcom/google/gson/TypeAdapter<TT;>;"}], "flds": [{"acc": 4112, "nme": "val$adapter", "dsc": "Lcom/google/gson/internal/bind/NumberTypeAdapter;"}]}, "com/google/gson/internal/Excluder$1.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/Excluder$1", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/google/gson/internal/Excluder;ZZLcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken;)V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "sig": "(Lcom/google/gson/stream/JsonReader;)TT;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "sig": "(Lcom/google/gson/stream/JsonWriter;TT;)V", "exs": ["java/io/IOException"]}, {"nme": "delegate", "acc": 2, "dsc": "()Lcom/google/gson/TypeAdapter;", "sig": "()Lcom/google/gson/TypeAdapter<TT;>;"}], "flds": [{"acc": 66, "nme": "delegate", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<TT;>;"}, {"acc": 4112, "nme": "val$skipDeserialize", "dsc": "Z"}, {"acc": 4112, "nme": "val$skipSerialize", "dsc": "Z"}, {"acc": 4112, "nme": "val$gson", "dsc": "Lcom/google/gson/Gson;"}, {"acc": 4112, "nme": "val$type", "dsc": "Lcom/google/gson/reflect/TypeToken;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lcom/google/gson/internal/Excluder;"}]}, "com/google/gson/internal/bind/TypeAdapters$EnumTypeAdapter$1.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$EnumTypeAdapter$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/google/gson/internal/bind/TypeAdapters$EnumTypeAdapter;Ljava/lang/Class;)V"}, {"nme": "run", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/reflect/Field;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 4112, "nme": "val$classOfT", "dsc": "L<PERSON>va/lang/Class;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lcom/google/gson/internal/bind/TypeAdapters$EnumTypeAdapter;"}]}, "com/google/gson/Strictness.class": {"ver": 51, "acc": 16433, "nme": "com/google/gson/Strictness", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lcom/google/gson/Strictness;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/google/gson/Strictness;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "LENIENT", "dsc": "Lcom/google/gson/Strictness;"}, {"acc": 16409, "nme": "LEGACY_STRICT", "dsc": "Lcom/google/gson/Strictness;"}, {"acc": 16409, "nme": "STRICT", "dsc": "Lcom/google/gson/Strictness;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lcom/google/gson/Strictness;"}]}, "com/google/gson/internal/bind/TypeAdapters$EnumTypeAdapter.class": {"ver": 51, "acc": 48, "nme": "com/google/gson/internal/bind/TypeAdapters$EnumTypeAdapter", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(Ljava/lang/Class<TT;>;)V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Enum;", "sig": "(Lcom/google/gson/stream/JsonReader;)TT;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON><PERSON><PERSON>/lang/Enum;)V", "sig": "(Lcom/google/gson/stream/JsonWriter;TT;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 18, "nme": "nameToConstant", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;TT;>;"}, {"acc": 18, "nme": "stringToConstant", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;TT;>;"}, {"acc": 18, "nme": "constantToName", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<TT;Ljava/lang/String;>;"}]}, "com/google/gson/TypeAdapter.class": {"ver": 51, "acc": 1057, "nme": "com/google/gson/TypeAdapter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "write", "acc": 1025, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "sig": "(Lcom/google/gson/stream/JsonWriter;TT;)V", "exs": ["java/io/IOException"]}, {"nme": "to<PERSON><PERSON>", "acc": 17, "dsc": "(<PERSON><PERSON><PERSON>/io/Writer;<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(<PERSON><PERSON><PERSON>/io/Writer;TT;)V", "exs": ["java/io/IOException"]}, {"nme": "to<PERSON><PERSON>", "acc": 17, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON>ja<PERSON>/lang/String;", "sig": "(TT;)Ljava/lang/String;"}, {"nme": "toJsonTree", "acc": 17, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lcom/google/gson/JsonElement;", "sig": "(TT;)Lcom/google/gson/JsonElement;"}, {"nme": "read", "acc": 1025, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "sig": "(Lcom/google/gson/stream/JsonReader;)TT;", "exs": ["java/io/IOException"]}, {"nme": "fromJson", "acc": 17, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/io/Reader;)TT;", "exs": ["java/io/IOException"]}, {"nme": "fromJson", "acc": 17, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;)TT;", "exs": ["java/io/IOException"]}, {"nme": "fromJsonTree", "acc": 17, "dsc": "(Lcom/google/gson/JsonElement;)Ljava/lang/Object;", "sig": "(Lcom/google/gson/JsonElement;)TT;"}, {"nme": "nullSafe", "acc": 17, "dsc": "()Lcom/google/gson/TypeAdapter;", "sig": "()Lcom/google/gson/TypeAdapter<TT;>;"}], "flds": []}, "com/google/gson/internal/ConstructorConstructor$4.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/ConstructorConstructor$4", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/google/gson/internal/ConstructorConstructor;Ljava/lang/String;)V"}, {"nme": "construct", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}], "flds": [{"acc": 4112, "nme": "val$message", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lcom/google/gson/internal/ConstructorConstructor;"}]}, "com/google/gson/Gson$FutureTypeAdapter.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/Gson$FutureTypeAdapter", "super": "com/google/gson/internal/bind/SerializationDelegatingTypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "setDelegate", "acc": 1, "dsc": "(Lcom/google/gson/TypeAdapter;)V", "sig": "(Lcom/google/gson/TypeAdapter<TT;>;)V"}, {"nme": "delegate", "acc": 2, "dsc": "()Lcom/google/gson/TypeAdapter;", "sig": "()Lcom/google/gson/TypeAdapter<TT;>;"}, {"nme": "getSerializationDelegate", "acc": 1, "dsc": "()Lcom/google/gson/TypeAdapter;", "sig": "()Lcom/google/gson/TypeAdapter<TT;>;"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "sig": "(Lcom/google/gson/stream/JsonReader;)TT;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "sig": "(Lcom/google/gson/stream/JsonWriter;TT;)V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 2, "nme": "delegate", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<TT;>;"}]}, "com/google/gson/internal/bind/TypeAdapters$34.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$34", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/lang/Class;Lcom/google/gson/TypeAdapter;)V"}, {"nme": "create", "acc": 1, "dsc": "(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken;)Lcom/google/gson/TypeAdapter;", "sig": "<T2:Ljava/lang/Object;>(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken<TT2;>;)Lcom/google/gson/TypeAdapter<TT2;>;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 4112, "nme": "val$clazz", "dsc": "L<PERSON>va/lang/Class;"}, {"acc": 4112, "nme": "val$typeAdapter", "dsc": "Lcom/google/gson/TypeAdapter;"}]}, "com/google/gson/JsonIOException.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/JsonIOException", "super": "com/google/gson/JsonParseException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}]}, "com/google/gson/package-info.class": {"ver": 51, "acc": 5632, "nme": "com/google/gson/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "com/google/gson/internal/sql/SqlTypesSupport$2.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/sql/SqlTypesSupport$2", "super": "com/google/gson/internal/bind/DefaultDateTypeAdapter$DateType", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V"}, {"nme": "deserialize", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/util/Date;)Ljava/sql/Timestamp;"}, {"nme": "deserialize", "acc": 4164, "dsc": "(<PERSON><PERSON><PERSON>/util/Date;)<PERSON><PERSON><PERSON>/util/Date;"}], "flds": []}, "com/google/gson/internal/bind/ObjectTypeAdapter.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/internal/bind/ObjectTypeAdapter", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lcom/google/gson/Gson;Lcom/google/gson/ToNumberStrategy;)V"}, {"nme": "newFactory", "acc": 10, "dsc": "(Lcom/google/gson/ToNumberStrategy;)Lcom/google/gson/TypeAdapterFactory;"}, {"nme": "getFactory", "acc": 9, "dsc": "(Lcom/google/gson/ToNumberStrategy;)Lcom/google/gson/TypeAdapterFactory;"}, {"nme": "tryBeginNesting", "acc": 2, "dsc": "(Lcom/google/gson/stream/JsonReader;Lcom/google/gson/stream/JsonToken;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "readTerminal", "acc": 2, "dsc": "(Lcom/google/gson/stream/JsonReader;Lcom/google/gson/stream/JsonToken;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 4096, "dsc": "(Lcom/google/gson/Gson;Lcom/google/gson/ToNumberStrategy;Lcom/google/gson/internal/bind/ObjectTypeAdapter$1;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "DOUBLE_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 18, "nme": "gson", "dsc": "Lcom/google/gson/Gson;"}, {"acc": 18, "nme": "toNumberStrategy", "dsc": "Lcom/google/gson/ToNumberStrategy;"}]}, "com/google/gson/Gson$5.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/Gson$5", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/google/gson/TypeAdapter;)V"}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/util/concurrent/atomic/AtomicLongArray;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/util/concurrent/atomic/AtomicLongArray;", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 4112, "nme": "val$longAdapter", "dsc": "Lcom/google/gson/TypeAdapter;"}]}, "com/google/gson/ReflectionAccessFilter$1.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/ReflectionAccessFilter$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "check", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Lcom/google/gson/ReflectionAccessFilter$FilterResult;", "sig": "(L<PERSON><PERSON>/lang/Class<*>;)Lcom/google/gson/ReflectionAccessFilter$FilterResult;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "com/google/gson/annotations/Since.class": {"ver": 51, "acc": 9729, "nme": "com/google/gson/annotations/Since", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()D"}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "RUNTIME"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "FIELD"], ["L<PERSON>va/lang/annotation/ElementType;", "TYPE"]]]}]}, "com/google/gson/internal/LinkedTreeMap$Node.class": {"ver": 51, "acc": 48, "nme": "com/google/gson/internal/LinkedTreeMap$Node", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Z)V"}, {"nme": "<init>", "acc": 0, "dsc": "(ZLcom/google/gson/internal/LinkedTreeMap$Node;<PERSON>java/lang/Object;Lcom/google/gson/internal/LinkedTreeMap$Node;Lcom/google/gson/internal/LinkedTreeMap$Node;)V", "sig": "(ZLcom/google/gson/internal/LinkedTreeMap$Node<TK;TV;>;TK;Lcom/google/gson/internal/LinkedTreeMap$Node<TK;TV;>;Lcom/google/gson/internal/LinkedTreeMap$Node<TK;TV;>;)V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TK;"}, {"nme": "getValue", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TV;"}, {"nme": "setValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(TV;)TV;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "first", "acc": 1, "dsc": "()Lcom/google/gson/internal/LinkedTreeMap$Node;", "sig": "()Lcom/google/gson/internal/LinkedTreeMap$Node<TK;TV;>;"}, {"nme": "last", "acc": 1, "dsc": "()Lcom/google/gson/internal/LinkedTreeMap$Node;", "sig": "()Lcom/google/gson/internal/LinkedTreeMap$Node<TK;TV;>;"}], "flds": [{"acc": 0, "nme": "parent", "dsc": "Lcom/google/gson/internal/LinkedTreeMap$Node;", "sig": "Lcom/google/gson/internal/LinkedTreeMap$Node<TK;TV;>;"}, {"acc": 0, "nme": "left", "dsc": "Lcom/google/gson/internal/LinkedTreeMap$Node;", "sig": "Lcom/google/gson/internal/LinkedTreeMap$Node<TK;TV;>;"}, {"acc": 0, "nme": "right", "dsc": "Lcom/google/gson/internal/LinkedTreeMap$Node;", "sig": "Lcom/google/gson/internal/LinkedTreeMap$Node<TK;TV;>;"}, {"acc": 0, "nme": "next", "dsc": "Lcom/google/gson/internal/LinkedTreeMap$Node;", "sig": "Lcom/google/gson/internal/LinkedTreeMap$Node<TK;TV;>;"}, {"acc": 0, "nme": "prev", "dsc": "Lcom/google/gson/internal/LinkedTreeMap$Node;", "sig": "Lcom/google/gson/internal/LinkedTreeMap$Node<TK;TV;>;"}, {"acc": 16, "nme": "key", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;", "sig": "TK;"}, {"acc": 16, "nme": "allowNullValue", "dsc": "Z"}, {"acc": 0, "nme": "value", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;", "sig": "TV;"}, {"acc": 0, "nme": "height", "dsc": "I"}]}, "com/google/gson/internal/bind/TypeAdapters$23.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$23", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/net/InetAddress;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/net/InetAddress;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/internal/bind/CollectionTypeAdapterFactory.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/internal/bind/CollectionTypeAdapterFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/google/gson/internal/ConstructorConstructor;)V"}, {"nme": "create", "acc": 1, "dsc": "(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken;)Lcom/google/gson/TypeAdapter;", "sig": "<T:Ljava/lang/Object;>(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken<TT;>;)Lcom/google/gson/TypeAdapter<TT;>;"}], "flds": [{"acc": 18, "nme": "constructorConstructor", "dsc": "Lcom/google/gson/internal/ConstructorConstructor;"}]}, "com/google/gson/internal/LinkedTreeMap$EntrySet$1.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/LinkedTreeMap$EntrySet$1", "super": "com/google/gson/internal/LinkedTreeMap$LinkedTreeMapIterator", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/google/gson/internal/LinkedTreeMap$EntrySet;)V"}, {"nme": "next", "acc": 1, "dsc": "()Ljava/util/Map$Entry;", "sig": "()Ljava/util/Map$Entry<TK;TV;>;"}, {"nme": "next", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 4112, "nme": "this$1", "dsc": "Lcom/google/gson/internal/LinkedTreeMap$EntrySet;"}]}, "com/google/gson/internal/bind/DefaultDateTypeAdapter$1.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/DefaultDateTypeAdapter$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "create", "acc": 1, "dsc": "(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken;)Lcom/google/gson/TypeAdapter;", "sig": "<T:Ljava/lang/Object;>(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken<TT;>;)Lcom/google/gson/TypeAdapter<TT;>;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "com/google/gson/internal/Streams$AppendableWriter$CurrentWrite.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/Streams$AppendableWriter$CurrentWrite", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 0, "dsc": "([C)V"}, {"nme": "length", "acc": 1, "dsc": "()I"}, {"nme": "char<PERSON>t", "acc": 1, "dsc": "(I)C"}, {"nme": "subSequence", "acc": 1, "dsc": "(II)<PERSON><PERSON><PERSON>/lang/CharSequence;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lcom/google/gson/internal/Streams$1;)V"}], "flds": [{"acc": 2, "nme": "chars", "dsc": "[C"}, {"acc": 2, "nme": "cachedString", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "com/google/gson/InstanceCreator.class": {"ver": 51, "acc": 1537, "nme": "com/google/gson/InstanceCreator", "super": "java/lang/Object", "mthds": [{"nme": "createInstance", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Type;)Ljava/lang/Object;", "sig": "(Lja<PERSON>/lang/reflect/Type;)TT;"}], "flds": []}, "com/google/gson/internal/sql/SqlTypesSupport$1.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/sql/SqlTypesSupport$1", "super": "com/google/gson/internal/bind/DefaultDateTypeAdapter$DateType", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V"}, {"nme": "deserialize", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/util/Date;)L<PERSON>va/sql/Date;"}, {"nme": "deserialize", "acc": 4164, "dsc": "(<PERSON><PERSON><PERSON>/util/Date;)<PERSON><PERSON><PERSON>/util/Date;"}], "flds": []}, "com/google/gson/internal/ConstructorConstructor$6.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/ConstructorConstructor$6", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Type;)V"}, {"nme": "construct", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}], "flds": [{"acc": 4112, "nme": "val$type", "dsc": "Ljava/lang/reflect/Type;"}]}, "com/google/gson/FieldNamingPolicy$1.class": {"ver": 51, "acc": 16432, "nme": "com/google/gson/FieldNamingPolicy$1", "super": "com/google/gson/FieldNamingPolicy", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"nme": "translateName", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)Ljava/lang/String;"}], "flds": []}, "com/google/gson/JsonParser.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/JsonParser", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "parseString", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/google/gson/JsonElement;", "exs": ["com/google/gson/JsonSyntaxException"]}, {"nme": "parse<PERSON><PERSON><PERSON>", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;)Lcom/google/gson/JsonElement;", "exs": ["com/google/gson/JsonIOException", "com/google/gson/JsonSyntaxException"]}, {"nme": "parse<PERSON><PERSON><PERSON>", "acc": 9, "dsc": "(Lcom/google/gson/stream/JsonReader;)Lcom/google/gson/JsonElement;", "exs": ["com/google/gson/JsonIOException", "com/google/gson/JsonSyntaxException"]}, {"nme": "parse", "acc": 131073, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/google/gson/JsonElement;", "exs": ["com/google/gson/JsonSyntaxException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/InlineMe;", "vals": ["replacement", "JsonParser.parseString(json)", "imports", ["com.google.gson.JsonParser"]]}]}, {"nme": "parse", "acc": 131073, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;)Lcom/google/gson/JsonElement;", "exs": ["com/google/gson/JsonIOException", "com/google/gson/JsonSyntaxException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/InlineMe;", "vals": ["replacement", "JsonParser.parseReader(json)", "imports", ["com.google.gson.JsonParser"]]}]}, {"nme": "parse", "acc": 131073, "dsc": "(Lcom/google/gson/stream/JsonReader;)Lcom/google/gson/JsonElement;", "exs": ["com/google/gson/JsonIOException", "com/google/gson/JsonSyntaxException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}], "invanns": [{"dsc": "Lcom/google/errorprone/annotations/InlineMe;", "vals": ["replacement", "JsonParser.parseReader(json)", "imports", ["com.google.gson.JsonParser"]]}]}], "flds": []}, "com/google/gson/internal/LinkedTreeMap$LinkedTreeMapIterator.class": {"ver": 51, "acc": 1056, "nme": "com/google/gson/internal/LinkedTreeMap$LinkedTreeMapIterator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/google/gson/internal/LinkedTreeMap;)V"}, {"nme": "hasNext", "acc": 17, "dsc": "()Z"}, {"nme": "nextNode", "acc": 16, "dsc": "()Lcom/google/gson/internal/LinkedTreeMap$Node;", "sig": "()Lcom/google/gson/internal/LinkedTreeMap$Node<TK;TV;>;"}, {"nme": "remove", "acc": 17, "dsc": "()V"}], "flds": [{"acc": 0, "nme": "next", "dsc": "Lcom/google/gson/internal/LinkedTreeMap$Node;", "sig": "Lcom/google/gson/internal/LinkedTreeMap$Node<TK;TV;>;"}, {"acc": 0, "nme": "lastReturned", "dsc": "Lcom/google/gson/internal/LinkedTreeMap$Node;", "sig": "Lcom/google/gson/internal/LinkedTreeMap$Node<TK;TV;>;"}, {"acc": 0, "nme": "expectedModCount", "dsc": "I"}, {"acc": 4112, "nme": "this$0", "dsc": "Lcom/google/gson/internal/LinkedTreeMap;"}]}, "com/google/gson/internal/bind/TypeAdapters$26.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$26", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/util/Calendar;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/util/Calendar;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 26, "nme": "YEAR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "year"}, {"acc": 26, "nme": "MONTH", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "month"}, {"acc": 26, "nme": "DAY_OF_MONTH", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "dayOfMonth"}, {"acc": 26, "nme": "HOUR_OF_DAY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "hourOfDay"}, {"acc": 26, "nme": "MINUTE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "minute"}, {"acc": 26, "nme": "SECOND", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "second"}]}, "com/google/gson/internal/bind/TypeAdapters$3.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$3", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Bo<PERSON>an;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON><PERSON><PERSON>/lang/<PERSON>;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/internal/bind/TypeAdapters$29.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$29", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "create", "acc": 1, "dsc": "(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken;)Lcom/google/gson/TypeAdapter;", "sig": "<T:Ljava/lang/Object;>(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken<TT;>;)Lcom/google/gson/TypeAdapter<TT;>;"}], "flds": []}, "com/google/gson/JsonObject.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/JsonObject", "super": "com/google/gson/JsonElement", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "deepCopy", "acc": 1, "dsc": "()Lcom/google/gson/JsonObject;"}, {"nme": "add", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lcom/google/gson/JsonElement;)V"}, {"nme": "remove", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/google/gson/JsonElement;", "invanns": [{"dsc": "Lcom/google/errorprone/annotations/CanIgnoreReturnValue;"}]}, {"nme": "addProperty", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "addProperty", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Number;)V"}, {"nme": "addProperty", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Boolean;)V"}, {"nme": "addProperty", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Character;)V"}, {"nme": "entrySet", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljava/util/Map$Entry<Ljava/lang/String;Lcom/google/gson/JsonElement;>;>;"}, {"nme": "keySet", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "size", "acc": 1, "dsc": "()I"}, {"nme": "isEmpty", "acc": 1, "dsc": "()Z"}, {"nme": "has", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "get", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/google/gson/JsonElement;"}, {"nme": "getAsJsonPrimitive", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/google/gson/JsonPrimitive;"}, {"nme": "getAsJsonArray", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/google/gson/Json<PERSON>y;"}, {"nme": "getAsJsonObject", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/google/gson/JsonObject;"}, {"nme": "asMap", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Lcom/google/gson/JsonElement;>;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "deepCopy", "acc": 4161, "dsc": "()Lcom/google/gson/JsonElement;"}], "flds": [{"acc": 18, "nme": "members", "dsc": "Lcom/google/gson/internal/LinkedTreeMap;", "sig": "Lcom/google/gson/internal/LinkedTreeMap<Ljava/lang/String;Lcom/google/gson/JsonElement;>;"}]}, "com/google/gson/internal/$Gson$Preconditions.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/internal/$Gson$Preconditions", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "checkNotNull", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(TT;)TT;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "checkArgument", "acc": 9, "dsc": "(Z)V"}], "flds": []}, "com/google/gson/internal/bind/TypeAdapters$19.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$19", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/StringBuilder;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/lang/StringBuilder;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/internal/ConstructorConstructor$9.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/ConstructorConstructor$9", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Constructor;)V"}, {"nme": "construct", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}], "flds": [{"acc": 4112, "nme": "val$constructor", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Constructor;"}]}, "com/google/gson/JsonSyntaxException.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/JsonSyntaxException", "super": "com/google/gson/JsonParseException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}]}, "com/google/gson/internal/bind/JsonAdapterAnnotationTypeAdapterFactory.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/internal/bind/JsonAdapterAnnotationTypeAdapterFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/google/gson/internal/ConstructorConstructor;)V"}, {"nme": "getAnnotation", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Lcom/google/gson/annotations/JsonAdapter;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Lcom/google/gson/annotations/JsonAdapter;"}, {"nme": "create", "acc": 1, "dsc": "(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken;)Lcom/google/gson/TypeAdapter;", "sig": "<T:Ljava/lang/Object;>(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken<TT;>;)Lcom/google/gson/TypeAdapter<TT;>;"}, {"nme": "createAdapter", "acc": 10, "dsc": "(Lcom/google/gson/internal/ConstructorConstructor;Ljava/lang/Class;)Ljava/lang/Object;", "sig": "(Lcom/google/gson/internal/ConstructorConstructor;Ljava/lang/Class<*>;)Ljava/lang/Object;"}, {"nme": "putFactoryAndGetCurrent", "acc": 2, "dsc": "(Ljava/lang/Class;Lcom/google/gson/TypeAdapterFactory;)Lcom/google/gson/TypeAdapterFactory;", "sig": "(Ljava/lang/Class<*>;Lcom/google/gson/TypeAdapterFactory;)Lcom/google/gson/TypeAdapterFactory;"}, {"nme": "getTypeAdapter", "acc": 0, "dsc": "(Lcom/google/gson/internal/ConstructorConstructor;Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken;Lcom/google/gson/annotations/JsonAdapter;Z)Lcom/google/gson/TypeAdapter;", "sig": "(Lcom/google/gson/internal/ConstructorConstructor;Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken<*>;Lcom/google/gson/annotations/JsonAdapter;Z)Lcom/google/gson/TypeAdapter<*>;"}, {"nme": "isClassJsonAdapterFactory", "acc": 1, "dsc": "(Lcom/google/gson/reflect/TypeToken;Lcom/google/gson/TypeAdapterFactory;)Z", "sig": "(Lcom/google/gson/reflect/TypeToken<*>;Lcom/google/gson/TypeAdapterFactory;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "TREE_TYPE_CLASS_DUMMY_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 26, "nme": "TREE_TYPE_FIELD_DUMMY_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 18, "nme": "constructorConstructor", "dsc": "Lcom/google/gson/internal/ConstructorConstructor;"}, {"acc": 18, "nme": "adapterFactoryMap", "dsc": "Ljava/util/concurrent/ConcurrentMap;", "sig": "Ljava/util/concurrent/ConcurrentMap<Ljava/lang/Class<*>;Lcom/google/gson/TypeAdapterFactory;>;"}]}, "com/google/gson/JsonSerializer.class": {"ver": 51, "acc": 1537, "nme": "com/google/gson/JsonSerializer", "super": "java/lang/Object", "mthds": [{"nme": "serialize", "acc": 1025, "dsc": "(Lja<PERSON>/lang/Object;Ljava/lang/reflect/Type;Lcom/google/gson/JsonSerializationContext;)Lcom/google/gson/JsonElement;", "sig": "(TT;Ljava/lang/reflect/Type;Lcom/google/gson/JsonSerializationContext;)Lcom/google/gson/JsonElement;"}], "flds": []}, "com/google/gson/internal/ConstructorConstructor$14.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/ConstructorConstructor$14", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "construct", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}], "flds": []}, "com/google/gson/internal/bind/TypeAdapters$30.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$30", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/google/gson/reflect/TypeToken;Lcom/google/gson/TypeAdapter;)V"}, {"nme": "create", "acc": 1, "dsc": "(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken;)Lcom/google/gson/TypeAdapter;", "sig": "<T:Ljava/lang/Object;>(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken<TT;>;)Lcom/google/gson/TypeAdapter<TT;>;"}], "flds": [{"acc": 4112, "nme": "val$type", "dsc": "Lcom/google/gson/reflect/TypeToken;"}, {"acc": 4112, "nme": "val$typeAdapter", "dsc": "Lcom/google/gson/TypeAdapter;"}]}, "com/google/gson/internal/UnsafeAllocator$1.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/UnsafeAllocator$1", "super": "com/google/gson/internal/UnsafeAllocator", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;Ljava/lang/Object;)V"}, {"nme": "newInstance", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>ja<PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;)TT;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 4112, "nme": "val$allocateInstance", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 4112, "nme": "val$unsafe", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "com/google/gson/internal/bind/DefaultDateTypeAdapter$DateType.class": {"ver": 51, "acc": 1057, "nme": "com/google/gson/internal/bind/DefaultDateTypeAdapter$DateType", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(Ljava/lang/Class<TT;>;)V"}, {"nme": "deserialize", "acc": 1028, "dsc": "(<PERSON><PERSON><PERSON>/util/Date;)<PERSON><PERSON><PERSON>/util/Date;", "sig": "(<PERSON><PERSON><PERSON>/util/Date;)TT;"}, {"nme": "createFactory", "acc": 2, "dsc": "(Lcom/google/gson/internal/bind/DefaultDateTypeAdapter;)Lcom/google/gson/TypeAdapterFactory;", "sig": "(Lcom/google/gson/internal/bind/DefaultDateTypeAdapter<TT;>;)Lcom/google/gson/TypeAdapterFactory;"}, {"nme": "createAdapterFactory", "acc": 17, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/google/gson/TypeAdapterFactory;"}, {"nme": "createAdapterFactory", "acc": 17, "dsc": "(II)Lcom/google/gson/TypeAdapterFactory;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "DATE", "dsc": "Lcom/google/gson/internal/bind/DefaultDateTypeAdapter$DateType;", "sig": "Lcom/google/gson/internal/bind/DefaultDateTypeAdapter$DateType<Ljava/util/Date;>;"}, {"acc": 18, "nme": "dateClass", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<TT;>;"}]}, "com/google/gson/TypeAdapter$1.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/TypeAdapter$1", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/google/gson/TypeAdapter;)V"}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "sig": "(Lcom/google/gson/stream/JsonWriter;TT;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "sig": "(Lcom/google/gson/stream/JsonReader;)TT;", "exs": ["java/io/IOException"]}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lcom/google/gson/TypeAdapter;"}]}, "com/google/gson/internal/bind/JsonAdapterAnnotationTypeAdapterFactory$DummyTypeAdapterFactory.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/JsonAdapterAnnotationTypeAdapterFactory$DummyTypeAdapterFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "create", "acc": 1, "dsc": "(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken;)Lcom/google/gson/TypeAdapter;", "sig": "<T:Ljava/lang/Object;>(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken<TT;>;)Lcom/google/gson/TypeAdapter<TT;>;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lcom/google/gson/internal/bind/JsonAdapterAnnotationTypeAdapterFactory$1;)V"}], "flds": []}, "com/google/gson/internal/bind/JsonTreeReader$2.class": {"ver": 51, "acc": 4128, "nme": "com/google/gson/internal/bind/JsonTreeReader$2", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$com$google$gson$stream$JsonToken", "dsc": "[I"}]}, "com/google/gson/internal/bind/TypeAdapters$33.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$33", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lja<PERSON>/lang/Class;Ljava/lang/Class;Lcom/google/gson/TypeAdapter;)V"}, {"nme": "create", "acc": 1, "dsc": "(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken;)Lcom/google/gson/TypeAdapter;", "sig": "<T:Ljava/lang/Object;>(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken<TT;>;)Lcom/google/gson/TypeAdapter<TT;>;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 4112, "nme": "val$base", "dsc": "L<PERSON>va/lang/Class;"}, {"acc": 4112, "nme": "val$sub", "dsc": "L<PERSON>va/lang/Class;"}, {"acc": 4112, "nme": "val$typeAdapter", "dsc": "Lcom/google/gson/TypeAdapter;"}]}, "com/google/gson/internal/ConstructorConstructor$17.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/ConstructorConstructor$17", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "construct", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}], "flds": []}, "com/google/gson/LongSerializationPolicy$1.class": {"ver": 51, "acc": 16432, "nme": "com/google/gson/LongSerializationPolicy$1", "super": "com/google/gson/LongSerializationPolicy", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"nme": "serialize", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Long;)Lcom/google/gson/JsonElement;"}], "flds": []}, "com/google/gson/internal/sql/SqlTypesSupport.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/internal/sql/SqlTypesSupport", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "SUPPORTS_SQL_TYPES", "dsc": "Z"}, {"acc": 25, "nme": "DATE_DATE_TYPE", "dsc": "Lcom/google/gson/internal/bind/DefaultDateTypeAdapter$DateType;", "sig": "Lcom/google/gson/internal/bind/DefaultDateTypeAdapter$DateType<+Ljava/util/Date;>;"}, {"acc": 25, "nme": "TIMESTAMP_DATE_TYPE", "dsc": "Lcom/google/gson/internal/bind/DefaultDateTypeAdapter$DateType;", "sig": "Lcom/google/gson/internal/bind/DefaultDateTypeAdapter$DateType<+Ljava/util/Date;>;"}, {"acc": 25, "nme": "DATE_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 25, "nme": "TIME_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 25, "nme": "TIMESTAMP_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}]}, "com/google/gson/internal/reflect/ReflectionHelper$RecordNotSupportedHelper.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/reflect/ReflectionHelper$RecordNotSupportedHelper", "super": "com/google/gson/internal/reflect/ReflectionHelper$RecordHelper", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "isRecord", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "getRecordComponentNames", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)[Lja<PERSON>/lang/String;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)[Ljava/lang/String;"}, {"nme": "getCanonicalRecordConstructor", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/reflect/Constructor;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;)Ljava/lang/reflect/Constructor<TT;>;"}, {"nme": "getAccessor", "acc": 1, "dsc": "(L<PERSON><PERSON>/lang/Class;Ljava/lang/reflect/Field;)Ljava/lang/reflect/Method;", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/reflect/Field;)Ljava/lang/reflect/Method;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lcom/google/gson/internal/reflect/ReflectionHelper$1;)V"}], "flds": []}, "com/google/gson/internal/sql/SqlDateTypeAdapter$1.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/sql/SqlDateTypeAdapter$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "create", "acc": 1, "dsc": "(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken;)Lcom/google/gson/TypeAdapter;", "sig": "<T:Ljava/lang/Object;>(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken<TT;>;)Lcom/google/gson/TypeAdapter<TT;>;"}], "flds": []}, "com/google/gson/JsonPrimitive.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/JsonPrimitive", "super": "com/google/gson/JsonElement", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/<PERSON>;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Number;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Character;)V"}, {"nme": "deepCopy", "acc": 1, "dsc": "()Lcom/google/gson/JsonPrimitive;"}, {"nme": "isBoolean", "acc": 1, "dsc": "()Z"}, {"nme": "getAsBoolean", "acc": 1, "dsc": "()Z"}, {"nme": "isNumber", "acc": 1, "dsc": "()Z"}, {"nme": "getAsNumber", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Number;"}, {"nme": "isString", "acc": 1, "dsc": "()Z"}, {"nme": "getAsString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getAsDouble", "acc": 1, "dsc": "()D"}, {"nme": "getAsBigDecimal", "acc": 1, "dsc": "()Ljava/math/BigDecimal;"}, {"nme": "getAsBigInteger", "acc": 1, "dsc": "()Ljava/math/BigInteger;"}, {"nme": "getAsFloat", "acc": 1, "dsc": "()F"}, {"nme": "getAsLong", "acc": 1, "dsc": "()J"}, {"nme": "getAsShort", "acc": 1, "dsc": "()S"}, {"nme": "getAsInt", "acc": 1, "dsc": "()I"}, {"nme": "getAsByte", "acc": 1, "dsc": "()B"}, {"nme": "getAsCharacter", "acc": 131073, "dsc": "()C", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "isIntegral", "acc": 10, "dsc": "(Lcom/google/gson/JsonPrimitive;)Z"}, {"nme": "deepCopy", "acc": 4161, "dsc": "()Lcom/google/gson/JsonElement;"}], "flds": [{"acc": 18, "nme": "value", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "com/google/gson/internal/reflect/ReflectionHelper$RecordSupportedHelper.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/reflect/ReflectionHelper$RecordSupportedHelper", "super": "com/google/gson/internal/reflect/ReflectionHelper$RecordHelper", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V", "exs": ["java/lang/NoSuchMethodException", "java/lang/ClassNotFoundException"]}, {"nme": "isRecord", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "getRecordComponentNames", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)[Lja<PERSON>/lang/String;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)[Ljava/lang/String;"}, {"nme": "getCanonicalRecordConstructor", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/reflect/Constructor;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;)Ljava/lang/reflect/Constructor<TT;>;"}, {"nme": "getAccessor", "acc": 1, "dsc": "(L<PERSON><PERSON>/lang/Class;Ljava/lang/reflect/Field;)Ljava/lang/reflect/Method;", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/reflect/Field;)Ljava/lang/reflect/Method;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lcom/google/gson/internal/reflect/ReflectionHelper$1;)V", "exs": ["java/lang/NoSuchMethodException", "java/lang/ClassNotFoundException"]}], "flds": [{"acc": 18, "nme": "isRecord", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 18, "nme": "getRecordComponents", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 18, "nme": "getName", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 18, "nme": "getType", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}]}, "com/google/gson/internal/bind/TypeAdapters$10.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$10", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/util/concurrent/atomic/AtomicIntegerArray;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/util/concurrent/atomic/AtomicIntegerArray;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/internal/reflect/ReflectionHelper$RecordHelper.class": {"ver": 51, "acc": 1056, "nme": "com/google/gson/internal/reflect/ReflectionHelper$RecordHelper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "isRecord", "acc": 1024, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "getRecordComponentNames", "acc": 1024, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)[Lja<PERSON>/lang/String;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)[Ljava/lang/String;"}, {"nme": "getCanonicalRecordConstructor", "acc": 1024, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/reflect/Constructor;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;)Ljava/lang/reflect/Constructor<TT;>;"}, {"nme": "getAccessor", "acc": 1025, "dsc": "(L<PERSON><PERSON>/lang/Class;Ljava/lang/reflect/Field;)Ljava/lang/reflect/Method;", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/reflect/Field;)Ljava/lang/reflect/Method;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lcom/google/gson/internal/reflect/ReflectionHelper$1;)V"}], "flds": []}, "com/google/gson/internal/bind/TypeAdapters$1.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$1", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/lang/Class;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Class;", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/internal/package-info.class": {"ver": 51, "acc": 5632, "nme": "com/google/gson/internal/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "com/google/gson/internal/bind/TypeAdapters$31.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$31", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/lang/Class;Lcom/google/gson/TypeAdapter;)V"}, {"nme": "create", "acc": 1, "dsc": "(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken;)Lcom/google/gson/TypeAdapter;", "sig": "<T:Ljava/lang/Object;>(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken<TT;>;)Lcom/google/gson/TypeAdapter<TT;>;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 4112, "nme": "val$type", "dsc": "L<PERSON>va/lang/Class;"}, {"acc": 4112, "nme": "val$typeAdapter", "dsc": "Lcom/google/gson/TypeAdapter;"}]}, "com/google/gson/annotations/Expose.class": {"ver": 51, "acc": 9729, "nme": "com/google/gson/annotations/Expose", "super": "java/lang/Object", "mthds": [{"nme": "serialize", "acc": 1025, "dsc": "()Z"}, {"nme": "deserialize", "acc": 1025, "dsc": "()Z"}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "RUNTIME"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "FIELD"]]]}]}, "com/google/gson/internal/Streams.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/internal/Streams", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "parse", "acc": 9, "dsc": "(Lcom/google/gson/stream/JsonReader;)Lcom/google/gson/JsonElement;", "exs": ["com/google/gson/JsonParseException"]}, {"nme": "write", "acc": 9, "dsc": "(Lcom/google/gson/JsonElement;Lcom/google/gson/stream/JsonWriter;)V", "exs": ["java/io/IOException"]}, {"nme": "writer<PERSON><PERSON><PERSON>ppendable", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Appendable;)<PERSON><PERSON><PERSON>/io/Writer;"}], "flds": []}, "com/google/gson/internal/sql/SqlTimestampTypeAdapter$1.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/sql/SqlTimestampTypeAdapter$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "create", "acc": 1, "dsc": "(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken;)Lcom/google/gson/TypeAdapter;", "sig": "<T:Ljava/lang/Object;>(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken<TT;>;)Lcom/google/gson/TypeAdapter<TT;>;"}], "flds": []}, "com/google/gson/internal/UnsafeAllocator.class": {"ver": 51, "acc": 1057, "nme": "com/google/gson/internal/UnsafeAllocator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "newInstance", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>ja<PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;)TT;", "exs": ["java/lang/Exception"]}, {"nme": "assertInstantiable", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)V"}, {"nme": "create", "acc": 10, "dsc": "()Lcom/google/gson/internal/UnsafeAllocator;"}, {"nme": "access$000", "acc": 4104, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "INSTANCE", "dsc": "Lcom/google/gson/internal/UnsafeAllocator;"}]}, "com/google/gson/internal/ConstructorConstructor$16.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/ConstructorConstructor$16", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "construct", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}], "flds": []}, "com/google/gson/internal/bind/ObjectTypeAdapter$2.class": {"ver": 51, "acc": 4128, "nme": "com/google/gson/internal/bind/ObjectTypeAdapter$2", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$com$google$gson$stream$JsonToken", "dsc": "[I"}]}, "com/google/gson/internal/ReflectionAccessFilterHelper$AccessChecker$1.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/ReflectionAccessFilterHelper$AccessChecker$1", "super": "com/google/gson/internal/ReflectionAccessFilterHelper$AccessChecker", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)V"}, {"nme": "canAccess", "acc": 1, "dsc": "(Lja<PERSON>/lang/reflect/AccessibleObject;Ljava/lang/Object;)Z"}], "flds": [{"acc": 4112, "nme": "val$canAccessMethod", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}]}, "com/google/gson/internal/UnsafeAllocator$4.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/UnsafeAllocator$4", "super": "com/google/gson/internal/UnsafeAllocator", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "newInstance", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>ja<PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;)TT;"}], "flds": []}, "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$Adapter.class": {"ver": 51, "acc": 1057, "nme": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$Adapter", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/google/gson/internal/bind/ReflectiveTypeAdapterFactory$FieldsData;)V"}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "sig": "(Lcom/google/gson/stream/JsonWriter;TT;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "sig": "(Lcom/google/gson/stream/JsonReader;)TT;", "exs": ["java/io/IOException"]}, {"nme": "createAccumulator", "acc": 1024, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TA;"}, {"nme": "readField", "acc": 1024, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lcom/google/gson/stream/JsonReader;Lcom/google/gson/internal/bind/ReflectiveTypeAdapterFactory$BoundField;)V", "sig": "(TA;Lcom/google/gson/stream/JsonReader;Lcom/google/gson/internal/bind/ReflectiveTypeAdapterFactory$BoundField;)V", "exs": ["java/lang/IllegalAccessException", "java/io/IOException"]}, {"nme": "finalize", "acc": 1024, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(TA;)TT;"}], "flds": [{"acc": 18, "nme": "fieldsData", "dsc": "Lcom/google/gson/internal/bind/ReflectiveTypeAdapterFactory$FieldsData;"}]}, "com/google/gson/internal/bind/TreeTypeAdapter$SingleTypeFactory.class": {"ver": 51, "acc": 48, "nme": "com/google/gson/internal/bind/TreeTypeAdapter$SingleTypeFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/lang/Object;Lcom/google/gson/reflect/TypeToken;ZLjava/lang/Class;)V", "sig": "(Ljava/lang/Object;Lcom/google/gson/reflect/TypeToken<*>;ZLjava/lang/Class<*>;)V"}, {"nme": "create", "acc": 1, "dsc": "(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken;)Lcom/google/gson/TypeAdapter;", "sig": "<T:Ljava/lang/Object;>(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken<TT;>;)Lcom/google/gson/TypeAdapter<TT;>;"}], "flds": [{"acc": 18, "nme": "exactType", "dsc": "Lcom/google/gson/reflect/TypeToken;", "sig": "Lcom/google/gson/reflect/TypeToken<*>;"}, {"acc": 18, "nme": "matchRawType", "dsc": "Z"}, {"acc": 18, "nme": "hierarchyType", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 18, "nme": "serializer", "dsc": "Lcom/google/gson/JsonSerializer;", "sig": "Lcom/google/gson/JsonSerializer<*>;"}, {"acc": 18, "nme": "deserializer", "dsc": "Lcom/google/gson/JsonDeserializer;", "sig": "Lcom/google/gson/JsonDeserializer<*>;"}]}, "com/google/gson/internal/bind/TypeAdapters$2.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$2", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/util/BitSet;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/util/BitSet;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/internal/bind/TypeAdapters$27.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$27", "super": "com/google/gson/TypeAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/util/Locale;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/google/gson/stream/JsonWriter;Ljava/util/Locale;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 4161, "dsc": "(Lcom/google/gson/stream/JsonWriter;<PERSON>java/lang/Object;)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/google/gson/JsonParseException.class": {"ver": 51, "acc": 33, "nme": "com/google/gson/JsonParseException", "super": "java/lang/RuntimeException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)V"}], "flds": [{"acc": 24, "nme": "serialVersionUID", "dsc": "J", "val": -4086729973971783390}]}, "com/google/gson/internal/UnsafeAllocator$3.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/UnsafeAllocator$3", "super": "com/google/gson/internal/UnsafeAllocator", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)V"}, {"nme": "newInstance", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>ja<PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;)TT;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 4112, "nme": "val$newInstance", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}]}, "com/google/gson/LongSerializationPolicy.class": {"ver": 51, "acc": 17441, "nme": "com/google/gson/LongSerializationPolicy", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lcom/google/gson/LongSerializationPolicy;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/google/gson/LongSerializationPolicy;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "serialize", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Long;)Lcom/google/gson/JsonElement;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Ljava/lang/String;ILcom/google/gson/LongSerializationPolicy$1;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "DEFAULT", "dsc": "Lcom/google/gson/LongSerializationPolicy;"}, {"acc": 16409, "nme": "STRING", "dsc": "Lcom/google/gson/LongSerializationPolicy;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lcom/google/gson/LongSerializationPolicy;"}]}, "com/google/gson/internal/ReflectionAccessFilterHelper$AccessChecker$2.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/ReflectionAccessFilterHelper$AccessChecker$2", "super": "com/google/gson/internal/ReflectionAccessFilterHelper$AccessChecker", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "canAccess", "acc": 1, "dsc": "(Lja<PERSON>/lang/reflect/AccessibleObject;Ljava/lang/Object;)Z"}], "flds": []}, "com/google/gson/internal/bind/TypeAdapters$32.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/TypeAdapters$32", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lja<PERSON>/lang/Class;Ljava/lang/Class;Lcom/google/gson/TypeAdapter;)V"}, {"nme": "create", "acc": 1, "dsc": "(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken;)Lcom/google/gson/TypeAdapter;", "sig": "<T:Ljava/lang/Object;>(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken<TT;>;)Lcom/google/gson/TypeAdapter<TT;>;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 4112, "nme": "val$unboxed", "dsc": "L<PERSON>va/lang/Class;"}, {"acc": 4112, "nme": "val$boxed", "dsc": "L<PERSON>va/lang/Class;"}, {"acc": 4112, "nme": "val$typeAdapter", "dsc": "Lcom/google/gson/TypeAdapter;"}]}, "com/google/gson/internal/ConstructorConstructor$7.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/ConstructorConstructor$7", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "construct", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}], "flds": [{"acc": 4112, "nme": "val$message", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "com/google/gson/internal/bind/ObjectTypeAdapter$1.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/bind/ObjectTypeAdapter$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/google/gson/ToNumberStrategy;)V"}, {"nme": "create", "acc": 1, "dsc": "(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken;)Lcom/google/gson/TypeAdapter;", "sig": "<T:Ljava/lang/Object;>(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken<TT;>;)Lcom/google/gson/TypeAdapter<TT;>;"}], "flds": [{"acc": 4112, "nme": "val$toNumberStrategy", "dsc": "Lcom/google/gson/ToNumberStrategy;"}]}, "com/google/gson/internal/ReflectionAccessFilterHelper$1.class": {"ver": 51, "acc": 4128, "nme": "com/google/gson/internal/ReflectionAccessFilterHelper$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "com/google/gson/internal/ConstructorConstructor$20.class": {"ver": 51, "acc": 32, "nme": "com/google/gson/internal/ConstructorConstructor$20", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/google/gson/internal/ConstructorConstructor;Ljava/lang/String;)V"}, {"nme": "construct", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}], "flds": [{"acc": 4112, "nme": "val$exceptionMessageF", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lcom/google/gson/internal/ConstructorConstructor;"}]}, "com/google/gson/internal/ReflectionAccessFilterHelper.class": {"ver": 51, "acc": 33, "nme": "com/google/gson/internal/ReflectionAccessFilterHelper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "isJavaType", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "isJavaType", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isAndroidType", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "isAndroidType", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isAnyPlatformType", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "getFilterResult", "acc": 9, "dsc": "(Lja<PERSON>/util/List;Ljava/lang/Class;)Lcom/google/gson/ReflectionAccessFilter$FilterResult;", "sig": "(Ljava/util/List<Lcom/google/gson/ReflectionAccessFilter;>;Ljava/lang/Class<*>;)Lcom/google/gson/ReflectionAccessFilter$FilterResult;"}, {"nme": "canAccess", "acc": 9, "dsc": "(Lja<PERSON>/lang/reflect/AccessibleObject;Ljava/lang/Object;)Z"}], "flds": []}, "com/google/gson/internal/bind/TypeAdapters.class": {"ver": 51, "acc": 49, "nme": "com/google/gson/internal/bind/TypeAdapters", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "newFactory", "acc": 9, "dsc": "(Lcom/google/gson/reflect/TypeToken;Lcom/google/gson/TypeAdapter;)Lcom/google/gson/TypeAdapterFactory;", "sig": "<TT:Ljava/lang/Object;>(Lcom/google/gson/reflect/TypeToken<TTT;>;Lcom/google/gson/TypeAdapter<TTT;>;)Lcom/google/gson/TypeAdapterFactory;"}, {"nme": "newFactory", "acc": 9, "dsc": "(Ljava/lang/Class;Lcom/google/gson/TypeAdapter;)Lcom/google/gson/TypeAdapterFactory;", "sig": "<TT:Ljava/lang/Object;>(Ljava/lang/Class<TTT;>;Lcom/google/gson/TypeAdapter<TTT;>;)Lcom/google/gson/TypeAdapterFactory;"}, {"nme": "newFactory", "acc": 9, "dsc": "(Ljava/lang/Class;Ljava/lang/Class;Lcom/google/gson/TypeAdapter;)Lcom/google/gson/TypeAdapterFactory;", "sig": "<TT:Ljava/lang/Object;>(Ljava/lang/Class<TTT;>;Ljava/lang/Class<TTT;>;Lcom/google/gson/TypeAdapter<-TTT;>;)Lcom/google/gson/TypeAdapterFactory;"}, {"nme": "newFactoryForMultipleTypes", "acc": 9, "dsc": "(Ljava/lang/Class;Ljava/lang/Class;Lcom/google/gson/TypeAdapter;)Lcom/google/gson/TypeAdapterFactory;", "sig": "<TT:Ljava/lang/Object;>(Ljava/lang/Class<TTT;>;Ljava/lang/Class<+TTT;>;Lcom/google/gson/TypeAdapter<-TTT;>;)Lcom/google/gson/TypeAdapterFactory;"}, {"nme": "newTypeHierarchyFactory", "acc": 9, "dsc": "(Ljava/lang/Class;Lcom/google/gson/TypeAdapter;)Lcom/google/gson/TypeAdapterFactory;", "sig": "<T1:Ljava/lang/Object;>(Ljava/lang/Class<TT1;>;Lcom/google/gson/TypeAdapter<TT1;>;)Lcom/google/gson/TypeAdapterFactory;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "CLASS", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<Ljava/lang/Class;>;"}, {"acc": 25, "nme": "CLASS_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 25, "nme": "BIT_SET", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<Ljava/util/BitSet;>;"}, {"acc": 25, "nme": "BIT_SET_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 25, "nme": "BOOLEAN", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<Ljava/lang/Boolean;>;"}, {"acc": 25, "nme": "BOOLEAN_AS_STRING", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<Ljava/lang/Boolean;>;"}, {"acc": 25, "nme": "BOOLEAN_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 25, "nme": "BYTE", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<Ljava/lang/Number;>;"}, {"acc": 25, "nme": "BYTE_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 25, "nme": "SHORT", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<Ljava/lang/Number;>;"}, {"acc": 25, "nme": "SHORT_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 25, "nme": "INTEGER", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<Ljava/lang/Number;>;"}, {"acc": 25, "nme": "INTEGER_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 25, "nme": "ATOMIC_INTEGER", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<Ljava/util/concurrent/atomic/AtomicInteger;>;"}, {"acc": 25, "nme": "ATOMIC_INTEGER_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 25, "nme": "ATOMIC_BOOLEAN", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<Ljava/util/concurrent/atomic/AtomicBoolean;>;"}, {"acc": 25, "nme": "ATOMIC_BOOLEAN_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 25, "nme": "ATOMIC_INTEGER_ARRAY", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<Ljava/util/concurrent/atomic/AtomicIntegerArray;>;"}, {"acc": 25, "nme": "ATOMIC_INTEGER_ARRAY_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 25, "nme": "LONG", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<Ljava/lang/Number;>;"}, {"acc": 25, "nme": "FLOAT", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<Ljava/lang/Number;>;"}, {"acc": 25, "nme": "DOUBLE", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<Ljava/lang/Number;>;"}, {"acc": 25, "nme": "CHARACTER", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<Ljava/lang/Character;>;"}, {"acc": 25, "nme": "CHARACTER_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 25, "nme": "STRING", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<Ljava/lang/String;>;"}, {"acc": 25, "nme": "BIG_DECIMAL", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<Ljava/math/BigDecimal;>;"}, {"acc": 25, "nme": "BIG_INTEGER", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<Ljava/math/BigInteger;>;"}, {"acc": 25, "nme": "LAZILY_PARSED_NUMBER", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<Lcom/google/gson/internal/LazilyParsedNumber;>;"}, {"acc": 25, "nme": "STRING_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 25, "nme": "STRING_BUILDER", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<Ljava/lang/StringBuilder;>;"}, {"acc": 25, "nme": "STRING_BUILDER_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 25, "nme": "STRING_BUFFER", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<Ljava/lang/StringBuffer;>;"}, {"acc": 25, "nme": "STRING_BUFFER_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 25, "nme": "URL", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<Ljava/net/URL;>;"}, {"acc": 25, "nme": "URL_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 25, "nme": "URI", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<Ljava/net/URI;>;"}, {"acc": 25, "nme": "URI_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 25, "nme": "INET_ADDRESS", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<Ljava/net/InetAddress;>;"}, {"acc": 25, "nme": "INET_ADDRESS_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 25, "nme": "UUID", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<Ljava/util/UUID;>;"}, {"acc": 25, "nme": "UUID_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 25, "nme": "CURRENCY", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<Ljava/util/Currency;>;"}, {"acc": 25, "nme": "CURRENCY_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 25, "nme": "CALENDAR", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<Ljava/util/Calendar;>;"}, {"acc": 25, "nme": "CALENDAR_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 25, "nme": "LOCALE", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<Ljava/util/Locale;>;"}, {"acc": 25, "nme": "LOCALE_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 25, "nme": "JSON_ELEMENT", "dsc": "Lcom/google/gson/TypeAdapter;", "sig": "Lcom/google/gson/TypeAdapter<Lcom/google/gson/JsonElement;>;"}, {"acc": 25, "nme": "JSON_ELEMENT_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}, {"acc": 25, "nme": "ENUM_FACTORY", "dsc": "Lcom/google/gson/TypeAdapterFactory;"}]}}}}