{"md5": "f59529b29202a5baf37f491ea5ec8627", "sha2": "7399e65dd7e9ff3404f4535b2f017093bdb134c7", "sha256": "88241573467ddca44ffd4d74aa04c2bbfd11bf7c17e0c342c94c9de7a70a7c64", "contents": {"classes": {"com/google/j2objc/annotations/UsedByReflection.class": {"ver": 52, "acc": 9729, "nme": "com/google/j2objc/annotations/UsedByReflection", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "CONSTRUCTOR"], ["L<PERSON>va/lang/annotation/ElementType;", "FIELD"]]]}]}, "com/google/j2objc/annotations/UsedByNative.class": {"ver": 52, "acc": 9729, "nme": "com/google/j2objc/annotations/UsedByNative", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "CONSTRUCTOR"], ["L<PERSON>va/lang/annotation/ElementType;", "FIELD"]]]}]}, "com/google/j2objc/annotations/Property.class": {"ver": 52, "acc": 9729, "nme": "com/google/j2objc/annotations/Property", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "SOURCE"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "FIELD"], ["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "TYPE"]]]}]}, "com/google/j2objc/annotations/RetainedWith.class": {"ver": 52, "acc": 9729, "nme": "com/google/j2objc/annotations/RetainedWith", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "FIELD"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}]}, "META-INF/versions/9/module-info.class": {"ver": 53, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "com/google/j2objc/annotations/RetainedLocalRef.class": {"ver": 52, "acc": 9729, "nme": "com/google/j2objc/annotations/RetainedLocalRef", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "LOCAL_VARIABLE"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "SOURCE"]]}]}, "com/google/j2objc/annotations/J2ObjCIncompatible.class": {"ver": 52, "acc": 9729, "nme": "com/google/j2objc/annotations/J2ObjCIncompatible", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "ANNOTATION_TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "CONSTRUCTOR"], ["L<PERSON>va/lang/annotation/ElementType;", "FIELD"], ["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "TYPE"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "SOURCE"]]}]}, "com/google/j2objc/annotations/OnDealloc.class": {"ver": 52, "acc": 9729, "nme": "com/google/j2objc/annotations/OnDealloc", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}]}, "com/google/j2objc/annotations/AutoreleasePool.class": {"ver": 52, "acc": 9729, "nme": "com/google/j2objc/annotations/AutoreleasePool", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "LOCAL_VARIABLE"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "SOURCE"]]}]}, "com/google/j2objc/annotations/GenerateObjectiveCGenerics.class": {"ver": 52, "acc": 9729, "nme": "com/google/j2objc/annotations/GenerateObjectiveCGenerics", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "TYPE"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "SOURCE"]]}]}, "com/google/j2objc/annotations/LoopTranslation$LoopStyle.class": {"ver": 52, "acc": 16433, "nme": "com/google/j2objc/annotations/LoopTranslation$LoopStyle", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lcom/google/j2objc/annotations/LoopTranslation$LoopStyle;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/google/j2objc/annotations/LoopTranslation$LoopStyle;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Lcom/google/j2objc/annotations/LoopTranslation$LoopStyle;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "JAVA_ITERATOR", "dsc": "Lcom/google/j2objc/annotations/LoopTranslation$LoopStyle;"}, {"acc": 16409, "nme": "FAST_ENUMERATION", "dsc": "Lcom/google/j2objc/annotations/LoopTranslation$LoopStyle;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lcom/google/j2objc/annotations/LoopTranslation$LoopStyle;"}]}, "com/google/j2objc/annotations/Property$Suppress.class": {"ver": 52, "acc": 9729, "nme": "com/google/j2objc/annotations/Property$Suppress", "super": "java/lang/Object", "mthds": [{"nme": "reason", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"]]]}]}, "com/google/j2objc/annotations/ReflectionSupport$Level.class": {"ver": 52, "acc": 16433, "nme": "com/google/j2objc/annotations/ReflectionSupport$Level", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lcom/google/j2objc/annotations/ReflectionSupport$Level;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/google/j2objc/annotations/ReflectionSupport$Level;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Lcom/google/j2objc/annotations/ReflectionSupport$Level;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "NATIVE_ONLY", "dsc": "Lcom/google/j2objc/annotations/ReflectionSupport$Level;"}, {"acc": 16409, "nme": "FULL", "dsc": "Lcom/google/j2objc/annotations/ReflectionSupport$Level;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lcom/google/j2objc/annotations/ReflectionSupport$Level;"}]}, "com/google/j2objc/annotations/ReflectionSupport.class": {"ver": 52, "acc": 9729, "nme": "com/google/j2objc/annotations/ReflectionSupport", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()Lcom/google/j2objc/annotations/ReflectionSupport$Level;"}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "PACKAGE"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}]}, "com/google/j2objc/annotations/WeakOuter.class": {"ver": 52, "acc": 9729, "nme": "com/google/j2objc/annotations/WeakOuter", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "TYPE_USE"], ["L<PERSON>va/lang/annotation/ElementType;", "LOCAL_VARIABLE"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "SOURCE"]]}]}, "com/google/j2objc/annotations/Weak.class": {"ver": 52, "acc": 9729, "nme": "com/google/j2objc/annotations/Weak", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "FIELD"], ["L<PERSON>va/lang/annotation/ElementType;", "LOCAL_VARIABLE"], ["L<PERSON>va/lang/annotation/ElementType;", "PARAMETER"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}]}, "com/google/j2objc/annotations/LoopTranslation.class": {"ver": 52, "acc": 9729, "nme": "com/google/j2objc/annotations/LoopTranslation", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()Lcom/google/j2objc/annotations/LoopTranslation$LoopStyle;"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "LOCAL_VARIABLE"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "SOURCE"]]}]}, "com/google/j2objc/annotations/ObjectiveCName.class": {"ver": 52, "acc": 9729, "nme": "com/google/j2objc/annotations/ObjectiveCName", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "CONSTRUCTOR"], ["L<PERSON>va/lang/annotation/ElementType;", "PACKAGE"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}]}}}}