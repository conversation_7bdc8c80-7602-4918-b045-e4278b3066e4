package shyrcs.extrastoragehook.economy;

import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.function.Consumer;

/**
 * Abstract class cho Economy Provider
 * Tương tự như EconomyProvider trong ExtraStorage
 */
public abstract class EconomyProvider {
    
    /**
     * Kiểm tra provider có hoạt động không
     */
    public abstract boolean isHooked();
    
    /**
     * Lấy số lượng cơ bản của item
     */
    public abstract int getAmount(ItemStack item);
    
    /**
     * Lấy giá của item
     */
    public abstract String getPrice(Player player, ItemStack item, int amount);
    
    /**
     * Bán item
     */
    public abstract void sellItem(Player player, ItemStack item, int amount, Consumer<Result> result);
    
    /**
     * Result class cho việc bán item
     */
    public static class Result {
        private final int amount;
        private final double price;
        private final boolean success;
        
        public Result(int amount, double price, boolean success) {
            this.amount = amount;
            this.price = price;
            this.success = success;
        }
        
        public int getAmount() {
            return amount;
        }
        
        public double getPrice() {
            return price;
        }
        
        public boolean isSuccess() {
            return success;
        }
    }
}
