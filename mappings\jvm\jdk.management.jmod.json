{"md5": "082b07bd5399791ca77763b7ea4bab36", "sha2": "55c23e00a958cf7bb2dd775c9a976bde7d21f816", "sha256": "35c42e327d669cff39b194dd1284350354e2dd68f20b232c6aeaf0ceda9fb67e", "contents": {"classes": {"classes/com/sun/management/GarbageCollectorMXBean.class": {"ver": 68, "acc": 1537, "nme": "com/sun/management/GarbageCollectorMXBean", "super": "java/lang/Object", "mthds": [{"nme": "getLastGcInfo", "acc": 1025, "dsc": "()Lcom/sun/management/GcInfo;"}], "flds": []}, "classes/com/sun/management/internal/GarbageCollectorExtImpl.class": {"ver": 68, "acc": 33, "nme": "com/sun/management/internal/GarbageCollectorExtImpl", "super": "sun/management/GarbageCollectorImpl", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getAllPoolNames", "acc": 34, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLastGcInfo", "acc": 1, "dsc": "()Lcom/sun/management/GcInfo;"}, {"nme": "getNotificationInfo", "acc": 1, "dsc": "()[Ljavax/management/MBeanNotificationInfo;"}, {"nme": "getNextSeqNumber", "acc": 10, "dsc": "()J"}, {"nme": "createGCNotification", "acc": 4, "dsc": "(J<PERSON>java/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/sun/management/GcInfo;)V"}, {"nme": "addNotificationListener", "acc": 33, "dsc": "(Ljavax/management/NotificationListener;Ljavax/management/NotificationFilter;Ljava/lang/Object;)V"}, {"nme": "removeNotificationListener", "acc": 33, "dsc": "(Ljavax/management/NotificationListener;)V", "exs": ["javax/management/ListenerNotFoundException"]}, {"nme": "removeNotificationListener", "acc": 33, "dsc": "(Ljavax/management/NotificationListener;Ljavax/management/NotificationFilter;Ljava/lang/Object;)V", "exs": ["javax/management/ListenerNotFoundException"]}, {"nme": "getGcInfoBuilder", "acc": 34, "dsc": "()Lcom/sun/management/internal/GcInfoBuilder;"}, {"nme": "setNotificationEnabled", "acc": 258, "dsc": "(Lcom/sun/management/GarbageCollectorMXBean;Z)V"}, {"nme": "createGarbageCollector", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;)Ljava/lang/management/GarbageCollectorMXBean;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "poolNames", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "notifName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "javax.management.Notification"}, {"acc": 26, "nme": "gcNotifTypes", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 10, "nme": "seqNumber", "dsc": "J"}, {"acc": 2, "nme": "gcInfoBuilder", "dsc": "Lcom/sun/management/internal/GcInfoBuilder;"}]}, "classes/com/sun/management/internal/Flag.class": {"ver": 68, "acc": 32, "nme": "com/sun/management/internal/Flag", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/lang/String;Ljava/lang/Object;ZZLcom/sun/management/VMOption$Origin;)V"}, {"nme": "getValue", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "isWriteable", "acc": 0, "dsc": "()Z"}, {"nme": "isExternal", "acc": 0, "dsc": "()Z"}, {"nme": "getVMOption", "acc": 0, "dsc": "()Lcom/sun/management/VMOption;"}, {"nme": "getFlag", "acc": 8, "dsc": "(Lja<PERSON>/lang/String;)Lcom/sun/management/internal/Flag;"}, {"nme": "getAllFlags", "acc": 8, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lcom/sun/management/internal/Flag;>;"}, {"nme": "getFlags", "acc": 10, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;I)Ljava/util/List;", "sig": "([<PERSON>ja<PERSON>/lang/String;I)Ljava/util/List<Lcom/sun/management/internal/Flag;>;"}, {"nme": "getAllFlagNames", "acc": 266, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getFlags", "acc": 266, "dsc": "([<PERSON>ja<PERSON>/lang/String;[Lcom/sun/management/internal/Flag;I)I"}, {"nme": "getInternalFlagCount", "acc": 266, "dsc": "()I"}, {"nme": "setLongValue", "acc": 296, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;J)V"}, {"nme": "setDoubleValue", "acc": 296, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;D)V"}, {"nme": "setBooleanValue", "acc": 296, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)V"}, {"nme": "setStringValue", "acc": 296, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "initialize", "acc": 266, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "value", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 2, "nme": "origin", "dsc": "Lcom/sun/management/VMOption$Origin;"}, {"acc": 2, "nme": "writeable", "dsc": "Z"}, {"acc": 2, "nme": "external", "dsc": "Z"}]}, "classes/com/sun/management/internal/PlatformMBeanProviderImpl$5.class": {"ver": 68, "acc": 32, "nme": "com/sun/management/internal/PlatformMBeanProviderImpl$5", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/sun/management/internal/PlatformMBeanProviderImpl;)V"}, {"nme": "mbeanInterfaces", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljava/lang/Class<+Lcom/sun/management/HotSpotDiagnosticMXBean;>;>;"}, {"nme": "mbeanInterfaceNames", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "getObjectNamePattern", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "nameToMBeanMap", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Lcom/sun/management/HotSpotDiagnosticMXBean;>;"}], "flds": [{"acc": 18, "nme": "hotSpotDiagnosticMXBeanInterfaceNames", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}]}, "classes/com/sun/management/internal/OperatingSystemImpl.class": {"ver": 68, "acc": 32, "nme": "com/sun/management/internal/OperatingSystemImpl", "super": "sun/management/BaseOperatingSystemImpl", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/management/VMManagement;)V"}, {"nme": "getCommittedVirtualMemorySize", "acc": 1, "dsc": "()J"}, {"nme": "getTotalSwapSpaceSize", "acc": 1, "dsc": "()J"}, {"nme": "getFreeSwapSpaceSize", "acc": 1, "dsc": "()J"}, {"nme": "getProcessCpuTime", "acc": 1, "dsc": "()J"}, {"nme": "getFreeMemorySize", "acc": 1, "dsc": "()J"}, {"nme": "getTotalMemorySize", "acc": 1, "dsc": "()J"}, {"nme": "getCpuLoad", "acc": 1, "dsc": "()D"}, {"nme": "getProcessCpuLoad", "acc": 1, "dsc": "()D"}, {"nme": "getCommittedVirtualMemorySize0", "acc": 258, "dsc": "()J"}, {"nme": "getFreeMemorySize0", "acc": 258, "dsc": "()J"}, {"nme": "getFreeSwapSpaceSize0", "acc": 258, "dsc": "()J"}, {"nme": "getProcessCpuLoad0", "acc": 258, "dsc": "()D"}, {"nme": "getProcessCpuTime0", "acc": 258, "dsc": "()J"}, {"nme": "getCpuLoad0", "acc": 258, "dsc": "()D"}, {"nme": "getTotalMemorySize0", "acc": 258, "dsc": "()J"}, {"nme": "getTotalSwapSpaceSize0", "acc": 258, "dsc": "()J"}, {"nme": "initialize0", "acc": 266, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 10, "nme": "psapiLock", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "classes/com/sun/management/GarbageCollectionNotificationInfo.class": {"ver": 68, "acc": 33, "nme": "com/sun/management/GarbageCollectionNotificationInfo", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/sun/management/GcInfo;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(Ljavax/management/openmbean/CompositeData;)V"}, {"nme": "getGcName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getGcAction", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getGcCause", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getGcInfo", "acc": 1, "dsc": "()Lcom/sun/management/GcInfo;"}, {"nme": "from", "acc": 9, "dsc": "(Ljavax/management/openmbean/CompositeData;)Lcom/sun/management/GarbageCollectionNotificationInfo;"}, {"nme": "toCompositeData", "acc": 1, "dsc": "(Ljavax/management/openmbean/CompositeType;)Ljavax/management/openmbean/CompositeData;"}], "flds": [{"acc": 18, "nme": "gcName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "gcAction", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "gcCause", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "gcInfo", "dsc": "Lcom/sun/management/GcInfo;"}, {"acc": 18, "nme": "cdata", "dsc": "Ljavax/management/openmbean/CompositeData;"}, {"acc": 25, "nme": "GARBAGE_COLLECTION_NOTIFICATION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.management.gc.notification"}]}, "classes/com/sun/management/ThreadMXBean.class": {"ver": 68, "acc": 1537, "nme": "com/sun/management/ThreadMXBean", "super": "java/lang/Object", "mthds": [{"nme": "getThreadCpuTime", "acc": 1025, "dsc": "([J)[J"}, {"nme": "getThreadUserTime", "acc": 1025, "dsc": "([J)[J"}, {"nme": "getTotalThreadAllocatedBytes", "acc": 1, "dsc": "()J"}, {"nme": "getCurrentThreadAllocatedBytes", "acc": 1, "dsc": "()J"}, {"nme": "getThreadAllocatedBytes", "acc": 1025, "dsc": "(J)J"}, {"nme": "getThreadAllocatedBytes", "acc": 1025, "dsc": "([J)[J"}, {"nme": "isThreadAllocatedMemorySupported", "acc": 1025, "dsc": "()Z"}, {"nme": "isThreadAllocatedMemoryEnabled", "acc": 1025, "dsc": "()Z"}, {"nme": "setThreadAllocatedMemoryEnabled", "acc": 1025, "dsc": "(Z)V"}], "flds": []}, "classes/com/sun/management/internal/DiagnosticCommandInfo.class": {"ver": 68, "acc": 32, "nme": "com/sun/management/internal/DiagnosticCommandInfo", "super": "java/lang/Object", "mthds": [{"nme": "getName", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDescription", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getImpact", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isEnabled", "acc": 0, "dsc": "()Z"}, {"nme": "getArgumentsInfo", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lcom/sun/management/internal/DiagnosticCommandArgumentInfo;>;"}, {"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Z<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLjava/util/List<Lcom/sun/management/internal/DiagnosticCommandArgumentInfo;>;)V"}], "flds": [{"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "description", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "impact", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "enabled", "dsc": "Z"}, {"acc": 18, "nme": "arguments", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lcom/sun/management/internal/DiagnosticCommandArgumentInfo;>;"}]}, "classes/com/sun/management/internal/VirtualThreadSchedulerImpls$BaseVirtualThreadSchedulerImpl.class": {"ver": 68, "acc": 1056, "nme": "com/sun/management/internal/VirtualThreadSchedulerImpls$BaseVirtualThreadSchedulerImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getObjectName", "acc": 17, "dsc": "()Ljavax/management/ObjectName;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "append", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;<PERSON><PERSON><PERSON>/lang/String;J)V"}], "flds": []}, "classes/com/sun/management/internal/GcInfoCompositeData.class": {"ver": 68, "acc": 33, "nme": "com/sun/management/internal/GcInfoCompositeData", "super": "sun/management/LazyCompositeData", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/sun/management/GcInfo;Lcom/sun/management/internal/GcInfoBuilder;[<PERSON>ja<PERSON>/lang/Object;)V"}, {"nme": "getGcInfo", "acc": 1, "dsc": "()Lcom/sun/management/GcInfo;"}, {"nme": "toCompositeData", "acc": 9, "dsc": "(Lcom/sun/management/GcInfo;)Ljavax/management/openmbean/CompositeData;"}, {"nme": "getCompositeData", "acc": 4, "dsc": "()Ljavax/management/openmbean/CompositeData;"}, {"nme": "getBaseGcInfoItemNames", "acc": 8, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getBaseGcInfoItemTypes", "acc": 40, "dsc": "()[Ljavax/management/openmbean/OpenType;", "sig": "()[Ljavax/management/openmbean/OpenType<*>;"}, {"nme": "getId", "acc": 9, "dsc": "(Ljavax/management/openmbean/CompositeData;)J"}, {"nme": "getStartTime", "acc": 9, "dsc": "(Ljavax/management/openmbean/CompositeData;)J"}, {"nme": "getEndTime", "acc": 9, "dsc": "(Ljavax/management/openmbean/CompositeData;)J"}, {"nme": "getMemoryUsageBeforeGc", "acc": 9, "dsc": "(Ljavax/management/openmbean/CompositeData;)Ljava/util/Map;", "sig": "(Ljavax/management/openmbean/CompositeData;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/management/MemoryUsage;>;"}, {"nme": "cast", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Ljava/util/Map;", "sig": "(Lja<PERSON>/lang/Object;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/management/MemoryUsage;>;"}, {"nme": "getMemoryUsageAfterGc", "acc": 9, "dsc": "(Ljavax/management/openmbean/CompositeData;)Ljava/util/Map;", "sig": "(Ljavax/management/openmbean/CompositeData;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/management/MemoryUsage;>;"}, {"nme": "validateCompositeData", "acc": 9, "dsc": "(Ljavax/management/openmbean/CompositeData;)V"}, {"nme": "getBaseGcInfoCompositeType", "acc": 40, "dsc": "()Ljavax/management/openmbean/CompositeType;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "info", "dsc": "Lcom/sun/management/GcInfo;"}, {"acc": 18, "nme": "builder", "dsc": "Lcom/sun/management/internal/GcInfoBuilder;"}, {"acc": 18, "nme": "gcExtItemValues", "dsc": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 26, "nme": "ID", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "id"}, {"acc": 26, "nme": "START_TIME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "startTime"}, {"acc": 26, "nme": "END_TIME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "endTime"}, {"acc": 26, "nme": "DURATION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "duration"}, {"acc": 26, "nme": "MEMORY_USAGE_BEFORE_GC", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "memoryUsageBeforeGc"}, {"acc": 26, "nme": "MEMORY_USAGE_AFTER_GC", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "memoryUsageAfterGc"}, {"acc": 26, "nme": "baseGcInfoItemNames", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 10, "nme": "memoryUsageMapType", "dsc": "Lsun/management/MappedMXBeanType;"}, {"acc": 10, "nme": "baseGcInfoItemTypes", "dsc": "[Ljavax/management/openmbean/OpenType;", "sig": "[Ljavax/management/openmbean/OpenType<*>;"}, {"acc": 10, "nme": "baseGcInfoCompositeType", "dsc": "Ljavax/management/openmbean/CompositeType;"}, {"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -5716428894085882742}]}, "classes/com/sun/management/internal/VirtualThreadSchedulerImpls$VirtualThreadSchedulerImpl.class": {"ver": 68, "acc": 48, "nme": "com/sun/management/internal/VirtualThreadSchedulerImpls$VirtualThreadSchedulerImpl", "super": "com/sun/management/internal/VirtualThreadSchedulerImpls$BaseVirtualThreadSchedulerImpl", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getParallelism", "acc": 1, "dsc": "()I"}, {"nme": "setParallelism", "acc": 1, "dsc": "(I)V"}, {"nme": "getPoolSize", "acc": 1, "dsc": "()I"}, {"nme": "getMountedVirtualThreadCount", "acc": 1, "dsc": "()I"}, {"nme": "getQueuedVirtualThreadCount", "acc": 1, "dsc": "()J"}, {"nme": "lambda$setParallelism$0", "acc": 4106, "dsc": "()V"}], "flds": []}, "classes/module-info.class": {"ver": 68, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "classes/com/sun/management/internal/GarbageCollectionNotifInfoCompositeData.class": {"ver": 68, "acc": 33, "nme": "com/sun/management/internal/GarbageCollectionNotifInfoCompositeData", "super": "sun/management/LazyCompositeData", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/sun/management/GarbageCollectionNotificationInfo;)V"}, {"nme": "getGarbageCollectionNotifInfo", "acc": 1, "dsc": "()Lcom/sun/management/GarbageCollectionNotificationInfo;"}, {"nme": "toCompositeData", "acc": 9, "dsc": "(Lcom/sun/management/GarbageCollectionNotificationInfo;)Ljavax/management/openmbean/CompositeData;"}, {"nme": "getCompositeTypeByBuilder", "acc": 2, "dsc": "()Ljavax/management/openmbean/CompositeType;"}, {"nme": "getCompositeData", "acc": 4, "dsc": "()Ljavax/management/openmbean/CompositeData;"}, {"nme": "getGcName", "acc": 9, "dsc": "(Ljavax/management/openmbean/CompositeData;)Ljava/lang/String;"}, {"nme": "getGcAction", "acc": 9, "dsc": "(Ljavax/management/openmbean/CompositeData;)Ljava/lang/String;"}, {"nme": "getGcCause", "acc": 9, "dsc": "(Ljavax/management/openmbean/CompositeData;)Ljava/lang/String;"}, {"nme": "getGcInfo", "acc": 9, "dsc": "(Ljavax/management/openmbean/CompositeData;)Lcom/sun/management/GcInfo;"}, {"nme": "validateCompositeData", "acc": 9, "dsc": "(Ljavax/management/openmbean/CompositeData;)V"}, {"nme": "getBaseGcNotifInfoCompositeType", "acc": 42, "dsc": "()Ljavax/management/openmbean/CompositeType;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "gcNotifInfo", "dsc": "Lcom/sun/management/GarbageCollectionNotificationInfo;"}, {"acc": 26, "nme": "GC_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "gcName"}, {"acc": 26, "nme": "GC_ACTION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "gcAction"}, {"acc": 26, "nme": "GC_CAUSE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "gcCause"}, {"acc": 26, "nme": "GC_INFO", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "gcInfo"}, {"acc": 26, "nme": "gcNotifInfoItemNames", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 10, "nme": "compositeTypeByBuilder", "dsc": "<PERSON><PERSON><PERSON>/util/HashMap;", "sig": "Ljava/util/HashMap<Lcom/sun/management/internal/GcInfoBuilder;Ljavax/management/openmbean/CompositeType;>;"}, {"acc": 10, "nme": "baseGcNotifInfoCompositeType", "dsc": "Ljavax/management/openmbean/CompositeType;"}, {"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -1805123446483771292}]}, "classes/com/sun/management/VMOption.class": {"ver": 68, "acc": 33, "nme": "com/sun/management/VMOption", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/lang/String;Ljava/lang/String;ZLcom/sun/management/VMOption$Origin;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(Ljavax/management/openmbean/CompositeData;)V"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getValue", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Lcom/sun/management/VMOption$Origin;"}, {"nme": "isWriteable", "acc": 1, "dsc": "()Z"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "from", "acc": 9, "dsc": "(Ljavax/management/openmbean/CompositeData;)Lcom/sun/management/VMOption;"}], "flds": [{"acc": 2, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "value", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "writeable", "dsc": "Z"}, {"acc": 2, "nme": "origin", "dsc": "Lcom/sun/management/VMOption$Origin;"}]}, "classes/com/sun/management/internal/HotSpotDiagnostic.class": {"ver": 68, "acc": 33, "nme": "com/sun/management/internal/HotSpotDiagnostic", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "dumpHeap", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)V", "exs": ["java/io/IOException"]}, {"nme": "dumpHeap0", "acc": 258, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)V", "exs": ["java/io/IOException"]}, {"nme": "getDiagnosticOptions", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lcom/sun/management/VMOption;>;"}, {"nme": "getVMOption", "acc": 1, "dsc": "(Ljava/lang/String;)Lcom/sun/management/VMOption;"}, {"nme": "setVMOption", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getObjectName", "acc": 1, "dsc": "()Ljavax/management/ObjectName;"}, {"nme": "dumpThreads", "acc": 1, "dsc": "(Ljava/lang/String;Lcom/sun/management/HotSpotDiagnosticMXBean$ThreadDumpFormat;)V", "exs": ["java/io/IOException"]}, {"nme": "dumpThreads", "acc": 2, "dsc": "(Ljava/io/OutputStream;Lcom/sun/management/HotSpotDiagnosticMXBean$ThreadDumpFormat;)V", "exs": ["java/io/IOException"]}], "flds": []}, "classes/jdk/management/VirtualThreadSchedulerMXBean.class": {"ver": 68, "acc": 1537, "nme": "jdk/management/VirtualThreadSchedulerMXBean", "super": "java/lang/Object", "mthds": [{"nme": "getParallelism", "acc": 1025, "dsc": "()I"}, {"nme": "setParallelism", "acc": 1025, "dsc": "(I)V"}, {"nme": "getPoolSize", "acc": 1025, "dsc": "()I"}, {"nme": "getMountedVirtualThreadCount", "acc": 1025, "dsc": "()I"}, {"nme": "getQueuedVirtualThreadCount", "acc": 1025, "dsc": "()J"}], "flds": []}, "classes/com/sun/management/internal/DiagnosticCommandArgumentInfo.class": {"ver": 68, "acc": 32, "nme": "com/sun/management/internal/DiagnosticCommandArgumentInfo", "super": "java/lang/Object", "mthds": [{"nme": "getName", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDescription", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getType", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDefault", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isMandatory", "acc": 0, "dsc": "()Z"}, {"nme": "isOption", "acc": 0, "dsc": "()Z"}, {"nme": "isMultiple", "acc": 0, "dsc": "()Z"}, {"nme": "getPosition", "acc": 0, "dsc": "()I"}, {"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;ZZZI)V"}], "flds": [{"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "description", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "type", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "defaultValue", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "mandatory", "dsc": "Z"}, {"acc": 18, "nme": "option", "dsc": "Z"}, {"acc": 18, "nme": "multiple", "dsc": "Z"}, {"acc": 18, "nme": "position", "dsc": "I"}]}, "classes/com/sun/management/internal/PlatformMBeanProviderImpl$3.class": {"ver": 68, "acc": 32, "nme": "com/sun/management/internal/PlatformMBeanProviderImpl$3", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/sun/management/internal/PlatformMBeanProviderImpl;)V"}, {"nme": "mbeanInterfaces", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljava/lang/Class<+Ljdk/management/VirtualThreadSchedulerMXBean;>;>;"}, {"nme": "mbeanInterfaceNames", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "getObjectNamePattern", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "nameToMBeanMap", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljdk/management/VirtualThreadSchedulerMXBean;>;"}], "flds": [{"acc": 18, "nme": "mbeanInterfaces", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/Class<+Ljdk/management/VirtualThreadSchedulerMXBean;>;>;"}, {"acc": 18, "nme": "mbeanInterfaceNames", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 2, "nme": "impl", "dsc": "Ljdk/management/VirtualThreadSchedulerMXBean;"}]}, "classes/com/sun/management/internal/VirtualThreadSchedulerImpls$VirtualThreadSchedulerImpl$Scheduler.class": {"ver": 68, "acc": 32, "nme": "com/sun/management/internal/VirtualThreadSchedulerImpls$VirtualThreadSchedulerImpl$Scheduler", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "instance", "acc": 8, "dsc": "()<PERSON><PERSON><PERSON>/util/concurrent/Executor;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "scheduler", "dsc": "<PERSON><PERSON><PERSON>/util/concurrent/Executor;"}]}, "classes/com/sun/management/internal/VirtualThreadSchedulerImpls$BoundVirtualThreadSchedulerImpl.class": {"ver": 68, "acc": 48, "nme": "com/sun/management/internal/VirtualThreadSchedulerImpls$BoundVirtualThreadSchedulerImpl", "super": "com/sun/management/internal/VirtualThreadSchedulerImpls$BaseVirtualThreadSchedulerImpl", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getParallelism", "acc": 1, "dsc": "()I"}, {"nme": "setParallelism", "acc": 1, "dsc": "(I)V"}, {"nme": "getPoolSize", "acc": 1, "dsc": "()I"}, {"nme": "getMountedVirtualThreadCount", "acc": 1, "dsc": "()I"}, {"nme": "getQueuedVirtualThreadCount", "acc": 1, "dsc": "()J"}], "flds": []}, "classes/com/sun/management/UnixOperatingSystemMXBean.class": {"ver": 68, "acc": 1537, "nme": "com/sun/management/UnixOperatingSystemMXBean", "super": "java/lang/Object", "mthds": [{"nme": "getOpenFileDescriptorCount", "acc": 1025, "dsc": "()J"}, {"nme": "getMaxFileDescriptorCount", "acc": 1025, "dsc": "()J"}], "flds": []}, "classes/com/sun/management/VMOption$Origin.class": {"ver": 68, "acc": 16433, "nme": "com/sun/management/VMOption$Origin", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lcom/sun/management/VMOption$Origin;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Lcom/sun/management/VMOption$Origin;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Lcom/sun/management/VMOption$Origin;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "DEFAULT", "dsc": "Lcom/sun/management/VMOption$Origin;"}, {"acc": 16409, "nme": "VM_CREATION", "dsc": "Lcom/sun/management/VMOption$Origin;"}, {"acc": 16409, "nme": "ENVIRON_VAR", "dsc": "Lcom/sun/management/VMOption$Origin;"}, {"acc": 16409, "nme": "CONFIG_FILE", "dsc": "Lcom/sun/management/VMOption$Origin;"}, {"acc": 16409, "nme": "MANAGEMENT", "dsc": "Lcom/sun/management/VMOption$Origin;"}, {"acc": 16409, "nme": "ERGONOMIC", "dsc": "Lcom/sun/management/VMOption$Origin;"}, {"acc": 16409, "nme": "ATTACH_ON_DEMAND", "dsc": "Lcom/sun/management/VMOption$Origin;"}, {"acc": 16409, "nme": "OTHER", "dsc": "Lcom/sun/management/VMOption$Origin;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lcom/sun/management/VMOption$Origin;"}]}, "classes/com/sun/management/internal/DiagnosticCommandImpl$Wrapper.class": {"ver": 68, "acc": 32, "nme": "com/sun/management/internal/DiagnosticCommandImpl$Wrapper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/sun/management/internal/DiagnosticCommandImpl;Ljava/lang/String;Ljava/lang/String;Lcom/sun/management/internal/DiagnosticCommandInfo;)V", "exs": ["java/lang/InstantiationException"]}, {"nme": "execute", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": [{"acc": 0, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "cmd", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "info", "dsc": "Lcom/sun/management/internal/DiagnosticCommandInfo;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lcom/sun/management/internal/DiagnosticCommandImpl;"}]}, "classes/com/sun/management/internal/VirtualThreadSchedulerImpls.class": {"ver": 68, "acc": 33, "nme": "com/sun/management/internal/VirtualThreadSchedulerImpls", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "create", "acc": 9, "dsc": "()Ljdk/management/VirtualThreadSchedulerMXBean;"}], "flds": []}, "classes/com/sun/management/internal/DiagnosticCommandImpl.class": {"ver": 68, "acc": 33, "nme": "com/sun/management/internal/DiagnosticCommandImpl", "super": "sun/management/NotificationEmitterSupport", "mthds": [{"nme": "getDiagnosticCommandMBean", "acc": 40, "dsc": "()Lcom/sun/management/DiagnosticCommandMBean;"}, {"nme": "getAttribute", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["javax/management/AttributeNotFoundException", "javax/management/MBeanException", "javax/management/ReflectionException"]}, {"nme": "setAttribute", "acc": 1, "dsc": "(Ljavax/management/Attribute;)V", "exs": ["javax/management/AttributeNotFoundException", "javax/management/InvalidAttributeValueException", "javax/management/MBeanException", "javax/management/ReflectionException"]}, {"nme": "getAttributes", "acc": 1, "dsc": "([Lja<PERSON>/lang/String;)Ljavax/management/AttributeList;"}, {"nme": "setAttributes", "acc": 1, "dsc": "(Ljavax/management/AttributeList;)Ljavax/management/AttributeList;"}, {"nme": "<init>", "acc": 0, "dsc": "(Lsun/management/VMManagement;)V"}, {"nme": "getMBeanInfo", "acc": 1, "dsc": "()Ljavax/management/MBeanInfo;"}, {"nme": "invoke", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/Object;[Ljava/lang/String;)<PERSON>ja<PERSON>/lang/Object;", "exs": ["javax/management/MBeanException", "javax/management/ReflectionException"]}, {"nme": "transform", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "commandDescriptor", "acc": 2, "dsc": "(Lcom/sun/management/internal/DiagnosticCommandImpl$Wrapper;)Ljavax/management/Descriptor;", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "sanitiseType", "acc": 26, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getNotificationInfo", "acc": 1, "dsc": "()[Ljavax/management/MBeanNotificationInfo;"}, {"nme": "getNextSeqNumber", "acc": 10, "dsc": "()J"}, {"nme": "createDiagnosticFrameworkNotification", "acc": 2, "dsc": "()V"}, {"nme": "addNotificationListener", "acc": 33, "dsc": "(Ljavax/management/NotificationListener;Ljavax/management/NotificationFilter;Ljava/lang/Object;)V"}, {"nme": "removeNotificationListener", "acc": 33, "dsc": "(Ljavax/management/NotificationListener;)V", "exs": ["javax/management/ListenerNotFoundException"]}, {"nme": "removeNotificationListener", "acc": 33, "dsc": "(Ljavax/management/NotificationListener;Ljavax/management/NotificationFilter;Ljava/lang/Object;)V", "exs": ["javax/management/ListenerNotFoundException"]}, {"nme": "setNotificationEnabled", "acc": 258, "dsc": "(Z)V"}, {"nme": "getDiagnosticCommands", "acc": 258, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDiagnosticCommandInfo", "acc": 258, "dsc": "([Ljava/lang/String;)[Lcom/sun/management/internal/DiagnosticCommandInfo;"}, {"nme": "executeDiagnosticCommand", "acc": 258, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "jvm", "dsc": "Lsun/management/VMManagement;"}, {"acc": 66, "nme": "wrappers", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lcom/sun/management/internal/DiagnosticCommandImpl$Wrapper;>;"}, {"acc": 26, "nme": "strClassName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "strArrayClassName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "isSupported", "dsc": "Z"}, {"acc": 10, "nme": "diagCommandMBean", "dsc": "Lcom/sun/management/internal/DiagnosticCommandImpl;"}, {"acc": 26, "nme": "publicTypes", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "notifName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "javax.management.Notification"}, {"acc": 26, "nme": "diagFramNotifTypes", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "notifInfo", "dsc": "[Ljavax/management/MBeanNotificationInfo;"}, {"acc": 10, "nme": "seqNumber", "dsc": "J"}]}, "classes/com/sun/management/internal/HotSpotThreadImpl.class": {"ver": 68, "acc": 33, "nme": "com/sun/management/internal/HotSpotThreadImpl", "super": "sun/management/ThreadImpl", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lsun/management/VMManagement;)V"}, {"nme": "isThreadAllocatedMemorySupported", "acc": 1, "dsc": "()Z"}, {"nme": "isThreadAllocatedMemoryEnabled", "acc": 1, "dsc": "()Z"}, {"nme": "getThreadCpuTime", "acc": 1, "dsc": "([J)[J"}, {"nme": "getThreadUserTime", "acc": 1, "dsc": "([J)[J"}, {"nme": "getTotalThreadAllocatedBytes", "acc": 1, "dsc": "()J"}, {"nme": "getCurrentThreadAllocatedBytes", "acc": 1, "dsc": "()J"}, {"nme": "getThreadAllocatedBytes", "acc": 1, "dsc": "(J)J"}, {"nme": "getThreadAllocatedBytes", "acc": 1, "dsc": "([J)[J"}, {"nme": "setThreadAllocatedMemoryEnabled", "acc": 1, "dsc": "(Z)V"}], "flds": []}, "classes/com/sun/management/internal/PlatformMBeanProviderImpl.class": {"ver": 68, "acc": 49, "nme": "com/sun/management/internal/PlatformMBeanProviderImpl", "super": "sun/management/spi/PlatformMBeanProvider", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getPlatformComponentList", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lsun/management/spi/PlatformMBeanProvider$PlatformComponent<*>;>;"}, {"nme": "init", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lsun/management/spi/PlatformMBeanProvider$PlatformComponent<*>;>;"}, {"nme": "getDiagnosticMXBean", "acc": 42, "dsc": "()Lcom/sun/management/HotSpotDiagnosticMXBean;"}, {"nme": "getOperatingSystemMXBean", "acc": 42, "dsc": "()Ljava/lang/management/OperatingSystemMXBean;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "DIAGNOSTIC_COMMAND_MBEAN_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.management:type=DiagnosticCommand"}, {"acc": 18, "nme": "mxbeanList", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lsun/management/spi/PlatformMBeanProvider$PlatformComponent<*>;>;"}, {"acc": 10, "nme": "hsDiagMBean", "dsc": "Lcom/sun/management/internal/HotSpotDiagnostic;"}, {"acc": 10, "nme": "osMBean", "dsc": "Ljava/lang/management/OperatingSystemMXBean;"}]}, "classes/com/sun/management/internal/GcInfoBuilder.class": {"ver": 68, "acc": 33, "nme": "com/sun/management/internal/GcInfoBuilder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/management/GarbageCollectorMXBean;[Ljava/lang/String;)V"}, {"nme": "getLastGcInfo", "acc": 0, "dsc": "()Lcom/sun/management/GcInfo;"}, {"nme": "getPoolNames", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getGcExtItemCount", "acc": 0, "dsc": "()I"}, {"nme": "getGcInfoCompositeType", "acc": 32, "dsc": "()Ljavax/management/openmbean/CompositeType;"}, {"nme": "getItemNames", "acc": 32, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getNumGcExtAttributes", "acc": 258, "dsc": "(Ljava/lang/management/GarbageCollectorMXBean;)I"}, {"nme": "fillGcAttributeInfo", "acc": 258, "dsc": "(<PERSON><PERSON><PERSON>/lang/management/GarbageCollectorMXBean;I[Ljava/lang/String;[C[Ljava/lang/String;)V"}, {"nme": "getLastGcInfo0", "acc": 258, "dsc": "(Ljava/lang/management/GarbageCollectorMXBean;I[Ljava/lang/Object;[C[Ljava/lang/management/MemoryUsage;[Ljava/lang/management/MemoryUsage;)Lcom/sun/management/GcInfo;"}], "flds": [{"acc": 18, "nme": "gc", "dsc": "Ljava/lang/management/GarbageCollectorMXBean;"}, {"acc": 18, "nme": "poolNames", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "allItemNames", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "gcInfoCompositeType", "dsc": "Ljavax/management/openmbean/CompositeType;"}, {"acc": 18, "nme": "gcExtItemCount", "dsc": "I"}, {"acc": 18, "nme": "gcExtItemNames", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "gcExtItemDescs", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "gcExtItemTypes", "dsc": "[C"}]}, "classes/com/sun/management/internal/PlatformMBeanProviderImpl$6.class": {"ver": 68, "acc": 32, "nme": "com/sun/management/internal/PlatformMBeanProviderImpl$6", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/sun/management/internal/PlatformMBeanProviderImpl;Lcom/sun/management/DiagnosticCommandMBean;)V", "sig": "()V"}, {"nme": "mbeanInterfaceNames", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "mbeanInterfaces", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljava/lang/Class<+Ljavax/management/DynamicMBean;>;>;"}, {"nme": "getObjectNamePattern", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "nameToMBeanMap", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljavax/management/DynamicMBean;>;"}], "flds": [{"acc": 16, "nme": "dynamicMBeanInterfaceNames", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 4112, "nme": "val$diagMBean", "dsc": "Lcom/sun/management/DiagnosticCommandMBean;"}]}, "classes/com/sun/management/HotSpotDiagnosticMXBean$ThreadDumpFormat.class": {"ver": 68, "acc": 16433, "nme": "com/sun/management/HotSpotDiagnosticMXBean$ThreadDumpFormat", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lcom/sun/management/HotSpotDiagnosticMXBean$ThreadDumpFormat;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Lcom/sun/management/HotSpotDiagnosticMXBean$ThreadDumpFormat;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Lcom/sun/management/HotSpotDiagnosticMXBean$ThreadDumpFormat;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "TEXT_PLAIN", "dsc": "Lcom/sun/management/HotSpotDiagnosticMXBean$ThreadDumpFormat;"}, {"acc": 16409, "nme": "JSON", "dsc": "Lcom/sun/management/HotSpotDiagnosticMXBean$ThreadDumpFormat;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lcom/sun/management/HotSpotDiagnosticMXBean$ThreadDumpFormat;"}]}, "classes/com/sun/management/internal/PlatformMBeanProviderImpl$1.class": {"ver": 68, "acc": 32, "nme": "com/sun/management/internal/PlatformMBeanProviderImpl$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/sun/management/internal/PlatformMBeanProviderImpl;)V"}, {"nme": "mbeanInterfaces", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljava/lang/Class<+Ljava/lang/management/MemoryManagerMXBean;>;>;"}, {"nme": "mbeanInterfaceNames", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "getObjectNamePattern", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isSingleton", "acc": 1, "dsc": "()Z"}, {"nme": "nameToMBeanMap", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/management/MemoryManagerMXBean;>;"}], "flds": [{"acc": 18, "nme": "garbageCollectorMXBeanInterfaceNames", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}]}, "classes/com/sun/management/internal/PlatformMBeanProviderImpl$2.class": {"ver": 68, "acc": 32, "nme": "com/sun/management/internal/PlatformMBeanProviderImpl$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/sun/management/internal/PlatformMBeanProviderImpl;)V"}, {"nme": "mbeanInterfaces", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljava/lang/Class<+Ljava/lang/management/ThreadMXBean;>;>;"}, {"nme": "mbeanInterfaceNames", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "getObjectNamePattern", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "nameToMBeanMap", "acc": 33, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/management/ThreadMXBean;>;"}], "flds": [{"acc": 18, "nme": "threadMXBeanInterfaceNames", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 2, "nme": "threadMBean", "dsc": "Lcom/sun/management/ThreadMXBean;"}]}, "classes/com/sun/management/DiagnosticCommandMBean.class": {"ver": 68, "acc": 1537, "nme": "com/sun/management/DiagnosticCommandMBean", "super": "java/lang/Object", "mthds": [], "flds": []}, "classes/com/sun/management/internal/DiagnosticCommandImpl$OperationInfoComparator.class": {"ver": 68, "acc": 32, "nme": "com/sun/management/internal/DiagnosticCommandImpl$OperationInfoComparator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "compare", "acc": 1, "dsc": "(Ljavax/management/MBeanOperationInfo;Ljavax/management/MBeanOperationInfo;)I"}, {"nme": "compare", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)I"}], "flds": []}, "classes/com/sun/management/HotSpotDiagnosticMXBean.class": {"ver": 68, "acc": 1537, "nme": "com/sun/management/HotSpotDiagnosticMXBean", "super": "java/lang/Object", "mthds": [{"nme": "dumpHeap", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)V", "exs": ["java/io/IOException"]}, {"nme": "getDiagnosticOptions", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lcom/sun/management/VMOption;>;"}, {"nme": "getVMOption", "acc": 1025, "dsc": "(Ljava/lang/String;)Lcom/sun/management/VMOption;"}, {"nme": "setVMOption", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "dumpThreads", "acc": 1, "dsc": "(Ljava/lang/String;Lcom/sun/management/HotSpotDiagnosticMXBean$ThreadDumpFormat;)V", "exs": ["java/io/IOException"]}], "flds": []}, "classes/com/sun/management/internal/VMOptionCompositeData.class": {"ver": 68, "acc": 33, "nme": "com/sun/management/internal/VMOptionCompositeData", "super": "sun/management/LazyCompositeData", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lcom/sun/management/VMOption;)V"}, {"nme": "getVMOption", "acc": 1, "dsc": "()Lcom/sun/management/VMOption;"}, {"nme": "toCompositeData", "acc": 9, "dsc": "(Lcom/sun/management/VMOption;)Ljavax/management/openmbean/CompositeData;"}, {"nme": "getCompositeData", "acc": 4, "dsc": "()Ljavax/management/openmbean/CompositeData;"}, {"nme": "getVMOptionCompositeType", "acc": 8, "dsc": "()Ljavax/management/openmbean/CompositeType;"}, {"nme": "getName", "acc": 9, "dsc": "(Ljavax/management/openmbean/CompositeData;)Ljava/lang/String;"}, {"nme": "getValue", "acc": 9, "dsc": "(Ljavax/management/openmbean/CompositeData;)Ljava/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 9, "dsc": "(Ljavax/management/openmbean/CompositeData;)Lcom/sun/management/VMOption$Origin;"}, {"nme": "isWriteable", "acc": 9, "dsc": "(Ljavax/management/openmbean/CompositeData;)Z"}, {"nme": "validateCompositeData", "acc": 9, "dsc": "(Ljavax/management/openmbean/CompositeData;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "option", "dsc": "Lcom/sun/management/VMOption;"}, {"acc": 26, "nme": "vmOptionCompositeType", "dsc": "Ljavax/management/openmbean/CompositeType;"}, {"acc": 26, "nme": "NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "name"}, {"acc": 26, "nme": "VALUE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "value"}, {"acc": 26, "nme": "WRITEABLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "writeable"}, {"acc": 26, "nme": "ORIGIN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "origin"}, {"acc": 26, "nme": "vmOptionItemNames", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -2395573975093578470}]}, "classes/com/sun/management/OperatingSystemMXBean.class": {"ver": 68, "acc": 1537, "nme": "com/sun/management/OperatingSystemMXBean", "super": "java/lang/Object", "mthds": [{"nme": "getCommittedVirtualMemorySize", "acc": 1025, "dsc": "()J"}, {"nme": "getTotalSwapSpaceSize", "acc": 1025, "dsc": "()J"}, {"nme": "getFreeSwapSpaceSize", "acc": 1025, "dsc": "()J"}, {"nme": "getProcessCpuTime", "acc": 1025, "dsc": "()J"}, {"nme": "getFreePhysicalMemorySize", "acc": 131073, "dsc": "()J", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "14"]}]}, {"nme": "getFreeMemorySize", "acc": 1025, "dsc": "()J"}, {"nme": "getTotalPhysicalMemorySize", "acc": 131073, "dsc": "()J", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "14"]}]}, {"nme": "getTotalMemorySize", "acc": 1025, "dsc": "()J"}, {"nme": "getSystemCpuLoad", "acc": 131073, "dsc": "()D", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "14"]}]}, {"nme": "getCpuLoad", "acc": 1025, "dsc": "()D"}, {"nme": "getProcessCpuLoad", "acc": 1025, "dsc": "()D"}], "flds": []}, "classes/com/sun/management/internal/PlatformMBeanProviderImpl$4.class": {"ver": 68, "acc": 32, "nme": "com/sun/management/internal/PlatformMBeanProviderImpl$4", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/sun/management/internal/PlatformMBeanProviderImpl;)V"}, {"nme": "mbeanInterfaces", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljava/lang/Class<+Ljava/lang/management/OperatingSystemMXBean;>;>;"}, {"nme": "mbeanInterfaceNames", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "getObjectNamePattern", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "nameToMBeanMap", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/management/OperatingSystemMXBean;>;"}], "flds": [{"acc": 18, "nme": "operatingSystemMXBeanInterfaceNames", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}]}, "classes/com/sun/management/internal/HotSpotDiagnostic$1.class": {"ver": 68, "acc": 4128, "nme": "com/sun/management/internal/HotSpotDiagnostic$1", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$com$sun$management$HotSpotDiagnosticMXBean$ThreadDumpFormat", "dsc": "[I"}]}, "classes/com/sun/management/GcInfo.class": {"ver": 68, "acc": 33, "nme": "com/sun/management/GcInfo", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lcom/sun/management/internal/GcInfoBuilder;JJJ[Ljava/lang/management/MemoryUsage;[Ljava/lang/management/MemoryUsage;[Ljava/lang/Object;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(Ljavax/management/openmbean/CompositeData;)V"}, {"nme": "getId", "acc": 1, "dsc": "()J"}, {"nme": "getStartTime", "acc": 1, "dsc": "()J"}, {"nme": "getEndTime", "acc": 1, "dsc": "()J"}, {"nme": "getDuration", "acc": 1, "dsc": "()J"}, {"nme": "getMemoryUsageBeforeGc", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/management/MemoryUsage;>;"}, {"nme": "getMemoryUsageAfterGc", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/management/MemoryUsage;>;"}, {"nme": "from", "acc": 9, "dsc": "(Ljavax/management/openmbean/CompositeData;)Lcom/sun/management/GcInfo;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "containsValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "get", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getAll", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)[Lja<PERSON>/lang/Object;"}, {"nme": "getCompositeType", "acc": 1, "dsc": "()Ljavax/management/openmbean/CompositeType;"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "values", "acc": 1, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<*>;"}, {"nme": "toCompositeData", "acc": 1, "dsc": "(Ljavax/management/openmbean/CompositeType;)Ljavax/management/openmbean/CompositeData;"}], "flds": [{"acc": 18, "nme": "index", "dsc": "J"}, {"acc": 18, "nme": "startTime", "dsc": "J"}, {"acc": 18, "nme": "endTime", "dsc": "J"}, {"acc": 18, "nme": "usageBeforeGc", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/management/MemoryUsage;>;"}, {"acc": 18, "nme": "usageAfterGc", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/management/MemoryUsage;>;"}, {"acc": 18, "nme": "extAttributes", "dsc": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 18, "nme": "cdata", "dsc": "Ljavax/management/openmbean/CompositeData;"}, {"acc": 18, "nme": "builder", "dsc": "Lcom/sun/management/internal/GcInfoBuilder;"}]}}}}