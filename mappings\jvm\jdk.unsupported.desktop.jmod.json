{"md5": "50fb89a37b405fe9ea978b1630de949a", "sha2": "6bed0a5fdeaf948774570ac65dbd677b03f3ee0b", "sha256": "98f36775fcdd6b4fa3d650fa3dc024e8ace7bd39fb32c35aee75655d981a18a5", "contents": {"classes": {"classes/jdk/swing/interop/LightweightContentWrapper.class": {"ver": 68, "acc": 1057, "nme": "jdk/swing/interop/LightweightContentWrapper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "get<PERSON>ontent", "acc": 0, "dsc": "()Ljdk/swing/interop/LightweightContentWrapper$LightweightContentProxy;"}, {"nme": "imageBufferReset", "acc": 1025, "dsc": "([IIIIII)V"}, {"nme": "imageBufferReset", "acc": 1025, "dsc": "([IIIIIIDD)V"}, {"nme": "getComponent", "acc": 1025, "dsc": "()Ljavax/swing/JComponent;"}, {"nme": "paintLock", "acc": 1025, "dsc": "()V"}, {"nme": "paintUnlock", "acc": 1025, "dsc": "()V"}, {"nme": "imageReshaped", "acc": 1025, "dsc": "(IIII)V"}, {"nme": "imageUpdated", "acc": 1025, "dsc": "(IIII)V"}, {"nme": "focusGrabbed", "acc": 1025, "dsc": "()V"}, {"nme": "focusUngrabbed", "acc": 1025, "dsc": "()V"}, {"nme": "preferredSizeChanged", "acc": 1025, "dsc": "(II)V"}, {"nme": "maximumSizeChanged", "acc": 1025, "dsc": "(II)V"}, {"nme": "minimumSizeChanged", "acc": 1025, "dsc": "(II)V"}, {"nme": "createDragGestureRecognizer", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/awt/dnd/DragSource;Ljava/awt/Component;ILjava/awt/dnd/DragGestureListener;)Ljava/awt/dnd/DragGestureRecognizer;", "sig": "<T:Ljava/awt/dnd/DragGestureRecognizer;>(Ljava/lang/Class<TT;>;Ljava/awt/dnd/DragSource;Ljava/awt/Component;ILjava/awt/dnd/DragGestureListener;)TT;"}, {"nme": "createDragSourceContext", "acc": 1025, "dsc": "(Ljava/awt/dnd/DragGestureEvent;)Ljdk/swing/interop/DragSourceContextWrapper;", "exs": ["java/awt/dnd/InvalidDnDOperationException"]}, {"nme": "addDropTarget", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/awt/dnd/DropTarget;)V"}, {"nme": "removeDropTarget", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/awt/dnd/DropTarget;)V"}], "flds": [{"acc": 2, "nme": "lwCnt", "dsc": "Ljdk/swing/interop/LightweightContentWrapper$LightweightContentProxy;"}]}, "classes/module-info.class": {"ver": 68, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "classes/jdk/swing/interop/LightweightFrameWrapper.class": {"ver": 68, "acc": 33, "nme": "jdk/swing/interop/LightweightFrameWrapper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getLightweightFrame", "acc": 2, "dsc": "()Lsun/swing/JLightweightFrame;"}, {"nme": "notifyDisplayChanged", "acc": 1, "dsc": "(I)V"}, {"nme": "notifyDisplayChanged", "acc": 1, "dsc": "(DD)V"}, {"nme": "overrideNativeWindowHandle", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/Runnable;)V"}, {"nme": "setHostBounds", "acc": 1, "dsc": "(IIII)V"}, {"nme": "dispose", "acc": 1, "dsc": "()V"}, {"nme": "addWindowFocusListener", "acc": 1, "dsc": "(Ljava/awt/event/WindowFocusListener;)V"}, {"nme": "setVisible", "acc": 1, "dsc": "(Z)V"}, {"nme": "setBounds", "acc": 1, "dsc": "(IIII)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Ljdk/swing/interop/LightweightContentWrapper;)V"}, {"nme": "emulateActivation", "acc": 1, "dsc": "(Z)V"}, {"nme": "createMouseEvent", "acc": 1, "dsc": "(Ljdk/swing/interop/LightweightFrameWrapper;IJIIIIIIZI)Ljava/awt/event/MouseEvent;"}, {"nme": "createMouseWheelEvent", "acc": 1, "dsc": "(Ljdk/swing/interop/LightweightFrameWrapper;IIII)Ljava/awt/event/MouseWheelEvent;"}, {"nme": "createKeyEvent", "acc": 1, "dsc": "(Ljdk/swing/interop/LightweightFrameWrapper;IJIIC)Ljava/awt/event/KeyEvent;"}, {"nme": "createUngrabEvent", "acc": 1, "dsc": "(Ljdk/swing/interop/LightweightFrameWrapper;)Ljava/awt/AWTEvent;"}, {"nme": "findComponentAt", "acc": 1, "dsc": "(Ljdk/swing/interop/LightweightFrameWrapper;IIZ)Ljava/awt/Component;"}, {"nme": "isCompEqual", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/awt/Component;Ljdk/swing/interop/LightweightFrameWrapper;)Z"}], "flds": [{"acc": 0, "nme": "lwFrame", "dsc": "Lsun/swing/JLightweightFrame;"}]}, "classes/jdk/swing/interop/DispatcherWrapper.class": {"ver": 68, "acc": 1057, "nme": "jdk/swing/interop/DispatcherWrapper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "isDispatchThread", "acc": 1025, "dsc": "()Z"}, {"nme": "scheduleDispatch", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Runnable;)V"}, {"nme": "createSecondaryLoop", "acc": 1025, "dsc": "()<PERSON>java/awt/SecondaryLoop;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/awt/EventQueue;Ljdk/swing/interop/DispatcherWrapper;)V"}], "flds": [{"acc": 2, "nme": "fwd", "dsc": "Ljdk/swing/interop/DispatcherWrapper$DispatcherProxy;"}]}, "classes/jdk/swing/interop/LightweightContentWrapper$LightweightContentProxy.class": {"ver": 68, "acc": 32, "nme": "jdk/swing/interop/LightweightContentWrapper$LightweightContentProxy", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljdk/swing/interop/LightweightContentWrapper;)V"}, {"nme": "getComponent", "acc": 1, "dsc": "()Ljavax/swing/JComponent;"}, {"nme": "paintLock", "acc": 1, "dsc": "()V"}, {"nme": "paintUnlock", "acc": 1, "dsc": "()V"}, {"nme": "imageBufferReset", "acc": 1, "dsc": "([IIIIII)V"}, {"nme": "imageBufferReset", "acc": 1, "dsc": "([IIIIIIDD)V"}, {"nme": "imageReshaped", "acc": 1, "dsc": "(IIII)V"}, {"nme": "imageUpdated", "acc": 1, "dsc": "(IIII)V"}, {"nme": "focusGrabbed", "acc": 1, "dsc": "()V"}, {"nme": "focusUngrabbed", "acc": 1, "dsc": "()V"}, {"nme": "preferredSizeChanged", "acc": 1, "dsc": "(II)V"}, {"nme": "maximumSizeChanged", "acc": 1, "dsc": "(II)V"}, {"nme": "minimumSizeChanged", "acc": 1, "dsc": "(II)V"}, {"nme": "createDragGestureRecognizer", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/awt/dnd/DragSource;Ljava/awt/Component;ILjava/awt/dnd/DragGestureListener;)Ljava/awt/dnd/DragGestureRecognizer;", "sig": "<T:Ljava/awt/dnd/DragGestureRecognizer;>(Ljava/lang/Class<TT;>;Ljava/awt/dnd/DragSource;Ljava/awt/Component;ILjava/awt/dnd/DragGestureListener;)TT;"}, {"nme": "createDragSourceContextPeer", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/awt/dnd/DragGestureEvent;)Ljava/awt/dnd/peer/DragSourceContextPeer;", "exs": ["java/awt/dnd/InvalidDnDOperationException"]}, {"nme": "addDropTarget", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/awt/dnd/DropTarget;)V"}, {"nme": "removeDropTarget", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/awt/dnd/DropTarget;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Ljdk/swing/interop/LightweightContentWrapper;"}]}, "classes/jdk/swing/interop/SwingInterOpUtils.class": {"ver": 68, "acc": 33, "nme": "jdk/swing/interop/SwingInterOpUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "postEvent", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Ljava/awt/AWTEvent;)V"}, {"nme": "grab", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/awt/Toolkit;<PERSON><PERSON><PERSON>/awt/Window;)V"}, {"nme": "ungrab", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/awt/Toolkit;<PERSON><PERSON><PERSON>/awt/Window;)V"}, {"nme": "isUngrabEvent", "acc": 9, "dsc": "(Ljava/awt/AWTEvent;)Z"}], "flds": [{"acc": 25, "nme": "GRAB_EVENT_MASK", "dsc": "I", "val": -2147483648}]}, "classes/jdk/swing/interop/DispatcherWrapper$DispatcherProxy.class": {"ver": 68, "acc": 32, "nme": "jdk/swing/interop/DispatcherWrapper$DispatcherProxy", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljdk/swing/interop/DispatcherWrapper;)V"}, {"nme": "isDispatchThread", "acc": 1, "dsc": "()Z"}, {"nme": "scheduleDispatch", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Runnable;)V"}, {"nme": "createSecondaryLoop", "acc": 1, "dsc": "()<PERSON>java/awt/SecondaryLoop;"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Ljdk/swing/interop/DispatcherWrapper;"}]}, "classes/jdk/swing/interop/DragSourceContextWrapper$DragSourceContextPeerProxy.class": {"ver": 68, "acc": 32, "nme": "jdk/swing/interop/DragSourceContextWrapper$DragSourceContextPeerProxy", "super": "sun/awt/dnd/SunDragSourceContextPeer", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljdk/swing/interop/DragSourceContextWrapper;Ljava/awt/dnd/DragGestureEvent;)V"}, {"nme": "startDrag", "acc": 4, "dsc": "(Ljava/awt/datatransfer/Transferable;[JLjava/util/Map;)V", "sig": "(Ljava/awt/datatransfer/Transferable;[JLjava/util/Map<Ljava/lang/Long;Ljava/awt/datatransfer/DataFlavor;>;)V"}, {"nme": "setNativeCursor", "acc": 4, "dsc": "(J<PERSON><PERSON><PERSON>/awt/<PERSON>ursor;I)V"}, {"nme": "startSecondaryEventLoop", "acc": 1, "dsc": "()V"}, {"nme": "quitSecondaryEventLoop", "acc": 1, "dsc": "()V"}, {"nme": "dragDropFinishedCall", "acc": 4, "dsc": "(ZIII)V"}, {"nme": "getDragSourceContextCall", "acc": 4, "dsc": "()Ljava/awt/dnd/DragSourceContext;"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Ljdk/swing/interop/DragSourceContextWrapper;"}]}, "classes/jdk/swing/interop/DragSourceContextWrapper.class": {"ver": 68, "acc": 1057, "nme": "jdk/swing/interop/DragSourceContextWrapper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/awt/dnd/DragGestureEvent;)V"}, {"nme": "getPeer", "acc": 0, "dsc": "()Ljava/awt/dnd/peer/DragSourceContextPeer;"}, {"nme": "convertModifiersToDropAction", "acc": 9, "dsc": "(II)I"}, {"nme": "setNativeCursor", "acc": 1028, "dsc": "(<PERSON><PERSON><PERSON>/awt/<PERSON><PERSON>or;I)V"}, {"nme": "startDrag", "acc": 1028, "dsc": "(Ljava/awt/datatransfer/Transferable;[JLjava/util/Map;)V", "sig": "(Ljava/awt/datatransfer/Transferable;[JLjava/util/Map<Ljava/lang/Long;Ljava/awt/datatransfer/DataFlavor;>;)V"}, {"nme": "startSecondaryEventLoop", "acc": 1025, "dsc": "()V"}, {"nme": "quitSecondaryEventLoop", "acc": 1025, "dsc": "()V"}, {"nme": "dragDropFinished", "acc": 1, "dsc": "(ZIII)V"}, {"nme": "getDragSourceContext", "acc": 1, "dsc": "()Ljava/awt/dnd/DragSourceContext;"}], "flds": [{"acc": 2, "nme": "dsp", "dsc": "Ljdk/swing/interop/DragSourceContextWrapper$DragSourceContextPeerProxy;"}]}, "classes/jdk/swing/interop/internal/InteropProviderImpl.class": {"ver": 68, "acc": 33, "nme": "jdk/swing/interop/internal/InteropProviderImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "classes/jdk/swing/interop/DropTargetContextWrapper.class": {"ver": 68, "acc": 1057, "nme": "jdk/swing/interop/DropTargetContextWrapper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "setDropTargetContext", "acc": 1, "dsc": "(Ljava/awt/dnd/DropTargetContext;Ljdk/swing/interop/DropTargetContextWrapper;)V"}, {"nme": "reset", "acc": 1, "dsc": "(Ljava/awt/dnd/DropTargetContext;)V"}, {"nme": "setTargetActions", "acc": 1025, "dsc": "(I)V"}, {"nme": "getTargetActions", "acc": 1025, "dsc": "()I"}, {"nme": "getDropTarget", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/awt/dnd/DropTarget;"}, {"nme": "getTransferDataFlavors", "acc": 1025, "dsc": "()[Ljava/awt/datatransfer/DataFlavor;"}, {"nme": "getTransferable", "acc": 1025, "dsc": "()Ljava/awt/datatransfer/Transferable;", "exs": ["java/awt/dnd/InvalidDnDOperationException"]}, {"nme": "isTransferableJVMLocal", "acc": 1025, "dsc": "()Z"}, {"nme": "acceptDrag", "acc": 1025, "dsc": "(I)V"}, {"nme": "rejectDrag", "acc": 1025, "dsc": "()V"}, {"nme": "acceptDrop", "acc": 1025, "dsc": "(I)V"}, {"nme": "rejectDrop", "acc": 1025, "dsc": "()V"}, {"nme": "dropComplete", "acc": 1025, "dsc": "(Z)V"}], "flds": [{"acc": 2, "nme": "dcp", "dsc": "Ljdk/swing/interop/DropTargetContextWrapper$DropTargetContextPeerProxy;"}]}, "classes/jdk/swing/interop/DropTargetContextWrapper$DropTargetContextPeerProxy.class": {"ver": 68, "acc": 32, "nme": "jdk/swing/interop/DropTargetContextWrapper$DropTargetContextPeerProxy", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljdk/swing/interop/DropTargetContextWrapper;)V"}, {"nme": "setTargetActions", "acc": 1, "dsc": "(I)V"}, {"nme": "getTargetActions", "acc": 1, "dsc": "()I"}, {"nme": "getDropTarget", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/awt/dnd/DropTarget;"}, {"nme": "getTransferDataFlavors", "acc": 1, "dsc": "()[Ljava/awt/datatransfer/DataFlavor;"}, {"nme": "getTransferable", "acc": 1, "dsc": "()Ljava/awt/datatransfer/Transferable;", "exs": ["java/awt/dnd/InvalidDnDOperationException"]}, {"nme": "isTransferableJVMLocal", "acc": 1, "dsc": "()Z"}, {"nme": "acceptDrag", "acc": 1, "dsc": "(I)V"}, {"nme": "rejectDrag", "acc": 1, "dsc": "()V"}, {"nme": "acceptDrop", "acc": 1, "dsc": "(I)V"}, {"nme": "rejectDrop", "acc": 1, "dsc": "()V"}, {"nme": "dropComplete", "acc": 1, "dsc": "(Z)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Ljdk/swing/interop/DropTargetContextWrapper;"}]}}}}