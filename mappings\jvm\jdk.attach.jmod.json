{"md5": "9475897c2fee1e7881aa8d4693975b60", "sha2": "f6721bc1adccdd49641fbc52c503d105af60f34f", "sha256": "1334caceae349f0d053cabe06287380a294a2573d6138660e083adbaaf6480cc", "contents": {"classes": {"classes/com/sun/tools/attach/AttachOperationFailedException.class": {"ver": 68, "acc": 33, "nme": "com/sun/tools/attach/AttachOperationFailedException", "super": "java/io/IOException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 2140308168167478043}]}, "classes/com/sun/tools/attach/AttachNotSupportedException.class": {"ver": 68, "acc": 33, "nme": "com/sun/tools/attach/AttachNotSupportedException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 24, "nme": "serialVersionUID", "dsc": "J", "val": 3391824968260177264}]}, "classes/sun/tools/attach/HotSpotVirtualMachine$AttachOutputStream.class": {"ver": 68, "acc": 1537, "nme": "sun/tools/attach/HotSpotVirtualMachine$AttachOutputStream", "super": "java/lang/Object", "mthds": [{"nme": "write", "acc": 1025, "dsc": "([BII)V", "exs": ["java/io/IOException"]}], "flds": []}, "classes/com/sun/tools/attach/VirtualMachineDescriptor.class": {"ver": 68, "acc": 33, "nme": "com/sun/tools/attach/VirtualMachineDescriptor", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/sun/tools/attach/spi/AttachProvider;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lcom/sun/tools/attach/spi/AttachProvider;Ljava/lang/String;)V"}, {"nme": "provider", "acc": 1, "dsc": "()Lcom/sun/tools/attach/spi/AttachProvider;"}, {"nme": "id", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "displayName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "provider", "dsc": "Lcom/sun/tools/attach/spi/AttachProvider;"}, {"acc": 2, "nme": "id", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "displayName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 66, "nme": "hash", "dsc": "I"}]}, "classes/com/sun/tools/attach/spi/AttachProvider.class": {"ver": 68, "acc": 1057, "nme": "com/sun/tools/attach/spi/AttachProvider", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "name", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "type", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "attachVirtualMachine", "acc": 1025, "dsc": "(Ljava/lang/String;)Lcom/sun/tools/attach/VirtualMachine;", "exs": ["com/sun/tools/attach/AttachNotSupportedException", "java/io/IOException"]}, {"nme": "attachVirtualMachine", "acc": 1, "dsc": "(Lcom/sun/tools/attach/VirtualMachineDescriptor;)Lcom/sun/tools/attach/VirtualMachine;", "exs": ["com/sun/tools/attach/AttachNotSupportedException", "java/io/IOException"]}, {"nme": "listVirtualMachines", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lcom/sun/tools/attach/VirtualMachineDescriptor;>;"}, {"nme": "providers", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lcom/sun/tools/attach/spi/AttachProvider;>;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "lock", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 10, "nme": "providers", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lcom/sun/tools/attach/spi/AttachProvider;>;"}]}, "classes/sun/tools/attach/VirtualMachineImpl$SocketInputStreamImpl.class": {"ver": 68, "acc": 32, "nme": "sun/tools/attach/VirtualMachineImpl$SocketInputStreamImpl", "super": "sun/tools/attach/HotSpotVirtualMachine$SocketInputStream", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(J)V"}, {"nme": "read", "acc": 4, "dsc": "(J[BII)I", "exs": ["java/io/IOException"]}, {"nme": "close", "acc": 4, "dsc": "(J)V", "exs": ["java/io/IOException"]}], "flds": []}, "classes/sun/tools/attach/HotSpotAttachProvider.class": {"ver": 68, "acc": 1057, "nme": "sun/tools/attach/HotSpotAttachProvider", "super": "com/sun/tools/attach/spi/AttachProvider", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "listVirtualMachines", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lcom/sun/tools/attach/VirtualMachineDescriptor;>;"}, {"nme": "testAttachable", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["com/sun/tools/attach/AttachNotSupportedException"]}], "flds": []}, "classes/sun/tools/attach/VirtualMachineImpl.class": {"ver": 68, "acc": 33, "nme": "sun/tools/attach/VirtualMachineImpl", "super": "sun/tools/attach/HotSpotVirtualMachine", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/sun/tools/attach/spi/AttachProvider;Ljava/lang/String;)V", "exs": ["com/sun/tools/attach/AttachNotSupportedException", "java/io/IOException"]}, {"nme": "detach", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "execute", "acc": 128, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/Object;)Ljava/io/InputStream;", "exs": ["com/sun/tools/attach/AgentLoadException", "java/io/IOException"]}, {"nme": "init", "acc": 264, "dsc": "()V"}, {"nme": "generateStub", "acc": 264, "dsc": "()[B"}, {"nme": "openProcess", "acc": 264, "dsc": "(I)J", "exs": ["java/io/IOException"]}, {"nme": "closeProcess", "acc": 264, "dsc": "(J)V", "exs": ["java/io/IOException"]}, {"nme": "createPipe", "acc": 264, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)J", "exs": ["java/io/IOException"]}, {"nme": "closePipe", "acc": 264, "dsc": "(J)V", "exs": ["java/io/IOException"]}, {"nme": "connectPipe", "acc": 264, "dsc": "(J)V", "exs": ["java/io/IOException"]}, {"nme": "readPipe", "acc": 264, "dsc": "(J[BII)I", "exs": ["java/io/IOException"]}, {"nme": "writePipe", "acc": 264, "dsc": "(J[BII)V", "exs": ["java/io/IOException"]}, {"nme": "enqueue", "acc": 392, "dsc": "(J[<PERSON><PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/Object;)V", "exs": ["java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 10, "nme": "stub", "dsc": "[B"}, {"acc": 66, "nme": "hProcess", "dsc": "J"}, {"acc": 2, "nme": "ver", "dsc": "I"}]}, "classes/sun/tools/attach/HotSpotVirtualMachine$SocketInputStream.class": {"ver": 68, "acc": 1056, "nme": "sun/tools/attach/HotSpotVirtualMachine$SocketInputStream", "super": "java/io/InputStream", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(J)V"}, {"nme": "read", "acc": 1028, "dsc": "(J[BII)I", "exs": ["java/io/IOException"]}, {"nme": "close", "acc": 1028, "dsc": "(J)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 33, "dsc": "()I", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 33, "dsc": "([BII)I", "exs": ["java/io/IOException"]}, {"nme": "close", "acc": 33, "dsc": "()V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 2, "nme": "fd", "dsc": "J"}]}, "classes/sun/tools/attach/HotSpotVirtualMachine.class": {"ver": 68, "acc": 1057, "nme": "sun/tools/attach/HotSpotVirtualMachine", "super": "com/sun/tools/attach/VirtualMachine", "mthds": [{"nme": "pid", "acc": 10, "dsc": "()J"}, {"nme": "<init>", "acc": 0, "dsc": "(Lcom/sun/tools/attach/spi/AttachProvider;Ljava/lang/String;)V", "exs": ["com/sun/tools/attach/AttachNotSupportedException", "java/io/IOException"]}, {"nme": "loadAgentLibrary", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["com/sun/tools/attach/AgentLoadException", "com/sun/tools/attach/AgentInitializationException", "java/io/IOException"]}, {"nme": "loadAgentLibrary", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["com/sun/tools/attach/AgentLoadException", "com/sun/tools/attach/AgentInitializationException", "java/io/IOException"]}, {"nme": "loadAgentPath", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["com/sun/tools/attach/AgentLoadException", "com/sun/tools/attach/AgentInitializationException", "java/io/IOException"]}, {"nme": "loadAgent", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["com/sun/tools/attach/AgentLoadException", "com/sun/tools/attach/AgentInitializationException", "java/io/IOException"]}, {"nme": "getSystemProperties", "acc": 1, "dsc": "()Ljava/util/Properties;", "exs": ["java/io/IOException"]}, {"nme": "getAgentProperties", "acc": 1, "dsc": "()Ljava/util/Properties;", "exs": ["java/io/IOException"]}, {"nme": "checkedKeyName", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "stripKeyName", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON>ja<PERSON>/lang/String;"}, {"nme": "startManagementAgent", "acc": 1, "dsc": "(Ljava/util/Properties;)V", "exs": ["java/io/IOException"]}, {"nme": "escape", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON>ja<PERSON>/lang/String;"}, {"nme": "startLocalManagementAgent", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "localDataDump", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "remoteDataDump", "acc": 129, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;)Ljava/io/InputStream;", "exs": ["java/io/IOException"]}, {"nme": "dumpHeap", "acc": 129, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;)Ljava/io/InputStream;", "exs": ["java/io/IOException"]}, {"nme": "heapHisto", "acc": 129, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;)Ljava/io/InputStream;", "exs": ["java/io/IOException"]}, {"nme": "setFlag", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/io/InputStream;", "exs": ["java/io/IOException"]}, {"nme": "printFlag", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/io/InputStream;", "exs": ["java/io/IOException"]}, {"nme": "executeJCmd", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/io/InputStream;", "exs": ["java/io/IOException"]}, {"nme": "execute", "acc": 1152, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/Object;)Ljava/io/InputStream;", "exs": ["com/sun/tools/attach/AgentLoadException", "java/io/IOException"]}, {"nme": "executeCommand", "acc": 129, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/Object;)Ljava/io/InputStream;", "exs": ["java/io/IOException"]}, {"nme": "detectVersion", "acc": 4, "dsc": "()I", "exs": ["java/io/IOException"]}, {"nme": "isAPIv2Enabled", "acc": 4, "dsc": "()Z"}, {"nme": "readInt", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;)I", "exs": ["java/io/IOException"]}, {"nme": "readMessage", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;)<PERSON>ja<PERSON>/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "processCompletionStatus", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/io/IOException;<PERSON><PERSON><PERSON>/lang/String;Ljava/io/InputStream;)V", "exs": ["com/sun/tools/attach/AgentLoadException", "java/io/IOException"]}, {"nme": "dataSize", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}, {"nme": "writeString", "acc": 2, "dsc": "(Lsun/tools/attach/HotSpotVirtualMachine$AttachOutputStream;Ljava/lang/Object;)V", "exs": ["java/io/IOException"]}, {"nme": "writeCommand", "acc": 132, "dsc": "(Lsun/tools/attach/HotSpotVirtualMachine$AttachOutputStream;ILjava/lang/String;[Ljava/lang/Object;)V", "exs": ["java/io/IOException"]}, {"nme": "attachTimeout", "acc": 0, "dsc": "()J"}, {"nme": "checkNulls", "acc": 140, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "lambda$startManagementAgent$1", "acc": 4098, "dsc": "(Ljava/util/Map$Entry;)Ljava/lang/String;"}, {"nme": "lambda$startManagementAgent$0", "acc": 4106, "dsc": "(Ljava/util/Map$Entry;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CURRENT_PID", "dsc": "J"}, {"acc": 26, "nme": "ALLOW_ATTACH_SELF", "dsc": "Z"}, {"acc": 26, "nme": "JNI_ENOMEM", "dsc": "I", "val": -4}, {"acc": 26, "nme": "ATTACH_ERROR_BADJAR", "dsc": "I", "val": 100}, {"acc": 26, "nme": "ATTACH_ERROR_NOTONCP", "dsc": "I", "val": 101}, {"acc": 26, "nme": "ATTACH_ERROR_STARTFAIL", "dsc": "I", "val": 102}, {"acc": 26, "nme": "ATTACH_ERROR_BADVERSION", "dsc": "I", "val": 101}, {"acc": 26, "nme": "MANAGEMENT_PREFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.management."}, {"acc": 28, "nme": "VERSION_1", "dsc": "I", "val": 1}, {"acc": 28, "nme": "VERSION_2", "dsc": "I", "val": 2}, {"acc": 10, "nme": "defaultAttachTimeout", "dsc": "J"}, {"acc": 66, "nme": "attachTimeout", "dsc": "J"}]}, "classes/com/sun/tools/attach/AgentInitializationException.class": {"ver": 68, "acc": 33, "nme": "com/sun/tools/attach/AgentInitializationException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"nme": "returnValue", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 24, "nme": "serialVersionUID", "dsc": "J", "val": -1508756333332806353}, {"acc": 2, "nme": "returnValue", "dsc": "I"}]}, "classes/module-info.class": {"ver": 68, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "classes/sun/tools/attach/VirtualMachineImpl$PipeOutputStream.class": {"ver": 68, "acc": 32, "nme": "sun/tools/attach/VirtualMachineImpl$PipeOutputStream", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(J)V"}, {"nme": "write", "acc": 1, "dsc": "([BII)V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 2, "nme": "hPipe", "dsc": "J"}]}, "classes/sun/tools/attach/AttachProviderImpl.class": {"ver": 68, "acc": 33, "nme": "sun/tools/attach/AttachProviderImpl", "super": "sun/tools/attach/HotSpotAttachProvider", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "name", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "type", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "attachVirtualMachine", "acc": 1, "dsc": "(Ljava/lang/String;)Lcom/sun/tools/attach/VirtualMachine;", "exs": ["com/sun/tools/attach/AttachNotSupportedException", "java/io/IOException"]}, {"nme": "listVirtualMachines", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lcom/sun/tools/attach/VirtualMachineDescriptor;>;"}, {"nme": "isTempPathSecure", "acc": 10, "dsc": "()Z"}, {"nme": "temp<PERSON>ath", "acc": 266, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "volumeFlags", "acc": 266, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)J"}, {"nme": "listJavaProcesses", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lcom/sun/tools/attach/VirtualMachineDescriptor;>;"}, {"nme": "enumProcesses", "acc": 266, "dsc": "([II)I"}, {"nme": "isLibraryLoadedByProcess", "acc": 266, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "FS_PERSISTENT_ACLS", "dsc": "J", "val": 8}, {"acc": 74, "nme": "wasTempPathChecked", "dsc": "Z"}, {"acc": 10, "nme": "isTempPathSecure", "dsc": "Z"}]}, "classes/sun/tools/attach/HotSpotAttachProvider$HotSpotVirtualMachineDescriptor.class": {"ver": 68, "acc": 32, "nme": "sun/tools/attach/HotSpotAttachProvider$HotSpotVirtualMachineDescriptor", "super": "com/sun/tools/attach/VirtualMachineDescriptor", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/sun/tools/attach/spi/AttachProvider;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "isAttachable", "acc": 1, "dsc": "()Z"}], "flds": []}, "classes/com/sun/tools/attach/VirtualMachine.class": {"ver": 68, "acc": 1057, "nme": "com/sun/tools/attach/VirtualMachine", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Lcom/sun/tools/attach/spi/AttachProvider;Ljava/lang/String;)V"}, {"nme": "list", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lcom/sun/tools/attach/VirtualMachineDescriptor;>;"}, {"nme": "attach", "acc": 9, "dsc": "(Ljava/lang/String;)Lcom/sun/tools/attach/VirtualMachine;", "exs": ["com/sun/tools/attach/AttachNotSupportedException", "java/io/IOException"]}, {"nme": "attach", "acc": 9, "dsc": "(Lcom/sun/tools/attach/VirtualMachineDescriptor;)Lcom/sun/tools/attach/VirtualMachine;", "exs": ["com/sun/tools/attach/AttachNotSupportedException", "java/io/IOException"]}, {"nme": "detach", "acc": 1025, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "provider", "acc": 17, "dsc": "()Lcom/sun/tools/attach/spi/AttachProvider;"}, {"nme": "id", "acc": 17, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "loadAgentLibrary", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["com/sun/tools/attach/AgentLoadException", "com/sun/tools/attach/AgentInitializationException", "java/io/IOException"]}, {"nme": "loadAgentLibrary", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["com/sun/tools/attach/AgentLoadException", "com/sun/tools/attach/AgentInitializationException", "java/io/IOException"]}, {"nme": "loadAgentPath", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["com/sun/tools/attach/AgentLoadException", "com/sun/tools/attach/AgentInitializationException", "java/io/IOException"]}, {"nme": "loadAgentPath", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["com/sun/tools/attach/AgentLoadException", "com/sun/tools/attach/AgentInitializationException", "java/io/IOException"]}, {"nme": "loadAgent", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["com/sun/tools/attach/AgentLoadException", "com/sun/tools/attach/AgentInitializationException", "java/io/IOException"]}, {"nme": "loadAgent", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["com/sun/tools/attach/AgentLoadException", "com/sun/tools/attach/AgentInitializationException", "java/io/IOException"]}, {"nme": "getSystemProperties", "acc": 1025, "dsc": "()Ljava/util/Properties;", "exs": ["java/io/IOException"]}, {"nme": "getAgentProperties", "acc": 1025, "dsc": "()Ljava/util/Properties;", "exs": ["java/io/IOException"]}, {"nme": "startManagementAgent", "acc": 1025, "dsc": "(Ljava/util/Properties;)V", "exs": ["java/io/IOException"]}, {"nme": "startLocalManagementAgent", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "provider", "dsc": "Lcom/sun/tools/attach/spi/AttachProvider;"}, {"acc": 2, "nme": "id", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 66, "nme": "hash", "dsc": "I"}]}, "classes/com/sun/tools/attach/AgentLoadException.class": {"ver": 68, "acc": 33, "nme": "com/sun/tools/attach/AgentLoadException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 24, "nme": "serialVersionUID", "dsc": "J", "val": 688047862952114238}]}, "classes/com/sun/tools/attach/AttachPermission.class": {"ver": 68, "acc": 49, "nme": "com/sun/tools/attach/AttachPermission", "super": "java/security/BasicPermission", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 24, "nme": "serialVersionUID", "dsc": "J", "val": -4619447669752976181}]}}}}