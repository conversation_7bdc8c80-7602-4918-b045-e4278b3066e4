package shyrcs.extrastoragehook.economy;

import net.milkbowl.vault.economy.Economy;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.RegisteredServiceProvider;
import shyrcs.extrastoragehook.application.Library;
import shyrcs.extrastoragehook.SbMagicHook;

import java.text.DecimalFormat;
import java.util.function.Consumer;

/**
 * Economy provider sử dụng Vault với giá cố định
 * Dùng khi không có shop plugin
 */
public class VaultEconomyHook extends EconomyProvider {
    
    private final Economy economy;
    private static final DecimalFormat formatter = new DecimalFormat("###,###.##");
    
    public VaultEconomyHook() {
        RegisteredServiceProvider<Economy> rsp = Bukkit.getServer().getServicesManager().getRegistration(Economy.class);
        economy = (rsp != null) ? rsp.getProvider() : null;
        
        if (this.isHooked()) {
            SbMagicHook.info("Đã kết nối với Vault làm economy provider.");
        } else {
            SbMagicHook.warn("Không tìm thấy Vault Economy. Vui lòng cài đặt plugin economy!");
        }
    }
    
    @Override
    public boolean isHooked() {
        return economy != null;
    }
    
    @Override
    public int getAmount(ItemStack item) {
        // Trả về 1 vì không có shop plugin để xác định stack size
        return 1;
    }
    
    @Override
    public String getPrice(Player player, ItemStack item, int amount) {
        if (!this.isHooked()) {
            return null;
        }
        
        double basePrice = getBasePrice(item.getType());
        double totalPrice = basePrice * amount;
        
        return formatter.format(totalPrice);
    }
    
    @Override
    public void sellItem(Player player, ItemStack item, int amount, Consumer<Result> result) {
        if (!this.isHooked()) {
            result.accept(new Result(-1, -1, false));
            return;
        }
        
        double basePrice = getBasePrice(item.getType());
        double totalPrice = basePrice * amount;
        
        if (totalPrice <= 0) {
            result.accept(new Result(-1, -1, false));
            return;
        }
        
        // Thực hiện giao dịch
        boolean success = economy.depositPlayer(player, totalPrice).transactionSuccess();
        result.accept(new Result(amount, totalPrice, success));
        
        if (success) {
            SbMagicHook.info("Người chơi " + player.getName() + " đã bán " + amount + " " + 
                item.getType().name() + " với giá " + formatter.format(totalPrice));
        }
    }
    
    /**
     * Lấy giá cơ bản của material
     * Có thể được config hóa trong tương lai
     */
    private double getBasePrice(Material material) {
        // Kiểm tra config trước
        String configPath = "prices." + material.name().toLowerCase();
        if (Library.config != null) {
            double configPrice = Library.config.getInt(configPath, -1);
            if (configPrice > 0) {
                return configPrice;
            }
        }
        
        // Giá mặc định
        switch (material) {
            // Ores và Ingots
            case DIAMOND:
                return 100.0;
            case EMERALD:
                return 200.0;
            case GOLD_INGOT:
                return 50.0;
            case IRON_INGOT:
                return 25.0;
            case COPPER_INGOT:
                return 15.0;
            case NETHERITE_INGOT:
                return 1000.0;
            
            // Raw Materials
            case RAW_GOLD:
                return 45.0;
            case RAW_IRON:
                return 20.0;
            case RAW_COPPER:
                return 12.0;
            
            // Gems
            case LAPIS_LAZULI:
                return 10.0;
            case REDSTONE:
                return 5.0;
            case QUARTZ:
                return 8.0;
            
            // Coal và Wood
            case COAL:
                return 5.0;
            case CHARCOAL:
                return 3.0;
            
            // Food
            case WHEAT:
                return 2.0;
            case CARROT:
                return 1.5;
            case POTATO:
                return 1.5;
            case BEETROOT:
                return 1.0;
            
            // Blocks
            case COBBLESTONE:
                return 0.5;
            case STONE:
                return 1.0;
            case DIRT:
                return 0.1;
            case SAND:
                return 0.5;
            case GRAVEL:
                return 0.5;
            

            
            // Default
            default:
                return 1.0;
        }
    }
}
