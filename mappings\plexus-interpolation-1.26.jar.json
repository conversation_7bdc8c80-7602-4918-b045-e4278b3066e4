{"md5": "1049ae9f5cd8cf618abf5bc5805e6b94", "sha2": "25b919c664b79795ccde0ede5cee0fd68b544197", "sha256": "b3b5412ce17889103ea564bcdfcf9fb3dfa540344ffeac6b538a73c9d7182662", "contents": {"classes": {"org/codehaus/plexus/interpolation/multi/MultiDelimiterInterpolatorFilterReader.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/multi/MultiDelimiterInterpolatorFilterReader", "super": "java/io/FilterReader", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;Lorg/codehaus/plexus/interpolation/Interpolator;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;Lorg/codehaus/plexus/interpolation/Interpolator;Lorg/codehaus/plexus/interpolation/RecursionInterceptor;)V"}, {"nme": "addDelimiterSpec", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/codehaus/plexus/interpolation/multi/MultiDelimiterInterpolatorFilterReader;"}, {"nme": "removeDelimiterSpec", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "setDelimiterSpecs", "acc": 1, "dsc": "(Ljava/util/LinkedHashSet;)Lorg/codehaus/plexus/interpolation/multi/MultiDelimiterInterpolatorFilterReader;", "sig": "(Ljava/util/LinkedHashSet<Ljava/lang/String;>;)Lorg/codehaus/plexus/interpolation/multi/MultiDelimiterInterpolatorFilterReader;"}, {"nme": "skip", "acc": 1, "dsc": "(J)J", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 1, "dsc": "([CII)I", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 1, "dsc": "()I", "exs": ["java/io/IOException"]}, {"nme": "reselectDelimiterSpec", "acc": 2, "dsc": "(I)Z"}, {"nme": "isInterpolateWithPrefixPattern", "acc": 1, "dsc": "()Z"}, {"nme": "setInterpolateWithPrefixPattern", "acc": 1, "dsc": "(Z)V"}, {"nme": "getEscapeString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setEscapeString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "isPreserveEscapeString", "acc": 1, "dsc": "()Z"}, {"nme": "setPreserveEscapeString", "acc": 1, "dsc": "(Z)V"}, {"nme": "getRecursionInterceptor", "acc": 1, "dsc": "()Lorg/codehaus/plexus/interpolation/RecursionInterceptor;"}, {"nme": "setRecursionInterceptor", "acc": 1, "dsc": "(Lorg/codehaus/plexus/interpolation/RecursionInterceptor;)Lorg/codehaus/plexus/interpolation/multi/MultiDelimiterInterpolatorFilterReader;"}], "flds": [{"acc": 2, "nme": "interpolator", "dsc": "Lorg/codehaus/plexus/interpolation/Interpolator;"}, {"acc": 2, "nme": "recursionInterceptor", "dsc": "Lorg/codehaus/plexus/interpolation/RecursionInterceptor;"}, {"acc": 2, "nme": "replaceData", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "replaceIndex", "dsc": "I"}, {"acc": 2, "nme": "previousIndex", "dsc": "I"}, {"acc": 25, "nme": "DEFAULT_BEGIN_TOKEN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "${"}, {"acc": 25, "nme": "DEFAULT_END_TOKEN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "}"}, {"acc": 2, "nme": "interpolateWithPrefixPattern", "dsc": "Z"}, {"acc": 2, "nme": "escapeString", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "useEscape", "dsc": "Z"}, {"acc": 2, "nme": "preserveEscapeString", "dsc": "Z"}, {"acc": 2, "nme": "delimiters", "dsc": "Ljava/util/LinkedHashSet;", "sig": "Ljava/util/LinkedHashSet<Lorg/codehaus/plexus/interpolation/multi/DelimiterSpecification;>;"}, {"acc": 2, "nme": "currentSpec", "dsc": "Lorg/codehaus/plexus/interpolation/multi/DelimiterSpecification;"}, {"acc": 2, "nme": "beginToken", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "originalBeginToken", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "endToken", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/codehaus/plexus/interpolation/FeedbackEnabledValueSource.class": {"ver": 50, "acc": 132609, "nme": "org/codehaus/plexus/interpolation/FeedbackEnabledValueSource", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/codehaus/plexus/interpolation/fixed/PrefixedObjectValueSource.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/fixed/PrefixedObjectValueSource", "super": "org/codehaus/plexus/interpolation/fixed/AbstractDelegatingValueSource", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/lang/Object;Z)V", "sig": "(<PERSON><PERSON><PERSON>/util/List<Ljava/lang/String;>;Ljava/lang/Object;Z)V"}], "flds": []}, "org/codehaus/plexus/interpolation/Interpolator.class": {"ver": 50, "acc": 1537, "nme": "org/codehaus/plexus/interpolation/Interpolator", "super": "java/lang/Object", "mthds": [{"nme": "addValueSource", "acc": 1025, "dsc": "(Lorg/codehaus/plexus/interpolation/ValueSource;)V"}, {"nme": "removeValuesSource", "acc": 1025, "dsc": "(Lorg/codehaus/plexus/interpolation/ValueSource;)V"}, {"nme": "addPostProcessor", "acc": 1025, "dsc": "(Lorg/codehaus/plexus/interpolation/InterpolationPostProcessor;)V"}, {"nme": "removePostProcessor", "acc": 1025, "dsc": "(Lorg/codehaus/plexus/interpolation/InterpolationPostProcessor;)V"}, {"nme": "interpolate", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "exs": ["org/codehaus/plexus/interpolation/InterpolationException"]}, {"nme": "interpolate", "acc": 1025, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/interpolation/RecursionInterceptor;)Ljava/lang/String;", "exs": ["org/codehaus/plexus/interpolation/InterpolationException"]}, {"nme": "getFeedback", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;"}, {"nme": "clearFeedback", "acc": 1025, "dsc": "()V"}, {"nme": "isCacheAnswers", "acc": 1025, "dsc": "()Z"}, {"nme": "setCacheAnswers", "acc": 1025, "dsc": "(Z)V"}, {"nme": "clearAnswers", "acc": 1025, "dsc": "()V"}], "flds": []}, "org/codehaus/plexus/interpolation/object/FieldBasedObjectInterpolator$1.class": {"ver": 50, "acc": 4128, "nme": "org/codehaus/plexus/interpolation/object/FieldBasedObjectInterpolator$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/codehaus/plexus/interpolation/PrefixedValueSourceWrapper.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/PrefixedValueSourceWrapper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/codehaus/plexus/interpolation/ValueSource;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/codehaus/plexus/interpolation/ValueSource;Ljava/lang/String;Z)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/codehaus/plexus/interpolation/ValueSource;Ljava/util/List;)V", "sig": "(Lorg/codehaus/plexus/interpolation/ValueSource;Ljava/util/List<Ljava/lang/String;>;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/codehaus/plexus/interpolation/ValueSource;Ljava/util/List;Z)V", "sig": "(Lorg/codehaus/plexus/interpolation/ValueSource;Ljava/util/List<Ljava/lang/String;>;Z)V"}, {"nme": "getValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getFeedback", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;"}, {"nme": "getLastExpression", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "clearFeedback", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "valueSource", "dsc": "Lorg/codehaus/plexus/interpolation/ValueSource;"}, {"acc": 18, "nme": "possiblePrefixes", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "allowUnprefixedExpressions", "dsc": "Z"}, {"acc": 2, "nme": "lastExpression", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/codehaus/plexus/interpolation/fixed/MapBasedValueSource.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/fixed/MapBasedValueSource", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/util/Map;)V"}, {"nme": "getValue", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Lorg/codehaus/plexus/interpolation/fixed/InterpolationState;)Ljava/lang/Object;"}], "flds": [{"acc": 18, "nme": "values", "dsc": "Ljava/util/Map;"}]}, "org/codehaus/plexus/interpolation/reflection/MethodMap.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/reflection/MethodMap", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "add", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)V"}, {"nme": "get", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/List<Ljava/lang/reflect/Method;>;"}, {"nme": "find", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Lja<PERSON>/lang/Object;)Ljava/lang/reflect/Method;", "exs": ["org/codehaus/plexus/interpolation/reflection/MethodMap$AmbiguousException"]}, {"nme": "getMostSpecific", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/List;[Ljava/lang/Class;)Ljava/lang/reflect/Method;", "sig": "(Lja<PERSON>/util/List<Ljava/lang/reflect/Method;>;[Ljava/lang/Class<*>;)Ljava/lang/reflect/Method;", "exs": ["org/codehaus/plexus/interpolation/reflection/MethodMap$AmbiguousException"]}, {"nme": "moreSpecific", "acc": 10, "dsc": "([<PERSON>ja<PERSON>/lang/Class;[Ljava/lang/Class;)I", "sig": "([<PERSON>ja<PERSON>/lang/Class<*>;[Ljava/lang/Class<*>;)I"}, {"nme": "getApplicables", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/List;[Lja<PERSON>/lang/Class;)Ljava/util/LinkedList;", "sig": "(Ljava/util/List<Ljava/lang/reflect/Method;>;[Ljava/lang/Class<*>;)Ljava/util/LinkedList<Ljava/lang/reflect/Method;>;"}, {"nme": "isApplicable", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;[Ljava/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;[Ljava/lang/Class<*>;)Z"}, {"nme": "isMethodInvocationConvertible", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/Class;)Z", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/Class<*>;)Z"}, {"nme": "isStrictMethodInvocationConvertible", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/Class;)Z", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/Class<*>;)Z"}], "flds": [{"acc": 26, "nme": "MORE_SPECIFIC", "dsc": "I", "val": 0}, {"acc": 26, "nme": "LESS_SPECIFIC", "dsc": "I", "val": 1}, {"acc": 26, "nme": "INCOMPARABLE", "dsc": "I", "val": 2}, {"acc": 0, "nme": "methodByNameMap", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljava/lang/reflect/Method;>;>;"}]}, "org/codehaus/plexus/interpolation/os/OperatingSystemUtils$DefaultEnvVarSource.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/os/OperatingSystemUtils$DefaultEnvVarSource", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getEnvMap", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}], "flds": []}, "org/codehaus/plexus/interpolation/reflection/ReflectionValueExtractor.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/reflection/ReflectionValueExtractor", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "evaluate", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}, {"nme": "evaluate", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;Z)Ljava/lang/Object;", "exs": ["java/lang/Exception"]}, {"nme": "getMappedValue", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;II<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}, {"nme": "getIndexedValue", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;II<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}, {"nme": "getPropertyValue", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}, {"nme": "getClassMap", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Lorg/codehaus/plexus/interpolation/reflection/ClassMap;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Lorg/codehaus/plexus/interpolation/reflection/ClassMap;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CLASS_ARGS", "dsc": "[Ljava/lang/Class;", "sig": "[Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "OBJECT_ARGS", "dsc": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 26, "nme": "classMaps", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Class<*>;Ljava/lang/ref/WeakReference<Lorg/codehaus/plexus/interpolation/reflection/ClassMap;>;>;"}, {"acc": 24, "nme": "EOF", "dsc": "I", "val": -1}, {"acc": 24, "nme": "PROPERTY_START", "dsc": "C", "val": 46}, {"acc": 24, "nme": "INDEXED_START", "dsc": "C", "val": 91}, {"acc": 24, "nme": "INDEXED_END", "dsc": "C", "val": 93}, {"acc": 24, "nme": "MAPPED_START", "dsc": "C", "val": 40}, {"acc": 24, "nme": "MAPPED_END", "dsc": "C", "val": 41}]}, "org/codehaus/plexus/interpolation/fixed/InterpolationCycleException.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/fixed/InterpolationCycleException", "super": "java/lang/RuntimeException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/codehaus/plexus/interpolation/RecursionInterceptor;Ljava/lang/String;Ljava/lang/String;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}]}, "org/codehaus/plexus/interpolation/StringSearchInterpolator.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/StringSearchInterpolator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "addValueSource", "acc": 1, "dsc": "(Lorg/codehaus/plexus/interpolation/ValueSource;)V"}, {"nme": "removeValuesSource", "acc": 1, "dsc": "(Lorg/codehaus/plexus/interpolation/ValueSource;)V"}, {"nme": "addPostProcessor", "acc": 1, "dsc": "(Lorg/codehaus/plexus/interpolation/InterpolationPostProcessor;)V"}, {"nme": "removePostProcessor", "acc": 1, "dsc": "(Lorg/codehaus/plexus/interpolation/InterpolationPostProcessor;)V"}, {"nme": "interpolate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "exs": ["org/codehaus/plexus/interpolation/InterpolationException"]}, {"nme": "interpolate", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/interpolation/RecursionInterceptor;)Ljava/lang/String;", "exs": ["org/codehaus/plexus/interpolation/InterpolationException"]}, {"nme": "interpolate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "exs": ["org/codehaus/plexus/interpolation/InterpolationException"]}, {"nme": "interpolate", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Lorg/codehaus/plexus/interpolation/RecursionInterceptor;)Ljava/lang/String;", "exs": ["org/codehaus/plexus/interpolation/InterpolationException"]}, {"nme": "interpolate", "acc": 2, "dsc": "(Lja<PERSON>/lang/String;Lorg/codehaus/plexus/interpolation/RecursionInterceptor;Ljava/util/Set;)Ljava/lang/String;", "sig": "(Ljava/lang/String;Lorg/codehaus/plexus/interpolation/RecursionInterceptor;Ljava/util/Set<Ljava/lang/String;>;)Ljava/lang/String;", "exs": ["org/codehaus/plexus/interpolation/InterpolationException"]}, {"nme": "getFeedback", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;"}, {"nme": "clearFeedback", "acc": 1, "dsc": "()V"}, {"nme": "isCacheAnswers", "acc": 1, "dsc": "()Z"}, {"nme": "setCacheAnswers", "acc": 1, "dsc": "(Z)V"}, {"nme": "clearAnswers", "acc": 1, "dsc": "()V"}, {"nme": "getEscapeString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setEscapeString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 2, "nme": "existingAnswers", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;"}, {"acc": 2, "nme": "valueSources", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/codehaus/plexus/interpolation/ValueSource;>;"}, {"acc": 2, "nme": "postProcessors", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/codehaus/plexus/interpolation/InterpolationPostProcessor;>;"}, {"acc": 2, "nme": "cacheAnswers", "dsc": "Z"}, {"acc": 25, "nme": "DEFAULT_START_EXPR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "${"}, {"acc": 25, "nme": "DEFAULT_END_EXPR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "}"}, {"acc": 2, "nme": "startExpr", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "endExpr", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "escapeString", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/codehaus/plexus/interpolation/multi/DelimiterSpecification.class": {"ver": 50, "acc": 49, "nme": "org/codehaus/plexus/interpolation/multi/DelimiterSpecification", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getNextStartIndex", "acc": 1, "dsc": "()I"}, {"nme": "setNextStartIndex", "acc": 1, "dsc": "(I)V"}, {"nme": "clearNextStart", "acc": 1, "dsc": "()V"}, {"nme": "parse", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/codehaus/plexus/interpolation/multi/DelimiterSpecification;"}, {"nme": "getBegin", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getEnd", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "DEFAULT_SPEC", "dsc": "Lorg/codehaus/plexus/interpolation/multi/DelimiterSpecification;"}, {"acc": 2, "nme": "begin", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "end", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "nextStart", "dsc": "I"}]}, "org/codehaus/plexus/interpolation/RegexBasedInterpolator.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/RegexBasedInterpolator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Z)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/List;)V"}, {"nme": "addValueSource", "acc": 1, "dsc": "(Lorg/codehaus/plexus/interpolation/ValueSource;)V"}, {"nme": "removeValuesSource", "acc": 1, "dsc": "(Lorg/codehaus/plexus/interpolation/ValueSource;)V"}, {"nme": "addPostProcessor", "acc": 1, "dsc": "(Lorg/codehaus/plexus/interpolation/InterpolationPostProcessor;)V"}, {"nme": "removePostProcessor", "acc": 1, "dsc": "(Lorg/codehaus/plexus/interpolation/InterpolationPostProcessor;)V"}, {"nme": "interpolate", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/interpolation/RecursionInterceptor;)Ljava/lang/String;", "exs": ["org/codehaus/plexus/interpolation/InterpolationException"]}, {"nme": "getPattern", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"nme": "interpolate", "acc": 2, "dsc": "(Lja<PERSON>/lang/String;Lorg/codehaus/plexus/interpolation/RecursionInterceptor;Lja<PERSON>/util/regex/Pattern;I)Ljava/lang/String;", "exs": ["org/codehaus/plexus/interpolation/InterpolationException"]}, {"nme": "getFeedback", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;"}, {"nme": "clearFeedback", "acc": 1, "dsc": "()V"}, {"nme": "interpolate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "exs": ["org/codehaus/plexus/interpolation/InterpolationException"]}, {"nme": "interpolate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "exs": ["org/codehaus/plexus/interpolation/InterpolationException"]}, {"nme": "interpolate", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Lorg/codehaus/plexus/interpolation/RecursionInterceptor;)Ljava/lang/String;", "exs": ["org/codehaus/plexus/interpolation/InterpolationException"]}, {"nme": "isReusePatterns", "acc": 1, "dsc": "()Z"}, {"nme": "setReusePatterns", "acc": 1, "dsc": "(Z)V"}, {"nme": "isCacheAnswers", "acc": 1, "dsc": "()Z"}, {"nme": "setCacheAnswers", "acc": 1, "dsc": "(Z)V"}, {"nme": "clearAnswers", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "startRegex", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "endRegex", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "existingAnswers", "dsc": "Ljava/util/Map;"}, {"acc": 2, "nme": "valueSources", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/codehaus/plexus/interpolation/ValueSource;>;"}, {"acc": 2, "nme": "postProcessors", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/codehaus/plexus/interpolation/InterpolationPostProcessor;>;"}, {"acc": 2, "nme": "reusePatterns", "dsc": "Z"}, {"acc": 2, "nme": "cacheAnswers", "dsc": "Z"}, {"acc": 25, "nme": "DEFAULT_REGEXP", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "\\$\\{(.+?)\\}"}, {"acc": 2, "nme": "compiledPatterns", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/util/regex/Pattern;>;"}]}, "org/codehaus/plexus/interpolation/util/ValueSourceUtils.class": {"ver": 50, "acc": 49, "nme": "org/codehaus/plexus/interpolation/util/ValueSourceUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "trimPrefix", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Collection;Z)Ljava/lang/String;", "sig": "(Lja<PERSON>/lang/String;Ljava/util/Collection<Ljava/lang/String;>;Z)Ljava/lang/String;"}, {"nme": "trimPrefix", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[<PERSON>ja<PERSON>/lang/String;Z)Ljava/lang/String;"}], "flds": []}, "org/codehaus/plexus/interpolation/fixed/InterpolationState.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/fixed/InterpolationState", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "addFeedback", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "asList", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;"}, {"nme": "clear", "acc": 1, "dsc": "()V"}, {"nme": "setRecursionInterceptor", "acc": 1, "dsc": "(Lorg/codehaus/plexus/interpolation/RecursionInterceptor;)V"}], "flds": [{"acc": 18, "nme": "messages", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 18, "nme": "causes", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/Throwable;>;"}, {"acc": 16, "nme": "unresolvable", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 0, "nme": "recursionInterceptor", "dsc": "Lorg/codehaus/plexus/interpolation/RecursionInterceptor;"}, {"acc": 0, "nme": "root", "dsc": "Lorg/codehaus/plexus/interpolation/fixed/FixedStringSearchInterpolator;"}]}, "org/codehaus/plexus/interpolation/os/Os.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/os/Os", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setValidFamilies", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "setFamily", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setName", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setArch", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setVersion", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "eval", "acc": 1, "dsc": "()Z", "exs": ["java/lang/Exception"]}, {"nme": "isFamily", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isName", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isArch", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isVersion", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isOs", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "getOsFamily", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isValidFamily", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "getValidFamilies", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "FAMILY_DOS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "dos"}, {"acc": 25, "nme": "FAMILY_MAC", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "mac"}, {"acc": 25, "nme": "FAMILY_NETWARE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "netware"}, {"acc": 25, "nme": "FAMILY_OS2", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "os/2"}, {"acc": 25, "nme": "FAMILY_TANDEM", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "tandem"}, {"acc": 25, "nme": "FAMILY_UNIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "unix"}, {"acc": 25, "nme": "FAMILY_WINDOWS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "windows"}, {"acc": 25, "nme": "FAMILY_WIN9X", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "win9x"}, {"acc": 25, "nme": "FAMILY_ZOS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "z/os"}, {"acc": 25, "nme": "FAMILY_OS400", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "os/400"}, {"acc": 25, "nme": "FAMILY_OPENVMS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "openvms"}, {"acc": 26, "nme": "validFamilies", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 26, "nme": "PATH_SEP", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 25, "nme": "OS_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 25, "nme": "OS_ARCH", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 25, "nme": "OS_VERSION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 25, "nme": "OS_FAMILY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "family", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "arch", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/codehaus/plexus/interpolation/PrefixAwareRecursionInterceptor.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/PrefixAwareRecursionInterceptor", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;Z)V", "sig": "(Lja<PERSON>/util/Collection<Ljava/lang/String;>;Z)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)V", "sig": "(Lja<PERSON>/util/Collection<Ljava/lang/String;>;)V"}, {"nme": "hasRecursiveExpression", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "expressionResolutionFinished", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "expressionResolutionStarted", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getExpressionCycle", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;"}, {"nme": "clear", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "DEFAULT_START_TOKEN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "\\$\\{"}, {"acc": 25, "nme": "DEFAULT_END_TOKEN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "\\}"}, {"acc": 2, "nme": "nakedExpressions", "dsc": "<PERSON><PERSON><PERSON>/util/<PERSON>ack;", "sig": "<PERSON>ja<PERSON>/util/Stack<Ljava/lang/String;>;"}, {"acc": 18, "nme": "possiblePrefixes", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "watchUnprefixedExpressions", "dsc": "Z"}]}, "org/codehaus/plexus/interpolation/AbstractValueSource.class": {"ver": 50, "acc": 1057, "nme": "org/codehaus/plexus/interpolation/AbstractValueSource", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Z)V"}, {"nme": "clearFeedback", "acc": 1, "dsc": "()V"}, {"nme": "getFeedback", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;"}, {"nme": "addFeedback", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "addFeedback", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}], "flds": [{"acc": 18, "nme": "feedback", "dsc": "<PERSON><PERSON><PERSON>/util/List;"}]}, "org/codehaus/plexus/interpolation/multi/MultiDelimiterStringSearchInterpolator.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/multi/MultiDelimiterStringSearchInterpolator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "addDelimiterSpec", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/codehaus/plexus/interpolation/multi/MultiDelimiterStringSearchInterpolator;"}, {"nme": "removeDelimiterSpec", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "withValueSource", "acc": 1, "dsc": "(Lorg/codehaus/plexus/interpolation/ValueSource;)Lorg/codehaus/plexus/interpolation/multi/MultiDelimiterStringSearchInterpolator;"}, {"nme": "withPostProcessor", "acc": 1, "dsc": "(Lorg/codehaus/plexus/interpolation/InterpolationPostProcessor;)Lorg/codehaus/plexus/interpolation/multi/MultiDelimiterStringSearchInterpolator;"}, {"nme": "addValueSource", "acc": 1, "dsc": "(Lorg/codehaus/plexus/interpolation/ValueSource;)V"}, {"nme": "removeValuesSource", "acc": 1, "dsc": "(Lorg/codehaus/plexus/interpolation/ValueSource;)V"}, {"nme": "addPostProcessor", "acc": 1, "dsc": "(Lorg/codehaus/plexus/interpolation/InterpolationPostProcessor;)V"}, {"nme": "removePostProcessor", "acc": 1, "dsc": "(Lorg/codehaus/plexus/interpolation/InterpolationPostProcessor;)V"}, {"nme": "interpolate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "exs": ["org/codehaus/plexus/interpolation/InterpolationException"]}, {"nme": "interpolate", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/interpolation/RecursionInterceptor;)Ljava/lang/String;", "exs": ["org/codehaus/plexus/interpolation/InterpolationException"]}, {"nme": "interpolate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "exs": ["org/codehaus/plexus/interpolation/InterpolationException"]}, {"nme": "interpolate", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Lorg/codehaus/plexus/interpolation/RecursionInterceptor;)Ljava/lang/String;", "exs": ["org/codehaus/plexus/interpolation/InterpolationException"]}, {"nme": "interpolate", "acc": 2, "dsc": "(Lja<PERSON>/lang/String;Lorg/codehaus/plexus/interpolation/RecursionInterceptor;Ljava/util/Set;)Ljava/lang/String;", "sig": "(Ljava/lang/String;Lorg/codehaus/plexus/interpolation/RecursionInterceptor;Ljava/util/Set<Ljava/lang/String;>;)Ljava/lang/String;", "exs": ["org/codehaus/plexus/interpolation/InterpolationException"]}, {"nme": "select", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Lorg/codehaus/plexus/interpolation/multi/DelimiterSpecification;"}, {"nme": "getFeedback", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;"}, {"nme": "clearFeedback", "acc": 1, "dsc": "()V"}, {"nme": "isCacheAnswers", "acc": 1, "dsc": "()Z"}, {"nme": "setCacheAnswers", "acc": 1, "dsc": "(Z)V"}, {"nme": "clearAnswers", "acc": 1, "dsc": "()V"}, {"nme": "getEscapeString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setEscapeString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "escapeString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/codehaus/plexus/interpolation/multi/MultiDelimiterStringSearchInterpolator;"}, {"nme": "setDelimiterSpecs", "acc": 1, "dsc": "(Ljava/util/LinkedHashSet;)Lorg/codehaus/plexus/interpolation/multi/MultiDelimiterStringSearchInterpolator;", "sig": "(Ljava/util/LinkedHashSet<Ljava/lang/String;>;)Lorg/codehaus/plexus/interpolation/multi/MultiDelimiterStringSearchInterpolator;"}], "flds": [{"acc": 26, "nme": "MAX_TRIES", "dsc": "I", "val": 10}, {"acc": 2, "nme": "existingAnswers", "dsc": "Ljava/util/Map;"}, {"acc": 2, "nme": "valueSources", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/codehaus/plexus/interpolation/ValueSource;>;"}, {"acc": 2, "nme": "postProcessors", "dsc": "<PERSON><PERSON><PERSON>/util/List;"}, {"acc": 2, "nme": "cacheAnswers", "dsc": "Z"}, {"acc": 2, "nme": "delimiters", "dsc": "Ljava/util/LinkedHashSet;", "sig": "Ljava/util/LinkedHashSet<Lorg/codehaus/plexus/interpolation/multi/DelimiterSpecification;>;"}, {"acc": 2, "nme": "escapeString", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/codehaus/plexus/interpolation/SingleResponseValueSource.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/SingleResponseValueSource", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "clearFeedback", "acc": 1, "dsc": "()V"}, {"nme": "getFeedback", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;"}, {"nme": "getValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 18, "nme": "expression", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "response", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "org/codehaus/plexus/interpolation/PropertiesBasedValueSource.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/PropertiesBasedValueSource", "super": "org/codehaus/plexus/interpolation/AbstractValueSource", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/util/Properties;)V"}, {"nme": "getValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 18, "nme": "properties", "dsc": "Ljava/util/Properties;"}]}, "org/codehaus/plexus/interpolation/fixed/ObjectBasedValueSource.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/fixed/ObjectBasedValueSource", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "getValue", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Lorg/codehaus/plexus/interpolation/fixed/InterpolationState;)Ljava/lang/Object;"}], "flds": [{"acc": 18, "nme": "root", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "org/codehaus/plexus/interpolation/object/FieldBasedObjectInterpolator$InterpolateObjectAction.class": {"ver": 50, "acc": 48, "nme": "org/codehaus/plexus/interpolation/object/FieldBasedObjectInterpolator$InterpolateObjectAction", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/codehaus/plexus/interpolation/BasicInterpolator;Lorg/codehaus/plexus/interpolation/RecursionInterceptor;Ljava/util/Set;<PERSON>ja<PERSON>/util/Set;<PERSON>ja<PERSON>/util/List;)V", "sig": "(<PERSON>ja<PERSON>/lang/Object;Lorg/codehaus/plexus/interpolation/BasicInterpolator;Lorg/codehaus/plexus/interpolation/RecursionInterceptor;Ljava/util/Set;Ljava/util/Set;Ljava/util/List<Lorg/codehaus/plexus/interpolation/object/ObjectInterpolationWarning;>;)V"}, {"nme": "run", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "traverseObjectWithParents", "acc": 2, "dsc": "(Ljava/lang/Class;Lorg/codehaus/plexus/interpolation/object/FieldBasedObjectInterpolator$InterpolationTarget;)V", "exs": ["org/codehaus/plexus/interpolation/InterpolationException"]}, {"nme": "interpolateObject", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/reflect/Field;)V", "exs": ["java/lang/IllegalAccessException", "org/codehaus/plexus/interpolation/InterpolationException"]}, {"nme": "interpolateMap", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/reflect/Field;)V", "exs": ["java/lang/IllegalAccessException", "org/codehaus/plexus/interpolation/InterpolationException"]}, {"nme": "interpolateCollection", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/reflect/Field;)Z", "exs": ["java/lang/IllegalAccessException", "org/codehaus/plexus/interpolation/InterpolationException"]}, {"nme": "interpolateString", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lja<PERSON>/lang/reflect/Field;)V", "exs": ["java/lang/IllegalAccessException", "org/codehaus/plexus/interpolation/InterpolationException"]}, {"nme": "isQualifiedForInterpolation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z"}, {"nme": "isQualifiedForInterpolation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;Ljava/lang/Class;)Z"}, {"nme": "evaluateArray", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/codehaus/plexus/interpolation/InterpolationException"]}], "flds": [{"acc": 18, "nme": "interpolationTargets", "dsc": "<PERSON><PERSON><PERSON>/util/LinkedList;", "sig": "Ljava/util/LinkedList<Lorg/codehaus/plexus/interpolation/object/FieldBasedObjectInterpolator$InterpolationTarget;>;"}, {"acc": 18, "nme": "interpolator", "dsc": "Lorg/codehaus/plexus/interpolation/BasicInterpolator;"}, {"acc": 18, "nme": "blacklistedFieldNames", "dsc": "<PERSON><PERSON><PERSON>/util/Set;"}, {"acc": 18, "nme": "blacklistedPackagePrefixes", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "warningCollector", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/codehaus/plexus/interpolation/object/ObjectInterpolationWarning;>;"}, {"acc": 18, "nme": "recursionInterceptor", "dsc": "Lorg/codehaus/plexus/interpolation/RecursionInterceptor;"}]}, "org/codehaus/plexus/interpolation/InterpolationPostProcessor.class": {"ver": 50, "acc": 1537, "nme": "org/codehaus/plexus/interpolation/InterpolationPostProcessor", "super": "java/lang/Object", "mthds": [{"nme": "execute", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "org/codehaus/plexus/interpolation/fixed/PropertiesBasedValueSource.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/fixed/PropertiesBasedValueSource", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/util/Properties;)V"}, {"nme": "getValue", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Lorg/codehaus/plexus/interpolation/fixed/InterpolationState;)Ljava/lang/Object;"}], "flds": [{"acc": 18, "nme": "properties", "dsc": "Ljava/util/Properties;"}]}, "org/codehaus/plexus/interpolation/os/OperatingSystemUtils.class": {"ver": 50, "acc": 49, "nme": "org/codehaus/plexus/interpolation/os/OperatingSystemUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getSystemEnvVars", "acc": 9, "dsc": "()Ljava/util/Properties;", "exs": ["java/io/IOException"]}, {"nme": "getSystemEnvVars", "acc": 9, "dsc": "(Z)Ljava/util/Properties;", "exs": ["java/io/IOException"]}, {"nme": "setEnvVarSource", "acc": 9, "dsc": "(Lorg/codehaus/plexus/interpolation/os/OperatingSystemUtils$EnvVarSource;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 10, "nme": "envVarSource", "dsc": "Lorg/codehaus/plexus/interpolation/os/OperatingSystemUtils$EnvVarSource;"}]}, "org/codehaus/plexus/interpolation/MapBasedValueSource.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/MapBasedValueSource", "super": "org/codehaus/plexus/interpolation/AbstractValueSource", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/util/Map;)V"}, {"nme": "getValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 18, "nme": "values", "dsc": "Ljava/util/Map;"}]}, "org/codehaus/plexus/interpolation/fixed/EnvarBasedValueSource.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/fixed/EnvarBasedValueSource", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 1, "dsc": "(Z)V", "exs": ["java/io/IOException"]}, {"nme": "get<PERSON>n<PERSON><PERSON>", "acc": 42, "dsc": "(Z)Ljava/util/Properties;", "exs": ["java/io/IOException"]}, {"nme": "getValue", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Lorg/codehaus/plexus/interpolation/fixed/InterpolationState;)Ljava/lang/Object;"}, {"nme": "resetStatics", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 10, "nme": "envarsCaseSensitive", "dsc": "Ljava/util/Properties;"}, {"acc": 10, "nme": "envarsCaseInsensitive", "dsc": "Ljava/util/Properties;"}, {"acc": 18, "nme": "envars", "dsc": "Ljava/util/Properties;"}, {"acc": 18, "nme": "caseSensitive", "dsc": "Z"}]}, "org/codehaus/plexus/interpolation/reflection/ClassMap$1.class": {"ver": 50, "acc": 4128, "nme": "org/codehaus/plexus/interpolation/reflection/ClassMap$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/codehaus/plexus/interpolation/PrefixedObjectValueSource.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/PrefixedObjectValueSource", "super": "org/codehaus/plexus/interpolation/AbstractDelegatingValueSource", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/lang/Object;Z)V", "sig": "(<PERSON><PERSON><PERSON>/util/List<Ljava/lang/String;>;Ljava/lang/Object;Z)V"}, {"nme": "getLastExpression", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "org/codehaus/plexus/interpolation/SimpleRecursionInterceptor.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/SimpleRecursionInterceptor", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "expressionResolutionFinished", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "expressionResolutionStarted", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "hasRecursiveExpression", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "getExpressionCycle", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;"}, {"nme": "clear", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "expressions", "dsc": "<PERSON><PERSON><PERSON>/util/<PERSON>ack;"}]}, "org/codehaus/plexus/interpolation/reflection/ClassMap.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/reflection/ClassMap", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)V"}, {"nme": "getCachedClass", "acc": 0, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<*>;"}, {"nme": "find<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Lja<PERSON>/lang/Object;)Ljava/lang/reflect/Method;", "exs": ["org/codehaus/plexus/interpolation/reflection/MethodMap$AmbiguousException"]}, {"nme": "populate<PERSON>ethod<PERSON>ache", "acc": 2, "dsc": "()V"}, {"nme": "makeMethodKey", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)Ljava/lang/String;"}, {"nme": "makeMethodKey", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/Object;)Ljava/lang/String;"}, {"nme": "getAccessibleMethods", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)[Ljava/lang/reflect/Method;", "sig": "(Lja<PERSON>/lang/Class<*>;)[Ljava/lang/reflect/Method;"}, {"nme": "getAccessibleMethods", "acc": 10, "dsc": "(Ljava/lang/Class;[Lorg/codehaus/plexus/interpolation/reflection/ClassMap$MethodInfo;I)I", "sig": "(Ljava/lang/Class<*>;[Lorg/codehaus/plexus/interpolation/reflection/ClassMap$MethodInfo;I)I"}, {"nme": "getPublicMethod", "acc": 9, "dsc": "(Lja<PERSON>/lang/reflect/Method;)Ljava/lang/reflect/Method;"}, {"nme": "getPublicMethod", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Lja<PERSON>/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/String;[Ljava/lang/Class<*>;)Ljava/lang/reflect/Method;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CACHE_MISS", "dsc": "Lorg/codehaus/plexus/interpolation/reflection/ClassMap$CacheMiss;"}, {"acc": 26, "nme": "OBJECT", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 2, "nme": "clazz", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 2, "nme": "methodCache", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;"}, {"acc": 2, "nme": "methodMap", "dsc": "Lorg/codehaus/plexus/interpolation/reflection/MethodMap;"}]}, "org/codehaus/plexus/interpolation/AbstractFunctionValueSourceWrapper.class": {"ver": 50, "acc": 1057, "nme": "org/codehaus/plexus/interpolation/AbstractFunctionValueSourceWrapper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Lorg/codehaus/plexus/interpolation/ValueSource;)V"}, {"nme": "getValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getValueSource", "acc": 4, "dsc": "()Lorg/codehaus/plexus/interpolation/ValueSource;"}, {"nme": "executeFunction", "acc": 1028, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 18, "nme": "valueSource", "dsc": "Lorg/codehaus/plexus/interpolation/ValueSource;"}]}, "org/codehaus/plexus/interpolation/fixed/FixedValueSource.class": {"ver": 50, "acc": 1537, "nme": "org/codehaus/plexus/interpolation/fixed/FixedValueSource", "super": "java/lang/Object", "mthds": [{"nme": "getValue", "acc": 1025, "dsc": "(Lja<PERSON>/lang/String;Lorg/codehaus/plexus/interpolation/fixed/InterpolationState;)Ljava/lang/Object;"}], "flds": []}, "org/codehaus/plexus/interpolation/fixed/PrefixedValueSourceWrapper.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/fixed/PrefixedValueSourceWrapper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/codehaus/plexus/interpolation/fixed/FixedValueSource;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/codehaus/plexus/interpolation/fixed/FixedValueSource;Ljava/lang/String;Z)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/codehaus/plexus/interpolation/fixed/FixedValueSource;Ljava/util/List;)V", "sig": "(Lorg/codehaus/plexus/interpolation/fixed/FixedValueSource;Ljava/util/List<Ljava/lang/String;>;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/codehaus/plexus/interpolation/fixed/FixedValueSource;Ljava/util/List;Z)V", "sig": "(Lorg/codehaus/plexus/interpolation/fixed/FixedValueSource;Ljava/util/List<Ljava/lang/String;>;Z)V"}, {"nme": "getValue", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Lorg/codehaus/plexus/interpolation/fixed/InterpolationState;)Ljava/lang/Object;"}], "flds": [{"acc": 18, "nme": "valueSource", "dsc": "Lorg/codehaus/plexus/interpolation/fixed/FixedValueSource;"}, {"acc": 18, "nme": "possiblePrefixes", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "allowUnprefixedExpressions", "dsc": "Z"}, {"acc": 2, "nme": "lastExpression", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/codehaus/plexus/interpolation/BasicInterpolator.class": {"ver": 50, "acc": 1537, "nme": "org/codehaus/plexus/interpolation/BasicInterpolator", "super": "java/lang/Object", "mthds": [{"nme": "interpolate", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "exs": ["org/codehaus/plexus/interpolation/InterpolationException"]}, {"nme": "interpolate", "acc": 1025, "dsc": "(Lja<PERSON>/lang/String;Lorg/codehaus/plexus/interpolation/RecursionInterceptor;)Ljava/lang/String;", "exs": ["org/codehaus/plexus/interpolation/InterpolationException"]}], "flds": []}, "org/codehaus/plexus/interpolation/object/FieldBasedObjectInterpolator$InterpolationTarget.class": {"ver": 50, "acc": 48, "nme": "org/codehaus/plexus/interpolation/object/FieldBasedObjectInterpolator$InterpolationTarget", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 4096, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Ljava/lang/String;Lorg/codehaus/plexus/interpolation/object/FieldBasedObjectInterpolator$1;)V"}, {"nme": "access$100", "acc": 4104, "dsc": "(Lorg/codehaus/plexus/interpolation/object/FieldBasedObjectInterpolator$InterpolationTarget;)Ljava/lang/Object;"}, {"nme": "access$200", "acc": 4104, "dsc": "(Lorg/codehaus/plexus/interpolation/object/FieldBasedObjectInterpolator$InterpolationTarget;)Ljava/lang/String;"}], "flds": [{"acc": 2, "nme": "value", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 2, "nme": "path", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/codehaus/plexus/interpolation/PrefixedPropertiesValueSource.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/PrefixedPropertiesValueSource", "super": "org/codehaus/plexus/interpolation/AbstractDelegatingValueSource", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(L<PERSON><PERSON>/lang/String;Ljava/util/Properties;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(L<PERSON><PERSON>/util/List;Ljava/util/Properties;Z)V", "sig": "(Ljava/util/List<Ljava/lang/String;>;Ljava/util/Properties;Z)V"}, {"nme": "getLastExpression", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "org/codehaus/plexus/interpolation/reflection/ReflectionValueExtractor$Tokenizer.class": {"ver": 50, "acc": 32, "nme": "org/codehaus/plexus/interpolation/reflection/ReflectionValueExtractor$Tokenizer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "peekChar", "acc": 1, "dsc": "()I"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()I"}, {"nme": "nextToken", "acc": 1, "dsc": "(C)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "nextPropertyName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getPosition", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 16, "nme": "expression", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "idx", "dsc": "I"}]}, "org/codehaus/plexus/interpolation/fixed/PrefixedPropertiesValueSource.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/fixed/PrefixedPropertiesValueSource", "super": "org/codehaus/plexus/interpolation/fixed/AbstractDelegatingValueSource", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(L<PERSON><PERSON>/lang/String;Ljava/util/Properties;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(L<PERSON><PERSON>/util/List;Ljava/util/Properties;Z)V", "sig": "(Ljava/util/List<Ljava/lang/String;>;Ljava/util/Properties;Z)V"}], "flds": []}, "org/codehaus/plexus/interpolation/InterpolationCycleException.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/InterpolationCycleException", "super": "org/codehaus/plexus/interpolation/InterpolationException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/codehaus/plexus/interpolation/RecursionInterceptor;Ljava/lang/String;Ljava/lang/String;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}]}, "org/codehaus/plexus/interpolation/AbstractDelegatingValueSource.class": {"ver": 50, "acc": 1057, "nme": "org/codehaus/plexus/interpolation/AbstractDelegatingValueSource", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Lorg/codehaus/plexus/interpolation/ValueSource;)V"}, {"nme": "getDelegate", "acc": 4, "dsc": "()Lorg/codehaus/plexus/interpolation/ValueSource;"}, {"nme": "getValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "clearFeedback", "acc": 1, "dsc": "()V"}, {"nme": "getFeedback", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;"}], "flds": [{"acc": 18, "nme": "delegate", "dsc": "Lorg/codehaus/plexus/interpolation/ValueSource;"}]}, "org/codehaus/plexus/interpolation/QueryEnabledValueSource.class": {"ver": 50, "acc": 1537, "nme": "org/codehaus/plexus/interpolation/QueryEnabledValueSource", "super": "java/lang/Object", "mthds": [{"nme": "getLastExpression", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "org/codehaus/plexus/interpolation/FixedInterpolatorValueSource.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/FixedInterpolatorValueSource", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/codehaus/plexus/interpolation/fixed/FixedStringSearchInterpolator;)V"}, {"nme": "getValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getFeedback", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;"}, {"nme": "clearFeedback", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "fixedStringSearchInterpolator", "dsc": "Lorg/codehaus/plexus/interpolation/fixed/FixedStringSearchInterpolator;"}, {"acc": 18, "nme": "errorCollector", "dsc": "Lorg/codehaus/plexus/interpolation/fixed/InterpolationState;"}]}, "org/codehaus/plexus/interpolation/fixed/AbstractDelegatingValueSource.class": {"ver": 50, "acc": 1057, "nme": "org/codehaus/plexus/interpolation/fixed/AbstractDelegatingValueSource", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Lorg/codehaus/plexus/interpolation/fixed/FixedValueSource;)V"}, {"nme": "getDelegate", "acc": 4, "dsc": "()Lorg/codehaus/plexus/interpolation/fixed/FixedValueSource;"}, {"nme": "getValue", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Lorg/codehaus/plexus/interpolation/fixed/InterpolationState;)Ljava/lang/Object;"}], "flds": [{"acc": 18, "nme": "delegate", "dsc": "Lorg/codehaus/plexus/interpolation/fixed/FixedValueSource;"}]}, "org/codehaus/plexus/interpolation/object/ObjectInterpolationWarning.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/object/ObjectInterpolationWarning", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getMessage", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getCause", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Throwable;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "message", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "cause", "dsc": "<PERSON><PERSON><PERSON>/lang/Throwable;"}, {"acc": 18, "nme": "path", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/codehaus/plexus/interpolation/util/StringUtils.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/util/StringUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "replace", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)<PERSON>java/lang/String;"}, {"nme": "replace", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;I)Ljava/lang/String;"}, {"nme": "capitalizeFirstLetter", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": []}, "org/codehaus/plexus/interpolation/RecursionInterceptor.class": {"ver": 50, "acc": 1537, "nme": "org/codehaus/plexus/interpolation/RecursionInterceptor", "super": "java/lang/Object", "mthds": [{"nme": "expressionResolutionStarted", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "expressionResolutionFinished", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "hasRecursiveExpression", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "getExpressionCycle", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;"}, {"nme": "clear", "acc": 1025, "dsc": "()V"}], "flds": []}, "org/codehaus/plexus/interpolation/object/ObjectInterpolator.class": {"ver": 50, "acc": 1537, "nme": "org/codehaus/plexus/interpolation/object/ObjectInterpolator", "super": "java/lang/Object", "mthds": [{"nme": "interpolate", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/codehaus/plexus/interpolation/BasicInterpolator;)V", "exs": ["org/codehaus/plexus/interpolation/InterpolationException"]}, {"nme": "interpolate", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/codehaus/plexus/interpolation/BasicInterpolator;Lorg/codehaus/plexus/interpolation/RecursionInterceptor;)V", "exs": ["org/codehaus/plexus/interpolation/InterpolationException"]}, {"nme": "hasWarnings", "acc": 1025, "dsc": "()Z"}, {"nme": "getWarnings", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;"}], "flds": []}, "org/codehaus/plexus/interpolation/InterpolationException.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/InterpolationException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "buildMessage", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getExpression", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}, {"acc": 18, "nme": "expression", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/codehaus/plexus/interpolation/reflection/ClassMap$CacheMiss.class": {"ver": 50, "acc": 48, "nme": "org/codehaus/plexus/interpolation/reflection/ClassMap$CacheMiss", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/codehaus/plexus/interpolation/reflection/ClassMap$1;)V"}], "flds": []}, "org/codehaus/plexus/interpolation/ObjectBasedValueSource.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/ObjectBasedValueSource", "super": "org/codehaus/plexus/interpolation/AbstractValueSource", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "getValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 18, "nme": "root", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "org/codehaus/plexus/interpolation/InterpolatorFilterReader.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/InterpolatorFilterReader", "super": "java/io/FilterReader", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;Lorg/codehaus/plexus/interpolation/Interpolator;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;Lorg/codehaus/plexus/interpolation/Interpolator;Lja<PERSON>/lang/String;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;Lorg/codehaus/plexus/interpolation/Interpolator;Lorg/codehaus/plexus/interpolation/RecursionInterceptor;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;Lorg/codehaus/plexus/interpolation/Interpolator;Ljava/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/interpolation/RecursionInterceptor;)V"}, {"nme": "skip", "acc": 1, "dsc": "(J)J", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 1, "dsc": "([CII)I", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 1, "dsc": "()I", "exs": ["java/io/IOException"]}, {"nme": "isInterpolateWithPrefixPattern", "acc": 1, "dsc": "()Z"}, {"nme": "setInterpolateWithPrefixPattern", "acc": 1, "dsc": "(Z)V"}, {"nme": "getEscapeString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setEscapeString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "isPreserveEscapeString", "acc": 1, "dsc": "()Z"}, {"nme": "setPreserveEscapeString", "acc": 1, "dsc": "(Z)V"}, {"nme": "getRecursionInterceptor", "acc": 1, "dsc": "()Lorg/codehaus/plexus/interpolation/RecursionInterceptor;"}, {"nme": "setRecursionInterceptor", "acc": 1, "dsc": "(Lorg/codehaus/plexus/interpolation/RecursionInterceptor;)Lorg/codehaus/plexus/interpolation/InterpolatorFilterReader;"}], "flds": [{"acc": 2, "nme": "interpolator", "dsc": "Lorg/codehaus/plexus/interpolation/Interpolator;"}, {"acc": 2, "nme": "recursionInterceptor", "dsc": "Lorg/codehaus/plexus/interpolation/RecursionInterceptor;"}, {"acc": 2, "nme": "replaceData", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "replaceIndex", "dsc": "I"}, {"acc": 2, "nme": "previousIndex", "dsc": "I"}, {"acc": 25, "nme": "DEFAULT_BEGIN_TOKEN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "${"}, {"acc": 25, "nme": "DEFAULT_END_TOKEN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "}"}, {"acc": 2, "nme": "beginToken", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "orginalBeginToken", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "endToken", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "interpolateWithPrefixPattern", "dsc": "Z"}, {"acc": 2, "nme": "escapeString", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "useEscape", "dsc": "Z"}, {"acc": 2, "nme": "preserveEscapeString", "dsc": "Z"}]}, "org/codehaus/plexus/interpolation/reflection/ClassMap$MethodInfo.class": {"ver": 50, "acc": 48, "nme": "org/codehaus/plexus/interpolation/reflection/ClassMap$MethodInfo", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)V"}, {"nme": "tryUpcasting", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)V", "exs": ["java/lang/NoSuchMethodException"]}], "flds": [{"acc": 0, "nme": "method", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 0, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "parameterTypes", "dsc": "[Ljava/lang/Class;", "sig": "[Ljava/lang/Class<*>;"}, {"acc": 0, "nme": "upcast", "dsc": "Z"}]}, "org/codehaus/plexus/interpolation/os/OperatingSystemUtils$EnvVarSource.class": {"ver": 50, "acc": 1537, "nme": "org/codehaus/plexus/interpolation/os/OperatingSystemUtils$EnvVarSource", "super": "java/lang/Object", "mthds": [{"nme": "getEnvMap", "acc": 1025, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}], "flds": []}, "org/codehaus/plexus/interpolation/fixed/FixedStringSearchInterpolator.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/fixed/FixedStringSearchInterpolator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 130, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/interpolation/InterpolationPostProcessor;[Lorg/codehaus/plexus/interpolation/fixed/FixedValueSource;)V"}, {"nme": "create", "acc": 137, "dsc": "(Ljava/lang/String;Ljava/lang/String;[Lorg/codehaus/plexus/interpolation/fixed/FixedValueSource;)Lorg/codehaus/plexus/interpolation/fixed/FixedStringSearchInterpolator;"}, {"nme": "create", "acc": 137, "dsc": "([Lorg/codehaus/plexus/interpolation/fixed/FixedValueSource;)Lorg/codehaus/plexus/interpolation/fixed/FixedStringSearchInterpolator;"}, {"nme": "createWithPermittedNulls", "acc": 137, "dsc": "([Lorg/codehaus/plexus/interpolation/fixed/FixedValueSource;)Lorg/codehaus/plexus/interpolation/fixed/FixedStringSearchInterpolator;"}, {"nme": "withExpressionMarkers", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;)Lorg/codehaus/plexus/interpolation/fixed/FixedStringSearchInterpolator;"}, {"nme": "withPostProcessor", "acc": 1, "dsc": "(Lorg/codehaus/plexus/interpolation/InterpolationPostProcessor;)Lorg/codehaus/plexus/interpolation/fixed/FixedStringSearchInterpolator;"}, {"nme": "withEscapeString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/codehaus/plexus/interpolation/fixed/FixedStringSearchInterpolator;"}, {"nme": "interpolate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "exs": ["org/codehaus/plexus/interpolation/fixed/InterpolationCycleException"]}, {"nme": "empty", "acc": 9, "dsc": "()Lorg/codehaus/plexus/interpolation/fixed/FixedStringSearchInterpolator;"}, {"nme": "getValue", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Lorg/codehaus/plexus/interpolation/fixed/InterpolationState;)Ljava/lang/Object;"}, {"nme": "asBasicInterpolator", "acc": 1, "dsc": "()Lorg/codehaus/plexus/interpolation/BasicInterpolator;"}, {"nme": "interpolate", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Lorg/codehaus/plexus/interpolation/fixed/InterpolationState;)Ljava/lang/String;", "exs": ["org/codehaus/plexus/interpolation/fixed/InterpolationCycleException"]}], "flds": [{"acc": 18, "nme": "valueSources", "dsc": "[Lorg/codehaus/plexus/interpolation/fixed/FixedValueSource;"}, {"acc": 18, "nme": "postProcessor", "dsc": "Lorg/codehaus/plexus/interpolation/InterpolationPostProcessor;"}, {"acc": 25, "nme": "DEFAULT_START_EXPR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "${"}, {"acc": 25, "nme": "DEFAULT_END_EXPR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "}"}, {"acc": 18, "nme": "startExpr", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "endExpr", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "escapeString", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/codehaus/plexus/interpolation/EnvarBasedValueSource.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/EnvarBasedValueSource", "super": "org/codehaus/plexus/interpolation/AbstractValueSource", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 1, "dsc": "(Z)V", "exs": ["java/io/IOException"]}, {"nme": "get<PERSON>n<PERSON><PERSON>", "acc": 42, "dsc": "(Z)Ljava/util/Properties;", "exs": ["java/io/IOException"]}, {"nme": "getValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "resetStatics", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 10, "nme": "envarsCaseSensitive", "dsc": "Ljava/util/Properties;"}, {"acc": 10, "nme": "envarsCaseInsensitive", "dsc": "Ljava/util/Properties;"}, {"acc": 18, "nme": "envars", "dsc": "Ljava/util/Properties;"}, {"acc": 18, "nme": "caseSensitive", "dsc": "Z"}]}, "org/codehaus/plexus/interpolation/reflection/MethodMap$AmbiguousException.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/reflection/MethodMap$AmbiguousException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "org/codehaus/plexus/interpolation/ValueSource.class": {"ver": 50, "acc": 1537, "nme": "org/codehaus/plexus/interpolation/ValueSource", "super": "java/lang/Object", "mthds": [{"nme": "getValue", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getFeedback", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;"}, {"nme": "clearFeedback", "acc": 1025, "dsc": "()V"}], "flds": []}, "org/codehaus/plexus/interpolation/fixed/FixedStringSearchInterpolator$1.class": {"ver": 50, "acc": 32, "nme": "org/codehaus/plexus/interpolation/fixed/FixedStringSearchInterpolator$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/codehaus/plexus/interpolation/fixed/FixedStringSearchInterpolator;Lorg/codehaus/plexus/interpolation/fixed/InterpolationState;)V"}, {"nme": "interpolate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "exs": ["org/codehaus/plexus/interpolation/InterpolationException"]}, {"nme": "interpolate", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Lorg/codehaus/plexus/interpolation/RecursionInterceptor;)Ljava/lang/String;", "exs": ["org/codehaus/plexus/interpolation/InterpolationException"]}], "flds": [{"acc": 4112, "nme": "val$is", "dsc": "Lorg/codehaus/plexus/interpolation/fixed/InterpolationState;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lorg/codehaus/plexus/interpolation/fixed/FixedStringSearchInterpolator;"}]}, "org/codehaus/plexus/interpolation/object/FieldBasedObjectInterpolator.class": {"ver": 50, "acc": 33, "nme": "org/codehaus/plexus/interpolation/object/FieldBasedObjectInterpolator", "super": "java/lang/Object", "mthds": [{"nme": "clearCaches", "acc": 9, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(L<PERSON><PERSON>/util/Set<Ljava/lang/String;>;Ljava/util/Set<Ljava/lang/String;>;)V"}, {"nme": "hasWarnings", "acc": 1, "dsc": "()Z"}, {"nme": "getWarnings", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/codehaus/plexus/interpolation/object/ObjectInterpolationWarning;>;"}, {"nme": "interpolate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/codehaus/plexus/interpolation/BasicInterpolator;)V", "exs": ["org/codehaus/plexus/interpolation/InterpolationException"]}, {"nme": "interpolate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/codehaus/plexus/interpolation/BasicInterpolator;Lorg/codehaus/plexus/interpolation/RecursionInterceptor;)V", "exs": ["org/codehaus/plexus/interpolation/InterpolationException"]}, {"nme": "access$300", "acc": 4104, "dsc": "()Ljava/util/Map;"}, {"nme": "access$400", "acc": 4104, "dsc": "()Ljava/util/Map;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "DEFAULT_BLACKLISTED_FIELD_NAMES", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 25, "nme": "DEFAULT_BLACKLISTED_PACKAGE_PREFIXES", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 26, "nme": "fieldsByClass", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Class;[Ljava/lang/reflect/Field;>;"}, {"acc": 26, "nme": "fieldIsPrimitiveByClass", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Class;Ljava/lang/Boolean;>;"}, {"acc": 2, "nme": "blacklistedFieldNames", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 2, "nme": "blacklistedPackagePrefixes", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 2, "nme": "warnings", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/codehaus/plexus/interpolation/object/ObjectInterpolationWarning;>;"}]}}}}