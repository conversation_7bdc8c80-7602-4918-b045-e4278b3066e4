{"md5": "595940ed571cbc2bb771c8fdbe30350c", "sha2": "012e6182deef32d366ade664aa33bec9f4dd3ffe", "sha256": "e7f9ac47f4ae61f2e63157a1f97e0750cb6fd90c9d0bf25f188260e732f284fa", "contents": {"classes": {"org/sqlite/ProgressHandler.class": {"ver": 52, "acc": 1057, "nme": "org/sqlite/ProgressHandler", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 25, "dsc": "(Ljava/sql/Connection;ILorg/sqlite/ProgressHandler;)V", "exs": ["java/sql/SQLException"]}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 25, "dsc": "(Ljava/sql/Connection;)V", "exs": ["java/sql/SQLException"]}, {"nme": "progress", "acc": 1028, "dsc": "()I", "exs": ["java/sql/SQLException"]}], "flds": []}, "org/sqlite/util/OSInfo$LogHolder.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/util/OSInfo$LogHolder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "access$000", "acc": 4104, "dsc": "()Lorg/slf4j/Logger;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "logger", "dsc": "Lorg/slf4j/Logger;"}]}, "org/sqlite/ExtendedCommand$SQLExtension.class": {"ver": 52, "acc": 1537, "nme": "org/sqlite/ExtendedCommand$SQLExtension", "super": "java/lang/Object", "mthds": [{"nme": "execute", "acc": 1025, "dsc": "(Lorg/sqlite/core/DB;)V", "exs": ["java/sql/SQLException"]}], "flds": []}, "org/sqlite/date/FastDateFormat.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/date/FastDateFormat", "super": "java/text/Format", "mthds": [{"nme": "getInstance", "acc": 9, "dsc": "()Lorg/sqlite/date/FastDateFormat;"}, {"nme": "getInstance", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/sqlite/date/FastDateFormat;"}, {"nme": "getInstance", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/util/TimeZone;)Lorg/sqlite/date/FastDateFormat;"}, {"nme": "getInstance", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/util/Locale;)Lorg/sqlite/date/FastDateFormat;"}, {"nme": "getInstance", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/TimeZone;<PERSON>ja<PERSON>/util/Locale;)Lorg/sqlite/date/FastDateFormat;"}, {"nme": "getDateInstance", "acc": 9, "dsc": "(I)Lorg/sqlite/date/FastDateFormat;"}, {"nme": "getDateInstance", "acc": 9, "dsc": "(ILjava/util/Locale;)Lorg/sqlite/date/FastDateFormat;"}, {"nme": "getDateInstance", "acc": 9, "dsc": "(ILjava/util/TimeZone;)Lorg/sqlite/date/FastDateFormat;"}, {"nme": "getDateInstance", "acc": 9, "dsc": "(ILjava/util/TimeZone;Ljava/util/Locale;)Lorg/sqlite/date/FastDateFormat;"}, {"nme": "getTimeInstance", "acc": 9, "dsc": "(I)Lorg/sqlite/date/FastDateFormat;"}, {"nme": "getTimeInstance", "acc": 9, "dsc": "(ILjava/util/Locale;)Lorg/sqlite/date/FastDateFormat;"}, {"nme": "getTimeInstance", "acc": 9, "dsc": "(ILjava/util/TimeZone;)Lorg/sqlite/date/FastDateFormat;"}, {"nme": "getTimeInstance", "acc": 9, "dsc": "(ILjava/util/TimeZone;Ljava/util/Locale;)Lorg/sqlite/date/FastDateFormat;"}, {"nme": "getDateTimeInstance", "acc": 9, "dsc": "(II)Lorg/sqlite/date/FastDateFormat;"}, {"nme": "getDateTimeInstance", "acc": 9, "dsc": "(IILjava/util/Locale;)Lorg/sqlite/date/FastDateFormat;"}, {"nme": "getDateTimeInstance", "acc": 9, "dsc": "(IILjava/util/TimeZone;)Lorg/sqlite/date/FastDateFormat;"}, {"nme": "getDateTimeInstance", "acc": 9, "dsc": "(IILjava/util/TimeZone;Ljava/util/Locale;)Lorg/sqlite/date/FastDateFormat;"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/TimeZone;<PERSON><PERSON><PERSON>/util/Locale;)V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/TimeZone;<PERSON><PERSON><PERSON>/util/Locale;<PERSON><PERSON><PERSON>/util/Date;)V"}, {"nme": "format", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/StringBuffer;<PERSON><PERSON><PERSON>/text/FieldPosition;)<PERSON><PERSON><PERSON>/lang/StringBuffer;"}, {"nme": "format", "acc": 1, "dsc": "(J)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "format", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Date;)L<PERSON><PERSON>/lang/String;"}, {"nme": "format", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Calendar;)Ljava/lang/String;"}, {"nme": "format", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/StringBuffer;)<PERSON><PERSON><PERSON>/lang/StringBuffer;"}, {"nme": "format", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Date;<PERSON><PERSON><PERSON>/lang/StringBuffer;)<PERSON><PERSON><PERSON>/lang/StringBuffer;"}, {"nme": "format", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Calendar;<PERSON><PERSON><PERSON>/lang/StringBuffer;)<PERSON>ja<PERSON>/lang/StringBuffer;"}, {"nme": "parse", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Date;", "exs": ["java/text/ParseException"]}, {"nme": "parse", "acc": 1, "dsc": "(L<PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/text/ParsePosition;)Ljava/util/Date;"}, {"nme": "parseObject", "acc": 1, "dsc": "(L<PERSON><PERSON>/lang/String;L<PERSON><PERSON>/text/ParsePosition;)Ljava/lang/Object;"}, {"nme": "getPattern", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getTimeZone", "acc": 1, "dsc": "()Ljava/util/TimeZone;"}, {"nme": "getLocale", "acc": 1, "dsc": "()Ljava/util/Locale;"}, {"nme": "getMaxLengthEstimate", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "applyRules", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/util/Calendar;<PERSON><PERSON><PERSON>/lang/StringBuffer;)<PERSON>ja<PERSON>/lang/StringBuffer;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 2}, {"acc": 25, "nme": "FULL", "dsc": "I", "val": 0}, {"acc": 25, "nme": "LONG", "dsc": "I", "val": 1}, {"acc": 25, "nme": "MEDIUM", "dsc": "I", "val": 2}, {"acc": 25, "nme": "SHORT", "dsc": "I", "val": 3}, {"acc": 26, "nme": "cache", "dsc": "Lorg/sqlite/date/FormatCache;", "sig": "Lorg/sqlite/date/FormatCache<Lorg/sqlite/date/FastDateFormat;>;"}, {"acc": 18, "nme": "printer", "dsc": "Lorg/sqlite/date/FastDatePrinter;"}, {"acc": 18, "nme": "parser", "dsc": "Lorg/sqlite/date/FastDateParser;"}]}, "org/sqlite/core/CorePreparedStatement.class": {"ver": 52, "acc": 1057, "nme": "org/sqlite/core/CorePreparedStatement", "super": "org/sqlite/jdbc4/JDBC4Statement", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Lorg/sqlite/SQLiteConnection;Ljava/lang/String;)V", "exs": ["java/sql/SQLException"]}, {"nme": "executeBatch", "acc": 1, "dsc": "()[I", "exs": ["java/sql/SQLException"]}, {"nme": "executeLargeBatch", "acc": 1, "dsc": "()[J", "exs": ["java/sql/SQLException"]}, {"nme": "clearBatch", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "batch", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "exs": ["java/sql/SQLException"]}, {"nme": "setDateByMilliseconds", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Long;<PERSON><PERSON><PERSON>/util/Calendar;)V", "exs": ["java/sql/SQLException"]}, {"nme": "lambda$executeLargeBatch$1", "acc": 4098, "dsc": "()[J", "exs": ["java/sql/SQLException"]}, {"nme": "lambda$executeBatch$0", "acc": 4106, "dsc": "(J)I"}], "flds": [{"acc": 4, "nme": "columnCount", "dsc": "I"}, {"acc": 4, "nme": "paramCount", "dsc": "I"}, {"acc": 4, "nme": "batchQueryCount", "dsc": "I"}]}, "org/sqlite/date/FastDatePrinter$PaddedNumberField.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/date/FastDatePrinter$PaddedNumberField", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(II)V"}, {"nme": "estimateLength", "acc": 1, "dsc": "()I"}, {"nme": "appendTo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;<PERSON><PERSON><PERSON>/util/Calendar;)V"}, {"nme": "appendTo", "acc": 17, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;I)V"}], "flds": [{"acc": 18, "nme": "m<PERSON>ield", "dsc": "I"}, {"acc": 18, "nme": "mSize", "dsc": "I"}]}, "org/sqlite/javax/SQLitePooledConnectionHandle.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/javax/SQLitePooledConnectionHandle", "super": "org/sqlite/SQLiteConnection", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/sqlite/javax/SQLitePooledConnection;)V"}, {"nme": "createStatement", "acc": 1, "dsc": "()Ljava/sql/Statement;", "exs": ["java/sql/SQLException"]}, {"nme": "prepareStatement", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/sql/PreparedStatement;", "exs": ["java/sql/SQLException"]}, {"nme": "prepareCall", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/sql/CallableStatement;", "exs": ["java/sql/SQLException"]}, {"nme": "nativeSQL", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "setAutoCommit", "acc": 1, "dsc": "(Z)V", "exs": ["java/sql/SQLException"]}, {"nme": "getAutoCommit", "acc": 1, "dsc": "()Z", "exs": ["java/sql/SQLException"]}, {"nme": "commit", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "rollback", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "isClosed", "acc": 1, "dsc": "()Z"}, {"nme": "getMetaData", "acc": 1, "dsc": "()Ljava/sql/DatabaseMetaData;", "exs": ["java/sql/SQLException"]}, {"nme": "setReadOnly", "acc": 1, "dsc": "(Z)V", "exs": ["java/sql/SQLException"]}, {"nme": "isReadOnly", "acc": 1, "dsc": "()Z", "exs": ["java/sql/SQLException"]}, {"nme": "setCatalog", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/sql/SQLException"]}, {"nme": "getCatalog", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "setTransactionIsolation", "acc": 1, "dsc": "(I)V", "exs": ["java/sql/SQLException"]}, {"nme": "getTransactionIsolation", "acc": 1, "dsc": "()I"}, {"nme": "getWarnings", "acc": 1, "dsc": "()Ljava/sql/SQLWarning;", "exs": ["java/sql/SQLException"]}, {"nme": "clearWarnings", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "createStatement", "acc": 1, "dsc": "(II)Ljava/sql/Statement;", "exs": ["java/sql/SQLException"]}, {"nme": "prepareStatement", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;II)Ljava/sql/PreparedStatement;", "exs": ["java/sql/SQLException"]}, {"nme": "prepareCall", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;II)Ljava/sql/CallableStatement;", "exs": ["java/sql/SQLException"]}, {"nme": "getTypeMap", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/Class<*>;>;", "exs": ["java/sql/SQLException"]}, {"nme": "setTypeMap", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/lang/Class<*>;>;)V", "exs": ["java/sql/SQLException"]}, {"nme": "setHoldability", "acc": 1, "dsc": "(I)V", "exs": ["java/sql/SQLException"]}, {"nme": "getHoldability", "acc": 1, "dsc": "()I", "exs": ["java/sql/SQLException"]}, {"nme": "setSavepoint", "acc": 1, "dsc": "()Ljava/sql/Savepoint;", "exs": ["java/sql/SQLException"]}, {"nme": "setSavepoint", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/sql/Savepoint;", "exs": ["java/sql/SQLException"]}, {"nme": "rollback", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/sql/Savepoint;)V", "exs": ["java/sql/SQLException"]}, {"nme": "releaseSavepoint", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/sql/Savepoint;)V", "exs": ["java/sql/SQLException"]}, {"nme": "createStatement", "acc": 1, "dsc": "(III)Ljava/sql/Statement;", "exs": ["java/sql/SQLException"]}, {"nme": "prepareStatement", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;III)Ljava/sql/PreparedStatement;", "exs": ["java/sql/SQLException"]}, {"nme": "prepareCall", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;III)Ljava/sql/CallableStatement;", "exs": ["java/sql/SQLException"]}, {"nme": "prepareStatement", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Ljava/sql/PreparedStatement;", "exs": ["java/sql/SQLException"]}, {"nme": "prepareStatement", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[I)Ljava/sql/PreparedStatement;", "exs": ["java/sql/SQLException"]}, {"nme": "prepareStatement", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Lja<PERSON>/lang/String;)Ljava/sql/PreparedStatement;", "exs": ["java/sql/SQLException"]}, {"nme": "createClob", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/sql/Clob;", "exs": ["java/sql/SQLException"]}, {"nme": "createBlob", "acc": 1, "dsc": "()L<PERSON>va/sql/Blob;", "exs": ["java/sql/SQLException"]}, {"nme": "createNClob", "acc": 1, "dsc": "()Ljava/sql/NClob;", "exs": ["java/sql/SQLException"]}, {"nme": "createSQLXML", "acc": 1, "dsc": "()Ljava/sql/SQLXML;", "exs": ["java/sql/SQLException"]}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(I)Z", "exs": ["java/sql/SQLException"]}, {"nme": "setClientInfo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/sql/SQLClientInfoException"]}, {"nme": "setClientInfo", "acc": 1, "dsc": "(Ljava/util/Properties;)V", "exs": ["java/sql/SQLClientInfoException"]}, {"nme": "getClientInfo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "getClientInfo", "acc": 1, "dsc": "()Ljava/util/Properties;", "exs": ["java/sql/SQLException"]}, {"nme": "createArrayOf", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Lja<PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/sql/Array;", "exs": ["java/sql/SQLException"]}, {"nme": "createStruct", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/sql/Struct;", "exs": ["java/sql/SQLException"]}, {"nme": "setSchema", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/sql/SQLException"]}, {"nme": "getSchema", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "abort", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/concurrent/Executor;)V", "exs": ["java/sql/SQLException"]}, {"nme": "setNetworkTimeout", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/concurrent/Executor;I)V", "exs": ["java/sql/SQLException"]}, {"nme": "getNetworkTimeout", "acc": 1, "dsc": "()I", "exs": ["java/sql/SQLException"]}, {"nme": "unwrap", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>ja<PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;)TT;", "exs": ["java/sql/SQLException"]}, {"nme": "isWrapperFor", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z", "exs": ["java/sql/SQLException"]}, {"nme": "getBusyTimeout", "acc": 1, "dsc": "()I"}, {"nme": "setBusyTimeout", "acc": 1, "dsc": "(I)V"}, {"nme": "getDatabase", "acc": 1, "dsc": "()Lorg/sqlite/core/DB;"}], "flds": [{"acc": 18, "nme": "parent", "dsc": "Lorg/sqlite/javax/SQLitePooledConnection;"}, {"acc": 18, "nme": "isClosed", "dsc": "Ljava/util/concurrent/atomic/AtomicBoolean;"}]}, "org/sqlite/jdbc4/JDBC4Connection.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/jdbc4/JDBC4Connection", "super": "org/sqlite/jdbc3/JDBC3Connection", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/util/Properties;)V", "exs": ["java/sql/SQLException"]}, {"nme": "createStatement", "acc": 1, "dsc": "(III)Ljava/sql/Statement;", "exs": ["java/sql/SQLException"]}, {"nme": "prepareStatement", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;III)Ljava/sql/PreparedStatement;", "exs": ["java/sql/SQLException"]}, {"nme": "isClosed", "acc": 1, "dsc": "()Z", "exs": ["java/sql/SQLException"]}, {"nme": "unwrap", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>ja<PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;)TT;", "exs": ["java/lang/ClassCastException"]}, {"nme": "isWrapperFor", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "createClob", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/sql/Clob;", "exs": ["java/sql/SQLException"]}, {"nme": "createBlob", "acc": 1, "dsc": "()L<PERSON>va/sql/Blob;", "exs": ["java/sql/SQLException"]}, {"nme": "createNClob", "acc": 1, "dsc": "()Ljava/sql/NClob;", "exs": ["java/sql/SQLException"]}, {"nme": "createSQLXML", "acc": 1, "dsc": "()Ljava/sql/SQLXML;", "exs": ["java/sql/SQLException"]}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(I)Z", "exs": ["java/sql/SQLException"]}, {"nme": "setClientInfo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/sql/SQLClientInfoException"]}, {"nme": "setClientInfo", "acc": 1, "dsc": "(Ljava/util/Properties;)V", "exs": ["java/sql/SQLClientInfoException"]}, {"nme": "getClientInfo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "getClientInfo", "acc": 1, "dsc": "()Ljava/util/Properties;", "exs": ["java/sql/SQLException"]}, {"nme": "createArrayOf", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Lja<PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/sql/Array;", "exs": ["java/sql/SQLException"]}], "flds": []}, "org/sqlite/jdbc3/JDBC3DatabaseMetaData.class": {"ver": 52, "acc": 1057, "nme": "org/sqlite/jdbc3/JDBC3DatabaseMetaData", "super": "org/sqlite/core/CoreDatabaseMetaData", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Lorg/sqlite/SQLiteConnection;)V"}, {"nme": "getConnection", "acc": 1, "dsc": "()Ljava/sql/Connection;"}, {"nme": "getDatabaseMajorVersion", "acc": 1, "dsc": "()I", "exs": ["java/sql/SQLException"]}, {"nme": "getDatabaseMinorVersion", "acc": 1, "dsc": "()I", "exs": ["java/sql/SQLException"]}, {"nme": "getDriverMajorVersion", "acc": 1, "dsc": "()I"}, {"nme": "getDriverMinorVersion", "acc": 1, "dsc": "()I"}, {"nme": "getJDBCMajorVersion", "acc": 1, "dsc": "()I"}, {"nme": "getJDBCMinorVersion", "acc": 1, "dsc": "()I"}, {"nme": "getDefaultTransactionIsolation", "acc": 1, "dsc": "()I"}, {"nme": "getMaxBinaryLiteralLength", "acc": 1, "dsc": "()I"}, {"nme": "getMaxCatalogNameLength", "acc": 1, "dsc": "()I"}, {"nme": "getMaxCharLiteralLength", "acc": 1, "dsc": "()I"}, {"nme": "getMaxColumnNameLength", "acc": 1, "dsc": "()I"}, {"nme": "getMaxColumnsInGroupBy", "acc": 1, "dsc": "()I"}, {"nme": "getMaxColumnsInIndex", "acc": 1, "dsc": "()I"}, {"nme": "getMaxColumnsInOrderBy", "acc": 1, "dsc": "()I"}, {"nme": "getMaxColumnsInSelect", "acc": 1, "dsc": "()I"}, {"nme": "getMaxColumnsInTable", "acc": 1, "dsc": "()I"}, {"nme": "getMaxConnections", "acc": 1, "dsc": "()I"}, {"nme": "getMaxCursorNameLength", "acc": 1, "dsc": "()I"}, {"nme": "getMaxIndexLength", "acc": 1, "dsc": "()I"}, {"nme": "getMaxProcedureNameLength", "acc": 1, "dsc": "()I"}, {"nme": "getMaxRowSize", "acc": 1, "dsc": "()I"}, {"nme": "getMaxSchemaNameLength", "acc": 1, "dsc": "()I"}, {"nme": "getMaxStatementLength", "acc": 1, "dsc": "()I"}, {"nme": "getMaxStatements", "acc": 1, "dsc": "()I"}, {"nme": "getMaxTableNameLength", "acc": 1, "dsc": "()I"}, {"nme": "getMaxTablesInSelect", "acc": 1, "dsc": "()I"}, {"nme": "getMaxUserNameLength", "acc": 1, "dsc": "()I"}, {"nme": "getResultSetHoldability", "acc": 1, "dsc": "()I"}, {"nme": "getSQLStateType", "acc": 1, "dsc": "()I"}, {"nme": "getDatabaseProductName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDatabaseProductVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "getDriverName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDriverVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getExtraNameCharacters", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getCatalogSeparator", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getCatalogTerm", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getSchemaTerm", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getProcedureTerm", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getSearchStringEscape", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getIdentifierQuoteString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getSQLKeywords", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getNumericFunctions", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getStringFunctions", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getSystemFunctions", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getTimeDateFunctions", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getURL", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getUserName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "allProceduresAreCallable", "acc": 1, "dsc": "()Z"}, {"nme": "allTablesAreSelectable", "acc": 1, "dsc": "()Z"}, {"nme": "dataDefinitionCausesTransactionCommit", "acc": 1, "dsc": "()Z"}, {"nme": "dataDefinitionIgnoredInTransactions", "acc": 1, "dsc": "()Z"}, {"nme": "doesMaxRowSizeIncludeBlobs", "acc": 1, "dsc": "()Z"}, {"nme": "deletesAreDetected", "acc": 1, "dsc": "(I)Z"}, {"nme": "insertsAreDetected", "acc": 1, "dsc": "(I)Z"}, {"nme": "isCatalogAtStart", "acc": 1, "dsc": "()Z"}, {"nme": "locatorsUpdateCopy", "acc": 1, "dsc": "()Z"}, {"nme": "nullPlusNonNullIsNull", "acc": 1, "dsc": "()Z"}, {"nme": "nullsAreSortedAtEnd", "acc": 1, "dsc": "()Z"}, {"nme": "nullsAreSortedAtStart", "acc": 1, "dsc": "()Z"}, {"nme": "nullsAreSortedHigh", "acc": 1, "dsc": "()Z"}, {"nme": "nullsAreSortedLow", "acc": 1, "dsc": "()Z"}, {"nme": "othersDeletesAreVisible", "acc": 1, "dsc": "(I)Z"}, {"nme": "othersInsertsAreVisible", "acc": 1, "dsc": "(I)Z"}, {"nme": "othersUpdatesAreVisible", "acc": 1, "dsc": "(I)Z"}, {"nme": "ownDeletesAreVisible", "acc": 1, "dsc": "(I)Z"}, {"nme": "ownInsertsAreVisible", "acc": 1, "dsc": "(I)Z"}, {"nme": "ownUpdatesAreVisible", "acc": 1, "dsc": "(I)Z"}, {"nme": "storesLowerCaseIdentifiers", "acc": 1, "dsc": "()Z"}, {"nme": "storesLowerCaseQuotedIdentifiers", "acc": 1, "dsc": "()Z"}, {"nme": "storesMixedCaseIdentifiers", "acc": 1, "dsc": "()Z"}, {"nme": "storesMixedCaseQuotedIdentifiers", "acc": 1, "dsc": "()Z"}, {"nme": "storesUpperCaseIdentifiers", "acc": 1, "dsc": "()Z"}, {"nme": "storesUpperCaseQuotedIdentifiers", "acc": 1, "dsc": "()Z"}, {"nme": "supportsAlterTableWithAddColumn", "acc": 1, "dsc": "()Z"}, {"nme": "supportsAlterTableWithDropColumn", "acc": 1, "dsc": "()Z"}, {"nme": "supportsANSI92EntryLevelSQL", "acc": 1, "dsc": "()Z"}, {"nme": "supportsANSI92FullSQL", "acc": 1, "dsc": "()Z"}, {"nme": "supportsANSI92IntermediateSQL", "acc": 1, "dsc": "()Z"}, {"nme": "supportsBatchUpdates", "acc": 1, "dsc": "()Z"}, {"nme": "supportsCatalogsInDataManipulation", "acc": 1, "dsc": "()Z"}, {"nme": "supportsCatalogsInIndexDefinitions", "acc": 1, "dsc": "()Z"}, {"nme": "supportsCatalogsInPrivilegeDefinitions", "acc": 1, "dsc": "()Z"}, {"nme": "supportsCatalogsInProcedureCalls", "acc": 1, "dsc": "()Z"}, {"nme": "supportsCatalogsInTableDefinitions", "acc": 1, "dsc": "()Z"}, {"nme": "supportsColumnAliasing", "acc": 1, "dsc": "()Z"}, {"nme": "supportsConvert", "acc": 1, "dsc": "()Z"}, {"nme": "supportsConvert", "acc": 1, "dsc": "(II)Z"}, {"nme": "supportsCorrelatedSubqueries", "acc": 1, "dsc": "()Z"}, {"nme": "supportsDataDefinitionAndDataManipulationTransactions", "acc": 1, "dsc": "()Z"}, {"nme": "supportsDataManipulationTransactionsOnly", "acc": 1, "dsc": "()Z"}, {"nme": "supportsDifferentTableCorrelationNames", "acc": 1, "dsc": "()Z"}, {"nme": "supportsExpressionsInOrderBy", "acc": 1, "dsc": "()Z"}, {"nme": "supportsMinimumSQLGrammar", "acc": 1, "dsc": "()Z"}, {"nme": "supportsCoreSQLGrammar", "acc": 1, "dsc": "()Z"}, {"nme": "supportsExtendedSQLGrammar", "acc": 1, "dsc": "()Z"}, {"nme": "supportsLimitedOuterJoins", "acc": 1, "dsc": "()Z"}, {"nme": "supportsFullOuterJoins", "acc": 1, "dsc": "()Z", "exs": ["java/sql/SQLException"]}, {"nme": "supportsGetGeneratedKeys", "acc": 1, "dsc": "()Z"}, {"nme": "supportsGroupBy", "acc": 1, "dsc": "()Z"}, {"nme": "supportsGroupByBeyondSelect", "acc": 1, "dsc": "()Z"}, {"nme": "supportsGroupByUnrelated", "acc": 1, "dsc": "()Z"}, {"nme": "supportsIntegrityEnhancementFacility", "acc": 1, "dsc": "()Z"}, {"nme": "supportsLikeEscapeClause", "acc": 1, "dsc": "()Z"}, {"nme": "supportsMixedCaseIdentifiers", "acc": 1, "dsc": "()Z"}, {"nme": "supportsMixedCaseQuotedIdentifiers", "acc": 1, "dsc": "()Z"}, {"nme": "supportsMultipleOpenResults", "acc": 1, "dsc": "()Z"}, {"nme": "supportsMultipleResultSets", "acc": 1, "dsc": "()Z"}, {"nme": "supportsMultipleTransactions", "acc": 1, "dsc": "()Z"}, {"nme": "supportsNamedParameters", "acc": 1, "dsc": "()Z"}, {"nme": "supportsNonNullableColumns", "acc": 1, "dsc": "()Z"}, {"nme": "supportsOpenCursorsAcrossCommit", "acc": 1, "dsc": "()Z"}, {"nme": "supportsOpenCursorsAcrossRollback", "acc": 1, "dsc": "()Z"}, {"nme": "supportsOpenStatementsAcrossCommit", "acc": 1, "dsc": "()Z"}, {"nme": "supportsOpenStatementsAcrossRollback", "acc": 1, "dsc": "()Z"}, {"nme": "supportsOrderByUnrelated", "acc": 1, "dsc": "()Z"}, {"nme": "supportsOuterJoins", "acc": 1, "dsc": "()Z"}, {"nme": "supportsPositionedDelete", "acc": 1, "dsc": "()Z"}, {"nme": "supportsPositionedUpdate", "acc": 1, "dsc": "()Z"}, {"nme": "supportsResultSetConcurrency", "acc": 1, "dsc": "(II)Z"}, {"nme": "supportsResultSetHoldability", "acc": 1, "dsc": "(I)Z"}, {"nme": "supportsResultSetType", "acc": 1, "dsc": "(I)Z"}, {"nme": "supportsSavepoints", "acc": 1, "dsc": "()Z"}, {"nme": "supportsSchemasInDataManipulation", "acc": 1, "dsc": "()Z"}, {"nme": "supportsSchemasInIndexDefinitions", "acc": 1, "dsc": "()Z"}, {"nme": "supportsSchemasInPrivilegeDefinitions", "acc": 1, "dsc": "()Z"}, {"nme": "supportsSchemasInProcedureCalls", "acc": 1, "dsc": "()Z"}, {"nme": "supportsSchemasInTableDefinitions", "acc": 1, "dsc": "()Z"}, {"nme": "supportsSelectForUpdate", "acc": 1, "dsc": "()Z"}, {"nme": "supportsStatementPooling", "acc": 1, "dsc": "()Z"}, {"nme": "supportsStoredProcedures", "acc": 1, "dsc": "()Z"}, {"nme": "supportsSubqueriesInComparisons", "acc": 1, "dsc": "()Z"}, {"nme": "supportsSubqueriesInExists", "acc": 1, "dsc": "()Z"}, {"nme": "supportsSubqueriesInIns", "acc": 1, "dsc": "()Z"}, {"nme": "supportsSubqueriesInQuantifieds", "acc": 1, "dsc": "()Z"}, {"nme": "supportsTableCorrelationNames", "acc": 1, "dsc": "()Z"}, {"nme": "supportsTransactionIsolationLevel", "acc": 1, "dsc": "(I)Z"}, {"nme": "supportsTransactions", "acc": 1, "dsc": "()Z"}, {"nme": "supportsUnion", "acc": 1, "dsc": "()Z"}, {"nme": "supportsUnionAll", "acc": 1, "dsc": "()Z"}, {"nme": "updatesAreDetected", "acc": 1, "dsc": "(I)Z"}, {"nme": "usesLocalFilePerTable", "acc": 1, "dsc": "()Z"}, {"nme": "usesLocalFiles", "acc": 1, "dsc": "()Z"}, {"nme": "isReadOnly", "acc": 1, "dsc": "()Z", "exs": ["java/sql/SQLException"]}, {"nme": "getAttributes", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/String;Ljava/lang/String;)L<PERSON><PERSON>/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "getBestRowIdentifier", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;IZ)Ljava/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "getColumnPrivileges", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/String;Ljava/lang/String;)L<PERSON><PERSON>/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "getColumns", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/String;Ljava/lang/String;)L<PERSON><PERSON>/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "getCrossReference", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "getSchemas", "acc": 1, "dsc": "()Ljava/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "getCatalogs", "acc": 1, "dsc": "()Ljava/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "getPrimaryKeys", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/String;)Ljava/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "getExportedKeys", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/String;)Ljava/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "appendDummyForeignKeyList", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;)<PERSON>ja<PERSON>/lang/StringBuilder;"}, {"nme": "getImportedKeys", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/String;)Ljava/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "getIndexInfo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;ZZ)Ljava/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "getProcedureColumns", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/String;Ljava/lang/String;)L<PERSON><PERSON>/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "getProcedures", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/String;)Ljava/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "getSuperTables", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/String;)Ljava/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "getSuperTypes", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/String;)Ljava/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "getTablePrivileges", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/String;)Ljava/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "getTables", "acc": 33, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/String;[Ljava/lang/String;)L<PERSON><PERSON>/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "getTableTypes", "acc": 1, "dsc": "()Ljava/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "getTypeInfo", "acc": 1, "dsc": "()Ljava/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "getUDTs", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;[I)Lja<PERSON>/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "getVersionColumns", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/String;)Ljava/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "getGeneratedKeys", "acc": 131073, "dsc": "()Ljava/sql/ResultSet;", "exs": ["java/sql/SQLException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "createStruct", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/sql/Struct;", "exs": ["java/sql/SQLException"]}, {"nme": "getFunctionColumns", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/String;Ljava/lang/String;)L<PERSON><PERSON>/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "finalize", "acc": 4, "dsc": "()V", "exs": ["java/lang/Throwable"]}, {"nme": "unquoteIdentifier", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "lambda$getTables$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "access$100", "acc": 4104, "dsc": "(Lorg/sqlite/jdbc3/JDBC3DatabaseMetaData;)Lorg/sqlite/SQLiteConnection;"}, {"nme": "access$200", "acc": 4104, "dsc": "(Lorg/sqlite/jdbc3/JDBC3DatabaseMetaData;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "access$300", "acc": 4104, "dsc": "(Lorg/sqlite/jdbc3/JDBC3DatabaseMetaData;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "access$400", "acc": 4104, "dsc": "(Lorg/sqlite/jdbc3/JDBC3DatabaseMetaData;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "access$500", "acc": 4104, "dsc": "(Lorg/sqlite/jdbc3/JDBC3DatabaseMetaData;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "access$600", "acc": 4104, "dsc": "(Lorg/sqlite/jdbc3/JDBC3DatabaseMetaData;)Lorg/sqlite/SQLiteConnection;"}, {"nme": "access$700", "acc": 4104, "dsc": "(Lorg/sqlite/jdbc3/JDBC3DatabaseMetaData;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "access$800", "acc": 4104, "dsc": "(Lorg/sqlite/jdbc3/JDBC3DatabaseMetaData;)Lorg/sqlite/SQLiteConnection;"}, {"nme": "access$900", "acc": 4104, "dsc": "(Lorg/sqlite/jdbc3/JDBC3DatabaseMetaData;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 10, "nme": "<PERSON><PERSON><PERSON>", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 10, "nme": "driverVersion", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 28, "nme": "TYPE_INTEGER", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 28, "nme": "TYPE_VARCHAR", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 28, "nme": "TYPE_FLOAT", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 26, "nme": "RULE_MAP", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Integer;>;"}, {"acc": 28, "nme": "PK_UNNAMED_PATTERN", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 28, "nme": "PK_NAMED_PATTERN", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}]}, "org/sqlite/date/FastDatePrinter$NumberRule.class": {"ver": 52, "acc": 1536, "nme": "org/sqlite/date/FastDatePrinter$NumberRule", "super": "java/lang/Object", "mthds": [{"nme": "appendTo", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;I)V"}], "flds": []}, "org/sqlite/core/CoreStatement.class": {"ver": 52, "acc": 1057, "nme": "org/sqlite/core/CoreStatement", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Lorg/sqlite/SQLiteConnection;)V"}, {"nme": "getDatabase", "acc": 1, "dsc": "()Lorg/sqlite/core/DB;"}, {"nme": "getConnectionConfig", "acc": 1, "dsc": "()Lorg/sqlite/SQLiteConnectionConfig;"}, {"nme": "checkOpen", "acc": 20, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "isOpen", "acc": 0, "dsc": "()Z", "exs": ["java/sql/SQLException"]}, {"nme": "exec", "acc": 4, "dsc": "()Z", "exs": ["java/sql/SQLException"]}, {"nme": "exec", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z", "exs": ["java/sql/SQLException"]}, {"nme": "internalClose", "acc": 4, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "notifyFirstStatementExecuted", "acc": 4, "dsc": "()V"}, {"nme": "execute<PERSON>uery", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)Ljava/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "checkIndex", "acc": 4, "dsc": "(I)V", "exs": ["java/sql/SQLException"]}], "flds": [{"acc": 17, "nme": "conn", "dsc": "Lorg/sqlite/SQLiteConnection;"}, {"acc": 20, "nme": "rs", "dsc": "Lorg/sqlite/core/CoreResultSet;"}, {"acc": 1, "nme": "pointer", "dsc": "Lorg/sqlite/core/SafeStmtPtr;"}, {"acc": 4, "nme": "sql", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4, "nme": "batchPos", "dsc": "I"}, {"acc": 4, "nme": "batch", "dsc": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 4, "nme": "resultsWaiting", "dsc": "Z"}]}, "org/sqlite/SQLiteUpdateListener$Type.class": {"ver": 52, "acc": 16433, "nme": "org/sqlite/SQLiteUpdateListener$Type", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lorg/sqlite/SQLiteUpdateListener$Type;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;)Lorg/sqlite/SQLiteUpdateListener$Type;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "INSERT", "dsc": "Lorg/sqlite/SQLiteUpdateListener$Type;"}, {"acc": 16409, "nme": "DELETE", "dsc": "Lorg/sqlite/SQLiteUpdateListener$Type;"}, {"acc": 16409, "nme": "UPDATE", "dsc": "Lorg/sqlite/SQLiteUpdateListener$Type;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lorg/sqlite/SQLiteUpdateListener$Type;"}]}, "org/sqlite/date/FastDateParser$TimeZoneStrategy.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/date/FastDateParser$TimeZoneStrategy", "super": "org/sqlite/date/FastDateParser$Strategy", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/util/Locale;)V"}, {"nme": "addRegex", "acc": 0, "dsc": "(Lorg/sqlite/date/FastDateParser;Lja<PERSON>/lang/StringBuilder;)Z"}, {"nme": "setCalendar", "acc": 0, "dsc": "(Lorg/sqlite/date/FastDateParser;Ljava/util/Calendar;Ljava/lang/String;)V"}], "flds": [{"acc": 18, "nme": "validTimeZoneChars", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "tzNames", "dsc": "Ljava/util/SortedMap;", "sig": "Ljava/util/SortedMap<Ljava/lang/String;Ljava/util/TimeZone;>;"}, {"acc": 26, "nme": "ID", "dsc": "I", "val": 0}, {"acc": 26, "nme": "LONG_STD", "dsc": "I", "val": 1}, {"acc": 26, "nme": "SHORT_STD", "dsc": "I", "val": 2}, {"acc": 26, "nme": "LONG_DST", "dsc": "I", "val": 3}, {"acc": 26, "nme": "SHORT_DST", "dsc": "I", "val": 4}]}, "org/sqlite/core/SafeStmtPtr$SafePtrConsumer.class": {"ver": 52, "acc": 1537, "nme": "org/sqlite/core/SafeStmtPtr$SafePtrConsumer", "super": "java/lang/Object", "mthds": [{"nme": "run", "acc": 1025, "dsc": "(Lorg/sqlite/core/DB;J)V", "sig": "(Lorg/sqlite/core/DB;J)V^TE;", "exs": ["java/lang/Throwable"]}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/FunctionalInterface;"}]}, "org/sqlite/date/FastDatePrinter$TwoDigitMonthField.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/date/FastDatePrinter$TwoDigitMonthField", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "estimateLength", "acc": 1, "dsc": "()I"}, {"nme": "appendTo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;<PERSON><PERSON><PERSON>/util/Calendar;)V"}, {"nme": "appendTo", "acc": 17, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;I)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "INSTANCE", "dsc": "Lorg/sqlite/date/FastDatePrinter$TwoDigitMonthField;"}]}, "org/sqlite/jdbc3/JDBC3DatabaseMetaData$PrimaryKeyFinder.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/jdbc3/JDBC3DatabaseMetaData$PrimaryKeyFinder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/sqlite/jdbc3/JDBC3DatabaseMetaData;Ljava/lang/String;)V", "exs": ["java/sql/SQLException"]}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getColumns", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 0, "nme": "table", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "pkName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "pkColumns", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lorg/sqlite/jdbc3/JDBC3DatabaseMetaData;"}]}, "org/sqlite/SQLiteConfig$Pragma.class": {"ver": 52, "acc": 16433, "nme": "org/sqlite/SQLiteConfig$Pragma", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lorg/sqlite/SQLiteConfig$Pragma;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/sqlite/SQLiteConfig$Pragma;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/String;[Ljava/lang/String;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;)V"}, {"nme": "getPragmaName", "acc": 17, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "OPEN_MODE", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "SHARED_CACHE", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "LOAD_EXTENSION", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "CACHE_SIZE", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "MMAP_SIZE", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "CASE_SENSITIVE_LIKE", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "COUNT_CHANGES", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "DEFAULT_CACHE_SIZE", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "DEFER_FOREIGN_KEYS", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "EMPTY_RESULT_CALLBACKS", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "ENCODING", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "FOREIGN_KEYS", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "FULL_COLUMN_NAMES", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "FULL_SYNC", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "INCREMENTAL_VACUUM", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "JOURNAL_MODE", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "JOURNAL_SIZE_LIMIT", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "LEGACY_ALTER_TABLE", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "LEGACY_FILE_FORMAT", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "LOCKING_MODE", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "PAGE_SIZE", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "MAX_PAGE_COUNT", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "READ_UNCOMMITTED", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "RECURSIVE_TRIGGERS", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "REVERSE_UNORDERED_SELECTS", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "SECURE_DELETE", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "SHORT_COLUMN_NAMES", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "SYNCHRONOUS", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "TEMP_STORE", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "TEMP_STORE_DIRECTORY", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "USER_VERSION", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "APPLICATION_ID", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "LIMIT_LENGTH", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "LIMIT_SQL_LENGTH", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "LIMIT_COLUMN", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "LIMIT_EXPR_DEPTH", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "LIMIT_COMPOUND_SELECT", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "LIMIT_VDBE_OP", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "LIMIT_FUNCTION_ARG", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "LIMIT_ATTACHED", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "LIMIT_LIKE_PATTERN_LENGTH", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "LIMIT_VARIABLE_NUMBER", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "LIMIT_TRIGGER_DEPTH", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "LIMIT_WORKER_THREADS", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "LIMIT_PAGE_COUNT", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "TRANSACTION_MODE", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "DATE_PRECISION", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "DATE_CLASS", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "DATE_STRING_FORMAT", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "BUSY_TIMEOUT", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "HEXKEY_MODE", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "PASSWORD", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 16409, "nme": "JDBC_EXPLICIT_READONLY", "dsc": "Lorg/sqlite/SQLiteConfig$Pragma;"}, {"acc": 17, "nme": "pragmaName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 17, "nme": "choices", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 17, "nme": "description", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lorg/sqlite/SQLiteConfig$Pragma;"}]}, "org/sqlite/jdbc3/JDBC3DatabaseMetaData$ImportedKeyFinder$ForeignKey.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/jdbc3/JDBC3DatabaseMetaData$ImportedKeyFinder$ForeignKey", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/sqlite/jdbc3/JDBC3DatabaseMetaData$ImportedKeyFinder;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "getFkName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "addColumnMapping", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getColumnMapping", "acc": 1, "dsc": "(I)[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getColumnMappingCount", "acc": 1, "dsc": "()I"}, {"nme": "getPkTableName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getFkTableName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getOnUpdate", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getOnDelete", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getMatch", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "fkName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "pkTableName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "fkTableName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "fkColNames", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 18, "nme": "pkColNames", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 18, "nme": "onUpdate", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "onDelete", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "match", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4112, "nme": "this$1", "dsc": "Lorg/sqlite/jdbc3/JDBC3DatabaseMetaData$ImportedKeyFinder;"}]}, "org/sqlite/SQLiteJDBCLoader.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/SQLiteJDBCLoader", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "initialize", "acc": 41, "dsc": "()Z", "exs": ["java/lang/Exception"]}, {"nme": "getTempDir", "acc": 10, "dsc": "()Ljava/io/File;"}, {"nme": "cleanup", "acc": 8, "dsc": "()V"}, {"nme": "isNativeMode", "acc": 9, "dsc": "()Z", "exs": ["java/lang/Exception"]}, {"nme": "md5sum", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;)<PERSON>ja<PERSON>/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "contentsEquals", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;L<PERSON><PERSON>/io/InputStream;)Z", "exs": ["java/io/IOException"]}, {"nme": "extractAndLoadLibraryFile", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z", "exs": ["org/sqlite/FileException"]}, {"nme": "getResourceAsStream", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/io/InputStream;"}, {"nme": "loadNativeLibrary", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "loadNativeLibraryJdk", "acc": 10, "dsc": "()Z"}, {"nme": "loadSQLiteNativeLibrary", "acc": 10, "dsc": "()V", "exs": ["java/lang/Exception"]}, {"nme": "getNativeLibraryFolderForTheCurrentOS", "acc": 10, "dsc": "()V"}, {"nme": "getMajorVersion", "acc": 9, "dsc": "()I"}, {"nme": "getMinorVersion", "acc": 9, "dsc": "()I"}, {"nme": "getVersion", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$cleanup$1", "acc": 4106, "dsc": "(Ljava/nio/file/Path;)V"}, {"nme": "lambda$cleanup$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/nio/file/Path;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "logger", "dsc": "Lorg/slf4j/Logger;"}, {"acc": 26, "nme": "LOCK_EXT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": ".lck"}, {"acc": 10, "nme": "extracted", "dsc": "Z"}]}, "org/sqlite/date/FastDateParser$2.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/date/FastDateParser$2", "super": "org/sqlite/date/FastDateParser$NumberStrategy", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(I)V"}, {"nme": "modify", "acc": 0, "dsc": "(I)I"}], "flds": []}, "org/sqlite/jdbc3/JDBC3DatabaseMetaData$ImportedKeyFinder.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/jdbc3/JDBC3DatabaseMetaData$ImportedKeyFinder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/sqlite/jdbc3/JDBC3DatabaseMetaData;Ljava/lang/String;)V", "exs": ["java/sql/SQLException"]}, {"nme": "getForeignKeyNames", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/List<Ljava/lang/String;>;", "exs": ["java/sql/SQLException"]}, {"nme": "getFkTableName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getFkList", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/sqlite/jdbc3/JDBC3DatabaseMetaData$ImportedKeyFinder$ForeignKey;>;"}], "flds": [{"acc": 18, "nme": "FK_NAMED_PATTERN", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 18, "nme": "fkTableName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "fkList", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/sqlite/jdbc3/JDBC3DatabaseMetaData$ImportedKeyFinder$ForeignKey;>;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lorg/sqlite/jdbc3/JDBC3DatabaseMetaData;"}]}, "org/sqlite/date/FastDatePrinter$UnpaddedNumberField.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/date/FastDatePrinter$UnpaddedNumberField", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(I)V"}, {"nme": "estimateLength", "acc": 1, "dsc": "()I"}, {"nme": "appendTo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;<PERSON><PERSON><PERSON>/util/Calendar;)V"}, {"nme": "appendTo", "acc": 17, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;I)V"}], "flds": [{"acc": 18, "nme": "m<PERSON>ield", "dsc": "I"}]}, "org/sqlite/core/SafeStmtPtr$SafePtrIntFunction.class": {"ver": 52, "acc": 1537, "nme": "org/sqlite/core/SafeStmtPtr$SafePtrIntFunction", "super": "java/lang/Object", "mthds": [{"nme": "run", "acc": 1025, "dsc": "(Lorg/sqlite/core/DB;J)I", "sig": "(Lorg/sqlite/core/DB;J)I^TE;", "exs": ["java/lang/Throwable"]}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/FunctionalInterface;"}]}, "org/sqlite/SQLiteLimits.class": {"ver": 52, "acc": 16433, "nme": "org/sqlite/SQLiteLimits", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lorg/sqlite/SQLiteLimits;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/sqlite/SQLiteLimits;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;II)V", "sig": "(I)V"}, {"nme": "getId", "acc": 1, "dsc": "()I"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "SQLITE_LIMIT_LENGTH", "dsc": "Lorg/sqlite/SQLiteLimits;"}, {"acc": 16409, "nme": "SQLITE_LIMIT_SQL_LENGTH", "dsc": "Lorg/sqlite/SQLiteLimits;"}, {"acc": 16409, "nme": "SQLITE_LIMIT_COLUMN", "dsc": "Lorg/sqlite/SQLiteLimits;"}, {"acc": 16409, "nme": "SQLITE_LIMIT_EXPR_DEPTH", "dsc": "Lorg/sqlite/SQLiteLimits;"}, {"acc": 16409, "nme": "SQLITE_LIMIT_COMPOUND_SELECT", "dsc": "Lorg/sqlite/SQLiteLimits;"}, {"acc": 16409, "nme": "SQLITE_LIMIT_VDBE_OP", "dsc": "Lorg/sqlite/SQLiteLimits;"}, {"acc": 16409, "nme": "SQLITE_LIMIT_FUNCTION_ARG", "dsc": "Lorg/sqlite/SQLiteLimits;"}, {"acc": 16409, "nme": "SQLITE_LIMIT_ATTACHED", "dsc": "Lorg/sqlite/SQLiteLimits;"}, {"acc": 16409, "nme": "SQLITE_LIMIT_LIKE_PATTERN_LENGTH", "dsc": "Lorg/sqlite/SQLiteLimits;"}, {"acc": 16409, "nme": "SQLITE_LIMIT_VARIABLE_NUMBER", "dsc": "Lorg/sqlite/SQLiteLimits;"}, {"acc": 16409, "nme": "SQLITE_LIMIT_TRIGGER_DEPTH", "dsc": "Lorg/sqlite/SQLiteLimits;"}, {"acc": 16409, "nme": "SQLITE_LIMIT_WORKER_THREADS", "dsc": "Lorg/sqlite/SQLiteLimits;"}, {"acc": 16409, "nme": "SQLITE_LIMIT_PAGE_COUNT", "dsc": "Lorg/sqlite/SQLiteLimits;"}, {"acc": 18, "nme": "id", "dsc": "I"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lorg/sqlite/SQLiteLimits;"}]}, "org/sqlite/date/FastDatePrinter$CharacterLiteral.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/date/FastDatePrinter$CharacterLiteral", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(C)V"}, {"nme": "estimateLength", "acc": 1, "dsc": "()I"}, {"nme": "appendTo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;<PERSON><PERSON><PERSON>/util/Calendar;)V"}], "flds": [{"acc": 18, "nme": "mValue", "dsc": "C"}]}, "org/sqlite/date/FastDateParser$4.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/date/FastDateParser$4", "super": "org/sqlite/date/FastDateParser$NumberStrategy", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(I)V"}, {"nme": "modify", "acc": 0, "dsc": "(I)I"}], "flds": []}, "org/sqlite/date/FormatCache$MultipartKey.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/date/FormatCache$MultipartKey", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 129, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "keys", "dsc": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 2, "nme": "hashCode", "dsc": "I"}]}, "org/sqlite/javax/SQLitePooledConnection.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/javax/SQLitePooledConnection", "super": "org/sqlite/jdbc4/JDBC4PooledConnection", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Lorg/sqlite/SQLiteConnection;)V"}, {"nme": "getPhysicalConn", "acc": 1, "dsc": "()Lorg/sqlite/SQLiteConnection;"}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "getConnection", "acc": 1, "dsc": "()Ljava/sql/Connection;", "exs": ["java/sql/SQLException"]}, {"nme": "addConnectionEventListener", "acc": 1, "dsc": "(Ljavax/sql/ConnectionEventListener;)V"}, {"nme": "removeConnectionEventListener", "acc": 1, "dsc": "(Ljavax/sql/ConnectionEventListener;)V"}, {"nme": "getListeners", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljavax/sql/ConnectionEventListener;>;"}], "flds": [{"acc": 4, "nme": "physicalConn", "dsc": "Lorg/sqlite/SQLiteConnection;"}, {"acc": 68, "nme": "handleConn", "dsc": "Ljava/sql/Connection;"}, {"acc": 4, "nme": "listeners", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljavax/sql/ConnectionEventListener;>;"}]}, "org/sqlite/date/ExceptionUtils.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/date/ExceptionUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "rethrow", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "<R:Lja<PERSON>/lang/Object;>(Ljava/lang/Throwable;)TR;"}, {"nme": "typeErasure", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "<R:L<PERSON><PERSON>/lang/Object;T:Ljava/lang/Throwable;>(Ljava/lang/Throwable;)TR;^TT;", "exs": ["java/lang/Throwable"]}], "flds": []}, "org/sqlite/Function.class": {"ver": 52, "acc": 1057, "nme": "org/sqlite/Function", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "create", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/sql/Connection;Lja<PERSON>/lang/String;Lorg/sqlite/Function;)V", "exs": ["java/sql/SQLException"]}, {"nme": "create", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/sql/Connection;<PERSON>ja<PERSON>/lang/String;Lorg/sqlite/Function;I)V", "exs": ["java/sql/SQLException"]}, {"nme": "create", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/sql/Connection;<PERSON>ja<PERSON>/lang/String;Lorg/sqlite/Function;II)V", "exs": ["java/sql/SQLException"]}, {"nme": "destroy", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/sql/Connection;<PERSON><PERSON><PERSON>/lang/String;I)V", "exs": ["java/sql/SQLException"]}, {"nme": "destroy", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/sql/Connection;Lja<PERSON>/lang/String;)V", "exs": ["java/sql/SQLException"]}, {"nme": "xFunc", "acc": 1028, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "args", "acc": 52, "dsc": "()I", "exs": ["java/sql/SQLException"]}, {"nme": "result", "acc": 52, "dsc": "([B)V", "exs": ["java/sql/SQLException"]}, {"nme": "result", "acc": 52, "dsc": "(D)V", "exs": ["java/sql/SQLException"]}, {"nme": "result", "acc": 52, "dsc": "(I)V", "exs": ["java/sql/SQLException"]}, {"nme": "result", "acc": 52, "dsc": "(J)V", "exs": ["java/sql/SQLException"]}, {"nme": "result", "acc": 52, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "result", "acc": 52, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/sql/SQLException"]}, {"nme": "error", "acc": 52, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/sql/SQLException"]}, {"nme": "value_text", "acc": 52, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "value_blob", "acc": 52, "dsc": "(I)[B", "exs": ["java/sql/SQLException"]}, {"nme": "value_double", "acc": 52, "dsc": "(I)D", "exs": ["java/sql/SQLException"]}, {"nme": "value_int", "acc": 52, "dsc": "(I)I", "exs": ["java/sql/SQLException"]}, {"nme": "value_long", "acc": 52, "dsc": "(I)J", "exs": ["java/sql/SQLException"]}, {"nme": "value_type", "acc": 52, "dsc": "(I)I", "exs": ["java/sql/SQLException"]}, {"nme": "checkContext", "acc": 2, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "checkValue", "acc": 2, "dsc": "(I)V", "exs": ["java/sql/SQLException"]}], "flds": [{"acc": 25, "nme": "FLAG_DETERMINISTIC", "dsc": "I", "val": 2048}, {"acc": 2, "nme": "conn", "dsc": "Lorg/sqlite/SQLiteConnection;"}, {"acc": 2, "nme": "db", "dsc": "Lorg/sqlite/core/DB;"}, {"acc": 0, "nme": "context", "dsc": "J"}, {"acc": 0, "nme": "value", "dsc": "J"}, {"acc": 0, "nme": "args", "dsc": "I"}]}, "org/sqlite/date/FastDateParser$ISO8601TimeZoneStrategy.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/date/FastDateParser$ISO8601TimeZoneStrategy", "super": "org/sqlite/date/FastDateParser$Strategy", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "addRegex", "acc": 0, "dsc": "(Lorg/sqlite/date/FastDateParser;Lja<PERSON>/lang/StringBuilder;)Z"}, {"nme": "setCalendar", "acc": 0, "dsc": "(Lorg/sqlite/date/FastDateParser;Ljava/util/Calendar;Ljava/lang/String;)V"}, {"nme": "getStrategy", "acc": 8, "dsc": "(I)Lorg/sqlite/date/FastDateParser$Strategy;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "pattern", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "ISO_8601_1_STRATEGY", "dsc": "Lorg/sqlite/date/FastDateParser$Strategy;"}, {"acc": 26, "nme": "ISO_8601_2_STRATEGY", "dsc": "Lorg/sqlite/date/FastDateParser$Strategy;"}, {"acc": 26, "nme": "ISO_8601_3_STRATEGY", "dsc": "Lorg/sqlite/date/FastDateParser$Strategy;"}]}, "org/sqlite/date/DateParser.class": {"ver": 52, "acc": 1537, "nme": "org/sqlite/date/DateParser", "super": "java/lang/Object", "mthds": [{"nme": "parse", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Date;", "exs": ["java/text/ParseException"]}, {"nme": "parse", "acc": 1025, "dsc": "(L<PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/text/ParsePosition;)Ljava/util/Date;"}, {"nme": "getPattern", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getTimeZone", "acc": 1025, "dsc": "()Ljava/util/TimeZone;"}, {"nme": "getLocale", "acc": 1025, "dsc": "()Ljava/util/Locale;"}, {"nme": "parseObject", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/text/ParseException"]}, {"nme": "parseObject", "acc": 1025, "dsc": "(L<PERSON><PERSON>/lang/String;L<PERSON><PERSON>/text/ParsePosition;)Ljava/lang/Object;"}], "flds": []}, "org/sqlite/javax/SQLitePooledConnection$1.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/javax/SQLitePooledConnection$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/sqlite/javax/SQLitePooledConnection;)V"}, {"nme": "invoke", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/reflect/Method;[Ljava/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Throwable"]}], "flds": [{"acc": 0, "nme": "isClosed", "dsc": "Z"}, {"acc": 4112, "nme": "this$0", "dsc": "Lorg/sqlite/javax/SQLitePooledConnection;"}]}, "org/sqlite/jdbc4/JDBC4Statement.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/jdbc4/JDBC4Statement", "super": "org/sqlite/jdbc3/JDBC3Statement", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/sqlite/SQLiteConnection;)V"}, {"nme": "unwrap", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>ja<PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;)TT;", "exs": ["java/lang/ClassCastException"]}, {"nme": "isWrapperFor", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "isClosed", "acc": 1, "dsc": "()Z"}, {"nme": "closeOnCompletion", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "isCloseOnCompletion", "acc": 1, "dsc": "()Z", "exs": ["java/sql/SQLException"]}, {"nme": "setPoolable", "acc": 1, "dsc": "(Z)V", "exs": ["java/sql/SQLException"]}, {"nme": "isPoolable", "acc": 1, "dsc": "()Z", "exs": ["java/sql/SQLException"]}], "flds": [{"acc": 2, "nme": "closed", "dsc": "Z"}, {"acc": 0, "nme": "closeOnCompletion", "dsc": "Z"}]}, "META-INF/versions/9/org/sqlite/nativeimage/SqliteJdbcFeature.class": {"ver": 53, "acc": 33, "nme": "org/sqlite/nativeimage/SqliteJdbcFeature", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "beforeAnalysis", "acc": 1, "dsc": "(Lorg/graalvm/nativeimage/hosted/Feature$BeforeAnalysisAccess;)V"}, {"nme": "nativeDbReachable", "acc": 2, "dsc": "(Lorg/graalvm/nativeimage/hosted/Feature$DuringAnalysisAccess;)V"}, {"nme": "handleLibraryResources", "acc": 2, "dsc": "()V"}, {"nme": "registerJNICalls", "acc": 2, "dsc": "()V"}, {"nme": "method", "acc": 130, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Lja<PERSON>/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/String;[Ljava/lang/Class<*>;)Ljava/lang/reflect/Method;"}, {"nme": "fields", "acc": 130, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;[<PERSON>ja<PERSON>/lang/String;)[Ljava/lang/reflect/Field;", "sig": "(L<PERSON><PERSON>/lang/Class<*>;[Ljava/lang/String;)[Ljava/lang/reflect/Field;"}], "flds": []}, "org/sqlite/date/FormatCache.class": {"ver": 52, "acc": 1056, "nme": "org/sqlite/date/FormatCache", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "getInstance", "acc": 1, "dsc": "()Ljava/text/Format;", "sig": "()TF;"}, {"nme": "getInstance", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/TimeZone;<PERSON>ja<PERSON>/util/Locale;)Ljava/text/Format;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/TimeZone;<PERSON>ja<PERSON>/util/Locale;)TF;"}, {"nme": "createInstance", "acc": 1028, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/TimeZone;<PERSON>ja<PERSON>/util/Locale;)Ljava/text/Format;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/TimeZone;<PERSON>ja<PERSON>/util/Locale;)TF;"}, {"nme": "getDateTimeInstance", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Integer;<PERSON><PERSON><PERSON>/lang/Integer;<PERSON><PERSON><PERSON>/util/TimeZone;<PERSON><PERSON><PERSON>/util/Locale;)Ljava/text/Format;", "sig": "(<PERSON><PERSON><PERSON>/lang/Integer;<PERSON><PERSON><PERSON>/lang/Integer;<PERSON><PERSON><PERSON>/util/TimeZone;<PERSON><PERSON><PERSON>/util/Locale;)TF;"}, {"nme": "getDateTimeInstance", "acc": 0, "dsc": "(IILjava/util/TimeZone;Ljava/util/Locale;)Ljava/text/Format;", "sig": "(IILjava/util/TimeZone;Ljava/util/Locale;)TF;"}, {"nme": "getDateInstance", "acc": 0, "dsc": "(ILjava/util/TimeZone;Ljava/util/Locale;)Ljava/text/Format;", "sig": "(ILjava/util/TimeZone;Ljava/util/Locale;)TF;"}, {"nme": "getTimeInstance", "acc": 0, "dsc": "(ILjava/util/TimeZone;Ljava/util/Locale;)Ljava/text/Format;", "sig": "(ILjava/util/TimeZone;Ljava/util/Locale;)TF;"}, {"nme": "getPatternForStyle", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Integer;<PERSON><PERSON><PERSON>/lang/Integer;<PERSON><PERSON><PERSON>/util/Locale;)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "NONE", "dsc": "I", "val": -1}, {"acc": 18, "nme": "cInstanceCache", "dsc": "Ljava/util/concurrent/ConcurrentMap;", "sig": "Ljava/util/concurrent/ConcurrentMap<Lorg/sqlite/date/FormatCache$MultipartKey;TF;>;"}, {"acc": 26, "nme": "cDateTimeInstanceCache", "dsc": "Ljava/util/concurrent/ConcurrentMap;", "sig": "Ljava/util/concurrent/ConcurrentMap<Lorg/sqlite/date/FormatCache$MultipartKey;Ljava/lang/String;>;"}]}, "org/sqlite/jdbc4/JDBC4DatabaseMetaData.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/jdbc4/JDBC4DatabaseMetaData", "super": "org/sqlite/jdbc3/JDBC3DatabaseMetaData", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/sqlite/SQLiteConnection;)V"}, {"nme": "unwrap", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>ja<PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;)TT;", "exs": ["java/lang/ClassCastException"]}, {"nme": "isWrapperFor", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "getRowIdLifetime", "acc": 1, "dsc": "()Ljava/sql/RowIdLifetime;", "exs": ["java/sql/SQLException"]}, {"nme": "getSchemas", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "supportsStoredFunctionsUsingCallSyntax", "acc": 1, "dsc": "()Z", "exs": ["java/sql/SQLException"]}, {"nme": "autoCommitFailureClosesAllResultSets", "acc": 1, "dsc": "()Z", "exs": ["java/sql/SQLException"]}, {"nme": "getClientInfoProperties", "acc": 1, "dsc": "()Ljava/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "getFunctions", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/String;)Ljava/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "getPseudoColumns", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/String;Ljava/lang/String;)L<PERSON><PERSON>/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "generatedKeyAlwaysReturned", "acc": 1, "dsc": "()Z", "exs": ["java/sql/SQLException"]}], "flds": []}, "org/sqlite/jdbc3/JDBC3PreparedStatement.class": {"ver": 52, "acc": 1057, "nme": "org/sqlite/jdbc3/JDBC3PreparedStatement", "super": "org/sqlite/core/CorePreparedStatement", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Lorg/sqlite/SQLiteConnection;Ljava/lang/String;)V", "exs": ["java/sql/SQLException"]}, {"nme": "clearParameters", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "execute", "acc": 1, "dsc": "()Z", "exs": ["java/sql/SQLException"]}, {"nme": "execute<PERSON>uery", "acc": 1, "dsc": "()Ljava/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "executeUpdate", "acc": 1, "dsc": "()I", "exs": ["java/sql/SQLException"]}, {"nme": "executeLargeUpdate", "acc": 1, "dsc": "()J", "exs": ["java/sql/SQLException"]}, {"nme": "addBatch", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "getParameterMetaData", "acc": 1, "dsc": "()Ljava/sql/ParameterMetaData;"}, {"nme": "getParameterCount", "acc": 1, "dsc": "()I", "exs": ["java/sql/SQLException"]}, {"nme": "getParameterClassName", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "getParameterTypeName", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "getParameterType", "acc": 1, "dsc": "(I)I", "exs": ["java/sql/SQLException"]}, {"nme": "getParameterMode", "acc": 1, "dsc": "(I)I"}, {"nme": "getPrecision", "acc": 1, "dsc": "(I)I"}, {"nme": "getScale", "acc": 1, "dsc": "(I)I"}, {"nme": "isNullable", "acc": 1, "dsc": "(I)I"}, {"nme": "isSigned", "acc": 1, "dsc": "(I)Z"}, {"nme": "getStatement", "acc": 1, "dsc": "()Ljava/sql/Statement;"}, {"nme": "setBigDecimal", "acc": 1, "dsc": "(ILjava/math/BigDecimal;)V", "exs": ["java/sql/SQLException"]}, {"nme": "readBytes", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;I)[B", "exs": ["java/sql/SQLException"]}, {"nme": "setBinaryStream", "acc": 1, "dsc": "(ILjava/io/InputStream;I)V", "exs": ["java/sql/SQLException"]}, {"nme": "setAsciiStream", "acc": 1, "dsc": "(ILjava/io/InputStream;I)V", "exs": ["java/sql/SQLException"]}, {"nme": "setUnicodeStream", "acc": 1, "dsc": "(ILjava/io/InputStream;I)V", "exs": ["java/sql/SQLException"]}, {"nme": "setBoolean", "acc": 1, "dsc": "(IZ)V", "exs": ["java/sql/SQLException"]}, {"nme": "setByte", "acc": 1, "dsc": "(IB)V", "exs": ["java/sql/SQLException"]}, {"nme": "setBytes", "acc": 1, "dsc": "(I[B)V", "exs": ["java/sql/SQLException"]}, {"nme": "setDouble", "acc": 1, "dsc": "(ID)V", "exs": ["java/sql/SQLException"]}, {"nme": "setFloat", "acc": 1, "dsc": "(IF)V", "exs": ["java/sql/SQLException"]}, {"nme": "setInt", "acc": 1, "dsc": "(II)V", "exs": ["java/sql/SQLException"]}, {"nme": "setLong", "acc": 1, "dsc": "(IJ)V", "exs": ["java/sql/SQLException"]}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(II)V", "exs": ["java/sql/SQLException"]}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(II<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/sql/SQLException"]}, {"nme": "setObject", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "exs": ["java/sql/SQLException"]}, {"nme": "setObject", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;I)V", "exs": ["java/sql/SQLException"]}, {"nme": "setObject", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;II)V", "exs": ["java/sql/SQLException"]}, {"nme": "setShort", "acc": 1, "dsc": "(IS)V", "exs": ["java/sql/SQLException"]}, {"nme": "setString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/sql/SQLException"]}, {"nme": "setCharacterStream", "acc": 1, "dsc": "(ILjava/io/Reader;I)V", "exs": ["java/sql/SQLException"]}, {"nme": "setDate", "acc": 1, "dsc": "(ILjava/sql/Date;)V", "exs": ["java/sql/SQLException"]}, {"nme": "setDate", "acc": 1, "dsc": "(ILjava/sql/Date;Ljava/util/Calendar;)V", "exs": ["java/sql/SQLException"]}, {"nme": "setTime", "acc": 1, "dsc": "(ILjava/sql/Time;)V", "exs": ["java/sql/SQLException"]}, {"nme": "setTime", "acc": 1, "dsc": "(ILjava/sql/Time;Ljava/util/Calendar;)V", "exs": ["java/sql/SQLException"]}, {"nme": "setTimestamp", "acc": 1, "dsc": "(ILjava/sql/Timestamp;)V", "exs": ["java/sql/SQLException"]}, {"nme": "setTimestamp", "acc": 1, "dsc": "(ILjava/sql/Timestamp;Ljava/util/Calendar;)V", "exs": ["java/sql/SQLException"]}, {"nme": "getMetaData", "acc": 1, "dsc": "()Ljava/sql/ResultSetMetaData;", "exs": ["java/sql/SQLException"]}, {"nme": "unsupported", "acc": 4, "dsc": "()Ljava/sql/SQLException;"}, {"nme": "invalid", "acc": 4, "dsc": "()Ljava/sql/SQLException;"}, {"nme": "set<PERSON><PERSON>y", "acc": 1, "dsc": "(ILjava/sql/Array;)V", "exs": ["java/sql/SQLException"]}, {"nme": "setBlob", "acc": 1, "dsc": "(ILjava/sql/Blob;)V", "exs": ["java/sql/SQLException"]}, {"nme": "setClob", "acc": 1, "dsc": "(ILjava/sql/<PERSON>lob;)V", "exs": ["java/sql/SQLException"]}, {"nme": "setRef", "acc": 1, "dsc": "(ILjava/sql/Ref;)V", "exs": ["java/sql/SQLException"]}, {"nme": "setURL", "acc": 1, "dsc": "(ILjava/net/URL;)V", "exs": ["java/sql/SQLException"]}, {"nme": "execute", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z", "exs": ["java/sql/SQLException"]}, {"nme": "execute", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Z", "exs": ["java/sql/SQLException"]}, {"nme": "execute", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[I)Z", "exs": ["java/sql/SQLException"]}, {"nme": "execute", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;)Z", "exs": ["java/sql/SQLException"]}, {"nme": "executeUpdate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I", "exs": ["java/sql/SQLException"]}, {"nme": "executeUpdate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)I", "exs": ["java/sql/SQLException"]}, {"nme": "executeUpdate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[I)I", "exs": ["java/sql/SQLException"]}, {"nme": "executeUpdate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;)I", "exs": ["java/sql/SQLException"]}, {"nme": "executeLargeUpdate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)J", "exs": ["java/sql/SQLException"]}, {"nme": "executeLargeUpdate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)J", "exs": ["java/sql/SQLException"]}, {"nme": "executeLargeUpdate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[I)J", "exs": ["java/sql/SQLException"]}, {"nme": "executeLargeUpdate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;)J", "exs": ["java/sql/SQLException"]}, {"nme": "execute<PERSON>uery", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "addBatch", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/sql/SQLException"]}, {"nme": "lambda$executeLargeUpdate$2", "acc": 4098, "dsc": "()<PERSON><PERSON><PERSON>/lang/Long;", "exs": ["java/sql/SQLException"]}, {"nme": "lambda$executeQuery$1", "acc": 4098, "dsc": "()Ljava/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "lambda$execute$0", "acc": 4098, "dsc": "()<PERSON><PERSON><PERSON>/lang/<PERSON>an;", "exs": ["java/sql/SQLException"]}], "flds": []}, "org/sqlite/util/LibraryLoaderUtil.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/util/LibraryLoaderUtil", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getNativeLibResourcePath", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getNativeLibName", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "hasNativeLib", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}], "flds": [{"acc": 25, "nme": "NATIVE_LIB_BASE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "sqlitejdbc"}]}, "org/sqlite/date/FastDatePrinter$TwentyFourHourField.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/date/FastDatePrinter$TwentyFourHourField", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/sqlite/date/FastDatePrinter$NumberRule;)V"}, {"nme": "estimateLength", "acc": 1, "dsc": "()I"}, {"nme": "appendTo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;<PERSON><PERSON><PERSON>/util/Calendar;)V"}, {"nme": "appendTo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;I)V"}], "flds": [{"acc": 18, "nme": "mRule", "dsc": "Lorg/sqlite/date/FastDatePrinter$NumberRule;"}]}, "org/sqlite/jdbc3/JDBC3DatabaseMetaData$LogHolder.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/jdbc3/JDBC3DatabaseMetaData$LogHolder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "access$000", "acc": 4104, "dsc": "()Lorg/slf4j/Logger;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "logger", "dsc": "Lorg/slf4j/Logger;"}]}, "org/sqlite/jdbc3/JDBC3Statement$SQLCallable.class": {"ver": 52, "acc": 1537, "nme": "org/sqlite/jdbc3/JDBC3Statement$SQLCallable", "super": "java/lang/Object", "mthds": [{"nme": "call", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;", "exs": ["java/sql/SQLException"]}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/FunctionalInterface;"}]}, "org/sqlite/SQLiteDataSource.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/SQLiteDataSource", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/sqlite/SQLiteConfig;)V"}, {"nme": "setConfig", "acc": 1, "dsc": "(Lorg/sqlite/SQLiteConfig;)V"}, {"nme": "getConfig", "acc": 1, "dsc": "()Lorg/sqlite/SQLiteConfig;"}, {"nme": "setUrl", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getUrl", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setDatabaseName", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getDatabaseName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setSharedCache", "acc": 1, "dsc": "(Z)V"}, {"nme": "setLoadExtension", "acc": 1, "dsc": "(Z)V"}, {"nme": "setReadOnly", "acc": 1, "dsc": "(Z)V"}, {"nme": "setBusyTimeout", "acc": 1, "dsc": "(I)V"}, {"nme": "setCacheSize", "acc": 1, "dsc": "(I)V"}, {"nme": "setCaseSensitiveLike", "acc": 1, "dsc": "(Z)V"}, {"nme": "setCountChanges", "acc": 1, "dsc": "(Z)V"}, {"nme": "setDefaultCacheSize", "acc": 1, "dsc": "(I)V"}, {"nme": "setEncoding", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setEnforceForeignKeys", "acc": 1, "dsc": "(Z)V"}, {"nme": "setFullColumnNames", "acc": 1, "dsc": "(Z)V"}, {"nme": "setFullSync", "acc": 1, "dsc": "(Z)V"}, {"nme": "setIncrementalVacuum", "acc": 1, "dsc": "(I)V"}, {"nme": "setJournalMode", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setJournalSizeLimit", "acc": 1, "dsc": "(I)V"}, {"nme": "setLegacyFileFormat", "acc": 1, "dsc": "(Z)V"}, {"nme": "setLegacyAlterTable", "acc": 1, "dsc": "(Z)V"}, {"nme": "setLockingMode", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setPageSize", "acc": 1, "dsc": "(I)V"}, {"nme": "setMaxPageCount", "acc": 1, "dsc": "(I)V"}, {"nme": "setReadUncommitted", "acc": 1, "dsc": "(Z)V"}, {"nme": "setRecursiveTriggers", "acc": 1, "dsc": "(Z)V"}, {"nme": "setReverseUnorderedSelects", "acc": 1, "dsc": "(Z)V"}, {"nme": "setShortColumnNames", "acc": 1, "dsc": "(Z)V"}, {"nme": "setSynchronous", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setTempStore", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setTempStoreDirectory", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setTransactionMode", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setUserVersion", "acc": 1, "dsc": "(I)V"}, {"nme": "getConnection", "acc": 1, "dsc": "()Ljava/sql/Connection;", "exs": ["java/sql/SQLException"]}, {"nme": "getConnection", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;)Lorg/sqlite/SQLiteConnection;", "exs": ["java/sql/SQLException"]}, {"nme": "getLogWriter", "acc": 1, "dsc": "()Ljava/io/PrintWriter;", "exs": ["java/sql/SQLException"]}, {"nme": "getLoginTimeout", "acc": 1, "dsc": "()I", "exs": ["java/sql/SQLException"]}, {"nme": "get<PERSON><PERSON>nt<PERSON>og<PERSON>", "acc": 1, "dsc": "()Ljava/util/logging/Logger;", "exs": ["java/sql/SQLFeatureNotSupportedException"]}, {"nme": "setLogWriter", "acc": 1, "dsc": "(Ljava/io/PrintWriter;)V", "exs": ["java/sql/SQLException"]}, {"nme": "setLoginTimeout", "acc": 1, "dsc": "(I)V", "exs": ["java/sql/SQLException"]}, {"nme": "isWrapperFor", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z", "exs": ["java/sql/SQLException"]}, {"nme": "unwrap", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>ja<PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;)TT;", "exs": ["java/sql/SQLException"]}, {"nme": "getConnection", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/sql/Connection;", "exs": ["java/sql/SQLException"]}], "flds": [{"acc": 2, "nme": "config", "dsc": "Lorg/sqlite/SQLiteConfig;"}, {"acc": 130, "nme": "logger", "dsc": "Ljava/io/PrintWriter;"}, {"acc": 2, "nme": "loginTimeout", "dsc": "I"}, {"acc": 2, "nme": "url", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "databaseName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/sqlite/core/SafeStmtPtr$SafePtrLongFunction.class": {"ver": 52, "acc": 1537, "nme": "org/sqlite/core/SafeStmtPtr$SafePtrLongFunction", "super": "java/lang/Object", "mthds": [{"nme": "run", "acc": 1025, "dsc": "(Lorg/sqlite/core/DB;J)J", "sig": "(Lorg/sqlite/core/DB;J)J^TE;", "exs": ["java/lang/Throwable"]}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/FunctionalInterface;"}]}, "org/sqlite/date/DateFormatUtils.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/date/DateFormatUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "formatUTC", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "formatUTC", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Date;<PERSON><PERSON><PERSON>/lang/String;)Lja<PERSON>/lang/String;"}, {"nme": "formatUTC", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Locale;)Ljava/lang/String;"}, {"nme": "formatUTC", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Date;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Locale;)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "format", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "format", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Date;<PERSON><PERSON><PERSON>/lang/String;)Lja<PERSON>/lang/String;"}, {"nme": "format", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Calendar;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "format", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/TimeZone;)Ljava/lang/String;"}, {"nme": "format", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Date;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/TimeZone;)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "format", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Calendar;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/TimeZone;)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "format", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Locale;)Ljava/lang/String;"}, {"nme": "format", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Date;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Locale;)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "format", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Calendar;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Locale;)Ljava/lang/String;"}, {"nme": "format", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/TimeZone;<PERSON><PERSON><PERSON>/util/Locale;)Ljava/lang/String;"}, {"nme": "format", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Date;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/TimeZone;<PERSON><PERSON><PERSON>/util/Locale;)L<PERSON>va/lang/String;"}, {"nme": "format", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Calendar;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/TimeZone;L<PERSON><PERSON>/util/Locale;)Ljava/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "UTC_TIME_ZONE", "dsc": "Ljava/util/TimeZone;"}, {"acc": 25, "nme": "ISO_DATETIME_FORMAT", "dsc": "Lorg/sqlite/date/FastDateFormat;"}, {"acc": 25, "nme": "ISO_DATETIME_TIME_ZONE_FORMAT", "dsc": "Lorg/sqlite/date/FastDateFormat;"}, {"acc": 25, "nme": "ISO_DATE_FORMAT", "dsc": "Lorg/sqlite/date/FastDateFormat;"}, {"acc": 25, "nme": "ISO_DATE_TIME_ZONE_FORMAT", "dsc": "Lorg/sqlite/date/FastDateFormat;"}, {"acc": 25, "nme": "ISO_TIME_FORMAT", "dsc": "Lorg/sqlite/date/FastDateFormat;"}, {"acc": 25, "nme": "ISO_TIME_TIME_ZONE_FORMAT", "dsc": "Lorg/sqlite/date/FastDateFormat;"}, {"acc": 25, "nme": "ISO_TIME_NO_T_FORMAT", "dsc": "Lorg/sqlite/date/FastDateFormat;"}, {"acc": 25, "nme": "ISO_TIME_NO_T_TIME_ZONE_FORMAT", "dsc": "Lorg/sqlite/date/FastDateFormat;"}, {"acc": 25, "nme": "SMTP_DATETIME_FORMAT", "dsc": "Lorg/sqlite/date/FastDateFormat;"}]}, "org/sqlite/core/NativeDB.class": {"ver": 52, "acc": 49, "nme": "org/sqlite/core/NativeDB", "super": "org/sqlite/core/DB", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>java/lang/String;Lorg/sqlite/SQLiteConfig;)V", "exs": ["java/sql/SQLException"]}, {"nme": "load", "acc": 9, "dsc": "()Z", "exs": ["java/lang/Exception"]}, {"nme": "_open", "acc": 36, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "exs": ["java/sql/SQLException"]}, {"nme": "_open_utf8", "acc": 288, "dsc": "([BI)V", "exs": ["java/sql/SQLException"]}, {"nme": "_close", "acc": 292, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "_exec", "acc": 33, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I", "exs": ["java/sql/SQLException"]}, {"nme": "_exec_utf8", "acc": 288, "dsc": "([B)I", "exs": ["java/sql/SQLException"]}, {"nme": "shared_cache", "acc": 289, "dsc": "(Z)I"}, {"nme": "enable_load_extension", "acc": 289, "dsc": "(Z)I"}, {"nme": "interrupt", "acc": 257, "dsc": "()V"}, {"nme": "busy_timeout", "acc": 289, "dsc": "(I)V"}, {"nme": "busy_handler", "acc": 289, "dsc": "(Lorg/sqlite/Bus<PERSON>;)V"}, {"nme": "prepare", "acc": 36, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/sqlite/core/SafeStmtPtr;", "exs": ["java/sql/SQLException"]}, {"nme": "prepare_utf8", "acc": 288, "dsc": "([B)J", "exs": ["java/sql/SQLException"]}, {"nme": "errmsg", "acc": 32, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "errmsg_utf8", "acc": 288, "dsc": "()<PERSON><PERSON><PERSON>/nio/<PERSON>;"}, {"nme": "libversion", "acc": 33, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "libversion_utf8", "acc": 256, "dsc": "()<PERSON><PERSON><PERSON>/nio/<PERSON>;"}, {"nme": "changes", "acc": 289, "dsc": "()J"}, {"nme": "total_changes", "acc": 289, "dsc": "()J"}, {"nme": "finalize", "acc": 292, "dsc": "(J)I"}, {"nme": "step", "acc": 289, "dsc": "(J)I"}, {"nme": "reset", "acc": 289, "dsc": "(J)I"}, {"nme": "clear_bindings", "acc": 289, "dsc": "(J)I"}, {"nme": "bind_parameter_count", "acc": 288, "dsc": "(J)I"}, {"nme": "column_count", "acc": 289, "dsc": "(J)I"}, {"nme": "column_type", "acc": 289, "dsc": "(JI)I"}, {"nme": "column_decltype", "acc": 33, "dsc": "(JI)Ljava/lang/String;"}, {"nme": "column_decltype_utf8", "acc": 288, "dsc": "(JI)Ljava/nio/ByteBuffer;"}, {"nme": "column_table_name", "acc": 33, "dsc": "(JI)Ljava/lang/String;"}, {"nme": "column_table_name_utf8", "acc": 288, "dsc": "(JI)Ljava/nio/ByteBuffer;"}, {"nme": "column_name", "acc": 33, "dsc": "(JI)Ljava/lang/String;"}, {"nme": "column_name_utf8", "acc": 288, "dsc": "(JI)Ljava/nio/ByteBuffer;"}, {"nme": "column_text", "acc": 33, "dsc": "(JI)Ljava/lang/String;"}, {"nme": "column_text_utf8", "acc": 288, "dsc": "(JI)Ljava/nio/ByteBuffer;"}, {"nme": "column_blob", "acc": 289, "dsc": "(JI)[B"}, {"nme": "column_double", "acc": 289, "dsc": "(JI)D"}, {"nme": "column_long", "acc": 289, "dsc": "(JI)J"}, {"nme": "column_int", "acc": 289, "dsc": "(JI)I"}, {"nme": "bind_null", "acc": 288, "dsc": "(JI)I"}, {"nme": "bind_int", "acc": 288, "dsc": "(JII)I"}, {"nme": "bind_long", "acc": 288, "dsc": "(JIJ)I"}, {"nme": "bind_double", "acc": 288, "dsc": "(JID)I"}, {"nme": "bind_text", "acc": 32, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "bind_text_utf8", "acc": 288, "dsc": "(JI[B)I"}, {"nme": "bind_blob", "acc": 288, "dsc": "(JI[B)I"}, {"nme": "result_null", "acc": 289, "dsc": "(J)V"}, {"nme": "result_text", "acc": 33, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "result_text_utf8", "acc": 288, "dsc": "(J[B)V"}, {"nme": "result_blob", "acc": 289, "dsc": "(J[B)V"}, {"nme": "result_double", "acc": 289, "dsc": "(JD)V"}, {"nme": "result_long", "acc": 289, "dsc": "(JJ)V"}, {"nme": "result_int", "acc": 289, "dsc": "(JI)V"}, {"nme": "result_error", "acc": 33, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "result_error_utf8", "acc": 288, "dsc": "(J[B)V"}, {"nme": "value_text", "acc": 33, "dsc": "(Lorg/sqlite/Function;I)Ljava/lang/String;"}, {"nme": "value_text_utf8", "acc": 288, "dsc": "(Lorg/sqlite/Function;I)<PERSON>java/nio/Byte<PERSON>uffer;"}, {"nme": "value_blob", "acc": 289, "dsc": "(Lorg/sqlite/Function;I)[B"}, {"nme": "value_double", "acc": 289, "dsc": "(Lorg/sqlite/Function;I)D"}, {"nme": "value_long", "acc": 289, "dsc": "(Lorg/sqlite/Function;I)J"}, {"nme": "value_int", "acc": 289, "dsc": "(Lorg/sqlite/Function;I)I"}, {"nme": "value_type", "acc": 289, "dsc": "(Lorg/sqlite/Function;I)I"}, {"nme": "create_function", "acc": 33, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/sqlite/Function;II)I", "exs": ["java/sql/SQLException"]}, {"nme": "create_function_utf8", "acc": 288, "dsc": "([BLorg/sqlite/Function;II)I"}, {"nme": "destroy_function", "acc": 33, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I", "exs": ["java/sql/SQLException"]}, {"nme": "destroy_function_utf8", "acc": 288, "dsc": "([B)I"}, {"nme": "create_collation", "acc": 33, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/sqlite/Collation;)I", "exs": ["java/sql/SQLException"]}, {"nme": "create_collation_utf8", "acc": 288, "dsc": "([BLorg/sqlite/Collation;)I"}, {"nme": "destroy_collation", "acc": 33, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I", "exs": ["java/sql/SQLException"]}, {"nme": "destroy_collation_utf8", "acc": 288, "dsc": "([B)I"}, {"nme": "limit", "acc": 289, "dsc": "(II)I", "exs": ["java/sql/SQLException"]}, {"nme": "nameToUtf8ByteArray", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)[B", "exs": ["java/sql/SQLException"]}, {"nme": "backup", "acc": 1, "dsc": "(Ljava/lang/String;Ljava/lang/String;Lorg/sqlite/core/DB$ProgressObserver;)I", "exs": ["java/sql/SQLException"]}, {"nme": "backup", "acc": 1, "dsc": "(Ljava/lang/String;Ljava/lang/String;Lorg/sqlite/core/DB$ProgressObserver;III)I", "exs": ["java/sql/SQLException"]}, {"nme": "backup", "acc": 288, "dsc": "([B[BLorg/sqlite/core/DB$ProgressObserver;III)I", "exs": ["java/sql/SQLException"]}, {"nme": "restore", "acc": 33, "dsc": "(Ljava/lang/String;Ljava/lang/String;Lorg/sqlite/core/DB$ProgressObserver;)I", "exs": ["java/sql/SQLException"]}, {"nme": "restore", "acc": 33, "dsc": "(Ljava/lang/String;Ljava/lang/String;Lorg/sqlite/core/DB$ProgressObserver;III)I", "exs": ["java/sql/SQLException"]}, {"nme": "restore", "acc": 288, "dsc": "([B[BLorg/sqlite/core/DB$ProgressObserver;III)I", "exs": ["java/sql/SQLException"]}, {"nme": "column_metadata", "acc": 288, "dsc": "(J)[[Z"}, {"nme": "set_commit_listener", "acc": 288, "dsc": "(Z)V"}, {"nme": "set_update_listener", "acc": 288, "dsc": "(Z)V"}, {"nme": "throwex", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/sql/SQLException"]}, {"nme": "stringToUtf8ByteArray", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[B"}, {"nme": "utf8ByteBufferToString", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/nio/ByteBuffer;)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "register_progress_handler", "acc": 289, "dsc": "(ILorg/sqlite/ProgressHandler;)V", "exs": ["java/sql/SQLException"]}, {"nme": "clear_progress_handler", "acc": 289, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "getBusyHandler", "acc": 0, "dsc": "()J"}, {"nme": "getCommitListener", "acc": 0, "dsc": "()J"}, {"nme": "getUpdateListener", "acc": 0, "dsc": "()J"}, {"nme": "getProgressHandler", "acc": 0, "dsc": "()J"}, {"nme": "serialize", "acc": 289, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[B", "exs": ["java/sql/SQLException"]}, {"nme": "deserialize", "acc": 289, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[B)V", "exs": ["java/sql/SQLException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "logger", "dsc": "Lorg/slf4j/Logger;"}, {"acc": 26, "nme": "DEFAULT_BACKUP_BUSY_SLEEP_TIME_MILLIS", "dsc": "I", "val": 100}, {"acc": 26, "nme": "DEFAULT_BACKUP_NUM_BUSY_BEFORE_FAIL", "dsc": "I", "val": 3}, {"acc": 26, "nme": "DEFAULT_PAGES_PER_BACKUP_STEP", "dsc": "I", "val": 100}, {"acc": 2, "nme": "pointer", "dsc": "J"}, {"acc": 10, "nme": "isLoaded", "dsc": "Z"}, {"acc": 10, "nme": "loadSucceeded", "dsc": "Z"}, {"acc": 2, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "J"}, {"acc": 2, "nme": "commitListener", "dsc": "J"}, {"acc": 2, "nme": "updateListener", "dsc": "J"}, {"acc": 2, "nme": "progressHandler", "dsc": "J"}]}, "META-INF/versions/9/org/sqlite/nativeimage/SqliteJdbcFeature$SqliteJdbcFeatureException.class": {"ver": 53, "acc": 32, "nme": "org/sqlite/nativeimage/SqliteJdbcFeature$SqliteJdbcFeatureException", "super": "java/lang/RuntimeException", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 4096, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/sqlite/nativeimage/SqliteJdbcFeature$1;)V"}, {"nme": "<init>", "acc": 4096, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;Lorg/sqlite/nativeimage/SqliteJdbcFeature$1;)V"}], "flds": []}, "org/sqlite/jdbc4/JDBC4PreparedStatement.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/jdbc4/JDBC4PreparedStatement", "super": "org/sqlite/jdbc3/JDBC3PreparedStatement", "mthds": [{"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/sqlite/SQLiteConnection;Ljava/lang/String;)V", "exs": ["java/sql/SQLException"]}, {"nme": "setRowId", "acc": 1, "dsc": "(ILjava/sql/RowId;)V", "exs": ["java/sql/SQLException"]}, {"nme": "setNString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/sql/SQLException"]}, {"nme": "setNCharacterStream", "acc": 1, "dsc": "(ILjava/io/Reader;J)V", "exs": ["java/sql/SQLException"]}, {"nme": "setNClob", "acc": 1, "dsc": "(ILjava/sql/NClob;)V", "exs": ["java/sql/SQLException"]}, {"nme": "setClob", "acc": 1, "dsc": "(ILjava/io/Reader;J)V", "exs": ["java/sql/SQLException"]}, {"nme": "setBlob", "acc": 1, "dsc": "(ILjava/io/InputStream;J)V", "exs": ["java/sql/SQLException"]}, {"nme": "setNClob", "acc": 1, "dsc": "(ILjava/io/Reader;J)V", "exs": ["java/sql/SQLException"]}, {"nme": "setSQLXML", "acc": 1, "dsc": "(ILjava/sql/SQLXML;)V", "exs": ["java/sql/SQLException"]}, {"nme": "setAsciiStream", "acc": 1, "dsc": "(ILjava/io/InputStream;J)V", "exs": ["java/sql/SQLException"]}, {"nme": "setBinaryStream", "acc": 1, "dsc": "(ILjava/io/InputStream;J)V", "exs": ["java/sql/SQLException"]}, {"nme": "setCharacterStream", "acc": 1, "dsc": "(ILjava/io/Reader;J)V", "exs": ["java/sql/SQLException"]}, {"nme": "setAsciiStream", "acc": 1, "dsc": "(ILjava/io/InputStream;)V", "exs": ["java/sql/SQLException"]}, {"nme": "setBinaryStream", "acc": 1, "dsc": "(ILjava/io/InputStream;)V", "exs": ["java/sql/SQLException"]}, {"nme": "setCharacterStream", "acc": 1, "dsc": "(ILjava/io/Reader;)V", "exs": ["java/sql/SQLException"]}, {"nme": "setNCharacterStream", "acc": 1, "dsc": "(ILjava/io/Reader;)V", "exs": ["java/sql/SQLException"]}, {"nme": "setClob", "acc": 1, "dsc": "(ILjava/io/Reader;)V", "exs": ["java/sql/SQLException"]}, {"nme": "setBlob", "acc": 1, "dsc": "(ILjava/io/InputStream;)V", "exs": ["java/sql/SQLException"]}, {"nme": "setNClob", "acc": 1, "dsc": "(ILjava/io/Reader;)V", "exs": ["java/sql/SQLException"]}], "flds": []}, "org/sqlite/date/FastDatePrinter$TwoDigitNumberField.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/date/FastDatePrinter$TwoDigitNumberField", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(I)V"}, {"nme": "estimateLength", "acc": 1, "dsc": "()I"}, {"nme": "appendTo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;<PERSON><PERSON><PERSON>/util/Calendar;)V"}, {"nme": "appendTo", "acc": 17, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;I)V"}], "flds": [{"acc": 18, "nme": "m<PERSON>ield", "dsc": "I"}]}, "org/sqlite/javax/SQLiteConnectionPoolDataSource.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/javax/SQLiteConnectionPoolDataSource", "super": "org/sqlite/SQLiteDataSource", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/sqlite/SQLiteConfig;)V"}, {"nme": "getPooledConnection", "acc": 1, "dsc": "()Ljavax/sql/PooledConnection;", "exs": ["java/sql/SQLException"]}, {"nme": "getPooledConnection", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;)Ljavax/sql/PooledConnection;", "exs": ["java/sql/SQLException"]}], "flds": []}, "org/sqlite/SQLiteConfig$TempStore.class": {"ver": 52, "acc": 16433, "nme": "org/sqlite/SQLiteConfig$TempStore", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lorg/sqlite/SQLiteConfig$TempStore;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/sqlite/SQLiteConfig$TempStore;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "getValue", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "DEFAULT", "dsc": "Lorg/sqlite/SQLiteConfig$TempStore;"}, {"acc": 16409, "nme": "FILE", "dsc": "Lorg/sqlite/SQLiteConfig$TempStore;"}, {"acc": 16409, "nme": "MEMORY", "dsc": "Lorg/sqlite/SQLiteConfig$TempStore;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lorg/sqlite/SQLiteConfig$TempStore;"}]}, "org/sqlite/SQLiteConfig$JournalMode.class": {"ver": 52, "acc": 16433, "nme": "org/sqlite/SQLiteConfig$JournalMode", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lorg/sqlite/SQLiteConfig$JournalMode;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;)Lorg/sqlite/SQLiteConfig$JournalMode;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "getValue", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "DELETE", "dsc": "Lorg/sqlite/SQLiteConfig$JournalMode;"}, {"acc": 16409, "nme": "TRUNCATE", "dsc": "Lorg/sqlite/SQLiteConfig$JournalMode;"}, {"acc": 16409, "nme": "PERSIST", "dsc": "Lorg/sqlite/SQLiteConfig$JournalMode;"}, {"acc": 16409, "nme": "MEMORY", "dsc": "Lorg/sqlite/SQLiteConfig$JournalMode;"}, {"acc": 16409, "nme": "WAL", "dsc": "Lorg/sqlite/SQLiteConfig$JournalMode;"}, {"acc": 16409, "nme": "OFF", "dsc": "Lorg/sqlite/SQLiteConfig$JournalMode;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lorg/sqlite/SQLiteConfig$JournalMode;"}]}, "org/sqlite/SQLiteConfig.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/SQLiteConfig", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/util/Properties;)V"}, {"nme": "newConnectionConfig", "acc": 1, "dsc": "()Lorg/sqlite/SQLiteConnectionConfig;"}, {"nme": "createConnection", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/sql/Connection;", "exs": ["java/sql/SQLException"]}, {"nme": "apply", "acc": 1, "dsc": "(Ljava/sql/Connection;)V", "exs": ["java/sql/SQLException"]}, {"nme": "set", "acc": 2, "dsc": "(Lorg/sqlite/SQLiteConfig$Pragma;Z)V"}, {"nme": "set", "acc": 2, "dsc": "(Lorg/sqlite/SQLiteConfig$Pragma;I)V"}, {"nme": "getBoolean", "acc": 2, "dsc": "(Lorg/sqlite/SQLiteConfig$Pragma;Ljava/lang/String;)Z"}, {"nme": "parseLimitPragma", "acc": 2, "dsc": "(Lorg/sqlite/SQLiteConfig$Pragma;I)I"}, {"nme": "isEnabledSharedCache", "acc": 1, "dsc": "()Z"}, {"nme": "isEnabledLoadExtension", "acc": 1, "dsc": "()Z"}, {"nme": "getOpenModeFlags", "acc": 1, "dsc": "()I"}, {"nme": "setPragma", "acc": 1, "dsc": "(Lorg/sqlite/SQLiteConfig$Pragma;Ljava/lang/String;)V"}, {"nme": "toProperties", "acc": 1, "dsc": "()Ljava/util/Properties;"}, {"nme": "getDriverPropertyInfo", "acc": 8, "dsc": "()[Ljava/sql/DriverPropertyInfo;"}, {"nme": "isExplicitReadOnly", "acc": 1, "dsc": "()Z"}, {"nme": "setExplicitReadOnly", "acc": 1, "dsc": "(Z)V"}, {"nme": "setOpenMode", "acc": 1, "dsc": "(Lorg/sqlite/SQLiteOpenMode;)V"}, {"nme": "resetOpenMode", "acc": 1, "dsc": "(Lorg/sqlite/SQLiteOpenMode;)V"}, {"nme": "setSharedCache", "acc": 1, "dsc": "(Z)V"}, {"nme": "enableLoadExtension", "acc": 1, "dsc": "(Z)V"}, {"nme": "setReadOnly", "acc": 1, "dsc": "(Z)V"}, {"nme": "setCacheSize", "acc": 1, "dsc": "(I)V"}, {"nme": "enableCaseSensitiveLike", "acc": 1, "dsc": "(Z)V"}, {"nme": "enableCountChanges", "acc": 131073, "dsc": "(Z)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "setDefaultCacheSize", "acc": 1, "dsc": "(I)V"}, {"nme": "defer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Z)V"}, {"nme": "enableEmptyResultCallBacks", "acc": 131073, "dsc": "(Z)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "toStringArray", "acc": 10, "dsc": "([Lorg/sqlite/SQLiteConfig$PragmaValue;)[Ljava/lang/String;"}, {"nme": "setEncoding", "acc": 1, "dsc": "(Lorg/sqlite/SQLiteConfig$Encoding;)V"}, {"nme": "enforceForeignKeys", "acc": 1, "dsc": "(Z)V"}, {"nme": "enableFullColumnNames", "acc": 131073, "dsc": "(Z)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "enableFullSync", "acc": 1, "dsc": "(Z)V"}, {"nme": "incrementalVacuum", "acc": 1, "dsc": "(I)V"}, {"nme": "setJournalMode", "acc": 1, "dsc": "(Lorg/sqlite/SQLiteConfig$JournalMode;)V"}, {"nme": "setJournalSizeLimit", "acc": 1, "dsc": "(I)V"}, {"nme": "useLegacyFileFormat", "acc": 1, "dsc": "(Z)V"}, {"nme": "setLegacyAlterTable", "acc": 1, "dsc": "(Z)V"}, {"nme": "setLockingMode", "acc": 1, "dsc": "(Lorg/sqlite/SQLiteConfig$LockingMode;)V"}, {"nme": "setPageSize", "acc": 1, "dsc": "(I)V"}, {"nme": "setMaxPageCount", "acc": 1, "dsc": "(I)V"}, {"nme": "setReadUncommitted", "acc": 1, "dsc": "(Z)V"}, {"nme": "enableRecursiveTriggers", "acc": 1, "dsc": "(Z)V"}, {"nme": "enableReverseUnorderedSelects", "acc": 1, "dsc": "(Z)V"}, {"nme": "enableShortColumnNames", "acc": 1, "dsc": "(Z)V"}, {"nme": "setSynchronous", "acc": 1, "dsc": "(Lorg/sqlite/SQLiteConfig$SynchronousMode;)V"}, {"nme": "setHexKeyMode", "acc": 1, "dsc": "(Lorg/sqlite/SQLiteConfig$HexKeyMode;)V"}, {"nme": "setTempStore", "acc": 1, "dsc": "(Lorg/sqlite/SQLiteConfig$TempStore;)V"}, {"nme": "setTempStoreDirectory", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setUserVersion", "acc": 1, "dsc": "(I)V"}, {"nme": "setApplicationId", "acc": 1, "dsc": "(I)V"}, {"nme": "setTransactionMode", "acc": 1, "dsc": "(Lorg/sqlite/SQLiteConfig$TransactionMode;)V"}, {"nme": "setTransactionMode", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getTransactionMode", "acc": 1, "dsc": "()Lorg/sqlite/SQLiteConfig$TransactionMode;"}, {"nme": "setDatePrecision", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setDateClass", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setDateStringFormat", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setBusyTimeout", "acc": 1, "dsc": "(I)V"}, {"nme": "getBusyTimeout", "acc": 1, "dsc": "()I"}, {"nme": "access$000", "acc": 4104, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "access$100", "acc": 4104, "dsc": "([Lorg/sqlite/SQLiteConfig$PragmaValue;)[Ljava/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "DEFAULT_DATE_STRING_FORMAT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "yyyy-MM-dd HH:mm:ss.SSS"}, {"acc": 26, "nme": "DEFAULT_MAX_LENGTH", "dsc": "I", "val": 1000000000}, {"acc": 26, "nme": "DEFAULT_MAX_COLUMN", "dsc": "I", "val": 2000}, {"acc": 26, "nme": "DEFAULT_MAX_SQL_LENGTH", "dsc": "I", "val": 1000000}, {"acc": 26, "nme": "DEFAULT_MAX_FUNCTION_ARG", "dsc": "I", "val": 100}, {"acc": 26, "nme": "DEFAULT_MAX_ATTACHED", "dsc": "I", "val": 10}, {"acc": 26, "nme": "DEFAULT_MAX_PAGE_COUNT", "dsc": "I", "val": 1073741823}, {"acc": 18, "nme": "pragmaTable", "dsc": "Ljava/util/Properties;"}, {"acc": 2, "nme": "openModeFlag", "dsc": "I"}, {"acc": 2, "nme": "busyTimeout", "dsc": "I"}, {"acc": 2, "nme": "explicitReadOnly", "dsc": "Z"}, {"acc": 18, "nme": "defaultConnectionConfig", "dsc": "Lorg/sqlite/SQLiteConnectionConfig;"}, {"acc": 26, "nme": "<PERSON><PERSON><PERSON>", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 24, "nme": "pragmaSet", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}]}, "org/sqlite/SQLiteErrorCode.class": {"ver": 52, "acc": 16433, "nme": "org/sqlite/SQLiteErrorCode", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lorg/sqlite/SQLiteErrorCode;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/sqlite/SQLiteErrorCode;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;II<PERSON><PERSON><PERSON>/lang/String;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getErrorCode", "acc": 9, "dsc": "(I)Lorg/sqlite/SQLiteErrorCode;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "UNKNOWN_ERROR", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_OK", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_ERROR", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_INTERNAL", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_PERM", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_ABORT", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_BUSY", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_LOCKED", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_NOMEM", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_READONLY", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_INTERRUPT", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_CORRUPT", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_NOTFOUND", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_FULL", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_CANTOPEN", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_PROTOCOL", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_EMPTY", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_SCHEMA", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_TOOBIG", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_CONSTRAINT", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_MISMATCH", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_MISUSE", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_NOLFS", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_AUTH", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_FORMAT", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_RANGE", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_NOTADB", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_NOTICE", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_WARNING", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_ROW", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_DONE", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_ABORT_ROLLBACK", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_AUTH_USER", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_BUSY_RECOVERY", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_BUSY_SNAPSHOT", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_BUSY_TIMEOUT", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_CANTOPEN_CONVPATH", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_CANTOPEN_DIRTYWAL", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_CANTOPEN_FULLPATH", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_CANTOPEN_ISDIR", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_CANTOPEN_NOTEMPDIR", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_CANTOPEN_SYMLINK", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_CONSTRAINT_CHECK", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_CONSTRAINT_COMMITHOOK", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_CONSTRAINT_DATATYPE", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_CONSTRAINT_FOREIGNKEY", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_CONSTRAINT_FUNCTION", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_CONSTRAINT_NOTNULL", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_CONSTRAINT_PINNED", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_CONSTRAINT_PRIMARYKEY", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_CONSTRAINT_ROWID", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_CONSTRAINT_TRIGGER", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_CONSTRAINT_UNIQUE", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_CONSTRAINT_VTAB", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_CORRUPT_INDEX", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_CORRUPT_SEQUENCE", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_CORRUPT_VTAB", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_ERROR_MISSING_COLLSEQ", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_ERROR_RETRY", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_ERROR_SNAPSHOT", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_ACCESS", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_AUTH", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_BEGIN_ATOMIC", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_BLOCKED", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_CHECKRESERVEDLOCK", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_CLOSE", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_COMMIT_ATOMIC", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_CONVPATH", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_CORRUPTFS", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_DATA", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_DELETE", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_DELETE_NOENT", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_DIR_CLOSE", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_DIR_FSYNC", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_FSTAT", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_FSYNC", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_GETTEMPPATH", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_LOCK", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_MMAP", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_NOMEM", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_RDLOCK", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_READ", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_ROLLBACK_ATOMIC", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_SEEK", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_SHMLOCK", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_SHMMAP", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_SHMOPEN", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_SHMSIZE", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_SHORT_READ", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_TRUNCATE", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_UNLOCK", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_VNODE", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_IOERR_WRITE", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_LOCKED_SHAREDCACHE", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_LOCKED_VTAB", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_NOTICE_RECOVER_ROLLBACK", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_NOTICE_RECOVER_WAL", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_OK_LOAD_PERMANENTLY", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_READONLY_CANTINIT", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_READONLY_CANTLOCK", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_READONLY_DBMOVED", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_READONLY_DIRECTORY", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_READONLY_RECOVERY", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_READONLY_ROLLBACK", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 16409, "nme": "SQLITE_WARNING_AUTOINDEX", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}, {"acc": 17, "nme": "code", "dsc": "I"}, {"acc": 17, "nme": "message", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lorg/sqlite/SQLiteErrorCode;"}]}, "org/sqlite/date/FastDatePrinter$TwoDigitYearField.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/date/FastDatePrinter$TwoDigitYearField", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "estimateLength", "acc": 1, "dsc": "()I"}, {"nme": "appendTo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;<PERSON><PERSON><PERSON>/util/Calendar;)V"}, {"nme": "appendTo", "acc": 17, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;I)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "INSTANCE", "dsc": "Lorg/sqlite/date/FastDatePrinter$TwoDigitYearField;"}]}, "org/sqlite/date/FastDatePrinter$TextField.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/date/FastDatePrinter$TextField", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(I[<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "estimateLength", "acc": 1, "dsc": "()I"}, {"nme": "appendTo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;<PERSON><PERSON><PERSON>/util/Calendar;)V"}], "flds": [{"acc": 18, "nme": "m<PERSON>ield", "dsc": "I"}, {"acc": 18, "nme": "m<PERSON><PERSON><PERSON>", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/sqlite/date/FastDatePrinter$TwelveHourField.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/date/FastDatePrinter$TwelveHourField", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/sqlite/date/FastDatePrinter$NumberRule;)V"}, {"nme": "estimateLength", "acc": 1, "dsc": "()I"}, {"nme": "appendTo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;<PERSON><PERSON><PERSON>/util/Calendar;)V"}, {"nme": "appendTo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;I)V"}], "flds": [{"acc": 18, "nme": "mRule", "dsc": "Lorg/sqlite/date/FastDatePrinter$NumberRule;"}]}, "org/sqlite/date/FastDateFormat$1.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/date/FastDateFormat$1", "super": "org/sqlite/date/FormatCache", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "createInstance", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/TimeZone;<PERSON>ja<PERSON>/util/Locale;)Lorg/sqlite/date/FastDateFormat;"}, {"nme": "createInstance", "acc": 4164, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/TimeZone;<PERSON>ja<PERSON>/util/Locale;)Ljava/text/Format;"}], "flds": []}, "org/sqlite/JDBC.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/JDBC", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getMajorVersion", "acc": 1, "dsc": "()I"}, {"nme": "getMinorVersion", "acc": 1, "dsc": "()I"}, {"nme": "jdbcCompliant", "acc": 1, "dsc": "()Z"}, {"nme": "get<PERSON><PERSON>nt<PERSON>og<PERSON>", "acc": 1, "dsc": "()Ljava/util/logging/Logger;", "exs": ["java/sql/SQLFeatureNotSupportedException"]}, {"nme": "acceptsURL", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isValidURL", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "getPropertyInfo", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/Properties;)[Ljava/sql/DriverPropertyInfo;", "exs": ["java/sql/SQLException"]}, {"nme": "connect", "acc": 1, "dsc": "(L<PERSON><PERSON>/lang/String;Ljava/util/Properties;)Ljava/sql/Connection;", "exs": ["java/sql/SQLException"]}, {"nme": "extractAddress", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "createConnection", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/Properties;)Lorg/sqlite/SQLiteConnection;", "exs": ["java/sql/SQLException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "logger", "dsc": "Lorg/slf4j/Logger;"}, {"acc": 25, "nme": "PREFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jdbc:sqlite:"}]}, "org/sqlite/date/FastDateParser$NumberStrategy.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/date/FastDateParser$NumberStrategy", "super": "org/sqlite/date/FastDateParser$Strategy", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(I)V"}, {"nme": "isNumber", "acc": 0, "dsc": "()Z"}, {"nme": "addRegex", "acc": 0, "dsc": "(Lorg/sqlite/date/FastDateParser;Lja<PERSON>/lang/StringBuilder;)Z"}, {"nme": "setCalendar", "acc": 0, "dsc": "(Lorg/sqlite/date/FastDateParser;Ljava/util/Calendar;Ljava/lang/String;)V"}, {"nme": "modify", "acc": 0, "dsc": "(I)I"}], "flds": [{"acc": 18, "nme": "field", "dsc": "I"}]}, "org/sqlite/jdbc3/JDBC3ResultSet.class": {"ver": 52, "acc": 1057, "nme": "org/sqlite/jdbc3/JDBC3ResultSet", "super": "org/sqlite/core/CoreResultSet", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Lorg/sqlite/core/CoreStatement;)V"}, {"nme": "findColumn", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I", "exs": ["java/sql/SQLException"]}, {"nme": "next", "acc": 1, "dsc": "()Z", "exs": ["java/sql/SQLException"]}, {"nme": "getType", "acc": 1, "dsc": "()I"}, {"nme": "getFetchSize", "acc": 1, "dsc": "()I"}, {"nme": "setFetchSize", "acc": 1, "dsc": "(I)V", "exs": ["java/sql/SQLException"]}, {"nme": "getFetchDirection", "acc": 1, "dsc": "()I", "exs": ["java/sql/SQLException"]}, {"nme": "setFetchDirection", "acc": 1, "dsc": "(I)V", "exs": ["java/sql/SQLException"]}, {"nme": "isAfterLast", "acc": 1, "dsc": "()Z"}, {"nme": "isBeforeFirst", "acc": 1, "dsc": "()Z"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Z"}, {"nme": "isLast", "acc": 1, "dsc": "()Z", "exs": ["java/sql/SQLException"]}, {"nme": "getRow", "acc": 1, "dsc": "()I"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Z", "exs": ["java/sql/SQLException"]}, {"nme": "getBigDecimal", "acc": 1, "dsc": "(I)Ljava/math/BigDecimal;", "exs": ["java/sql/SQLException"]}, {"nme": "getBigDecimal", "acc": 1, "dsc": "(<PERSON>ja<PERSON>/lang/String;)Ljava/math/BigDecimal;", "exs": ["java/sql/SQLException"]}, {"nme": "getBoolean", "acc": 1, "dsc": "(I)Z", "exs": ["java/sql/SQLException"]}, {"nme": "getBoolean", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z", "exs": ["java/sql/SQLException"]}, {"nme": "getBinaryStream", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/io/InputStream;", "exs": ["java/sql/SQLException"]}, {"nme": "getBinaryStream", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/io/InputStream;", "exs": ["java/sql/SQLException"]}, {"nme": "getByte", "acc": 1, "dsc": "(I)B", "exs": ["java/sql/SQLException"]}, {"nme": "getByte", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)B", "exs": ["java/sql/SQLException"]}, {"nme": "getBytes", "acc": 1, "dsc": "(I)[B", "exs": ["java/sql/SQLException"]}, {"nme": "getBytes", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[B", "exs": ["java/sql/SQLException"]}, {"nme": "getCharacterStream", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/io/Reader;", "exs": ["java/sql/SQLException"]}, {"nme": "getCharacterStream", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/io/Reader;", "exs": ["java/sql/SQLException"]}, {"nme": "getDate", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/sql/Date;", "exs": ["java/sql/SQLException"]}, {"nme": "getDate", "acc": 1, "dsc": "(ILjava/util/Calendar;)Ljava/sql/Date;", "exs": ["java/sql/SQLException"]}, {"nme": "getDate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/sql/Date;", "exs": ["java/sql/SQLException"]}, {"nme": "getDate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Calendar;)Ljava/sql/Date;", "exs": ["java/sql/SQLException"]}, {"nme": "getDouble", "acc": 1, "dsc": "(I)D", "exs": ["java/sql/SQLException"]}, {"nme": "getDouble", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)D", "exs": ["java/sql/SQLException"]}, {"nme": "getFloat", "acc": 1, "dsc": "(I)F", "exs": ["java/sql/SQLException"]}, {"nme": "getFloat", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)F", "exs": ["java/sql/SQLException"]}, {"nme": "getInt", "acc": 1, "dsc": "(I)I", "exs": ["java/sql/SQLException"]}, {"nme": "getInt", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I", "exs": ["java/sql/SQLException"]}, {"nme": "getLong", "acc": 1, "dsc": "(I)J", "exs": ["java/sql/SQLException"]}, {"nme": "getLong", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)J", "exs": ["java/sql/SQLException"]}, {"nme": "getShort", "acc": 1, "dsc": "(I)S", "exs": ["java/sql/SQLException"]}, {"nme": "getShort", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)S", "exs": ["java/sql/SQLException"]}, {"nme": "getString", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "getString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "getTime", "acc": 1, "dsc": "(I)Ljava/sql/Time;", "exs": ["java/sql/SQLException"]}, {"nme": "getTime", "acc": 1, "dsc": "(ILjava/util/Calendar;)Ljava/sql/Time;", "exs": ["java/sql/SQLException"]}, {"nme": "getTime", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/sql/Time;", "exs": ["java/sql/SQLException"]}, {"nme": "getTime", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Calendar;)Ljava/sql/Time;", "exs": ["java/sql/SQLException"]}, {"nme": "getTimestamp", "acc": 1, "dsc": "(I)Ljava/sql/Timestamp;", "exs": ["java/sql/SQLException"]}, {"nme": "getTimestamp", "acc": 1, "dsc": "(ILjava/util/Calendar;)Ljava/sql/Timestamp;", "exs": ["java/sql/SQLException"]}, {"nme": "getTimestamp", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/sql/Timestamp;", "exs": ["java/sql/SQLException"]}, {"nme": "getTimestamp", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Calendar;)Ljava/sql/Timestamp;", "exs": ["java/sql/SQLException"]}, {"nme": "getObject", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/sql/SQLException"]}, {"nme": "getObject", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/sql/SQLException"]}, {"nme": "getStatement", "acc": 1, "dsc": "()Ljava/sql/Statement;"}, {"nme": "getCursorName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getWarnings", "acc": 1, "dsc": "()Ljava/sql/SQLWarning;"}, {"nme": "clearWarnings", "acc": 1, "dsc": "()V"}, {"nme": "getMetaData", "acc": 1, "dsc": "()Ljava/sql/ResultSetMetaData;"}, {"nme": "getCatalogName", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "getColumnClassName", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "getColumnCount", "acc": 1, "dsc": "()I", "exs": ["java/sql/SQLException"]}, {"nme": "getColumnDisplaySize", "acc": 1, "dsc": "(I)I"}, {"nme": "getColumnLabel", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "getColumnName", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "getColumnType", "acc": 1, "dsc": "(I)I", "exs": ["java/sql/SQLException"]}, {"nme": "getColumnTypeName", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "getPrecision", "acc": 1, "dsc": "(I)I", "exs": ["java/sql/SQLException"]}, {"nme": "getColumnDeclType", "acc": 2, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "getScale", "acc": 1, "dsc": "(I)I", "exs": ["java/sql/SQLException"]}, {"nme": "getSchemaName", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getTableName", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "isNullable", "acc": 1, "dsc": "(I)I", "exs": ["java/sql/SQLException"]}, {"nme": "isAutoIncrement", "acc": 1, "dsc": "(I)Z", "exs": ["java/sql/SQLException"]}, {"nme": "isCaseSensitive", "acc": 1, "dsc": "(I)Z"}, {"nme": "isCurrency", "acc": 1, "dsc": "(I)Z"}, {"nme": "isDefinitelyWritable", "acc": 1, "dsc": "(I)Z"}, {"nme": "isReadOnly", "acc": 1, "dsc": "(I)Z"}, {"nme": "isSearchable", "acc": 1, "dsc": "(I)Z"}, {"nme": "isSigned", "acc": 1, "dsc": "(I)Z", "exs": ["java/sql/SQLException"]}, {"nme": "isWritable", "acc": 1, "dsc": "(I)Z"}, {"nme": "getConcurrency", "acc": 1, "dsc": "()I"}, {"nme": "rowDeleted", "acc": 1, "dsc": "()Z"}, {"nme": "rowInserted", "acc": 1, "dsc": "()Z"}, {"nme": "rowUpdated", "acc": 1, "dsc": "()Z"}, {"nme": "julianDateToCalendar", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Double;)Ljava/util/Calendar;"}, {"nme": "julianDateToCalendar", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Double;<PERSON><PERSON><PERSON>/util/Calendar;)Ljava/util/Calendar;"}, {"nme": "requireCalendarNotNull", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/Calendar;)V", "exs": ["java/sql/SQLException"]}, {"nme": "safeGetColumnType", "acc": 4, "dsc": "(I)I", "exs": ["java/sql/SQLException"]}, {"nme": "safeGetLongCol", "acc": 2, "dsc": "(I)J", "exs": ["java/sql/SQLException"]}, {"nme": "safeGetDoubleCol", "acc": 2, "dsc": "(I)D", "exs": ["java/sql/SQLException"]}, {"nme": "safeGetColumnText", "acc": 2, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "safeGetColumnTableName", "acc": 2, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "safeGetColumnName", "acc": 2, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "lambda$safeGetColumnName$8", "acc": 4098, "dsc": "(ILorg/sqlite/core/DB;J)Ljava/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "lambda$safeGetColumnTableName$7", "acc": 4098, "dsc": "(ILorg/sqlite/core/DB;J)Ljava/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "lambda$safeGetColumnText$6", "acc": 4098, "dsc": "(ILorg/sqlite/core/DB;J)Ljava/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "lambda$safeGetDoubleCol$5", "acc": 4098, "dsc": "(ILorg/sqlite/core/DB;J)D", "exs": ["java/sql/SQLException"]}, {"nme": "lambda$safeGetLongCol$4", "acc": 4098, "dsc": "(ILorg/sqlite/core/DB;J)J", "exs": ["java/sql/SQLException"]}, {"nme": "lambda$safeGetColumnType$3", "acc": 4106, "dsc": "(ILorg/sqlite/core/DB;J)I", "exs": ["java/sql/SQLException"]}, {"nme": "lambda$getColumnDeclType$2", "acc": 4098, "dsc": "(ILorg/sqlite/core/DB;J)Ljava/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "lambda$getInt$1", "acc": 4098, "dsc": "(ILorg/sqlite/core/DB;J)I", "exs": ["java/sql/SQLException"]}, {"nme": "lambda$getBytes$0", "acc": 4098, "dsc": "(ILorg/sqlite/core/DB;J)[B", "exs": ["java/sql/SQLException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 28, "nme": "COLUMN_TYPENAME", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 28, "nme": "COLUMN_TYPECAST", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 28, "nme": "COLUMN_PRECISION", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}]}, "org/sqlite/core/SafeStmtPtr.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/core/SafeStmtPtr", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/sqlite/core/DB;J)V"}, {"nme": "isClosed", "acc": 1, "dsc": "()Z"}, {"nme": "close", "acc": 1, "dsc": "()I", "exs": ["java/sql/SQLException"]}, {"nme": "internalClose", "acc": 2, "dsc": "()I", "exs": ["java/sql/SQLException"]}, {"nme": "safeRunInt", "acc": 1, "dsc": "(Lorg/sqlite/core/SafeStmtPtr$SafePtrIntFunction;)I", "sig": "<E:Ljava/lang/Throwable;>(Lorg/sqlite/core/SafeStmtPtr$SafePtrIntFunction<TE;>;)I^Ljava/sql/SQLException;^TE;", "exs": ["java/sql/SQLException", "java/lang/Throwable"]}, {"nme": "safeRunLong", "acc": 1, "dsc": "(Lorg/sqlite/core/SafeStmtPtr$SafePtrLongFunction;)J", "sig": "<E:Ljava/lang/Throwable;>(Lorg/sqlite/core/SafeStmtPtr$SafePtrLongFunction<TE;>;)J^Ljava/sql/SQLException;^TE;", "exs": ["java/sql/SQLException", "java/lang/Throwable"]}, {"nme": "safeRunDouble", "acc": 1, "dsc": "(Lorg/sqlite/core/SafeStmtPtr$SafePtrDoubleFunction;)D", "sig": "<E:Ljava/lang/Throwable;>(Lorg/sqlite/core/SafeStmtPtr$SafePtrDoubleFunction<TE;>;)D^Ljava/sql/SQLException;^TE;", "exs": ["java/sql/SQLException", "java/lang/Throwable"]}, {"nme": "safeRun", "acc": 1, "dsc": "(Lorg/sqlite/core/SafeStmtPtr$SafePtrFunction;)Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;E:Ljava/lang/Throwable;>(Lorg/sqlite/core/SafeStmtPtr$SafePtrFunction<TT;TE;>;)TT;^Ljava/sql/SQLException;^TE;", "exs": ["java/sql/SQLException", "java/lang/Throwable"]}, {"nme": "safeRunConsume", "acc": 1, "dsc": "(Lorg/sqlite/core/SafeStmtPtr$SafePtrConsumer;)V", "sig": "<E:Ljava/lang/Throwable;>(Lorg/sqlite/core/SafeStmtPtr$SafePtrConsumer<TE;>;)V^Ljava/sql/SQLException;^TE;", "exs": ["java/sql/SQLException", "java/lang/Throwable"]}, {"nme": "ensureOpen", "acc": 2, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "db", "dsc": "Lorg/sqlite/core/DB;"}, {"acc": 18, "nme": "ptr", "dsc": "J"}, {"acc": 66, "nme": "closed", "dsc": "Z"}, {"acc": 2, "nme": "closedRC", "dsc": "I"}, {"acc": 2, "nme": "closeException", "dsc": "Ljava/sql/SQLException;"}]}, "org/sqlite/core/CoreDatabaseMetaData.class": {"ver": 52, "acc": 1057, "nme": "org/sqlite/core/CoreDatabaseMetaData", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Lorg/sqlite/SQLiteConnection;)V"}, {"nme": "getGeneratedKeys", "acc": 132097, "dsc": "()Ljava/sql/ResultSet;", "exs": ["java/sql/SQLException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "checkOpen", "acc": 4, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "close", "acc": 33, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "quote", "acc": 12, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "escape", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "finalize", "acc": 4, "dsc": "()V", "exs": ["java/lang/Throwable"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4, "nme": "conn", "dsc": "Lorg/sqlite/SQLiteConnection;"}, {"acc": 4, "nme": "getTables", "dsc": "<PERSON><PERSON><PERSON>/sql/PreparedStatement;"}, {"acc": 4, "nme": "getTableTypes", "dsc": "<PERSON><PERSON><PERSON>/sql/PreparedStatement;"}, {"acc": 4, "nme": "getTypeInfo", "dsc": "<PERSON><PERSON><PERSON>/sql/PreparedStatement;"}, {"acc": 4, "nme": "getCatalogs", "dsc": "<PERSON><PERSON><PERSON>/sql/PreparedStatement;"}, {"acc": 4, "nme": "getSchemas", "dsc": "<PERSON><PERSON><PERSON>/sql/PreparedStatement;"}, {"acc": 4, "nme": "getUDTs", "dsc": "<PERSON><PERSON><PERSON>/sql/PreparedStatement;"}, {"acc": 4, "nme": "getColumnsTblName", "dsc": "<PERSON><PERSON><PERSON>/sql/PreparedStatement;"}, {"acc": 4, "nme": "getSuperTypes", "dsc": "<PERSON><PERSON><PERSON>/sql/PreparedStatement;"}, {"acc": 4, "nme": "getSuperTables", "dsc": "<PERSON><PERSON><PERSON>/sql/PreparedStatement;"}, {"acc": 4, "nme": "getTablePrivileges", "dsc": "<PERSON><PERSON><PERSON>/sql/PreparedStatement;"}, {"acc": 4, "nme": "getIndexInfo", "dsc": "<PERSON><PERSON><PERSON>/sql/PreparedStatement;"}, {"acc": 4, "nme": "getProcedures", "dsc": "<PERSON><PERSON><PERSON>/sql/PreparedStatement;"}, {"acc": 4, "nme": "getProcedureColumns", "dsc": "<PERSON><PERSON><PERSON>/sql/PreparedStatement;"}, {"acc": 4, "nme": "getAttributes", "dsc": "<PERSON><PERSON><PERSON>/sql/PreparedStatement;"}, {"acc": 4, "nme": "getBestRowIdentifier", "dsc": "<PERSON><PERSON><PERSON>/sql/PreparedStatement;"}, {"acc": 4, "nme": "getVersionColumns", "dsc": "<PERSON><PERSON><PERSON>/sql/PreparedStatement;"}, {"acc": 4, "nme": "getColumnPrivileges", "dsc": "<PERSON><PERSON><PERSON>/sql/PreparedStatement;"}, {"acc": 28, "nme": "PK_UNNAMED_PATTERN", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 28, "nme": "PK_NAMED_PATTERN", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}]}, "org/sqlite/date/FastDateParser$CopyQuotedStrategy.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/date/FastDateParser$CopyQuotedStrategy", "super": "org/sqlite/date/FastDateParser$Strategy", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "isNumber", "acc": 0, "dsc": "()Z"}, {"nme": "addRegex", "acc": 0, "dsc": "(Lorg/sqlite/date/FastDateParser;Lja<PERSON>/lang/StringBuilder;)Z"}], "flds": [{"acc": 18, "nme": "formatField", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/sqlite/util/QueryUtils.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/util/QueryUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "valuesQuery", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/util/List;)Lja<PERSON>/lang/String;", "sig": "(Ljava/util/List<Ljava/lang/String;>;Ljava/util/List<Ljava/util/List<Ljava/lang/Object;>;>;)Ljava/lang/String;"}, {"nme": "lambda$valuesQuery$2", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Ljava/lang/String;"}, {"nme": "lambda$valuesQuery$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON>ja<PERSON>/lang/String;"}, {"nme": "lambda$valuesQuery$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/util/List;)V"}], "flds": []}, "org/sqlite/SQLiteCommitListener.class": {"ver": 52, "acc": 1537, "nme": "org/sqlite/SQLiteCommitListener", "super": "java/lang/Object", "mthds": [{"nme": "onCommit", "acc": 1025, "dsc": "()V"}, {"nme": "onRollback", "acc": 1025, "dsc": "()V"}], "flds": []}, "org/sqlite/date/FastDatePrinter$StringLiteral.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/date/FastDatePrinter$StringLiteral", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "estimateLength", "acc": 1, "dsc": "()I"}, {"nme": "appendTo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;<PERSON><PERSON><PERSON>/util/Calendar;)V"}], "flds": [{"acc": 18, "nme": "mValue", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/sqlite/core/DB.class": {"ver": 52, "acc": 1057, "nme": "org/sqlite/core/DB", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>java/lang/String;Lorg/sqlite/SQLiteConfig;)V", "exs": ["java/sql/SQLException"]}, {"nme": "getUrl", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isClosed", "acc": 1, "dsc": "()Z"}, {"nme": "getConfig", "acc": 1, "dsc": "()Lorg/sqlite/SQLiteConfig;"}, {"nme": "interrupt", "acc": 1025, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "busy_timeout", "acc": 1025, "dsc": "(I)V", "exs": ["java/sql/SQLException"]}, {"nme": "busy_handler", "acc": 1025, "dsc": "(Lorg/sqlite/Bus<PERSON>;)V", "exs": ["java/sql/SQLException"]}, {"nme": "errmsg", "acc": 1024, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "libversion", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "changes", "acc": 1025, "dsc": "()J", "exs": ["java/sql/SQLException"]}, {"nme": "total_changes", "acc": 1025, "dsc": "()J", "exs": ["java/sql/SQLException"]}, {"nme": "shared_cache", "acc": 1025, "dsc": "(Z)I", "exs": ["java/sql/SQLException"]}, {"nme": "enable_load_extension", "acc": 1025, "dsc": "(Z)I", "exs": ["java/sql/SQLException"]}, {"nme": "exec", "acc": 49, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)V", "exs": ["java/sql/SQLException"]}, {"nme": "open", "acc": 49, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "exs": ["java/sql/SQLException"]}, {"nme": "close", "acc": 49, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "prepare", "acc": 49, "dsc": "(Lorg/sqlite/core/CoreStatement;)V", "exs": ["java/sql/SQLException"]}, {"nme": "finalize", "acc": 33, "dsc": "(Lorg/sqlite/core/SafeStmtPtr;J)I", "exs": ["java/sql/SQLException"]}, {"nme": "_open", "acc": 1028, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "exs": ["java/sql/SQLException"]}, {"nme": "_close", "acc": 1028, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "_exec", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I", "exs": ["java/sql/SQLException"]}, {"nme": "prepare", "acc": 1028, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/sqlite/core/SafeStmtPtr;", "exs": ["java/sql/SQLException"]}, {"nme": "finalize", "acc": 1028, "dsc": "(J)I", "exs": ["java/sql/SQLException"]}, {"nme": "step", "acc": 1025, "dsc": "(J)I", "exs": ["java/sql/SQLException"]}, {"nme": "reset", "acc": 1025, "dsc": "(J)I", "exs": ["java/sql/SQLException"]}, {"nme": "clear_bindings", "acc": 1025, "dsc": "(J)I", "exs": ["java/sql/SQLException"]}, {"nme": "bind_parameter_count", "acc": 1024, "dsc": "(J)I", "exs": ["java/sql/SQLException"]}, {"nme": "column_count", "acc": 1025, "dsc": "(J)I", "exs": ["java/sql/SQLException"]}, {"nme": "column_type", "acc": 1025, "dsc": "(JI)I", "exs": ["java/sql/SQLException"]}, {"nme": "column_decltype", "acc": 1025, "dsc": "(JI)Ljava/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "column_table_name", "acc": 1025, "dsc": "(JI)Ljava/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "column_name", "acc": 1025, "dsc": "(JI)Ljava/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "column_text", "acc": 1025, "dsc": "(JI)Ljava/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "column_blob", "acc": 1025, "dsc": "(JI)[B", "exs": ["java/sql/SQLException"]}, {"nme": "column_double", "acc": 1025, "dsc": "(JI)D", "exs": ["java/sql/SQLException"]}, {"nme": "column_long", "acc": 1025, "dsc": "(JI)J", "exs": ["java/sql/SQLException"]}, {"nme": "column_int", "acc": 1025, "dsc": "(JI)I", "exs": ["java/sql/SQLException"]}, {"nme": "bind_null", "acc": 1024, "dsc": "(JI)I", "exs": ["java/sql/SQLException"]}, {"nme": "bind_int", "acc": 1024, "dsc": "(JII)I", "exs": ["java/sql/SQLException"]}, {"nme": "bind_long", "acc": 1024, "dsc": "(JIJ)I", "exs": ["java/sql/SQLException"]}, {"nme": "bind_double", "acc": 1024, "dsc": "(JID)I", "exs": ["java/sql/SQLException"]}, {"nme": "bind_text", "acc": 1024, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)I", "exs": ["java/sql/SQLException"]}, {"nme": "bind_blob", "acc": 1024, "dsc": "(JI[B)I", "exs": ["java/sql/SQLException"]}, {"nme": "result_null", "acc": 1025, "dsc": "(J)V", "exs": ["java/sql/SQLException"]}, {"nme": "result_text", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/sql/SQLException"]}, {"nme": "result_blob", "acc": 1025, "dsc": "(J[B)V", "exs": ["java/sql/SQLException"]}, {"nme": "result_double", "acc": 1025, "dsc": "(JD)V", "exs": ["java/sql/SQLException"]}, {"nme": "result_long", "acc": 1025, "dsc": "(JJ)V", "exs": ["java/sql/SQLException"]}, {"nme": "result_int", "acc": 1025, "dsc": "(JI)V", "exs": ["java/sql/SQLException"]}, {"nme": "result_error", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/sql/SQLException"]}, {"nme": "value_text", "acc": 1025, "dsc": "(Lorg/sqlite/Function;I)Ljava/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "value_blob", "acc": 1025, "dsc": "(Lorg/sqlite/Function;I)[B", "exs": ["java/sql/SQLException"]}, {"nme": "value_double", "acc": 1025, "dsc": "(Lorg/sqlite/Function;I)D", "exs": ["java/sql/SQLException"]}, {"nme": "value_long", "acc": 1025, "dsc": "(Lorg/sqlite/Function;I)J", "exs": ["java/sql/SQLException"]}, {"nme": "value_int", "acc": 1025, "dsc": "(Lorg/sqlite/Function;I)I", "exs": ["java/sql/SQLException"]}, {"nme": "value_type", "acc": 1025, "dsc": "(Lorg/sqlite/Function;I)I", "exs": ["java/sql/SQLException"]}, {"nme": "create_function", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/sqlite/Function;II)I", "exs": ["java/sql/SQLException"]}, {"nme": "destroy_function", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I", "exs": ["java/sql/SQLException"]}, {"nme": "create_collation", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/sqlite/Collation;)I", "exs": ["java/sql/SQLException"]}, {"nme": "destroy_collation", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I", "exs": ["java/sql/SQLException"]}, {"nme": "backup", "acc": 1025, "dsc": "(Ljava/lang/String;Ljava/lang/String;Lorg/sqlite/core/DB$ProgressObserver;)I", "exs": ["java/sql/SQLException"]}, {"nme": "backup", "acc": 1025, "dsc": "(Ljava/lang/String;Ljava/lang/String;Lorg/sqlite/core/DB$ProgressObserver;III)I", "exs": ["java/sql/SQLException"]}, {"nme": "restore", "acc": 1025, "dsc": "(Ljava/lang/String;Ljava/lang/String;Lorg/sqlite/core/DB$ProgressObserver;)I", "exs": ["java/sql/SQLException"]}, {"nme": "restore", "acc": 1025, "dsc": "(Ljava/lang/String;Ljava/lang/String;Lorg/sqlite/core/DB$ProgressObserver;III)I", "exs": ["java/sql/SQLException"]}, {"nme": "limit", "acc": 1025, "dsc": "(II)I", "exs": ["java/sql/SQLException"]}, {"nme": "register_progress_handler", "acc": 1025, "dsc": "(ILorg/sqlite/ProgressHandler;)V", "exs": ["java/sql/SQLException"]}, {"nme": "clear_progress_handler", "acc": 1025, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "column_metadata", "acc": 1024, "dsc": "(J)[[Z", "exs": ["java/sql/SQLException"]}, {"nme": "column_names", "acc": 49, "dsc": "(J)[<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "sqlbind", "acc": 48, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/Object;)I", "exs": ["java/sql/SQLException"]}, {"nme": "executeBatch", "acc": 48, "dsc": "(Lorg/sqlite/core/SafeStmtPtr;I[<PERSON><PERSON><PERSON>/lang/Object;Z)[J", "exs": ["java/sql/SQLException"]}, {"nme": "executeBatch", "acc": 34, "dsc": "(JI[<PERSON><PERSON><PERSON>/lang/Object;Z)[J", "exs": ["java/sql/SQLException"]}, {"nme": "execute", "acc": 49, "dsc": "(Lorg/sqlite/core/CoreStatement;[<PERSON><PERSON><PERSON>/lang/Object;)Z", "exs": ["java/sql/SQLException"]}, {"nme": "execute", "acc": 34, "dsc": "(J[<PERSON><PERSON><PERSON>/lang/Object;)I", "exs": ["java/sql/SQLException"]}, {"nme": "execute", "acc": 48, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)Z", "exs": ["java/sql/SQLException"]}, {"nme": "executeUpdate", "acc": 49, "dsc": "(Lorg/sqlite/core/CoreStatement;[<PERSON><PERSON><PERSON>/lang/Object;)J", "exs": ["java/sql/SQLException"]}, {"nme": "set_commit_listener", "acc": 1024, "dsc": "(Z)V"}, {"nme": "set_update_listener", "acc": 1024, "dsc": "(Z)V"}, {"nme": "addUpdateListener", "acc": 33, "dsc": "(Lorg/sqlite/SQLiteUpdateListener;)V"}, {"nme": "addCommitListener", "acc": 33, "dsc": "(Lorg/sqlite/SQLiteCommitListener;)V"}, {"nme": "removeUpdateListener", "acc": 33, "dsc": "(Lorg/sqlite/SQLiteUpdateListener;)V"}, {"nme": "removeCommitListener", "acc": 33, "dsc": "(Lorg/sqlite/SQLiteCommitListener;)V"}, {"nme": "onUpdate", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;J)V"}, {"nme": "onCommit", "acc": 0, "dsc": "(Z)V"}, {"nme": "throwex", "acc": 16, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "throwex", "acc": 17, "dsc": "(I)V", "exs": ["java/sql/SQLException"]}, {"nme": "throwex", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/sql/SQLException"]}, {"nme": "newSQLException", "acc": 9, "dsc": "(ILjava/lang/String;)Lorg/sqlite/SQLiteException;"}, {"nme": "newSQLException", "acc": 2, "dsc": "(I)Lorg/sqlite/SQLiteException;", "exs": ["java/sql/SQLException"]}, {"nme": "ensureAutoCommit", "acc": 16, "dsc": "(Z)V", "exs": ["java/sql/SQLException"]}, {"nme": "ensureBeginAndCommit", "acc": 2, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "ensureAutocommit", "acc": 2, "dsc": "(JJ)V", "exs": ["java/sql/SQLException"]}, {"nme": "serialize", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[B", "exs": ["java/sql/SQLException"]}, {"nme": "deserialize", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[B)V", "exs": ["java/sql/SQLException"]}, {"nme": "lambda$ensureAutoCommit$3", "acc": 4098, "dsc": "(Lorg/sqlite/core/DB;J)V", "exs": ["java/sql/SQLException"]}, {"nme": "lambda$ensureAutoCommit$2", "acc": 4098, "dsc": "(JLorg/sqlite/core/DB;J)V", "exs": ["java/sql/SQLException"]}, {"nme": "lambda$execute$1", "acc": 4098, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;Lorg/sqlite/core/DB;J)I", "exs": ["java/sql/SQLException"]}, {"nme": "lambda$executeBatch$0", "acc": 4098, "dsc": "(I[<PERSON><PERSON><PERSON>/lang/Object;ZLorg/sqlite/core/DB;J)[J", "exs": ["java/sql/SQLException"]}], "flds": [{"acc": 18, "nme": "url", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "fileName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "config", "dsc": "Lorg/sqlite/SQLiteConfig;"}, {"acc": 18, "nme": "closed", "dsc": "Ljava/util/concurrent/atomic/AtomicBoolean;"}, {"acc": 64, "nme": "begin", "dsc": "Lorg/sqlite/core/SafeStmtPtr;"}, {"acc": 64, "nme": "commit", "dsc": "Lorg/sqlite/core/SafeStmtPtr;"}, {"acc": 18, "nme": "stmts", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Lorg/sqlite/core/SafeStmtPtr;>;"}, {"acc": 18, "nme": "updateListeners", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Lorg/sqlite/SQLiteUpdateListener;>;"}, {"acc": 18, "nme": "commitListeners", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Lorg/sqlite/SQLiteCommitListener;>;"}]}, "org/sqlite/date/DatePrinter.class": {"ver": 52, "acc": 1537, "nme": "org/sqlite/date/DatePrinter", "super": "java/lang/Object", "mthds": [{"nme": "format", "acc": 1025, "dsc": "(J)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "format", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/Date;)L<PERSON><PERSON>/lang/String;"}, {"nme": "format", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/Calendar;)Ljava/lang/String;"}, {"nme": "format", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/StringBuffer;)<PERSON><PERSON><PERSON>/lang/StringBuffer;"}, {"nme": "format", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/Date;<PERSON><PERSON><PERSON>/lang/StringBuffer;)<PERSON><PERSON><PERSON>/lang/StringBuffer;"}, {"nme": "format", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/Calendar;<PERSON><PERSON><PERSON>/lang/StringBuffer;)<PERSON>ja<PERSON>/lang/StringBuffer;"}, {"nme": "getPattern", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getTimeZone", "acc": 1025, "dsc": "()Ljava/util/TimeZone;"}, {"nme": "getLocale", "acc": 1025, "dsc": "()Ljava/util/Locale;"}, {"nme": "format", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/StringBuffer;<PERSON><PERSON><PERSON>/text/FieldPosition;)<PERSON><PERSON><PERSON>/lang/StringBuffer;"}], "flds": []}, "org/sqlite/jdbc3/JDBC3Connection.class": {"ver": 52, "acc": 1057, "nme": "org/sqlite/jdbc3/JDBC3Connection", "super": "org/sqlite/SQLiteConnection", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/util/Properties;)V", "exs": ["java/sql/SQLException"]}, {"nme": "tryEnforceTransactionMode", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "getCatalog", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "setCatalog", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/sql/SQLException"]}, {"nme": "getHoldability", "acc": 1, "dsc": "()I", "exs": ["java/sql/SQLException"]}, {"nme": "setHoldability", "acc": 1, "dsc": "(I)V", "exs": ["java/sql/SQLException"]}, {"nme": "getTypeMap", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/Class<*>;>;", "exs": ["java/sql/SQLException"]}, {"nme": "setTypeMap", "acc": 1, "dsc": "(Ljava/util/Map;)V", "exs": ["java/sql/SQLException"]}, {"nme": "isReadOnly", "acc": 1, "dsc": "()Z"}, {"nme": "setReadOnly", "acc": 1, "dsc": "(Z)V", "exs": ["java/sql/SQLException"]}, {"nme": "nativeSQL", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "clearWarnings", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "getWarnings", "acc": 1, "dsc": "()Ljava/sql/SQLWarning;", "exs": ["java/sql/SQLException"]}, {"nme": "createStatement", "acc": 1, "dsc": "()Ljava/sql/Statement;", "exs": ["java/sql/SQLException"]}, {"nme": "createStatement", "acc": 1, "dsc": "(II)Ljava/sql/Statement;", "exs": ["java/sql/SQLException"]}, {"nme": "createStatement", "acc": 1025, "dsc": "(III)Ljava/sql/Statement;", "exs": ["java/sql/SQLException"]}, {"nme": "prepareCall", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/sql/CallableStatement;", "exs": ["java/sql/SQLException"]}, {"nme": "prepareCall", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;II)Ljava/sql/CallableStatement;", "exs": ["java/sql/SQLException"]}, {"nme": "prepareCall", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;III)Ljava/sql/CallableStatement;", "exs": ["java/sql/SQLException"]}, {"nme": "prepareStatement", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/sql/PreparedStatement;", "exs": ["java/sql/SQLException"]}, {"nme": "prepareStatement", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Ljava/sql/PreparedStatement;", "exs": ["java/sql/SQLException"]}, {"nme": "prepareStatement", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[I)Ljava/sql/PreparedStatement;", "exs": ["java/sql/SQLException"]}, {"nme": "prepareStatement", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Lja<PERSON>/lang/String;)Ljava/sql/PreparedStatement;", "exs": ["java/sql/SQLException"]}, {"nme": "prepareStatement", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;II)Ljava/sql/PreparedStatement;", "exs": ["java/sql/SQLException"]}, {"nme": "prepareStatement", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;III)Ljava/sql/PreparedStatement;", "exs": ["java/sql/SQLException"]}, {"nme": "setSavepoint", "acc": 1, "dsc": "()Ljava/sql/Savepoint;", "exs": ["java/sql/SQLException"]}, {"nme": "setSavepoint", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/sql/Savepoint;", "exs": ["java/sql/SQLException"]}, {"nme": "releaseSavepoint", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/sql/Savepoint;)V", "exs": ["java/sql/SQLException"]}, {"nme": "rollback", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/sql/Savepoint;)V", "exs": ["java/sql/SQLException"]}, {"nme": "createStruct", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/sql/Struct;", "exs": ["java/sql/SQLException"]}], "flds": [{"acc": 18, "nme": "savePoint", "dsc": "Ljava/util/concurrent/atomic/AtomicInteger;"}, {"acc": 2, "nme": "typeMap", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Class<*>;>;"}, {"acc": 2, "nme": "readOnly", "dsc": "Z"}]}, "META-INF/versions/9/org/sqlite/nativeimage/SqliteJdbcFeature$1.class": {"ver": 53, "acc": 4128, "nme": "org/sqlite/nativeimage/SqliteJdbcFeature$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/sqlite/date/FastDateParser$3.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/date/FastDateParser$3", "super": "org/sqlite/date/FastDateParser$NumberStrategy", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(I)V"}, {"nme": "modify", "acc": 0, "dsc": "(I)I"}], "flds": []}, "org/sqlite/date/FastDatePrinter$Rule.class": {"ver": 52, "acc": 1536, "nme": "org/sqlite/date/FastDatePrinter$Rule", "super": "java/lang/Object", "mthds": [{"nme": "estimateLength", "acc": 1025, "dsc": "()I"}, {"nme": "appendTo", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;<PERSON><PERSON><PERSON>/util/Calendar;)V"}], "flds": []}, "org/sqlite/date/FastDatePrinter.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/date/FastDatePrinter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/TimeZone;<PERSON><PERSON><PERSON>/util/Locale;)V"}, {"nme": "init", "acc": 2, "dsc": "()V"}, {"nme": "parsePattern", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/sqlite/date/FastDatePrinter$Rule;>;"}, {"nme": "parseToken", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[I)<PERSON>java/lang/String;"}, {"nme": "selectNumberRule", "acc": 4, "dsc": "(II)Lorg/sqlite/date/FastDatePrinter$NumberRule;"}, {"nme": "format", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/StringBuffer;<PERSON><PERSON><PERSON>/text/FieldPosition;)<PERSON><PERSON><PERSON>/lang/StringBuffer;"}, {"nme": "format", "acc": 1, "dsc": "(J)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "applyRulesToString", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/Calendar;)Ljava/lang/String;"}, {"nme": "newCalendar", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/util/<PERSON>;"}, {"nme": "format", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Date;)L<PERSON><PERSON>/lang/String;"}, {"nme": "format", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Calendar;)Ljava/lang/String;"}, {"nme": "format", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/StringBuffer;)<PERSON><PERSON><PERSON>/lang/StringBuffer;"}, {"nme": "format", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Date;<PERSON><PERSON><PERSON>/lang/StringBuffer;)<PERSON><PERSON><PERSON>/lang/StringBuffer;"}, {"nme": "format", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Calendar;<PERSON><PERSON><PERSON>/lang/StringBuffer;)<PERSON>ja<PERSON>/lang/StringBuffer;"}, {"nme": "applyRules", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/util/Calendar;<PERSON><PERSON><PERSON>/lang/StringBuffer;)<PERSON>ja<PERSON>/lang/StringBuffer;"}, {"nme": "getPattern", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getTimeZone", "acc": 1, "dsc": "()Ljava/util/TimeZone;"}, {"nme": "getLocale", "acc": 1, "dsc": "()Ljava/util/Locale;"}, {"nme": "getMaxLengthEstimate", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "readObject", "acc": 2, "dsc": "(Ljava/io/ObjectInputStream;)V", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}, {"nme": "appendDigits", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;I)V"}, {"nme": "getTimeZoneDisplay", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/util/TimeZone;ZILjava/util/Locale;)Ljava/lang/String;"}, {"nme": "access$000", "acc": 4104, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;I)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}, {"acc": 25, "nme": "FULL", "dsc": "I", "val": 0}, {"acc": 25, "nme": "LONG", "dsc": "I", "val": 1}, {"acc": 25, "nme": "MEDIUM", "dsc": "I", "val": 2}, {"acc": 25, "nme": "SHORT", "dsc": "I", "val": 3}, {"acc": 18, "nme": "mPattern", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "mTimeZone", "dsc": "Ljava/util/TimeZone;"}, {"acc": 18, "nme": "mLocale", "dsc": "Ljava/util/Locale;"}, {"acc": 130, "nme": "mRules", "dsc": "[Lorg/sqlite/date/FastDatePrinter$Rule;"}, {"acc": 130, "nme": "mMaxLengthEstimate", "dsc": "I"}, {"acc": 26, "nme": "cTimeZoneDisplayCache", "dsc": "Ljava/util/concurrent/ConcurrentMap;", "sig": "Ljava/util/concurrent/ConcurrentMap<Lorg/sqlite/date/FastDatePrinter$TimeZoneDisplayKey;Ljava/lang/String;>;"}]}, "org/sqlite/date/FastDatePrinter$TimeZoneDisplayKey.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/date/FastDatePrinter$TimeZoneDisplayKey", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/util/TimeZone;ZILjava/util/Locale;)V"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "flds": [{"acc": 18, "nme": "mTimeZone", "dsc": "Ljava/util/TimeZone;"}, {"acc": 18, "nme": "mStyle", "dsc": "I"}, {"acc": 18, "nme": "mLocale", "dsc": "Ljava/util/Locale;"}]}, "org/sqlite/SQLiteConfig$DatePrecision.class": {"ver": 52, "acc": 16433, "nme": "org/sqlite/SQLiteConfig$DatePrecision", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lorg/sqlite/SQLiteConfig$DatePrecision;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/sqlite/SQLiteConfig$DatePrecision;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "getValue", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getPrecision", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/sqlite/SQLiteConfig$DatePrecision;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "SECONDS", "dsc": "Lorg/sqlite/SQLiteConfig$DatePrecision;"}, {"acc": 16409, "nme": "MILLISECONDS", "dsc": "Lorg/sqlite/SQLiteConfig$DatePrecision;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lorg/sqlite/SQLiteConfig$DatePrecision;"}]}, "org/sqlite/Collation.class": {"ver": 52, "acc": 1057, "nme": "org/sqlite/Collation", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "create", "acc": 25, "dsc": "(<PERSON><PERSON><PERSON>/sql/Connection;Lja<PERSON>/lang/String;Lorg/sqlite/Collation;)V", "exs": ["java/sql/SQLException"]}, {"nme": "destroy", "acc": 25, "dsc": "(<PERSON><PERSON><PERSON>/sql/Connection;Lja<PERSON>/lang/String;)V", "exs": ["java/sql/SQLException"]}, {"nme": "xCompare", "acc": 1028, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)I"}], "flds": [{"acc": 2, "nme": "conn", "dsc": "Lorg/sqlite/SQLiteConnection;"}, {"acc": 2, "nme": "db", "dsc": "Lorg/sqlite/core/DB;"}]}, "org/sqlite/date/FastDateParser.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/date/FastDateParser", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/TimeZone;<PERSON><PERSON><PERSON>/util/Locale;)V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/TimeZone;<PERSON><PERSON><PERSON>/util/Locale;<PERSON><PERSON><PERSON>/util/Date;)V"}, {"nme": "init", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/Calendar;)V"}, {"nme": "getPattern", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getTimeZone", "acc": 1, "dsc": "()Ljava/util/TimeZone;"}, {"nme": "getLocale", "acc": 1, "dsc": "()Ljava/util/Locale;"}, {"nme": "getParsePattern", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "readObject", "acc": 2, "dsc": "(Ljava/io/ObjectInputStream;)V", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}, {"nme": "parseObject", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/text/ParseException"]}, {"nme": "parse", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Date;", "exs": ["java/text/ParseException"]}, {"nme": "parseObject", "acc": 1, "dsc": "(L<PERSON><PERSON>/lang/String;L<PERSON><PERSON>/text/ParsePosition;)Ljava/lang/Object;"}, {"nme": "parse", "acc": 1, "dsc": "(L<PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/text/ParsePosition;)Ljava/util/Date;"}, {"nme": "escapeRegex", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;<PERSON><PERSON><PERSON>/lang/String;Z)Ljava/lang/StringBuilder;"}, {"nme": "getDisplayNames", "acc": 10, "dsc": "(ILjava/util/Calendar;Ljava/util/Locale;)Ljava/util/Map;", "sig": "(ILjava/util/Calendar;Ljava/util/Locale;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/Integer;>;"}, {"nme": "adjustYear", "acc": 2, "dsc": "(I)I"}, {"nme": "isNextNumber", "acc": 0, "dsc": "()Z"}, {"nme": "getFieldWidth", "acc": 0, "dsc": "()I"}, {"nme": "getStrategy", "acc": 2, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/Calendar;)Lorg/sqlite/date/FastDateParser$Strategy;"}, {"nme": "getCache", "acc": 10, "dsc": "(I)Ljava/util/concurrent/ConcurrentMap;", "sig": "(I)Ljava/util/concurrent/ConcurrentMap<Ljava/util/Locale;Lorg/sqlite/date/FastDateParser$Strategy;>;"}, {"nme": "getLocaleSpecificStrategy", "acc": 2, "dsc": "(ILjava/util/Calendar;)Lorg/sqlite/date/FastDateParser$Strategy;"}, {"nme": "access$100", "acc": 4104, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;<PERSON><PERSON><PERSON>/lang/String;Z)Ljava/lang/StringBuilder;"}, {"nme": "access$200", "acc": 4104, "dsc": "(ILjava/util/Calendar;Ljava/util/Locale;)Ljava/util/Map;"}, {"nme": "access$300", "acc": 4104, "dsc": "(Lorg/sqlite/date/FastDateParser;I)I"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 2}, {"acc": 24, "nme": "JAPANESE_IMPERIAL", "dsc": "Ljava/util/Locale;"}, {"acc": 18, "nme": "pattern", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "timeZone", "dsc": "Ljava/util/TimeZone;"}, {"acc": 18, "nme": "locale", "dsc": "Ljava/util/Locale;"}, {"acc": 18, "nme": "century", "dsc": "I"}, {"acc": 18, "nme": "startYear", "dsc": "I"}, {"acc": 130, "nme": "parsePattern", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 130, "nme": "strategies", "dsc": "[Lorg/sqlite/date/FastDateParser$Strategy;"}, {"acc": 130, "nme": "currentFormatField", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 130, "nme": "nextStrategy", "dsc": "Lorg/sqlite/date/FastDateParser$Strategy;"}, {"acc": 26, "nme": "formatPattern", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 26, "nme": "caches", "dsc": "[Ljava/util/concurrent/ConcurrentMap;", "sig": "[Ljava/util/concurrent/ConcurrentMap<Ljava/util/Locale;Lorg/sqlite/date/FastDateParser$Strategy;>;"}, {"acc": 26, "nme": "ABBREVIATED_YEAR_STRATEGY", "dsc": "Lorg/sqlite/date/FastDateParser$Strategy;"}, {"acc": 26, "nme": "NUMBER_MONTH_STRATEGY", "dsc": "Lorg/sqlite/date/FastDateParser$Strategy;"}, {"acc": 26, "nme": "LITERAL_YEAR_STRATEGY", "dsc": "Lorg/sqlite/date/FastDateParser$Strategy;"}, {"acc": 26, "nme": "WEEK_OF_YEAR_STRATEGY", "dsc": "Lorg/sqlite/date/FastDateParser$Strategy;"}, {"acc": 26, "nme": "WEEK_OF_MONTH_STRATEGY", "dsc": "Lorg/sqlite/date/FastDateParser$Strategy;"}, {"acc": 26, "nme": "DAY_OF_YEAR_STRATEGY", "dsc": "Lorg/sqlite/date/FastDateParser$Strategy;"}, {"acc": 26, "nme": "DAY_OF_MONTH_STRATEGY", "dsc": "Lorg/sqlite/date/FastDateParser$Strategy;"}, {"acc": 26, "nme": "DAY_OF_WEEK_IN_MONTH_STRATEGY", "dsc": "Lorg/sqlite/date/FastDateParser$Strategy;"}, {"acc": 26, "nme": "HOUR_OF_DAY_STRATEGY", "dsc": "Lorg/sqlite/date/FastDateParser$Strategy;"}, {"acc": 26, "nme": "HOUR24_OF_DAY_STRATEGY", "dsc": "Lorg/sqlite/date/FastDateParser$Strategy;"}, {"acc": 26, "nme": "HOUR12_STRATEGY", "dsc": "Lorg/sqlite/date/FastDateParser$Strategy;"}, {"acc": 26, "nme": "HOUR_STRATEGY", "dsc": "Lorg/sqlite/date/FastDateParser$Strategy;"}, {"acc": 26, "nme": "MINUTE_STRATEGY", "dsc": "Lorg/sqlite/date/FastDateParser$Strategy;"}, {"acc": 26, "nme": "SECOND_STRATEGY", "dsc": "Lorg/sqlite/date/FastDateParser$Strategy;"}, {"acc": 26, "nme": "MILLISECOND_STRATEGY", "dsc": "Lorg/sqlite/date/FastDateParser$Strategy;"}, {"acc": 26, "nme": "ISO_8601_STRATEGY", "dsc": "Lorg/sqlite/date/FastDateParser$Strategy;"}]}, "org/sqlite/util/ResourceFinder.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/util/ResourceFinder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "find", "acc": 9, "dsc": "(Ljava/lang/Class;Ljava/lang/String;)Ljava/net/URL;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/String;)Ljava/net/URL;"}, {"nme": "find", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;Lja<PERSON>/lang/Package;Ljava/lang/String;)Ljava/net/URL;"}, {"nme": "find", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;Ljava/lang/String;Ljava/lang/String;)Ljava/net/URL;"}, {"nme": "packagePath", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/String;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Ljava/lang/String;"}, {"nme": "packagePath", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Package;)<PERSON>java/lang/String;"}, {"nme": "packagePath", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": []}, "org/sqlite/core/CoreResultSet.class": {"ver": 52, "acc": 1057, "nme": "org/sqlite/core/CoreResultSet", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Lorg/sqlite/core/CoreStatement;)V"}, {"nme": "getDatabase", "acc": 4, "dsc": "()Lorg/sqlite/core/DB;"}, {"nme": "getConnectionConfig", "acc": 4, "dsc": "()Lorg/sqlite/SQLiteConnectionConfig;"}, {"nme": "isOpen", "acc": 1, "dsc": "()Z"}, {"nme": "checkOpen", "acc": 4, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "checkCol", "acc": 1, "dsc": "(I)I", "exs": ["java/sql/SQLException"]}, {"nme": "markCol", "acc": 4, "dsc": "(I)I", "exs": ["java/sql/SQLException"]}, {"nme": "checkMeta", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "findColumnIndexInCache", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Integer;"}, {"nme": "addColumnIndexInCache", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)I"}], "flds": [{"acc": 20, "nme": "stmt", "dsc": "Lorg/sqlite/core/CoreStatement;"}, {"acc": 1, "nme": "emptyResultSet", "dsc": "Z"}, {"acc": 1, "nme": "open", "dsc": "Z"}, {"acc": 1, "nme": "maxRows", "dsc": "J"}, {"acc": 1, "nme": "cols", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 1, "nme": "colsMeta", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4, "nme": "meta", "dsc": "[[Z"}, {"acc": 4, "nme": "limitRows", "dsc": "I"}, {"acc": 4, "nme": "row", "dsc": "I"}, {"acc": 4, "nme": "pastLastRow", "dsc": "Z"}, {"acc": 4, "nme": "lastCol", "dsc": "I"}, {"acc": 1, "nme": "closeStmt", "dsc": "Z"}, {"acc": 4, "nme": "columnNameToIndex", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Integer;>;"}]}, "org/sqlite/date/FastDateParser$Strategy.class": {"ver": 52, "acc": 1056, "nme": "org/sqlite/date/FastDateParser$Strategy", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "isNumber", "acc": 0, "dsc": "()Z"}, {"nme": "setCalendar", "acc": 0, "dsc": "(Lorg/sqlite/date/FastDateParser;Ljava/util/Calendar;Ljava/lang/String;)V"}, {"nme": "addRegex", "acc": 1024, "dsc": "(Lorg/sqlite/date/FastDateParser;Lja<PERSON>/lang/StringBuilder;)Z"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/sqlite/date/FastDateParser$1;)V"}], "flds": []}, "org/sqlite/SQLiteJDBCLoader$VersionHolder.class": {"ver": 52, "acc": 49, "nme": "org/sqlite/SQLiteJDBCLoader$VersionHolder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "access$000", "acc": 4104, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "VERSION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/sqlite/Function$Aggregate.class": {"ver": 52, "acc": 1057, "nme": "org/sqlite/Function$Aggregate", "super": "org/sqlite/Function", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "xFunc", "acc": 20, "dsc": "()V"}, {"nme": "xStep", "acc": 1028, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "xFinal", "acc": 1028, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "clone", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": []}, "org/sqlite/SQLiteConfig$DateClass.class": {"ver": 52, "acc": 16433, "nme": "org/sqlite/SQLiteConfig$DateClass", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lorg/sqlite/SQLiteConfig$DateClass;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/sqlite/SQLiteConfig$DateClass;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "getValue", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDateClass", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/sqlite/SQLiteConfig$DateClass;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "INTEGER", "dsc": "Lorg/sqlite/SQLiteConfig$DateClass;"}, {"acc": 16409, "nme": "TEXT", "dsc": "Lorg/sqlite/SQLiteConfig$DateClass;"}, {"acc": 16409, "nme": "REAL", "dsc": "Lorg/sqlite/SQLiteConfig$DateClass;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lorg/sqlite/SQLiteConfig$DateClass;"}]}, "org/sqlite/core/DB$ProgressObserver.class": {"ver": 52, "acc": 1537, "nme": "org/sqlite/core/DB$ProgressObserver", "super": "java/lang/Object", "mthds": [{"nme": "progress", "acc": 1025, "dsc": "(II)V"}], "flds": []}, "org/sqlite/date/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/sqlite/date/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/sqlite/SQLiteException.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/SQLiteException", "super": "java/sql/SQLException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/sqlite/SQLiteErrorCode;)V"}, {"nme": "getResultCode", "acc": 1, "dsc": "()Lorg/sqlite/SQLiteErrorCode;"}], "flds": [{"acc": 2, "nme": "resultCode", "dsc": "Lorg/sqlite/SQLiteErrorCode;"}]}, "org/sqlite/NativeLibraryNotFoundException.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/NativeLibraryNotFoundException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "org/sqlite/BusyHandler.class": {"ver": 52, "acc": 1057, "nme": "org/sqlite/BusyHandler", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 10, "dsc": "(Ljava/sql/Connection;Lorg/sqlite/BusyHandler;)V", "exs": ["java/sql/SQLException"]}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 25, "dsc": "(Ljava/sql/Connection;Lorg/sqlite/BusyHandler;)V", "exs": ["java/sql/SQLException"]}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 25, "dsc": "(Ljava/sql/Connection;)V", "exs": ["java/sql/SQLException"]}, {"nme": "callback", "acc": 1028, "dsc": "(I)I", "exs": ["java/sql/SQLException"]}], "flds": []}, "org/sqlite/ExtendedCommand.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/ExtendedCommand", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "parse", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/sqlite/ExtendedCommand$SQLExtension;", "exs": ["java/sql/SQLException"]}, {"nme": "removeQuotation", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": []}, "org/sqlite/jdbc4/JDBC4ResultSet$SqliteClob.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/jdbc4/JDBC4ResultSet$SqliteClob", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Lorg/sqlite/jdbc4/JDBC4ResultSet;Ljava/lang/String;)V"}, {"nme": "free", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "getAsciiStream", "acc": 1, "dsc": "()Ljava/io/InputStream;", "exs": ["java/sql/SQLException"]}, {"nme": "getCharacterStream", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/io/Reader;", "exs": ["java/sql/SQLException"]}, {"nme": "getCharacterStream", "acc": 1, "dsc": "(JJ)Ljava/io/Reader;", "exs": ["java/sql/SQLException"]}, {"nme": "getSubString", "acc": 1, "dsc": "(JI)Ljava/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "length", "acc": 1, "dsc": "()J", "exs": ["java/sql/SQLException"]}, {"nme": "position", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;J)J", "exs": ["java/sql/SQLException"]}, {"nme": "position", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/sql/<PERSON>lo<PERSON>;J)J", "exs": ["java/sql/SQLException"]}, {"nme": "setAsciiStream", "acc": 1, "dsc": "(J)Ljava/io/OutputStream;", "exs": ["java/sql/SQLException"]}, {"nme": "setCharacterStream", "acc": 1, "dsc": "(J)<PERSON><PERSON><PERSON>/io/Writer;", "exs": ["java/sql/SQLException"]}, {"nme": "setString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)I", "exs": ["java/sql/SQLException"]}, {"nme": "setString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;II)I", "exs": ["java/sql/SQLException"]}, {"nme": "truncate", "acc": 1, "dsc": "(J)V", "exs": ["java/sql/SQLException"]}], "flds": [{"acc": 2, "nme": "data", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lorg/sqlite/jdbc4/JDBC4ResultSet;"}]}, "org/sqlite/date/FastDatePrinter$TimeZoneNameRule.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/date/FastDatePrinter$TimeZoneNameRule", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lja<PERSON>/util/TimeZone;Ljava/util/Locale;I)V"}, {"nme": "estimateLength", "acc": 1, "dsc": "()I"}, {"nme": "appendTo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;<PERSON><PERSON><PERSON>/util/Calendar;)V"}], "flds": [{"acc": 18, "nme": "mLocale", "dsc": "Ljava/util/Locale;"}, {"acc": 18, "nme": "mStyle", "dsc": "I"}, {"acc": 18, "nme": "m<PERSON><PERSON><PERSON>d", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "mDaylight", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/sqlite/SQLiteConnectionConfig.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/SQLiteConnectionConfig", "super": "java/lang/Object", "mthds": [{"nme": "fromPragmaTable", "acc": 9, "dsc": "(Ljava/util/Properties;)Lorg/sqlite/SQLiteConnectionConfig;"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/sqlite/SQLiteConfig$DateClass;Lorg/sqlite/SQLiteConfig$DatePrecision;Ljava/lang/String;ILorg/sqlite/SQLiteConfig$TransactionMode;Z)V"}, {"nme": "copyConfig", "acc": 1, "dsc": "()Lorg/sqlite/SQLiteConnectionConfig;"}, {"nme": "getDateMultiplier", "acc": 1, "dsc": "()J"}, {"nme": "getDateClass", "acc": 1, "dsc": "()Lorg/sqlite/SQLiteConfig$DateClass;"}, {"nme": "setDateClass", "acc": 1, "dsc": "(Lorg/sqlite/SQLiteConfig$DateClass;)V"}, {"nme": "getDatePrecision", "acc": 1, "dsc": "()Lorg/sqlite/SQLiteConfig$DatePrecision;"}, {"nme": "setDatePrecision", "acc": 1, "dsc": "(Lorg/sqlite/SQLiteConfig$DatePrecision;)V"}, {"nme": "getDateStringFormat", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setDateStringFormat", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getDateFormat", "acc": 1, "dsc": "()Lorg/sqlite/date/FastDateFormat;"}, {"nme": "isAutoCommit", "acc": 1, "dsc": "()Z"}, {"nme": "setAutoCommit", "acc": 1, "dsc": "(Z)V"}, {"nme": "getTransactionIsolation", "acc": 1, "dsc": "()I"}, {"nme": "setTransactionIsolation", "acc": 1, "dsc": "(I)V"}, {"nme": "getTransactionMode", "acc": 1, "dsc": "()Lorg/sqlite/SQLiteConfig$TransactionMode;"}, {"nme": "setTransactionMode", "acc": 1, "dsc": "(Lorg/sqlite/SQLiteConfig$TransactionMode;)V"}, {"nme": "transactionPrefix", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "dateClass", "dsc": "Lorg/sqlite/SQLiteConfig$DateClass;"}, {"acc": 2, "nme": "datePrecision", "dsc": "Lorg/sqlite/SQLiteConfig$DatePrecision;"}, {"acc": 2, "nme": "dateStringFormat", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "dateFormat", "dsc": "Lorg/sqlite/date/FastDateFormat;"}, {"acc": 2, "nme": "transactionIsolation", "dsc": "I"}, {"acc": 2, "nme": "transactionMode", "dsc": "Lorg/sqlite/SQLiteConfig$TransactionMode;"}, {"acc": 2, "nme": "autoCommit", "dsc": "Z"}, {"acc": 26, "nme": "beginCommandMap", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Lorg/sqlite/SQLiteConfig$TransactionMode;Ljava/lang/String;>;"}]}, "org/sqlite/jdbc3/JDBC3Statement$BackupObserver.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/jdbc3/JDBC3Statement$BackupObserver", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "progress", "acc": 1, "dsc": "(II)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "logger", "dsc": "Lorg/slf4j/Logger;"}]}, "org/sqlite/SQLiteUpdateListener.class": {"ver": 52, "acc": 1537, "nme": "org/sqlite/SQLiteUpdateListener", "super": "java/lang/Object", "mthds": [{"nme": "onUpdate", "acc": 1025, "dsc": "(Lorg/sqlite/SQLiteUpdateListener$Type;Ljava/lang/String;Ljava/lang/String;J)V"}], "flds": []}, "org/sqlite/SQLiteOpenMode.class": {"ver": 52, "acc": 16433, "nme": "org/sqlite/SQLiteOpenMode", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lorg/sqlite/SQLiteOpenMode;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/sqlite/SQLiteOpenMode;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;II)V", "sig": "(I)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "READONLY", "dsc": "Lorg/sqlite/SQLiteOpenMode;"}, {"acc": 16409, "nme": "READWRITE", "dsc": "Lorg/sqlite/SQLiteOpenMode;"}, {"acc": 16409, "nme": "CREATE", "dsc": "Lorg/sqlite/SQLiteOpenMode;"}, {"acc": 16409, "nme": "DELETEONCLOSE", "dsc": "Lorg/sqlite/SQLiteOpenMode;"}, {"acc": 16409, "nme": "EXCLUSIVE", "dsc": "Lorg/sqlite/SQLiteOpenMode;"}, {"acc": 16409, "nme": "OPEN_URI", "dsc": "Lorg/sqlite/SQLiteOpenMode;"}, {"acc": 16409, "nme": "OPEN_MEMORY", "dsc": "Lorg/sqlite/SQLiteOpenMode;"}, {"acc": 16409, "nme": "MAIN_DB", "dsc": "Lorg/sqlite/SQLiteOpenMode;"}, {"acc": 16409, "nme": "TEMP_DB", "dsc": "Lorg/sqlite/SQLiteOpenMode;"}, {"acc": 16409, "nme": "TRANSIENT_DB", "dsc": "Lorg/sqlite/SQLiteOpenMode;"}, {"acc": 16409, "nme": "MAIN_JOURNAL", "dsc": "Lorg/sqlite/SQLiteOpenMode;"}, {"acc": 16409, "nme": "TEMP_JOURNAL", "dsc": "Lorg/sqlite/SQLiteOpenMode;"}, {"acc": 16409, "nme": "SUBJOURNAL", "dsc": "Lorg/sqlite/SQLiteOpenMode;"}, {"acc": 16409, "nme": "MASTER_JOURNAL", "dsc": "Lorg/sqlite/SQLiteOpenMode;"}, {"acc": 16409, "nme": "NOMUTEX", "dsc": "Lorg/sqlite/SQLiteOpenMode;"}, {"acc": 16409, "nme": "FULLMUTEX", "dsc": "Lorg/sqlite/SQLiteOpenMode;"}, {"acc": 16409, "nme": "SHAREDCACHE", "dsc": "Lorg/sqlite/SQLiteOpenMode;"}, {"acc": 16409, "nme": "PRIVATECACHE", "dsc": "Lorg/sqlite/SQLiteOpenMode;"}, {"acc": 17, "nme": "flag", "dsc": "I"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lorg/sqlite/SQLiteOpenMode;"}]}, "org/sqlite/FileException.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/FileException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "org/sqlite/SQLiteConfig$HexKeyMode.class": {"ver": 52, "acc": 16433, "nme": "org/sqlite/SQLiteConfig$HexKeyMode", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lorg/sqlite/SQLiteConfig$HexKeyMode;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/sqlite/SQLiteConfig$HexKeyMode;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "getValue", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "NONE", "dsc": "Lorg/sqlite/SQLiteConfig$HexKeyMode;"}, {"acc": 16409, "nme": "SSE", "dsc": "Lorg/sqlite/SQLiteConfig$HexKeyMode;"}, {"acc": 16409, "nme": "SQLCIPHER", "dsc": "Lorg/sqlite/SQLiteConfig$HexKeyMode;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lorg/sqlite/SQLiteConfig$HexKeyMode;"}]}, "org/sqlite/util/OSInfo.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/util/OSInfo", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "main", "acc": 9, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getNativeLibFolderPathForCurrentOS", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getOSName", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isAndroid", "acc": 9, "dsc": "()Z"}, {"nme": "isAndroidRuntime", "acc": 9, "dsc": "()Z"}, {"nme": "isAndroidTermux", "acc": 9, "dsc": "()Z"}, {"nme": "isMusl", "acc": 9, "dsc": "()Z"}, {"nme": "isAlpineLinux", "acc": 10, "dsc": "()Z"}, {"nme": "getHardwareName", "acc": 8, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "resolveArmArchType", "acc": 8, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getArchName", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "translateOSNameToFolderName", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "translateArchNameToFolderName", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "lambda$isAlpineLinux$2", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "lambda$isMusl$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "lambda$isMusl$0", "acc": 4106, "dsc": "(Lja<PERSON>/nio/file/Path;)Ljava/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 12, "nme": "processRunner", "dsc": "Lorg/sqlite/util/ProcessRunner;"}, {"acc": 26, "nme": "archMapping", "dsc": "<PERSON><PERSON><PERSON>/util/HashMap;", "sig": "Lja<PERSON>/util/HashMap<Ljava/lang/String;Ljava/lang/String;>;"}, {"acc": 25, "nme": "X86", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "x86"}, {"acc": 25, "nme": "X86_64", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "x86_64"}, {"acc": 25, "nme": "IA64_32", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "ia64_32"}, {"acc": 25, "nme": "IA64", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "ia64"}, {"acc": 25, "nme": "PPC", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "ppc"}, {"acc": 25, "nme": "PPC64", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "ppc64"}]}, "org/sqlite/jdbc3/JDBC3Savepoint.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/jdbc3/JDBC3Savepoint", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(I)V"}, {"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getSavepointId", "acc": 1, "dsc": "()I", "exs": ["java/sql/SQLException"]}, {"nme": "getSavepointName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/sql/SQLException"]}], "flds": [{"acc": 16, "nme": "id", "dsc": "I"}, {"acc": 16, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/sqlite/SQLiteConfig$PragmaValue.class": {"ver": 52, "acc": 1536, "nme": "org/sqlite/SQLiteConfig$PragmaValue", "super": "java/lang/Object", "mthds": [{"nme": "getValue", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "org/sqlite/date/FastDateParser$CaseInsensitiveTextStrategy.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/date/FastDateParser$CaseInsensitiveTextStrategy", "super": "org/sqlite/date/FastDateParser$Strategy", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(ILjava/util/Calendar;Ljava/util/Locale;)V"}, {"nme": "addRegex", "acc": 0, "dsc": "(Lorg/sqlite/date/FastDateParser;Lja<PERSON>/lang/StringBuilder;)Z"}, {"nme": "setCalendar", "acc": 0, "dsc": "(Lorg/sqlite/date/FastDateParser;Ljava/util/Calendar;Ljava/lang/String;)V"}], "flds": [{"acc": 18, "nme": "field", "dsc": "I"}, {"acc": 18, "nme": "locale", "dsc": "Ljava/util/Locale;"}, {"acc": 18, "nme": "lKeyValues", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Integer;>;"}]}, "org/sqlite/util/ProcessRunner.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/util/ProcessRunner", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "runAndWaitFor", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "exs": ["java/io/IOException", "java/lang/InterruptedException"]}, {"nme": "runAndWaitFor", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON><PERSON>/util/concurrent/TimeUnit;)Ljava/lang/String;", "exs": ["java/io/IOException", "java/lang/InterruptedException"]}, {"nme": "getProcessOutput", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Process;)Ljava/lang/String;", "exs": ["java/io/IOException"]}], "flds": []}, "org/sqlite/jdbc3/JDBC3Statement.class": {"ver": 52, "acc": 1057, "nme": "org/sqlite/jdbc3/JDBC3Statement", "super": "org/sqlite/core/CoreStatement", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Lorg/sqlite/SQLiteConnection;)V"}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "execute", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z", "exs": ["java/sql/SQLException"]}, {"nme": "execute", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Z", "exs": ["java/sql/SQLException"]}, {"nme": "execute<PERSON>uery", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)Ljava/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "execute<PERSON>uery", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "executeUpdate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I", "exs": ["java/sql/SQLException"]}, {"nme": "executeUpdate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)I", "exs": ["java/sql/SQLException"]}, {"nme": "executeLargeUpdate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)J", "exs": ["java/sql/SQLException"]}, {"nme": "executeLargeUpdate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)J", "exs": ["java/sql/SQLException"]}, {"nme": "getResultSet", "acc": 1, "dsc": "()Ljava/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "getUpdateCount", "acc": 1, "dsc": "()I", "exs": ["java/sql/SQLException"]}, {"nme": "getLargeUpdateCount", "acc": 1, "dsc": "()J", "exs": ["java/sql/SQLException"]}, {"nme": "addBatch", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/sql/SQLException"]}, {"nme": "clearBatch", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "executeBatch", "acc": 1, "dsc": "()[I", "exs": ["java/sql/SQLException"]}, {"nme": "executeLargeBatch", "acc": 1, "dsc": "()[J", "exs": ["java/sql/SQLException"]}, {"nme": "setCursorName", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getWarnings", "acc": 1, "dsc": "()Ljava/sql/SQLWarning;", "exs": ["java/sql/SQLException"]}, {"nme": "clearWarnings", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "getConnection", "acc": 1, "dsc": "()Ljava/sql/Connection;", "exs": ["java/sql/SQLException"]}, {"nme": "cancel", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "getQueryTimeout", "acc": 1, "dsc": "()I", "exs": ["java/sql/SQLException"]}, {"nme": "setQueryTimeout", "acc": 1, "dsc": "(I)V", "exs": ["java/sql/SQLException"]}, {"nme": "getMaxRows", "acc": 1, "dsc": "()I", "exs": ["java/sql/SQLException"]}, {"nme": "getLargeMaxRows", "acc": 1, "dsc": "()J", "exs": ["java/sql/SQLException"]}, {"nme": "setMaxRows", "acc": 1, "dsc": "(I)V", "exs": ["java/sql/SQLException"]}, {"nme": "setLargeMaxRows", "acc": 1, "dsc": "(J)V", "exs": ["java/sql/SQLException"]}, {"nme": "getMaxFieldSize", "acc": 1, "dsc": "()I", "exs": ["java/sql/SQLException"]}, {"nme": "setMaxFieldSize", "acc": 1, "dsc": "(I)V", "exs": ["java/sql/SQLException"]}, {"nme": "getFetchSize", "acc": 1, "dsc": "()I", "exs": ["java/sql/SQLException"]}, {"nme": "setFetchSize", "acc": 1, "dsc": "(I)V", "exs": ["java/sql/SQLException"]}, {"nme": "getFetchDirection", "acc": 1, "dsc": "()I", "exs": ["java/sql/SQLException"]}, {"nme": "setFetchDirection", "acc": 1, "dsc": "(I)V", "exs": ["java/sql/SQLException"]}, {"nme": "getGeneratedKeys", "acc": 1, "dsc": "()Ljava/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "getMoreResults", "acc": 1, "dsc": "()Z", "exs": ["java/sql/SQLException"]}, {"nme": "getMoreResults", "acc": 1, "dsc": "(I)Z", "exs": ["java/sql/SQLException"]}, {"nme": "getResultSetConcurrency", "acc": 1, "dsc": "()I", "exs": ["java/sql/SQLException"]}, {"nme": "getResultSetHoldability", "acc": 1, "dsc": "()I", "exs": ["java/sql/SQLException"]}, {"nme": "getResultSetType", "acc": 1, "dsc": "()I", "exs": ["java/sql/SQLException"]}, {"nme": "setEscapeProcessing", "acc": 1, "dsc": "(Z)V"}, {"nme": "unsupported", "acc": 4, "dsc": "()Ljava/sql/SQLException;"}, {"nme": "execute", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[I)Z", "exs": ["java/sql/SQLException"]}, {"nme": "execute", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;)Z", "exs": ["java/sql/SQLException"]}, {"nme": "executeUpdate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[I)I", "exs": ["java/sql/SQLException"]}, {"nme": "executeUpdate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;)I", "exs": ["java/sql/SQLException"]}, {"nme": "executeLargeUpdate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[I)J", "exs": ["java/sql/SQLException"]}, {"nme": "executeLargeUpdate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;)J", "exs": ["java/sql/SQLException"]}, {"nme": "withConnectionTimeout", "acc": 4, "dsc": "(Lorg/sqlite/jdbc3/JDBC3Statement$SQLCallable;)Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Lorg/sqlite/jdbc3/JDBC3Statement$SQLCallable<TT;>;)TT;", "exs": ["java/sql/SQLException"]}, {"nme": "lambda$executeBatch$3", "acc": 4106, "dsc": "(J)I"}, {"nme": "lambda$executeLargeUpdate$2", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Long;", "exs": ["java/sql/SQLException"]}, {"nme": "lambda$executeQuery$1", "acc": 4098, "dsc": "()Ljava/sql/ResultSet;", "exs": ["java/sql/SQLException"]}, {"nme": "lambda$execute$0", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Bo<PERSON>an;", "exs": ["java/sql/SQLException"]}], "flds": [{"acc": 2, "nme": "queryTimeout", "dsc": "I"}, {"acc": 4, "nme": "updateCount", "dsc": "J"}, {"acc": 4, "nme": "exhaustedResults", "dsc": "Z"}]}, "org/sqlite/jdbc4/JDBC4ResultSet.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/jdbc4/JDBC4ResultSet", "super": "org/sqlite/jdbc3/JDBC3ResultSet", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/sqlite/core/CoreStatement;)V"}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "unwrap", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>ja<PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;)TT;", "exs": ["java/lang/ClassCastException"]}, {"nme": "isWrapperFor", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "getRowId", "acc": 1, "dsc": "(I)<PERSON>java/sql/RowId;", "exs": ["java/sql/SQLException"]}, {"nme": "getRowId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/sql/RowId;", "exs": ["java/sql/SQLException"]}, {"nme": "updateRowId", "acc": 1, "dsc": "(ILjava/sql/RowId;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateRowId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/sql/RowId;)V", "exs": ["java/sql/SQLException"]}, {"nme": "getHoldability", "acc": 1, "dsc": "()I", "exs": ["java/sql/SQLException"]}, {"nme": "isClosed", "acc": 1, "dsc": "()Z", "exs": ["java/sql/SQLException"]}, {"nme": "updateNString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateNString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateNClob", "acc": 1, "dsc": "(ILjava/sql/NClob;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateNClob", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/sql/NClob;)V", "exs": ["java/sql/SQLException"]}, {"nme": "getNClob", "acc": 1, "dsc": "(I)Ljava/sql/NClob;", "exs": ["java/sql/SQLException"]}, {"nme": "getNClob", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/sql/NClob;", "exs": ["java/sql/SQLException"]}, {"nme": "getSQLXML", "acc": 1, "dsc": "(I)Ljava/sql/SQLXML;", "exs": ["java/sql/SQLException"]}, {"nme": "getSQLXML", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;)Ljava/sql/SQLXML;", "exs": ["java/sql/SQLException"]}, {"nme": "updateSQLXML", "acc": 1, "dsc": "(ILjava/sql/SQLXML;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateSQLXML", "acc": 1, "dsc": "(Ljava/lang/String;Ljava/sql/SQLXML;)V", "exs": ["java/sql/SQLException"]}, {"nme": "getNString", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "getNString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "getNCharacterStream", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/io/Reader;", "exs": ["java/sql/SQLException"]}, {"nme": "getNCharacterStreamInternal", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/io/Reader;"}, {"nme": "getNCharacterStream", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/io/Reader;", "exs": ["java/sql/SQLException"]}, {"nme": "updateNCharacterStream", "acc": 1, "dsc": "(ILjava/io/Reader;J)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateNCharacterStream", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/io/Reader;J)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateAsciiStream", "acc": 1, "dsc": "(ILjava/io/InputStream;J)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateBinaryStream", "acc": 1, "dsc": "(ILjava/io/InputStream;J)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateCharacterStream", "acc": 1, "dsc": "(ILjava/io/Reader;J)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateAsciiStream", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/io/InputStream;J)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateBinaryStream", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/io/InputStream;J)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateCharacterStream", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/io/Reader;J)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateBlob", "acc": 1, "dsc": "(ILjava/io/InputStream;J)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateBlob", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/io/InputStream;J)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateClob", "acc": 1, "dsc": "(ILjava/io/Reader;J)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateClob", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/io/Reader;J)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateNClob", "acc": 1, "dsc": "(ILjava/io/Reader;J)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateNClob", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/io/Reader;J)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateNCharacterStream", "acc": 1, "dsc": "(ILjava/io/Reader;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateNCharacterStream", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/io/Reader;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateAsciiStream", "acc": 1, "dsc": "(ILjava/io/InputStream;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateBinaryStream", "acc": 1, "dsc": "(ILjava/io/InputStream;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateCharacterStream", "acc": 1, "dsc": "(ILjava/io/Reader;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateAsciiStream", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/io/InputStream;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateBinaryStream", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/io/InputStream;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateCharacterStream", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/io/Reader;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateBlob", "acc": 1, "dsc": "(ILjava/io/InputStream;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateBlob", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/io/InputStream;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateClob", "acc": 1, "dsc": "(ILjava/io/Reader;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateClob", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/io/Reader;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateNClob", "acc": 1, "dsc": "(ILjava/io/Reader;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateNClob", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/io/Reader;)V", "exs": ["java/sql/SQLException"]}, {"nme": "getObject", "acc": 1, "dsc": "(<PERSON>java/lang/Class;)Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>(ILjava/lang/Class<TT;>;)TT;", "exs": ["java/sql/SQLException"]}, {"nme": "getObject", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Class;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/String;Ljava/lang/Class<TT;>;)TT;", "exs": ["java/sql/SQLException"]}, {"nme": "unsupported", "acc": 4, "dsc": "()Ljava/sql/SQLException;"}, {"nme": "getArray", "acc": 1, "dsc": "(I)Ljava/sql/Array;", "exs": ["java/sql/SQLException"]}, {"nme": "getArray", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON>java/sql/Array;", "exs": ["java/sql/SQLException"]}, {"nme": "getAsciiStream", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/io/InputStream;", "exs": ["java/sql/SQLException"]}, {"nme": "getAsciiStream", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/io/InputStream;", "exs": ["java/sql/SQLException"]}, {"nme": "getAsciiStreamInternal", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/io/InputStream;"}, {"nme": "getBigDecimal", "acc": 131073, "dsc": "(II)Ljava/math/BigDecimal;", "exs": ["java/sql/SQLException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getBigDecimal", "acc": 131073, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Ljava/math/BigDecimal;", "exs": ["java/sql/SQLException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getBlob", "acc": 1, "dsc": "(I)<PERSON><PERSON>va/sql/Blob;", "exs": ["java/sql/SQLException"]}, {"nme": "getBlob", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON>ja<PERSON>/sql/Blob;", "exs": ["java/sql/SQLException"]}, {"nme": "getClob", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/sql/Clob;", "exs": ["java/sql/SQLException"]}, {"nme": "getClob", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON>ja<PERSON>/sql/Clob;", "exs": ["java/sql/SQLException"]}, {"nme": "getObject", "acc": 1, "dsc": "(ILjava/util/Map;)Ljava/lang/Object;", "exs": ["java/sql/SQLException"]}, {"nme": "getObject", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)<PERSON>java/lang/Object;", "exs": ["java/sql/SQLException"]}, {"nme": "getRef", "acc": 1, "dsc": "(I)<PERSON>java/sql/Ref;", "exs": ["java/sql/SQLException"]}, {"nme": "getRef", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/sql/Ref;", "exs": ["java/sql/SQLException"]}, {"nme": "getUnicodeStream", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/io/InputStream;", "exs": ["java/sql/SQLException"]}, {"nme": "getUnicodeStream", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/io/InputStream;", "exs": ["java/sql/SQLException"]}, {"nme": "getURL", "acc": 1, "dsc": "(I)Ljava/net/URL;", "exs": ["java/sql/SQLException"]}, {"nme": "getURL", "acc": 1, "dsc": "(Ljava/lang/String;)Ljava/net/URL;", "exs": ["java/sql/SQLException"]}, {"nme": "insertRow", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "moveToCurrentRow", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "moveToInsertRow", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "last", "acc": 1, "dsc": "()Z", "exs": ["java/sql/SQLException"]}, {"nme": "previous", "acc": 1, "dsc": "()Z", "exs": ["java/sql/SQLException"]}, {"nme": "relative", "acc": 1, "dsc": "(I)Z", "exs": ["java/sql/SQLException"]}, {"nme": "absolute", "acc": 1, "dsc": "(I)Z", "exs": ["java/sql/SQLException"]}, {"nme": "afterLast", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "first", "acc": 1, "dsc": "()Z", "exs": ["java/sql/SQLException"]}, {"nme": "cancelRowUpdates", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "deleteRow", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "updateArray", "acc": 1, "dsc": "(ILjava/sql/Array;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateArray", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/sql/Array;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateAsciiStream", "acc": 1, "dsc": "(ILjava/io/InputStream;I)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateAsciiStream", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/io/InputStream;I)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateBigDecimal", "acc": 1, "dsc": "(ILjava/math/BigDecimal;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateBigDecimal", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Ljava/math/BigDecimal;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateBinaryStream", "acc": 1, "dsc": "(ILjava/io/InputStream;I)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateBinaryStream", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/io/InputStream;I)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateBlob", "acc": 1, "dsc": "(ILjava/sql/Blob;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateBlob", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/sql/Blob;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateBoolean", "acc": 1, "dsc": "(IZ)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateBoolean", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateByte", "acc": 1, "dsc": "(IB)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateByte", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;B)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateBytes", "acc": 1, "dsc": "(I[B)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateBytes", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[B)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateCharacterStream", "acc": 1, "dsc": "(ILjava/io/Reader;I)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateCharacterStream", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/io/Reader;I)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateClob", "acc": 1, "dsc": "(ILjava/sql/<PERSON>lob;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateClob", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/sql/Clob;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateDate", "acc": 1, "dsc": "(ILjava/sql/Date;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateDate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/sql/Date;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateDouble", "acc": 1, "dsc": "(ID)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateDouble", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;D)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateFloat", "acc": 1, "dsc": "(IF)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateFloat", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;F)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateInt", "acc": 1, "dsc": "(II)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateInt", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateLong", "acc": 1, "dsc": "(IJ)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateLong", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;J)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateNull", "acc": 1, "dsc": "(I)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateNull", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateObject", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateObject", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;I)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateObject", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateObject", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;I)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateRef", "acc": 1, "dsc": "(ILjava/sql/Ref;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateRef", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/sql/Ref;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateRow", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "updateShort", "acc": 1, "dsc": "(IS)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateShort", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;S)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateTime", "acc": 1, "dsc": "(ILjava/sql/Time;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateTime", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/sql/Time;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateTimestamp", "acc": 1, "dsc": "(ILjava/sql/Timestamp;)V", "exs": ["java/sql/SQLException"]}, {"nme": "updateTimestamp", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/sql/Timestamp;)V", "exs": ["java/sql/SQLException"]}, {"nme": "refreshRow", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "access$000", "acc": 4104, "dsc": "(Lorg/sqlite/jdbc4/JDBC4ResultSet;Ljava/lang/String;)Ljava/io/InputStream;"}, {"nme": "access$100", "acc": 4104, "dsc": "(Lorg/sqlite/jdbc4/JDBC4ResultSet;Lja<PERSON>/lang/String;)Ljava/io/Reader;"}], "flds": []}, "org/sqlite/core/CorePreparedStatement$1.class": {"ver": 52, "acc": 4128, "nme": "org/sqlite/core/CorePreparedStatement$1", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$org$sqlite$SQLiteConfig$DateClass", "dsc": "[I"}]}, "org/sqlite/date/FastDateParser$1.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/date/FastDateParser$1", "super": "org/sqlite/date/FastDateParser$NumberStrategy", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(I)V"}, {"nme": "setCalendar", "acc": 0, "dsc": "(Lorg/sqlite/date/FastDateParser;Ljava/util/Calendar;Ljava/lang/String;)V"}], "flds": []}, "org/sqlite/core/Codes.class": {"ver": 52, "acc": 1537, "nme": "org/sqlite/core/Codes", "super": "java/lang/Object", "mthds": [], "flds": [{"acc": 25, "nme": "SQLITE_OK", "dsc": "I", "val": 0}, {"acc": 25, "nme": "SQLITE_ERROR", "dsc": "I", "val": 1}, {"acc": 25, "nme": "SQLITE_INTERNAL", "dsc": "I", "val": 2}, {"acc": 25, "nme": "SQLITE_PERM", "dsc": "I", "val": 3}, {"acc": 25, "nme": "SQLITE_ABORT", "dsc": "I", "val": 4}, {"acc": 25, "nme": "SQLITE_BUSY", "dsc": "I", "val": 5}, {"acc": 25, "nme": "SQLITE_LOCKED", "dsc": "I", "val": 6}, {"acc": 25, "nme": "SQLITE_NOMEM", "dsc": "I", "val": 7}, {"acc": 25, "nme": "SQLITE_READONLY", "dsc": "I", "val": 8}, {"acc": 25, "nme": "SQLITE_INTERRUPT", "dsc": "I", "val": 9}, {"acc": 25, "nme": "SQLITE_IOERR", "dsc": "I", "val": 10}, {"acc": 25, "nme": "SQLITE_CORRUPT", "dsc": "I", "val": 11}, {"acc": 25, "nme": "SQLITE_NOTFOUND", "dsc": "I", "val": 12}, {"acc": 25, "nme": "SQLITE_FULL", "dsc": "I", "val": 13}, {"acc": 25, "nme": "SQLITE_CANTOPEN", "dsc": "I", "val": 14}, {"acc": 25, "nme": "SQLITE_PROTOCOL", "dsc": "I", "val": 15}, {"acc": 25, "nme": "SQLITE_EMPTY", "dsc": "I", "val": 16}, {"acc": 25, "nme": "SQLITE_SCHEMA", "dsc": "I", "val": 17}, {"acc": 25, "nme": "SQLITE_TOOBIG", "dsc": "I", "val": 18}, {"acc": 25, "nme": "SQLITE_CONSTRAINT", "dsc": "I", "val": 19}, {"acc": 25, "nme": "SQLITE_MISMATCH", "dsc": "I", "val": 20}, {"acc": 25, "nme": "SQLITE_MISUSE", "dsc": "I", "val": 21}, {"acc": 25, "nme": "SQLITE_NOLFS", "dsc": "I", "val": 22}, {"acc": 25, "nme": "SQLITE_AUTH", "dsc": "I", "val": 23}, {"acc": 25, "nme": "SQLITE_ROW", "dsc": "I", "val": 100}, {"acc": 25, "nme": "SQLITE_DONE", "dsc": "I", "val": 101}, {"acc": 25, "nme": "SQLITE_INTEGER", "dsc": "I", "val": 1}, {"acc": 25, "nme": "SQLITE_FLOAT", "dsc": "I", "val": 2}, {"acc": 25, "nme": "SQLITE_TEXT", "dsc": "I", "val": 3}, {"acc": 25, "nme": "SQLITE_BLOB", "dsc": "I", "val": 4}, {"acc": 25, "nme": "SQLITE_NULL", "dsc": "I", "val": 5}]}, "org/sqlite/Function$Window.class": {"ver": 52, "acc": 1057, "nme": "org/sqlite/Function$Window", "super": "org/sqlite/Function$Aggregate", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "xInverse", "acc": 1028, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "xValue", "acc": 1028, "dsc": "()V", "exs": ["java/sql/SQLException"]}], "flds": []}, "org/sqlite/util/StringUtils.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/util/StringUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "join", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/String;>;Ljava/lang/String;)Ljava/lang/String;"}], "flds": []}, "org/sqlite/date/FastDatePrinter$UnpaddedMonthField.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/date/FastDatePrinter$UnpaddedMonthField", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "estimateLength", "acc": 1, "dsc": "()I"}, {"nme": "appendTo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;<PERSON><PERSON><PERSON>/util/Calendar;)V"}, {"nme": "appendTo", "acc": 17, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;I)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "INSTANCE", "dsc": "Lorg/sqlite/date/FastDatePrinter$UnpaddedMonthField;"}]}, "org/sqlite/SQLiteConnection.class": {"ver": 52, "acc": 1057, "nme": "org/sqlite/SQLiteConnection", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/sqlite/core/DB;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/sql/SQLException"]}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/util/Properties;)V", "exs": ["java/sql/SQLException"]}, {"nme": "getCurrentTransactionMode", "acc": 1, "dsc": "()Lorg/sqlite/SQLiteConfig$TransactionMode;"}, {"nme": "setCurrentTransactionMode", "acc": 1, "dsc": "(Lorg/sqlite/SQLiteConfig$TransactionMode;)V"}, {"nme": "setFirstStatementExecuted", "acc": 1, "dsc": "(Z)V"}, {"nme": "isFirstStatementExecuted", "acc": 1, "dsc": "()Z"}, {"nme": "getConnectionConfig", "acc": 1, "dsc": "()Lorg/sqlite/SQLiteConnectionConfig;"}, {"nme": "getSQLiteDatabaseMetaData", "acc": 1, "dsc": "()Lorg/sqlite/core/CoreDatabaseMetaData;", "exs": ["java/sql/SQLException"]}, {"nme": "getMetaData", "acc": 1, "dsc": "()Ljava/sql/DatabaseMetaData;", "exs": ["java/sql/SQLException"]}, {"nme": "getUrl", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setSchema", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/sql/SQLException"]}, {"nme": "getSchema", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "abort", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/concurrent/Executor;)V", "exs": ["java/sql/SQLException"]}, {"nme": "setNetworkTimeout", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/concurrent/Executor;I)V", "exs": ["java/sql/SQLException"]}, {"nme": "getNetworkTimeout", "acc": 1, "dsc": "()I", "exs": ["java/sql/SQLException"]}, {"nme": "checkCursor", "acc": 4, "dsc": "(III)V", "exs": ["java/sql/SQLException"]}, {"nme": "setTransactionMode", "acc": 4, "dsc": "(Lorg/sqlite/SQLiteConfig$TransactionMode;)V"}, {"nme": "getTransactionIsolation", "acc": 1, "dsc": "()I"}, {"nme": "setTransactionIsolation", "acc": 1, "dsc": "(I)V", "exs": ["java/sql/SQLException"]}, {"nme": "open", "acc": 10, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;Ljava/util/Properties;)Lorg/sqlite/core/DB;", "exs": ["java/sql/SQLException"]}, {"nme": "extractResource", "acc": 10, "dsc": "(Ljava/net/URL;)Ljava/io/File;", "exs": ["java/io/IOException"]}, {"nme": "getDatabase", "acc": 1, "dsc": "()Lorg/sqlite/core/DB;"}, {"nme": "getAutoCommit", "acc": 1, "dsc": "()Z", "exs": ["java/sql/SQLException"]}, {"nme": "setAutoCommit", "acc": 1, "dsc": "(Z)V", "exs": ["java/sql/SQLException"]}, {"nme": "getBusyTimeout", "acc": 1, "dsc": "()I"}, {"nme": "setBusyTimeout", "acc": 1, "dsc": "(I)V", "exs": ["java/sql/SQLException"]}, {"nme": "setLimit", "acc": 1, "dsc": "(Lorg/sqlite/SQLiteLimits;I)V", "exs": ["java/sql/SQLException"]}, {"nme": "getLimit", "acc": 1, "dsc": "(Lorg/sqlite/SQLiteLimits;)V", "exs": ["java/sql/SQLException"]}, {"nme": "isClosed", "acc": 1, "dsc": "()Z", "exs": ["java/sql/SQLException"]}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "checkOpen", "acc": 4, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "libversion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "commit", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "rollback", "acc": 1, "dsc": "()V", "exs": ["java/sql/SQLException"]}, {"nme": "addUpdateListener", "acc": 1, "dsc": "(Lorg/sqlite/SQLiteUpdateListener;)V"}, {"nme": "removeUpdateListener", "acc": 1, "dsc": "(Lorg/sqlite/SQLiteUpdateListener;)V"}, {"nme": "addCommitListener", "acc": 1, "dsc": "(Lorg/sqlite/SQLiteCommitListener;)V"}, {"nme": "removeCommitListener", "acc": 1, "dsc": "(Lorg/sqlite/SQLiteCommitListener;)V"}, {"nme": "extractPragmasFromFilename", "acc": 12, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Ljava/util/Properties;)Ljava/lang/String;", "exs": ["java/sql/SQLException"]}, {"nme": "transactionPrefix", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "serialize", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[B", "exs": ["java/sql/SQLException"]}, {"nme": "deserialize", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[B)V", "exs": ["java/sql/SQLException"]}], "flds": [{"acc": 26, "nme": "RESOURCE_NAME_PREFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": ":resource:"}, {"acc": 18, "nme": "db", "dsc": "Lorg/sqlite/core/DB;"}, {"acc": 2, "nme": "meta", "dsc": "Lorg/sqlite/core/CoreDatabaseMetaData;"}, {"acc": 18, "nme": "connectionConfig", "dsc": "Lorg/sqlite/SQLiteConnectionConfig;"}, {"acc": 2, "nme": "currentTransactionMode", "dsc": "Lorg/sqlite/SQLiteConfig$TransactionMode;"}, {"acc": 2, "nme": "firstStatementExecuted", "dsc": "Z"}]}, "org/sqlite/SQLiteConfig$LockingMode.class": {"ver": 52, "acc": 16433, "nme": "org/sqlite/SQLiteConfig$LockingMode", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lorg/sqlite/SQLiteConfig$LockingMode;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/sqlite/SQLiteConfig$LockingMode;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "getValue", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "NORMAL", "dsc": "Lorg/sqlite/SQLiteConfig$LockingMode;"}, {"acc": 16409, "nme": "EXCLUSIVE", "dsc": "Lorg/sqlite/SQLiteConfig$LockingMode;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lorg/sqlite/SQLiteConfig$LockingMode;"}]}, "org/sqlite/SQLiteConfig$Encoding.class": {"ver": 52, "acc": 16433, "nme": "org/sqlite/SQLiteConfig$Encoding", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lorg/sqlite/SQLiteConfig$Encoding;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/sqlite/SQLiteConfig$Encoding;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(Ljava/lang/String;ILorg/sqlite/SQLiteConfig$Encoding;)V", "sig": "(Lorg/sqlite/SQLiteConfig$Encoding;)V"}, {"nme": "getValue", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getEncoding", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/sqlite/SQLiteConfig$Encoding;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "UTF8", "dsc": "Lorg/sqlite/SQLiteConfig$Encoding;"}, {"acc": 16409, "nme": "UTF16", "dsc": "Lorg/sqlite/SQLiteConfig$Encoding;"}, {"acc": 16409, "nme": "UTF16_LITTLE_ENDIAN", "dsc": "Lorg/sqlite/SQLiteConfig$Encoding;"}, {"acc": 16409, "nme": "UTF16_BIG_ENDIAN", "dsc": "Lorg/sqlite/SQLiteConfig$Encoding;"}, {"acc": 16409, "nme": "UTF_8", "dsc": "Lorg/sqlite/SQLiteConfig$Encoding;"}, {"acc": 16409, "nme": "UTF_16", "dsc": "Lorg/sqlite/SQLiteConfig$Encoding;"}, {"acc": 16409, "nme": "UTF_16LE", "dsc": "Lorg/sqlite/SQLiteConfig$Encoding;"}, {"acc": 16409, "nme": "UTF_16BE", "dsc": "Lorg/sqlite/SQLiteConfig$Encoding;"}, {"acc": 17, "nme": "typeName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lorg/sqlite/SQLiteConfig$Encoding;"}]}, "org/sqlite/SQLiteConfig$TransactionMode.class": {"ver": 52, "acc": 16433, "nme": "org/sqlite/SQLiteConfig$TransactionMode", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lorg/sqlite/SQLiteConfig$TransactionMode;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/sqlite/SQLiteConfig$TransactionMode;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "getValue", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getMode", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/sqlite/SQLiteConfig$TransactionMode;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "DEFERRED", "dsc": "Lorg/sqlite/SQLiteConfig$TransactionMode;"}, {"acc": 16409, "nme": "IMMEDIATE", "dsc": "Lorg/sqlite/SQLiteConfig$TransactionMode;"}, {"acc": 16409, "nme": "EXCLUSIVE", "dsc": "Lorg/sqlite/SQLiteConfig$TransactionMode;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lorg/sqlite/SQLiteConfig$TransactionMode;"}]}, "META-INF/versions/9/module-info.class": {"ver": 53, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "org/sqlite/jdbc4/JDBC4PooledConnection.class": {"ver": 52, "acc": 1057, "nme": "org/sqlite/jdbc4/JDBC4PooledConnection", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "addStatementEventListener", "acc": 1, "dsc": "(Ljavax/sql/StatementEventListener;)V"}, {"nme": "removeStatementEventListener", "acc": 1, "dsc": "(Ljavax/sql/StatementEventListener;)V"}], "flds": []}, "org/sqlite/date/FastDatePrinter$TimeZoneNumberRule.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/date/FastDatePrinter$TimeZoneNumberRule", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(ZZ)V"}, {"nme": "estimateLength", "acc": 1, "dsc": "()I"}, {"nme": "appendTo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;<PERSON><PERSON><PERSON>/util/Calendar;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "INSTANCE_COLON", "dsc": "Lorg/sqlite/date/FastDatePrinter$TimeZoneNumberRule;"}, {"acc": 24, "nme": "INSTANCE_NO_COLON", "dsc": "Lorg/sqlite/date/FastDatePrinter$TimeZoneNumberRule;"}, {"acc": 24, "nme": "INSTANCE_ISO_8601", "dsc": "Lorg/sqlite/date/FastDatePrinter$TimeZoneNumberRule;"}, {"acc": 16, "nme": "mColon", "dsc": "Z"}, {"acc": 16, "nme": "mISO8601", "dsc": "Z"}]}, "org/sqlite/date/FastDatePrinter$Iso8601_Rule.class": {"ver": 52, "acc": 32, "nme": "org/sqlite/date/FastDatePrinter$Iso8601_Rule", "super": "java/lang/Object", "mthds": [{"nme": "getRule", "acc": 8, "dsc": "(I)Lorg/sqlite/date/FastDatePrinter$Iso8601_Rule;"}, {"nme": "<init>", "acc": 0, "dsc": "(I)V"}, {"nme": "estimateLength", "acc": 1, "dsc": "()I"}, {"nme": "appendTo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;<PERSON><PERSON><PERSON>/util/Calendar;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "ISO8601_HOURS", "dsc": "Lorg/sqlite/date/FastDatePrinter$Iso8601_Rule;"}, {"acc": 24, "nme": "ISO8601_HOURS_MINUTES", "dsc": "Lorg/sqlite/date/FastDatePrinter$Iso8601_Rule;"}, {"acc": 24, "nme": "ISO8601_HOURS_COLON_MINUTES", "dsc": "Lorg/sqlite/date/FastDatePrinter$Iso8601_Rule;"}, {"acc": 16, "nme": "length", "dsc": "I"}]}, "org/sqlite/core/SafeStmtPtr$SafePtrFunction.class": {"ver": 52, "acc": 1537, "nme": "org/sqlite/core/SafeStmtPtr$SafePtrFunction", "super": "java/lang/Object", "mthds": [{"nme": "run", "acc": 1025, "dsc": "(Lorg/sqlite/core/DB;J)Ljava/lang/Object;", "sig": "(Lorg/sqlite/core/DB;J)TT;^TE;", "exs": ["java/lang/Throwable"]}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/FunctionalInterface;"}]}, "org/sqlite/SQLiteConfig$SynchronousMode.class": {"ver": 52, "acc": 16433, "nme": "org/sqlite/SQLiteConfig$SynchronousMode", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lorg/sqlite/SQLiteConfig$SynchronousMode;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/sqlite/SQLiteConfig$SynchronousMode;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "getValue", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "OFF", "dsc": "Lorg/sqlite/SQLiteConfig$SynchronousMode;"}, {"acc": 16409, "nme": "NORMAL", "dsc": "Lorg/sqlite/SQLiteConfig$SynchronousMode;"}, {"acc": 16409, "nme": "FULL", "dsc": "Lorg/sqlite/SQLiteConfig$SynchronousMode;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lorg/sqlite/SQLiteConfig$SynchronousMode;"}]}, "org/sqlite/ExtendedCommand$RestoreCommand.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/ExtendedCommand$RestoreCommand", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "parse", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/sqlite/ExtendedCommand$RestoreCommand;", "exs": ["java/sql/SQLException"]}, {"nme": "execute", "acc": 1, "dsc": "(Lorg/sqlite/core/DB;)V", "exs": ["java/sql/SQLException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 17, "nme": "targetDB", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 17, "nme": "srcFile", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 10, "nme": "restoreCmd", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}]}, "org/sqlite/ExtendedCommand$BackupCommand.class": {"ver": 52, "acc": 33, "nme": "org/sqlite/ExtendedCommand$BackupCommand", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "parse", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/sqlite/ExtendedCommand$BackupCommand;", "exs": ["java/sql/SQLException"]}, {"nme": "execute", "acc": 1, "dsc": "(Lorg/sqlite/core/DB;)V", "exs": ["java/sql/SQLException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 17, "nme": "srcDB", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 17, "nme": "destFile", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 10, "nme": "backupCmd", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}]}, "org/sqlite/core/SafeStmtPtr$SafePtrDoubleFunction.class": {"ver": 52, "acc": 1537, "nme": "org/sqlite/core/SafeStmtPtr$SafePtrDoubleFunction", "super": "java/lang/Object", "mthds": [{"nme": "run", "acc": 1025, "dsc": "(Lorg/sqlite/core/DB;J)D", "sig": "(Lorg/sqlite/core/DB;J)D^TE;", "exs": ["java/lang/Throwable"]}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/FunctionalInterface;"}]}}}}