package shyrcs.extrastoragehook.security;

import java.security.MessageDigest;
import java.util.Base64;

/**
 * License validation system
 */
public class LicenseValidator {
    
    private static final String EXPECTED_HASH = "YOUR_LICENSE_HASH_HERE";
    private static final String SALT = "SbMagicHook_2024";
    
    /**
     * Validate license key
     */
    public static boolean validateLicense(String licenseKey) {
        try {
            if (licenseKey == null || licenseKey.trim().isEmpty()) {
                return false;
            }
            
            // Generate hash from license key + salt
            String combined = licenseKey + SALT;
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(combined.getBytes("UTF-8"));
            String hashString = Base64.getEncoder().encodeToString(hash);
            
            // Compare with expected hash
            return EXPECTED_HASH.equals(hashString);
            
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Get hardware ID for binding
     */
    public static String getHardwareId() {
        try {
            String os = System.getProperty("os.name");
            String arch = System.getProperty("os.arch");
            String user = System.getProperty("user.name");
            
            String combined = os + arch + user;
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(combined.getBytes("UTF-8"));
            
            StringBuilder sb = new StringBuilder();
            for (byte b : hash) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
            
        } catch (Exception e) {
            return "unknown";
        }
    }
    
    /**
     * Validate license with hardware binding
     */
    public static boolean validateLicenseWithHWID(String licenseKey, String expectedHWID) {
        if (!validateLicense(licenseKey)) {
            return false;
        }
        
        String currentHWID = getHardwareId();
        return expectedHWID.equals(currentHWID);
    }
}
