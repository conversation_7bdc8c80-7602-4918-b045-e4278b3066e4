package shyrcs.extrastoragehook.discord;

import net.dv8tion.jda.api.events.interaction.component.ButtonInteractionEvent;
import net.dv8tion.jda.api.hooks.ListenerAdapter;
import shyrcs.extrastoragehook.application.Library;
import shyrcs.extrastoragehook.discord.commands.CommandStorage;

import java.util.List;

/**
 * Listener để xử lý button interactions cho storage pagination
 */
public class ButtonListener extends ListenerAdapter {
    
    @Override
    public void onButtonInteraction(ButtonInteractionEvent event) {
        String buttonId = event.getComponentId();

        // Kiểm tra channel permissions
        if (!isChannelAllowed(event)) {
            return; // Bỏ thông báo lỗi channel
        }

        // Chỉ xử lý storage buttons
        if (!buttonId.startsWith("storage_")) {
            return;
        }

        // Delegate to CommandStorage
        CommandStorage.handleButtonInteraction(event);
    }
    


    /**
     * Kiểm tra xem channel có được phép sử dụng bot không
     */
    private boolean isChannelAllowed(ButtonInteractionEvent event) {
        List<String> whitelistedChannels = Library.config.getWhitelistedChannels();

        // Nếu không có whitelist thì cho phép tất cả
        if (whitelistedChannels == null || whitelistedChannels.isEmpty()) {
            return true;
        }

        String channelId = event.getChannel().getId();
        return whitelistedChannels.contains(channelId);
    }
}
