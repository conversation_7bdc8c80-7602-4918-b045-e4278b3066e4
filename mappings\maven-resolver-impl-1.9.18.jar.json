{"md5": "0204f7788eb26200ff419d4abe21072b", "sha2": "e928b128d1e52e6299f94431ce3df74647bc8c26", "sha256": "6bb9c90d007098004749c867da2eaf5785fc1139907718749c1097bdb2929bf8", "contents": {"classes": {"org/eclipse/aether/internal/impl/collect/DataPool$WeakInternPool.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/internal/impl/collect/DataPool$WeakInternPool", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "get", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(TK;)TV;"}, {"nme": "intern", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(TK;TV;)TV;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/eclipse/aether/internal/impl/collect/DataPool$1;)V"}], "flds": [{"acc": 18, "nme": "map", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<TK;Ljava/lang/ref/WeakReference<TV;>;>;"}]}, "org/eclipse/aether/internal/impl/Utils.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/internal/impl/Utils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "sortMetadataGeneratorFactories", "acc": 9, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;<PERSON><PERSON><PERSON>/util/Collection;)Lorg/eclipse/aether/internal/impl/PrioritizedComponents;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection<+Lorg/eclipse/aether/impl/MetadataGeneratorFactory;>;)Lorg/eclipse/aether/internal/impl/PrioritizedComponents<Lorg/eclipse/aether/impl/MetadataGeneratorFactory;>;"}, {"nme": "prepareMetadata", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/util/List;)<PERSON>java/util/List;", "sig": "(Ljava/util/List<+Lorg/eclipse/aether/impl/MetadataGenerator;>;Ljava/util/List<+Lorg/eclipse/aether/artifact/Artifact;>;)Ljava/util/List<Lorg/eclipse/aether/metadata/Metadata;>;"}, {"nme": "finishMetadata", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/util/List;)<PERSON>java/util/List;", "sig": "(Ljava/util/List<+Lorg/eclipse/aether/impl/MetadataGenerator;>;Ljava/util/List<+Lorg/eclipse/aether/artifact/Artifact;>;)Ljava/util/List<Lorg/eclipse/aether/metadata/Metadata;>;"}, {"nme": "combine", "acc": 9, "dsc": "(Lja<PERSON>/util/Collection;Ljava/util/Collection;)Ljava/util/List;", "sig": "<T:Ljava/lang/Object;>(Ljava/util/Collection<+TT;>;Ljava/util/Collection<+TT;>;)Ljava/util/List<TT;>;"}, {"nme": "getPolicy", "acc": 9, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/RemoteRepository;)I"}, {"nme": "getPolicy", "acc": 9, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/RemoteRepository;)I"}, {"nme": "appendClassLoader", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "checkOffline", "acc": 9, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/impl/OfflineController;Lorg/eclipse/aether/repository/RemoteRepository;)V", "exs": ["org/eclipse/aether/transfer/RepositoryOfflineException"]}], "flds": []}, "org/eclipse/aether/impl/ArtifactDescriptorReader.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/impl/ArtifactDescriptorReader", "super": "java/lang/Object", "mthds": [{"nme": "readArtifactDescriptor", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/ArtifactDescriptorRequest;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;", "exs": ["org/eclipse/aether/resolution/ArtifactDescriptorException"]}], "flds": []}, "org/eclipse/aether/internal/impl/ArtifactRequestBuilder.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/internal/impl/ArtifactRequestBuilder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/RequestTrace;)V"}, {"nme": "getRequests", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/resolution/ArtifactRequest;>;"}, {"nme": "visitEnter", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Z"}, {"nme": "visitLeave", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Z"}], "flds": [{"acc": 18, "nme": "trace", "dsc": "Lorg/eclipse/aether/RequestTrace;"}, {"acc": 18, "nme": "requests", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/resolution/ArtifactRequest;>;"}]}, "org/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper$NeverDependencyResolutionSkipper.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper$NeverDependencyResolutionSkipper", "super": "org/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "skipResolution", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;<PERSON>ja<PERSON>/util/List;)Z", "sig": "(Lorg/eclipse/aether/graph/DependencyNode;Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;)Z"}, {"nme": "cache", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;Lja<PERSON>/util/List;)V", "sig": "(Lorg/eclipse/aether/graph/DependencyNode;Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;)V"}, {"nme": "close", "acc": 1, "dsc": "()V"}, {"nme": "access$000", "acc": 4104, "dsc": "()Lorg/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "INSTANCE", "dsc": "Lorg/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper;"}]}, "org/eclipse/aether/internal/impl/resolution/TrustedChecksumsArtifactResolverPostProcessor.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/internal/impl/resolution/TrustedChecksumsArtifactResolverPostProcessor", "super": "org/eclipse/aether/internal/impl/resolution/ArtifactResolverPostProcessorSupport", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactorySelector;Ljava/util/Map;)V", "sig": "(Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactorySelector;Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/spi/checksums/TrustedChecksumsSource;>;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "doPostProcess", "acc": 4, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/List<Lorg/eclipse/aether/resolution/ArtifactResult;>;)V"}, {"nme": "recordArtifactChecksums", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/ArtifactResult;<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/ArtifactResult;Ljava/util/List<Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;>;)V"}, {"nme": "validateArtifactChecksums", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/ArtifactResult;<PERSON><PERSON><PERSON>/util/List;Z)Z", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/ArtifactResult;Ljava/util/List<Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;>;Z)Z"}, {"nme": "lambda$doPostProcess$0", "acc": 4098, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "trustedChecksums"}, {"acc": 26, "nme": "CONF_NAME_CHECKSUM_ALGORITHMS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "checksumAlgorithms"}, {"acc": 26, "nme": "DEFAULT_CHECKSUM_ALGORITHMS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "SHA-1"}, {"acc": 26, "nme": "CONF_NAME_FAIL_IF_MISSING", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "failIfMissing"}, {"acc": 26, "nme": "CONF_NAME_SNAPSHOTS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "snapshots"}, {"acc": 26, "nme": "CONF_NAME_RECORD", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "record"}, {"acc": 26, "nme": "CHECKSUM_ALGORITHMS_CACHE_KEY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "checksumAlgorithmFactorySelector", "dsc": "Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactorySelector;"}, {"acc": 18, "nme": "trustedChecksumsSources", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/spi/checksums/TrustedChecksumsSource;>;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;", "vals": ["value", "trustedChecksums"]}]}, "org/eclipse/aether/internal/impl/resolution/ArtifactResolverPostProcessorSupport.class": {"ver": 52, "acc": 1057, "nme": "org/eclipse/aether/internal/impl/resolution/ArtifactResolverPostProcessorSupport", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "postProcess", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/List<Lorg/eclipse/aether/resolution/ArtifactResult;>;)V"}, {"nme": "doPostProcess", "acc": 1028, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/List<Lorg/eclipse/aether/resolution/ArtifactResult;>;)V"}, {"nme": "configPropKey", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "isEnabled", "acc": 4, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Z"}], "flds": [{"acc": 26, "nme": "CONFIG_PROP_PREFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.artifactResolver.postProcessor."}, {"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/eclipse/aether/internal/impl/DefaultDeployer$EventCatapult.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/internal/impl/DefaultDeployer$EventCatapult", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/impl/RepositoryEventDispatcher;)V"}, {"nme": "getSession", "acc": 1, "dsc": "()Lorg/eclipse/aether/RepositorySystemSession;"}, {"nme": "getTrace", "acc": 1, "dsc": "()Lorg/eclipse/aether/RequestTrace;"}, {"nme": "artifactDeploying", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/io/File;)V"}, {"nme": "artifactDeployed", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/io/File;Lorg/eclipse/aether/transfer/ArtifactTransferException;)V"}, {"nme": "metadataDeploying", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;Ljava/io/File;)V"}, {"nme": "metadataDeployed", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;Ljava/io/File;Ljava/lang/Exception;)V"}], "flds": [{"acc": 18, "nme": "session", "dsc": "Lorg/eclipse/aether/RepositorySystemSession;"}, {"acc": 18, "nme": "trace", "dsc": "Lorg/eclipse/aether/RequestTrace;"}, {"acc": 18, "nme": "repository", "dsc": "Lorg/eclipse/aether/repository/RemoteRepository;"}, {"acc": 18, "nme": "dispatcher", "dsc": "Lorg/eclipse/aether/impl/RepositoryEventDispatcher;"}]}, "org/eclipse/aether/internal/impl/LocalPathComposer.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/internal/impl/LocalPathComposer", "super": "java/lang/Object", "mthds": [{"nme": "getPathForArtifact", "acc": 1025, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Z)Ljava/lang/String;"}, {"nme": "getPathForMetadata", "acc": 1025, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;Ljava/lang/String;)Ljava/lang/String;"}], "flds": []}, "org/eclipse/aether/impl/Deployer.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/impl/Deployer", "super": "java/lang/Object", "mthds": [{"nme": "deploy", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/deployment/DeployRequest;)Lorg/eclipse/aether/deployment/DeployResult;", "exs": ["org/eclipse/aether/deployment/DeploymentException"]}], "flds": []}, "org/eclipse/aether/impl/guice/AetherModule$1.class": {"ver": 52, "acc": 4128, "nme": "org/eclipse/aether/impl/guice/AetherModule$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/internal/impl/collect/DefaultVersionFilterContext.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/internal/impl/collect/DefaultVersionFilterContext", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)V"}, {"nme": "set", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/Dependency;Lorg/eclipse/aether/resolution/VersionRangeResult;)V"}, {"nme": "get", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/version/Version;>;"}, {"nme": "getSession", "acc": 1, "dsc": "()Lorg/eclipse/aether/RepositorySystemSession;"}, {"nme": "getDependency", "acc": 1, "dsc": "()Lorg/eclipse/aether/graph/Dependency;"}, {"nme": "getVersionConstraint", "acc": 1, "dsc": "()Lorg/eclipse/aether/version/VersionConstraint;"}, {"nme": "getCount", "acc": 1, "dsc": "()I"}, {"nme": "getRepository", "acc": 1, "dsc": "(Lorg/eclipse/aether/version/Version;)Lorg/eclipse/aether/repository/ArtifactRepository;"}, {"nme": "getRepositories", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"nme": "iterator", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Iterator;", "sig": "()Ljava/util/Iterator<Lorg/eclipse/aether/version/Version;>;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "session", "dsc": "Lorg/eclipse/aether/RepositorySystemSession;"}, {"acc": 2, "nme": "dependency", "dsc": "Lorg/eclipse/aether/graph/Dependency;"}, {"acc": 0, "nme": "result", "dsc": "Lorg/eclipse/aether/resolution/VersionRangeResult;"}, {"acc": 2, "nme": "versions", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/version/Version;>;"}]}, "org/eclipse/aether/internal/impl/LocalPathPrefixComposerFactorySupport$LocalPathPrefixComposerSupport.class": {"ver": 52, "acc": 1057, "nme": "org/eclipse/aether/internal/impl/LocalPathPrefixComposerFactorySupport$LocalPathPrefixComposerSupport", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Z<PERSON><PERSON><PERSON>/lang/String;ZLjava/lang/String;ZZZLjava/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getPathPrefixForLocalArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lja<PERSON>/lang/String;"}, {"nme": "getPathPrefixForRemoteArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/RemoteRepository;)Ljava/lang/String;"}, {"nme": "getPathPrefixForLocalMetadata", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;)Ljava/lang/String;"}, {"nme": "getPathPrefixForRemoteMetadata", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/RemoteRepository;)Ljava/lang/String;"}, {"nme": "isSnapshot", "acc": 4, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;)Z"}], "flds": [{"acc": 20, "nme": "split", "dsc": "Z"}, {"acc": 20, "nme": "localPrefix", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 20, "nme": "splitLocal", "dsc": "Z"}, {"acc": 20, "nme": "remotePrefix", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 20, "nme": "splitRemote", "dsc": "Z"}, {"acc": 20, "nme": "splitRemoteRepository", "dsc": "Z"}, {"acc": 20, "nme": "splitRemoteRepositoryLast", "dsc": "Z"}, {"acc": 20, "nme": "releasesPrefix", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 20, "nme": "snapshotsPrefix", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/eclipse/aether/internal/impl/collect/DataPool$Constraint.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/internal/impl/collect/DataPool$Constraint", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/resolution/VersionRangeResult;)V"}, {"nme": "toResult", "acc": 0, "dsc": "(Lorg/eclipse/aether/resolution/VersionRangeRequest;)Lorg/eclipse/aether/resolution/VersionRangeResult;"}], "flds": [{"acc": 16, "nme": "repositories", "dsc": "[Lorg/eclipse/aether/internal/impl/collect/DataPool$Constraint$VersionRepo;"}, {"acc": 16, "nme": "versionConstraint", "dsc": "Lorg/eclipse/aether/version/VersionConstraint;"}]}, "org/eclipse/aether/internal/impl/DefaultUpdateCheckManager$1.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/internal/impl/DefaultUpdateCheckManager$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "org/eclipse/aether/internal/impl/AbstractChecksumPolicy.class": {"ver": 52, "acc": 1056, "nme": "org/eclipse/aether/internal/impl/AbstractChecksumPolicy", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Lorg/eclipse/aether/transfer/TransferResource;)V"}, {"nme": "onChecksumMatch", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/eclipse/aether/spi/connector/checksum/ChecksumPolicy$ChecksumKind;)Z"}, {"nme": "onChecksumMismatch", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/eclipse/aether/spi/connector/checksum/ChecksumPolicy$ChecksumKind;Lorg/eclipse/aether/transfer/ChecksumFailureException;)V", "exs": ["org/eclipse/aether/transfer/ChecksumFailureException"]}, {"nme": "onChecksumError", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/eclipse/aether/spi/connector/checksum/ChecksumPolicy$ChecksumKind;Lorg/eclipse/aether/transfer/ChecksumFailureException;)V", "exs": ["org/eclipse/aether/transfer/ChecksumFailureException"]}, {"nme": "onNoMoreChecksums", "acc": 1, "dsc": "()V", "exs": ["org/eclipse/aether/transfer/ChecksumFailureException"]}, {"nme": "onTransferRetry", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 20, "nme": "logger", "dsc": "Lorg/slf4j/Logger;"}, {"acc": 20, "nme": "resource", "dsc": "Lorg/eclipse/aether/transfer/TransferResource;"}]}, "org/eclipse/aether/impl/VersionRangeResolver.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/impl/VersionRangeResolver", "super": "java/lang/Object", "mthds": [{"nme": "resolveVersionRange", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/VersionRangeRequest;)Lorg/eclipse/aether/resolution/VersionRangeResult;", "exs": ["org/eclipse/aether/resolution/VersionRangeResolutionException"]}], "flds": []}, "org/eclipse/aether/impl/guice/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/impl/guice/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/internal/impl/collect/DataPool$ConstraintKey.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/internal/impl/collect/DataPool$ConstraintKey", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/resolution/VersionRangeRequest;)V"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "equals", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/util/List;)Z", "sig": "(Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "artifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}, {"acc": 18, "nme": "repositories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"acc": 18, "nme": "hashCode", "dsc": "I"}]}, "org/eclipse/aether/internal/impl/checksum/MessageDigestChecksumAlgorithmFactorySupport.class": {"ver": 52, "acc": 1057, "nme": "org/eclipse/aether/internal/impl/checksum/MessageDigestChecksumAlgorithmFactorySupport", "super": "org/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactorySupport", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getAlgorithm", "acc": 1, "dsc": "()Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithm;"}], "flds": []}, "org/eclipse/aether/impl/UpdateCheck.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/impl/UpdateCheck", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getLocalLastUpdated", "acc": 1, "dsc": "()J"}, {"nme": "setLocalLastUpdated", "acc": 1, "dsc": "(J)Lorg/eclipse/aether/impl/UpdateCheck;", "sig": "(J)Lorg/eclipse/aether/impl/UpdateCheck<TT;TE;>;"}, {"nme": "getItem", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}, {"nme": "setItem", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/eclipse/aether/impl/UpdateCheck;", "sig": "(TT;)Lorg/eclipse/aether/impl/UpdateCheck<TT;TE;>;"}, {"nme": "getFile", "acc": 1, "dsc": "()Ljava/io/File;"}, {"nme": "setFile", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)Lorg/eclipse/aether/impl/UpdateCheck;", "sig": "(Ljava/io/File;)Lorg/eclipse/aether/impl/UpdateCheck<TT;TE;>;"}, {"nme": "isFileValid", "acc": 1, "dsc": "()Z"}, {"nme": "setFileValid", "acc": 1, "dsc": "(Z)Lorg/eclipse/aether/impl/UpdateCheck;", "sig": "(Z)Lorg/eclipse/aether/impl/UpdateCheck<TT;TE;>;"}, {"nme": "getPolicy", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setPolicy", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/impl/UpdateCheck;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/impl/UpdateCheck<TT;TE;>;"}, {"nme": "getRepository", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/RemoteRepository;"}, {"nme": "setRepository", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/impl/UpdateCheck;", "sig": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/impl/UpdateCheck<TT;TE;>;"}, {"nme": "getAuthoritativeRepository", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/RemoteRepository;"}, {"nme": "setAuthoritativeRepository", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/impl/UpdateCheck;", "sig": "(Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/impl/UpdateCheck<TT;TE;>;"}, {"nme": "isRequired", "acc": 1, "dsc": "()Z"}, {"nme": "setRequired", "acc": 1, "dsc": "(Z)Lorg/eclipse/aether/impl/UpdateCheck;", "sig": "(Z)Lorg/eclipse/aether/impl/UpdateCheck<TT;TE;>;"}, {"nme": "getException", "acc": 1, "dsc": "()Lorg/eclipse/aether/RepositoryException;", "sig": "()TE;"}, {"nme": "setException", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryException;)Lorg/eclipse/aether/impl/UpdateCheck;", "sig": "(TE;)Lorg/eclipse/aether/impl/UpdateCheck<TT;TE;>;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "localLastUpdated", "dsc": "J"}, {"acc": 2, "nme": "item", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;", "sig": "TT;"}, {"acc": 2, "nme": "file", "dsc": "Ljava/io/File;"}, {"acc": 2, "nme": "fileValid", "dsc": "Z"}, {"acc": 2, "nme": "policy", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "repository", "dsc": "Lorg/eclipse/aether/repository/RemoteRepository;"}, {"acc": 2, "nme": "authoritativeRepository", "dsc": "Lorg/eclipse/aether/repository/RemoteRepository;"}, {"acc": 2, "nme": "required", "dsc": "Z"}, {"acc": 2, "nme": "exception", "dsc": "Lorg/eclipse/aether/RepositoryException;", "sig": "TE;"}]}, "org/eclipse/aether/internal/impl/SafeTransferListener.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/internal/impl/SafeTransferListener", "super": "org/eclipse/aether/transfer/AbstractTransferListener", "mthds": [{"nme": "wrap", "acc": 9, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lorg/eclipse/aether/transfer/TransferListener;"}, {"nme": "<init>", "acc": 4, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(Lorg/eclipse/aether/transfer/TransferListener;)V"}, {"nme": "logError", "acc": 2, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;Lja<PERSON>/lang/Throwable;)V"}, {"nme": "transferInitiated", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V", "exs": ["org/eclipse/aether/transfer/TransferCancelledException"]}, {"nme": "transferStarted", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V", "exs": ["org/eclipse/aether/transfer/TransferCancelledException"]}, {"nme": "transferProgressed", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V", "exs": ["org/eclipse/aether/transfer/TransferCancelledException"]}, {"nme": "transferCorrupted", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V", "exs": ["org/eclipse/aether/transfer/TransferCancelledException"]}, {"nme": "transferSucceeded", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V"}, {"nme": "transferFailed", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "LOGGER", "dsc": "Lorg/slf4j/Logger;"}, {"acc": 18, "nme": "listener", "dsc": "Lorg/eclipse/aether/transfer/TransferListener;"}]}, "org/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper$DefaultDependencyResolutionSkipper$CoordinateManager.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper$DefaultDependencyResolutionSkipper$CoordinateManager", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getCoordinate", "acc": 0, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Lorg/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper$DefaultDependencyResolutionSkipper$Coordinate;"}, {"nme": "createCoordinate", "acc": 0, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;I)Lorg/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper$DefaultDependencyResolutionSkipper$Coordinate;"}, {"nme": "updateLeftmost", "acc": 0, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)V"}, {"nme": "isLeftmost", "acc": 0, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;<PERSON>ja<PERSON>/util/List;)Z", "sig": "(Lorg/eclipse/aether/graph/DependencyNode;Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;)Z"}, {"nme": "lambda$createCoordinate$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Integer;)Ljava/util/concurrent/atomic/AtomicInteger;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper$1;)V"}], "flds": [{"acc": 18, "nme": "sequenceGen", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Integer;Ljava/util/concurrent/atomic/AtomicInteger;>;"}, {"acc": 18, "nme": "coordinateMap", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Lorg/eclipse/aether/graph/DependencyNode;Lorg/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper$DefaultDependencyResolutionSkipper$Coordinate;>;"}, {"acc": 18, "nme": "leftmostCoordinates", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper$DefaultDependencyResolutionSkipper$Coordinate;>;"}]}, "org/eclipse/aether/internal/impl/checksum/SummaryFileTrustedChecksumsSource$1.class": {"ver": 52, "acc": 4128, "nme": "org/eclipse/aether/internal/impl/checksum/SummaryFileTrustedChecksumsSource$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper$DefaultDependencyResolutionSkipper$Coordinate.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper$DefaultDependencyResolutionSkipper$Coordinate", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(II)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 0, "nme": "depth", "dsc": "I"}, {"acc": 0, "nme": "sequence", "dsc": "I"}]}, "org/eclipse/aether/internal/impl/DefaultDeployer$MetadataUploadListener.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/internal/impl/DefaultDeployer$MetadataUploadListener", "super": "org/eclipse/aether/internal/impl/SafeTransferListener", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/internal/impl/DefaultDeployer$EventCatapult;Lorg/eclipse/aether/spi/connector/MetadataUpload;)V"}, {"nme": "transferInitiated", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V", "exs": ["org/eclipse/aether/transfer/TransferCancelledException"]}, {"nme": "transferFailed", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V"}, {"nme": "transferSucceeded", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V"}], "flds": [{"acc": 18, "nme": "catapult", "dsc": "Lorg/eclipse/aether/internal/impl/DefaultDeployer$EventCatapult;"}, {"acc": 18, "nme": "transfer", "dsc": "Lorg/eclipse/aether/spi/connector/MetadataUpload;"}]}, "org/eclipse/aether/internal/impl/SimpleLocalRepositoryManager.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/internal/impl/SimpleLocalRepositoryManager", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lja<PERSON>/io/File;Ljava/lang/String;Lorg/eclipse/aether/internal/impl/LocalPathComposer;)V"}, {"nme": "getRepository", "acc": 1, "dsc": "()Lorg/eclipse/aether/repository/LocalRepository;"}, {"nme": "getPathForLocalArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lja<PERSON>/lang/String;"}, {"nme": "getPathForRemoteArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "getPathForLocalMetadata", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;)Ljava/lang/String;"}, {"nme": "getPathForRemoteMetadata", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "getRepositoryKey", "acc": 4, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "find", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/LocalArtifactRequest;)Lorg/eclipse/aether/repository/LocalArtifactResult;"}, {"nme": "add", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/LocalArtifactRegistration;)V"}, {"nme": "find", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/LocalMetadataRequest;)Lorg/eclipse/aether/repository/LocalMetadataResult;"}, {"nme": "add", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/LocalMetadataRegistration;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "repository", "dsc": "Lorg/eclipse/aether/repository/LocalRepository;"}, {"acc": 18, "nme": "localPathComposer", "dsc": "Lorg/eclipse/aether/internal/impl/LocalPathComposer;"}]}, "org/eclipse/aether/internal/impl/filter/DefaultRemoteRepositoryFilterManager.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/internal/impl/filter/DefaultRemoteRepositoryFilterManager", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilterSource;>;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "getRemoteRepositoryFilter", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter;"}, {"nme": "lambda$getRemoteRepositoryFilter$0", "acc": 4098, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "INSTANCE_KEY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "sources", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilterSource;>;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;"}]}, "org/eclipse/aether/internal/impl/slf4j/Slf4jLoggerFactory.class": {"ver": 52, "acc": 131105, "nme": "org/eclipse/aether/internal/impl/slf4j/Slf4jLoggerFactory", "super": "java/lang/Object", "mthds": [{"nme": "isSlf4jAvailable", "acc": 9, "dsc": "()Z"}, {"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 0, "dsc": "(Lorg/slf4j/ILoggerFactory;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "initService", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/locator/ServiceLocator;)V"}, {"nme": "setLoggerFactory", "acc": 1, "dsc": "(Lorg/slf4j/ILoggerFactory;)Lorg/eclipse/aether/internal/impl/slf4j/Slf4jLoggerFactory;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/spi/log/Logger;"}, {"nme": "getFactory", "acc": 2, "dsc": "()Lorg/slf4j/ILoggerFactory;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "AVAILABLE", "dsc": "Z"}, {"acc": 2, "nme": "factory", "dsc": "Lorg/slf4j/ILoggerFactory;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;", "vals": ["value", "slf4j"]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "org/eclipse/aether/impl/UpdatePolicyAnalyzer.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/impl/UpdatePolicyAnalyzer", "super": "java/lang/Object", "mthds": [{"nme": "getEffectiveUpdatePolicy", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "isUpdatedRequired", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;JLjava/lang/String;)Z"}], "flds": []}, "org/eclipse/aether/internal/impl/DefaultRepositoryLayoutProvider.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/internal/impl/DefaultRepositoryLayoutProvider", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(Ljava/util/Set<Lorg/eclipse/aether/spi/connector/layout/RepositoryLayoutFactory;>;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "initService", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/locator/ServiceLocator;)V"}, {"nme": "addRepositoryLayoutFactory", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/connector/layout/RepositoryLayoutFactory;)Lorg/eclipse/aether/internal/impl/DefaultRepositoryLayoutProvider;"}, {"nme": "setRepositoryLayoutFactories", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Lorg/eclipse/aether/internal/impl/DefaultRepositoryLayoutProvider;", "sig": "(Ljava/util/Collection<Lorg/eclipse/aether/spi/connector/layout/RepositoryLayoutFactory;>;)Lorg/eclipse/aether/internal/impl/DefaultRepositoryLayoutProvider;"}, {"nme": "newRepositoryLayout", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/spi/connector/layout/RepositoryLayout;", "exs": ["org/eclipse/aether/transfer/NoRepositoryLayoutException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "LOGGER", "dsc": "Lorg/slf4j/Logger;"}, {"acc": 2, "nme": "factories", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lorg/eclipse/aether/spi/connector/layout/RepositoryLayoutFactory;>;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;"}]}, "org/eclipse/aether/internal/impl/synccontext/named/NameMapper.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/internal/impl/synccontext/named/NameMapper", "super": "java/lang/Object", "mthds": [{"nme": "isFileSystemFriendly", "acc": 1025, "dsc": "()Z"}, {"nme": "nameLocks", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection;Ljava/util/Collection;)Ljava/util/Collection;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection<+Lorg/eclipse/aether/artifact/Artifact;>;Ljava/util/Collection<+Lorg/eclipse/aether/metadata/Metadata;>;)Ljava/util/Collection<Ljava/lang/String;>;"}], "flds": []}, "org/eclipse/aether/internal/impl/DefaultRepositoryConnectorProvider.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/DefaultRepositoryConnectorProvider", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;Lorg/eclipse/aether/impl/RemoteRepositoryFilterManager;)V", "sig": "(L<PERSON><PERSON>/util/Set<Lorg/eclipse/aether/spi/connector/RepositoryConnectorFactory;>;Lorg/eclipse/aether/impl/RemoteRepositoryFilterManager;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "initService", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/locator/ServiceLocator;)V"}, {"nme": "addRepositoryConnectorFactory", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/connector/RepositoryConnectorFactory;)Lorg/eclipse/aether/internal/impl/DefaultRepositoryConnectorProvider;"}, {"nme": "setRepositoryConnectorFactories", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Lorg/eclipse/aether/internal/impl/DefaultRepositoryConnectorProvider;", "sig": "(Ljava/util/Collection<Lorg/eclipse/aether/spi/connector/RepositoryConnectorFactory;>;)Lorg/eclipse/aether/internal/impl/DefaultRepositoryConnectorProvider;"}, {"nme": "setRemoteRepositoryFilterManager", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/RemoteRepositoryFilterManager;)Lorg/eclipse/aether/internal/impl/DefaultRepositoryConnectorProvider;"}, {"nme": "newRepositoryConnector", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/spi/connector/RepositoryConnector;", "exs": ["org/eclipse/aether/transfer/NoRepositoryConnectorException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "LOGGER", "dsc": "Lorg/slf4j/Logger;"}, {"acc": 2, "nme": "connectorFactories", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lorg/eclipse/aether/spi/connector/RepositoryConnectorFactory;>;"}, {"acc": 2, "nme": "remoteRepositoryFilterManager", "dsc": "Lorg/eclipse/aether/impl/RemoteRepositoryFilterManager;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;"}]}, "org/eclipse/aether/internal/impl/DefaultUpdateCheckManager.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/DefaultUpdateCheckManager", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/internal/impl/TrackingFileManager;Lorg/eclipse/aether/impl/UpdatePolicyAnalyzer;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "initService", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/locator/ServiceLocator;)V"}, {"nme": "setTrackingFileManager", "acc": 1, "dsc": "(Lorg/eclipse/aether/internal/impl/TrackingFileManager;)Lorg/eclipse/aether/internal/impl/DefaultUpdateCheckManager;"}, {"nme": "setUpdatePolicyAnalyzer", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/UpdatePolicyAnalyzer;)Lorg/eclipse/aether/internal/impl/DefaultUpdateCheckManager;"}, {"nme": "checkArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/impl/UpdateCheck;)V", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/impl/UpdateCheck<Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/transfer/ArtifactTransferException;>;)V"}, {"nme": "getCacheFlag", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "newException", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/transfer/ArtifactTransferException;"}, {"nme": "checkMetadata", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/impl/UpdateCheck;)V", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/impl/UpdateCheck<Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/transfer/MetadataTransferException;>;)V"}, {"nme": "newException", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/transfer/MetadataTransferException;"}, {"nme": "getLastUpdated", "acc": 2, "dsc": "(L<PERSON><PERSON>/util/Properties;Ljava/lang/String;)J"}, {"nme": "getError", "acc": 2, "dsc": "(L<PERSON><PERSON>/util/Properties;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "getArtifactTouchFile", "acc": 2, "dsc": "(Lja<PERSON>/io/File;)Ljava/io/File;"}, {"nme": "getMetadataTouchFile", "acc": 2, "dsc": "(Lja<PERSON>/io/File;)Ljava/io/File;"}, {"nme": "getDataKey", "acc": 2, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Ljava/lang/String;"}, {"nme": "getTransferKey", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;)Ljava/lang/String;"}, {"nme": "getDataKey", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)Ljava/lang/String;"}, {"nme": "getTransferKey", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/io/File;Lorg/eclipse/aether/repository/RemoteRepository;)Ljava/lang/String;"}, {"nme": "getRepoKey", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;)Ljava/lang/String;"}, {"nme": "normalizeRepoUrl", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getUpdate<PERSON>ey", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/io/File;Lorg/eclipse/aether/repository/RemoteRepository;)Ljava/lang/String;"}, {"nme": "getSessionState", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)I"}, {"nme": "isAlreadyUpdated", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "setUpdated", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "isUpdatedRequired", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;JLjava/lang/String;)Z"}, {"nme": "read", "acc": 2, "dsc": "(Ljava/io/File;)Ljava/util/Properties;"}, {"nme": "touchArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/impl/UpdateCheck;)V", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/impl/UpdateCheck<Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/transfer/ArtifactTransferException;>;)V"}, {"nme": "hasErrors", "acc": 2, "dsc": "(Ljava/util/Properties;)Z"}, {"nme": "touchMetadata", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/impl/UpdateCheck;)V", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/impl/UpdateCheck<Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/transfer/MetadataTransferException;>;)V"}, {"nme": "write", "acc": 2, "dsc": "(L<PERSON><PERSON>/io/File;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Exception;)Ljava/util/Properties;"}, {"nme": "lambda$setUpdated$0", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "LOGGER", "dsc": "Lorg/slf4j/Logger;"}, {"acc": 2, "nme": "trackingFileManager", "dsc": "Lorg/eclipse/aether/internal/impl/TrackingFileManager;"}, {"acc": 2, "nme": "updatePolicyAnalyzer", "dsc": "Lorg/eclipse/aether/impl/UpdatePolicyAnalyzer;"}, {"acc": 26, "nme": "UPDATED_KEY_SUFFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": ".lastUpdated"}, {"acc": 26, "nme": "ERROR_KEY_SUFFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": ".error"}, {"acc": 26, "nme": "NOT_FOUND", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": ""}, {"acc": 24, "nme": "SESSION_CHECKS", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 24, "nme": "CONFIG_PROP_SESSION_STATE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.updateCheckManager.sessionState"}, {"acc": 26, "nme": "STATE_ENABLED", "dsc": "I", "val": 0}, {"acc": 26, "nme": "STATE_BYPASS", "dsc": "I", "val": 1}, {"acc": 26, "nme": "STATE_DISABLED", "dsc": "I", "val": 2}, {"acc": 26, "nme": "TS_NEVER", "dsc": "J", "val": 0}, {"acc": 26, "nme": "TS_UNKNOWN", "dsc": "J", "val": 1}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;"}]}, "org/eclipse/aether/internal/impl/DefaultMetadataResolver.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/DefaultMetadataResolver", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/RepositoryEventDispatcher;Lorg/eclipse/aether/impl/UpdateCheckManager;Lorg/eclipse/aether/impl/RepositoryConnectorProvider;Lorg/eclipse/aether/impl/RemoteRepositoryManager;Lorg/eclipse/aether/spi/synccontext/SyncContextFactory;Lorg/eclipse/aether/impl/OfflineController;Lorg/eclipse/aether/impl/RemoteRepositoryFilterManager;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "initService", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/locator/ServiceLocator;)V"}, {"nme": "setRepositoryEventDispatcher", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/RepositoryEventDispatcher;)Lorg/eclipse/aether/internal/impl/DefaultMetadataResolver;"}, {"nme": "setUpdateCheckManager", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/UpdateCheckManager;)Lorg/eclipse/aether/internal/impl/DefaultMetadataResolver;"}, {"nme": "setRepositoryConnectorProvider", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/RepositoryConnectorProvider;)Lorg/eclipse/aether/internal/impl/DefaultMetadataResolver;"}, {"nme": "setRemoteRepositoryManager", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/RemoteRepositoryManager;)Lorg/eclipse/aether/internal/impl/DefaultMetadataResolver;"}, {"nme": "setSyncContextFactory", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/synccontext/SyncContextFactory;)Lorg/eclipse/aether/internal/impl/DefaultMetadataResolver;"}, {"nme": "setOfflineController", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/OfflineController;)Lorg/eclipse/aether/internal/impl/DefaultMetadataResolver;"}, {"nme": "setRemoteRepositoryFilterManager", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/RemoteRepositoryFilterManager;)Lorg/eclipse/aether/internal/impl/DefaultMetadataResolver;"}, {"nme": "resolveMetadata", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection;)Ljava/util/List;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection<+Lorg/eclipse/aether/resolution/MetadataRequest;>;)Ljava/util/List<Lorg/eclipse/aether/resolution/MetadataResult;>;"}, {"nme": "resolve", "acc": 2, "dsc": "(Lorg/eclipse/aether/SyncContext;Lorg/eclipse/aether/SyncContext;Ljava/util/Collection;Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection;)Ljava/util/List;", "sig": "(Lorg/eclipse/aether/SyncContext;Lorg/eclipse/aether/SyncContext;Ljava/util/Collection<Lorg/eclipse/aether/metadata/Metadata;>;Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection<+Lorg/eclipse/aether/resolution/MetadataRequest;>;)Ljava/util/List<Lorg/eclipse/aether/resolution/MetadataResult;>;"}, {"nme": "getLocalFile", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/metadata/Metadata;)Ljava/io/File;"}, {"nme": "getEnabledSourceRepositories", "acc": 2, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/metadata/Metadata$Nature;)Ljava/util/List;", "sig": "(Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/metadata/Metadata$Nature;)Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"nme": "isEnabled", "acc": 2, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/metadata/Metadata$Nature;)Z"}, {"nme": "getPolicy", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/metadata/Metadata$Nature;)Lorg/eclipse/aether/repository/RepositoryPolicy;"}, {"nme": "metadataResolving", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/ArtifactRepository;)V"}, {"nme": "metadataResolved", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/ArtifactRepository;Ljava/lang/Exception;)V"}, {"nme": "metadataDownloading", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/ArtifactRepository;)V"}, {"nme": "metadataDownloaded", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/ArtifactRepository;Ljava/io/File;Ljava/lang/Exception;)V"}, {"nme": "access$000", "acc": 4104, "dsc": "(Lorg/eclipse/aether/internal/impl/DefaultMetadataResolver;)Lorg/eclipse/aether/impl/RepositoryConnectorProvider;"}], "flds": [{"acc": 26, "nme": "CONFIG_PROP_THREADS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.metadataResolver.threads"}, {"acc": 2, "nme": "repositoryEventDispatcher", "dsc": "Lorg/eclipse/aether/impl/RepositoryEventDispatcher;"}, {"acc": 2, "nme": "updateCheckManager", "dsc": "Lorg/eclipse/aether/impl/UpdateCheckManager;"}, {"acc": 2, "nme": "repositoryConnectorProvider", "dsc": "Lorg/eclipse/aether/impl/RepositoryConnectorProvider;"}, {"acc": 2, "nme": "remoteRepositoryManager", "dsc": "Lorg/eclipse/aether/impl/RemoteRepositoryManager;"}, {"acc": 2, "nme": "syncContextFactory", "dsc": "Lorg/eclipse/aether/spi/synccontext/SyncContextFactory;"}, {"acc": 2, "nme": "offlineController", "dsc": "Lorg/eclipse/aether/impl/OfflineController;"}, {"acc": 2, "nme": "remoteRepositoryFilterManager", "dsc": "Lorg/eclipse/aether/impl/RemoteRepositoryFilterManager;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;"}]}, "org/eclipse/aether/internal/impl/filter/GroupIdRemoteRepositoryFilterSource$GroupIdFilter.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/internal/impl/filter/GroupIdRemoteRepositoryFilterSource$GroupIdFilter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lorg/eclipse/aether/internal/impl/filter/GroupIdRemoteRepositoryFilterSource;Lorg/eclipse/aether/RepositorySystemSession;)V"}, {"nme": "acceptArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter$Result;"}, {"nme": "acceptMetadata", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/metadata/Metadata;)Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter$Result;"}, {"nme": "acceptGroupId", "acc": 2, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;)Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter$Result;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/eclipse/aether/internal/impl/filter/GroupIdRemoteRepositoryFilterSource;Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/internal/impl/filter/GroupIdRemoteRepositoryFilterSource$1;)V"}], "flds": [{"acc": 18, "nme": "session", "dsc": "Lorg/eclipse/aether/RepositorySystemSession;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lorg/eclipse/aether/internal/impl/filter/GroupIdRemoteRepositoryFilterSource;"}]}, "org/eclipse/aether/internal/impl/filter/PrefixesRemoteRepositoryFilterSource$1.class": {"ver": 52, "acc": 4128, "nme": "org/eclipse/aether/internal/impl/filter/PrefixesRemoteRepositoryFilterSource$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/internal/impl/DefaultOfflineController.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/DefaultOfflineController", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "checkOffline", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;)V", "exs": ["org/eclipse/aether/transfer/RepositoryOfflineException"]}, {"nme": "isOfflineProtocol", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;)Z"}, {"nme": "isOfflineHost", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;)Z"}, {"nme": "getConfig", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lja<PERSON>/lang/String;)[Lja<PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "CONFIG_PROP_OFFLINE_PROTOCOLS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.offline.protocols"}, {"acc": 24, "nme": "CONFIG_PROP_OFFLINE_HOSTS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.offline.hosts"}, {"acc": 26, "nme": "SEP", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;"}]}, "org/eclipse/aether/internal/impl/synccontext/named/providers/StaticNameMapperProvider.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/synccontext/named/providers/StaticNameMapperProvider", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "get", "acc": 1, "dsc": "()Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;"}, {"nme": "get", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 18, "nme": "mapper", "dsc": "Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;", "vals": ["value", "static"]}]}, "org/eclipse/aether/internal/impl/filter/PrefixesRemoteRepositoryFilterSource$PrefixesFilter.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/internal/impl/filter/PrefixesRemoteRepositoryFilterSource$PrefixesFilter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lorg/eclipse/aether/internal/impl/filter/PrefixesRemoteRepositoryFilterSource;Lorg/eclipse/aether/RepositorySystemSession;Ljava/nio/file/Path;)V"}, {"nme": "acceptArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter$Result;"}, {"nme": "acceptMetadata", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/metadata/Metadata;)Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter$Result;"}, {"nme": "acceptPrefix", "acc": 2, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;)Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter$Result;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/eclipse/aether/internal/impl/filter/PrefixesRemoteRepositoryFilterSource;Lorg/eclipse/aether/RepositorySystemSession;Ljava/nio/file/Path;Lorg/eclipse/aether/internal/impl/filter/PrefixesRemoteRepositoryFilterSource$1;)V"}], "flds": [{"acc": 18, "nme": "session", "dsc": "Lorg/eclipse/aether/RepositorySystemSession;"}, {"acc": 18, "nme": "basedir", "dsc": "Ljava/nio/file/Path;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lorg/eclipse/aether/internal/impl/filter/PrefixesRemoteRepositoryFilterSource;"}]}, "org/eclipse/aether/internal/impl/synccontext/named/StaticNameMapper.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/synccontext/named/StaticNameMapper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "isFileSystemFriendly", "acc": 1, "dsc": "()Z"}, {"nme": "nameLocks", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection;Ljava/util/Collection;)Ljava/util/Collection;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection<+Lorg/eclipse/aether/artifact/Artifact;>;Ljava/util/Collection<+Lorg/eclipse/aether/metadata/Metadata;>;)Ljava/util/Collection<Ljava/lang/String;>;"}], "flds": []}, "org/eclipse/aether/internal/impl/LocalPathPrefixComposer.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/internal/impl/LocalPathPrefixComposer", "super": "java/lang/Object", "mthds": [{"nme": "getPathPrefixForLocalArtifact", "acc": 1025, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lja<PERSON>/lang/String;"}, {"nme": "getPathPrefixForRemoteArtifact", "acc": 1025, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/RemoteRepository;)Ljava/lang/String;"}, {"nme": "getPathPrefixForLocalMetadata", "acc": 1025, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;)Ljava/lang/String;"}, {"nme": "getPathPrefixForRemoteMetadata", "acc": 1025, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/RemoteRepository;)Ljava/lang/String;"}], "flds": []}, "org/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$Args.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$Args", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/internal/impl/collect/DataPool;Lorg/eclipse/aether/internal/impl/collect/DefaultDependencyCollectionContext;Lorg/eclipse/aether/internal/impl/collect/DefaultVersionFilterContext;Lorg/eclipse/aether/collection/CollectRequest;Lorg/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper;Lorg/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$ParallelDescriptorResolver;)V"}], "flds": [{"acc": 16, "nme": "session", "dsc": "Lorg/eclipse/aether/RepositorySystemSession;"}, {"acc": 16, "nme": "ignoreRepos", "dsc": "Z"}, {"acc": 16, "nme": "premanagedState", "dsc": "Z"}, {"acc": 16, "nme": "pool", "dsc": "Lorg/eclipse/aether/internal/impl/collect/DataPool;"}, {"acc": 16, "nme": "dependencyProcessingQueue", "dsc": "<PERSON><PERSON><PERSON>/util/Queue;", "sig": "Ljava/util/Queue<Lorg/eclipse/aether/internal/impl/collect/bf/DependencyProcessingContext;>;"}, {"acc": 16, "nme": "collectionContext", "dsc": "Lorg/eclipse/aether/internal/impl/collect/DefaultDependencyCollectionContext;"}, {"acc": 16, "nme": "versionContext", "dsc": "Lorg/eclipse/aether/internal/impl/collect/DefaultVersionFilterContext;"}, {"acc": 16, "nme": "request", "dsc": "Lorg/eclipse/aether/collection/CollectRequest;"}, {"acc": 16, "nme": "skipper", "dsc": "Lorg/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper;"}, {"acc": 16, "nme": "resolver", "dsc": "Lorg/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$ParallelDescriptorResolver;"}]}, "org/eclipse/aether/impl/LocalRepositoryProvider.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/impl/LocalRepositoryProvider", "super": "java/lang/Object", "mthds": [{"nme": "newLocalRepositoryManager", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/LocalRepository;)Lorg/eclipse/aether/repository/LocalRepositoryManager;", "exs": ["org/eclipse/aether/repository/NoLocalRepositoryManagerException"]}], "flds": []}, "org/eclipse/aether/internal/impl/checksum/SparseDirectoryTrustedChecksumsSource$SparseDirectoryWriter.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/internal/impl/checksum/SparseDirectoryTrustedChecksumsSource$SparseDirectoryWriter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lorg/eclipse/aether/internal/impl/checksum/SparseDirectoryTrustedChecksumsSource;Ljava/nio/file/Path;Z)V"}, {"nme": "addTrustedArtifactChecksums", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/ArtifactRepository;Ljava/util/List;Ljava/util/Map;)V", "sig": "(Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/ArtifactRepository;Ljava/util/List<Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;>;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/eclipse/aether/internal/impl/checksum/SparseDirectoryTrustedChecksumsSource;Ljava/nio/file/Path;ZLorg/eclipse/aether/internal/impl/checksum/SparseDirectoryTrustedChecksumsSource$1;)V"}], "flds": [{"acc": 18, "nme": "basedir", "dsc": "Ljava/nio/file/Path;"}, {"acc": 18, "nme": "originAware", "dsc": "Z"}, {"acc": 4112, "nme": "this$0", "dsc": "Lorg/eclipse/aether/internal/impl/checksum/SparseDirectoryTrustedChecksumsSource;"}]}, "org/eclipse/aether/internal/impl/DefaultRepositoryEventDispatcher$1.class": {"ver": 52, "acc": 4128, "nme": "org/eclipse/aether/internal/impl/DefaultRepositoryEventDispatcher$1", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$org$eclipse$aether$RepositoryEvent$EventType", "dsc": "[I"}]}, "org/eclipse/aether/internal/impl/collect/DataPool.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/internal/impl/collect/DataPool", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)V"}, {"nme": "intern", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "intern", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/Dependency;)Lorg/eclipse/aether/graph/Dependency;"}, {"nme": "to<PERSON><PERSON>", "acc": 1, "dsc": "(Lorg/eclipse/aether/resolution/ArtifactDescriptorRequest;)Ljava/lang/Object;"}, {"nme": "getDescriptor", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/eclipse/aether/resolution/ArtifactDescriptorRequest;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;"}, {"nme": "putDescriptor", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;)V"}, {"nme": "putDescriptor", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/eclipse/aether/resolution/ArtifactDescriptorException;)V"}, {"nme": "to<PERSON><PERSON>", "acc": 1, "dsc": "(Lorg/eclipse/aether/resolution/VersionRangeRequest;)Ljava/lang/Object;"}, {"nme": "getConstraint", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/eclipse/aether/resolution/VersionRangeRequest;)Lorg/eclipse/aether/resolution/VersionRangeResult;"}, {"nme": "putConstraint", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/eclipse/aether/resolution/VersionRangeResult;)V"}, {"nme": "to<PERSON><PERSON>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/util/List;Lorg/eclipse/aether/collection/DependencySelector;Lorg/eclipse/aether/collection/DependencyManager;Lorg/eclipse/aether/collection/DependencyTraverser;Lorg/eclipse/aether/collection/VersionFilter;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;Lorg/eclipse/aether/collection/DependencySelector;Lorg/eclipse/aether/collection/DependencyManager;Lorg/eclipse/aether/collection/DependencyTraverser;Lorg/eclipse/aether/collection/VersionFilter;)Lja<PERSON>/lang/Object;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON>java/util/List;", "sig": "(Lja<PERSON>/lang/Object;)Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(L<PERSON><PERSON>/lang/Object;Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;)V"}, {"nme": "createPool", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/internal/impl/collect/DataPool$InternPool;", "sig": "<K:Ljava/lang/Object;V:Ljava/lang/Object;>(Ljava/lang/String;)Lorg/eclipse/aether/internal/impl/collect/DataPool$InternPool<TK;TV;>;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CONFIG_PROP_COLLECTOR_POOL_ARTIFACT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.dependencyCollector.pool.artifact"}, {"acc": 26, "nme": "CONFIG_PROP_COLLECTOR_POOL_DEPENDENCY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.dependencyCollector.pool.dependency"}, {"acc": 26, "nme": "CONFIG_PROP_COLLECTOR_POOL_DESCRIPTOR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.dependencyCollector.pool.descriptor"}, {"acc": 26, "nme": "ARTIFACT_POOL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "DEPENDENCY_POOL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "DESCRIPTORS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 25, "nme": "NO_DESCRIPTOR", "dsc": "Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;"}, {"acc": 18, "nme": "artifacts", "dsc": "Lorg/eclipse/aether/internal/impl/collect/DataPool$InternPool;", "sig": "Lorg/eclipse/aether/internal/impl/collect/DataPool$InternPool<Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/artifact/Artifact;>;"}, {"acc": 18, "nme": "dependencies", "dsc": "Lorg/eclipse/aether/internal/impl/collect/DataPool$InternPool;", "sig": "Lorg/eclipse/aether/internal/impl/collect/DataPool$InternPool<Lorg/eclipse/aether/graph/Dependency;Lorg/eclipse/aether/graph/Dependency;>;"}, {"acc": 18, "nme": "descriptors", "dsc": "Lorg/eclipse/aether/internal/impl/collect/DataPool$InternPool;", "sig": "Lorg/eclipse/aether/internal/impl/collect/DataPool$InternPool<Ljava/lang/Object;Lorg/eclipse/aether/internal/impl/collect/DataPool$Descriptor;>;"}, {"acc": 18, "nme": "constraints", "dsc": "Ljava/util/concurrent/ConcurrentHashMap;", "sig": "Ljava/util/concurrent/ConcurrentHashMap<Ljava/lang/Object;Lorg/eclipse/aether/internal/impl/collect/DataPool$Constraint;>;"}, {"acc": 18, "nme": "nodes", "dsc": "Ljava/util/concurrent/ConcurrentHashMap;", "sig": "Ljava/util/concurrent/ConcurrentHashMap<Ljava/lang/Object;Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;>;"}, {"acc": 26, "nme": "HARD", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "hard"}, {"acc": 26, "nme": "WEAK", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "weak"}]}, "org/eclipse/aether/impl/MetadataResolver.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/impl/MetadataResolver", "super": "java/lang/Object", "mthds": [{"nme": "resolveMetadata", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection;)Ljava/util/List;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection<+Lorg/eclipse/aether/resolution/MetadataRequest;>;)Ljava/util/List<Lorg/eclipse/aether/resolution/MetadataResult;>;"}], "flds": []}, "org/eclipse/aether/internal/impl/checksum/Sha256ChecksumAlgorithmFactory.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/checksum/Sha256ChecksumAlgorithmFactory", "super": "org/eclipse/aether/internal/impl/checksum/MessageDigestChecksumAlgorithmFactorySupport", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}], "flds": [{"acc": 25, "nme": "NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "SHA-256"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;", "vals": ["value", "SHA-256"]}]}, "org/eclipse/aether/impl/Installer.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/impl/Installer", "super": "java/lang/Object", "mthds": [{"nme": "install", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/installation/InstallRequest;)Lorg/eclipse/aether/installation/InstallResult;", "exs": ["org/eclipse/aether/installation/InstallationException"]}], "flds": []}, "org/eclipse/aether/internal/impl/LocalPathPrefixComposerFactory.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/internal/impl/LocalPathPrefixComposerFactory", "super": "java/lang/Object", "mthds": [{"nme": "createComposer", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lorg/eclipse/aether/internal/impl/LocalPathPrefixComposer;"}], "flds": []}, "org/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate$Results.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate$Results", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/collection/CollectResult;Lorg/eclipse/aether/RepositorySystemSession;)V"}, {"nme": "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "addException", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/Dependency;Lja<PERSON>/lang/Exception;<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Lorg/eclipse/aether/graph/Dependency;Ljava/lang/Exception;Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;)V"}, {"nme": "addCycle", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;ILorg/eclipse/aether/graph/Dependency;)V", "sig": "(<PERSON><PERSON><PERSON>/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;ILorg/eclipse/aether/graph/Dependency;)V"}], "flds": [{"acc": 18, "nme": "result", "dsc": "Lorg/eclipse/aether/collection/CollectResult;"}, {"acc": 16, "nme": "maxExceptions", "dsc": "I"}, {"acc": 16, "nme": "maxCycles", "dsc": "I"}, {"acc": 0, "nme": "errorPath", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/eclipse/aether/internal/impl/checksum/MessageDigestChecksumAlgorithmFactorySupport$1.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/internal/impl/checksum/MessageDigestChecksumAlgorithmFactorySupport$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/internal/impl/checksum/MessageDigestChecksumAlgorithmFactorySupport;Ljava/security/MessageDigest;)V", "sig": "()V"}, {"nme": "update", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/nio/<PERSON>;)V"}, {"nme": "checksum", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 4112, "nme": "val$messageDigest", "dsc": "Ljava/security/MessageDigest;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lorg/eclipse/aether/internal/impl/checksum/MessageDigestChecksumAlgorithmFactorySupport;"}]}, "org/eclipse/aether/internal/impl/DefaultLocalRepositoryProvider.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/DefaultLocalRepositoryProvider", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(<PERSON><PERSON><PERSON>/util/Set<Lorg/eclipse/aether/spi/localrepo/LocalRepositoryManagerFactory;>;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "initService", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/locator/ServiceLocator;)V"}, {"nme": "addLocalRepositoryManagerFactory", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/localrepo/LocalRepositoryManagerFactory;)Lorg/eclipse/aether/internal/impl/DefaultLocalRepositoryProvider;"}, {"nme": "setLocalRepositoryManagerFactories", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Lorg/eclipse/aether/internal/impl/DefaultLocalRepositoryProvider;", "sig": "(Ljava/util/Collection<Lorg/eclipse/aether/spi/localrepo/LocalRepositoryManagerFactory;>;)Lorg/eclipse/aether/internal/impl/DefaultLocalRepositoryProvider;"}, {"nme": "newLocalRepositoryManager", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/LocalRepository;)Lorg/eclipse/aether/repository/LocalRepositoryManager;", "exs": ["org/eclipse/aether/repository/NoLocalRepositoryManagerException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "LOGGER", "dsc": "Lorg/slf4j/Logger;"}, {"acc": 2, "nme": "managerFactories", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lorg/eclipse/aether/spi/localrepo/LocalRepositoryManagerFactory;>;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;"}]}, "org/eclipse/aether/internal/impl/DefaultChecksumPolicyProvider.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/internal/impl/DefaultChecksumPolicyProvider", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "newChecksumPolicy", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/transfer/TransferResource;Ljava/lang/String;)Lorg/eclipse/aether/spi/connector/checksum/ChecksumPolicy;"}, {"nme": "getEffectiveChecksumPolicy", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "ordinalOfPolicy", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "validatePolicy", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 26, "nme": "ORDINAL_IGNORE", "dsc": "I", "val": 0}, {"acc": 26, "nme": "ORDINAL_WARN", "dsc": "I", "val": 1}, {"acc": 26, "nme": "ORDINAL_FAIL", "dsc": "I", "val": 2}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;"}]}, "org/eclipse/aether/internal/impl/DefaultMetadataResolver$ResolveTask.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/internal/impl/DefaultMetadataResolver$ResolveTask", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/internal/impl/DefaultMetadataResolver;Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/resolution/MetadataResult;Ljava/io/File;Ljava/util/List;Ljava/lang/String;)V", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/resolution/MetadataResult;Ljava/io/File;Ljava/util/List<Lorg/eclipse/aether/impl/UpdateCheck<Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/transfer/MetadataTransferException;>;>;Ljava/lang/String;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 16, "nme": "session", "dsc": "Lorg/eclipse/aether/RepositorySystemSession;"}, {"acc": 16, "nme": "trace", "dsc": "Lorg/eclipse/aether/RequestTrace;"}, {"acc": 16, "nme": "result", "dsc": "Lorg/eclipse/aether/resolution/MetadataResult;"}, {"acc": 16, "nme": "request", "dsc": "Lorg/eclipse/aether/resolution/MetadataRequest;"}, {"acc": 16, "nme": "metadataFile", "dsc": "Ljava/io/File;"}, {"acc": 16, "nme": "policy", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 16, "nme": "checks", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/impl/UpdateCheck<Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/transfer/MetadataTransferException;>;>;"}, {"acc": 64, "nme": "exception", "dsc": "Lorg/eclipse/aether/transfer/MetadataTransferException;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lorg/eclipse/aether/internal/impl/DefaultMetadataResolver;"}]}, "org/eclipse/aether/internal/impl/filter/RemoteRepositoryFilterSourceSupport.class": {"ver": 52, "acc": 1057, "nme": "org/eclipse/aether/internal/impl/filter/RemoteRepositoryFilterSourceSupport", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "configPropKey", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "isEnabled", "acc": 4, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Z"}, {"nme": "getBasedir", "acc": 4, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Z)Ljava/nio/file/Path;"}], "flds": [{"acc": 26, "nme": "CONFIG_PROP_PREFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.remoteRepositoryFilter."}, {"acc": 26, "nme": "CONF_NAME_BASEDIR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "basedir"}, {"acc": 24, "nme": "LOCAL_REPO_PREFIX_DIR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": ".remoteRepositoryFilters"}, {"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$ParallelDescriptorResolver.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$ParallelDescriptorResolver", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(I)V"}, {"nme": "resolveDescriptors", "acc": 0, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/util/concurrent/Callable;)V", "sig": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/util/concurrent/Callable<Lorg/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$DescriptorResolutionResult;>;)V"}, {"nme": "cacheVersionRangeDescriptor", "acc": 0, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$DescriptorResolutionResult;)V"}, {"nme": "find", "acc": 0, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Ljava/util/concurrent/Future;", "sig": "(Lorg/eclipse/aether/artifact/Artifact;)Ljava/util/concurrent/Future<Lorg/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$DescriptorResolutionResult;>;"}, {"nme": "close", "acc": 1, "dsc": "()V"}, {"nme": "lambda$cacheVersionRangeDescriptor$1", "acc": 4106, "dsc": "(Lorg/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$DescriptorResolutionResult;Ljava/lang/String;)Ljava/util/concurrent/Future;"}, {"nme": "lambda$resolveDescriptors$0", "acc": 4098, "dsc": "(L<PERSON><PERSON>/util/concurrent/Callable;Lja<PERSON>/lang/String;)Ljava/util/concurrent/Future;"}], "flds": [{"acc": 18, "nme": "executorService", "dsc": "Ljava/util/concurrent/ExecutorService;"}, {"acc": 18, "nme": "results", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/util/concurrent/Future<Lorg/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$DescriptorResolutionResult;>;>;"}]}, "org/eclipse/aether/internal/impl/DefaultRepositoryEventDispatcher.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/DefaultRepositoryEventDispatcher", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(<PERSON><PERSON><PERSON>/util/Set<Lorg/eclipse/aether/RepositoryListener;>;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "addRepositoryListener", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryListener;)Lorg/eclipse/aether/internal/impl/DefaultRepositoryEventDispatcher;"}, {"nme": "setRepositoryListeners", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Lorg/eclipse/aether/internal/impl/DefaultRepositoryEventDispatcher;", "sig": "(Lja<PERSON>/util/Collection<Lorg/eclipse/aether/RepositoryListener;>;)Lorg/eclipse/aether/internal/impl/DefaultRepositoryEventDispatcher;"}, {"nme": "initService", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/locator/ServiceLocator;)V"}, {"nme": "dispatch", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}, {"nme": "dispatch", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;Lorg/eclipse/aether/RepositoryListener;)V"}, {"nme": "logError", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "LOGGER", "dsc": "Lorg/slf4j/Logger;"}, {"acc": 2, "nme": "listeners", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lorg/eclipse/aether/RepositoryListener;>;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;"}]}, "org/eclipse/aether/internal/impl/collect/DefaultDependencyCollector.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/collect/DefaultDependencyCollector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate;>;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "initService", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/locator/ServiceLocator;)V"}, {"nme": "collectDependencies", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/collection/CollectRequest;)Lorg/eclipse/aether/collection/CollectResult;", "exs": ["org/eclipse/aether/collection/DependencyCollectionException"]}], "flds": [{"acc": 26, "nme": "CONFIG_PROP_COLLECTOR_IMPL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.dependencyCollector.impl"}, {"acc": 26, "nme": "DEFAULT_COLLECTOR_IMPL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "df"}, {"acc": 18, "nme": "delegates", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate;>;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;"}]}, "org/eclipse/aether/internal/impl/LoggerFactoryProvider.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/LoggerFactoryProvider", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "get", "acc": 1, "dsc": "()Lorg/eclipse/aether/spi/log/LoggerFactory;"}, {"nme": "get", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 2, "nme": "slf4j", "dsc": "Ljavax/inject/Provider;", "sig": "Ljavax/inject/Provider<Lorg/eclipse/aether/spi/log/LoggerFactory;>;"}], "vanns": [{"dsc": "Ljavax/inject/Named;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/eclipse/aether/impl/guice/AetherModule.class": {"ver": 52, "acc": 131105, "nme": "org/eclipse/aether/impl/guice/AetherModule", "super": "com/google/inject/AbstractModule", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "configure", "acc": 4, "dsc": "()V"}, {"nme": "remoteRepositoryFilterSources", "acc": 0, "dsc": "(Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilterSource;Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilterSource;)Ljava/util/Map;", "sig": "(Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilterSource;Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilterSource;)Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilterSource;>;", "vanns": [{"dsc": "Lcom/google/inject/Provides;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, {"nme": "artifactResolverProcessors", "acc": 0, "dsc": "(Lorg/eclipse/aether/spi/resolution/ArtifactResolverPostProcessor;)Ljava/util/Map;", "sig": "(Lorg/eclipse/aether/spi/resolution/ArtifactResolverPostProcessor;)Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/spi/resolution/ArtifactResolverPostProcessor;>;", "vanns": [{"dsc": "Lcom/google/inject/Provides;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, {"nme": "dependencyCollectorDelegates", "acc": 0, "dsc": "(Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate;Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate;)Ljava/util/Map;", "sig": "(Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate;Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate;)Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate;>;", "vanns": [{"dsc": "Lcom/google/inject/Provides;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, {"nme": "providedChecksumSources", "acc": 0, "dsc": "(Lorg/eclipse/aether/spi/checksums/ProvidedChecksumsSource;)Ljava/util/Map;", "sig": "(Lorg/eclipse/aether/spi/checksums/ProvidedChecksumsSource;)Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/spi/checksums/ProvidedChecksumsSource;>;", "vanns": [{"dsc": "Lcom/google/inject/Provides;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, {"nme": "trustedChecksumSources", "acc": 0, "dsc": "(Lorg/eclipse/aether/spi/checksums/TrustedChecksumsSource;Lorg/eclipse/aether/spi/checksums/TrustedChecksumsSource;)Ljava/util/Map;", "sig": "(Lorg/eclipse/aether/spi/checksums/TrustedChecksumsSource;Lorg/eclipse/aether/spi/checksums/TrustedChecksumsSource;)Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/spi/checksums/TrustedChecksumsSource;>;", "vanns": [{"dsc": "Lcom/google/inject/Provides;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, {"nme": "provideChecksumTypes", "acc": 0, "dsc": "(Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;)Ljava/util/Map;", "sig": "(Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;)Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;>;", "vanns": [{"dsc": "Lcom/google/inject/Provides;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, {"nme": "provideNameMappers", "acc": 0, "dsc": "(Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;)Ljava/util/Map;", "sig": "(Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;)Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;>;", "vanns": [{"dsc": "Lcom/google/inject/Provides;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, {"nme": "provideNamedLockFactories", "acc": 0, "dsc": "(Lorg/eclipse/aether/named/NamedLockFactory;Lorg/eclipse/aether/named/NamedLockFactory;Lorg/eclipse/aether/named/NamedLockFactory;)Ljava/util/Map;", "sig": "(Lorg/eclipse/aether/named/NamedLockFactory;Lorg/eclipse/aether/named/NamedLockFactory;Lorg/eclipse/aether/named/NamedLockFactory;)Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/named/NamedLockFactory;>;", "vanns": [{"dsc": "Lcom/google/inject/Provides;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, {"nme": "provideLocalRepositoryManagerFactories", "acc": 0, "dsc": "(Lorg/eclipse/aether/spi/localrepo/LocalRepositoryManagerFactory;Lorg/eclipse/aether/spi/localrepo/LocalRepositoryManagerFactory;)Ljava/util/Set;", "sig": "(Lorg/eclipse/aether/spi/localrepo/LocalRepositoryManagerFactory;Lorg/eclipse/aether/spi/localrepo/LocalRepositoryManagerFactory;)Ljava/util/Set<Lorg/eclipse/aether/spi/localrepo/LocalRepositoryManagerFactory;>;", "vanns": [{"dsc": "Lcom/google/inject/Provides;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, {"nme": "provideRepositoryLayoutFactories", "acc": 0, "dsc": "(Lorg/eclipse/aether/spi/connector/layout/RepositoryLayoutFactory;)Ljava/util/Set;", "sig": "(Lorg/eclipse/aether/spi/connector/layout/RepositoryLayoutFactory;)Ljava/util/Set<Lorg/eclipse/aether/spi/connector/layout/RepositoryLayoutFactory;>;", "vanns": [{"dsc": "Lcom/google/inject/Provides;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, {"nme": "providesRepositoryListeners", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Lorg/eclipse/aether/RepositoryListener;>;", "vanns": [{"dsc": "Lcom/google/inject/Provides;"}, {"dsc": "Ljavax/inject/Singleton;"}]}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "org/eclipse/aether/impl/DefaultServiceLocator.class": {"ver": 52, "acc": 131121, "nme": "org/eclipse/aether/impl/DefaultServiceLocator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getEntry", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Z)Lorg/eclipse/aether/impl/DefaultServiceLocator$Entry;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;Z)Lorg/eclipse/aether/impl/DefaultServiceLocator$Entry<TT;>;"}, {"nme": "setService", "acc": 1, "dsc": "(Lja<PERSON>/lang/Class;Ljava/lang/Class;)Lorg/eclipse/aether/impl/DefaultServiceLocator;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;Ljava/lang/Class<+TT;>;)Lorg/eclipse/aether/impl/DefaultServiceLocator;"}, {"nme": "addService", "acc": 1, "dsc": "(Lja<PERSON>/lang/Class;Ljava/lang/Class;)Lorg/eclipse/aether/impl/DefaultServiceLocator;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;Ljava/lang/Class<+TT;>;)Lorg/eclipse/aether/impl/DefaultServiceLocator;"}, {"nme": "setServices", "acc": 129, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;[Ljava/lang/Object;)Lorg/eclipse/aether/impl/DefaultServiceLocator;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;[TT;)Lorg/eclipse/aether/impl/DefaultServiceLocator;"}, {"nme": "getService", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>ja<PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;)TT;"}, {"nme": "getServices", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/util/List;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;)Ljava/util/List<TT;>;"}, {"nme": "serviceCreationFailed", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/Class;Lja<PERSON>/lang/Throwable;)V", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/Class<*>;Ljava/lang/Throwable;)V"}, {"nme": "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/DefaultServiceLocator$ErrorHandler;)V"}, {"nme": "access$000", "acc": 4104, "dsc": "(Lorg/eclipse/aether/impl/DefaultServiceLocator;Ljava/lang/Class;Ljava/lang/Class;Ljava/lang/Throwable;)V"}], "flds": [{"acc": 18, "nme": "entries", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Class<*>;Lorg/eclipse/aether/impl/DefaultServiceLocator$Entry<*>;>;"}, {"acc": 2, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "Lorg/eclipse/aether/impl/DefaultServiceLocator$ErrorHandler;"}], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "org/eclipse/aether/internal/impl/collect/DataPool$1.class": {"ver": 52, "acc": 4128, "nme": "org/eclipse/aether/internal/impl/collect/DataPool$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/internal/impl/collect/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/internal/impl/collect/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/internal/impl/DefaultLocalPathComposer.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/internal/impl/DefaultLocalPathComposer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getPathForArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Z)Ljava/lang/String;"}, {"nme": "getPathForMetadata", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "insertRepositoryKey", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": [], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;"}]}, "org/eclipse/aether/internal/impl/PrioritizedComponent.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/internal/impl/PrioritizedComponent", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Ljava/lang/Class;FI)V", "sig": "(TT;Ljava/lang/Class<*>;FI)V"}, {"nme": "getComponent", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}, {"nme": "getType", "acc": 1, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<*>;"}, {"nme": "getPriority", "acc": 1, "dsc": "()F"}, {"nme": "isDisabled", "acc": 1, "dsc": "()Z"}, {"nme": "compareTo", "acc": 1, "dsc": "(Lorg/eclipse/aether/internal/impl/PrioritizedComponent;)I", "sig": "(Lorg/eclipse/aether/internal/impl/PrioritizedComponent<*>;)I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "compareTo", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}], "flds": [{"acc": 18, "nme": "component", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;", "sig": "TT;"}, {"acc": 18, "nme": "type", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 18, "nme": "priority", "dsc": "F"}, {"acc": 18, "nme": "index", "dsc": "I"}]}, "org/eclipse/aether/internal/impl/checksum/Md5ChecksumAlgorithmFactory.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/checksum/Md5ChecksumAlgorithmFactory", "super": "org/eclipse/aether/internal/impl/checksum/MessageDigestChecksumAlgorithmFactorySupport", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}], "flds": [{"acc": 25, "nme": "NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "MD5"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;", "vals": ["value", "MD5"]}]}, "org/eclipse/aether/impl/RepositorySystemLifecycle.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/impl/RepositorySystemLifecycle", "super": "java/lang/Object", "mthds": [{"nme": "systemEnded", "acc": 1025, "dsc": "()V"}, {"nme": "addOnSystemEndedHandler", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Runnable;)V"}], "flds": []}, "org/eclipse/aether/internal/impl/DefaultTrackingFileManager.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/internal/impl/DefaultTrackingFileManager", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "(Ljava/io/File;)Ljava/util/Properties;"}, {"nme": "update", "acc": 1, "dsc": "(Ljava/io/File;Ljava/util/Map;)Ljava/util/Properties;", "sig": "(Ljava/io/File;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)Ljava/util/Properties;"}, {"nme": "getMutex", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)Lja<PERSON>/lang/Object;"}, {"nme": "fileLock", "acc": 2, "dsc": "(Ljava/nio/channels/FileChannel;JZ)Ljava/nio/channels/FileLock;", "exs": ["java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "LOGGER", "dsc": "Lorg/slf4j/Logger;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;"}]}, "org/eclipse/aether/internal/impl/collect/df/DfDependencyCollector$Args.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/internal/impl/collect/df/DfDependencyCollector$Args", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/internal/impl/collect/DataPool;Lorg/eclipse/aether/internal/impl/collect/df/NodeStack;Lorg/eclipse/aether/internal/impl/collect/DefaultDependencyCollectionContext;Lorg/eclipse/aether/internal/impl/collect/DefaultVersionFilterContext;Lorg/eclipse/aether/collection/CollectRequest;)V"}], "flds": [{"acc": 16, "nme": "session", "dsc": "Lorg/eclipse/aether/RepositorySystemSession;"}, {"acc": 16, "nme": "ignoreRepos", "dsc": "Z"}, {"acc": 16, "nme": "premanagedState", "dsc": "Z"}, {"acc": 16, "nme": "pool", "dsc": "Lorg/eclipse/aether/internal/impl/collect/DataPool;"}, {"acc": 16, "nme": "nodes", "dsc": "Lorg/eclipse/aether/internal/impl/collect/df/NodeStack;"}, {"acc": 16, "nme": "collectionContext", "dsc": "Lorg/eclipse/aether/internal/impl/collect/DefaultDependencyCollectionContext;"}, {"acc": 16, "nme": "versionContext", "dsc": "Lorg/eclipse/aether/internal/impl/collect/DefaultVersionFilterContext;"}, {"acc": 16, "nme": "request", "dsc": "Lorg/eclipse/aether/collection/CollectRequest;"}]}, "org/eclipse/aether/internal/impl/filter/DefaultRemoteRepositoryFilterManager$1.class": {"ver": 52, "acc": 4128, "nme": "org/eclipse/aether/internal/impl/filter/DefaultRemoteRepositoryFilterManager$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/internal/impl/EnhancedLocalRepositoryManagerFactory.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/EnhancedLocalRepositoryManagerFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/internal/impl/LocalPathComposer;Lorg/eclipse/aether/internal/impl/TrackingFileManager;Lorg/eclipse/aether/internal/impl/LocalPathPrefixComposerFactory;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "initService", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/locator/ServiceLocator;)V"}, {"nme": "newInstance", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/LocalRepository;)Lorg/eclipse/aether/repository/LocalRepositoryManager;", "exs": ["org/eclipse/aether/repository/NoLocalRepositoryManagerException"]}, {"nme": "getPriority", "acc": 1, "dsc": "()F"}, {"nme": "setPriority", "acc": 1, "dsc": "(F)Lorg/eclipse/aether/internal/impl/EnhancedLocalRepositoryManagerFactory;"}], "flds": [{"acc": 25, "nme": "NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "enhanced"}, {"acc": 26, "nme": "CONFIG_PROP_TRACKING_FILENAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.enhancedLocalRepository.trackingFilename"}, {"acc": 26, "nme": "DEFAULT_TRACKING_FILENAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "_remote.repositories"}, {"acc": 2, "nme": "priority", "dsc": "F"}, {"acc": 2, "nme": "localPathComposer", "dsc": "Lorg/eclipse/aether/internal/impl/LocalPathComposer;"}, {"acc": 2, "nme": "trackingFileManager", "dsc": "Lorg/eclipse/aether/internal/impl/TrackingFileManager;"}, {"acc": 2, "nme": "localPathPrefixComposerFactory", "dsc": "Lorg/eclipse/aether/internal/impl/LocalPathPrefixComposerFactory;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;", "vals": ["value", "enhanced"]}]}, "org/eclipse/aether/internal/impl/synccontext/named/NameMappers.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/internal/impl/synccontext/named/NameMappers", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "staticNameMapper", "acc": 9, "dsc": "()Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;"}, {"nme": "gavNameMapper", "acc": 9, "dsc": "()Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;"}, {"nme": "fileGavNameMapper", "acc": 9, "dsc": "()Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;"}, {"nme": "fileStaticNameMapper", "acc": 9, "dsc": "()Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;"}, {"nme": "fileHashingGavNameMapper", "acc": 9, "dsc": "()Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;"}, {"nme": "discriminatingNameMapper", "acc": 9, "dsc": "()Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;"}], "flds": [{"acc": 25, "nme": "STATIC_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "static"}, {"acc": 25, "nme": "GAV_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "gav"}, {"acc": 25, "nme": "FILE_GAV_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "file-gav"}, {"acc": 25, "nme": "FILE_HGAV_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "file-hgav"}, {"acc": 25, "nme": "FILE_STATIC_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "file-static"}, {"acc": 25, "nme": "DISCRIMINATING_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "discriminating"}]}, "org/eclipse/aether/internal/impl/DefaultTransporterProvider.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/internal/impl/DefaultTransporterProvider", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(Ljava/util/Set<Lorg/eclipse/aether/spi/connector/transport/TransporterFactory;>;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "initService", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/locator/ServiceLocator;)V"}, {"nme": "addTransporterFactory", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/connector/transport/TransporterFactory;)Lorg/eclipse/aether/internal/impl/DefaultTransporterProvider;"}, {"nme": "setTransporterFactories", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Lorg/eclipse/aether/internal/impl/DefaultTransporterProvider;", "sig": "(Ljava/util/Collection<Lorg/eclipse/aether/spi/connector/transport/TransporterFactory;>;)Lorg/eclipse/aether/internal/impl/DefaultTransporterProvider;"}, {"nme": "newTransporter", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/spi/connector/transport/Transporter;", "exs": ["org/eclipse/aether/transfer/NoTransporterException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "LOGGER", "dsc": "Lorg/slf4j/Logger;"}, {"acc": 2, "nme": "factories", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lorg/eclipse/aether/spi/connector/transport/TransporterFactory;>;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;"}]}, "org/eclipse/aether/impl/RemoteRepositoryManager.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/impl/RemoteRepositoryManager", "super": "java/lang/Object", "mthds": [{"nme": "aggregateRepositories", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/List;<PERSON><PERSON><PERSON>/util/List;Z)Ljava/util/List;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;Z)Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"nme": "getPolicy", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;ZZ)Lorg/eclipse/aether/repository/RepositoryPolicy;"}], "flds": []}, "org/eclipse/aether/impl/OfflineController.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/impl/OfflineController", "super": "java/lang/Object", "mthds": [{"nme": "checkOffline", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;)V", "exs": ["org/eclipse/aether/transfer/RepositoryOfflineException"]}], "flds": []}, "org/eclipse/aether/internal/impl/checksum/FileTrustedChecksumsSourceSupport.class": {"ver": 52, "acc": 1056, "nme": "org/eclipse/aether/internal/impl/checksum/FileTrustedChecksumsSourceSupport", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getTrustedArtifactChecksums", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/ArtifactRepository;Ljava/util/List;)Ljava/util/Map;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/ArtifactRepository;Ljava/util/List<Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;>;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "getTrustedArtifactChecksumsWriter", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lorg/eclipse/aether/spi/checksums/TrustedChecksumsSource$Writer;"}, {"nme": "doGetTrustedArtifactChecksums", "acc": 1028, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/ArtifactRepository;Ljava/util/List;)Ljava/util/Map;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/ArtifactRepository;Ljava/util/List<Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;>;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "doGetTrustedArtifactChecksumsWriter", "acc": 4, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lorg/eclipse/aether/spi/checksums/TrustedChecksumsSource$Writer;"}, {"nme": "configPropKey", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "isEnabled", "acc": 4, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Z"}, {"nme": "isOriginAware", "acc": 4, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Z"}, {"nme": "getBasedir", "acc": 4, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Z)Ljava/nio/file/Path;"}], "flds": [{"acc": 26, "nme": "CONFIG_PROP_PREFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.trustedChecksumsSource."}, {"acc": 26, "nme": "CONF_NAME_BASEDIR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "basedir"}, {"acc": 26, "nme": "CONF_NAME_ORIGIN_AWARE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "originAware"}, {"acc": 24, "nme": "LOCAL_REPO_PREFIX_DIR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": ".checksums"}, {"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/eclipse/aether/internal/impl/synccontext/named/HashingNameMapper.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/synccontext/named/HashingNameMapper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;)V"}, {"nme": "isFileSystemFriendly", "acc": 1, "dsc": "()Z"}, {"nme": "nameLocks", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection;Ljava/util/Collection;)Ljava/util/Collection;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection<+Lorg/eclipse/aether/artifact/Artifact;>;Ljava/util/Collection<+Lorg/eclipse/aether/metadata/Metadata;>;)Ljava/util/Collection<Ljava/lang/String;>;"}, {"nme": "hashName", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Ljava/lang/String;"}, {"nme": "lambda$nameLocks$0", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": [{"acc": 26, "nme": "CONFIG_PROP_DEPTH", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.syncContext.named.hashing.depth"}, {"acc": 18, "nme": "delegate", "dsc": "Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;"}]}, "org/eclipse/aether/internal/impl/collect/DataPool$BadDescriptor.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/internal/impl/collect/DataPool$BadDescriptor", "super": "org/eclipse/aether/internal/impl/collect/DataPool$Descriptor", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "toResult", "acc": 1, "dsc": "(Lorg/eclipse/aether/resolution/ArtifactDescriptorRequest;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "INSTANCE", "dsc": "Lorg/eclipse/aether/internal/impl/collect/DataPool$BadDescriptor;"}]}, "org/eclipse/aether/internal/impl/collect/bf/DependencyProcessingContext.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/internal/impl/collect/bf/DependencyProcessingContext", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/collection/DependencySelector;Lorg/eclipse/aether/collection/DependencyManager;Lorg/eclipse/aether/collection/DependencyTraverser;Lorg/eclipse/aether/collection/VersionFilter;Lorg/eclipse/aether/RequestTrace;Lja<PERSON>/util/List;Ljava/util/List;Ljava/util/List;Lorg/eclipse/aether/graph/Dependency;Lorg/eclipse/aether/internal/impl/collect/PremanagedDependency;)V", "sig": "(Lorg/eclipse/aether/collection/DependencySelector;Lorg/eclipse/aether/collection/DependencyManager;Lorg/eclipse/aether/collection/DependencyTraverser;Lorg/eclipse/aether/collection/VersionFilter;Lorg/eclipse/aether/RequestTrace;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;Lorg/eclipse/aether/graph/Dependency;Lorg/eclipse/aether/internal/impl/collect/PremanagedDependency;)V"}, {"nme": "withDependency", "acc": 0, "dsc": "(Lorg/eclipse/aether/graph/Dependency;)Lorg/eclipse/aether/internal/impl/collect/bf/DependencyProcessingContext;"}, {"nme": "copy", "acc": 0, "dsc": "()Lorg/eclipse/aether/internal/impl/collect/bf/DependencyProcessingContext;"}, {"nme": "getParent", "acc": 0, "dsc": "()Lorg/eclipse/aether/graph/DependencyNode;"}], "flds": [{"acc": 16, "nme": "depSelector", "dsc": "Lorg/eclipse/aether/collection/DependencySelector;"}, {"acc": 16, "nme": "depManager", "dsc": "Lorg/eclipse/aether/collection/DependencyManager;"}, {"acc": 16, "nme": "depTraverser", "dsc": "Lorg/eclipse/aether/collection/DependencyTraverser;"}, {"acc": 16, "nme": "verFilter", "dsc": "Lorg/eclipse/aether/collection/VersionFilter;"}, {"acc": 16, "nme": "repositories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"acc": 16, "nme": "managedDependencies", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;"}, {"acc": 16, "nme": "parents", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;"}, {"acc": 16, "nme": "premanagedDependency", "dsc": "Lorg/eclipse/aether/internal/impl/collect/PremanagedDependency;"}, {"acc": 16, "nme": "trace", "dsc": "Lorg/eclipse/aether/RequestTrace;"}, {"acc": 0, "nme": "dependency", "dsc": "Lorg/eclipse/aether/graph/Dependency;"}]}, "org/eclipse/aether/internal/impl/synccontext/named/BasedirNameMapper.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/synccontext/named/BasedirNameMapper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;)V"}, {"nme": "isFileSystemFriendly", "acc": 1, "dsc": "()Z"}, {"nme": "nameLocks", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection;Ljava/util/Collection;)Ljava/util/Collection;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection<+Lorg/eclipse/aether/artifact/Artifact;>;Ljava/util/Collection<+Lorg/eclipse/aether/metadata/Metadata;>;)Ljava/util/Collection<Ljava/lang/String;>;"}, {"nme": "lambda$nameLocks$0", "acc": 4106, "dsc": "(Lja<PERSON>/nio/file/Path;Ljava/lang/String;)Ljava/lang/String;"}], "flds": [{"acc": 26, "nme": "CONFIG_PROP_LOCKS_DIR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.syncContext.named.basedir.locksDir"}, {"acc": 18, "nme": "delegate", "dsc": "Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;"}]}, "org/eclipse/aether/internal/impl/synccontext/named/providers/FileGAVNameMapperProvider.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/synccontext/named/providers/FileGAVNameMapperProvider", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "get", "acc": 1, "dsc": "()Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;"}, {"nme": "get", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 18, "nme": "mapper", "dsc": "Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;", "vals": ["value", "file-gav"]}]}, "org/eclipse/aether/internal/impl/checksum/TrustedToProvidedChecksumsSourceAdapter.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/internal/impl/checksum/TrustedToProvidedChecksumsSourceAdapter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/spi/checksums/TrustedChecksumsSource;>;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "getProvidedArtifactChecksums", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/spi/connector/ArtifactDownload;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/util/List;)Ljava/util/Map;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/spi/connector/ArtifactDownload;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/util/List<Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;>;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}], "flds": [{"acc": 25, "nme": "NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "trusted2provided"}, {"acc": 18, "nme": "trustedChecksumsSources", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/spi/checksums/TrustedChecksumsSource;>;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;", "vals": ["value", "trusted2provided"]}]}, "org/eclipse/aether/internal/impl/collect/DefaultDependencyCollectionContext.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/internal/impl/collect/DefaultDependencyCollectionContext", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/graph/Dependency;<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/graph/Dependency;Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;)V"}, {"nme": "getSession", "acc": 1, "dsc": "()Lorg/eclipse/aether/RepositorySystemSession;"}, {"nme": "getArtifact", "acc": 1, "dsc": "()Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "getDependency", "acc": 1, "dsc": "()Lorg/eclipse/aether/graph/Dependency;"}, {"nme": "getManagedDependencies", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;"}, {"nme": "set", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/Dependency;Lja<PERSON>/util/List;)V", "sig": "(Lorg/eclipse/aether/graph/Dependency;Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "session", "dsc": "Lorg/eclipse/aether/RepositorySystemSession;"}, {"acc": 2, "nme": "artifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}, {"acc": 2, "nme": "dependency", "dsc": "Lorg/eclipse/aether/graph/Dependency;"}, {"acc": 2, "nme": "managedDependencies", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;"}]}, "org/eclipse/aether/impl/guice/AetherModule$Slf4jModule.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/impl/guice/AetherModule$Slf4jModule", "super": "com/google/inject/AbstractModule", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "configure", "acc": 4, "dsc": "()V"}, {"nme": "getLoggerFactory", "acc": 0, "dsc": "()Lorg/slf4j/ILoggerFactory;", "vanns": [{"dsc": "Lcom/google/inject/Provides;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/eclipse/aether/impl/guice/AetherModule$1;)V"}], "flds": []}, "org/eclipse/aether/internal/impl/slf4j/Slf4jLoggerFactory$Slf4jLoggerEx.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/internal/impl/slf4j/Slf4jLoggerFactory$Slf4jLoggerEx", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/slf4j/spi/LocationAwareLogger;)V"}, {"nme": "isDebugEnabled", "acc": 1, "dsc": "()Z"}, {"nme": "debug", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "debug", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "is<PERSON>arn<PERSON>nabled", "acc": 1, "dsc": "()Z"}, {"nme": "warn", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "warn", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "FQCN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "logger", "dsc": "Lorg/slf4j/spi/LocationAwareLogger;"}]}, "org/eclipse/aether/impl/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/impl/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/impl/RepositoryConnectorProvider.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/impl/RepositoryConnectorProvider", "super": "java/lang/Object", "mthds": [{"nme": "newRepositoryConnector", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/spi/connector/RepositoryConnector;", "exs": ["org/eclipse/aether/transfer/NoRepositoryConnectorException"]}], "flds": []}, "org/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper$DefaultDependencyResolutionSkipper$CacheManager.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper$DefaultDependencyResolutionSkipper$CacheManager", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "isVersionConflict", "acc": 0, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Z"}, {"nme": "cacheWinner", "acc": 0, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)V"}, {"nme": "isDuplicate", "acc": 0, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Z"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper$1;)V"}], "flds": [{"acc": 18, "nme": "winners", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/graph/DependencyNode;>;"}, {"acc": 18, "nme": "winnerGAs", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/artifact/Artifact;>;"}]}, "org/eclipse/aether/internal/impl/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/internal/impl/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/impl/MetadataGenerator.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/impl/MetadataGenerator", "super": "java/lang/Object", "mthds": [{"nme": "prepare", "acc": 1025, "dsc": "(L<PERSON><PERSON>/util/Collection;)Ljava/util/Collection;", "sig": "(Ljava/util/Collection<+Lorg/eclipse/aether/artifact/Artifact;>;)Ljava/util/Collection<+Lorg/eclipse/aether/metadata/Metadata;>;"}, {"nme": "transformArtifact", "acc": 1025, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "finish", "acc": 1025, "dsc": "(L<PERSON><PERSON>/util/Collection;)Ljava/util/Collection;", "sig": "(Ljava/util/Collection<+Lorg/eclipse/aether/artifact/Artifact;>;)Ljava/util/Collection<+Lorg/eclipse/aether/metadata/Metadata;>;"}], "flds": []}, "org/eclipse/aether/internal/impl/synccontext/named/providers/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/internal/impl/synccontext/named/providers/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/internal/impl/DefaultArtifactResolver$ResolutionItem.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/internal/impl/DefaultArtifactResolver$ResolutionItem", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/artifact/Artifact;Ljava/util/concurrent/atomic/AtomicBoolean;Lorg/eclipse/aether/resolution/ArtifactResult;Lorg/eclipse/aether/repository/LocalArtifactResult;Lorg/eclipse/aether/repository/RemoteRepository;)V"}], "flds": [{"acc": 16, "nme": "trace", "dsc": "Lorg/eclipse/aether/RequestTrace;"}, {"acc": 16, "nme": "request", "dsc": "Lorg/eclipse/aether/resolution/ArtifactRequest;"}, {"acc": 16, "nme": "result", "dsc": "Lorg/eclipse/aether/resolution/ArtifactResult;"}, {"acc": 16, "nme": "local", "dsc": "Lorg/eclipse/aether/repository/LocalArtifactResult;"}, {"acc": 16, "nme": "repository", "dsc": "Lorg/eclipse/aether/repository/RemoteRepository;"}, {"acc": 16, "nme": "artifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}, {"acc": 16, "nme": "resolved", "dsc": "Ljava/util/concurrent/atomic/AtomicBoolean;"}, {"acc": 0, "nme": "download", "dsc": "Lorg/eclipse/aether/spi/connector/ArtifactDownload;"}, {"acc": 0, "nme": "updateCheck", "dsc": "Lorg/eclipse/aether/impl/UpdateCheck;", "sig": "Lorg/eclipse/aether/impl/UpdateCheck<Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/transfer/ArtifactTransferException;>;"}]}, "org/eclipse/aether/internal/impl/checksum/SummaryFileTrustedChecksumsSource$SummaryFileWriter.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/internal/impl/checksum/SummaryFileTrustedChecksumsSource$SummaryFileWriter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lorg/eclipse/aether/internal/impl/checksum/SummaryFileTrustedChecksumsSource;Ljava/util/concurrent/ConcurrentHashMap;Ljava/nio/file/Path;Z)V", "sig": "(Ljava/util/concurrent/ConcurrentHashMap<Ljava/nio/file/Path;Ljava/util/concurrent/ConcurrentHashMap<Ljava/lang/String;Ljava/lang/String;>;>;Ljava/nio/file/Path;Z)V"}, {"nme": "addTrustedArtifactChecksums", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/ArtifactRepository;Ljava/util/List;Ljava/util/Map;)V", "sig": "(Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/ArtifactRepository;Ljava/util/List<Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;>;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "lambda$addTrustedArtifactChecksums$0", "acc": 4098, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/Path;)Ljava/util/concurrent/ConcurrentHashMap;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/eclipse/aether/internal/impl/checksum/SummaryFileTrustedChecksumsSource;Ljava/util/concurrent/ConcurrentHashMap;Ljava/nio/file/Path;ZLorg/eclipse/aether/internal/impl/checksum/SummaryFileTrustedChecksumsSource$1;)V"}], "flds": [{"acc": 18, "nme": "cache", "dsc": "Ljava/util/concurrent/ConcurrentHashMap;", "sig": "Ljava/util/concurrent/ConcurrentHashMap<Ljava/nio/file/Path;Ljava/util/concurrent/ConcurrentHashMap<Ljava/lang/String;Ljava/lang/String;>;>;"}, {"acc": 18, "nme": "basedir", "dsc": "Ljava/nio/file/Path;"}, {"acc": 18, "nme": "originAware", "dsc": "Z"}, {"acc": 4112, "nme": "this$0", "dsc": "Lorg/eclipse/aether/internal/impl/checksum/SummaryFileTrustedChecksumsSource;"}]}, "org/eclipse/aether/internal/impl/checksum/DefaultChecksumAlgorithmFactorySelector.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/checksum/DefaultChecksumAlgorithmFactorySelector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;>;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "select", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;"}, {"nme": "selectList", "acc": 1, "dsc": "(L<PERSON><PERSON>/util/Collection;)Ljava/util/List;", "sig": "(Ljava/util/Collection<Ljava/lang/String;>;)Ljava/util/List<Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;>;"}, {"nme": "getChecksumAlgorithmFactories", "acc": 1, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;>;"}, {"nme": "isChecksumExtension", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "lambda$isChecksumExtension$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;)Z"}, {"nme": "lambda$isChecksumExtension$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;)Z"}], "flds": [{"acc": 18, "nme": "factories", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;>;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;"}]}, "org/eclipse/aether/internal/impl/filter/DefaultRemoteRepositoryFilterManager$Participants.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/internal/impl/filter/DefaultRemoteRepositoryFilterManager$Participants", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljava/util/Map;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter;>;)V"}, {"nme": "acceptArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter$Result;"}, {"nme": "acceptMetadata", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/metadata/Metadata;)Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter$Result;"}, {"nme": "lambda$acceptMetadata$1", "acc": 4106, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/metadata/Metadata;Ljava/util/Map$Entry;)Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter$Result;"}, {"nme": "lambda$acceptArtifact$0", "acc": 4106, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/artifact/Artifact;Ljava/util/Map$Entry;)Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter$Result;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Ljava/util/Map;Lorg/eclipse/aether/internal/impl/filter/DefaultRemoteRepositoryFilterManager$1;)V"}], "flds": [{"acc": 18, "nme": "participants", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter;>;"}]}, "org/eclipse/aether/internal/impl/collect/DefaultDependencyGraphTransformationContext.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/collect/DefaultDependencyGraphTransformationContext", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)V"}, {"nme": "getSession", "acc": 1, "dsc": "()Lorg/eclipse/aether/RepositorySystemSession;"}, {"nme": "get", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "put", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "session", "dsc": "Lorg/eclipse/aether/RepositorySystemSession;"}, {"acc": 18, "nme": "map", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;"}]}, "org/eclipse/aether/internal/impl/PrioritizedComponents.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/internal/impl/PrioritizedComponents", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(Ljava/util/Map;)V", "sig": "(Ljava/util/Map<**>;)V"}, {"nme": "add", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;F)V", "sig": "(TT;F)V"}, {"nme": "getImplClass", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Ljava/lang/Class;", "sig": "(<PERSON><PERSON><PERSON>/lang/Object;)Ljava/lang/Class<*>;"}, {"nme": "getConfigKeys", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)[Lja<PERSON>/lang/String;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)[Ljava/lang/String;"}, {"nme": "isEmpty", "acc": 1, "dsc": "()Z"}, {"nme": "getAll", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/internal/impl/PrioritizedComponent<TT;>;>;"}, {"nme": "getEnabled", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/internal/impl/PrioritizedComponent<TT;>;>;"}, {"nme": "list", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 26, "nme": "FACTORY_SUFFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "Factory"}, {"acc": 18, "nme": "configProps", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<**>;"}, {"acc": 18, "nme": "useInsertionOrder", "dsc": "Z"}, {"acc": 18, "nme": "components", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/internal/impl/PrioritizedComponent<TT;>;>;"}, {"acc": 2, "nme": "firstDisabled", "dsc": "I"}]}, "org/eclipse/aether/internal/impl/collect/df/DfDependencyCollector.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/collect/df/DfDependencyCollector", "super": "org/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/RemoteRepositoryManager;Lorg/eclipse/aether/impl/ArtifactDescriptorReader;Lorg/eclipse/aether/impl/VersionRangeResolver;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "doCollectDependencies", "acc": 4, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/internal/impl/collect/DataPool;Lorg/eclipse/aether/internal/impl/collect/DefaultDependencyCollectionContext;Lorg/eclipse/aether/internal/impl/collect/DefaultVersionFilterContext;Lorg/eclipse/aether/collection/CollectRequest;Lorg/eclipse/aether/graph/DependencyNode;Ljava/util/List;Ljava/util/List;Ljava/util/List;Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate$Results;)V", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/internal/impl/collect/DataPool;Lorg/eclipse/aether/internal/impl/collect/DefaultDependencyCollectionContext;Lorg/eclipse/aether/internal/impl/collect/DefaultVersionFilterContext;Lorg/eclipse/aether/collection/CollectRequest;Lorg/eclipse/aether/graph/DependencyNode;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate$Results;)V"}, {"nme": "process", "acc": 2, "dsc": "(Lorg/eclipse/aether/internal/impl/collect/df/DfDependencyCollector$Args;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate$Results;Ljava/util/List;Ljava/util/List;Lorg/eclipse/aether/collection/DependencySelector;Lorg/eclipse/aether/collection/DependencyManager;Lorg/eclipse/aether/collection/DependencyTraverser;Lorg/eclipse/aether/collection/VersionFilter;)V", "sig": "(Lorg/eclipse/aether/internal/impl/collect/df/DfDependencyCollector$Args;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate$Results;Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;Lorg/eclipse/aether/collection/DependencySelector;Lorg/eclipse/aether/collection/DependencyManager;Lorg/eclipse/aether/collection/DependencyTraverser;Lorg/eclipse/aether/collection/VersionFilter;)V"}, {"nme": "processDependency", "acc": 2, "dsc": "(Lorg/eclipse/aether/internal/impl/collect/df/DfDependencyCollector$Args;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate$Results;Ljava/util/List;Lorg/eclipse/aether/collection/DependencySelector;Lorg/eclipse/aether/collection/DependencyManager;Lorg/eclipse/aether/collection/DependencyTraverser;Lorg/eclipse/aether/collection/VersionFilter;Lorg/eclipse/aether/graph/Dependency;)V", "sig": "(Lorg/eclipse/aether/internal/impl/collect/df/DfDependencyCollector$Args;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate$Results;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;Lorg/eclipse/aether/collection/DependencySelector;Lorg/eclipse/aether/collection/DependencyManager;Lorg/eclipse/aether/collection/DependencyTraverser;Lorg/eclipse/aether/collection/VersionFilter;Lorg/eclipse/aether/graph/Dependency;)V"}, {"nme": "processDependency", "acc": 2, "dsc": "(Lorg/eclipse/aether/internal/impl/collect/df/DfDependencyCollector$Args;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate$Results;Ljava/util/List;Lorg/eclipse/aether/collection/DependencySelector;Lorg/eclipse/aether/collection/DependencyManager;Lorg/eclipse/aether/collection/DependencyTraverser;Lorg/eclipse/aether/collection/VersionFilter;Lorg/eclipse/aether/graph/Dependency;Ljava/util/List;Z)V", "sig": "(Lorg/eclipse/aether/internal/impl/collect/df/DfDependencyCollector$Args;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate$Results;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;Lorg/eclipse/aether/collection/DependencySelector;Lorg/eclipse/aether/collection/DependencyManager;Lorg/eclipse/aether/collection/DependencyTraverser;Lorg/eclipse/aether/collection/VersionFilter;Lorg/eclipse/aether/graph/Dependency;Ljava/util/List<Lorg/eclipse/aether/artifact/Artifact;>;Z)V"}, {"nme": "doRecurse", "acc": 2, "dsc": "(Lorg/eclipse/aether/internal/impl/collect/df/DfDependencyCollector$Args;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate$Results;Ljava/util/List;Lorg/eclipse/aether/collection/DependencySelector;Lorg/eclipse/aether/collection/DependencyManager;Lorg/eclipse/aether/collection/DependencyTraverser;Lorg/eclipse/aether/collection/VersionFilter;Lorg/eclipse/aether/graph/Dependency;Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;Lorg/eclipse/aether/graph/DefaultDependencyNode;)V", "sig": "(Lorg/eclipse/aether/internal/impl/collect/df/DfDependencyCollector$Args;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate$Results;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;Lorg/eclipse/aether/collection/DependencySelector;Lorg/eclipse/aether/collection/DependencyManager;Lorg/eclipse/aether/collection/DependencyTraverser;Lorg/eclipse/aether/collection/VersionFilter;Lorg/eclipse/aether/graph/Dependency;Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;Lorg/eclipse/aether/graph/DefaultDependencyNode;)V"}, {"nme": "getArtifactDescriptorResult", "acc": 2, "dsc": "(Lorg/eclipse/aether/internal/impl/collect/df/DfDependencyCollector$Args;Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate$Results;ZLorg/eclipse/aether/graph/Dependency;Lorg/eclipse/aether/resolution/ArtifactDescriptorRequest;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;"}, {"nme": "resolveCachedArtifactDescriptor", "acc": 2, "dsc": "(Lorg/eclipse/aether/internal/impl/collect/DataPool;Lorg/eclipse/aether/resolution/ArtifactDescriptorRequest;Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/graph/Dependency;Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate$Results;Lorg/eclipse/aether/internal/impl/collect/df/DfDependencyCollector$Args;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;"}], "flds": [{"acc": 25, "nme": "NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "df"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;", "vals": ["value", "df"]}]}, "org/eclipse/aether/internal/impl/DefaultDeployer.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/DefaultDeployer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/io/FileProcessor;Lorg/eclipse/aether/impl/RepositoryEventDispatcher;Lorg/eclipse/aether/impl/RepositoryConnectorProvider;Lorg/eclipse/aether/impl/RemoteRepositoryManager;Lorg/eclipse/aether/impl/UpdateCheckManager;Ljava/util/Set;Lorg/eclipse/aether/spi/synccontext/SyncContextFactory;Lorg/eclipse/aether/impl/OfflineController;)V", "sig": "(Lorg/eclipse/aether/spi/io/FileProcessor;Lorg/eclipse/aether/impl/RepositoryEventDispatcher;Lorg/eclipse/aether/impl/RepositoryConnectorProvider;Lorg/eclipse/aether/impl/RemoteRepositoryManager;Lorg/eclipse/aether/impl/UpdateCheckManager;Ljava/util/Set<Lorg/eclipse/aether/impl/MetadataGeneratorFactory;>;Lorg/eclipse/aether/spi/synccontext/SyncContextFactory;Lorg/eclipse/aether/impl/OfflineController;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "initService", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/locator/ServiceLocator;)V"}, {"nme": "setFileProcessor", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/io/FileProcessor;)Lorg/eclipse/aether/internal/impl/DefaultDeployer;"}, {"nme": "setRepositoryEventDispatcher", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/RepositoryEventDispatcher;)Lorg/eclipse/aether/internal/impl/DefaultDeployer;"}, {"nme": "setRepositoryConnectorProvider", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/RepositoryConnectorProvider;)Lorg/eclipse/aether/internal/impl/DefaultDeployer;"}, {"nme": "setRemoteRepositoryManager", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/RemoteRepositoryManager;)Lorg/eclipse/aether/internal/impl/DefaultDeployer;"}, {"nme": "setUpdateCheckManager", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/UpdateCheckManager;)Lorg/eclipse/aether/internal/impl/DefaultDeployer;"}, {"nme": "addMetadataGeneratorFactory", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/MetadataGeneratorFactory;)Lorg/eclipse/aether/internal/impl/DefaultDeployer;"}, {"nme": "setMetadataGeneratorFactories", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Lorg/eclipse/aether/internal/impl/DefaultDeployer;", "sig": "(Lja<PERSON>/util/Collection<Lorg/eclipse/aether/impl/MetadataGeneratorFactory;>;)Lorg/eclipse/aether/internal/impl/DefaultDeployer;"}, {"nme": "setSyncContextFactory", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/synccontext/SyncContextFactory;)Lorg/eclipse/aether/internal/impl/DefaultDeployer;"}, {"nme": "setOfflineController", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/OfflineController;)Lorg/eclipse/aether/internal/impl/DefaultDeployer;"}, {"nme": "deploy", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/deployment/DeployRequest;)Lorg/eclipse/aether/deployment/DeployResult;", "exs": ["org/eclipse/aether/deployment/DeploymentException"]}, {"nme": "deploy", "acc": 2, "dsc": "(Lorg/eclipse/aether/SyncContext;Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/deployment/DeployRequest;)Lorg/eclipse/aether/deployment/DeployResult;", "exs": ["org/eclipse/aether/deployment/DeploymentException"]}, {"nme": "getMetadataGenerators", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/deployment/DeployRequest;)Ljava/util/List;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/deployment/DeployRequest;)Ljava/util/List<+Lorg/eclipse/aether/impl/MetadataGenerator;>;"}, {"nme": "upload", "acc": 2, "dsc": "(Ljava/util/Collection;Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/spi/connector/RepositoryConnector;Lorg/eclipse/aether/internal/impl/DefaultDeployer$EventCatapult;)V", "sig": "(Ljava/util/Collection<Lorg/eclipse/aether/spi/connector/MetadataUpload;>;Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/spi/connector/RepositoryConnector;Lorg/eclipse/aether/internal/impl/DefaultDeployer$EventCatapult;)V", "exs": ["org/eclipse/aether/deployment/DeploymentException"]}, {"nme": "getPolicy", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/metadata/Metadata$Nature;)Lorg/eclipse/aether/repository/RepositoryPolicy;"}], "flds": [{"acc": 2, "nme": "fileProcessor", "dsc": "Lorg/eclipse/aether/spi/io/FileProcessor;"}, {"acc": 2, "nme": "repositoryEventDispatcher", "dsc": "Lorg/eclipse/aether/impl/RepositoryEventDispatcher;"}, {"acc": 2, "nme": "repositoryConnectorProvider", "dsc": "Lorg/eclipse/aether/impl/RepositoryConnectorProvider;"}, {"acc": 2, "nme": "remoteRepositoryManager", "dsc": "Lorg/eclipse/aether/impl/RemoteRepositoryManager;"}, {"acc": 2, "nme": "updateCheckManager", "dsc": "Lorg/eclipse/aether/impl/UpdateCheckManager;"}, {"acc": 2, "nme": "metadataFactories", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lorg/eclipse/aether/impl/MetadataGeneratorFactory;>;"}, {"acc": 2, "nme": "syncContextFactory", "dsc": "Lorg/eclipse/aether/spi/synccontext/SyncContextFactory;"}, {"acc": 2, "nme": "offlineController", "dsc": "Lorg/eclipse/aether/impl/OfflineController;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;"}]}, "org/eclipse/aether/internal/impl/WarnChecksumPolicy.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/internal/impl/WarnChecksumPolicy", "super": "org/eclipse/aether/internal/impl/AbstractChecksumPolicy", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/transfer/TransferResource;)V"}, {"nme": "onTransferChecksumFailure", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/ChecksumFailureException;)Z"}], "flds": []}, "org/eclipse/aether/internal/impl/filter/RemoteRepositoryFilterSourceSupport$SimpleResult.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/filter/RemoteRepositoryFilterSourceSupport$SimpleResult", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "isAccepted", "acc": 1, "dsc": "()Z"}, {"nme": "reasoning", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "accepted", "dsc": "Z"}, {"acc": 18, "nme": "reasoning", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/eclipse/aether/internal/impl/checksum/SparseDirectoryTrustedChecksumsSource.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/internal/impl/checksum/SparseDirectoryTrustedChecksumsSource", "super": "org/eclipse/aether/internal/impl/checksum/FileTrustedChecksumsSourceSupport", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/io/FileProcessor;Lorg/eclipse/aether/internal/impl/LocalPathComposer;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "doGetTrustedArtifactChecksums", "acc": 4, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/ArtifactRepository;Ljava/util/List;)Ljava/util/Map;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/ArtifactRepository;Ljava/util/List<Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;>;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "doGetTrustedArtifactChecksumsWriter", "acc": 4, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lorg/eclipse/aether/internal/impl/checksum/SparseDirectoryTrustedChecksumsSource$SparseDirectoryWriter;"}, {"nme": "calculateArtifactPath", "acc": 2, "dsc": "(ZLorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/ArtifactRepository;Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;)Ljava/lang/String;"}, {"nme": "doGetTrustedArtifactChecksumsWriter", "acc": 4164, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lorg/eclipse/aether/spi/checksums/TrustedChecksumsSource$Writer;"}, {"nme": "getTrustedArtifactChecksumsWriter", "acc": 4161, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lorg/eclipse/aether/spi/checksums/TrustedChecksumsSource$Writer;"}, {"nme": "getTrustedArtifactChecksums", "acc": 4161, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/ArtifactRepository;Ljava/util/List;)Ljava/util/Map;"}, {"nme": "access$100", "acc": 4104, "dsc": "(Lorg/eclipse/aether/internal/impl/checksum/SparseDirectoryTrustedChecksumsSource;ZLorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/ArtifactRepository;Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;)Ljava/lang/String;"}, {"nme": "access$200", "acc": 4104, "dsc": "(Lorg/eclipse/aether/internal/impl/checksum/SparseDirectoryTrustedChecksumsSource;)Lorg/eclipse/aether/spi/io/FileProcessor;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "sparseDirectory"}, {"acc": 26, "nme": "LOGGER", "dsc": "Lorg/slf4j/Logger;"}, {"acc": 18, "nme": "fileProcessor", "dsc": "Lorg/eclipse/aether/spi/io/FileProcessor;"}, {"acc": 18, "nme": "localPathComposer", "dsc": "Lorg/eclipse/aether/internal/impl/LocalPathComposer;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;", "vals": ["value", "sparseDirectory"]}]}, "org/eclipse/aether/impl/DependencyCollector.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/impl/DependencyCollector", "super": "java/lang/Object", "mthds": [{"nme": "collectDependencies", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/collection/CollectRequest;)Lorg/eclipse/aether/collection/CollectResult;", "exs": ["org/eclipse/aether/collection/DependencyCollectionException"]}], "flds": []}, "org/eclipse/aether/internal/impl/collect/DataPool$InternPool.class": {"ver": 52, "acc": 1536, "nme": "org/eclipse/aether/internal/impl/collect/DataPool$InternPool", "super": "java/lang/Object", "mthds": [{"nme": "get", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(TK;)TV;"}, {"nme": "intern", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(TK;TV;)TV;"}], "flds": []}, "org/eclipse/aether/internal/impl/slf4j/Slf4jLoggerFactory$Slf4jLogger.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/internal/impl/slf4j/Slf4jLoggerFactory$Slf4jLogger", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/slf4j/Logger;)V"}, {"nme": "isDebugEnabled", "acc": 1, "dsc": "()Z"}, {"nme": "debug", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "debug", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "is<PERSON>arn<PERSON>nabled", "acc": 1, "dsc": "()Z"}, {"nme": "warn", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "warn", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}], "flds": [{"acc": 18, "nme": "logger", "dsc": "Lorg/slf4j/Logger;"}]}, "org/eclipse/aether/impl/ArtifactResolver.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/impl/ArtifactResolver", "super": "java/lang/Object", "mthds": [{"nme": "resolveArtifact", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/ArtifactRequest;)Lorg/eclipse/aether/resolution/ArtifactResult;", "exs": ["org/eclipse/aether/resolution/ArtifactResolutionException"]}, {"nme": "resolveArtifacts", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection;)Ljava/util/List;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection<+Lorg/eclipse/aether/resolution/ArtifactRequest;>;)Ljava/util/List<Lorg/eclipse/aether/resolution/ArtifactResult;>;", "exs": ["org/eclipse/aether/resolution/ArtifactResolutionException"]}], "flds": []}, "org/eclipse/aether/internal/impl/checksum/SummaryFileTrustedChecksumsSource.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/internal/impl/checksum/SummaryFileTrustedChecksumsSource", "super": "org/eclipse/aether/internal/impl/checksum/FileTrustedChecksumsSourceSupport", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/internal/impl/LocalPathComposer;Lorg/eclipse/aether/impl/RepositorySystemLifecycle;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "doGetTrustedArtifactChecksums", "acc": 4, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/ArtifactRepository;Ljava/util/List;)Ljava/util/Map;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/ArtifactRepository;Ljava/util/List<Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;>;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "doGetTrustedArtifactChecksumsWriter", "acc": 4, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lorg/eclipse/aether/internal/impl/checksum/SummaryFileTrustedChecksumsSource$SummaryFileWriter;"}, {"nme": "summaryFile", "acc": 2, "dsc": "(Ljava/nio/file/Path;ZLjava/lang/String;Ljava/lang/String;)Ljava/nio/file/Path;"}, {"nme": "loadProvidedChecksums", "acc": 2, "dsc": "(Ljava/nio/file/Path;)Ljava/util/concurrent/ConcurrentHashMap;", "sig": "(Ljava/nio/file/Path;)Ljava/util/concurrent/ConcurrentHashMap<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "saveRecordedLines", "acc": 2, "dsc": "()V"}, {"nme": "doGetTrustedArtifactChecksumsWriter", "acc": 4164, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lorg/eclipse/aether/spi/checksums/TrustedChecksumsSource$Writer;"}, {"nme": "getTrustedArtifactChecksumsWriter", "acc": 4161, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lorg/eclipse/aether/spi/checksums/TrustedChecksumsSource$Writer;"}, {"nme": "getTrustedArtifactChecksums", "acc": 4161, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/ArtifactRepository;Ljava/util/List;)Ljava/util/Map;"}, {"nme": "lambda$saveRecordedLines$2", "acc": 4106, "dsc": "(Ljava/util/concurrent/ConcurrentHashMap;Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "lambda$saveRecordedLines$1", "acc": 4106, "dsc": "(Ljava/util/Map$Entry;)Ljava/lang/String;"}, {"nme": "lambda$doGetTrustedArtifactChecksums$0", "acc": 4098, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/Path;)Ljava/util/concurrent/ConcurrentHashMap;"}, {"nme": "access$100", "acc": 4104, "dsc": "(Lorg/eclipse/aether/internal/impl/checksum/SummaryFileTrustedChecksumsSource;)Lorg/eclipse/aether/internal/impl/LocalPathComposer;"}, {"nme": "access$200", "acc": 4104, "dsc": "(Lorg/eclipse/aether/internal/impl/checksum/SummaryFileTrustedChecksumsSource;Ljava/nio/file/Path;ZLjava/lang/String;Ljava/lang/String;)Ljava/nio/file/Path;"}, {"nme": "access$300", "acc": 4104, "dsc": "(Lorg/eclipse/aether/internal/impl/checksum/SummaryFileTrustedChecksumsSource;)Ljava/util/concurrent/ConcurrentHashMap;"}, {"nme": "access$400", "acc": 4104, "dsc": "()Lorg/slf4j/Logger;"}, {"nme": "access$500", "acc": 4104, "dsc": "(Lorg/eclipse/aether/internal/impl/checksum/SummaryFileTrustedChecksumsSource;Ljava/nio/file/Path;)Ljava/util/concurrent/ConcurrentHashMap;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "summaryFile"}, {"acc": 26, "nme": "CHECKSUMS_FILE_PREFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "checksums"}, {"acc": 26, "nme": "LOGGER", "dsc": "Lorg/slf4j/Logger;"}, {"acc": 18, "nme": "localPathComposer", "dsc": "Lorg/eclipse/aether/internal/impl/LocalPathComposer;"}, {"acc": 18, "nme": "repositorySystemLifecycle", "dsc": "Lorg/eclipse/aether/impl/RepositorySystemLifecycle;"}, {"acc": 18, "nme": "checksums", "dsc": "Ljava/util/concurrent/ConcurrentHashMap;", "sig": "Ljava/util/concurrent/ConcurrentHashMap<Ljava/nio/file/Path;Ljava/util/concurrent/ConcurrentHashMap<Ljava/lang/String;Ljava/lang/String;>;>;"}, {"acc": 18, "nme": "changedChecksums", "dsc": "Ljava/util/concurrent/ConcurrentHashMap;", "sig": "Ljava/util/concurrent/ConcurrentHashMap<Ljava/nio/file/Path;Ljava/lang/Boolean;>;"}, {"acc": 18, "nme": "onShutdownHandlerRegistered", "dsc": "Ljava/util/concurrent/atomic/AtomicBoolean;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;", "vals": ["value", "summaryFile"]}]}, "org/eclipse/aether/internal/impl/filter/FilteringRepositoryConnector.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/internal/impl/filter/FilteringRepositoryConnector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/spi/connector/RepositoryConnector;Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter;)V"}, {"nme": "close", "acc": 1, "dsc": "()V"}, {"nme": "get", "acc": 1, "dsc": "(Lja<PERSON>/util/Collection;Ljava/util/Collection;)V", "sig": "(Ljava/util/Collection<+Lorg/eclipse/aether/spi/connector/ArtifactDownload;>;Ljava/util/Collection<+Lorg/eclipse/aether/spi/connector/MetadataDownload;>;)V"}, {"nme": "put", "acc": 1, "dsc": "(Lja<PERSON>/util/Collection;Ljava/util/Collection;)V", "sig": "(Ljava/util/Collection<+Lorg/eclipse/aether/spi/connector/ArtifactUpload;>;Ljava/util/Collection<+Lorg/eclipse/aether/spi/connector/MetadataUpload;>;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "remoteRepository", "dsc": "Lorg/eclipse/aether/repository/RemoteRepository;"}, {"acc": 18, "nme": "delegate", "dsc": "Lorg/eclipse/aether/spi/connector/RepositoryConnector;"}, {"acc": 18, "nme": "remoteRepositoryFilter", "dsc": "Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter;"}]}, "org/eclipse/aether/internal/impl/checksum/Sha512ChecksumAlgorithmFactory.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/checksum/Sha512ChecksumAlgorithmFactory", "super": "org/eclipse/aether/internal/impl/checksum/MessageDigestChecksumAlgorithmFactorySupport", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}], "flds": [{"acc": 25, "nme": "NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "SHA-512"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;", "vals": ["value", "SHA-512"]}]}, "org/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate.class": {"ver": 52, "acc": 1057, "nme": "org/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 131076, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 4, "dsc": "(Lorg/eclipse/aether/impl/RemoteRepositoryManager;Lorg/eclipse/aether/impl/ArtifactDescriptorReader;Lorg/eclipse/aether/impl/VersionRangeResolver;)V"}, {"nme": "initService", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/locator/ServiceLocator;)V"}, {"nme": "setRemoteRepositoryManager", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/RemoteRepositoryManager;)Lorg/eclipse/aether/impl/DependencyCollector;"}, {"nme": "setArtifactDescriptorReader", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/ArtifactDescriptorReader;)Lorg/eclipse/aether/impl/DependencyCollector;"}, {"nme": "setVersionRangeResolver", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/VersionRangeResolver;)Lorg/eclipse/aether/impl/DependencyCollector;"}, {"nme": "collectDependencies", "acc": 17, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/collection/CollectRequest;)Lorg/eclipse/aether/collection/CollectResult;", "exs": ["org/eclipse/aether/collection/DependencyCollectionException"]}, {"nme": "collectStepTrace", "acc": 4, "dsc": "(Lorg/eclipse/aether/RequestTrace;Lja<PERSON>/lang/String;Ljava/util/List;Lorg/eclipse/aether/graph/Dependency;)Lorg/eclipse/aether/RequestTrace;", "sig": "(Lorg/eclipse/aether/RequestTrace;Ljava/lang/String;Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;Lorg/eclipse/aether/graph/Dependency;)Lorg/eclipse/aether/RequestTrace;"}, {"nme": "doCollectDependencies", "acc": 1028, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/internal/impl/collect/DataPool;Lorg/eclipse/aether/internal/impl/collect/DefaultDependencyCollectionContext;Lorg/eclipse/aether/internal/impl/collect/DefaultVersionFilterContext;Lorg/eclipse/aether/collection/CollectRequest;Lorg/eclipse/aether/graph/DependencyNode;Ljava/util/List;Ljava/util/List;Ljava/util/List;Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate$Results;)V", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/internal/impl/collect/DataPool;Lorg/eclipse/aether/internal/impl/collect/DefaultDependencyCollectionContext;Lorg/eclipse/aether/internal/impl/collect/DefaultVersionFilterContext;Lorg/eclipse/aether/collection/CollectRequest;Lorg/eclipse/aether/graph/DependencyNode;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate$Results;)V"}, {"nme": "optimizeSession", "acc": 4, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lorg/eclipse/aether/RepositorySystemSession;"}, {"nme": "mergeDeps", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/util/List;)<PERSON>java/util/List;", "sig": "(Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;)Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;"}, {"nme": "getId", "acc": 12, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lja<PERSON>/lang/String;"}, {"nme": "createDependencyNode", "acc": 12, "dsc": "(Lja<PERSON>/util/List;Lorg/eclipse/aether/internal/impl/collect/PremanagedDependency;Lorg/eclipse/aether/resolution/VersionRangeResult;Lorg/eclipse/aether/version/Version;Lorg/eclipse/aether/graph/Dependency;Ljava/util/Collection;Ljava/util/List;Ljava/lang/String;)Lorg/eclipse/aether/graph/DefaultDependencyNode;", "sig": "(Ljava/util/List<Lorg/eclipse/aether/artifact/Artifact;>;Lorg/eclipse/aether/internal/impl/collect/PremanagedDependency;Lorg/eclipse/aether/resolution/VersionRangeResult;Lorg/eclipse/aether/version/Version;Lorg/eclipse/aether/graph/Dependency;Ljava/util/Collection<Lorg/eclipse/aether/artifact/Artifact;>;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;Ljava/lang/String;)Lorg/eclipse/aether/graph/DefaultDependencyNode;"}, {"nme": "createDependencyNode", "acc": 12, "dsc": "(<PERSON><PERSON><PERSON>/util/List;Lorg/eclipse/aether/internal/impl/collect/PremanagedDependency;Lorg/eclipse/aether/resolution/VersionRangeResult;Lorg/eclipse/aether/version/Version;Lorg/eclipse/aether/graph/Dependency;Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;Lorg/eclipse/aether/graph/DependencyNode;)Lorg/eclipse/aether/graph/DefaultDependencyNode;", "sig": "(<PERSON>ja<PERSON>/util/List<Lorg/eclipse/aether/artifact/Artifact;>;Lorg/eclipse/aether/internal/impl/collect/PremanagedDependency;Lorg/eclipse/aether/resolution/VersionRangeResult;Lorg/eclipse/aether/version/Version;Lorg/eclipse/aether/graph/Dependency;Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;Lorg/eclipse/aether/graph/DependencyNode;)Lorg/eclipse/aether/graph/DefaultDependencyNode;"}, {"nme": "createArtifactDescriptorRequest", "acc": 12, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/eclipse/aether/RequestTrace;Ljava/util/List;Lorg/eclipse/aether/graph/Dependency;)Lorg/eclipse/aether/resolution/ArtifactDescriptorRequest;", "sig": "(Lja<PERSON>/lang/String;Lorg/eclipse/aether/RequestTrace;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;Lorg/eclipse/aether/graph/Dependency;)Lorg/eclipse/aether/resolution/ArtifactDescriptorRequest;"}, {"nme": "createVersionRangeRequest", "acc": 12, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/eclipse/aether/RequestTrace;Ljava/util/List;Lorg/eclipse/aether/graph/Dependency;)Lorg/eclipse/aether/resolution/VersionRangeRequest;", "sig": "(Lja<PERSON>/lang/String;Lorg/eclipse/aether/RequestTrace;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;Lorg/eclipse/aether/graph/Dependency;)Lorg/eclipse/aether/resolution/VersionRangeRequest;"}, {"nme": "cachedResolveRangeResult", "acc": 4, "dsc": "(Lorg/eclipse/aether/resolution/VersionRangeRequest;Lorg/eclipse/aether/internal/impl/collect/DataPool;Lorg/eclipse/aether/RepositorySystemSession;)Lorg/eclipse/aether/resolution/VersionRangeResult;", "exs": ["org/eclipse/aether/resolution/VersionRangeResolutionException"]}, {"nme": "isLackingDescriptor", "acc": 12, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Z"}, {"nme": "getRemoteRepositories", "acc": 12, "dsc": "(Lorg/eclipse/aether/repository/ArtifactRepository;Ljava/util/List;)Ljava/util/List;", "sig": "(Lorg/eclipse/aether/repository/ArtifactRepository;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;)Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"nme": "filterVersions", "acc": 12, "dsc": "(Lorg/eclipse/aether/graph/Dependency;Lorg/eclipse/aether/resolution/VersionRangeResult;Lorg/eclipse/aether/collection/VersionFilter;Lorg/eclipse/aether/internal/impl/collect/DefaultVersionFilterContext;)Ljava/util/List;", "sig": "(Lorg/eclipse/aether/graph/Dependency;Lorg/eclipse/aether/resolution/VersionRangeResult;Lorg/eclipse/aether/collection/VersionFilter;Lorg/eclipse/aether/internal/impl/collect/DefaultVersionFilterContext;)Ljava/util/List<+Lorg/eclipse/aether/version/Version;>;", "exs": ["org/eclipse/aether/resolution/VersionRangeResolutionException"]}], "flds": [{"acc": 28, "nme": "CONFIG_PROP_MAX_EXCEPTIONS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.dependencyCollector.maxExceptions"}, {"acc": 28, "nme": "CONFIG_PROP_MAX_EXCEPTIONS_DEFAULT", "dsc": "I", "val": 50}, {"acc": 28, "nme": "CONFIG_PROP_MAX_CYCLES", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.dependencyCollector.maxCycles"}, {"acc": 28, "nme": "CONFIG_PROP_MAX_CYCLES_DEFAULT", "dsc": "I", "val": 10}, {"acc": 20, "nme": "logger", "dsc": "Lorg/slf4j/Logger;"}, {"acc": 4, "nme": "remoteRepositoryManager", "dsc": "Lorg/eclipse/aether/impl/RemoteRepositoryManager;"}, {"acc": 4, "nme": "descriptor<PERSON><PERSON><PERSON>", "dsc": "Lorg/eclipse/aether/impl/ArtifactDescriptorReader;"}, {"acc": 4, "nme": "versionRangeResolver", "dsc": "Lorg/eclipse/aether/impl/VersionRangeResolver;"}]}, "org/eclipse/aether/internal/impl/checksum/Sha1ChecksumAlgorithmFactory.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/checksum/Sha1ChecksumAlgorithmFactory", "super": "org/eclipse/aether/internal/impl/checksum/MessageDigestChecksumAlgorithmFactorySupport", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}], "flds": [{"acc": 25, "nme": "NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "SHA-1"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;", "vals": ["value", "SHA-1"]}]}, "org/eclipse/aether/internal/impl/collect/CachingArtifactTypeRegistry.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/collect/CachingArtifactTypeRegistry", "super": "java/lang/Object", "mthds": [{"nme": "newInstance", "acc": 9, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lorg/eclipse/aether/artifact/ArtifactTypeRegistry;"}, {"nme": "newInstance", "acc": 9, "dsc": "(Lorg/eclipse/aether/artifact/ArtifactTypeRegistry;)Lorg/eclipse/aether/artifact/ArtifactTypeRegistry;"}, {"nme": "<init>", "acc": 2, "dsc": "(Lorg/eclipse/aether/artifact/ArtifactTypeRegistry;)V"}, {"nme": "get", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/artifact/ArtifactType;"}], "flds": [{"acc": 18, "nme": "delegate", "dsc": "Lorg/eclipse/aether/artifact/ArtifactTypeRegistry;"}, {"acc": 18, "nme": "types", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/artifact/ArtifactType;>;"}]}, "org/eclipse/aether/internal/impl/checksum/SparseDirectoryTrustedChecksumsSource$1.class": {"ver": 52, "acc": 4128, "nme": "org/eclipse/aether/internal/impl/checksum/SparseDirectoryTrustedChecksumsSource$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/internal/impl/DefaultFileProcessor.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/DefaultFileProcessor", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "mkdirs", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)Z"}, {"nme": "write", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;Ljava/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lja<PERSON>/io/File;Ljava/io/InputStream;)V", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 1, "dsc": "(Ljava/io/File;Ljava/io/File;)V", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 1, "dsc": "(Ljava/io/File;Ljava/io/File;Lorg/eclipse/aether/spi/io/FileProcessor$ProgressListener;)J", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 2, "dsc": "(Ljava/io/OutputStream;Ljava/io/InputStream;Lorg/eclipse/aether/spi/io/FileProcessor$ProgressListener;)J", "exs": ["java/io/IOException"]}, {"nme": "move", "acc": 1, "dsc": "(Ljava/io/File;Ljava/io/File;)V", "exs": ["java/io/IOException"]}, {"nme": "readChecksum", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)Ljava/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "writeChecksum", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;Ljava/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "lambda$write$1", "acc": 4106, "dsc": "(Ljava/io/InputStream;Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "lambda$write$0", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}], "flds": [], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;"}]}, "org/eclipse/aether/internal/impl/DefaultRemoteRepositoryManager.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/DefaultRemoteRepositoryManager", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/UpdatePolicyAnalyzer;Lorg/eclipse/aether/spi/connector/checksum/ChecksumPolicyProvider;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "initService", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/locator/ServiceLocator;)V"}, {"nme": "setUpdatePolicyAnalyzer", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/UpdatePolicyAnalyzer;)Lorg/eclipse/aether/internal/impl/DefaultRemoteRepositoryManager;"}, {"nme": "setChecksumPolicyProvider", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/connector/checksum/ChecksumPolicyProvider;)Lorg/eclipse/aether/internal/impl/DefaultRemoteRepositoryManager;"}, {"nme": "aggregateRepositories", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/List;<PERSON><PERSON><PERSON>/util/List;Z)Ljava/util/List;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;Z)Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"nme": "logMirror", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/repository/RemoteRepository;)V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Ljava/lang/String;"}, {"nme": "mergeMirrors", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/repository/RemoteRepository;"}, {"nme": "getPolicy", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;ZZ)Lorg/eclipse/aether/repository/RepositoryPolicy;"}, {"nme": "merge", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RepositoryPolicy;Lorg/eclipse/aether/repository/RepositoryPolicy;Z)Lorg/eclipse/aether/repository/RepositoryPolicy;"}, {"nme": "merge", "acc": 2, "dsc": "(Lorg/eclipse/aether/repository/RepositoryPolicy;Ljava/lang/String;Ljava/lang/String;)Lorg/eclipse/aether/repository/RepositoryPolicy;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "LOGGER", "dsc": "Lorg/slf4j/Logger;"}, {"acc": 2, "nme": "updatePolicyAnalyzer", "dsc": "Lorg/eclipse/aether/impl/UpdatePolicyAnalyzer;"}, {"acc": 2, "nme": "checksumPolicyProvider", "dsc": "Lorg/eclipse/aether/spi/connector/checksum/ChecksumPolicyProvider;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;"}]}, "org/eclipse/aether/internal/impl/collect/DefaultDependencyCycle.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/internal/impl/collect/DefaultDependencyCycle", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;ILorg/eclipse/aether/graph/Dependency;)V", "sig": "(<PERSON><PERSON><PERSON>/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;ILorg/eclipse/aether/graph/Dependency;)V"}, {"nme": "getPrecedingDependencies", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;"}, {"nme": "getCyclicDependencies", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;"}, {"nme": "find", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/List;Lorg/eclipse/aether/artifact/Artifact;)I", "sig": "(<PERSON><PERSON><PERSON>/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;Lorg/eclipse/aether/artifact/Artifact;)I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "dependencies", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;"}, {"acc": 18, "nme": "cycleEntry", "dsc": "I"}]}, "org/eclipse/aether/internal/impl/collect/CollectStepDataImpl.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/internal/impl/collect/CollectStepDataImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/List;Lorg/eclipse/aether/graph/Dependency;)V", "sig": "(Lja<PERSON>/lang/String;Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;Lorg/eclipse/aether/graph/Dependency;)V"}, {"nme": "getContext", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;"}, {"nme": "getNode", "acc": 1, "dsc": "()Lorg/eclipse/aether/graph/Dependency;"}], "flds": [{"acc": 18, "nme": "context", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "path", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;"}, {"acc": 18, "nme": "node", "dsc": "Lorg/eclipse/aether/graph/Dependency;"}]}, "org/eclipse/aether/internal/impl/DefaultUpdatePolicyAnalyzer.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/DefaultUpdatePolicyAnalyzer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getEffectiveUpdatePolicy", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "ordinalOfUpdatePolicy", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "isUpdatedRequired", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;JLjava/lang/String;)Z"}, {"nme": "getMinutes", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "LOGGER", "dsc": "Lorg/slf4j/Logger;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;"}]}, "org/eclipse/aether/internal/impl/collect/DataPool$HardInternPool.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/internal/impl/collect/DataPool$HardInternPool", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "get", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(TK;)TV;"}, {"nme": "intern", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(TK;TV;)TV;"}, {"nme": "lambda$intern$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/eclipse/aether/internal/impl/collect/DataPool$1;)V"}], "flds": [{"acc": 18, "nme": "map", "dsc": "Ljava/util/concurrent/ConcurrentHashMap;", "sig": "Ljava/util/concurrent/ConcurrentHashMap<TK;TV;>;"}]}, "org/eclipse/aether/internal/impl/DefaultRemoteRepositoryManager$LoggedMirror.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/internal/impl/DefaultRemoteRepositoryManager$LoggedMirror", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/repository/RemoteRepository;)V"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "keys", "dsc": "[<PERSON><PERSON><PERSON>/lang/Object;"}]}, "org/eclipse/aether/internal/impl/synccontext/named/providers/FileStaticNameMapperProvider.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/synccontext/named/providers/FileStaticNameMapperProvider", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "get", "acc": 1, "dsc": "()Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;"}, {"nme": "get", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 18, "nme": "mapper", "dsc": "Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;", "vals": ["value", "file-static"]}]}, "org/eclipse/aether/internal/impl/DefaultDeployer$ArtifactUploadListener.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/internal/impl/DefaultDeployer$ArtifactUploadListener", "super": "org/eclipse/aether/internal/impl/SafeTransferListener", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/internal/impl/DefaultDeployer$EventCatapult;Lorg/eclipse/aether/spi/connector/ArtifactUpload;)V"}, {"nme": "transferInitiated", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V", "exs": ["org/eclipse/aether/transfer/TransferCancelledException"]}, {"nme": "transferFailed", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V"}, {"nme": "transferSucceeded", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferEvent;)V"}], "flds": [{"acc": 18, "nme": "catapult", "dsc": "Lorg/eclipse/aether/internal/impl/DefaultDeployer$EventCatapult;"}, {"acc": 18, "nme": "transfer", "dsc": "Lorg/eclipse/aether/spi/connector/ArtifactUpload;"}]}, "org/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper$DependencyResolutionResult.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper$DependencyResolutionResult", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)V"}, {"nme": "toResolve", "acc": 0, "dsc": "()Z"}], "flds": [{"acc": 0, "nme": "current", "dsc": "Lorg/eclipse/aether/graph/DependencyNode;"}, {"acc": 0, "nme": "skippedAsVersionConflict", "dsc": "Z"}, {"acc": 0, "nme": "skippedAsDuplicate", "dsc": "Z"}, {"acc": 0, "nme": "resolve", "dsc": "Z"}, {"acc": 0, "nme": "forceResolution", "dsc": "Z"}]}, "org/eclipse/aether/internal/impl/synccontext/named/NamedLockFactoryAdapter$AdaptedLockSyncContext.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/internal/impl/synccontext/named/NamedLockFactoryAdapter$AdaptedLockSyncContext", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;ZLorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;Lorg/eclipse/aether/named/NamedLockFactory;)V"}, {"nme": "getTime", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)J"}, {"nme": "getTimeUnit", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Ljava/util/concurrent/TimeUnit;"}, {"nme": "getRetry", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)I"}, {"nme": "getRetryWait", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)J"}, {"nme": "acquire", "acc": 1, "dsc": "(Lja<PERSON>/util/Collection;Ljava/util/Collection;)V", "sig": "(Ljava/util/Collection<+Lorg/eclipse/aether/artifact/Artifact;>;Ljava/util/Collection<+Lorg/eclipse/aether/metadata/Metadata;>;)V"}, {"nme": "closeAll", "acc": 2, "dsc": "()V"}, {"nme": "close", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;ZLorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;Lorg/eclipse/aether/named/NamedLockFactory;Lorg/eclipse/aether/internal/impl/synccontext/named/NamedLockFactoryAdapter$1;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "LOGGER", "dsc": "Lorg/slf4j/Logger;"}, {"acc": 18, "nme": "session", "dsc": "Lorg/eclipse/aether/RepositorySystemSession;"}, {"acc": 18, "nme": "shared", "dsc": "Z"}, {"acc": 18, "nme": "lockNaming", "dsc": "Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;"}, {"acc": 18, "nme": "namedLockFactory", "dsc": "Lorg/eclipse/aether/named/NamedLockFactory;"}, {"acc": 18, "nme": "time", "dsc": "J"}, {"acc": 18, "nme": "timeUnit", "dsc": "Ljava/util/concurrent/TimeUnit;"}, {"acc": 18, "nme": "retry", "dsc": "I"}, {"acc": 18, "nme": "retryWait", "dsc": "J"}, {"acc": 18, "nme": "locks", "dsc": "<PERSON><PERSON><PERSON>/util/Deque;", "sig": "<PERSON><PERSON><PERSON>/util/Deque<Lorg/eclipse/aether/named/NamedLock;>;"}]}, "org/eclipse/aether/internal/impl/DefaultLocalPathPrefixComposerFactory.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/internal/impl/DefaultLocalPathPrefixComposerFactory", "super": "org/eclipse/aether/internal/impl/LocalPathPrefixComposerFactorySupport", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "createComposer", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lorg/eclipse/aether/internal/impl/LocalPathPrefixComposer;"}], "flds": [], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;"}]}, "org/eclipse/aether/internal/impl/DefaultLocalPathPrefixComposerFactory$1.class": {"ver": 52, "acc": 4128, "nme": "org/eclipse/aether/internal/impl/DefaultLocalPathPrefixComposerFactory$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/internal/impl/filter/PrefixesRemoteRepositoryFilterSource.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/internal/impl/filter/PrefixesRemoteRepositoryFilterSource", "super": "org/eclipse/aether/internal/impl/filter/RemoteRepositoryFilterSourceSupport", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/connector/layout/RepositoryLayoutProvider;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "getRemoteRepositoryFilter", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter;"}, {"nme": "cacheLayout", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/spi/connector/layout/RepositoryLayout;"}, {"nme": "cacheNode", "acc": 2, "dsc": "(Ljava/nio/file/Path;Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/internal/impl/filter/PrefixesRemoteRepositoryFilterSource$Node;"}, {"nme": "loadRepositoryPrefixes", "acc": 2, "dsc": "(Ljava/nio/file/Path;Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/internal/impl/filter/PrefixesRemoteRepositoryFilterSource$Node;"}, {"nme": "elementsOf", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "lambda$elementsOf$2", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "lambda$cacheNode$1", "acc": 4098, "dsc": "(Ljava/nio/file/Path;Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/internal/impl/filter/PrefixesRemoteRepositoryFilterSource$Node;"}, {"nme": "lambda$cacheLayout$0", "acc": 4098, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/spi/connector/layout/RepositoryLayout;"}, {"nme": "access$200", "acc": 4104, "dsc": "(Lorg/eclipse/aether/internal/impl/filter/PrefixesRemoteRepositoryFilterSource;Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/spi/connector/layout/RepositoryLayout;"}, {"nme": "access$300", "acc": 4104, "dsc": "(Lorg/eclipse/aether/internal/impl/filter/PrefixesRemoteRepositoryFilterSource;Ljava/nio/file/Path;Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/internal/impl/filter/PrefixesRemoteRepositoryFilterSource$Node;"}, {"nme": "access$400", "acc": 4104, "dsc": "()Lorg/eclipse/aether/internal/impl/filter/PrefixesRemoteRepositoryFilterSource$Node;"}, {"nme": "access$500", "acc": 4104, "dsc": "()Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter$Result;"}, {"nme": "access$600", "acc": 4104, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "prefixes"}, {"acc": 24, "nme": "PREFIXES_FILE_PREFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "prefixes-"}, {"acc": 24, "nme": "PREFIXES_FILE_SUFFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": ".txt"}, {"acc": 26, "nme": "LOGGER", "dsc": "Lorg/slf4j/Logger;"}, {"acc": 18, "nme": "repositoryLayoutProvider", "dsc": "Lorg/eclipse/aether/spi/connector/layout/RepositoryLayoutProvider;"}, {"acc": 18, "nme": "prefixes", "dsc": "Ljava/util/concurrent/ConcurrentHashMap;", "sig": "Ljava/util/concurrent/ConcurrentHashMap<Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/internal/impl/filter/PrefixesRemoteRepositoryFilterSource$Node;>;"}, {"acc": 18, "nme": "layouts", "dsc": "Ljava/util/concurrent/ConcurrentHashMap;", "sig": "Ljava/util/concurrent/ConcurrentHashMap<Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/spi/connector/layout/RepositoryLayout;>;"}, {"acc": 26, "nme": "NOT_PRESENT_NODE", "dsc": "Lorg/eclipse/aether/internal/impl/filter/PrefixesRemoteRepositoryFilterSource$Node;"}, {"acc": 26, "nme": "NOT_PRESENT_RESULT", "dsc": "Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter$Result;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;", "vals": ["value", "prefixes"]}]}, "org/eclipse/aether/internal/impl/synccontext/named/providers/FileHashingGAVNameMapperProvider.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/synccontext/named/providers/FileHashingGAVNameMapperProvider", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "get", "acc": 1, "dsc": "()Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;"}, {"nme": "get", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 18, "nme": "mapper", "dsc": "Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;", "vals": ["value", "file-hgav"]}]}, "org/eclipse/aether/impl/DefaultServiceLocator$ErrorHandler.class": {"ver": 52, "acc": 1057, "nme": "org/eclipse/aether/impl/DefaultServiceLocator$ErrorHandler", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "serviceCreationFailed", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/Class;Lja<PERSON>/lang/Throwable;)V", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/Class<*>;Ljava/lang/Throwable;)V"}], "flds": []}, "org/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper$1.class": {"ver": 52, "acc": 4128, "nme": "org/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/internal/impl/collect/DataPool$GraphKey.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/internal/impl/collect/DataPool$GraphKey", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/util/List;Lorg/eclipse/aether/collection/DependencySelector;Lorg/eclipse/aether/collection/DependencyManager;Lorg/eclipse/aether/collection/DependencyTraverser;Lorg/eclipse/aether/collection/VersionFilter;)V", "sig": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;Lorg/eclipse/aether/collection/DependencySelector;Lorg/eclipse/aether/collection/DependencyManager;Lorg/eclipse/aether/collection/DependencyTraverser;Lorg/eclipse/aether/collection/VersionFilter;)V"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "artifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}, {"acc": 18, "nme": "repositories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"acc": 18, "nme": "selector", "dsc": "Lorg/eclipse/aether/collection/DependencySelector;"}, {"acc": 18, "nme": "manager", "dsc": "Lorg/eclipse/aether/collection/DependencyManager;"}, {"acc": 18, "nme": "traverser", "dsc": "Lorg/eclipse/aether/collection/DependencyTraverser;"}, {"acc": 18, "nme": "filter", "dsc": "Lorg/eclipse/aether/collection/VersionFilter;"}, {"acc": 18, "nme": "hashCode", "dsc": "I"}]}, "org/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper$DefaultDependencyResolutionSkipper.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper$DefaultDependencyResolutionSkipper", "super": "org/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "skipResolution", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;<PERSON>ja<PERSON>/util/List;)Z", "sig": "(Lorg/eclipse/aether/graph/DependencyNode;Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;)Z"}, {"nme": "cache", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;Lja<PERSON>/util/List;)V", "sig": "(Lorg/eclipse/aether/graph/DependencyNode;Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;)V"}, {"nme": "close", "acc": 1, "dsc": "()V"}, {"nme": "getResults", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Lorg/eclipse/aether/graph/DependencyNode;Lorg/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper$DependencyResolutionResult;>;"}, {"nme": "lambda$close$4", "acc": 4106, "dsc": "(Ljava/util/Map$Entry;)Z"}, {"nme": "lambda$close$3", "acc": 4106, "dsc": "(Ljava/util/Map$Entry;)Z"}, {"nme": "lambda$close$2", "acc": 4106, "dsc": "(Ljava/util/Map$Entry;)Z"}, {"nme": "lambda$close$1", "acc": 4106, "dsc": "(Ljava/util/Map$Entry;)Z"}, {"nme": "lambda$cache$0", "acc": 4098, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "LOGGER", "dsc": "Lorg/slf4j/Logger;"}, {"acc": 18, "nme": "results", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Lorg/eclipse/aether/graph/DependencyNode;Lorg/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper$DependencyResolutionResult;>;"}, {"acc": 18, "nme": "cacheManager", "dsc": "Lorg/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper$DefaultDependencyResolutionSkipper$CacheManager;"}, {"acc": 18, "nme": "coordinateManager", "dsc": "Lorg/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper$DefaultDependencyResolutionSkipper$CoordinateManager;"}]}, "org/eclipse/aether/internal/impl/collect/PremanagedDependency.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/collect/PremanagedDependency", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Boolean;Lja<PERSON>/util/Collection;Ljava/util/Map;ILorg/eclipse/aether/graph/Dependency;Z)V", "sig": "(Lja<PERSON>/lang/String;<PERSON>java/lang/String;<PERSON>java/lang/Bo<PERSON>an;Ljava/util/Collection<Lorg/eclipse/aether/graph/Exclusion;>;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;ILorg/eclipse/aether/graph/Dependency;Z)V"}, {"nme": "create", "acc": 9, "dsc": "(Lorg/eclipse/aether/collection/DependencyManager;Lorg/eclipse/aether/graph/Dependency;ZZ)Lorg/eclipse/aether/internal/impl/collect/PremanagedDependency;"}, {"nme": "getManagedDependency", "acc": 1, "dsc": "()Lorg/eclipse/aether/graph/Dependency;"}, {"nme": "applyTo", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DefaultDependencyNode;)V"}], "flds": [{"acc": 16, "nme": "premanagedVersion", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 16, "nme": "premanagedScope", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 16, "nme": "premanagedOptional", "dsc": "<PERSON><PERSON><PERSON>/lang/Boolean;"}, {"acc": 16, "nme": "premanagedExclusions", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lorg/eclipse/aether/graph/Exclusion;>;"}, {"acc": 16, "nme": "premanagedProperties", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"acc": 16, "nme": "managedBits", "dsc": "I"}, {"acc": 16, "nme": "managedDependency", "dsc": "Lorg/eclipse/aether/graph/Dependency;"}, {"acc": 16, "nme": "premanagedState", "dsc": "Z"}]}, "org/eclipse/aether/internal/impl/Maven2RepositoryLayoutFactory.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/internal/impl/Maven2RepositoryLayoutFactory", "super": "java/lang/Object", "mthds": [{"nme": "getPriority", "acc": 1, "dsc": "()F"}, {"nme": "<init>", "acc": 131073, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactorySelector;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "setPriority", "acc": 1, "dsc": "(F)Lorg/eclipse/aether/internal/impl/Maven2RepositoryLayoutFactory;"}, {"nme": "newInstance", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/spi/connector/layout/RepositoryLayout;", "exs": ["org/eclipse/aether/transfer/NoRepositoryLayoutException"]}, {"nme": "lambda$newInstance$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "lambda$newInstance$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}], "flds": [{"acc": 25, "nme": "NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "maven2"}, {"acc": 25, "nme": "CONFIG_PROP_CHECKSUMS_ALGORITHMS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.checksums.algorithms"}, {"acc": 26, "nme": "DEFAULT_CHECKSUMS_ALGORITHMS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "SHA-1,MD5"}, {"acc": 25, "nme": "CONFIG_PROP_OMIT_CHECKSUMS_FOR_EXTENSIONS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.checksums.omitChecksumsForExtensions"}, {"acc": 26, "nme": "DEFAULT_OMIT_CHECKSUMS_FOR_EXTENSIONS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": ".asc,.sigstore"}, {"acc": 2, "nme": "priority", "dsc": "F"}, {"acc": 18, "nme": "checksumAlgorithmFactorySelector", "dsc": "Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactorySelector;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;", "vals": ["value", "maven2"]}]}, "org/eclipse/aether/internal/impl/synccontext/named/GAVNameMapper.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/synccontext/named/GAVNameMapper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;L<PERSON><PERSON>/lang/String;)V"}, {"nme": "isFileSystemFriendly", "acc": 1, "dsc": "()Z"}, {"nme": "nameLocks", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection;Ljava/util/Collection;)Ljava/util/Collection;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection<+Lorg/eclipse/aether/artifact/Artifact;>;Ljava/util/Collection<+Lorg/eclipse/aether/metadata/Metadata;>;)Ljava/util/Collection<Ljava/lang/String;>;"}, {"nme": "getArtifactName", "acc": 2, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lja<PERSON>/lang/String;"}, {"nme": "getMetadataName", "acc": 2, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;)Ljava/lang/String;"}, {"nme": "gav", "acc": 9, "dsc": "()Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;"}, {"nme": "fileGav", "acc": 9, "dsc": "()Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;"}], "flds": [{"acc": 18, "nme": "fileSystemFriendly", "dsc": "Z"}, {"acc": 18, "nme": "artifactPrefix", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "artifactSuffix", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "metadataPrefix", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "metadataSuffix", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "fieldSeparator", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/eclipse/aether/internal/impl/synccontext/DefaultSyncContextFactory.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/internal/impl/synccontext/DefaultSyncContextFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/internal/impl/synccontext/named/NamedLockFactoryAdapterFactory;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "<init>", "acc": 131073, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "initService", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/locator/ServiceLocator;)V"}, {"nme": "newInstance", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Z)Lorg/eclipse/aether/SyncContext;"}, {"nme": "lambda$newInstance$0", "acc": 4098, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "ADAPTER_KEY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "namedLockFactoryAdapterFactory", "dsc": "Lorg/eclipse/aether/internal/impl/synccontext/named/NamedLockFactoryAdapterFactory;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;"}]}, "org/eclipse/aether/internal/impl/DefaultArtifactResolver$ResolutionGroup.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/internal/impl/DefaultArtifactResolver$ResolutionGroup", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)V"}, {"nme": "matches", "acc": 0, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;)Z"}], "flds": [{"acc": 16, "nme": "repository", "dsc": "Lorg/eclipse/aether/repository/RemoteRepository;"}, {"acc": 16, "nme": "items", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/internal/impl/DefaultArtifactResolver$ResolutionItem;>;"}]}, "org/eclipse/aether/internal/impl/collect/DataPool$GoodDescriptor.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/internal/impl/collect/DataPool$GoodDescriptor", "super": "org/eclipse/aether/internal/impl/collect/DataPool$Descriptor", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;)V"}, {"nme": "toResult", "acc": 1, "dsc": "(Lorg/eclipse/aether/resolution/ArtifactDescriptorRequest;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;"}], "flds": [{"acc": 16, "nme": "artifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}, {"acc": 16, "nme": "relocations", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/artifact/Artifact;>;"}, {"acc": 16, "nme": "aliases", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lorg/eclipse/aether/artifact/Artifact;>;"}, {"acc": 16, "nme": "repositories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"acc": 16, "nme": "dependencies", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;"}, {"acc": 16, "nme": "managedDependencies", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;"}]}, "org/eclipse/aether/internal/impl/collect/df/NodeStack.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/internal/impl/collect/df/NodeStack", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "top", "acc": 1, "dsc": "()Lorg/eclipse/aether/graph/DependencyNode;"}, {"nme": "push", "acc": 1, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;)V"}, {"nme": "pop", "acc": 1, "dsc": "()V"}, {"nme": "size", "acc": 1, "dsc": "()I"}, {"nme": "get", "acc": 1, "dsc": "(I)Lorg/eclipse/aether/graph/DependencyNode;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 0, "nme": "nodes", "dsc": "<PERSON><PERSON><PERSON>/util/ArrayList;", "sig": "Ljava/util/ArrayList<Lorg/eclipse/aether/graph/DependencyNode;>;"}]}, "org/eclipse/aether/internal/impl/collect/DataPool$Constraint$VersionRepo.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/internal/impl/collect/DataPool$Constraint$VersionRepo", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/version/Version;Lorg/eclipse/aether/repository/ArtifactRepository;)V"}], "flds": [{"acc": 16, "nme": "version", "dsc": "Lorg/eclipse/aether/version/Version;"}, {"acc": 16, "nme": "repo", "dsc": "Lorg/eclipse/aether/repository/ArtifactRepository;"}]}, "org/eclipse/aether/impl/SyncContextFactory.class": {"ver": 52, "acc": 132609, "nme": "org/eclipse/aether/impl/SyncContextFactory", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "org/eclipse/aether/internal/impl/TrackingFileManager.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/internal/impl/TrackingFileManager", "super": "java/lang/Object", "mthds": [{"nme": "read", "acc": 1025, "dsc": "(Ljava/io/File;)Ljava/util/Properties;"}, {"nme": "update", "acc": 1025, "dsc": "(Ljava/io/File;Ljava/util/Map;)Ljava/util/Properties;", "sig": "(Ljava/io/File;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)Ljava/util/Properties;"}], "flds": []}, "org/eclipse/aether/internal/impl/Maven2RepositoryLayoutFactory$Maven2RepositoryLayout.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/internal/impl/Maven2RepositoryLayoutFactory$Maven2RepositoryLayout", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactorySelector;Ljava/util/List;Ljava/util/Set;)V", "sig": "(Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactorySelector;Ljava/util/List<Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;>;Ljava/util/Set<Ljava/lang/String;>;)V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(Lja<PERSON>/lang/String;)Ljava/net/URI;"}, {"nme": "getChecksumAlgorithmFactories", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;>;"}, {"nme": "hasChecksums", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Z"}, {"nme": "getLocation", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Z)Ljava/net/URI;"}, {"nme": "getLocation", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;Z)Ljava/net/URI;"}, {"nme": "getChecksumLocations", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;ZLjava/net/URI;)Ljava/util/List;", "sig": "(Lorg/eclipse/aether/artifact/Artifact;ZLjava/net/URI;)Ljava/util/List<Lorg/eclipse/aether/spi/connector/layout/RepositoryLayout$ChecksumLocation;>;"}, {"nme": "getChecksumLocations", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;ZLjava/net/URI;)Ljava/util/List;", "sig": "(Lorg/eclipse/aether/metadata/Metadata;ZLjava/net/URI;)Ljava/util/List<Lorg/eclipse/aether/spi/connector/layout/RepositoryLayout$ChecksumLocation;>;"}, {"nme": "getChecksumLocations", "acc": 2, "dsc": "(Ljava/net/URI;)Ljava/util/List;", "sig": "(Ljava/net/URI;)Ljava/util/List<Lorg/eclipse/aether/spi/connector/layout/RepositoryLayout$ChecksumLocation;>;"}, {"nme": "isChecksum", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactorySelector;Ljava/util/List;Ljava/util/Set;Lorg/eclipse/aether/internal/impl/Maven2RepositoryLayoutFactory$1;)V"}], "flds": [{"acc": 18, "nme": "checksumAlgorithmFactorySelector", "dsc": "Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactorySelector;"}, {"acc": 18, "nme": "configuredChecksumAlgorithms", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;>;"}, {"acc": 18, "nme": "extensionsWithoutChecksums", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}]}, "org/eclipse/aether/internal/impl/Maven2RepositoryLayoutFactory$1.class": {"ver": 52, "acc": 4128, "nme": "org/eclipse/aether/internal/impl/Maven2RepositoryLayoutFactory$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/internal/impl/synccontext/named/DiscriminatingNameMapper.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/synccontext/named/DiscriminatingNameMapper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;)V"}, {"nme": "isFileSystemFriendly", "acc": 1, "dsc": "()Z"}, {"nme": "nameLocks", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection;Ljava/util/Collection;)Ljava/util/Collection;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection<+Lorg/eclipse/aether/artifact/Artifact;>;Ljava/util/Collection<+Lorg/eclipse/aether/metadata/Metadata;>;)Ljava/util/Collection<Ljava/lang/String;>;"}, {"nme": "getHostname", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "createDiscriminator", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lja<PERSON>/lang/String;"}, {"nme": "lambda$nameLocks$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CONFIG_PROP_DISCRIMINATOR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.syncContext.named.discriminating.discriminator"}, {"acc": 26, "nme": "CONFIG_PROP_HOSTNAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.syncContext.named.discriminating.hostname"}, {"acc": 26, "nme": "DEFAULT_DISCRIMINATOR_DIGEST", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "da39a3ee5e6b4b0d3255bfef95601890afd80709"}, {"acc": 26, "nme": "DEFAULT_HOSTNAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "localhost"}, {"acc": 26, "nme": "LOGGER", "dsc": "Lorg/slf4j/Logger;"}, {"acc": 18, "nme": "delegate", "dsc": "Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;"}, {"acc": 18, "nme": "hostname", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/eclipse/aether/impl/RemoteRepositoryFilterManager.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/impl/RemoteRepositoryFilterManager", "super": "java/lang/Object", "mthds": [{"nme": "getRemoteRepositoryFilter", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter;"}], "flds": []}, "org/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$DescriptorResolutionResult.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$DescriptorResolutionResult", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/resolution/VersionRangeResult;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/resolution/VersionRangeResult;Lorg/eclipse/aether/version/Version;Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;)V"}, {"nme": "flatten", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$DescriptorResolutionResult;>;"}, {"nme": "lambda$flatten$0", "acc": 4098, "dsc": "(Ljava/util/Map$Entry;)Lorg/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$DescriptorResolutionResult;"}], "flds": [{"acc": 0, "nme": "artifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}, {"acc": 0, "nme": "rangeResult", "dsc": "Lorg/eclipse/aether/resolution/VersionRangeResult;"}, {"acc": 0, "nme": "descriptors", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Lorg/eclipse/aether/version/Version;Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;>;"}]}, "org/eclipse/aether/internal/impl/filter/DefaultRemoteRepositoryFilterManager$Consensus.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/internal/impl/filter/DefaultRemoteRepositoryFilterManager$Consensus", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/util/Map;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter$Result;>;)V"}, {"nme": "isAccepted", "acc": 1, "dsc": "()Z"}, {"nme": "reasoning", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$new$0", "acc": 4106, "dsc": "(Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter$Result;)Z"}], "flds": [{"acc": 18, "nme": "accepted", "dsc": "Z"}, {"acc": 18, "nme": "reasoning", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/eclipse/aether/internal/impl/LocalPathPrefixComposerFactorySupport.class": {"ver": 52, "acc": 1057, "nme": "org/eclipse/aether/internal/impl/LocalPathPrefixComposerFactorySupport", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "isSplit", "acc": 4, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Z"}, {"nme": "getLocalPrefix", "acc": 4, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lja<PERSON>/lang/String;"}, {"nme": "isSplitLocal", "acc": 4, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Z"}, {"nme": "getRemotePrefix", "acc": 4, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lja<PERSON>/lang/String;"}, {"nme": "isSplitRemote", "acc": 4, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Z"}, {"nme": "isSplitRemoteRepository", "acc": 4, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Z"}, {"nme": "isSplitRemoteRepositoryLast", "acc": 4, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Z"}, {"nme": "getReleasesPrefix", "acc": 4, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lja<PERSON>/lang/String;"}, {"nme": "getSnapshotsPrefix", "acc": 4, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lja<PERSON>/lang/String;"}], "flds": [{"acc": 28, "nme": "CONF_PROP_SPLIT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.enhancedLocalRepository.split"}, {"acc": 28, "nme": "DEFAULT_SPLIT", "dsc": "Z", "val": 0}, {"acc": 28, "nme": "CONF_PROP_LOCAL_PREFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.enhancedLocalRepository.localPrefix"}, {"acc": 28, "nme": "DEFAULT_LOCAL_PREFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "installed"}, {"acc": 28, "nme": "CONF_PROP_SPLIT_LOCAL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.enhancedLocalRepository.splitLocal"}, {"acc": 28, "nme": "DEFAULT_SPLIT_LOCAL", "dsc": "Z", "val": 0}, {"acc": 28, "nme": "CONF_PROP_REMOTE_PREFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.enhancedLocalRepository.remotePrefix"}, {"acc": 28, "nme": "DEFAULT_REMOTE_PREFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "cached"}, {"acc": 28, "nme": "CONF_PROP_SPLIT_REMOTE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.enhancedLocalRepository.splitRemote"}, {"acc": 28, "nme": "DEFAULT_SPLIT_REMOTE", "dsc": "Z", "val": 0}, {"acc": 28, "nme": "CONF_PROP_SPLIT_REMOTE_REPOSITORY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.enhancedLocalRepository.splitRemoteRepository"}, {"acc": 28, "nme": "DEFAULT_SPLIT_REMOTE_REPOSITORY", "dsc": "Z", "val": 0}, {"acc": 28, "nme": "CONF_PROP_SPLIT_REMOTE_REPOSITORY_LAST", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.enhancedLocalRepository.splitRemoteRepositoryLast"}, {"acc": 28, "nme": "DEFAULT_SPLIT_REMOTE_REPOSITORY_LAST", "dsc": "Z", "val": 0}, {"acc": 28, "nme": "CONF_PROP_RELEASES_PREFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.enhancedLocalRepository.releasesPrefix"}, {"acc": 28, "nme": "DEFAULT_RELEASES_PREFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "releases"}, {"acc": 28, "nme": "CONF_PROP_SNAPSHOTS_PREFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.enhancedLocalRepository.snapshotsPrefix"}, {"acc": 28, "nme": "DEFAULT_SNAPSHOTS_PREFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "snapshots"}]}, "org/eclipse/aether/impl/DefaultServiceLocator$Entry.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/impl/DefaultServiceLocator$Entry", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/impl/DefaultServiceLocator;Lja<PERSON>/lang/Class;)V", "sig": "(Ljava/lang/Class<TT;>;)V"}, {"nme": "setServices", "acc": 161, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "([TT;)V"}, {"nme": "setService", "acc": 33, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(Ljava/lang/Class<+TT;>;)V"}, {"nme": "addService", "acc": 33, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(Ljava/lang/Class<+TT;>;)V"}, {"nme": "getInstance", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}, {"nme": "getInstances", "acc": 33, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<TT;>;"}, {"nme": "newInstance", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>ja<PERSON>/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)TT;"}], "flds": [{"acc": 18, "nme": "type", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<TT;>;"}, {"acc": 18, "nme": "providers", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Ljava/lang/Object;>;"}, {"acc": 2, "nme": "instances", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<TT;>;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lorg/eclipse/aether/impl/DefaultServiceLocator;"}]}, "org/eclipse/aether/internal/impl/DefaultRepositorySystem.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/DefaultRepositorySystem", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/VersionResolver;Lorg/eclipse/aether/impl/VersionRangeResolver;Lorg/eclipse/aether/impl/ArtifactResolver;Lorg/eclipse/aether/impl/MetadataResolver;Lorg/eclipse/aether/impl/ArtifactDescriptorReader;Lorg/eclipse/aether/impl/DependencyCollector;Lorg/eclipse/aether/impl/Installer;Lorg/eclipse/aether/impl/Deployer;Lorg/eclipse/aether/impl/LocalRepositoryProvider;Lorg/eclipse/aether/spi/synccontext/SyncContextFactory;Lorg/eclipse/aether/impl/RemoteRepositoryManager;Lorg/eclipse/aether/impl/RepositorySystemLifecycle;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "initService", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/locator/ServiceLocator;)V"}, {"nme": "setLoggerFactory", "acc": 131073, "dsc": "(Lorg/eclipse/aether/spi/log/LoggerFactory;)Lorg/eclipse/aether/internal/impl/DefaultRepositorySystem;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "setVersionResolver", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/VersionResolver;)Lorg/eclipse/aether/internal/impl/DefaultRepositorySystem;"}, {"nme": "setVersionRangeResolver", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/VersionRangeResolver;)Lorg/eclipse/aether/internal/impl/DefaultRepositorySystem;"}, {"nme": "setArtifactResolver", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/ArtifactResolver;)Lorg/eclipse/aether/internal/impl/DefaultRepositorySystem;"}, {"nme": "setMetadataResolver", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/MetadataResolver;)Lorg/eclipse/aether/internal/impl/DefaultRepositorySystem;"}, {"nme": "setArtifactDescriptorReader", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/ArtifactDescriptorReader;)Lorg/eclipse/aether/internal/impl/DefaultRepositorySystem;"}, {"nme": "setDependencyCollector", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/DependencyCollector;)Lorg/eclipse/aether/internal/impl/DefaultRepositorySystem;"}, {"nme": "setInstaller", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/Installer;)Lorg/eclipse/aether/internal/impl/DefaultRepositorySystem;"}, {"nme": "setDeployer", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/Deployer;)Lorg/eclipse/aether/internal/impl/DefaultRepositorySystem;"}, {"nme": "setLocalRepositoryProvider", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/LocalRepositoryProvider;)Lorg/eclipse/aether/internal/impl/DefaultRepositorySystem;"}, {"nme": "setSyncContextFactory", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/synccontext/SyncContextFactory;)Lorg/eclipse/aether/internal/impl/DefaultRepositorySystem;"}, {"nme": "setRemoteRepositoryManager", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/RemoteRepositoryManager;)Lorg/eclipse/aether/internal/impl/DefaultRepositorySystem;"}, {"nme": "setRepositorySystemLifecycle", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/RepositorySystemLifecycle;)Lorg/eclipse/aether/internal/impl/DefaultRepositorySystem;"}, {"nme": "resolveVersion", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/VersionRequest;)Lorg/eclipse/aether/resolution/VersionResult;", "exs": ["org/eclipse/aether/resolution/VersionResolutionException"]}, {"nme": "resolveVersionRange", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/VersionRangeRequest;)Lorg/eclipse/aether/resolution/VersionRangeResult;", "exs": ["org/eclipse/aether/resolution/VersionRangeResolutionException"]}, {"nme": "readArtifactDescriptor", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/ArtifactDescriptorRequest;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;", "exs": ["org/eclipse/aether/resolution/ArtifactDescriptorException"]}, {"nme": "resolveArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/ArtifactRequest;)Lorg/eclipse/aether/resolution/ArtifactResult;", "exs": ["org/eclipse/aether/resolution/ArtifactResolutionException"]}, {"nme": "resolveArtifacts", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection;)Ljava/util/List;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection<+Lorg/eclipse/aether/resolution/ArtifactRequest;>;)Ljava/util/List<Lorg/eclipse/aether/resolution/ArtifactResult;>;", "exs": ["org/eclipse/aether/resolution/ArtifactResolutionException"]}, {"nme": "resolveMetadata", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection;)Ljava/util/List;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection<+Lorg/eclipse/aether/resolution/MetadataRequest;>;)Ljava/util/List<Lorg/eclipse/aether/resolution/MetadataResult;>;"}, {"nme": "collectDependencies", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/collection/CollectRequest;)Lorg/eclipse/aether/collection/CollectResult;", "exs": ["org/eclipse/aether/collection/DependencyCollectionException"]}, {"nme": "resolveDependencies", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/DependencyRequest;)Lorg/eclipse/aether/resolution/DependencyResult;", "exs": ["org/eclipse/aether/resolution/DependencyResolutionException"]}, {"nme": "updateNodesWithResolvedArtifacts", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(<PERSON><PERSON><PERSON>/util/List<Lorg/eclipse/aether/resolution/ArtifactResult;>;)V"}, {"nme": "install", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/installation/InstallRequest;)Lorg/eclipse/aether/installation/InstallResult;", "exs": ["org/eclipse/aether/installation/InstallationException"]}, {"nme": "deploy", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/deployment/DeployRequest;)Lorg/eclipse/aether/deployment/DeployResult;", "exs": ["org/eclipse/aether/deployment/DeploymentException"]}, {"nme": "newLocalRepositoryManager", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/LocalRepository;)Lorg/eclipse/aether/repository/LocalRepositoryManager;"}, {"nme": "newSyncContext", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Z)Lorg/eclipse/aether/SyncContext;"}, {"nme": "newResolutionRepositories", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lja<PERSON>/util/List;)Ljava/util/List;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;)Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"nme": "newDeploymentRepository", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/repository/RemoteRepository;"}, {"nme": "addOnSystemEndedHandler", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Runnable;)V"}, {"nme": "shutdown", "acc": 1, "dsc": "()V"}, {"nme": "validateSession", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)V"}, {"nme": "validateRepositories", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;)V"}, {"nme": "invalidSession", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 18, "nme": "shutdown", "dsc": "Ljava/util/concurrent/atomic/AtomicBoolean;"}, {"acc": 2, "nme": "versionResolver", "dsc": "Lorg/eclipse/aether/impl/VersionResolver;"}, {"acc": 2, "nme": "versionRangeResolver", "dsc": "Lorg/eclipse/aether/impl/VersionRangeResolver;"}, {"acc": 2, "nme": "artifactResolver", "dsc": "Lorg/eclipse/aether/impl/ArtifactResolver;"}, {"acc": 2, "nme": "metadataResolver", "dsc": "Lorg/eclipse/aether/impl/MetadataResolver;"}, {"acc": 2, "nme": "artifactDescriptorReader", "dsc": "Lorg/eclipse/aether/impl/ArtifactDescriptorReader;"}, {"acc": 2, "nme": "dependencyCollector", "dsc": "Lorg/eclipse/aether/impl/DependencyCollector;"}, {"acc": 2, "nme": "installer", "dsc": "Lorg/eclipse/aether/impl/Installer;"}, {"acc": 2, "nme": "deployer", "dsc": "Lorg/eclipse/aether/impl/Deployer;"}, {"acc": 2, "nme": "localRepositoryProvider", "dsc": "Lorg/eclipse/aether/impl/LocalRepositoryProvider;"}, {"acc": 2, "nme": "syncContextFactory", "dsc": "Lorg/eclipse/aether/spi/synccontext/SyncContextFactory;"}, {"acc": 2, "nme": "remoteRepositoryManager", "dsc": "Lorg/eclipse/aether/impl/RemoteRepositoryManager;"}, {"acc": 2, "nme": "repositorySystemLifecycle", "dsc": "Lorg/eclipse/aether/impl/RepositorySystemLifecycle;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;"}]}, "org/eclipse/aether/internal/impl/DefaultInstaller.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/DefaultInstaller", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/io/FileProcessor;Lorg/eclipse/aether/impl/RepositoryEventDispatcher;<PERSON><PERSON><PERSON>/util/Set;Lorg/eclipse/aether/spi/synccontext/SyncContextFactory;)V", "sig": "(Lorg/eclipse/aether/spi/io/FileProcessor;Lorg/eclipse/aether/impl/RepositoryEventDispatcher;Ljava/util/Set<Lorg/eclipse/aether/impl/MetadataGeneratorFactory;>;Lorg/eclipse/aether/spi/synccontext/SyncContextFactory;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "initService", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/locator/ServiceLocator;)V"}, {"nme": "setFileProcessor", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/io/FileProcessor;)Lorg/eclipse/aether/internal/impl/DefaultInstaller;"}, {"nme": "setRepositoryEventDispatcher", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/RepositoryEventDispatcher;)Lorg/eclipse/aether/internal/impl/DefaultInstaller;"}, {"nme": "addMetadataGeneratorFactory", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/MetadataGeneratorFactory;)Lorg/eclipse/aether/internal/impl/DefaultInstaller;"}, {"nme": "setMetadataGeneratorFactories", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Lorg/eclipse/aether/internal/impl/DefaultInstaller;", "sig": "(Lja<PERSON>/util/Collection<Lorg/eclipse/aether/impl/MetadataGeneratorFactory;>;)Lorg/eclipse/aether/internal/impl/DefaultInstaller;"}, {"nme": "setSyncContextFactory", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/synccontext/SyncContextFactory;)Lorg/eclipse/aether/internal/impl/DefaultInstaller;"}, {"nme": "install", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/installation/InstallRequest;)Lorg/eclipse/aether/installation/InstallResult;", "exs": ["org/eclipse/aether/installation/InstallationException"]}, {"nme": "install", "acc": 2, "dsc": "(Lorg/eclipse/aether/SyncContext;Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/installation/InstallRequest;)Lorg/eclipse/aether/installation/InstallResult;", "exs": ["org/eclipse/aether/installation/InstallationException"]}, {"nme": "getMetadataGenerators", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/installation/InstallRequest;)Ljava/util/List;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/installation/InstallRequest;)Ljava/util/List<+Lorg/eclipse/aether/impl/MetadataGenerator;>;"}, {"nme": "install", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/artifact/Artifact;)V", "exs": ["org/eclipse/aether/installation/InstallationException"]}, {"nme": "install", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/LocalRepositoryManager;Ljava/io/File;Lorg/eclipse/aether/transform/FileTransformer;)V", "exs": ["org/eclipse/aether/installation/InstallationException"]}, {"nme": "install", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/metadata/Metadata;)V", "exs": ["org/eclipse/aether/installation/InstallationException"]}, {"nme": "artifactInstalling", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/artifact/Artifact;Ljava/io/File;)V"}, {"nme": "artifactInstalled", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/artifact/Artifact;Ljava/io/File;Ljava/lang/Exception;)V"}, {"nme": "metadataInstalling", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/metadata/Metadata;Ljava/io/File;)V"}, {"nme": "metadataInstalled", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/metadata/Metadata;Ljava/io/File;Ljava/lang/Exception;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "LOGGER", "dsc": "Lorg/slf4j/Logger;"}, {"acc": 2, "nme": "fileProcessor", "dsc": "Lorg/eclipse/aether/spi/io/FileProcessor;"}, {"acc": 2, "nme": "repositoryEventDispatcher", "dsc": "Lorg/eclipse/aether/impl/RepositoryEventDispatcher;"}, {"acc": 2, "nme": "metadataFactories", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lorg/eclipse/aether/impl/MetadataGeneratorFactory;>;"}, {"acc": 2, "nme": "syncContextFactory", "dsc": "Lorg/eclipse/aether/spi/synccontext/SyncContextFactory;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;"}]}, "org/eclipse/aether/internal/impl/DefaultArtifactResolver.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/DefaultArtifactResolver", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/io/FileProcessor;Lorg/eclipse/aether/impl/RepositoryEventDispatcher;Lorg/eclipse/aether/impl/VersionResolver;Lorg/eclipse/aether/impl/UpdateCheckManager;Lorg/eclipse/aether/impl/RepositoryConnectorProvider;Lorg/eclipse/aether/impl/RemoteRepositoryManager;Lorg/eclipse/aether/spi/synccontext/SyncContextFactory;Lorg/eclipse/aether/impl/OfflineController;Ljava/util/Map;Lorg/eclipse/aether/impl/RemoteRepositoryFilterManager;)V", "sig": "(Lorg/eclipse/aether/spi/io/FileProcessor;Lorg/eclipse/aether/impl/RepositoryEventDispatcher;Lorg/eclipse/aether/impl/VersionResolver;Lorg/eclipse/aether/impl/UpdateCheckManager;Lorg/eclipse/aether/impl/RepositoryConnectorProvider;Lorg/eclipse/aether/impl/RemoteRepositoryManager;Lorg/eclipse/aether/spi/synccontext/SyncContextFactory;Lorg/eclipse/aether/impl/OfflineController;Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/spi/resolution/ArtifactResolverPostProcessor;>;Lorg/eclipse/aether/impl/RemoteRepositoryFilterManager;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "initService", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/locator/ServiceLocator;)V"}, {"nme": "setLoggerFactory", "acc": 131073, "dsc": "(Lorg/eclipse/aether/spi/log/LoggerFactory;)Lorg/eclipse/aether/internal/impl/DefaultArtifactResolver;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "setFileProcessor", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/io/FileProcessor;)Lorg/eclipse/aether/internal/impl/DefaultArtifactResolver;"}, {"nme": "setRepositoryEventDispatcher", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/RepositoryEventDispatcher;)Lorg/eclipse/aether/internal/impl/DefaultArtifactResolver;"}, {"nme": "setVersionResolver", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/VersionResolver;)Lorg/eclipse/aether/internal/impl/DefaultArtifactResolver;"}, {"nme": "setUpdateCheckManager", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/UpdateCheckManager;)Lorg/eclipse/aether/internal/impl/DefaultArtifactResolver;"}, {"nme": "setRepositoryConnectorProvider", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/RepositoryConnectorProvider;)Lorg/eclipse/aether/internal/impl/DefaultArtifactResolver;"}, {"nme": "setRemoteRepositoryManager", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/RemoteRepositoryManager;)Lorg/eclipse/aether/internal/impl/DefaultArtifactResolver;"}, {"nme": "setSyncContextFactory", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/synccontext/SyncContextFactory;)Lorg/eclipse/aether/internal/impl/DefaultArtifactResolver;"}, {"nme": "setOfflineController", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/OfflineController;)Lorg/eclipse/aether/internal/impl/DefaultArtifactResolver;"}, {"nme": "setArtifactResolverPostProcessors", "acc": 1, "dsc": "(<PERSON>java/util/Map;)Lorg/eclipse/aether/internal/impl/DefaultArtifactResolver;", "sig": "(Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/spi/resolution/ArtifactResolverPostProcessor;>;)Lorg/eclipse/aether/internal/impl/DefaultArtifactResolver;"}, {"nme": "setRemoteRepositoryFilterManager", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/RemoteRepositoryFilterManager;)Lorg/eclipse/aether/internal/impl/DefaultArtifactResolver;"}, {"nme": "resolveArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/ArtifactRequest;)Lorg/eclipse/aether/resolution/ArtifactResult;", "exs": ["org/eclipse/aether/resolution/ArtifactResolutionException"]}, {"nme": "resolveArtifacts", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection;)Ljava/util/List;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection<+Lorg/eclipse/aether/resolution/ArtifactRequest;>;)Ljava/util/List<Lorg/eclipse/aether/resolution/ArtifactResult;>;", "exs": ["org/eclipse/aether/resolution/ArtifactResolutionException"]}, {"nme": "resolve", "acc": 2, "dsc": "(Lorg/eclipse/aether/SyncContext;Lorg/eclipse/aether/SyncContext;Ljava/util/Collection;Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection;)Ljava/util/List;", "sig": "(Lorg/eclipse/aether/SyncContext;Lorg/eclipse/aether/SyncContext;Ljava/util/Collection<Lorg/eclipse/aether/artifact/Artifact;>;Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection<+Lorg/eclipse/aether/resolution/ArtifactRequest;>;)Ljava/util/List<Lorg/eclipse/aether/resolution/ArtifactResult;>;", "exs": ["org/eclipse/aether/resolution/ArtifactResolutionException"]}, {"nme": "isLocallyInstalled", "acc": 2, "dsc": "(Lorg/eclipse/aether/repository/LocalArtifactResult;Lorg/eclipse/aether/resolution/VersionResult;)Z"}, {"nme": "getFile", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Ljava/io/File;)Ljava/io/File;", "exs": ["org/eclipse/aether/transfer/ArtifactTransferException"]}, {"nme": "performDownloads", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/internal/impl/DefaultArtifactResolver$ResolutionGroup;)V"}, {"nme": "gatherDownloads", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/internal/impl/DefaultArtifactResolver$ResolutionGroup;)Ljava/util/List;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/internal/impl/DefaultArtifactResolver$ResolutionGroup;)Ljava/util/List<Lorg/eclipse/aether/spi/connector/ArtifactDownload;>;"}, {"nme": "evaluateDownloads", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/internal/impl/DefaultArtifactResolver$ResolutionGroup;)V"}, {"nme": "artifactResolving", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/artifact/Artifact;)V"}, {"nme": "artifactResolved", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/ArtifactRepository;Lja<PERSON>/util/List;)V", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/ArtifactRepository;Ljava/util/List<Ljava/lang/Exception;>;)V"}, {"nme": "artifactDownloading", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/RemoteRepository;)V"}, {"nme": "artifactDownloaded", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/RemoteRepository;<PERSON><PERSON><PERSON>/lang/Exception;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CONFIG_PROP_SNAPSHOT_NORMALIZATION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.artifactResolver.snapshotNormalization"}, {"acc": 26, "nme": "CONFIG_PROP_SIMPLE_LRM_INTEROP", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.artifactResolver.simpleLrmInterop"}, {"acc": 26, "nme": "LOGGER", "dsc": "Lorg/slf4j/Logger;"}, {"acc": 2, "nme": "fileProcessor", "dsc": "Lorg/eclipse/aether/spi/io/FileProcessor;"}, {"acc": 2, "nme": "repositoryEventDispatcher", "dsc": "Lorg/eclipse/aether/impl/RepositoryEventDispatcher;"}, {"acc": 2, "nme": "versionResolver", "dsc": "Lorg/eclipse/aether/impl/VersionResolver;"}, {"acc": 2, "nme": "updateCheckManager", "dsc": "Lorg/eclipse/aether/impl/UpdateCheckManager;"}, {"acc": 2, "nme": "repositoryConnectorProvider", "dsc": "Lorg/eclipse/aether/impl/RepositoryConnectorProvider;"}, {"acc": 2, "nme": "remoteRepositoryManager", "dsc": "Lorg/eclipse/aether/impl/RemoteRepositoryManager;"}, {"acc": 2, "nme": "syncContextFactory", "dsc": "Lorg/eclipse/aether/spi/synccontext/SyncContextFactory;"}, {"acc": 2, "nme": "offlineController", "dsc": "Lorg/eclipse/aether/impl/OfflineController;"}, {"acc": 2, "nme": "artifactResolverPostProcessors", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/spi/resolution/ArtifactResolverPostProcessor;>;"}, {"acc": 2, "nme": "remoteRepositoryFilterManager", "dsc": "Lorg/eclipse/aether/impl/RemoteRepositoryFilterManager;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;"}]}, "org/eclipse/aether/impl/VersionResolver.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/impl/VersionResolver", "super": "java/lang/Object", "mthds": [{"nme": "resolveVersion", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/VersionRequest;)Lorg/eclipse/aether/resolution/VersionResult;", "exs": ["org/eclipse/aether/resolution/VersionResolutionException"]}], "flds": []}, "org/eclipse/aether/impl/UpdateCheckManager.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/impl/UpdateCheckManager", "super": "java/lang/Object", "mthds": [{"nme": "checkArtifact", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/impl/UpdateCheck;)V", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/impl/UpdateCheck<Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/transfer/ArtifactTransferException;>;)V"}, {"nme": "touchArtifact", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/impl/UpdateCheck;)V", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/impl/UpdateCheck<Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/transfer/ArtifactTransferException;>;)V"}, {"nme": "checkMetadata", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/impl/UpdateCheck;)V", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/impl/UpdateCheck<Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/transfer/MetadataTransferException;>;)V"}, {"nme": "touchMetadata", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/impl/UpdateCheck;)V", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/impl/UpdateCheck<Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/transfer/MetadataTransferException;>;)V"}], "flds": []}, "org/eclipse/aether/internal/impl/synccontext/named/NamedLockFactoryAdapter$1.class": {"ver": 52, "acc": 4128, "nme": "org/eclipse/aether/internal/impl/synccontext/named/NamedLockFactoryAdapter$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/internal/impl/filter/GroupIdRemoteRepositoryFilterSource.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/internal/impl/filter/GroupIdRemoteRepositoryFilterSource", "super": "org/eclipse/aether/internal/impl/filter/RemoteRepositoryFilterSourceSupport", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/RepositorySystemLifecycle;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "getRemoteRepositoryFilter", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter;"}, {"nme": "postProcess", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/List<Lorg/eclipse/aether/resolution/ArtifactResult;>;)V"}, {"nme": "filePath", "acc": 2, "dsc": "(Ljava/nio/file/Path;Ljava/lang/String;)Ljava/nio/file/Path;"}, {"nme": "cacheRules", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;)Ljava/util/Set;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;)Ljava/util/Set<Ljava/lang/String;>;"}, {"nme": "loadRepositoryRules", "acc": 2, "dsc": "(Ljava/nio/file/Path;)Ljava/util/Set;", "sig": "(Ljava/nio/file/Path;)Ljava/util/Set<Ljava/lang/String;>;"}, {"nme": "isRecord", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Z"}, {"nme": "saveRecordedLines", "acc": 2, "dsc": "()V"}, {"nme": "lambda$saveRecordedLines$2", "acc": 4106, "dsc": "(Ljava/util/TreeSet;Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "lambda$cacheRules$1", "acc": 4098, "dsc": "(Ljava/nio/file/Path;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/nio/file/Path;)Ljava/util/Set;"}, {"nme": "lambda$postProcess$0", "acc": 4106, "dsc": "(Ljava/nio/file/Path;)Ljava/util/Set;"}, {"nme": "access$100", "acc": 4104, "dsc": "(Lorg/eclipse/aether/internal/impl/filter/GroupIdRemoteRepositoryFilterSource;Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;)Ljava/util/Set;"}, {"nme": "access$200", "acc": 4104, "dsc": "()Ljava/util/TreeSet;"}, {"nme": "access$300", "acc": 4104, "dsc": "()Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter$Result;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "groupId"}, {"acc": 26, "nme": "CONF_NAME_RECORD", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "record"}, {"acc": 24, "nme": "GROUP_ID_FILE_PREFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "groupId-"}, {"acc": 24, "nme": "GROUP_ID_FILE_SUFFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": ".txt"}, {"acc": 26, "nme": "LOGGER", "dsc": "Lorg/slf4j/Logger;"}, {"acc": 18, "nme": "repositorySystemLifecycle", "dsc": "Lorg/eclipse/aether/impl/RepositorySystemLifecycle;"}, {"acc": 18, "nme": "rules", "dsc": "Ljava/util/concurrent/ConcurrentHashMap;", "sig": "Ljava/util/concurrent/ConcurrentHashMap<Ljava/nio/file/Path;Ljava/util/Set<Ljava/lang/String;>;>;"}, {"acc": 18, "nme": "changedRules", "dsc": "Ljava/util/concurrent/ConcurrentHashMap;", "sig": "Ljava/util/concurrent/ConcurrentHashMap<Ljava/nio/file/Path;Ljava/lang/Boolean;>;"}, {"acc": 18, "nme": "onShutdownHandlerRegistered", "dsc": "Ljava/util/concurrent/atomic/AtomicBoolean;"}, {"acc": 26, "nme": "NOT_PRESENT", "dsc": "L<PERSON><PERSON>/util/TreeSet;", "sig": "Ljava/util/TreeSet<Ljava/lang/String;>;"}, {"acc": 26, "nme": "NOT_PRESENT_RESULT", "dsc": "Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter$Result;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;", "vals": ["value", "groupId"]}]}, "org/eclipse/aether/internal/impl/EnhancedLocalRepositoryManager.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/internal/impl/EnhancedLocalRepositoryManager", "super": "org/eclipse/aether/internal/impl/SimpleLocalRepositoryManager", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/io/File;Lorg/eclipse/aether/internal/impl/LocalPathComposer;Ljava/lang/String;Lorg/eclipse/aether/internal/impl/TrackingFileManager;Lorg/eclipse/aether/internal/impl/LocalPathPrefixComposer;)V"}, {"nme": "concatPaths", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getPathForLocalArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lja<PERSON>/lang/String;"}, {"nme": "getPathForRemoteArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "getPathForLocalMetadata", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;)Ljava/lang/String;"}, {"nme": "getPathForRemoteMetadata", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "find", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/LocalArtifactRequest;)Lorg/eclipse/aether/repository/LocalArtifactResult;"}, {"nme": "checkFind", "acc": 2, "dsc": "(Ljava/io/File;Lorg/eclipse/aether/repository/LocalArtifactResult;)V"}, {"nme": "add", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/LocalArtifactRegistration;)V"}, {"nme": "getRepositoryKeys", "acc": 2, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;Ljava/util/Collection;)Ljava/util/Collection;", "sig": "(Lorg/eclipse/aether/repository/RemoteRepository;Ljava/util/Collection<Ljava/lang/String;>;)Ljava/util/Collection<Ljava/lang/String;>;"}, {"nme": "addArtifact", "acc": 2, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/util/Collection;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;)V", "sig": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/util/Collection<Ljava/lang/String;>;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/lang/String;)V"}, {"nme": "readRepos", "acc": 2, "dsc": "(Ljava/io/File;)Ljava/util/Properties;"}, {"nme": "addRepo", "acc": 2, "dsc": "(Ljava/io/File;Ljava/util/Collection;)V", "sig": "(Ljava/io/File;Ljava/util/Collection<Ljava/lang/String;>;)V"}, {"nme": "getTrackingFile", "acc": 2, "dsc": "(Lja<PERSON>/io/File;)Ljava/io/File;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/io/File;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "isTracked", "acc": 2, "dsc": "(Lja<PERSON>/util/Properties;Ljava/io/File;)Z"}], "flds": [{"acc": 26, "nme": "LOCAL_REPO_ID", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": ""}, {"acc": 18, "nme": "trackingFilename", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "trackingFileManager", "dsc": "Lorg/eclipse/aether/internal/impl/TrackingFileManager;"}, {"acc": 18, "nme": "localPathPrefixComposer", "dsc": "Lorg/eclipse/aether/internal/impl/LocalPathPrefixComposer;"}]}, "org/eclipse/aether/internal/impl/synccontext/legacy/DefaultSyncContextFactory.class": {"ver": 52, "acc": 131121, "nme": "org/eclipse/aether/internal/impl/synccontext/legacy/DefaultSyncContextFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/synccontext/SyncContextFactory;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "initService", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/locator/ServiceLocator;)V"}, {"nme": "newInstance", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Z)Lorg/eclipse/aether/SyncContext;"}], "flds": [{"acc": 2, "nme": "delegate", "dsc": "Lorg/eclipse/aether/spi/synccontext/SyncContextFactory;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "org/eclipse/aether/internal/impl/synccontext/named/providers/GAVNameMapperProvider.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/synccontext/named/providers/GAVNameMapperProvider", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "get", "acc": 1, "dsc": "()Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;"}, {"nme": "get", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 18, "nme": "mapper", "dsc": "Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;", "vals": ["value", "gav"]}]}, "org/eclipse/aether/internal/impl/synccontext/named/NamedLockFactoryAdapterFactoryImpl.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/synccontext/named/NamedLockFactoryAdapterFactoryImpl", "super": "java/lang/Object", "mthds": [{"nme": "getManuallyCreatedFactories", "acc": 10, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/named/NamedLockFactory;>;"}, {"nme": "getManuallyCreatedNameMappers", "acc": 10, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;>;"}, {"nme": "<init>", "acc": 131073, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "initService", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/locator/ServiceLocator;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/util/Map;Ljava/util/Map;Lorg/eclipse/aether/impl/RepositorySystemLifecycle;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/named/NamedLockFactory;>;Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;>;Lorg/eclipse/aether/impl/RepositorySystemLifecycle;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "<init>", "acc": 1, "dsc": "(Lja<PERSON>/util/Map;Ljava/lang/String;Ljava/util/Map;Ljava/lang/String;Lorg/eclipse/aether/impl/RepositorySystemLifecycle;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/named/NamedLockFactory;>;Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;>;Ljava/lang/String;Lorg/eclipse/aether/impl/RepositorySystemLifecycle;)V"}, {"nme": "getAdapter", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lorg/eclipse/aether/internal/impl/synccontext/named/NamedLockFactoryAdapter;"}, {"nme": "createAdapter", "acc": 4, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lorg/eclipse/aether/internal/impl/synccontext/named/NamedLockFactoryAdapter;"}, {"nme": "getFactoryName", "acc": 4, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lja<PERSON>/lang/String;"}, {"nme": "getDefaultFactoryName", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getNameMapperName", "acc": 4, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lja<PERSON>/lang/String;"}, {"nme": "getDefaultNameMapperName", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "selectFactory", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/named/NamedLockFactory;"}, {"nme": "selectNameMapper", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;"}, {"nme": "shutdown", "acc": 4, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "DEFAULT_FACTORY_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "rwlock-local"}, {"acc": 26, "nme": "DEFAULT_NAME_MAPPER_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "gav"}, {"acc": 28, "nme": "FACTORY_KEY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.syncContext.named.factory"}, {"acc": 28, "nme": "NAME_MAPPER_KEY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.syncContext.named.nameMapper"}, {"acc": 20, "nme": "logger", "dsc": "Lorg/slf4j/Logger;"}, {"acc": 20, "nme": "factories", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/named/NamedLockFactory;>;"}, {"acc": 20, "nme": "defaultFactoryName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 20, "nme": "nameMappers", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;>;"}, {"acc": 20, "nme": "defaultNameMapperName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;"}]}, "org/eclipse/aether/internal/impl/synccontext/named/NamedLockFactoryAdapterFactory.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/internal/impl/synccontext/named/NamedLockFactoryAdapterFactory", "super": "java/lang/Object", "mthds": [{"nme": "getAdapter", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lorg/eclipse/aether/internal/impl/synccontext/named/NamedLockFactoryAdapter;"}], "flds": []}, "org/eclipse/aether/internal/impl/filter/PrefixesRemoteRepositoryFilterSource$Node.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/internal/impl/filter/PrefixesRemoteRepositoryFilterSource$Node", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Z"}, {"nme": "addSibling", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/internal/impl/filter/PrefixesRemoteRepositoryFilterSource$Node;"}, {"nme": "getSibling", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/internal/impl/filter/PrefixesRemoteRepositoryFilterSource$Node;"}, {"nme": "<init>", "acc": 4096, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/eclipse/aether/internal/impl/filter/PrefixesRemoteRepositoryFilterSource$1;)V"}], "flds": [{"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "siblings", "dsc": "<PERSON><PERSON><PERSON>/util/HashMap;", "sig": "Ljava/util/HashMap<Ljava/lang/String;Lorg/eclipse/aether/internal/impl/filter/PrefixesRemoteRepositoryFilterSource$Node;>;"}]}, "org/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper.class": {"ver": 52, "acc": 1056, "nme": "org/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "skipResolution", "acc": 1024, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;<PERSON>ja<PERSON>/util/List;)Z", "sig": "(Lorg/eclipse/aether/graph/DependencyNode;Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;)Z"}, {"nme": "cache", "acc": 1024, "dsc": "(Lorg/eclipse/aether/graph/DependencyNode;Lja<PERSON>/util/List;)V", "sig": "(Lorg/eclipse/aether/graph/DependencyNode;Ljava/util/List<Lorg/eclipse/aether/graph/DependencyNode;>;)V"}, {"nme": "close", "acc": 1025, "dsc": "()V"}, {"nme": "defaultSkipper", "acc": 9, "dsc": "()Lorg/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper$DefaultDependencyResolutionSkipper;"}, {"nme": "neverSkipper", "acc": 9, "dsc": "()Lorg/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper;"}], "flds": []}, "org/eclipse/aether/impl/RepositoryEventDispatcher.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/impl/RepositoryEventDispatcher", "super": "java/lang/Object", "mthds": [{"nme": "dispatch", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositoryEvent;)V"}], "flds": []}, "org/eclipse/aether/internal/impl/filter/GroupIdRemoteRepositoryFilterSource$1.class": {"ver": 52, "acc": 4128, "nme": "org/eclipse/aether/internal/impl/filter/GroupIdRemoteRepositoryFilterSource$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/impl/MetadataGeneratorFactory.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/impl/MetadataGeneratorFactory", "super": "java/lang/Object", "mthds": [{"nme": "newInstance", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/installation/InstallRequest;)Lorg/eclipse/aether/impl/MetadataGenerator;"}, {"nme": "newInstance", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/deployment/DeployRequest;)Lorg/eclipse/aether/impl/MetadataGenerator;"}, {"nme": "getPriority", "acc": 1025, "dsc": "()F"}], "flds": []}, "org/eclipse/aether/internal/impl/DefaultLocalPathPrefixComposerFactory$DefaultLocalPathPrefixComposer.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/internal/impl/DefaultLocalPathPrefixComposerFactory$DefaultLocalPathPrefixComposer", "super": "org/eclipse/aether/internal/impl/LocalPathPrefixComposerFactorySupport$LocalPathPrefixComposerSupport", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Z<PERSON><PERSON><PERSON>/lang/String;ZLjava/lang/String;ZZZLjava/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 4096, "dsc": "(ZLjava/lang/String;ZLjava/lang/String;ZZZLjava/lang/String;Ljava/lang/String;Lorg/eclipse/aether/internal/impl/DefaultLocalPathPrefixComposerFactory$1;)V"}], "flds": []}, "org/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector", "super": "org/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/RemoteRepositoryManager;Lorg/eclipse/aether/impl/ArtifactDescriptorReader;Lorg/eclipse/aether/impl/VersionRangeResolver;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "doCollectDependencies", "acc": 4, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/internal/impl/collect/DataPool;Lorg/eclipse/aether/internal/impl/collect/DefaultDependencyCollectionContext;Lorg/eclipse/aether/internal/impl/collect/DefaultVersionFilterContext;Lorg/eclipse/aether/collection/CollectRequest;Lorg/eclipse/aether/graph/DependencyNode;Ljava/util/List;Ljava/util/List;Ljava/util/List;Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate$Results;)V", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/internal/impl/collect/DataPool;Lorg/eclipse/aether/internal/impl/collect/DefaultDependencyCollectionContext;Lorg/eclipse/aether/internal/impl/collect/DefaultVersionFilterContext;Lorg/eclipse/aether/collection/CollectRequest;Lorg/eclipse/aether/graph/DependencyNode;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;Ljava/util/List<Lorg/eclipse/aether/graph/Dependency;>;Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate$Results;)V"}, {"nme": "processDependency", "acc": 2, "dsc": "(Lorg/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$Args;Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate$Results;Lorg/eclipse/aether/internal/impl/collect/bf/DependencyProcessingContext;Ljava/util/List;Z)V", "sig": "(Lorg/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$Args;Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate$Results;Lorg/eclipse/aether/internal/impl/collect/bf/DependencyProcessingContext;Ljava/util/List<Lorg/eclipse/aether/artifact/Artifact;>;Z)V"}, {"nme": "doRecurse", "acc": 2, "dsc": "(Lorg/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$Args;Lorg/eclipse/aether/internal/impl/collect/bf/DependencyProcessingContext;Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;Lorg/eclipse/aether/graph/DefaultDependencyNode;Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate$Results;Z)V"}, {"nme": "filter", "acc": 2, "dsc": "(Lorg/eclipse/aether/internal/impl/collect/bf/DependencyProcessingContext;)Z"}, {"nme": "resolveArtifactDescriptorAsync", "acc": 2, "dsc": "(Lorg/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$Args;Lorg/eclipse/aether/internal/impl/collect/bf/DependencyProcessingContext;Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate$Results;)V"}, {"nme": "resolveDescriptorForVersion", "acc": 2, "dsc": "(Lorg/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$Args;Lorg/eclipse/aether/internal/impl/collect/bf/DependencyProcessingContext;Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate$Results;Lorg/eclipse/aether/graph/Dependency;Lorg/eclipse/aether/version/Version;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;"}, {"nme": "resolveCachedArtifactDescriptor", "acc": 2, "dsc": "(Lorg/eclipse/aether/internal/impl/collect/DataPool;Lorg/eclipse/aether/resolution/ArtifactDescriptorRequest;Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/internal/impl/collect/bf/DependencyProcessingContext;Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate$Results;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;"}, {"nme": "lambda$resolveArtifactDescriptorAsync$4", "acc": 4098, "dsc": "(Lorg/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$Args;Lorg/eclipse/aether/internal/impl/collect/bf/DependencyProcessingContext;Lorg/eclipse/aether/graph/Dependency;Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate$Results;)Lorg/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$DescriptorResolutionResult;", "exs": ["java/lang/Exception"]}, {"nme": "lambda$resolveArtifactDescriptorAsync$3", "acc": 4106, "dsc": "(Lorg/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$Args;Lorg/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$DescriptorResolutionResult;)V"}, {"nme": "lambda$resolveArtifactDescriptorAsync$2", "acc": 4106, "dsc": "(Lorg/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$DescriptorResolutionResult;Ljava/util/Map;Lorg/eclipse/aether/version/Version;)V"}, {"nme": "lambda$resolveArtifactDescriptorAsync$1", "acc": 4098, "dsc": "(Lorg/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$Args;Lorg/eclipse/aether/internal/impl/collect/bf/DependencyProcessingContext;Lorg/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate$Results;Lorg/eclipse/aether/graph/Dependency;Ljava/util/Map;Lorg/eclipse/aether/version/Version;)V"}, {"nme": "lambda$resolveArtifactDescriptorAsync$0", "acc": 4106, "dsc": "(Ljava/util/Map;Lorg/eclipse/aether/version/Version;Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;)V"}], "flds": [{"acc": 25, "nme": "NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "bf"}, {"acc": 24, "nme": "CONFIG_PROP_SKIPPER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.dependencyCollector.bf.skipper"}, {"acc": 24, "nme": "CONFIG_PROP_SKIPPER_DEFAULT", "dsc": "Z", "val": 1}, {"acc": 24, "nme": "CONFIG_PROP_THREADS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.dependencyCollector.bf.threads"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;", "vals": ["value", "bf"]}]}, "org/eclipse/aether/internal/impl/SimpleLocalRepositoryManagerFactory.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/SimpleLocalRepositoryManagerFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/internal/impl/LocalPathComposer;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "initService", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/locator/ServiceLocator;)V"}, {"nme": "newInstance", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/LocalRepository;)Lorg/eclipse/aether/repository/LocalRepositoryManager;", "exs": ["org/eclipse/aether/repository/NoLocalRepositoryManagerException"]}, {"nme": "getPriority", "acc": 1, "dsc": "()F"}, {"nme": "setPriority", "acc": 1, "dsc": "(F)Lorg/eclipse/aether/internal/impl/SimpleLocalRepositoryManagerFactory;"}], "flds": [{"acc": 25, "nme": "NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "simple"}, {"acc": 2, "nme": "priority", "dsc": "F"}, {"acc": 2, "nme": "localPathComposer", "dsc": "Lorg/eclipse/aether/internal/impl/LocalPathComposer;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;", "vals": ["value", "simple"]}]}, "org/eclipse/aether/internal/impl/synccontext/named/NamedLockFactoryAdapter.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/internal/impl/synccontext/named/NamedLockFactoryAdapter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;Lorg/eclipse/aether/named/NamedLockFactory;)V"}, {"nme": "newInstance", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Z)Lorg/eclipse/aether/SyncContext;"}, {"nme": "getNameMapper", "acc": 1, "dsc": "()Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;"}, {"nme": "getNamedLockFactory", "acc": 1, "dsc": "()Lorg/eclipse/aether/named/NamedLockFactory;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "TIME_KEY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.syncContext.named.time"}, {"acc": 25, "nme": "DEFAULT_TIME", "dsc": "J", "val": 30}, {"acc": 25, "nme": "TIME_UNIT_KEY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.syncContext.named.time.unit"}, {"acc": 25, "nme": "DEFAULT_TIME_UNIT", "dsc": "Ljava/util/concurrent/TimeUnit;"}, {"acc": 25, "nme": "RETRY_KEY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.syncContext.named.retry"}, {"acc": 25, "nme": "DEFAULT_RETRY", "dsc": "I", "val": 1}, {"acc": 25, "nme": "RETRY_WAIT_KEY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "aether.syncContext.named.retry.wait"}, {"acc": 25, "nme": "DEFAULT_RETRY_WAIT", "dsc": "J", "val": 200}, {"acc": 18, "nme": "nameMapper", "dsc": "Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;"}, {"acc": 18, "nme": "namedLockFactory", "dsc": "Lorg/eclipse/aether/named/NamedLockFactory;"}]}, "org/eclipse/aether/internal/impl/FailChecksumPolicy.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/internal/impl/FailChecksumPolicy", "super": "org/eclipse/aether/internal/impl/AbstractChecksumPolicy", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/transfer/TransferResource;)V"}, {"nme": "onTransferChecksumFailure", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/ChecksumFailureException;)Z"}], "flds": []}, "org/eclipse/aether/internal/impl/collect/DataPool$Descriptor.class": {"ver": 52, "acc": 1056, "nme": "org/eclipse/aether/internal/impl/collect/DataPool$Descriptor", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "toResult", "acc": 1025, "dsc": "(Lorg/eclipse/aether/resolution/ArtifactDescriptorRequest;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;"}], "flds": []}, "org/eclipse/aether/internal/impl/synccontext/named/providers/DiscriminatingNameMapperProvider.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/synccontext/named/providers/DiscriminatingNameMapperProvider", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "get", "acc": 1, "dsc": "()Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;"}, {"nme": "get", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 18, "nme": "mapper", "dsc": "Lorg/eclipse/aether/internal/impl/synccontext/named/NameMapper;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;", "vals": ["value", "discriminating"]}]}, "org/eclipse/aether/internal/impl/DefaultRepositorySystemLifecycle.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/internal/impl/DefaultRepositorySystemLifecycle", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "systemEnded", "acc": 1, "dsc": "()V"}, {"nme": "addOnSystemEndedHandler", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Runnable;)V"}, {"nme": "requireNotShutdown", "acc": 2, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "shutdown", "dsc": "Ljava/util/concurrent/atomic/AtomicBoolean;"}, {"acc": 18, "nme": "onSystemEndedHandlers", "dsc": "Ljava/util/concurrent/CopyOnWriteArrayList;", "sig": "Ljava/util/concurrent/CopyOnWriteArrayList<Ljava/lang/Runnable;>;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;"}]}, "org/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$DoneFuture.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$DoneFuture", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TV;)V"}, {"nme": "cancel", "acc": 1, "dsc": "(Z)Z"}, {"nme": "isCancelled", "acc": 1, "dsc": "()Z"}, {"nme": "isDone", "acc": 1, "dsc": "()Z"}, {"nme": "get", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TV;", "exs": ["java/lang/InterruptedException", "java/util/concurrent/ExecutionException"]}, {"nme": "get", "acc": 1, "dsc": "(J<PERSON>ja<PERSON>/util/concurrent/TimeUnit;)Ljava/lang/Object;", "sig": "(JLjava/util/concurrent/TimeUnit;)TV;", "exs": ["java/lang/InterruptedException", "java/util/concurrent/ExecutionException", "java/util/concurrent/TimeoutException"]}], "flds": [{"acc": 18, "nme": "v", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;", "sig": "TV;"}]}}}}