package shyrcs.extrastoragehook.application;

import org.bukkit.plugin.Plugin;
import org.bukkit.plugin.RegisteredServiceProvider;
import org.bukkit.plugin.java.JavaPlugin;
import shyrcs.extrastoragehook.SbMagicHook;
import shyrcs.extrastoragehook.bridge.BridgeHook;
import shyrcs.extrastoragehook.bridge.StorageHook;
import shyrcs.extrastoragehook.config.DataConfig;
import shyrcs.extrastoragehook.discord.BotImpl;
import shyrcs.extrastoragehook.discord.BotSetup;
import shyrcs.extrastoragehook.executor.CommandManager;
import shyrcs.extrastoragehook.extrastorage.ExtraStorageHook;
import shyrcs.extrastoragehook.minecraft.CommandSbMagicHook;
import shyrcs.extrastoragehook.minecraft.PlayerListener;

import net.milkbowl.vault.economy.Economy;

import java.util.Objects;
import java.util.logging.Level;

/**
 * Class khởi động chính của plugin
 * <PERSON><PERSON>ản lý việc khởi tạo tất cả các component
 */
public class PluginBoot {
    
    public static final String[] depends = new String[] { "ExtraStorage", "Vault" };
    
    public static String config;
    public static JavaPlugin main;
    
    /**
     * Khởi động plugin
     */
    public static void start(JavaPlugin plugin) {
        main = plugin;
        
        // Lưu config mặc định
        plugin.saveDefaultConfig();
        
        // Khởi tạo config
        Library.config = new DataConfig(plugin.getDataFolder() + "/config.yml");
        config = Library.config.getConfigFile().getAbsolutePath();
        
        // Kiểm tra bot token
        if(Library.config.getBotToken().trim().isEmpty()) {
            forceShutdown("no_token");
            return;
        }
        
        // Kiểm tra guild ID
        if(Library.config.getGuildID().trim().isEmpty()) {
            forceShutdown("no_guild");
            return;
        }
        
        // Kiểm tra dependencies
        for(String dependency: depends) {
            if(main.getServer().getPluginManager().getPlugin(dependency) == null) {
                forceShutdown("dependency_error");
                return;
            }
        }
        
        try {
            // Khởi tạo ExtraStorage hook
            Plugin extraStoragePlugin = Objects.requireNonNull(
                main.getServer().getPluginManager().getPlugin(depends[0])
            );
            Library.extraStorageHook = new ExtraStorageHook(extraStoragePlugin);
            
            // Khởi tạo Vault Economy
            RegisteredServiceProvider<Economy> service = main.getServer()
                .getServicesManager().getRegistration(Economy.class);
            if (service != null) {
                Library.economy = service.getProvider();
            } else {
                SbMagicHook.warn("Không tìm thấy Economy provider!");
            }
            
            // Khởi tạo các component khác
            Library.bridge = new BridgeHook((long)Library.config.getCodeTimeout() * 20);
            Library.storage = new StorageHook();
            Library.database = new shyrcs.extrastoragehook.database.LiteDatabase(plugin.getDataFolder());

            // Sync existing connections từ database vào memory
            syncConnectionsFromDatabase();



            Library.manager = new CommandManager();
            Library.economyManager = new shyrcs.extrastoragehook.economy.EconomyManager();
            
            // Thiết lập Discord bot
            BotSetup.buildInternalCommands();
            Library.application = new BotImpl(Library.config.getBotToken());
            
            SbMagicHook.console.log(Level.INFO, "Hoàn thành thiết lập tất cả component Discord!");
            
            // Đăng ký commands và events
            Objects.requireNonNull(main.getCommand("sbmagichook"))
                .setExecutor(new CommandSbMagicHook());
            main.getServer().getPluginManager().registerEvents(new PlayerListener(), main);

            // Đăng ký Discord commands
            registerDiscordCommands();
            
        } catch(Exception err) { 
            SbMagicHook.error("Lỗi khi khởi tạo plugin: " + err.getMessage());
            err.printStackTrace();
            forceShutdown("invalid_plugin"); 
        }
    }
    
    /**
     * Tắt plugin
     */
    public static void stop() {
        try {
            if (Library.application != null) {
                Library.application.shutdown();
            }

            // Đóng database
            if (Library.database != null) {
                Library.database.close();
            }

            SbMagicHook.info("Plugin đã được tắt thành công!");
        } catch (Exception e) {
            SbMagicHook.error("Lỗi khi tắt plugin: " + e.getMessage());
        }
    }

    /**
     * Đăng ký Discord commands
     */
    private static void registerDiscordCommands() {
        try {
            Library.manager.registerCommand("storage", new shyrcs.extrastoragehook.discord.commands.CommandStorage());
            Library.manager.registerCommand("sell", new shyrcs.extrastoragehook.discord.commands.CommandSell());
            Library.manager.registerCommand("unlink", new shyrcs.extrastoragehook.discord.commands.CommandUnlink());
            Library.manager.registerCommand("connect", new shyrcs.extrastoragehook.discord.commands.CommandConnect());
            Library.manager.registerCommand("help", new shyrcs.extrastoragehook.discord.commands.CommandHelp());

            SbMagicHook.info("Đã đăng ký " + Library.manager.getCommandNames().size() + " Discord commands");
        } catch (Exception e) {
            SbMagicHook.error("Lỗi khi đăng ký Discord commands: " + e.getMessage());
        }
    }

    /**
     * Sync connections từ database vào in-memory storage
     */
    private static void syncConnectionsFromDatabase() {
        try {
            var connections = Library.database.getAllConnections();
            for (var entry : connections.entrySet()) {
                String discordId = entry.getKey();
                java.util.UUID minecraftUuid = entry.getValue();
                Library.storage.connect(discordId, minecraftUuid);
            }
            SbMagicHook.info("Synced " + connections.size() + " connections from database to memory");
        } catch (Exception e) {
            SbMagicHook.error("Failed to sync connections from database: " + e.getMessage());
        }
    }

    /**
     * Buộc tắt plugin với lý do
     */
    public static void forceShutdown(String reason) {
        switch(reason) {
            case "no_token":
                SbMagicHook.error("Không tìm thấy Discord bot token trong config!");
                break;
            case "no_guild":
                SbMagicHook.error("Không tìm thấy Guild ID trong config!");
                break;
            case "dependency_error":
                SbMagicHook.error("Thiếu plugin dependency cần thiết!");
                break;
            case "invalid_plugin":
                SbMagicHook.error("Plugin dependency không hợp lệ!");
                break;
            default:
                SbMagicHook.error("Lỗi không xác định: " + reason);
        }
        main.getServer().getPluginManager().disablePlugin(main);
    }
}
