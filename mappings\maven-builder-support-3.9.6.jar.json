{"md5": "e004677a88ec086ba79a6a646b120f06", "sha2": "bcfc9d8175eaba21111edf21e0355a8523461abc", "sha256": "e1f4d2784459ce8a34b9dae1829a1999b569e483e21ee9faa7368691e729296e", "contents": {"classes": {"org/apache/maven/building/ProblemCollector.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/building/ProblemCollector", "super": "java/lang/Object", "mthds": [{"nme": "add", "acc": 1025, "dsc": "(Lorg/apache/maven/building/Problem$Severity;Ljava/lang/String;IILjava/lang/Exception;)V"}, {"nme": "setSource", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getProblems", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/building/Problem;>;"}], "flds": []}, "org/apache/maven/building/Source.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/building/Source", "super": "java/lang/Object", "mthds": [{"nme": "getInputStream", "acc": 1025, "dsc": "()Ljava/io/InputStream;", "exs": ["java/io/IOException"]}, {"nme": "getLocation", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "org/apache/maven/building/ProblemCollectorFactory.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/building/ProblemCollectorFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "newInstance", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/apache/maven/building/ProblemCollector;", "sig": "(<PERSON><PERSON><PERSON>/util/List<Lorg/apache/maven/building/Problem;>;)Lorg/apache/maven/building/ProblemCollector;"}], "flds": []}, "org/apache/maven/building/UrlSource.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/building/UrlSource", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/net/URL;)V"}, {"nme": "getInputStream", "acc": 1, "dsc": "()Ljava/io/InputStream;", "exs": ["java/io/IOException"]}, {"nme": "getLocation", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getUrl", "acc": 1, "dsc": "()Ljava/net/URL;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "url", "dsc": "Ljava/net/URL;"}]}, "org/apache/maven/building/DefaultProblem.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/building/DefaultProblem", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lja<PERSON>/lang/String;Lorg/apache/maven/building/Problem$Severity;Ljava/lang/String;IILjava/lang/Exception;)V"}, {"nme": "getSource", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLineNumber", "acc": 1, "dsc": "()I"}, {"nme": "getColumnNumber", "acc": 1, "dsc": "()I"}, {"nme": "getLocation", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getException", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Exception;"}, {"nme": "getMessage", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getSeverity", "acc": 1, "dsc": "()Lorg/apache/maven/building/Problem$Severity;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "source", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "lineNumber", "dsc": "I"}, {"acc": 18, "nme": "columnNumber", "dsc": "I"}, {"acc": 18, "nme": "message", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "exception", "dsc": "<PERSON><PERSON><PERSON>/lang/Exception;"}, {"acc": 18, "nme": "severity", "dsc": "Lorg/apache/maven/building/Problem$Severity;"}]}, "org/apache/maven/building/Problem.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/building/Problem", "super": "java/lang/Object", "mthds": [{"nme": "getSource", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLineNumber", "acc": 1025, "dsc": "()I"}, {"nme": "getColumnNumber", "acc": 1025, "dsc": "()I"}, {"nme": "getLocation", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getException", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/Exception;"}, {"nme": "getMessage", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getSeverity", "acc": 1025, "dsc": "()Lorg/apache/maven/building/Problem$Severity;"}], "flds": []}, "org/apache/maven/building/StringSource.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/building/StringSource", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;<PERSON>ja<PERSON>/lang/String;)V"}, {"nme": "getInputStream", "acc": 1, "dsc": "()Ljava/io/InputStream;", "exs": ["java/io/IOException"]}, {"nme": "getLocation", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "get<PERSON>ontent", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "content", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "location", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/apache/maven/building/Problem$Severity.class": {"ver": 52, "acc": 16433, "nme": "org/apache/maven/building/Problem$Severity", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lorg/apache/maven/building/Problem$Severity;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/apache/maven/building/Problem$Severity;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Lorg/apache/maven/building/Problem$Severity;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "FATAL", "dsc": "Lorg/apache/maven/building/Problem$Severity;"}, {"acc": 16409, "nme": "ERROR", "dsc": "Lorg/apache/maven/building/Problem$Severity;"}, {"acc": 16409, "nme": "WARNING", "dsc": "Lorg/apache/maven/building/Problem$Severity;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lorg/apache/maven/building/Problem$Severity;"}]}, "org/apache/maven/building/DefaultProblemCollector.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/building/DefaultProblemCollector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(<PERSON><PERSON><PERSON>/util/List<Lorg/apache/maven/building/Problem;>;)V"}, {"nme": "getProblems", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/building/Problem;>;"}, {"nme": "setSource", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "add", "acc": 1, "dsc": "(Lorg/apache/maven/building/Problem$Severity;Ljava/lang/String;IILjava/lang/Exception;)V"}], "flds": [{"acc": 2, "nme": "problems", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/building/Problem;>;"}, {"acc": 2, "nme": "source", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/apache/maven/building/FileSource.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/building/FileSource", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V"}, {"nme": "getInputStream", "acc": 1, "dsc": "()Ljava/io/InputStream;", "exs": ["java/io/IOException"]}, {"nme": "getLocation", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getFile", "acc": 1, "dsc": "()Ljava/io/File;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "file", "dsc": "Ljava/io/File;"}]}}}}