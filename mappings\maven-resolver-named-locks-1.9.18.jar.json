{"md5": "e6da3cfadecd128a47814dfc661163dc", "sha2": "31f948d89dcb3d9739e70d5e1000ebd68eb4405d", "sha256": "098de7bbc5b0b26c3eff74ac30ffba6680fdab9bf4aebab95c3f5e2fe9eaeea8", "contents": {"classes": {"org/eclipse/aether/named/support/ReadWriteLockNamedLock.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/named/support/ReadWriteLockNamedLock", "super": "org/eclipse/aether/named/support/NamedLockSupport", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Lorg/eclipse/aether/named/support/NamedLockFactorySupport;Ljava/util/concurrent/locks/ReadWriteLock;)V"}, {"nme": "doLockShared", "acc": 4, "dsc": "(JLjava/util/concurrent/TimeUnit;)Z", "exs": ["java/lang/InterruptedException"]}, {"nme": "doLockExclusively", "acc": 4, "dsc": "(JLjava/util/concurrent/TimeUnit;)Z", "exs": ["java/lang/InterruptedException"]}, {"nme": "do<PERSON>nlock", "acc": 4, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "threadSteps", "dsc": "<PERSON><PERSON><PERSON>/lang/ThreadLocal;", "sig": "Ljava/lang/ThreadLocal<Ljava/util/Deque<Lorg/eclipse/aether/named/support/ReadWriteLockNamedLock$Step;>;>;"}, {"acc": 18, "nme": "readWriteLock", "dsc": "Ljava/util/concurrent/locks/ReadWriteLock;"}]}, "org/eclipse/aether/named/providers/NoopNamedLockFactory$1.class": {"ver": 52, "acc": 4128, "nme": "org/eclipse/aether/named/providers/NoopNamedLockFactory$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/named/support/NamedLockSupport.class": {"ver": 52, "acc": 1057, "nme": "org/eclipse/aether/named/support/NamedLockSupport", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/eclipse/aether/named/support/NamedLockFactorySupport;)V"}, {"nme": "name", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lockShared", "acc": 1, "dsc": "(JLjava/util/concurrent/TimeUnit;)Z", "exs": ["java/lang/InterruptedException"]}, {"nme": "doLockShared", "acc": 1028, "dsc": "(JLjava/util/concurrent/TimeUnit;)Z", "exs": ["java/lang/InterruptedException"]}, {"nme": "lockExclusively", "acc": 1, "dsc": "(JLjava/util/concurrent/TimeUnit;)Z", "exs": ["java/lang/InterruptedException"]}, {"nme": "doLockExclusively", "acc": 1028, "dsc": "(JLjava/util/concurrent/TimeUnit;)Z", "exs": ["java/lang/InterruptedException"]}, {"nme": "unlock", "acc": 1, "dsc": "()V"}, {"nme": "do<PERSON>nlock", "acc": 1028, "dsc": "()V"}, {"nme": "close", "acc": 1, "dsc": "()V"}, {"nme": "doClose", "acc": 4, "dsc": "()V"}, {"nme": "diagnosticState", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/Thread;L<PERSON>va/util/Deque<Ljava/lang/String;>;>;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$unlock$2", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Thread;)<PERSON><PERSON><PERSON>/util/Deque;"}, {"nme": "lambda$lockExclusively$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Thread;)<PERSON><PERSON><PERSON>/util/Deque;"}, {"nme": "lambda$lockShared$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Thread;)<PERSON><PERSON><PERSON>/util/Deque;"}], "flds": [{"acc": 20, "nme": "logger", "dsc": "Lorg/slf4j/Logger;"}, {"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "factory", "dsc": "Lorg/eclipse/aether/named/support/NamedLockFactorySupport;"}, {"acc": 18, "nme": "diagnosticState", "dsc": "Ljava/util/concurrent/ConcurrentHashMap;", "sig": "Ljava/util/concurrent/ConcurrentHashMap<Ljava/lang/Thread;Ljava/util/Deque<Ljava/lang/String;>;>;"}]}, "org/eclipse/aether/named/providers/LocalSemaphoreNamedLockFactory$1.class": {"ver": 52, "acc": 4128, "nme": "org/eclipse/aether/named/providers/LocalSemaphoreNamedLockFactory$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/named/providers/FileLockNamedLockFactory.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/named/providers/FileLockNamedLockFactory", "super": "org/eclipse/aether/named/support/NamedLockFactorySupport", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "createLock", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/named/support/NamedLockSupport;"}, {"nme": "destroyLock", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "lambda$createLock$1", "acc": 4106, "dsc": "(Ljava/nio/file/Path;Ljava/lang/String;Ljava/lang/String;)Ljava/nio/channels/FileChannel;"}, {"nme": "lambda$createLock$0", "acc": 4106, "dsc": "(Ljava/nio/file/Path;)Ljava/nio/channels/FileChannel;", "exs": ["java/lang/Exception"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "file-lock"}, {"acc": 26, "nme": "DELETE_LOCK_FILES", "dsc": "Z"}, {"acc": 26, "nme": "ATTEMPTS", "dsc": "I"}, {"acc": 26, "nme": "SLEEP_MILLIS", "dsc": "J"}, {"acc": 18, "nme": "fileChannels", "dsc": "Ljava/util/concurrent/ConcurrentMap;", "sig": "Ljava/util/concurrent/ConcurrentMap<Ljava/lang/String;Ljava/nio/channels/FileChannel;>;"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;", "vals": ["value", "file-lock"]}]}, "org/eclipse/aether/named/providers/LocalSemaphoreNamedLockFactory$JVMSemaphore.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/named/providers/LocalSemaphoreNamedLockFactory$JVMSemaphore", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/concurrent/Semaphore;)V"}, {"nme": "tryAcquire", "acc": 1, "dsc": "(IJLjava/util/concurrent/TimeUnit;)Z", "exs": ["java/lang/InterruptedException"]}, {"nme": "release", "acc": 1, "dsc": "(I)V"}, {"nme": "<init>", "acc": 4096, "dsc": "(Ljava/util/concurrent/Semaphore;Lorg/eclipse/aether/named/providers/LocalSemaphoreNamedLockFactory$1;)V"}], "flds": [{"acc": 18, "nme": "semaphore", "dsc": "<PERSON><PERSON><PERSON>/util/concurrent/Semaphore;"}]}, "org/eclipse/aether/named/support/ReadWriteLockNamedLock$Step.class": {"ver": 52, "acc": 16432, "nme": "org/eclipse/aether/named/support/ReadWriteLockNamedLock$Step", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lorg/eclipse/aether/named/support/ReadWriteLockNamedLock$Step;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;)Lorg/eclipse/aether/named/support/ReadWriteLockNamedLock$Step;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Lorg/eclipse/aether/named/support/ReadWriteLockNamedLock$Step;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "SHARED", "dsc": "Lorg/eclipse/aether/named/support/ReadWriteLockNamedLock$Step;"}, {"acc": 16409, "nme": "EXCLUSIVE", "dsc": "Lorg/eclipse/aether/named/support/ReadWriteLockNamedLock$Step;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lorg/eclipse/aether/named/support/ReadWriteLockNamedLock$Step;"}]}, "org/eclipse/aether/named/NamedLock.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/named/NamedLock", "super": "java/lang/Object", "mthds": [{"nme": "name", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lockShared", "acc": 1025, "dsc": "(JLjava/util/concurrent/TimeUnit;)Z", "exs": ["java/lang/InterruptedException"]}, {"nme": "lockExclusively", "acc": 1025, "dsc": "(JLjava/util/concurrent/TimeUnit;)Z", "exs": ["java/lang/InterruptedException"]}, {"nme": "unlock", "acc": 1025, "dsc": "()V"}, {"nme": "close", "acc": 1025, "dsc": "()V"}], "flds": []}, "org/eclipse/aether/named/support/FileLockNamedLock.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/named/support/FileLockNamedLock", "super": "org/eclipse/aether/named/support/NamedLockSupport", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/lang/String;Ljava/nio/channels/FileChannel;Lorg/eclipse/aether/named/support/NamedLockFactorySupport;)V"}, {"nme": "doLockShared", "acc": 4, "dsc": "(JLjava/util/concurrent/TimeUnit;)Z", "exs": ["java/lang/InterruptedException"]}, {"nme": "doLockExclusively", "acc": 4, "dsc": "(JLjava/util/concurrent/TimeUnit;)Z", "exs": ["java/lang/InterruptedException"]}, {"nme": "doLockShared", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/<PERSON>an;"}, {"nme": "doLockExclusively", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/<PERSON>an;"}, {"nme": "do<PERSON>nlock", "acc": 4, "dsc": "()V"}, {"nme": "anyOtherThreadHasSteps", "acc": 2, "dsc": "()Z"}, {"nme": "obtainFileLock", "acc": 2, "dsc": "(Z)Ljava/nio/channels/FileLock;"}, {"nme": "lambda$anyOtherThreadHasSteps$4", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Deque;)Z"}, {"nme": "lambda$anyOtherThreadHasSteps$3", "acc": 4106, "dsc": "(Ljava/util/Map$Entry;)Z"}, {"nme": "lambda$doUnlock$2", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Thread;)<PERSON><PERSON><PERSON>/util/Deque;"}, {"nme": "lambda$doLockExclusively$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Thread;)<PERSON><PERSON><PERSON>/util/Deque;"}, {"nme": "lambda$doLockShared$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Thread;)<PERSON><PERSON><PERSON>/util/Deque;"}], "flds": [{"acc": 26, "nme": "RETRY_SLEEP_MILLIS", "dsc": "J", "val": 100}, {"acc": 26, "nme": "LOCK_POSITION", "dsc": "J", "val": 0}, {"acc": 26, "nme": "LOCK_SIZE", "dsc": "J", "val": 1}, {"acc": 18, "nme": "threadSteps", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Thread;Ljava/util/Deque<Ljava/lang/Boolean;>;>;"}, {"acc": 18, "nme": "fileChannel", "dsc": "Ljava/nio/channels/FileChannel;"}, {"acc": 18, "nme": "fileLockRef", "dsc": "Ljava/util/concurrent/atomic/AtomicReference;", "sig": "Ljava/util/concurrent/atomic/AtomicReference<Ljava/nio/channels/FileLock;>;"}, {"acc": 18, "nme": "criticalRegion", "dsc": "Ljava/util/concurrent/locks/ReentrantLock;"}]}, "org/eclipse/aether/named/support/AdaptedSemaphoreNamedLock$AdaptedSemaphore.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/named/support/AdaptedSemaphoreNamedLock$AdaptedSemaphore", "super": "java/lang/Object", "mthds": [{"nme": "tryAcquire", "acc": 1025, "dsc": "(IJLjava/util/concurrent/TimeUnit;)Z", "exs": ["java/lang/InterruptedException"]}, {"nme": "release", "acc": 1025, "dsc": "(I)V"}], "flds": []}, "org/eclipse/aether/named/providers/LocalReadWriteLockNamedLockFactory.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/named/providers/LocalReadWriteLockNamedLockFactory", "super": "org/eclipse/aether/named/support/NamedLockFactorySupport", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "createLock", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/named/support/ReadWriteLockNamedLock;"}, {"nme": "createLock", "acc": 4164, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/named/support/NamedLockSupport;"}], "flds": [{"acc": 25, "nme": "NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "rwlock-local"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;", "vals": ["value", "rwlock-local"]}]}, "org/eclipse/aether/named/support/Retry.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/named/support/Retry", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "retry", "acc": 9, "dsc": "(JLjava/util/concurrent/TimeUnit;JLjava/util/concurrent/Callable;Ljava/util/function/Predicate;<PERSON><PERSON><PERSON>/lang/Object;)Ljava/lang/Object;", "sig": "<R:Ljava/lang/Object;>(JLjava/util/concurrent/TimeUnit;JLjava/util/concurrent/Callable<TR;>;Ljava/util/function/Predicate<Ljava/lang/Exception;>;TR;)TR;", "exs": ["java/lang/InterruptedException"]}, {"nme": "retry", "acc": 9, "dsc": "(IJL<PERSON>va/util/concurrent/Callable;Ljava/util/function/Predicate;<PERSON><PERSON><PERSON>/lang/Object;)L<PERSON><PERSON>/lang/Object;", "sig": "<R:Ljava/lang/Object;>(IJLjava/util/concurrent/Callable<TR;>;Ljava/util/function/Predicate<Ljava/lang/Exception;>;TR;)TR;", "exs": ["java/lang/InterruptedException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "LOGGER", "dsc": "Lorg/slf4j/Logger;"}]}, "org/eclipse/aether/named/support/NamedLockFactorySupport.class": {"ver": 52, "acc": 1057, "nme": "org/eclipse/aether/named/support/NamedLockFactorySupport", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Z)V"}, {"nme": "isDiagnosticEnabled", "acc": 1, "dsc": "()Z"}, {"nme": "getLock", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/named/support/NamedLockSupport;"}, {"nme": "shutdown", "acc": 1, "dsc": "()V"}, {"nme": "onFailure", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)<PERSON><PERSON><PERSON>/lang/Throwable;", "sig": "<E:Ljava/lang/Throwable;>(TE;)TE;"}, {"nme": "closeLock", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "createLock", "acc": 1028, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/named/support/NamedLockSupport;"}, {"nme": "destroyLock", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getLock", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/named/NamedLock;"}, {"nme": "lambda$closeLock$2", "acc": 4098, "dsc": "(Ljava/lang/String;Lorg/eclipse/aether/named/support/NamedLockFactorySupport$NamedLockHolder;)Lorg/eclipse/aether/named/support/NamedLockFactorySupport$NamedLockHolder;"}, {"nme": "lambda$onFailure$1", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/Thread;<PERSON><PERSON><PERSON>/util/Deque;)V"}, {"nme": "lambda$getLock$0", "acc": 4098, "dsc": "(Ljava/lang/String;Lorg/eclipse/aether/named/support/NamedLockFactorySupport$NamedLockHolder;)Lorg/eclipse/aether/named/support/NamedLockFactorySupport$NamedLockHolder;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "DIAGNOSTIC_ENABLED", "dsc": "Z"}, {"acc": 20, "nme": "logger", "dsc": "Lorg/slf4j/Logger;"}, {"acc": 18, "nme": "locks", "dsc": "Ljava/util/concurrent/ConcurrentMap;", "sig": "Ljava/util/concurrent/ConcurrentMap<Ljava/lang/String;Lorg/eclipse/aether/named/support/NamedLockFactorySupport$NamedLockHolder;>;"}, {"acc": 18, "nme": "diagnosticEnabled", "dsc": "Z"}]}, "org/eclipse/aether/named/support/LockUpgradeNotSupportedException.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/named/support/LockUpgradeNotSupportedException", "super": "java/lang/RuntimeException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/named/support/NamedLockSupport;)V"}], "flds": []}, "org/eclipse/aether/named/providers/NoopNamedLockFactory$NoopNamedLock.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/named/providers/NoopNamedLockFactory$NoopNamedLock", "super": "org/eclipse/aether/named/support/NamedLockSupport", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/eclipse/aether/named/support/NamedLockFactorySupport;)V"}, {"nme": "doLockShared", "acc": 4, "dsc": "(JLjava/util/concurrent/TimeUnit;)Z"}, {"nme": "doLockExclusively", "acc": 4, "dsc": "(JLjava/util/concurrent/TimeUnit;)Z"}, {"nme": "do<PERSON>nlock", "acc": 4, "dsc": "()V"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lja<PERSON>/lang/String;Lorg/eclipse/aether/named/support/NamedLockFactorySupport;Lorg/eclipse/aether/named/providers/NoopNamedLockFactory$1;)V"}], "flds": []}, "org/eclipse/aether/named/providers/LocalSemaphoreNamedLockFactory.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/named/providers/LocalSemaphoreNamedLockFactory", "super": "org/eclipse/aether/named/support/NamedLockFactorySupport", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "createLock", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/named/support/AdaptedSemaphoreNamedLock;"}, {"nme": "createLock", "acc": 4164, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/named/support/NamedLockSupport;"}], "flds": [{"acc": 25, "nme": "NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "semaphore-local"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;", "vals": ["value", "semaphore-local"]}]}, "org/eclipse/aether/named/providers/NoopNamedLockFactory.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/named/providers/NoopNamedLockFactory", "super": "org/eclipse/aether/named/support/NamedLockFactorySupport", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "createLock", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/named/providers/NoopNamedLockFactory$NoopNamedLock;"}, {"nme": "createLock", "acc": 4164, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/named/support/NamedLockSupport;"}], "flds": [{"acc": 25, "nme": "NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "noop"}], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;", "vals": ["value", "noop"]}]}, "org/eclipse/aether/named/support/NamedLockFactorySupport$NamedLockHolder.class": {"ver": 52, "acc": 48, "nme": "org/eclipse/aether/named/support/NamedLockFactorySupport$NamedLockHolder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lorg/eclipse/aether/named/support/NamedLockSupport;)V"}, {"nme": "incRef", "acc": 2, "dsc": "()I"}, {"nme": "decRef", "acc": 2, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "access$000", "acc": 4104, "dsc": "(Lorg/eclipse/aether/named/support/NamedLockFactorySupport$NamedLockHolder;)Lorg/eclipse/aether/named/support/NamedLockSupport;"}, {"nme": "access$100", "acc": 4104, "dsc": "(Lorg/eclipse/aether/named/support/NamedLockFactorySupport$NamedLockHolder;)Ljava/util/concurrent/atomic/AtomicInteger;"}, {"nme": "access$200", "acc": 4104, "dsc": "(Lorg/eclipse/aether/named/support/NamedLockFactorySupport$NamedLockHolder;)I"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/eclipse/aether/named/support/NamedLockSupport;Lorg/eclipse/aether/named/support/NamedLockFactorySupport$1;)V"}, {"nme": "access$400", "acc": 4104, "dsc": "(Lorg/eclipse/aether/named/support/NamedLockFactorySupport$NamedLockHolder;)I"}], "flds": [{"acc": 18, "nme": "named<PERSON><PERSON>", "dsc": "Lorg/eclipse/aether/named/support/NamedLockSupport;"}, {"acc": 18, "nme": "referenceCount", "dsc": "Ljava/util/concurrent/atomic/AtomicInteger;"}]}, "org/eclipse/aether/named/NamedLockFactory.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/named/NamedLockFactory", "super": "java/lang/Object", "mthds": [{"nme": "getLock", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/named/NamedLock;"}, {"nme": "shutdown", "acc": 1025, "dsc": "()V"}, {"nme": "onFailure", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)<PERSON><PERSON><PERSON>/lang/Throwable;", "sig": "<E:Ljava/lang/Throwable;>(TE;)TE;"}], "flds": []}, "org/eclipse/aether/named/support/AdaptedSemaphoreNamedLock.class": {"ver": 52, "acc": 33, "nme": "org/eclipse/aether/named/support/AdaptedSemaphoreNamedLock", "super": "org/eclipse/aether/named/support/NamedLockSupport", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/lang/String;Lorg/eclipse/aether/named/support/NamedLockFactorySupport;Lorg/eclipse/aether/named/support/AdaptedSemaphoreNamedLock$AdaptedSemaphore;)V"}, {"nme": "doLockShared", "acc": 4, "dsc": "(JLjava/util/concurrent/TimeUnit;)Z", "exs": ["java/lang/InterruptedException"]}, {"nme": "doLockExclusively", "acc": 4, "dsc": "(JLjava/util/concurrent/TimeUnit;)Z", "exs": ["java/lang/InterruptedException"]}, {"nme": "do<PERSON>nlock", "acc": 4, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "NONE", "dsc": "I", "val": 0}, {"acc": 26, "nme": "SHARED", "dsc": "I", "val": 1}, {"acc": 26, "nme": "EXCLUSIVE", "dsc": "I", "val": 2147483647}, {"acc": 18, "nme": "threadPerms", "dsc": "<PERSON><PERSON><PERSON>/lang/ThreadLocal;", "sig": "<PERSON><PERSON><PERSON>/lang/ThreadLocal<Ljava/util/Deque<Lja<PERSON>/lang/Integer;>;>;"}, {"acc": 18, "nme": "semaphore", "dsc": "Lorg/eclipse/aether/named/support/AdaptedSemaphoreNamedLock$AdaptedSemaphore;"}]}, "org/eclipse/aether/named/support/NamedLockFactorySupport$1.class": {"ver": 52, "acc": 4128, "nme": "org/eclipse/aether/named/support/NamedLockFactorySupport$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/named/support/Retry$DoNotRetry.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/named/support/Retry$DoNotRetry", "super": "java/lang/Object", "mthds": [], "flds": []}}}}