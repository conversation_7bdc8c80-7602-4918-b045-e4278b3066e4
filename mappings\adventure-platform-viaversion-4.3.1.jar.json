{"md5": "d7330e3e448ffe72c67cb2fb9f4dff49", "sha2": "f88d6816a53f4c3a43f76de0a9ee7cd50f5f397f", "sha256": "df463083060a51403df0cfe3e08e3c8890103bfc707e9235f6279e87c619753f", "contents": {"classes": {"net/kyori/adventure/platform/viaversion/ViaFacet.class": {"ver": 52, "acc": 33, "nme": "net/kyori/adventure/platform/viaversion/ViaFacet", "super": "net/kyori/adventure/platform/facet/FacetBase", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/util/function/Function;I)V", "sig": "(Ljava/lang/Class<+TV;>;Ljava/util/function/Function<TV;Lcom/viaversion/viaversion/api/connection/UserConnection;>;I)V"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "isApplicable", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z", "sig": "(TV;)Z"}, {"nme": "findConnection", "acc": 1, "dsc": "(Lja<PERSON>/lang/Object;)Lcom/viaversion/viaversion/api/connection/UserConnection;", "sig": "(TV;)Lcom/viaversion/viaversion/api/connection/UserConnection;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "findProtocol", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)I", "sig": "(TV;)I"}, {"nme": "createMessage", "acc": 1, "dsc": "(Lja<PERSON>/lang/Object;Lnet/kyori/adventure/text/Component;)Ljava/lang/String;", "sig": "(TV;Lnet/kyori/adventure/text/Component;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "createMessage", "acc": 4161, "dsc": "(L<PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/text/Component;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "PACKAGE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.viaversion.viaversion"}, {"acc": 26, "nme": "SUPPORTED_VIA_MAJOR_VERSION", "dsc": "I", "val": 4}, {"acc": 26, "nme": "SUPPORTED", "dsc": "Z"}, {"acc": 18, "nme": "connectionFunction", "dsc": "Ljava/util/function/Function;", "sig": "Ljava/util/function/Function<TV;Lcom/viaversion/viaversion/api/connection/UserConnection;>;"}, {"acc": 18, "nme": "minProtocol", "dsc": "I"}]}, "net/kyori/adventure/platform/viaversion/ViaFacet$1.class": {"ver": 52, "acc": 4128, "nme": "net/kyori/adventure/platform/viaversion/ViaFacet$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "net/kyori/adventure/platform/viaversion/ViaFacet$BossBar$Builder.class": {"ver": 52, "acc": 33, "nme": "net/kyori/adventure/platform/viaversion/ViaFacet$BossBar$Builder", "super": "net/kyori/adventure/platform/viaversion/ViaFacet", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lja<PERSON>/lang/Class;Ljava/util/function/Function;)V", "sig": "(Ljava/lang/Class<+TV;>;Ljava/util/function/Function<TV;Lcom/viaversion/viaversion/api/connection/UserConnection;>;)V"}, {"nme": "createBossBar", "acc": 1, "dsc": "(Ljava/util/Collection;)Lnet/kyori/adventure/platform/facet/Facet$BossBar;", "sig": "(Ljava/util/Collection<TV;>;)Lnet/kyori/adventure/platform/facet/Facet$BossBar<TV;>;"}, {"nme": "createMessage", "acc": 4161, "dsc": "(L<PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/text/Component;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": []}, "net/kyori/adventure/platform/viaversion/ViaFacet$ActionBar.class": {"ver": 52, "acc": 33, "nme": "net/kyori/adventure/platform/viaversion/ViaFacet$ActionBar", "super": "net/kyori/adventure/platform/viaversion/ViaFacet$Chat", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lja<PERSON>/lang/Class;Ljava/util/function/Function;)V", "sig": "(Ljava/lang/Class<+TV;>;Ljava/util/function/Function<TV;Lcom/viaversion/viaversion/api/connection/UserConnection;>;)V"}, {"nme": "createMessageType", "acc": 1, "dsc": "(Lnet/kyori/adventure/audience/MessageType;)B"}, {"nme": "sendMessage", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;)V", "sig": "(TV;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "sendMessage", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}], "flds": []}, "net/kyori/adventure/platform/viaversion/ViaFacet$ProtocolBased.class": {"ver": 52, "acc": 33, "nme": "net/kyori/adventure/platform/viaversion/ViaFacet$ProtocolBased", "super": "net/kyori/adventure/platform/viaversion/ViaFacet", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;<PERSON>java/lang/String;Ljava/lang/Class;Ljava/util/function/Function;)V", "sig": "(Ljava/lang/String;Ljava/lang/String;ILjava/lang/String;Ljava/lang/Class<+TV;>;Ljava/util/function/Function<TV;Lcom/viaversion/viaversion/api/connection/UserConnection;>;)V"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "createPacket", "acc": 1, "dsc": "(Ljava/lang/Object;)Lcom/viaversion/viaversion/api/protocol/packet/PacketWrapper;", "sig": "(TV;)Lcom/viaversion/viaversion/api/protocol/packet/PacketWrapper;"}, {"nme": "sendPacket", "acc": 1, "dsc": "(Lcom/viaversion/viaversion/api/protocol/packet/PacketWrapper;)V"}, {"nme": "parse", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/viaversion/viaversion/libs/gson/JsonElement;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "createMessage", "acc": 4161, "dsc": "(L<PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/text/Component;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": [{"acc": 18, "nme": "protocolClass", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<+Lcom/viaversion/viaversion/api/protocol/Protocol<****>;>;"}, {"acc": 18, "nme": "packetClass", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<+Lcom/viaversion/viaversion/api/protocol/packet/ClientboundPacketType;>;"}, {"acc": 18, "nme": "packetId", "dsc": "I"}]}, "net/kyori/adventure/platform/viaversion/ViaFacet$TabList.class": {"ver": 52, "acc": 49, "nme": "net/kyori/adventure/platform/viaversion/ViaFacet$TabList", "super": "net/kyori/adventure/platform/viaversion/ViaFacet$ProtocolBased", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lja<PERSON>/lang/Class;Ljava/util/function/Function;)V", "sig": "(Ljava/lang/Class<+TV;>;Ljava/util/function/Function<TV;Lcom/viaversion/viaversion/api/connection/UserConnection;>;)V"}, {"nme": "send", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "sig": "(TV;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "send", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}], "flds": []}, "net/kyori/adventure/platform/viaversion/ViaFacet$Chat.class": {"ver": 52, "acc": 33, "nme": "net/kyori/adventure/platform/viaversion/ViaFacet$Chat", "super": "net/kyori/adventure/platform/viaversion/ViaFacet$ProtocolBased", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lja<PERSON>/lang/Class;Ljava/util/function/Function;)V", "sig": "(Ljava/lang/Class<+TV;>;Ljava/util/function/Function<TV;Lcom/viaversion/viaversion/api/connection/UserConnection;>;)V"}, {"nme": "sendMessage", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/identity/Identity;Ljava/lang/String;Ljava/lang/Object;)V", "sig": "(TV;Lnet/kyori/adventure/identity/Identity;Ljava/lang/String;Ljava/lang/Object;)V"}, {"nme": "sendMessage", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/identity/Identity;Lja<PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}], "flds": []}, "net/kyori/adventure/platform/viaversion/ViaFacet$ActionBarTitle.class": {"ver": 52, "acc": 33, "nme": "net/kyori/adventure/platform/viaversion/ViaFacet$ActionBarTitle", "super": "net/kyori/adventure/platform/viaversion/ViaFacet$ProtocolBased", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lja<PERSON>/lang/Class;Ljava/util/function/Function;)V", "sig": "(Ljava/lang/Class<+TV;>;Ljava/util/function/Function<TV;Lcom/viaversion/viaversion/api/connection/UserConnection;>;)V"}, {"nme": "sendMessage", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;)V", "sig": "(TV;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "sendMessage", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}], "flds": []}, "net/kyori/adventure/platform/viaversion/ViaFacet$BossBar$Builder1_9_To_1_15.class": {"ver": 52, "acc": 33, "nme": "net/kyori/adventure/platform/viaversion/ViaFacet$BossBar$Builder1_9_To_1_15", "super": "net/kyori/adventure/platform/viaversion/ViaFacet", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lja<PERSON>/lang/Class;Ljava/util/function/Function;)V", "sig": "(Ljava/lang/Class<+TV;>;Ljava/util/function/Function<TV;Lcom/viaversion/viaversion/api/connection/UserConnection;>;)V"}, {"nme": "createBossBar", "acc": 1, "dsc": "(Ljava/util/Collection;)Lnet/kyori/adventure/platform/facet/Facet$BossBar;", "sig": "(Ljava/util/Collection<TV;>;)Lnet/kyori/adventure/platform/facet/Facet$BossBar<TV;>;"}, {"nme": "createMessage", "acc": 4161, "dsc": "(L<PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/text/Component;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": []}, "net/kyori/adventure/platform/viaversion/ViaFacet$BossBar.class": {"ver": 52, "acc": 49, "nme": "net/kyori/adventure/platform/viaversion/ViaFacet$BossBar", "super": "net/kyori/adventure/platform/viaversion/ViaFacet$ProtocolBased", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/Class;Ljava/util/function/Function;Ljava/util/Collection;)V", "sig": "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Class<+TV;>;Ljava/util/function/Function<TV;Lcom/viaversion/viaversion/api/connection/UserConnection;>;Ljava/util/Collection<TV;>;)V"}, {"nme": "bossBarInitialized", "acc": 1, "dsc": "(Lnet/kyori/adventure/bossbar/BossB<PERSON>;)V"}, {"nme": "bossBarNameChanged", "acc": 1, "dsc": "(Lnet/kyori/adventure/bossbar/BossBar;Lnet/kyori/adventure/text/Component;Lnet/kyori/adventure/text/Component;)V"}, {"nme": "bossBarProgressChanged", "acc": 1, "dsc": "(Lnet/kyori/adventure/bossbar/BossBar;FF)V"}, {"nme": "bossBarColorChanged", "acc": 1, "dsc": "(Lnet/kyori/adventure/bossbar/BossBar;Lnet/kyori/adventure/bossbar/BossBar$Color;Lnet/kyori/adventure/bossbar/BossBar$Color;)V"}, {"nme": "bossBarOverlayChanged", "acc": 1, "dsc": "(Lnet/kyori/adventure/bossbar/BossBar;Lnet/kyori/adventure/bossbar/BossBar$Overlay;Lnet/kyori/adventure/bossbar/BossBar$Overlay;)V"}, {"nme": "bossBarFlagsChanged", "acc": 1, "dsc": "(Lnet/kyori/adventure/bossbar/BossBar;<PERSON><PERSON><PERSON>/util/Set;<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(Lnet/kyori/adventure/bossbar/BossBar;Ljava/util/Set<Lnet/kyori/adventure/bossbar/BossBar$Flag;>;Ljava/util/Set<Lnet/kyori/adventure/bossbar/BossBar$Flag;>;)V"}, {"nme": "sendPacket", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;I)V", "sig": "(TV;I)V"}, {"nme": "broadcastPacket", "acc": 1, "dsc": "(I)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TV;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TV;)V"}, {"nme": "isEmpty", "acc": 1, "dsc": "()Z"}, {"nme": "close", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/Class;Ljava/util/function/Function;Ljava/util/Collection;Lnet/kyori/adventure/platform/viaversion/ViaFacet$1;)V"}], "flds": [{"acc": 18, "nme": "viewers", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<TV;>;"}, {"acc": 2, "nme": "id", "dsc": "Ljava/util/UUID;"}, {"acc": 2, "nme": "title", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "health", "dsc": "F"}, {"acc": 2, "nme": "color", "dsc": "I"}, {"acc": 2, "nme": "overlay", "dsc": "I"}, {"acc": 2, "nme": "flags", "dsc": "B"}]}, "net/kyori/adventure/platform/viaversion/ViaFacet$Title.class": {"ver": 52, "acc": 33, "nme": "net/kyori/adventure/platform/viaversion/ViaFacet$Title", "super": "net/kyori/adventure/platform/viaversion/ViaFacet$ProtocolBased", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>java/lang/String;<PERSON>java/lang/Class;Ljava/util/function/Function;)V", "sig": "(Ljava/lang/String;Ljava/lang/String;ILjava/lang/Class<+TV;>;Ljava/util/function/Function<TV;Lcom/viaversion/viaversion/api/connection/UserConnection;>;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lja<PERSON>/lang/Class;Ljava/util/function/Function;)V", "sig": "(Ljava/lang/Class<+TV;>;Ljava/util/function/Function<TV;Lcom/viaversion/viaversion/api/connection/UserConnection;>;)V"}, {"nme": "createTitleCollection", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/util/function/Consumer<Lcom/viaversion/viaversion/api/protocol/packet/PacketWrapper;>;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "contributeTitle", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/lang/String;)V", "sig": "(Ljava/util/List<Ljava/util/function/Consumer<Lcom/viaversion/viaversion/api/protocol/packet/PacketWrapper;>;>;Ljava/lang/String;)V"}, {"nme": "contributeSubtitle", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/lang/String;)V", "sig": "(Ljava/util/List<Ljava/util/function/Consumer<Lcom/viaversion/viaversion/api/protocol/packet/PacketWrapper;>;>;Ljava/lang/String;)V"}, {"nme": "contributeTimes", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;III)V", "sig": "(Ljava/util/List<Ljava/util/function/Consumer<Lcom/viaversion/viaversion/api/protocol/packet/PacketWrapper;>;>;III)V"}, {"nme": "completeTitle", "acc": 1, "dsc": "(Ljava/util/List;)Ljava/util/function/Consumer;", "sig": "(Ljava/util/List<Ljava/util/function/Consumer<Lcom/viaversion/viaversion/api/protocol/packet/PacketWrapper;>;>;)Ljava/util/function/Consumer<TV;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "showTitle", "acc": 1, "dsc": "(L<PERSON><PERSON>/lang/Object;Ljava/util/function/Consumer;)V", "sig": "(TV;Ljava/util/function/Consumer<TV;>;)V"}, {"nme": "clearTitle", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TV;)V"}, {"nme": "resetTitle", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TV;)V"}, {"nme": "showTitle", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "completeTitle", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "contributeTimes", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;III)V"}, {"nme": "contributeSubtitle", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "contributeTitle", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "createTitleCollection", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "lambda$completeTitle$3", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "lambda$contributeTimes$2", "acc": 4106, "dsc": "(IIILcom/viaversion/viaversion/api/protocol/packet/PacketWrapper;)V"}, {"nme": "lambda$contributeSubtitle$1", "acc": 4098, "dsc": "(Ljava/lang/String;Lcom/viaversion/viaversion/api/protocol/packet/PacketWrapper;)V"}, {"nme": "lambda$contributeTitle$0", "acc": 4098, "dsc": "(Ljava/lang/String;Lcom/viaversion/viaversion/api/protocol/packet/PacketWrapper;)V"}], "flds": []}}}}