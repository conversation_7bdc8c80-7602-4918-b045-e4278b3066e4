{"md5": "2f4315273dfc5dd0dc1863431a291962", "sha2": "e58a9d57dc657d14a251f944bd5bd19f5d8491fc", "sha256": "30e6fe17a9e1ea3dced0e04758012c4bf67bf2c906765dc6d4f5b905add932e5", "contents": {"classes": {"classes/org/w3c/dom/html/HTMLQuoteElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLQuoteElement", "super": "java/lang/Object", "mthds": [{"nme": "getCite", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCite", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLOptionElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLOptionElement", "super": "java/lang/Object", "mthds": [{"nme": "getForm", "acc": 1025, "dsc": "()Lorg/w3c/dom/html/HTMLFormElement;"}, {"nme": "getDefaultSelected", "acc": 1025, "dsc": "()Z"}, {"nme": "setDefaultSelected", "acc": 1025, "dsc": "(Z)V"}, {"nme": "getText", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getIndex", "acc": 1025, "dsc": "()I"}, {"nme": "getDisabled", "acc": 1025, "dsc": "()Z"}, {"nme": "setDisabled", "acc": 1025, "dsc": "(Z)V"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getSelected", "acc": 1025, "dsc": "()Z"}, {"nme": "setSelected", "acc": 1025, "dsc": "(Z)V"}, {"nme": "getValue", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setValue", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLDListElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLDListElement", "super": "java/lang/Object", "mthds": [{"nme": "getCompact", "acc": 1025, "dsc": "()Z"}, {"nme": "setCompact", "acc": 1025, "dsc": "(Z)V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLImageElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLImageElement", "super": "java/lang/Object", "mthds": [{"nme": "getLowSrc", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLowSrc", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setName", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getAlign", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAlign", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getAlt", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAlt", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getBorder", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBorder", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getHeight", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setHeight", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getHspace", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setHspace", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getIsMap", "acc": 1025, "dsc": "()Z"}, {"nme": "setIsMap", "acc": 1025, "dsc": "(Z)V"}, {"nme": "getLongDesc", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLongDesc", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getSrc", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setSrc", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getUseMap", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setUseMap", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getVspace", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setVspace", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getWidth", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLParamElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLParamElement", "super": "java/lang/Object", "mthds": [{"nme": "getName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setName", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getType", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setType", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getValue", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setValue", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getValueType", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setValueType", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLTableCaptionElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLTableCaptionElement", "super": "java/lang/Object", "mthds": [{"nme": "getAlign", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAlign", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLTableSectionElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLTableSectionElement", "super": "java/lang/Object", "mthds": [{"nme": "getAlign", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAlign", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getCh", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCh", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "get<PERSON><PERSON><PERSON>ff", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getVAlign", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setVAlign", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getRows", "acc": 1025, "dsc": "()Lorg/w3c/dom/html/HTMLCollection;"}, {"nme": "insertRow", "acc": 1025, "dsc": "(I)Lorg/w3c/dom/html/HTMLElement;", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "deleteRow", "acc": 1025, "dsc": "(I)V", "exs": ["org/w3c/dom/DOMException"]}], "flds": []}, "classes/org/w3c/dom/html/HTMLLIElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLLIElement", "super": "java/lang/Object", "mthds": [{"nme": "getType", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setType", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getValue", "acc": 1025, "dsc": "()I"}, {"nme": "setValue", "acc": 1025, "dsc": "(I)V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLObjectElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLObjectElement", "super": "java/lang/Object", "mthds": [{"nme": "getForm", "acc": 1025, "dsc": "()Lorg/w3c/dom/html/HTMLFormElement;"}, {"nme": "getCode", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCode", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getAlign", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAlign", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getArchive", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setArchive", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getBorder", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBorder", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getCodeBase", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCodeBase", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getCodeType", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCodeType", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getData", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setData", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getDeclare", "acc": 1025, "dsc": "()Z"}, {"nme": "setDeclare", "acc": 1025, "dsc": "(Z)V"}, {"nme": "getHeight", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setHeight", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getHspace", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setHspace", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setName", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getStandby", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setStandby", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getTabIndex", "acc": 1025, "dsc": "()I"}, {"nme": "setTabIndex", "acc": 1025, "dsc": "(I)V"}, {"nme": "getType", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setType", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getUseMap", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setUseMap", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getVspace", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setVspace", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getWidth", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getContentDocument", "acc": 1025, "dsc": "()Lorg/w3c/dom/Document;"}], "flds": []}, "classes/org/w3c/dom/css/CSS2Properties.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/css/CSS2Properties", "super": "java/lang/Object", "mthds": [{"nme": "getAzimuth", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAzimuth", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getBackground", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBackground", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getBackgroundAttachment", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBackgroundAttachment", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getBackgroundColor", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBackgroundColor", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getBackgroundImage", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBackgroundImage", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getBackgroundPosition", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBackgroundPosition", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getBackgroundRepeat", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBackgroundRepeat", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getBorder", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBorder", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getBorderCollapse", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBorderCollapse", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getBorderColor", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBorderColor", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getBorderSpacing", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBorderSpacing", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getBorderStyle", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBorderStyle", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getBorderTop", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBorderTop", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getBorderRight", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBorderRight", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getBorderBottom", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBorderBottom", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getBorderLeft", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBorderLeft", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getBorderTopColor", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBorderTopColor", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getBorderRightColor", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBorderRightColor", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getBorderBottomColor", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBorderBottomColor", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getBorderLeftColor", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBorderLeftColor", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getBorderTopStyle", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBorderTopStyle", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getBorderRightStyle", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBorderRightStyle", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getBorderBottomStyle", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBorderBottomStyle", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getBorderLeftStyle", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBorderLeftStyle", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getBorderTopWidth", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBorderTopWidth", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getBorderRightWidth", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBorderRightWidth", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getBorderBottomWidth", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBorderBottomWidth", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getBorderLeftWidth", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBorderLeftWidth", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getBorderWidth", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBorder<PERSON>idth", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getBottom", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBottom", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getCaptionSide", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCaptionSide", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getClear", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setClear", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getClip", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setClip", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getColor", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setColor", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "get<PERSON>ontent", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getCounterIncrement", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCounterIncrement", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getCounterReset", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCounterReset", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getCue", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCue", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getCueAfter", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCueAfter", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getCueBefore", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCueBefore", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getCursor", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCursor", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getDirection", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setDirection", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getDisplay", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setDisplay", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getElevation", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setElevation", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getEmptyCells", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "set<PERSON>mpty<PERSON>ells", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getCssFloat", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCssFloat", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getFont", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setFont", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getFontFamily", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setFontFamily", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getFontSize", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setFontSize", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getFontSizeAdjust", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setFontSizeAdjust", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getFontStretch", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setFontStretch", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getFontStyle", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setFontStyle", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getFontVariant", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setFontVariant", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getFontWeight", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setFontWeight", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getHeight", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setHeight", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getLeft", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLeft", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getLetterSpacing", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLetterSpacing", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getLineHeight", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLineHeight", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getListStyle", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setListStyle", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getListStyleImage", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setListStyleImage", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getListStylePosition", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setListStylePosition", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getListStyleType", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setListStyleType", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getMarginTop", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setMarginTop", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getMarginRight", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setMarginRight", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getMarginBottom", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setMarginBottom", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getMarginLeft", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setMarginLeft", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getMarkerOffset", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setMarkerOffset", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getMarks", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setMarks", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getMaxHeight", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setMaxHeight", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getMaxWidth", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setMaxWidth", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getMinHeight", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setMinHeight", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getOutline", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setOutline", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getOutlineColor", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setOutlineColor", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getOutlineStyle", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setOutlineStyle", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getOutlineWidth", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setOutlineWidth", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getOverflow", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setOverflow", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getPadding", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setPadding", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getPaddingTop", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setPaddingTop", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getPaddingRight", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setPaddingRight", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getPaddingBottom", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setPaddingBottom", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getPaddingLeft", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setPaddingLeft", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getPage", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setPage", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getPageBreakAfter", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setPageBreakAfter", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getPageBreakBefore", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setPageBreakBefore", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getPageBreakInside", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setPageBreakInside", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getPause", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setPause", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getPauseAfter", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setPauseAfter", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getPauseBefore", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setPauseBefore", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "get<PERSON><PERSON>", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getPitchRange", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getPlayDuring", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setPlayDuring", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getPosition", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setPosition", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getQuotes", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setQuotes", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getRichness", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setRichness", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getRight", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setRight", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getSize", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setSize", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getSpeak", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setSpeak", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getSpeakHeader", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setSpeakHeader", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getSpeakNumeral", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setSpeakNumeral", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getSpeakPunctuation", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setSpeakPunctuation", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getSpeechRate", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setSpeechRate", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getStress", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setStress", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getTableLayout", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setTableLayout", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getTextAlign", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setTextAlign", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getTextDecoration", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setTextDecoration", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getTextIndent", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setTextIndent", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getTextShadow", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setTextShadow", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getTextTransform", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setTextTransform", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getTop", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setTop", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getUnicodeBidi", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setUnicodeBidi", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getVerticalAlign", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setVerticalAlign", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getVisibility", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setVisibility", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getVoiceFamily", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setVoiceFamily", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getVolume", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setVolume", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getWhiteSpace", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setWhiteSpace", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getWidows", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setWidows", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getWidth", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getWordSpacing", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setWordSpacing", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getZIndex", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setZIndex", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}], "flds": []}, "classes/org/w3c/dom/html/HTMLOListElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLOListElement", "super": "java/lang/Object", "mthds": [{"nme": "getCompact", "acc": 1025, "dsc": "()Z"}, {"nme": "setCompact", "acc": 1025, "dsc": "(Z)V"}, {"nme": "getStart", "acc": 1025, "dsc": "()I"}, {"nme": "setStart", "acc": 1025, "dsc": "(I)V"}, {"nme": "getType", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setType", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/css/CSSValueList.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/css/CSSValueList", "super": "java/lang/Object", "mthds": [{"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()I"}, {"nme": "item", "acc": 1025, "dsc": "(I)Lorg/w3c/dom/css/CSSValue;"}], "flds": []}, "classes/org/w3c/dom/css/CSSMediaRule.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/css/CSSMediaRule", "super": "java/lang/Object", "mthds": [{"nme": "getMedia", "acc": 1025, "dsc": "()Lorg/w3c/dom/stylesheets/MediaList;"}, {"nme": "getCssRules", "acc": 1025, "dsc": "()Lorg/w3c/dom/css/CSSRuleList;"}, {"nme": "insertRule", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)I", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "deleteRule", "acc": 1025, "dsc": "(I)V", "exs": ["org/w3c/dom/DOMException"]}], "flds": []}, "classes/org/w3c/dom/html/HTMLButtonElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLButtonElement", "super": "java/lang/Object", "mthds": [{"nme": "getForm", "acc": 1025, "dsc": "()Lorg/w3c/dom/html/HTMLFormElement;"}, {"nme": "getAccessKey", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAccess<PERSON>ey", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getDisabled", "acc": 1025, "dsc": "()Z"}, {"nme": "setDisabled", "acc": 1025, "dsc": "(Z)V"}, {"nme": "getName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setName", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getTabIndex", "acc": 1025, "dsc": "()I"}, {"nme": "setTabIndex", "acc": 1025, "dsc": "(I)V"}, {"nme": "getType", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getValue", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setValue", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/module-info.class": {"ver": 68, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "classes/org/w3c/dom/html/HTMLDocument.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLDocument", "super": "java/lang/Object", "mthds": [{"nme": "getTitle", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setTitle", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDomain", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getURL", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getBody", "acc": 1025, "dsc": "()Lorg/w3c/dom/html/HTMLElement;"}, {"nme": "setBody", "acc": 1025, "dsc": "(Lorg/w3c/dom/html/HTMLElement;)V"}, {"nme": "getImages", "acc": 1025, "dsc": "()Lorg/w3c/dom/html/HTMLCollection;"}, {"nme": "getApplets", "acc": 1025, "dsc": "()Lorg/w3c/dom/html/HTMLCollection;"}, {"nme": "getLinks", "acc": 1025, "dsc": "()Lorg/w3c/dom/html/HTMLCollection;"}, {"nme": "getForms", "acc": 1025, "dsc": "()Lorg/w3c/dom/html/HTMLCollection;"}, {"nme": "getAnchors", "acc": 1025, "dsc": "()Lorg/w3c/dom/html/HTMLCollection;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "open", "acc": 1025, "dsc": "()V"}, {"nme": "close", "acc": 1025, "dsc": "()V"}, {"nme": "write", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "writeln", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getElementsByName", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/w3c/dom/NodeList;"}], "flds": []}, "classes/org/w3c/dom/html/HTMLMenuElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLMenuElement", "super": "java/lang/Object", "mthds": [{"nme": "getCompact", "acc": 1025, "dsc": "()Z"}, {"nme": "setCompact", "acc": 1025, "dsc": "(Z)V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLFormElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLFormElement", "super": "java/lang/Object", "mthds": [{"nme": "getElements", "acc": 1025, "dsc": "()Lorg/w3c/dom/html/HTMLCollection;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()I"}, {"nme": "getName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setName", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getAcceptCharset", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAcceptCharset", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getAction", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAction", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getEnctype", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setEnctype", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getMethod", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "set<PERSON>ethod", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "submit", "acc": 1025, "dsc": "()V"}, {"nme": "reset", "acc": 1025, "dsc": "()V"}], "flds": []}, "classes/org/w3c/dom/css/CSSStyleDeclaration.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/css/CSSStyleDeclaration", "super": "java/lang/Object", "mthds": [{"nme": "getCssText", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCssText", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getPropertyValue", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getPropertyCSSValue", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/w3c/dom/css/CSSValue;"}, {"nme": "removeProperty", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getPropertyPriority", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "setProperty", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()I"}, {"nme": "item", "acc": 1025, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getParentRule", "acc": 1025, "dsc": "()Lorg/w3c/dom/css/CSSRule;"}], "flds": []}, "classes/org/w3c/dom/html/HTMLHtmlElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLHtmlElement", "super": "java/lang/Object", "mthds": [{"nme": "getVersion", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setVersion", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLStyleElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLStyleElement", "super": "java/lang/Object", "mthds": [{"nme": "getDisabled", "acc": 1025, "dsc": "()Z"}, {"nme": "setDisabled", "acc": 1025, "dsc": "(Z)V"}, {"nme": "getMedia", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setMedia", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getType", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setType", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLAppletElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLAppletElement", "super": "java/lang/Object", "mthds": [{"nme": "getAlign", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAlign", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getAlt", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAlt", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getArchive", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setArchive", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getCode", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCode", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getCodeBase", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCodeBase", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getHeight", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setHeight", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getHspace", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setHspace", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setName", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getObject", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setObject", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getVspace", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setVspace", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getWidth", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/css/CSSCharsetRule.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/css/CSSCharsetRule", "super": "java/lang/Object", "mthds": [{"nme": "getEncoding", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setEncoding", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}], "flds": []}, "classes/org/w3c/dom/html/HTMLLegendElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLLegendElement", "super": "java/lang/Object", "mthds": [{"nme": "getForm", "acc": 1025, "dsc": "()Lorg/w3c/dom/html/HTMLFormElement;"}, {"nme": "getAccessKey", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAccess<PERSON>ey", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getAlign", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAlign", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLSelectElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLSelectElement", "super": "java/lang/Object", "mthds": [{"nme": "getType", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getSelectedIndex", "acc": 1025, "dsc": "()I"}, {"nme": "setSelectedIndex", "acc": 1025, "dsc": "(I)V"}, {"nme": "getValue", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setValue", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()I"}, {"nme": "getForm", "acc": 1025, "dsc": "()Lorg/w3c/dom/html/HTMLFormElement;"}, {"nme": "getOptions", "acc": 1025, "dsc": "()Lorg/w3c/dom/html/HTMLCollection;"}, {"nme": "getDisabled", "acc": 1025, "dsc": "()Z"}, {"nme": "setDisabled", "acc": 1025, "dsc": "(Z)V"}, {"nme": "getMultiple", "acc": 1025, "dsc": "()Z"}, {"nme": "setMultiple", "acc": 1025, "dsc": "(Z)V"}, {"nme": "getName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setName", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getSize", "acc": 1025, "dsc": "()I"}, {"nme": "setSize", "acc": 1025, "dsc": "(I)V"}, {"nme": "getTabIndex", "acc": 1025, "dsc": "()I"}, {"nme": "setTabIndex", "acc": 1025, "dsc": "(I)V"}, {"nme": "add", "acc": 1025, "dsc": "(Lorg/w3c/dom/html/HTMLElement;Lorg/w3c/dom/html/HTMLElement;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "remove", "acc": 1025, "dsc": "(I)V"}, {"nme": "blur", "acc": 1025, "dsc": "()V"}, {"nme": "focus", "acc": 1025, "dsc": "()V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLTitleElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLTitleElement", "super": "java/lang/Object", "mthds": [{"nme": "getText", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setText", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLPreElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLPreElement", "super": "java/lang/Object", "mthds": [{"nme": "getWidth", "acc": 1025, "dsc": "()I"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(I)V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLUListElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLUListElement", "super": "java/lang/Object", "mthds": [{"nme": "getCompact", "acc": 1025, "dsc": "()Z"}, {"nme": "setCompact", "acc": 1025, "dsc": "(Z)V"}, {"nme": "getType", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setType", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/xpath/XPathEvaluator.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/xpath/XPathEvaluator", "super": "java/lang/Object", "mthds": [{"nme": "createExpression", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/w3c/dom/xpath/XPathNSResolver;)Lorg/w3c/dom/xpath/XPathExpression;", "exs": ["org/w3c/dom/xpath/XPathException", "org/w3c/dom/DOMException"]}, {"nme": "createNSResolver", "acc": 1025, "dsc": "(Lorg/w3c/dom/Node;)Lorg/w3c/dom/xpath/XPathNSResolver;"}, {"nme": "evaluate", "acc": 1025, "dsc": "(Lja<PERSON>/lang/String;Lorg/w3c/dom/Node;Lorg/w3c/dom/xpath/XPathNSResolver;SLjava/lang/Object;)Ljava/lang/Object;", "exs": ["org/w3c/dom/xpath/XPathException", "org/w3c/dom/DOMException"]}], "flds": []}, "classes/org/w3c/dom/html/HTMLCollection.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLCollection", "super": "java/lang/Object", "mthds": [{"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()I"}, {"nme": "item", "acc": 1025, "dsc": "(I)Lorg/w3c/dom/Node;"}, {"nme": "namedItem", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/w3c/dom/Node;"}], "flds": []}, "classes/org/w3c/dom/stylesheets/MediaList.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/stylesheets/MediaList", "super": "java/lang/Object", "mthds": [{"nme": "getMediaText", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setMediaText", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()I"}, {"nme": "item", "acc": 1025, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "deleteMedium", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "appendMedium", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}], "flds": []}, "classes/org/w3c/dom/html/HTMLFrameElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLFrameElement", "super": "java/lang/Object", "mthds": [{"nme": "getFrameBorder", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setFrameBorder", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getLongDesc", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLongDesc", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getMarginHeight", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setMarginHeight", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setName", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getNoResize", "acc": 1025, "dsc": "()Z"}, {"nme": "setNoResize", "acc": 1025, "dsc": "(Z)V"}, {"nme": "getScrolling", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setScrolling", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getSrc", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setSrc", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getContentDocument", "acc": 1025, "dsc": "()Lorg/w3c/dom/Document;"}], "flds": []}, "classes/org/w3c/dom/html/HTMLElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLElement", "super": "java/lang/Object", "mthds": [{"nme": "getId", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setId", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getTitle", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setTitle", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getLang", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLang", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getDir", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setDir", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getClassName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setClassName", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLAreaElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLAreaElement", "super": "java/lang/Object", "mthds": [{"nme": "getAccessKey", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAccess<PERSON>ey", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getAlt", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAlt", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getCoords", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCoords", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getHref", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "set<PERSON><PERSON>f", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getNoHref", "acc": 1025, "dsc": "()Z"}, {"nme": "setNoHref", "acc": 1025, "dsc": "(Z)V"}, {"nme": "getShape", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setShape", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getTabIndex", "acc": 1025, "dsc": "()I"}, {"nme": "setTabIndex", "acc": 1025, "dsc": "(I)V"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/css/CSSRule.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/css/CSSRule", "super": "java/lang/Object", "mthds": [{"nme": "getType", "acc": 1025, "dsc": "()S"}, {"nme": "getCssText", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCssText", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getParentStyleSheet", "acc": 1025, "dsc": "()Lorg/w3c/dom/css/CSSStyleSheet;"}, {"nme": "getParentRule", "acc": 1025, "dsc": "()Lorg/w3c/dom/css/CSSRule;"}], "flds": [{"acc": 25, "nme": "UNKNOWN_RULE", "dsc": "S", "val": 0}, {"acc": 25, "nme": "STYLE_RULE", "dsc": "S", "val": 1}, {"acc": 25, "nme": "CHARSET_RULE", "dsc": "S", "val": 2}, {"acc": 25, "nme": "IMPORT_RULE", "dsc": "S", "val": 3}, {"acc": 25, "nme": "MEDIA_RULE", "dsc": "S", "val": 4}, {"acc": 25, "nme": "FONT_FACE_RULE", "dsc": "S", "val": 5}, {"acc": 25, "nme": "PAGE_RULE", "dsc": "S", "val": 6}]}, "classes/org/w3c/dom/css/CSSUnknownRule.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/css/CSSUnknownRule", "super": "java/lang/Object", "mthds": [], "flds": []}, "classes/org/w3c/dom/css/CSSPrimitiveValue.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/css/CSSPrimitiveValue", "super": "java/lang/Object", "mthds": [{"nme": "getPrimitiveType", "acc": 1025, "dsc": "()S"}, {"nme": "setFloatValue", "acc": 1025, "dsc": "(SF)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getFloatValue", "acc": 1025, "dsc": "(S)F", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "setStringValue", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getStringValue", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getCounterValue", "acc": 1025, "dsc": "()Lorg/w3c/dom/css/Counter;", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getRectValue", "acc": 1025, "dsc": "()Lorg/w3c/dom/css/Rect;", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getRGBColorValue", "acc": 1025, "dsc": "()Lorg/w3c/dom/css/RGBColor;", "exs": ["org/w3c/dom/DOMException"]}], "flds": [{"acc": 25, "nme": "CSS_UNKNOWN", "dsc": "S", "val": 0}, {"acc": 25, "nme": "CSS_NUMBER", "dsc": "S", "val": 1}, {"acc": 25, "nme": "CSS_PERCENTAGE", "dsc": "S", "val": 2}, {"acc": 25, "nme": "CSS_EMS", "dsc": "S", "val": 3}, {"acc": 25, "nme": "CSS_EXS", "dsc": "S", "val": 4}, {"acc": 25, "nme": "CSS_PX", "dsc": "S", "val": 5}, {"acc": 25, "nme": "CSS_CM", "dsc": "S", "val": 6}, {"acc": 25, "nme": "CSS_MM", "dsc": "S", "val": 7}, {"acc": 25, "nme": "CSS_IN", "dsc": "S", "val": 8}, {"acc": 25, "nme": "CSS_PT", "dsc": "S", "val": 9}, {"acc": 25, "nme": "CSS_PC", "dsc": "S", "val": 10}, {"acc": 25, "nme": "CSS_DEG", "dsc": "S", "val": 11}, {"acc": 25, "nme": "CSS_RAD", "dsc": "S", "val": 12}, {"acc": 25, "nme": "CSS_GRAD", "dsc": "S", "val": 13}, {"acc": 25, "nme": "CSS_MS", "dsc": "S", "val": 14}, {"acc": 25, "nme": "CSS_S", "dsc": "S", "val": 15}, {"acc": 25, "nme": "CSS_HZ", "dsc": "S", "val": 16}, {"acc": 25, "nme": "CSS_KHZ", "dsc": "S", "val": 17}, {"acc": 25, "nme": "CSS_DIMENSION", "dsc": "S", "val": 18}, {"acc": 25, "nme": "CSS_STRING", "dsc": "S", "val": 19}, {"acc": 25, "nme": "CSS_URI", "dsc": "S", "val": 20}, {"acc": 25, "nme": "CSS_IDENT", "dsc": "S", "val": 21}, {"acc": 25, "nme": "CSS_ATTR", "dsc": "S", "val": 22}, {"acc": 25, "nme": "CSS_COUNTER", "dsc": "S", "val": 23}, {"acc": 25, "nme": "CSS_RECT", "dsc": "S", "val": 24}, {"acc": 25, "nme": "CSS_RGBCOLOR", "dsc": "S", "val": 25}]}, "classes/org/w3c/dom/html/HTMLFrameSetElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLFrameSetElement", "super": "java/lang/Object", "mthds": [{"nme": "getCols", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCols", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getRows", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setRows", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/css/CSSFontFaceRule.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/css/CSSFontFaceRule", "super": "java/lang/Object", "mthds": [{"nme": "getStyle", "acc": 1025, "dsc": "()Lorg/w3c/dom/css/CSSStyleDeclaration;"}], "flds": []}, "classes/org/w3c/dom/css/ViewCSS.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/css/ViewCSS", "super": "java/lang/Object", "mthds": [{"nme": "getComputedStyle", "acc": 1025, "dsc": "(Lorg/w3c/dom/Element;<PERSON><PERSON><PERSON>/lang/String;)Lorg/w3c/dom/css/CSSStyleDeclaration;"}], "flds": []}, "classes/org/w3c/dom/html/HTMLMetaElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLMetaElement", "super": "java/lang/Object", "mthds": [{"nme": "get<PERSON>ontent", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getHttpEquiv", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setHttpEquiv", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setName", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getScheme", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setScheme", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLHRElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLHRElement", "super": "java/lang/Object", "mthds": [{"nme": "getAlign", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAlign", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getNoShade", "acc": 1025, "dsc": "()Z"}, {"nme": "setNoShade", "acc": 1025, "dsc": "(Z)V"}, {"nme": "getSize", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setSize", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getWidth", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLDirectoryElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLDirectoryElement", "super": "java/lang/Object", "mthds": [{"nme": "getCompact", "acc": 1025, "dsc": "()Z"}, {"nme": "setCompact", "acc": 1025, "dsc": "(Z)V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLBodyElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLBodyElement", "super": "java/lang/Object", "mthds": [{"nme": "getALink", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setALink", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getBackground", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBackground", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getBgColor", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBgColor", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getLink", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLink", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getText", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setText", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getVLink", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setVLink", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLOptGroupElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLOptGroupElement", "super": "java/lang/Object", "mthds": [{"nme": "getDisabled", "acc": 1025, "dsc": "()Z"}, {"nme": "setDisabled", "acc": 1025, "dsc": "(Z)V"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLParagraphElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLParagraphElement", "super": "java/lang/Object", "mthds": [{"nme": "getAlign", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAlign", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/stylesheets/StyleSheet.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/stylesheets/StyleSheet", "super": "java/lang/Object", "mthds": [{"nme": "getType", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDisabled", "acc": 1025, "dsc": "()Z"}, {"nme": "setDisabled", "acc": 1025, "dsc": "(Z)V"}, {"nme": "getOwnerNode", "acc": 1025, "dsc": "()Lorg/w3c/dom/Node;"}, {"nme": "getParentStyleSheet", "acc": 1025, "dsc": "()Lorg/w3c/dom/stylesheets/StyleSheet;"}, {"nme": "getHref", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getTitle", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getMedia", "acc": 1025, "dsc": "()Lorg/w3c/dom/stylesheets/MediaList;"}], "flds": []}, "classes/org/w3c/dom/xpath/XPathException.class": {"ver": 68, "acc": 33, "nme": "org/w3c/dom/xpath/XPathException", "super": "java/lang/RuntimeException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 3471034171575979943}, {"acc": 1, "nme": "code", "dsc": "S"}, {"acc": 25, "nme": "INVALID_EXPRESSION_ERR", "dsc": "S", "val": 1}, {"acc": 25, "nme": "TYPE_ERR", "dsc": "S", "val": 2}]}, "classes/org/w3c/dom/html/HTMLModElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLModElement", "super": "java/lang/Object", "mthds": [{"nme": "getCite", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCite", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getDateTime", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setDateTime", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLTableElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLTableElement", "super": "java/lang/Object", "mthds": [{"nme": "getCaption", "acc": 1025, "dsc": "()Lorg/w3c/dom/html/HTMLTableCaptionElement;"}, {"nme": "setCaption", "acc": 1025, "dsc": "(Lorg/w3c/dom/html/HTMLTableCaptionElement;)V"}, {"nme": "getTHead", "acc": 1025, "dsc": "()Lorg/w3c/dom/html/HTMLTableSectionElement;"}, {"nme": "setTHead", "acc": 1025, "dsc": "(Lorg/w3c/dom/html/HTMLTableSectionElement;)V"}, {"nme": "getTFoot", "acc": 1025, "dsc": "()Lorg/w3c/dom/html/HTMLTableSectionElement;"}, {"nme": "setTFoot", "acc": 1025, "dsc": "(Lorg/w3c/dom/html/HTMLTableSectionElement;)V"}, {"nme": "getRows", "acc": 1025, "dsc": "()Lorg/w3c/dom/html/HTMLCollection;"}, {"nme": "getTBodies", "acc": 1025, "dsc": "()Lorg/w3c/dom/html/HTMLCollection;"}, {"nme": "getAlign", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAlign", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getBgColor", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBgColor", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getBorder", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBorder", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getCellPadding", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCellPadding", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getCellSpacing", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCellSpacing", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getFrame", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "set<PERSON>rame", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getRules", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setRules", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getSummary", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "set<PERSON>ummary", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getWidth", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "createTHead", "acc": 1025, "dsc": "()Lorg/w3c/dom/html/HTMLElement;"}, {"nme": "deleteTHead", "acc": 1025, "dsc": "()V"}, {"nme": "createTFoot", "acc": 1025, "dsc": "()Lorg/w3c/dom/html/HTMLElement;"}, {"nme": "deleteTFoot", "acc": 1025, "dsc": "()V"}, {"nme": "createCaption", "acc": 1025, "dsc": "()Lorg/w3c/dom/html/HTMLElement;"}, {"nme": "deleteCaption", "acc": 1025, "dsc": "()V"}, {"nme": "insertRow", "acc": 1025, "dsc": "(I)Lorg/w3c/dom/html/HTMLElement;", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "deleteRow", "acc": 1025, "dsc": "(I)V", "exs": ["org/w3c/dom/DOMException"]}], "flds": []}, "classes/org/w3c/dom/css/DocumentCSS.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/css/DocumentCSS", "super": "java/lang/Object", "mthds": [{"nme": "getOverrideStyle", "acc": 1025, "dsc": "(Lorg/w3c/dom/Element;<PERSON><PERSON><PERSON>/lang/String;)Lorg/w3c/dom/css/CSSStyleDeclaration;"}], "flds": []}, "classes/org/w3c/dom/html/HTMLDivElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLDivElement", "super": "java/lang/Object", "mthds": [{"nme": "getAlign", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAlign", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/stylesheets/DocumentStyle.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/stylesheets/DocumentStyle", "super": "java/lang/Object", "mthds": [{"nme": "getStyleSheets", "acc": 1025, "dsc": "()Lorg/w3c/dom/stylesheets/StyleSheetList;"}], "flds": []}, "classes/org/w3c/dom/html/HTMLHeadElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLHeadElement", "super": "java/lang/Object", "mthds": [{"nme": "getProfile", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setProfile", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLInputElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLInputElement", "super": "java/lang/Object", "mthds": [{"nme": "getDefaultValue", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setDefaultValue", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getDefaultChecked", "acc": 1025, "dsc": "()Z"}, {"nme": "setDefaultChecked", "acc": 1025, "dsc": "(Z)V"}, {"nme": "getForm", "acc": 1025, "dsc": "()Lorg/w3c/dom/html/HTMLFormElement;"}, {"nme": "getAccept", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAccept", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getAccessKey", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAccess<PERSON>ey", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getAlign", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAlign", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getAlt", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAlt", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getChecked", "acc": 1025, "dsc": "()Z"}, {"nme": "setChecked", "acc": 1025, "dsc": "(Z)V"}, {"nme": "getDisabled", "acc": 1025, "dsc": "()Z"}, {"nme": "setDisabled", "acc": 1025, "dsc": "(Z)V"}, {"nme": "getMaxLength", "acc": 1025, "dsc": "()I"}, {"nme": "setMaxLength", "acc": 1025, "dsc": "(I)V"}, {"nme": "getName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setName", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getReadOnly", "acc": 1025, "dsc": "()Z"}, {"nme": "setReadOnly", "acc": 1025, "dsc": "(Z)V"}, {"nme": "getSize", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setSize", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getSrc", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setSrc", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getTabIndex", "acc": 1025, "dsc": "()I"}, {"nme": "setTabIndex", "acc": 1025, "dsc": "(I)V"}, {"nme": "getType", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getUseMap", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setUseMap", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getValue", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setValue", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "blur", "acc": 1025, "dsc": "()V"}, {"nme": "focus", "acc": 1025, "dsc": "()V"}, {"nme": "select", "acc": 1025, "dsc": "()V"}, {"nme": "click", "acc": 1025, "dsc": "()V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLDOMImplementation.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLDOMImplementation", "super": "java/lang/Object", "mthds": [{"nme": "createHTMLDocument", "acc": 1025, "dsc": "(Lja<PERSON>/lang/String;)Lorg/w3c/dom/html/HTMLDocument;"}], "flds": []}, "classes/org/w3c/dom/html/HTMLLinkElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLLinkElement", "super": "java/lang/Object", "mthds": [{"nme": "getDisabled", "acc": 1025, "dsc": "()Z"}, {"nme": "setDisabled", "acc": 1025, "dsc": "(Z)V"}, {"nme": "getCharset", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCharset", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getHref", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "set<PERSON><PERSON>f", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getHreflang", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setHreflang", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getMedia", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setMedia", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getRel", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setRel", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getType", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setType", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/css/CSSImportRule.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/css/CSSImportRule", "super": "java/lang/Object", "mthds": [{"nme": "getHref", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getMedia", "acc": 1025, "dsc": "()Lorg/w3c/dom/stylesheets/MediaList;"}, {"nme": "getStyleSheet", "acc": 1025, "dsc": "()Lorg/w3c/dom/css/CSSStyleSheet;"}], "flds": []}, "classes/org/w3c/dom/css/CSSRuleList.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/css/CSSRuleList", "super": "java/lang/Object", "mthds": [{"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()I"}, {"nme": "item", "acc": 1025, "dsc": "(I)Lorg/w3c/dom/css/CSSRule;"}], "flds": []}, "classes/org/w3c/dom/css/DOMImplementationCSS.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/css/DOMImplementationCSS", "super": "java/lang/Object", "mthds": [{"nme": "createCSSStyleSheet", "acc": 1025, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;)Lorg/w3c/dom/css/CSSStyleSheet;", "exs": ["org/w3c/dom/DOMException"]}], "flds": []}, "classes/org/w3c/dom/css/CSSValue.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/css/CSSValue", "super": "java/lang/Object", "mthds": [{"nme": "getCssText", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCssText", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getCssValueType", "acc": 1025, "dsc": "()S"}], "flds": [{"acc": 25, "nme": "CSS_INHERIT", "dsc": "S", "val": 0}, {"acc": 25, "nme": "CSS_PRIMITIVE_VALUE", "dsc": "S", "val": 1}, {"acc": 25, "nme": "CSS_VALUE_LIST", "dsc": "S", "val": 2}, {"acc": 25, "nme": "CSS_CUSTOM", "dsc": "S", "val": 3}]}, "classes/org/w3c/dom/stylesheets/StyleSheetList.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/stylesheets/StyleSheetList", "super": "java/lang/Object", "mthds": [{"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()I"}, {"nme": "item", "acc": 1025, "dsc": "(I)Lorg/w3c/dom/stylesheets/StyleSheet;"}], "flds": []}, "classes/org/w3c/dom/xpath/XPathNSResolver.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/xpath/XPathNSResolver", "super": "java/lang/Object", "mthds": [{"nme": "lookupNamespaceURI", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": []}, "classes/org/w3c/dom/html/HTMLFontElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLFontElement", "super": "java/lang/Object", "mthds": [{"nme": "getColor", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setColor", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getFace", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setFace", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getSize", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setSize", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLTableCellElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLTableCellElement", "super": "java/lang/Object", "mthds": [{"nme": "getCellIndex", "acc": 1025, "dsc": "()I"}, {"nme": "getAbbr", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAbbr", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getAlign", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAlign", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getAxis", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAxis", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getBgColor", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBgColor", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getCh", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCh", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "get<PERSON><PERSON><PERSON>ff", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getColSpan", "acc": 1025, "dsc": "()I"}, {"nme": "setColSpan", "acc": 1025, "dsc": "(I)V"}, {"nme": "getHeaders", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setHeaders", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getHeight", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setHeight", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getNoWrap", "acc": 1025, "dsc": "()Z"}, {"nme": "setNoWrap", "acc": 1025, "dsc": "(Z)V"}, {"nme": "getRowSpan", "acc": 1025, "dsc": "()I"}, {"nme": "setRowSpan", "acc": 1025, "dsc": "(I)V"}, {"nme": "getScope", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setScope", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getVAlign", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setVAlign", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getWidth", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLBRElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLBRElement", "super": "java/lang/Object", "mthds": [{"nme": "getClear", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setClear", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLLabelElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLLabelElement", "super": "java/lang/Object", "mthds": [{"nme": "getForm", "acc": 1025, "dsc": "()Lorg/w3c/dom/html/HTMLFormElement;"}, {"nme": "getAccessKey", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAccess<PERSON>ey", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getHtmlFor", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setHtmlFor", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/xpath/XPathResult.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/xpath/XPathResult", "super": "java/lang/Object", "mthds": [{"nme": "getResultType", "acc": 1025, "dsc": "()S"}, {"nme": "getNumberValue", "acc": 1025, "dsc": "()D", "exs": ["org/w3c/dom/xpath/XPathException"]}, {"nme": "getStringValue", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["org/w3c/dom/xpath/XPathException"]}, {"nme": "getBooleanValue", "acc": 1025, "dsc": "()Z", "exs": ["org/w3c/dom/xpath/XPathException"]}, {"nme": "getSingleNodeValue", "acc": 1025, "dsc": "()Lorg/w3c/dom/Node;", "exs": ["org/w3c/dom/xpath/XPathException"]}, {"nme": "getInvalidIteratorState", "acc": 1025, "dsc": "()Z"}, {"nme": "getSnapshotLength", "acc": 1025, "dsc": "()I", "exs": ["org/w3c/dom/xpath/XPathException"]}, {"nme": "iterateNext", "acc": 1025, "dsc": "()Lorg/w3c/dom/Node;", "exs": ["org/w3c/dom/xpath/XPathException", "org/w3c/dom/DOMException"]}, {"nme": "snapshotItem", "acc": 1025, "dsc": "(I)Lorg/w3c/dom/Node;", "exs": ["org/w3c/dom/xpath/XPathException"]}], "flds": [{"acc": 25, "nme": "ANY_TYPE", "dsc": "S", "val": 0}, {"acc": 25, "nme": "NUMBER_TYPE", "dsc": "S", "val": 1}, {"acc": 25, "nme": "STRING_TYPE", "dsc": "S", "val": 2}, {"acc": 25, "nme": "BOOLEAN_TYPE", "dsc": "S", "val": 3}, {"acc": 25, "nme": "UNORDERED_NODE_ITERATOR_TYPE", "dsc": "S", "val": 4}, {"acc": 25, "nme": "ORDERED_NODE_ITERATOR_TYPE", "dsc": "S", "val": 5}, {"acc": 25, "nme": "UNORDERED_NODE_SNAPSHOT_TYPE", "dsc": "S", "val": 6}, {"acc": 25, "nme": "ORDERED_NODE_SNAPSHOT_TYPE", "dsc": "S", "val": 7}, {"acc": 25, "nme": "ANY_UNORDERED_NODE_TYPE", "dsc": "S", "val": 8}, {"acc": 25, "nme": "FIRST_ORDERED_NODE_TYPE", "dsc": "S", "val": 9}]}, "classes/org/w3c/dom/html/HTMLMapElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLMapElement", "super": "java/lang/Object", "mthds": [{"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()Lorg/w3c/dom/html/HTMLCollection;"}, {"nme": "getName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setName", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLBaseElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLBaseElement", "super": "java/lang/Object", "mthds": [{"nme": "getHref", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "set<PERSON><PERSON>f", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/css/Rect.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/css/Rect", "super": "java/lang/Object", "mthds": [{"nme": "getTop", "acc": 1025, "dsc": "()Lorg/w3c/dom/css/CSSPrimitiveValue;"}, {"nme": "getRight", "acc": 1025, "dsc": "()Lorg/w3c/dom/css/CSSPrimitiveValue;"}, {"nme": "getBottom", "acc": 1025, "dsc": "()Lorg/w3c/dom/css/CSSPrimitiveValue;"}, {"nme": "getLeft", "acc": 1025, "dsc": "()Lorg/w3c/dom/css/CSSPrimitiveValue;"}], "flds": []}, "classes/org/w3c/dom/css/ElementCSSInlineStyle.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/css/ElementCSSInlineStyle", "super": "java/lang/Object", "mthds": [{"nme": "getStyle", "acc": 1025, "dsc": "()Lorg/w3c/dom/css/CSSStyleDeclaration;"}], "flds": []}, "classes/org/w3c/dom/css/Counter.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/css/Counter", "super": "java/lang/Object", "mthds": [{"nme": "getIdentifier", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getListStyle", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getSeparator", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "classes/org/w3c/dom/html/HTMLIFrameElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLIFrameElement", "super": "java/lang/Object", "mthds": [{"nme": "getAlign", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAlign", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getFrameBorder", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setFrameBorder", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getHeight", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setHeight", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getLongDesc", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLongDesc", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getMarginHeight", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setMarginHeight", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setName", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getScrolling", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setScrolling", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getSrc", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setSrc", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getWidth", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getContentDocument", "acc": 1025, "dsc": "()Lorg/w3c/dom/Document;"}], "flds": []}, "classes/org/w3c/dom/stylesheets/LinkStyle.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/stylesheets/LinkStyle", "super": "java/lang/Object", "mthds": [{"nme": "getSheet", "acc": 1025, "dsc": "()Lorg/w3c/dom/stylesheets/StyleSheet;"}], "flds": []}, "classes/org/w3c/dom/css/CSSStyleSheet.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/css/CSSStyleSheet", "super": "java/lang/Object", "mthds": [{"nme": "getOwnerRule", "acc": 1025, "dsc": "()Lorg/w3c/dom/css/CSSRule;"}, {"nme": "getCssRules", "acc": 1025, "dsc": "()Lorg/w3c/dom/css/CSSRuleList;"}, {"nme": "insertRule", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)I", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "deleteRule", "acc": 1025, "dsc": "(I)V", "exs": ["org/w3c/dom/DOMException"]}], "flds": []}, "classes/org/w3c/dom/xpath/XPathNamespace.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/xpath/XPathNamespace", "super": "java/lang/Object", "mthds": [{"nme": "getOwnerElement", "acc": 1025, "dsc": "()Lorg/w3c/dom/Element;"}], "flds": [{"acc": 25, "nme": "XPATH_NAMESPACE_NODE", "dsc": "S", "val": 13}]}, "classes/org/w3c/dom/html/HTMLHeadingElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLHeadingElement", "super": "java/lang/Object", "mthds": [{"nme": "getAlign", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAlign", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/css/RGBColor.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/css/RGBColor", "super": "java/lang/Object", "mthds": [{"nme": "getRed", "acc": 1025, "dsc": "()Lorg/w3c/dom/css/CSSPrimitiveValue;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()Lorg/w3c/dom/css/CSSPrimitiveValue;"}, {"nme": "getBlue", "acc": 1025, "dsc": "()Lorg/w3c/dom/css/CSSPrimitiveValue;"}], "flds": []}, "classes/org/w3c/dom/html/HTMLFieldSetElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLFieldSetElement", "super": "java/lang/Object", "mthds": [{"nme": "getForm", "acc": 1025, "dsc": "()Lorg/w3c/dom/html/HTMLFormElement;"}], "flds": []}, "classes/org/w3c/dom/css/CSSStyleRule.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/css/CSSStyleRule", "super": "java/lang/Object", "mthds": [{"nme": "getSelectorText", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setSelectorText", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getStyle", "acc": 1025, "dsc": "()Lorg/w3c/dom/css/CSSStyleDeclaration;"}], "flds": []}, "classes/org/w3c/dom/html/HTMLScriptElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLScriptElement", "super": "java/lang/Object", "mthds": [{"nme": "getText", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setText", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getHtmlFor", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setHtmlFor", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getEvent", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setEvent", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getCharset", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCharset", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()Z"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(Z)V"}, {"nme": "getSrc", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setSrc", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getType", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setType", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/css/CSSPageRule.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/css/CSSPageRule", "super": "java/lang/Object", "mthds": [{"nme": "getSelectorText", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setSelectorText", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "getStyle", "acc": 1025, "dsc": "()Lorg/w3c/dom/css/CSSStyleDeclaration;"}], "flds": []}, "classes/org/w3c/dom/xpath/XPathExpression.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/xpath/XPathExpression", "super": "java/lang/Object", "mthds": [{"nme": "evaluate", "acc": 1025, "dsc": "(Lorg/w3c/dom/Node;SLjava/lang/Object;)<PERSON>java/lang/Object;", "exs": ["org/w3c/dom/xpath/XPathException", "org/w3c/dom/DOMException"]}], "flds": []}, "classes/org/w3c/dom/html/HTMLTableColElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLTableColElement", "super": "java/lang/Object", "mthds": [{"nme": "getAlign", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAlign", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getCh", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCh", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "get<PERSON><PERSON><PERSON>ff", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getSpan", "acc": 1025, "dsc": "()I"}, {"nme": "setSpan", "acc": 1025, "dsc": "(I)V"}, {"nme": "getVAlign", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setVAlign", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getWidth", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLAnchorElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLAnchorElement", "super": "java/lang/Object", "mthds": [{"nme": "getAccessKey", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAccess<PERSON>ey", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getCharset", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCharset", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getCoords", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCoords", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getHref", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "set<PERSON><PERSON>f", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getHreflang", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setHreflang", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setName", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getRel", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setRel", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getShape", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setShape", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getTabIndex", "acc": 1025, "dsc": "()I"}, {"nme": "setTabIndex", "acc": 1025, "dsc": "(I)V"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getType", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setType", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "blur", "acc": 1025, "dsc": "()V"}, {"nme": "focus", "acc": 1025, "dsc": "()V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLBaseFontElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLBaseFontElement", "super": "java/lang/Object", "mthds": [{"nme": "getColor", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setColor", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getFace", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setFace", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getSize", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setSize", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLTableRowElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLTableRowElement", "super": "java/lang/Object", "mthds": [{"nme": "getRowIndex", "acc": 1025, "dsc": "()I"}, {"nme": "getSectionRowIndex", "acc": 1025, "dsc": "()I"}, {"nme": "get<PERSON>ells", "acc": 1025, "dsc": "()Lorg/w3c/dom/html/HTMLCollection;"}, {"nme": "getAlign", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAlign", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getBgColor", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBgColor", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getCh", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCh", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "get<PERSON><PERSON><PERSON>ff", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getVAlign", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setVAlign", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "insertCell", "acc": 1025, "dsc": "(I)Lorg/w3c/dom/html/HTMLElement;", "exs": ["org/w3c/dom/DOMException"]}, {"nme": "deleteCell", "acc": 1025, "dsc": "(I)V", "exs": ["org/w3c/dom/DOMException"]}], "flds": []}, "classes/org/w3c/dom/html/HTMLTextAreaElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLTextAreaElement", "super": "java/lang/Object", "mthds": [{"nme": "getDefaultValue", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setDefaultValue", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getForm", "acc": 1025, "dsc": "()Lorg/w3c/dom/html/HTMLFormElement;"}, {"nme": "getAccessKey", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAccess<PERSON>ey", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getCols", "acc": 1025, "dsc": "()I"}, {"nme": "setCols", "acc": 1025, "dsc": "(I)V"}, {"nme": "getDisabled", "acc": 1025, "dsc": "()Z"}, {"nme": "setDisabled", "acc": 1025, "dsc": "(Z)V"}, {"nme": "getName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setName", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getReadOnly", "acc": 1025, "dsc": "()Z"}, {"nme": "setReadOnly", "acc": 1025, "dsc": "(Z)V"}, {"nme": "getRows", "acc": 1025, "dsc": "()I"}, {"nme": "setRows", "acc": 1025, "dsc": "(I)V"}, {"nme": "getTabIndex", "acc": 1025, "dsc": "()I"}, {"nme": "setTabIndex", "acc": 1025, "dsc": "(I)V"}, {"nme": "getType", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getValue", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setValue", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "blur", "acc": 1025, "dsc": "()V"}, {"nme": "focus", "acc": 1025, "dsc": "()V"}, {"nme": "select", "acc": 1025, "dsc": "()V"}], "flds": []}, "classes/org/w3c/dom/html/HTMLIsIndexElement.class": {"ver": 68, "acc": 1537, "nme": "org/w3c/dom/html/HTMLIsIndexElement", "super": "java/lang/Object", "mthds": [{"nme": "getForm", "acc": 1025, "dsc": "()Lorg/w3c/dom/html/HTMLFormElement;"}, {"nme": "getPrompt", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setPrompt", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}}}}