{"md5": "9ae0100009dc422901c9955f6899d26a", "sha2": "05c4ad4b33bbfe93c061e8c8a37d0542f7539415", "sha256": "10cf2e586dd7edd55d6edff54e7a01ded51b345683b4889601222db37fa97d43", "contents": {"classes": {"net/milkbowl/vault/economy/AbstractEconomy.class": {"ver": 52, "acc": 1057, "nme": "net/milkbowl/vault/economy/AbstractEconomy", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "hasAccount", "acc": 1, "dsc": "(Lorg/bukkit/OfflinePlayer;)Z"}, {"nme": "hasAccount", "acc": 1, "dsc": "(Lorg/bukkit/OfflinePlayer;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "getBalance", "acc": 1, "dsc": "(Lorg/bukkit/OfflinePlayer;)D"}, {"nme": "getBalance", "acc": 1, "dsc": "(Lorg/bukkit/OfflinePlayer;<PERSON><PERSON><PERSON>/lang/String;)D"}, {"nme": "has", "acc": 1, "dsc": "(Lorg/bukkit/OfflinePlayer;D)Z"}, {"nme": "has", "acc": 1, "dsc": "(Lorg/bukkit/OfflinePlayer;<PERSON><PERSON><PERSON>/lang/String;D)Z"}, {"nme": "withdrawPlayer", "acc": 1, "dsc": "(Lorg/bukkit/OfflinePlayer;D)Lnet/milkbowl/vault/economy/EconomyResponse;"}, {"nme": "withdrawPlayer", "acc": 1, "dsc": "(Lorg/bukkit/OfflinePlayer;Ljava/lang/String;D)Lnet/milkbowl/vault/economy/EconomyResponse;"}, {"nme": "depositPlayer", "acc": 1, "dsc": "(Lorg/bukkit/OfflinePlayer;D)Lnet/milkbowl/vault/economy/EconomyResponse;"}, {"nme": "depositPlayer", "acc": 1, "dsc": "(Lorg/bukkit/OfflinePlayer;Ljava/lang/String;D)Lnet/milkbowl/vault/economy/EconomyResponse;"}, {"nme": "createBank", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;)Lnet/milkbowl/vault/economy/EconomyResponse;"}, {"nme": "isBankOwner", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;)Lnet/milkbowl/vault/economy/EconomyResponse;"}, {"nme": "isBankMember", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;)Lnet/milkbowl/vault/economy/EconomyResponse;"}, {"nme": "createPlayerAccount", "acc": 1, "dsc": "(Lorg/bukkit/OfflinePlayer;)Z"}, {"nme": "createPlayerAccount", "acc": 1, "dsc": "(Lorg/bukkit/OfflinePlayer;<PERSON><PERSON><PERSON>/lang/String;)Z"}], "flds": []}, "net/milkbowl/vault/economy/EconomyResponse$ResponseType.class": {"ver": 52, "acc": 16433, "nme": "net/milkbowl/vault/economy/EconomyResponse$ResponseType", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lnet/milkbowl/vault/economy/EconomyResponse$ResponseType;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Lnet/milkbowl/vault/economy/EconomyResponse$ResponseType;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;II)V", "sig": "(I)V"}, {"nme": "getId", "acc": 0, "dsc": "()I"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "SUCCESS", "dsc": "Lnet/milkbowl/vault/economy/EconomyResponse$ResponseType;"}, {"acc": 16409, "nme": "FAILURE", "dsc": "Lnet/milkbowl/vault/economy/EconomyResponse$ResponseType;"}, {"acc": 16409, "nme": "NOT_IMPLEMENTED", "dsc": "Lnet/milkbowl/vault/economy/EconomyResponse$ResponseType;"}, {"acc": 2, "nme": "id", "dsc": "I"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lnet/milkbowl/vault/economy/EconomyResponse$ResponseType;"}]}, "net/milkbowl/vault/economy/EconomyResponse$1.class": {"ver": 52, "acc": 4128, "nme": "net/milkbowl/vault/economy/EconomyResponse$1", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$net$milkbowl$vault$economy$EconomyResponse$ResponseType", "dsc": "[I"}]}, "net/milkbowl/vault/permission/Permission.class": {"ver": 52, "acc": 1057, "nme": "net/milkbowl/vault/permission/Permission", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isEnabled", "acc": 1025, "dsc": "()Z"}, {"nme": "hasSuperPermsCompat", "acc": 1025, "dsc": "()Z"}, {"nme": "has", "acc": 131073, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "has", "acc": 131073, "dsc": "(<PERSON><PERSON>/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "has", "acc": 1, "dsc": "(Lorg/bukkit/command/CommandSender;Lja<PERSON>/lang/String;)Z"}, {"nme": "has", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 131073, "dsc": "(<PERSON><PERSON>/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;Ljava/lang/String;)Z"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "playerAdd", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "playerAdd", "acc": 131073, "dsc": "(<PERSON><PERSON>/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "playerAdd", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;Ljava/lang/String;)Z"}, {"nme": "playerAdd", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Lorg/bukkit/OfflinePlayer;<PERSON><PERSON><PERSON>/lang/String;)Z", "exs": ["java/lang/UnsupportedOperationException"]}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;Ljava/lang/String;)Z"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/entity/Player;Ljava/lang/String;)Z"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;Ljava/lang/String;)Z"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/entity/Player;Ljava/lang/String;)Z"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;Ljava/lang/String;)Z"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 131073, "dsc": "(<PERSON><PERSON>/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Lorg/bukkit/OfflinePlayer;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "groupHas", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "groupHas", "acc": 1, "dsc": "(<PERSON><PERSON>/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "groupAdd", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "groupAdd", "acc": 1, "dsc": "(<PERSON><PERSON>/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "groupRemove", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "groupRemove", "acc": 1, "dsc": "(<PERSON><PERSON>/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "playerInGroup", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "playerInGroup", "acc": 131073, "dsc": "(<PERSON><PERSON>/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "playerInGroup", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;Ljava/lang/String;)Z"}, {"nme": "playerInGroup", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "playerAddGroup", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "playerAddGroup", "acc": 131073, "dsc": "(<PERSON><PERSON>/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "playerAddGroup", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;Ljava/lang/String;)Z"}, {"nme": "playerAddGroup", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "playerRemoveGroup", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "playerRemoveGroup", "acc": 131073, "dsc": "(<PERSON><PERSON>/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "playerRemoveGroup", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;Ljava/lang/String;)Z"}, {"nme": "playerRemoveGroup", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "getPlayerGroups", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)[Ljava/lang/String;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getPlayerGroups", "acc": 131073, "dsc": "(Lorg/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;)[Ljava/lang/String;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getPlayerGroups", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;)[Ljava/lang/String;"}, {"nme": "getPlayerGroups", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;)[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getPrimaryGroup", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getPrimaryGroup", "acc": 131073, "dsc": "(Lorg/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getPrimaryGroup", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;)Ljava/lang/String;"}, {"nme": "getPrimaryGroup", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;)Ljava/lang/String;"}, {"nme": "getGroups", "acc": 1025, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "hasGroupSupport", "acc": 1025, "dsc": "()Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 28, "nme": "log", "dsc": "Ljava/util/logging/Logger;"}, {"acc": 4, "nme": "plugin", "dsc": "Lorg/bukkit/plugin/Plugin;"}]}, "net/milkbowl/vault/chat/Chat.class": {"ver": 52, "acc": 1057, "nme": "net/milkbowl/vault/chat/Chat", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lnet/milkbowl/vault/permission/Permission;)V"}, {"nme": "getName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isEnabled", "acc": 1025, "dsc": "()Z"}, {"nme": "getPlayerPrefix", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getPlayerPrefix", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;)Ljava/lang/String;"}, {"nme": "getPlayerPrefix", "acc": 131073, "dsc": "(Lorg/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getPlayerPrefix", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;)Ljava/lang/String;"}, {"nme": "setPlayerPrefix", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "setPlayerPrefix", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;Ljava/lang/String;)V"}, {"nme": "setPlayerPrefix", "acc": 131073, "dsc": "(Lo<PERSON>/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "setPlayerPrefix", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Lja<PERSON>/lang/String;)V"}, {"nme": "getPlayerSuffix", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getPlayerSuffix", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;)Ljava/lang/String;"}, {"nme": "getPlayerSuffix", "acc": 131073, "dsc": "(Lorg/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getPlayerSuffix", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;)Ljava/lang/String;"}, {"nme": "setPlayerSuffix", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "setPlayerSuffix", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;Ljava/lang/String;)V"}, {"nme": "setPlayerSuffix", "acc": 131073, "dsc": "(Lo<PERSON>/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "setPlayerSuffix", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Lja<PERSON>/lang/String;)V"}, {"nme": "getGroupPrefix", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getGroupPrefix", "acc": 1, "dsc": "(Lorg/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "setGroupPrefix", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;)V"}, {"nme": "setGroupPrefix", "acc": 1, "dsc": "(Lo<PERSON>/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getGroupSuffix", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getGroupSuffix", "acc": 1, "dsc": "(Lorg/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "setGroupSuffix", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;)V"}, {"nme": "setGroupSuffix", "acc": 1, "dsc": "(Lo<PERSON>/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getPlayerInfoInteger", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;<PERSON>ja<PERSON>/lang/String;I)I"}, {"nme": "getPlayerInfoInteger", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;I)I", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getPlayerInfoInteger", "acc": 131073, "dsc": "(<PERSON><PERSON>/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;I)I", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getPlayerInfoInteger", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;<PERSON><PERSON><PERSON>/lang/String;I)I"}, {"nme": "setPlayerInfoInteger", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;Ljava/lang/String;I)V"}, {"nme": "setPlayerInfoInteger", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;I)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "setPlayerInfoInteger", "acc": 131073, "dsc": "(<PERSON><PERSON>/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;I)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "setPlayerInfoInteger", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"nme": "getGroupInfoInteger", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;I)I"}, {"nme": "getGroupInfoInteger", "acc": 1, "dsc": "(<PERSON><PERSON>/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;I)I"}, {"nme": "setGroupInfoInteger", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"nme": "setGroupInfoInteger", "acc": 1, "dsc": "(<PERSON><PERSON>/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"nme": "getPlayerInfoDouble", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;Ljava/lang/String;D)D"}, {"nme": "getPlayerInfoDouble", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;D)D", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getPlayerInfoDouble", "acc": 131073, "dsc": "(Lo<PERSON>/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;D)D", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getPlayerInfoDouble", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;<PERSON>ja<PERSON>/lang/String;D)D"}, {"nme": "setPlayerInfoDouble", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;Ljava/lang/String;D)V"}, {"nme": "setPlayerInfoDouble", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;D)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "setPlayerInfoDouble", "acc": 131073, "dsc": "(Lo<PERSON>/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;D)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "setPlayerInfoDouble", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;<PERSON>ja<PERSON>/lang/String;D)V"}, {"nme": "getGroupInfoDouble", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;D)D"}, {"nme": "getGroupInfoDouble", "acc": 1, "dsc": "(Lo<PERSON>/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;D)D"}, {"nme": "setGroupInfoDouble", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;D)V"}, {"nme": "setGroupInfoDouble", "acc": 1, "dsc": "(Lo<PERSON>/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;D)V"}, {"nme": "getPlayerInfoBoolean", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;Ljava/lang/String;Z)Z"}, {"nme": "getPlayerInfoBoolean", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Z)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getPlayerInfoBoolean", "acc": 131073, "dsc": "(<PERSON><PERSON>/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Z)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getPlayerInfoBoolean", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;<PERSON><PERSON><PERSON>/lang/String;Z)Z"}, {"nme": "setPlayerInfoBoolean", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;Ljava/lang/String;Z)V"}, {"nme": "setPlayerInfoBoolean", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Z)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "setPlayerInfoBoolean", "acc": 131073, "dsc": "(<PERSON><PERSON>/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Z)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "setPlayerInfoBoolean", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;<PERSON><PERSON><PERSON>/lang/String;Z)V"}, {"nme": "getGroupInfoBoolean", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Z)Z"}, {"nme": "getGroupInfoBoolean", "acc": 1, "dsc": "(<PERSON><PERSON>/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Z)Z"}, {"nme": "setGroupInfoBoolean", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Z)V"}, {"nme": "setGroupInfoBoolean", "acc": 1, "dsc": "(<PERSON><PERSON>/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Z)V"}, {"nme": "getPlayerInfoString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "getPlayerInfoString", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;L<PERSON><PERSON>/lang/String;)Ljava/lang/String;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getPlayerInfoString", "acc": 131073, "dsc": "(Lorg/bukkit/World;L<PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getPlayerInfoString", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "setPlayerInfoString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "setPlayerInfoString", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "setPlayerInfoString", "acc": 131073, "dsc": "(Lorg/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "setPlayerInfoString", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "getGroupInfoString", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;L<PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getGroupInfoString", "acc": 1, "dsc": "(Lorg/bukkit/World;L<PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "setGroupInfoString", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setGroupInfoString", "acc": 1, "dsc": "(Lorg/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "playerInGroup", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;Ljava/lang/String;)Z"}, {"nme": "playerInGroup", "acc": 131073, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "playerInGroup", "acc": 131073, "dsc": "(<PERSON><PERSON>/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "playerInGroup", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "getPlayerGroups", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;)[Ljava/lang/String;"}, {"nme": "getPlayerGroups", "acc": 131073, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)[Ljava/lang/String;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getPlayerGroups", "acc": 131073, "dsc": "(Lorg/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;)[Ljava/lang/String;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getPlayerGroups", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;)[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getPrimaryGroup", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;)Ljava/lang/String;"}, {"nme": "getPrimaryGroup", "acc": 131073, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getPrimaryGroup", "acc": 131073, "dsc": "(Lorg/bukkit/World;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getPrimaryGroup", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;)Ljava/lang/String;"}, {"nme": "getGroups", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "perms", "dsc": "Lnet/milkbowl/vault/permission/Permission;"}]}, "net/milkbowl/vault/economy/Economy.class": {"ver": 52, "acc": 1537, "nme": "net/milkbowl/vault/economy/Economy", "super": "java/lang/Object", "mthds": [{"nme": "isEnabled", "acc": 1025, "dsc": "()Z"}, {"nme": "getName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "hasBankSupport", "acc": 1025, "dsc": "()Z"}, {"nme": "fractionalDigits", "acc": 1025, "dsc": "()I"}, {"nme": "format", "acc": 1025, "dsc": "(D)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "currencyNamePlural", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "currencyNameSingular", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "hasAccount", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "hasAccount", "acc": 1025, "dsc": "(Lorg/bukkit/OfflinePlayer;)Z"}, {"nme": "hasAccount", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "hasAccount", "acc": 1025, "dsc": "(Lorg/bukkit/OfflinePlayer;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "getBalance", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)D", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getBalance", "acc": 1025, "dsc": "(Lorg/bukkit/OfflinePlayer;)D"}, {"nme": "getBalance", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)D", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getBalance", "acc": 1025, "dsc": "(Lorg/bukkit/OfflinePlayer;<PERSON><PERSON><PERSON>/lang/String;)D"}, {"nme": "has", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;D)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "has", "acc": 1025, "dsc": "(Lorg/bukkit/OfflinePlayer;D)Z"}, {"nme": "has", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;D)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "has", "acc": 1025, "dsc": "(Lorg/bukkit/OfflinePlayer;<PERSON><PERSON><PERSON>/lang/String;D)Z"}, {"nme": "withdrawPlayer", "acc": 132097, "dsc": "(Lja<PERSON>/lang/String;D)Lnet/milkbowl/vault/economy/EconomyResponse;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "withdrawPlayer", "acc": 1025, "dsc": "(Lorg/bukkit/OfflinePlayer;D)Lnet/milkbowl/vault/economy/EconomyResponse;"}, {"nme": "withdrawPlayer", "acc": 132097, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;D)Lnet/milkbowl/vault/economy/EconomyResponse;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "withdrawPlayer", "acc": 1025, "dsc": "(Lorg/bukkit/OfflinePlayer;Ljava/lang/String;D)Lnet/milkbowl/vault/economy/EconomyResponse;"}, {"nme": "depositPlayer", "acc": 132097, "dsc": "(Lja<PERSON>/lang/String;D)Lnet/milkbowl/vault/economy/EconomyResponse;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "depositPlayer", "acc": 1025, "dsc": "(Lorg/bukkit/OfflinePlayer;D)Lnet/milkbowl/vault/economy/EconomyResponse;"}, {"nme": "depositPlayer", "acc": 132097, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;D)Lnet/milkbowl/vault/economy/EconomyResponse;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "depositPlayer", "acc": 1025, "dsc": "(Lorg/bukkit/OfflinePlayer;Ljava/lang/String;D)Lnet/milkbowl/vault/economy/EconomyResponse;"}, {"nme": "createBank", "acc": 132097, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;)Lnet/milkbowl/vault/economy/EconomyResponse;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "createBank", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;)Lnet/milkbowl/vault/economy/EconomyResponse;"}, {"nme": "deleteBank", "acc": 1025, "dsc": "(Lja<PERSON>/lang/String;)Lnet/milkbowl/vault/economy/EconomyResponse;"}, {"nme": "bankBalance", "acc": 1025, "dsc": "(Lja<PERSON>/lang/String;)Lnet/milkbowl/vault/economy/EconomyResponse;"}, {"nme": "bankHas", "acc": 1025, "dsc": "(Lja<PERSON>/lang/String;D)Lnet/milkbowl/vault/economy/EconomyResponse;"}, {"nme": "bankWithdraw", "acc": 1025, "dsc": "(Lja<PERSON>/lang/String;D)Lnet/milkbowl/vault/economy/EconomyResponse;"}, {"nme": "bankDeposit", "acc": 1025, "dsc": "(Lja<PERSON>/lang/String;D)Lnet/milkbowl/vault/economy/EconomyResponse;"}, {"nme": "isBankOwner", "acc": 132097, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;)Lnet/milkbowl/vault/economy/EconomyResponse;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "isBankOwner", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;)Lnet/milkbowl/vault/economy/EconomyResponse;"}, {"nme": "isBankMember", "acc": 132097, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;)Lnet/milkbowl/vault/economy/EconomyResponse;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "isBankMember", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/bukkit/OfflinePlayer;)Lnet/milkbowl/vault/economy/EconomyResponse;"}, {"nme": "getBanks", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "createPlayerAccount", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "createPlayerAccount", "acc": 1025, "dsc": "(Lorg/bukkit/OfflinePlayer;)Z"}, {"nme": "createPlayerAccount", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "createPlayerAccount", "acc": 1025, "dsc": "(Lorg/bukkit/OfflinePlayer;<PERSON><PERSON><PERSON>/lang/String;)Z"}], "flds": []}, "net/milkbowl/vault/economy/EconomyResponse.class": {"ver": 52, "acc": 33, "nme": "net/milkbowl/vault/economy/EconomyResponse", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(DDLnet/milkbowl/vault/economy/EconomyResponse$ResponseType;Ljava/lang/String;)V"}, {"nme": "transactionSuccess", "acc": 1, "dsc": "()Z"}], "flds": [{"acc": 17, "nme": "amount", "dsc": "D"}, {"acc": 17, "nme": "balance", "dsc": "D"}, {"acc": 17, "nme": "type", "dsc": "Lnet/milkbowl/vault/economy/EconomyResponse$ResponseType;"}, {"acc": 17, "nme": "errorMessage", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}}}}