{"md5": "ea08de66492717980f9c33e15d243577", "sha2": "6f9f8621d8230cd38aa42e58ccbc0c00569131ce", "sha256": "624a3d745ef1d28e955a6a67af8edba0fdfc5c9bad680a73f67a70bb950a683d", "contents": {"classes": {"org/junit/platform/commons/util/ClassLoaderUtils.class": {"ver": 52, "acc": 49, "nme": "org/junit/platform/commons/util/ClassLoaderUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getDefaultClassLoader", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/lang/ClassLoader;"}, {"nme": "getLocation", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Ljava/util/Optional;", "sig": "(Lja<PERSON>/lang/Object;)Ljava/util/Optional<Ljava/net/URL;>;"}], "flds": [], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.0"]}]}, "org/junit/platform/commons/util/ReflectionUtils.class": {"ver": 52, "acc": 49, "nme": "org/junit/platform/commons/util/ReflectionUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "isPublic", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "isPublic", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Member;)Z"}, {"nme": "isPrivate", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "isPrivate", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Member;)Z"}, {"nme": "isNotPrivate", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.4"]}]}, {"nme": "isNotPrivate", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Member;)Z", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.1"]}]}, {"nme": "isAbstract", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "isAbstract", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Member;)Z"}, {"nme": "isStatic", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "isNotStatic", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.4"]}]}, {"nme": "isStatic", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Member;)Z"}, {"nme": "isNotStatic", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Member;)Z", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.1"]}]}, {"nme": "isFinal", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.5"]}]}, {"nme": "isNotFinal", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.5"]}]}, {"nme": "isFinal", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Member;)Z", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.5"]}]}, {"nme": "isNotFinal", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Member;)Z", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.5"]}]}, {"nme": "isInnerClass", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "returnsVoid", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)Z"}, {"nme": "isArray", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "isMultidimensionalArray", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.3.2"]}]}, {"nme": "isAssignableTo", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/Class;)Z", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/Class<*>;)Z"}, {"nme": "isAssignableTo", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Object;Ljava/lang/Class<*>;)Z"}, {"nme": "isWideningConversion", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/Class;)Z", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/Class<*>;)Z"}, {"nme": "getWrapperType", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/Class;", "sig": "(Lja<PERSON>/lang/Class<*>;)Ljava/lang/Class<*>;"}, {"nme": "newInstance", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;[<PERSON>ja<PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;[Ljava/lang/Object;)TT;"}, {"nme": "newInstance", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Constructor;[<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/reflect/Constructor<TT;>;[Ljava/lang/Object;)TT;"}, {"nme": "readFieldValue", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)Ljava/util/Optional;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;Ljava/lang/String;TT;)Ljava/util/Optional<Ljava/lang/Object;>;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "DEPRECATED"], "since", "1.4"]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "tryToReadFieldValue", "acc": 9, "dsc": "(Lja<PERSON>/lang/Class;Ljava/lang/String;Ljava/lang/Object;)Lorg/junit/platform/commons/function/Try;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;Ljava/lang/String;TT;)Lorg/junit/platform/commons/function/Try<Ljava/lang/Object;>;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.4"]}]}, {"nme": "readFieldValue", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)Ljava/util/Optional;", "sig": "(Lja<PERSON>/lang/reflect/Field;)Ljava/util/Optional<Ljava/lang/Object;>;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "DEPRECATED"], "since", "1.4"]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "tryToReadFieldValue", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)Lorg/junit/platform/commons/function/Try;", "sig": "(Ljava/lang/reflect/Field;)Lorg/junit/platform/commons/function/Try<Ljava/lang/Object;>;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.4"]}]}, {"nme": "readFieldValue", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;L<PERSON><PERSON>/lang/Object;)Ljava/util/Optional;", "sig": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;Ljava/lang/Object;)Ljava/util/Optional<Ljava/lang/Object;>;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "DEPRECATED"], "since", "1.4"]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "tryToReadFieldValue", "acc": 9, "dsc": "(Lja<PERSON>/lang/reflect/Field;Ljava/lang/Object;)Lorg/junit/platform/commons/function/Try;", "sig": "(Ljava/lang/reflect/Field;Ljava/lang/Object;)Lorg/junit/platform/commons/function/Try<Ljava/lang/Object;>;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.4"]}]}, {"nme": "readFieldValues", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/lang/Object;)Ljava/util/List;", "sig": "(Ljava/util/List<Ljava/lang/reflect/Field;>;Ljava/lang/Object;)Ljava/util/List<Ljava/lang/Object;>;"}, {"nme": "readFieldValues", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/util/function/Predicate;)Ljava/util/List;", "sig": "(Ljava/util/List<Ljava/lang/reflect/Field;>;Ljava/lang/Object;Ljava/util/function/Predicate<Ljava/lang/reflect/Field;>;)Ljava/util/List<Ljava/lang/Object;>;"}, {"nme": "invoke<PERSON><PERSON><PERSON>", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;<PERSON><PERSON><PERSON>/lang/Object;[Ljava/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "loadClass", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Optional;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/Optional<Ljava/lang/Class<*>;>;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "DEPRECATED"], "since", "1.4"]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "tryToLoadClass", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/junit/platform/commons/function/Try;", "sig": "(Ljava/lang/String;)Lorg/junit/platform/commons/function/Try<Ljava/lang/Class<*>;>;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.4"]}]}, {"nme": "loadClass", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/ClassLoader;)Ljava/util/Optional;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/ClassLoader;)Ljava/util/Optional<Ljava/lang/Class<*>;>;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "DEPRECATED"], "since", "1.4"]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "tryToLoadClass", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/ClassLoader;)Lorg/junit/platform/commons/function/Try;", "sig": "(Ljava/lang/String;Ljava/lang/ClassLoader;)Lorg/junit/platform/commons/function/Try<Ljava/lang/Class<*>;>;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.4"]}]}, {"nme": "loadArrayType", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;<PERSON><PERSON><PERSON>/lang/String;I)Ljava/lang/Class;", "sig": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;<PERSON><PERSON><PERSON>/lang/String;I)Ljava/lang/Class<*>;", "exs": ["java/lang/ClassNotFoundException"]}, {"nme": "getFullyQualifiedMethodName", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Lja<PERSON>/lang/reflect/Method;)Ljava/lang/String;", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/reflect/Method;)Ljava/lang/String;"}, {"nme": "getFullyQualifiedMethodName", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;L<PERSON><PERSON>/lang/String;[Ljava/lang/Class;)Ljava/lang/String;", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/String;[Ljava/lang/Class<*>;)Ljava/lang/String;"}, {"nme": "parseFullyQualifiedMethodName", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[Lja<PERSON>/lang/String;"}, {"nme": "parseQualifiedMethodName", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[Lja<PERSON>/lang/String;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.9.2"]}]}, {"nme": "getOutermostInstance", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Class;)Ljava/util/Optional;", "sig": "(L<PERSON><PERSON>/lang/Object;Ljava/lang/Class<*>;)Ljava/util/Optional<Ljava/lang/Object;>;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "DEPRECATED"], "since", "1.4"]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getOuterInstance", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Ljava/util/Optional;", "sig": "(<PERSON><PERSON><PERSON>/lang/Object;)Ljava/util/Optional<Ljava/lang/Object;>;"}, {"nme": "getAllClasspathRootDirectories", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljava/nio/file/Path;>;"}, {"nme": "findAllClassesInClasspathRoot", "acc": 9, "dsc": "(Lja<PERSON>/net/URI;Ljava/util/function/Predicate;Ljava/util/function/Predicate;)Ljava/util/List;", "sig": "(Ljava/net/URI;Ljava/util/function/Predicate<Ljava/lang/Class<*>;>;Ljava/util/function/Predicate<Ljava/lang/String;>;)Ljava/util/List<Ljava/lang/Class<*>;>;"}, {"nme": "findAllClassesInClasspathRoot", "acc": 9, "dsc": "(Ljava/net/URI;Lorg/junit/platform/commons/util/ClassFilter;)Ljava/util/List;", "sig": "(Ljava/net/URI;Lorg/junit/platform/commons/util/ClassFilter;)Ljava/util/List<Ljava/lang/Class<*>;>;"}, {"nme": "findAllClassesInPackage", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/function/Predicate;<PERSON><PERSON><PERSON>/util/function/Predicate;)Ljava/util/List;", "sig": "(Ljava/lang/String;Ljava/util/function/Predicate<Ljava/lang/Class<*>;>;Ljava/util/function/Predicate<Ljava/lang/String;>;)Ljava/util/List<Ljava/lang/Class<*>;>;"}, {"nme": "findAllClassesInPackage", "acc": 9, "dsc": "(Ljava/lang/String;Lorg/junit/platform/commons/util/ClassFilter;)Ljava/util/List;", "sig": "(Ljava/lang/String;Lorg/junit/platform/commons/util/ClassFilter;)Ljava/util/List<Ljava/lang/Class<*>;>;"}, {"nme": "findAllClassesInModule", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/function/Predicate;<PERSON><PERSON><PERSON>/util/function/Predicate;)Ljava/util/List;", "sig": "(Ljava/lang/String;Ljava/util/function/Predicate<Ljava/lang/Class<*>;>;Ljava/util/function/Predicate<Ljava/lang/String;>;)Ljava/util/List<Ljava/lang/Class<*>;>;"}, {"nme": "findAllClassesInModule", "acc": 9, "dsc": "(Ljava/lang/String;Lorg/junit/platform/commons/util/ClassFilter;)Ljava/util/List;", "sig": "(Ljava/lang/String;Lorg/junit/platform/commons/util/ClassFilter;)Ljava/util/List<Ljava/lang/Class<*>;>;"}, {"nme": "findNestedClasses", "acc": 9, "dsc": "(L<PERSON><PERSON>/lang/Class;Ljava/util/function/Predicate;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;Ljava/util/function/Predicate<Ljava/lang/Class<*>;>;)Ljava/util/List<Ljava/lang/Class<*>;>;"}, {"nme": "findNestedClasses", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/util/function/Predicate;Lja<PERSON>/util/Set;)V", "sig": "(Ljava/lang/Class<*>;Ljava/util/function/Predicate<Ljava/lang/Class<*>;>;Ljava/util/Set<Ljava/lang/Class<*>;>;)V"}, {"nme": "detectInnerClassCycle", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)V"}, {"nme": "getDeclaredConstructor", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/reflect/Constructor;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;)Ljava/lang/reflect/Constructor<TT;>;"}, {"nme": "findConstructors", "acc": 9, "dsc": "(L<PERSON><PERSON>/lang/Class;Ljava/util/function/Predicate;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;Ljava/util/function/Predicate<Ljava/lang/reflect/Constructor<*>;>;)Ljava/util/List<Ljava/lang/reflect/Constructor<*>;>;"}, {"nme": "findFields", "acc": 9, "dsc": "(Ljava/lang/Class;Ljava/util/function/Predicate;Lorg/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;Ljava/util/function/Predicate<Ljava/lang/reflect/Field;>;Lorg/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode;)Ljava/util/List<Ljava/lang/reflect/Field;>;"}, {"nme": "findAllFieldsInHierarchy", "acc": 10, "dsc": "(Ljava/lang/Class;Lorg/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;Lorg/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode;)Ljava/util/List<Ljava/lang/reflect/Field;>;"}, {"nme": "isMethodPresent", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/util/function/Predicate;)Z", "sig": "(Ljava/lang/Class<*>;Ljava/util/function/Predicate<Ljava/lang/reflect/Method;>;)Z"}, {"nme": "getMethod", "acc": 131208, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;L<PERSON><PERSON>/lang/String;[Ljava/lang/Class;)Ljava/util/Optional;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/String;[Ljava/lang/Class<*>;)Ljava/util/Optional<Ljava/lang/reflect/Method;>;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "DEPRECATED"], "since", "1.4"]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "tryToGetMethod", "acc": 137, "dsc": "(Ljava/lang/Class;Ljava/lang/String;[Ljava/lang/Class;)Lorg/junit/platform/commons/function/Try;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/String;[Ljava/lang/Class<*>;)Lorg/junit/platform/commons/function/Try<Ljava/lang/reflect/Method;>;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.4"]}]}, {"nme": "find<PERSON><PERSON><PERSON>", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Optional;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/String;Ljava/lang/String;)Ljava/util/Optional<Ljava/lang/reflect/Method;>;"}, {"nme": "resolveParameterTypes", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;)[Ljava/lang/Class;", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/String;Ljava/lang/String;)[Ljava/lang/Class<*>;"}, {"nme": "loadRequiredParameterType", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class;", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/Class<*>;"}, {"nme": "find<PERSON><PERSON><PERSON>", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;L<PERSON><PERSON>/lang/String;[Ljava/lang/Class;)Ljava/util/Optional;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/String;[Ljava/lang/Class<*>;)Ljava/util/Optional<Ljava/lang/reflect/Method;>;"}, {"nme": "find<PERSON><PERSON><PERSON>", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Lja<PERSON>/util/function/Predicate;)Ljava/util/Optional;", "sig": "(Ljava/lang/Class<*>;Ljava/util/function/Predicate<Ljava/lang/reflect/Method;>;)Ljava/util/Optional<Ljava/lang/reflect/Method;>;"}, {"nme": "getRequiredMethod", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Lja<PERSON>/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/String;[Ljava/lang/Class<*>;)Ljava/lang/reflect/Method;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "STABLE"], "since", "1.7"]}]}, {"nme": "findMethods", "acc": 9, "dsc": "(L<PERSON><PERSON>/lang/Class;Ljava/util/function/Predicate;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;Ljava/util/function/Predicate<Ljava/lang/reflect/Method;>;)Ljava/util/List<Ljava/lang/reflect/Method;>;"}, {"nme": "findMethods", "acc": 9, "dsc": "(Ljava/lang/Class;Ljava/util/function/Predicate;Lorg/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;Ljava/util/function/Predicate<Ljava/lang/reflect/Method;>;Lorg/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode;)Ljava/util/List<Ljava/lang/reflect/Method;>;"}, {"nme": "findAllMethodsInHierarchy", "acc": 10, "dsc": "(Ljava/lang/Class;Lorg/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;Lorg/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode;)Ljava/util/List<Ljava/lang/reflect/Method;>;"}, {"nme": "getFields", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;)Ljava/util/List<Ljava/lang/reflect/Field;>;"}, {"nme": "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;)Ljava/util/List<Ljava/lang/reflect/Field;>;"}, {"nme": "getMethods", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;)Ljava/util/List<Ljava/lang/reflect/Method;>;"}, {"nme": "getDeclaredMethods", "acc": 10, "dsc": "(Ljava/lang/Class;Lorg/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;Lorg/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode;)Ljava/util/List<Ljava/lang/reflect/Method;>;"}, {"nme": "getDefaultMethods", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;)Ljava/util/List<Ljava/lang/reflect/Method;>;"}, {"nme": "toSortedMutableList", "acc": 10, "dsc": "([<PERSON><PERSON><PERSON>/lang/reflect/Field;)<PERSON>ja<PERSON>/util/List;", "sig": "([<PERSON><PERSON><PERSON>/lang/reflect/Field;)Ljava/util/List<Ljava/lang/reflect/Field;>;"}, {"nme": "toSortedMutableList", "acc": 10, "dsc": "([<PERSON><PERSON><PERSON>/lang/reflect/Method;)Ljava/util/List;", "sig": "([Lja<PERSON>/lang/reflect/Method;)Ljava/util/List<Ljava/lang/reflect/Method;>;"}, {"nme": "default<PERSON>ieldSorter", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;<PERSON>ja<PERSON>/lang/reflect/Field;)I"}, {"nme": "defaultMethodSorter", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;Ljava/lang/reflect/Method;)I"}, {"nme": "getInterfaceMethods", "acc": 10, "dsc": "(Ljava/lang/Class;Lorg/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;Lorg/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode;)Ljava/util/List<Ljava/lang/reflect/Method;>;"}, {"nme": "getInterfaceFields", "acc": 10, "dsc": "(Ljava/lang/Class;Lorg/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;Lorg/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode;)Ljava/util/List<Ljava/lang/reflect/Field;>;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "acc": 10, "dsc": "(Ljava/lang/Class;Lorg/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;Lorg/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode;)Ljava/util/List<Ljava/lang/reflect/Field;>;"}, {"nme": "isFieldShadowedByLocalFields", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;<PERSON>ja<PERSON>/util/List;)Z", "sig": "(Lja<PERSON>/lang/reflect/Field;Ljava/util/List<Ljava/lang/reflect/Field;>;)Z"}, {"nme": "getSuperclassMethods", "acc": 10, "dsc": "(Ljava/lang/Class;Lorg/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;Lorg/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode;)Ljava/util/List<Ljava/lang/reflect/Method;>;"}, {"nme": "isMethodShadowedByLocalMethods", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;Ljava/util/List;)Z", "sig": "(Lja<PERSON>/lang/reflect/Method;Ljava/util/List<Ljava/lang/reflect/Method;>;)Z"}, {"nme": "isMethodShadowedBy", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;Ljava/lang/reflect/Method;)Z"}, {"nme": "hasCompatibleSignature", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;Ljava/lang/String;[Ljava/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;Ljava/lang/String;[Ljava/lang/Class<*>;)Z"}, {"nme": "isGeneric", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)Z"}, {"nme": "isGeneric", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Type;)Z"}, {"nme": "makeAccessible", "acc": 9, "dsc": "(Ljava/lang/reflect/AccessibleObject;)Ljava/lang/reflect/AccessibleObject;", "sig": "<T:Ljava/lang/reflect/AccessibleObject;>(TT;)TT;"}, {"nme": "getAllAssignmentCompatibleClasses", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>java/util/Set;", "sig": "(Ljava/lang/Class<*>;)Ljava/util/Set<Ljava/lang/Class<*>;>;"}, {"nme": "getAllAssignmentCompatibleClasses", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON>ja<PERSON>/util/Set;)V", "sig": "(Ljava/lang/Class<*>;Ljava/util/Set<Ljava/lang/Class<*>;>;)V"}, {"nme": "isSearchable", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "getUnderlyingCause", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)<PERSON><PERSON><PERSON>/lang/Throwable;"}, {"nme": "lambda$isMethodShadowedByLocalMethods$34", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;Ljava/lang/reflect/Method;)Z"}, {"nme": "lambda$isFieldShadowedByLocalFields$33", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;<PERSON>java/lang/reflect/Field;)Z"}, {"nme": "lambda$getInterfaceFields$32", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/lang/reflect/Field;)Z"}, {"nme": "lambda$getInterfaceMethods$31", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/List;L<PERSON><PERSON>/lang/reflect/Method;)Z"}, {"nme": "lambda$getInterfaceMethods$30", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)Z"}, {"nme": "lambda$findAllMethodsInHierarchy$29", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/List;L<PERSON><PERSON>/lang/reflect/Method;)Z"}, {"nme": "lambda$findAllMethodsInHierarchy$28", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/List;L<PERSON><PERSON>/lang/reflect/Method;)Z"}, {"nme": "lambda$findAllMethodsInHierarchy$27", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)Z"}, {"nme": "lambda$getRequiredMethod$26", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/Class;)Lorg/junit/platform/commons/JUnitException;"}, {"nme": "lambda$findMethod$25", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/Class;Ljava/lang/reflect/Method;)Z"}, {"nme": "lambda$loadRequiredParameterType$24", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/Class;Ljava/lang/Exception;)Lorg/junit/platform/commons/JUnitException;"}, {"nme": "lambda$resolveParameterTypes$23", "acc": 4106, "dsc": "(I)[<PERSON><PERSON><PERSON>/lang/Class;"}, {"nme": "lambda$resolveParameterTypes$22", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class;"}, {"nme": "lambda$tryToGetMethod$21", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Lja<PERSON>/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;", "exs": ["java/lang/Exception"]}, {"nme": "lambda$findAllFieldsInHierarchy$20", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/lang/reflect/Field;)Z"}, {"nme": "lambda$findAllFieldsInHierarchy$19", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/lang/reflect/Field;)Z"}, {"nme": "lambda$findAllFieldsInHierarchy$18", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)Z"}, {"nme": "lambda$getDeclaredConstructor$17", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/String;"}, {"nme": "lambda$getDeclaredConstructor$16", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Constructor;)Z"}, {"nme": "lambda$findNestedClasses$15", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/String;"}, {"nme": "lambda$getAllClasspathRootDirectories$14", "acc": 4106, "dsc": "(Ljava/nio/file/Path;)Z"}, {"nme": "lambda$getAllClasspathRootDirectories$13", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;)Ljava/nio/file/Path;"}, {"nme": "lambda$getOuterInstance$12", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/reflect/Field;)<PERSON>ja<PERSON>/lang/Object;"}, {"nme": "lambda$getOuterInstance$11", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)Z"}, {"nme": "lambda$parseFullyQualifiedMethodName$10", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "lambda$tryToLoadClass$9", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/ClassLoader;)Ljava/lang/Class;", "exs": ["java/lang/Exception"]}, {"nme": "lambda$invokeMethod$8", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)Ljava/lang/String;"}, {"nme": "lambda$readFieldValues$7", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/reflect/Field;)<PERSON>ja<PERSON>/lang/Object;"}, {"nme": "lambda$readFieldValues$6", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)Z"}, {"nme": "lambda$tryToReadFieldValue$5", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}, {"nme": "lambda$tryToReadFieldValue$4", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)Ljava/lang/String;"}, {"nme": "lambda$tryToReadFieldValue$3", "acc": 4106, "dsc": "(L<PERSON><PERSON>/lang/Object;Ljava/lang/reflect/Field;)Lorg/junit/platform/commons/function/Try;"}, {"nme": "lambda$tryToReadFieldValue$2", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;L<PERSON><PERSON>/lang/String;)Ljava/lang/reflect/Field;", "exs": ["java/lang/Exception"]}, {"nme": "lambda$newInstance$1", "acc": 4106, "dsc": "(I)[<PERSON><PERSON><PERSON>/lang/Class;"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "(L<PERSON><PERSON>/util/Map;Ljava/lang/Class;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "logger", "dsc": "Lorg/junit/platform/commons/logging/Logger;"}, {"acc": 26, "nme": "VM_INTERNAL_OBJECT_ARRAY_PATTERN", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 26, "nme": "VM_INTERNAL_PRIMITIVE_ARRAY_PATTERN", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 26, "nme": "SOURCE_CODE_SYNTAX_ARRAY_PATTERN", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 26, "nme": "EMPTY_CLASS_ARRAY", "dsc": "[Ljava/lang/Class;", "sig": "[Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "classpathScanner", "dsc": "Lorg/junit/platform/commons/util/ClasspathScanner;"}, {"acc": 26, "nme": "noCyclesDetectedCache", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 26, "nme": "classNameToTypeMap", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Class<*>;>;"}, {"acc": 26, "nme": "primitiveToWrapperMap", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Class<*>;Ljava/lang/Class<*>;>;"}], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.0"]}]}, "org/junit/platform/commons/util/ModuleUtils.class": {"ver": 52, "acc": 33, "nme": "org/junit/platform/commons/util/ModuleUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "findAllNonSystemBootModuleNames", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "isJavaPlatformModuleSystemAvailable", "acc": 9, "dsc": "()Z"}, {"nme": "getModuleName", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/util/Optional;", "sig": "(Ljava/lang/Class<*>;)Ljava/util/Optional<Ljava/lang/String;>;"}, {"nme": "getModuleVersion", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/util/Optional;", "sig": "(Ljava/lang/Class<*>;)Ljava/util/Optional<Ljava/lang/String;>;"}, {"nme": "findAllClassesInModule", "acc": 9, "dsc": "(Ljava/lang/String;Lorg/junit/platform/commons/util/ClassFilter;)Ljava/util/List;", "sig": "(Ljava/lang/String;Lorg/junit/platform/commons/util/ClassFilter;)Ljava/util/List<Ljava/lang/Class<*>;>;"}, {"nme": "lambda$findAllClassesInModule$1", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$findAllNonSystemBootModuleNames$0", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "logger", "dsc": "Lorg/junit/platform/commons/logging/Logger;"}], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.1"]}]}, "org/junit/platform/commons/support/AnnotationSupport.class": {"ver": 52, "acc": 49, "nme": "org/junit/platform/commons/support/AnnotationSupport", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "isAnnotated", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Optional;Lja<PERSON>/lang/Class;)Z", "sig": "(L<PERSON><PERSON>/util/Optional<+Ljava/lang/reflect/AnnotatedElement;>;Ljava/lang/Class<+Ljava/lang/annotation/Annotation;>;)Z", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "MAINTAINED"], "since", "1.3"]}]}, {"nme": "isAnnotated", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/AnnotatedElement;Ljava/lang/Class;)Z", "sig": "(L<PERSON><PERSON>/lang/reflect/AnnotatedElement;Ljava/lang/Class<+Ljava/lang/annotation/Annotation;>;)Z"}, {"nme": "findAnnotation", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Optional;L<PERSON><PERSON>/lang/Class;)Ljava/util/Optional;", "sig": "<A::Ljava/lang/annotation/Annotation;>(Ljava/util/Optional<+Ljava/lang/reflect/AnnotatedElement;>;Ljava/lang/Class<TA;>;)Ljava/util/Optional<TA;>;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "MAINTAINED"], "since", "1.1"]}]}, {"nme": "findAnnotation", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/AnnotatedElement;Ljava/lang/Class;)Ljava/util/Optional;", "sig": "<A::Ljava/lang/annotation/Annotation;>(Ljava/lang/reflect/AnnotatedElement;Ljava/lang/Class<TA;>;)Ljava/util/Optional<TA;>;"}, {"nme": "findAnnotation", "acc": 9, "dsc": "(Ljava/lang/Class;Ljava/lang/Class;Lorg/junit/platform/commons/support/SearchOption;)Ljava/util/Optional;", "sig": "<A::Ljava/lang/annotation/Annotation;>(Ljava/lang/Class<*>;Ljava/lang/Class<TA;>;Lorg/junit/platform/commons/support/SearchOption;)Ljava/util/Optional<TA;>;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "EXPERIMENTAL"], "since", "1.8"]}]}, {"nme": "findRepeatableAnnotations", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Optional;L<PERSON><PERSON>/lang/Class;)Ljava/util/List;", "sig": "<A::Ljava/lang/annotation/Annotation;>(Ljava/util/Optional<+Ljava/lang/reflect/AnnotatedElement;>;Ljava/lang/Class<TA;>;)Ljava/util/List<TA;>;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "MAINTAINED"], "since", "1.5"]}]}, {"nme": "findRepeatableAnnotations", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/AnnotatedElement;Ljava/lang/Class;)Ljava/util/List;", "sig": "<A::Ljava/lang/annotation/Annotation;>(Ljava/lang/reflect/AnnotatedElement;Ljava/lang/Class<TA;>;)Ljava/util/List<TA;>;"}, {"nme": "findPublicAnnotatedFields", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;L<PERSON>va/lang/Class;Ljava/lang/Class;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Class<*>;Ljava/lang/Class<+Ljava/lang/annotation/Annotation;>;)Ljava/util/List<Ljava/lang/reflect/Field;>;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "acc": 9, "dsc": "(L<PERSON><PERSON>/lang/Class;Ljava/lang/Class;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Class<+Ljava/lang/annotation/Annotation;>;)Ljava/util/List<Ljava/lang/reflect/Field;>;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "MAINTAINED"], "since", "1.4"]}]}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "acc": 9, "dsc": "(Ljava/lang/Class;Ljava/lang/Class;Ljava/util/function/Predicate;Lorg/junit/platform/commons/support/HierarchyTraversalMode;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Class<+Ljava/lang/annotation/Annotation;>;Ljava/util/function/Predicate<Ljava/lang/reflect/Field;>;Lorg/junit/platform/commons/support/HierarchyTraversalMode;)Ljava/util/List<Ljava/lang/reflect/Field;>;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "MAINTAINED"], "since", "1.4"]}]}, {"nme": "findAnnotatedFieldValues", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;L<PERSON><PERSON>/lang/Class;)Ljava/util/List;", "sig": "(L<PERSON><PERSON>/lang/Object;Ljava/lang/Class<+Ljava/lang/annotation/Annotation;>;)Ljava/util/List<Ljava/lang/Object;>;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "MAINTAINED"], "since", "1.4"]}]}, {"nme": "findAnnotatedFieldValues", "acc": 9, "dsc": "(L<PERSON><PERSON>/lang/Class;Ljava/lang/Class;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Class<+Ljava/lang/annotation/Annotation;>;)Ljava/util/List<Ljava/lang/Object;>;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "MAINTAINED"], "since", "1.4"]}]}, {"nme": "findAnnotatedFieldValues", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/Class;)Ljava/util/List;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Object;Ljava/lang/Class<+Ljava/lang/annotation/Annotation;>;Ljava/lang/Class<TT;>;)Ljava/util/List<TT;>;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "MAINTAINED"], "since", "1.4"]}]}, {"nme": "findAnnotatedFieldValues", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;L<PERSON>va/lang/Class;Ljava/lang/Class;)Ljava/util/List;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<*>;Ljava/lang/Class<+Ljava/lang/annotation/Annotation;>;Ljava/lang/Class<TT;>;)Ljava/util/List<TT;>;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "MAINTAINED"], "since", "1.4"]}]}, {"nme": "findAnnotatedMethods", "acc": 9, "dsc": "(Ljava/lang/Class;Ljava/lang/Class;Lorg/junit/platform/commons/support/HierarchyTraversalMode;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Class<+Ljava/lang/annotation/Annotation;>;Lorg/junit/platform/commons/support/HierarchyTraversalMode;)Ljava/util/List<Ljava/lang/reflect/Method;>;"}, {"nme": "lambda$findAnnotatedFieldValues$2", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/reflect/Field;)Z"}, {"nme": "lambda$findAnnotatedFieldValues$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/reflect/Field;)Z"}, {"nme": "lambda$findAnnotatedFields$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)Z"}], "flds": [], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "MAINTAINED"], "since", "1.0"]}]}, "org/junit/platform/commons/util/UnrecoverableExceptions.class": {"ver": 52, "acc": 49, "nme": "org/junit/platform/commons/util/UnrecoverableExceptions", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "rethrowIfUnrecoverable", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)V"}], "flds": [], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.7"]}]}, "org/junit/platform/commons/support/ClassSupport.class": {"ver": 52, "acc": 49, "nme": "org/junit/platform/commons/support/ClassSupport", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "nullSafeToString", "acc": 137, "dsc": "([<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/String;", "sig": "([<PERSON><PERSON><PERSON>/lang/Class<*>;)Ljava/lang/String;"}, {"nme": "nullSafeToString", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Function;[Ljava/lang/Class;)Ljava/lang/String;", "sig": "(L<PERSON><PERSON>/util/function/Function<-Ljava/lang/Class<*>;+Ljava/lang/String;>;[Ljava/lang/Class<*>;)Ljava/lang/String;"}], "flds": [], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "MAINTAINED"], "since", "1.1"]}]}, "org/junit/platform/commons/logging/LogRecordListener.class": {"ver": 52, "acc": 33, "nme": "org/junit/platform/commons/logging/LogRecordListener", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "logRecordSubmitted", "acc": 1, "dsc": "(Ljava/util/logging/LogRecord;)V"}, {"nme": "stream", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<Ljava/util/logging/LogRecord;>;"}, {"nme": "stream", "acc": 1, "dsc": "(Ljava/util/logging/Level;)Ljava/util/stream/Stream;", "sig": "(Ljava/util/logging/Level;)Ljava/util/stream/Stream<Ljava/util/logging/LogRecord;>;"}, {"nme": "stream", "acc": 1, "dsc": "(Ljava/lang/Class;)Ljava/util/stream/Stream;", "sig": "(Ljava/lang/Class<*>;)Ljava/util/stream/Stream<Ljava/util/logging/LogRecord;>;"}, {"nme": "stream", "acc": 1, "dsc": "(Ljava/lang/Class;Ljava/util/logging/Level;)Ljava/util/stream/Stream;", "sig": "(Ljava/lang/Class<*>;Ljava/util/logging/Level;)Ljava/util/stream/Stream<Ljava/util/logging/LogRecord;>;"}, {"nme": "clear", "acc": 1, "dsc": "()V"}, {"nme": "lambda$stream$2", "acc": 4106, "dsc": "(Ljava/util/logging/Level;Ljava/util/logging/LogRecord;)Z"}, {"nme": "lambda$stream$1", "acc": 4106, "dsc": "(Ljava/lang/Class;Ljava/util/logging/LogRecord;)Z"}, {"nme": "lambda$stream$0", "acc": 4106, "dsc": "(Ljava/util/logging/Level;Ljava/util/logging/LogRecord;)Z"}], "flds": [{"acc": 18, "nme": "logRecords", "dsc": "<PERSON><PERSON><PERSON>/lang/ThreadLocal;", "sig": "Ljava/lang/ThreadLocal<Ljava/util/List<Ljava/util/logging/LogRecord;>;>;"}], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.1"]}]}, "org/junit/platform/commons/JUnitException.class": {"ver": 52, "acc": 33, "nme": "org/junit/platform/commons/JUnitException", "super": "java/lang/RuntimeException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "STABLE"], "since", "1.5"]}]}, "org/junit/platform/commons/logging/LoggerFactory$DelegatingLogger.class": {"ver": 52, "acc": 48, "nme": "org/junit/platform/commons/logging/LoggerFactory$DelegatingLogger", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "error", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Supplier;)V", "sig": "(Lja<PERSON>/util/function/Supplier<Ljava/lang/String;>;)V"}, {"nme": "error", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;Lja<PERSON>/util/function/Supplier;)V", "sig": "(L<PERSON><PERSON>/lang/Throwable;Ljava/util/function/Supplier<Ljava/lang/String;>;)V"}, {"nme": "warn", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Supplier;)V", "sig": "(Lja<PERSON>/util/function/Supplier<Ljava/lang/String;>;)V"}, {"nme": "warn", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;Lja<PERSON>/util/function/Supplier;)V", "sig": "(L<PERSON><PERSON>/lang/Throwable;Ljava/util/function/Supplier<Ljava/lang/String;>;)V"}, {"nme": "info", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Supplier;)V", "sig": "(Lja<PERSON>/util/function/Supplier<Ljava/lang/String;>;)V"}, {"nme": "info", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;Lja<PERSON>/util/function/Supplier;)V", "sig": "(L<PERSON><PERSON>/lang/Throwable;Ljava/util/function/Supplier<Ljava/lang/String;>;)V"}, {"nme": "config", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Supplier;)V", "sig": "(Lja<PERSON>/util/function/Supplier<Ljava/lang/String;>;)V"}, {"nme": "config", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;Lja<PERSON>/util/function/Supplier;)V", "sig": "(L<PERSON><PERSON>/lang/Throwable;Ljava/util/function/Supplier<Ljava/lang/String;>;)V"}, {"nme": "debug", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Supplier;)V", "sig": "(Lja<PERSON>/util/function/Supplier<Ljava/lang/String;>;)V"}, {"nme": "debug", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;Lja<PERSON>/util/function/Supplier;)V", "sig": "(L<PERSON><PERSON>/lang/Throwable;Ljava/util/function/Supplier<Ljava/lang/String;>;)V"}, {"nme": "trace", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Supplier;)V", "sig": "(Lja<PERSON>/util/function/Supplier<Ljava/lang/String;>;)V"}, {"nme": "trace", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;Lja<PERSON>/util/function/Supplier;)V", "sig": "(L<PERSON><PERSON>/lang/Throwable;Ljava/util/function/Supplier<Ljava/lang/String;>;)V"}, {"nme": "log", "acc": 2, "dsc": "(Lja<PERSON>/util/logging/Level;Ljava/lang/Throwable;Ljava/util/function/Supplier;)V", "sig": "(Ljava/util/logging/Level;Ljava/lang/Throwable;Ljava/util/function/Supplier<Ljava/lang/String;>;)V"}, {"nme": "createLogRecord", "acc": 2, "dsc": "(Ljava/util/logging/Level;Ljava/lang/Throwable;Ljava/lang/String;)Ljava/util/logging/LogRecord;"}, {"nme": "nullSafeGet", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Supplier;)Ljava/lang/String;", "sig": "(L<PERSON><PERSON>/util/function/Supplier<Ljava/lang/String;>;)Ljava/lang/String;"}, {"nme": "lambda$log$0", "acc": 4106, "dsc": "(Ljava/util/logging/LogRecord;Lorg/junit/platform/commons/logging/LogRecordListener;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "FQCN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "jul<PERSON><PERSON>ger", "dsc": "Ljava/util/logging/Logger;"}]}, "org/junit/platform/commons/logging/LoggerFactory.class": {"ver": 52, "acc": 49, "nme": "org/junit/platform/commons/logging/LoggerFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 9, "dsc": "(Lja<PERSON>/lang/Class;)Lorg/junit/platform/commons/logging/Logger;", "sig": "(Ljava/lang/Class<*>;)Lorg/junit/platform/commons/logging/Logger;"}, {"nme": "addListener", "acc": 9, "dsc": "(Lorg/junit/platform/commons/logging/LogRecordListener;)V"}, {"nme": "removeListener", "acc": 9, "dsc": "(Lorg/junit/platform/commons/logging/LogRecordListener;)V"}, {"nme": "access$000", "acc": 4104, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "listeners", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Lorg/junit/platform/commons/logging/LogRecordListener;>;"}], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.0"]}]}, "org/junit/platform/commons/util/BlacklistedExceptions.class": {"ver": 52, "acc": 131121, "nme": "org/junit/platform/commons/util/BlacklistedExceptions", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "rethrowIfBlacklisted", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}, {"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "DEPRECATED"], "since", "1.7"]}]}, "module-info.class": {"ver": 53, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "org/junit/platform/commons/function/Try$1.class": {"ver": 52, "acc": 4128, "nme": "org/junit/platform/commons/function/Try$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/junit/platform/commons/util/LruCache.class": {"ver": 52, "acc": 33, "nme": "org/junit/platform/commons/util/LruCache", "super": "java/util/LinkedHashMap", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(I)V"}, {"nme": "removeEldestEntry", "acc": 4, "dsc": "(Ljava/util/Map$Entry;)Z", "sig": "(Ljava/util/Map$Entry<TK;TV;>;)Z"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}, {"acc": 18, "nme": "maxSize", "dsc": "I"}], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.6"]}]}, "org/junit/platform/commons/util/ToStringBuilder.class": {"ver": 52, "acc": 33, "nme": "org/junit/platform/commons/util/ToStringBuilder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.7"]}]}, {"nme": "append", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/Object;)Lorg/junit/platform/commons/util/ToStringBuilder;"}, {"nme": "toString", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON>ja<PERSON>/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "typeName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "values", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.0"]}]}, "org/junit/platform/commons/support/HierarchyTraversalMode.class": {"ver": 52, "acc": 16433, "nme": "org/junit/platform/commons/support/HierarchyTraversalMode", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lorg/junit/platform/commons/support/HierarchyTraversalMode;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON>ja<PERSON>/lang/String;)Lorg/junit/platform/commons/support/HierarchyTraversalMode;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Lorg/junit/platform/commons/support/HierarchyTraversalMode;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "TOP_DOWN", "dsc": "Lorg/junit/platform/commons/support/HierarchyTraversalMode;"}, {"acc": 16409, "nme": "BOTTOM_UP", "dsc": "Lorg/junit/platform/commons/support/HierarchyTraversalMode;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lorg/junit/platform/commons/support/HierarchyTraversalMode;"}], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "MAINTAINED"], "since", "1.0"]}]}, "org/junit/platform/commons/util/ClassNamePatternFilterUtils.class": {"ver": 52, "acc": 33, "nme": "org/junit/platform/commons/util/ClassNamePatternFilterUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "excludeMatchingClasses", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/function/Predicate;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/String;)Ljava/util/function/Predicate<TT;>;"}, {"nme": "createPredicateFromPatterns", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/function/Predicate;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/String;)Ljava/util/function/Predicate<TT;>;"}, {"nme": "convertToRegularExpressions", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List<Ljava/util/regex/Pattern;>;"}, {"nme": "replaceRegExElements", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "lambda$createPredicateFromPatterns$3", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "lambda$createPredicateFromPatterns$2", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/util/regex/Pattern;)Z"}, {"nme": "lambda$createPredicateFromPatterns$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "lambda$excludeMatchingClasses$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "flds": [{"acc": 25, "nme": "DEACTIVATE_ALL_PATTERN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "*"}], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.7"]}]}, "org/junit/platform/commons/util/Preconditions.class": {"ver": 52, "acc": 49, "nme": "org/junit/platform/commons/util/Preconditions", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "notNull", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(TT;Ljava/lang/String;)TT;", "exs": ["org/junit/platform/commons/PreconditionViolationException"]}, {"nme": "notNull", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/util/function/Supplier;)Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>(TT;Ljava/util/function/Supplier<Ljava/lang/String;>;)TT;", "exs": ["org/junit/platform/commons/PreconditionViolationException"]}, {"nme": "notEmpty", "acc": 9, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)[I", "exs": ["org/junit/platform/commons/PreconditionViolationException"], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "EXPERIMENTAL"], "since", "1.9"]}]}, {"nme": "notEmpty", "acc": 9, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;)[Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>([TT;Ljava/lang/String;)[TT;", "exs": ["org/junit/platform/commons/PreconditionViolationException"]}, {"nme": "notEmpty", "acc": 9, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/util/function/Supplier;)[Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>([TT;Ljava/util/function/Supplier<Ljava/lang/String;>;)[TT;", "exs": ["org/junit/platform/commons/PreconditionViolationException"]}, {"nme": "notEmpty", "acc": 9, "dsc": "(L<PERSON><PERSON>/util/Collection;L<PERSON><PERSON>/lang/String;)Ljava/util/Collection;", "sig": "<T::Ljava/util/Collection<*>;>(TT;Ljava/lang/String;)TT;", "exs": ["org/junit/platform/commons/PreconditionViolationException"]}, {"nme": "notEmpty", "acc": 9, "dsc": "(Ljava/util/Collection;Ljava/util/function/Supplier;)Ljava/util/Collection;", "sig": "<T::Ljava/util/Collection<*>;>(TT;Ljava/util/function/Supplier<Ljava/lang/String;>;)TT;", "exs": ["org/junit/platform/commons/PreconditionViolationException"]}, {"nme": "containsNoNullElements", "acc": 9, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;)[Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>([TT;Ljava/lang/String;)[TT;", "exs": ["org/junit/platform/commons/PreconditionViolationException"]}, {"nme": "containsNoNullElements", "acc": 9, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/util/function/Supplier;)[Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>([TT;Ljava/util/function/Supplier<Ljava/lang/String;>;)[TT;", "exs": ["org/junit/platform/commons/PreconditionViolationException"]}, {"nme": "containsNoNullElements", "acc": 9, "dsc": "(L<PERSON><PERSON>/util/Collection;L<PERSON><PERSON>/lang/String;)Ljava/util/Collection;", "sig": "<T::Ljava/util/Collection<*>;>(TT;Ljava/lang/String;)TT;", "exs": ["org/junit/platform/commons/PreconditionViolationException"]}, {"nme": "containsNoNullElements", "acc": 9, "dsc": "(Ljava/util/Collection;Ljava/util/function/Supplier;)Ljava/util/Collection;", "sig": "<T::Ljava/util/Collection<*>;>(TT;Ljava/util/function/Supplier<Ljava/lang/String;>;)TT;", "exs": ["org/junit/platform/commons/PreconditionViolationException"]}, {"nme": "notBlank", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "exs": ["org/junit/platform/commons/PreconditionViolationException"]}, {"nme": "notBlank", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;L<PERSON><PERSON>/util/function/Supplier;)Ljava/lang/String;", "sig": "(Ljava/lang/String;Ljava/util/function/Supplier<Ljava/lang/String;>;)Ljava/lang/String;", "exs": ["org/junit/platform/commons/PreconditionViolationException"]}, {"nme": "condition", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/junit/platform/commons/PreconditionViolationException"]}, {"nme": "condition", "acc": 9, "dsc": "(ZLjava/util/function/Supplier;)V", "sig": "(ZLjava/util/function/Supplier<Ljava/lang/String;>;)V", "exs": ["org/junit/platform/commons/PreconditionViolationException"]}, {"nme": "lambda$containsNoNullElements$3", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Supplier;Ljava/lang/Object;)V"}, {"nme": "lambda$containsNoNullElements$2", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "lambda$containsNoNullElements$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Supplier;Ljava/lang/Object;)V"}, {"nme": "lambda$containsNoNullElements$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}], "flds": [], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.0"]}]}, "org/junit/platform/commons/util/CloseablePath.class": {"ver": 52, "acc": 48, "nme": "org/junit/platform/commons/util/CloseablePath", "super": "java/lang/Object", "mthds": [{"nme": "create", "acc": 8, "dsc": "(Ljava/net/URI;)Lorg/junit/platform/commons/util/CloseablePath;", "exs": ["java/net/URISyntaxException"]}, {"nme": "createForJarFileSystem", "acc": 10, "dsc": "(Ljava/net/URI;Ljava/util/function/Function;)Lorg/junit/platform/commons/util/CloseablePath;", "sig": "(Ljava/net/URI;Ljava/util/function/Function<Ljava/nio/file/FileSystem;Ljava/nio/file/Path;>;)Lorg/junit/platform/commons/util/CloseablePath;"}, {"nme": "<init>", "acc": 2, "dsc": "(Ljava/nio/file/Path;Ljava/io/Closeable;)V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "lambda$createForJarFileSystem$5", "acc": 4106, "dsc": "(Ljava/net/URI;Lorg/junit/platform/commons/util/CloseablePath$ManagedFileSystem;)V", "exs": ["java/io/IOException"]}, {"nme": "lambda$createForJarFileSystem$4", "acc": 4106, "dsc": "(Lorg/junit/platform/commons/util/CloseablePath$ManagedFileSystem;Ljava/net/URI;Lorg/junit/platform/commons/util/CloseablePath$ManagedFileSystem;)Lorg/junit/platform/commons/util/CloseablePath$ManagedFileSystem;"}, {"nme": "lambda$createForJarFileSystem$3", "acc": 4106, "dsc": "(Ljava/net/URI;Ljava/net/URI;Lorg/junit/platform/commons/util/CloseablePath$ManagedFileSystem;)Lorg/junit/platform/commons/util/CloseablePath$ManagedFileSystem;"}, {"nme": "lambda$create$2", "acc": 4106, "dsc": "(Ljava/nio/file/FileSystem;)Ljava/nio/file/Path;"}, {"nme": "lambda$create$1", "acc": 4106, "dsc": "(Ljava/lang/String;Ljava/nio/file/FileSystem;)Ljava/nio/file/Path;"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "FILE_URI_SCHEME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "file"}, {"acc": 26, "nme": "JAR_URI_SCHEME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jar"}, {"acc": 26, "nme": "JAR_FILE_EXTENSION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": ".jar"}, {"acc": 26, "nme": "JAR_URI_SEPARATOR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "!"}, {"acc": 26, "nme": "NULL_CLOSEABLE", "dsc": "<PERSON><PERSON><PERSON>/io/Closeable;"}, {"acc": 26, "nme": "MANAGED_FILE_SYSTEMS", "dsc": "Ljava/util/concurrent/ConcurrentMap;", "sig": "Ljava/util/concurrent/ConcurrentMap<Ljava/net/URI;Lorg/junit/platform/commons/util/CloseablePath$ManagedFileSystem;>;"}, {"acc": 18, "nme": "path", "dsc": "Ljava/nio/file/Path;"}, {"acc": 18, "nme": "delegate", "dsc": "<PERSON><PERSON><PERSON>/io/Closeable;"}]}, "org/junit/platform/commons/util/StringUtils.class": {"ver": 52, "acc": 49, "nme": "org/junit/platform/commons/util/StringUtils", "super": "java/lang/Object", "mthds": [{"nme": "compileIsoControlPattern", "acc": 8, "dsc": "()<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "isBlank", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isNotBlank", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "containsWhitespace", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "doesNotContainWhitespace", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "containsIsoControlCharacter", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "doesNotContainIsoControlCharacter", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "nullSafeToString", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON>ja<PERSON>/lang/String;"}, {"nme": "defaultToString", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON>ja<PERSON>/lang/String;"}, {"nme": "replaceIsoControlCharacters", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.4"]}]}, {"nme": "replaceWhitespaceCharacters", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.4"]}]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "ISO_CONTROL_PATTERN", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 26, "nme": "WHITESPACE_PATTERN", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.0"]}]}, "org/junit/platform/commons/support/ModifierSupport.class": {"ver": 52, "acc": 49, "nme": "org/junit/platform/commons/support/ModifierSupport", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "isPublic", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "isPublic", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Member;)Z"}, {"nme": "isPrivate", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "isPrivate", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Member;)Z"}, {"nme": "isNotPrivate", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "isNotPrivate", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Member;)Z"}, {"nme": "isAbstract", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "isAbstract", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Member;)Z"}, {"nme": "isStatic", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "isStatic", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Member;)Z"}, {"nme": "isNotStatic", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "isNotStatic", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Member;)Z"}, {"nme": "isFinal", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "MAINTAINED"], "since", "1.5"]}]}, {"nme": "isNotFinal", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "MAINTAINED"], "since", "1.5"]}]}, {"nme": "isFinal", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Member;)Z", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "MAINTAINED"], "since", "1.5"]}]}, {"nme": "isNotFinal", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Member;)Z", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "MAINTAINED"], "since", "1.5"]}]}], "flds": [], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "MAINTAINED"], "since", "1.4"]}]}, "org/junit/platform/commons/util/ClassUtils.class": {"ver": 52, "acc": 49, "nme": "org/junit/platform/commons/util/ClassUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "nullSafeToString", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/String;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Ljava/lang/String;"}, {"nme": "nullSafeToString", "acc": 137, "dsc": "([<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/String;", "sig": "([<PERSON><PERSON><PERSON>/lang/Class<*>;)Ljava/lang/String;"}, {"nme": "nullSafeToString", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Function;[Ljava/lang/Class;)Ljava/lang/String;", "sig": "(L<PERSON><PERSON>/util/function/Function<-Ljava/lang/Class<*>;+Ljava/lang/String;>;[Ljava/lang/Class<*>;)Ljava/lang/String;"}, {"nme": "lambda$nullSafeToString$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Function;Ljava/lang/Class;)Ljava/lang/String;"}], "flds": [], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.0"]}]}, "org/junit/platform/commons/PreconditionViolationException.class": {"ver": 52, "acc": 33, "nme": "org/junit/platform/commons/PreconditionViolationException", "super": "org/junit/platform/commons/util/PreconditionViolationException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "STABLE"], "since", "1.5"]}]}, "org/junit/platform/commons/annotation/Testable.class": {"ver": 52, "acc": 9729, "nme": "org/junit/platform/commons/annotation/Testable", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "RUNTIME"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Inherited;"}, {"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "STABLE"], "since", "1.0"]}]}, "org/junit/platform/commons/util/ExceptionUtils.class": {"ver": 52, "acc": 49, "nme": "org/junit/platform/commons/util/ExceptionUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "throwAsUncheckedException", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)<PERSON>ja<PERSON>/lang/RuntimeException;"}, {"nme": "throwAs", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)V", "sig": "<T:L<PERSON><PERSON>/lang/Throwable;>(Ljava/lang/Throwable;)V^TT;", "exs": ["java/lang/Throwable"]}, {"nme": "readStackTrace", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.0"]}]}, "org/junit/platform/commons/support/SearchOption.class": {"ver": 52, "acc": 16433, "nme": "org/junit/platform/commons/support/SearchOption", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lorg/junit/platform/commons/support/SearchOption;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;)Lorg/junit/platform/commons/support/SearchOption;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Lorg/junit/platform/commons/support/SearchOption;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "DEFAULT", "dsc": "Lorg/junit/platform/commons/support/SearchOption;"}, {"acc": 16409, "nme": "INCLUDE_ENCLOSING_CLASSES", "dsc": "Lorg/junit/platform/commons/support/SearchOption;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lorg/junit/platform/commons/support/SearchOption;"}], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "EXPERIMENTAL"], "since", "1.8"]}]}, "org/junit/platform/commons/util/ClassFileVisitor.class": {"ver": 52, "acc": 32, "nme": "org/junit/platform/commons/util/ClassFileVisitor", "super": "java/nio/file/SimpleFileVisitor", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/util/function/Consumer;)V", "sig": "(Ljava/util/function/Consumer<Ljava/nio/file/Path;>;)V"}, {"nme": "visitFile", "acc": 1, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/attribute/BasicFileAttributes;)Ljava/nio/file/FileVisitResult;"}, {"nme": "visitFileFailed", "acc": 1, "dsc": "(Ljava/nio/file/Path;Ljava/io/IOException;)Ljava/nio/file/FileVisitResult;"}, {"nme": "postVisitDirectory", "acc": 1, "dsc": "(Ljava/nio/file/Path;Ljava/io/IOException;)Ljava/nio/file/FileVisitResult;"}, {"nme": "isNotPackageInfo", "acc": 10, "dsc": "(Ljava/nio/file/Path;)Z"}, {"nme": "isNotModuleInfo", "acc": 10, "dsc": "(Ljava/nio/file/Path;)Z"}, {"nme": "isClassFile", "acc": 10, "dsc": "(Ljava/nio/file/Path;)Z"}, {"nme": "postVisitDirectory", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Ljava/io/IOException;)Ljava/nio/file/FileVisitResult;", "exs": ["java/io/IOException"]}, {"nme": "visitFileFailed", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Ljava/io/IOException;)Ljava/nio/file/FileVisitResult;", "exs": ["java/io/IOException"]}, {"nme": "visitFile", "acc": 4161, "dsc": "(Lja<PERSON>/lang/Object;Ljava/nio/file/attribute/BasicFileAttributes;)Ljava/nio/file/FileVisitResult;", "exs": ["java/io/IOException"]}, {"nme": "lambda$postVisitDirectory$1", "acc": 4106, "dsc": "(Lja<PERSON>/nio/file/Path;)Ljava/lang/String;"}, {"nme": "lambda$visitFileFailed$0", "acc": 4106, "dsc": "(Lja<PERSON>/nio/file/Path;)Ljava/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "logger", "dsc": "Lorg/junit/platform/commons/logging/Logger;"}, {"acc": 24, "nme": "CLASS_FILE_SUFFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": ".class"}, {"acc": 26, "nme": "PACKAGE_INFO_FILE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "package-info.class"}, {"acc": 26, "nme": "MODULE_INFO_FILE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "module-info.class"}, {"acc": 18, "nme": "classFileConsumer", "dsc": "Ljava/util/function/Consumer;", "sig": "Ljava/util/function/Consumer<Ljava/nio/file/Path;>;"}]}, "org/junit/platform/commons/util/FunctionUtils.class": {"ver": 52, "acc": 49, "nme": "org/junit/platform/commons/util/FunctionUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "where", "acc": 9, "dsc": "(Lja<PERSON>/util/function/Function;Ljava/util/function/Predicate;)Ljava/util/function/Predicate;", "sig": "<T:Ljava/lang/Object;V:Ljava/lang/Object;>(Ljava/util/function/Function<TT;TV;>;Ljava/util/function/Predicate<-TV;>;)Ljava/util/function/Predicate<TT;>;"}, {"nme": "lambda$where$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Predicate;Ljava/util/function/Function;Ljava/lang/Object;)Z"}], "flds": [], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.0"]}]}, "org/junit/platform/commons/util/AnnotationUtils.class": {"ver": 52, "acc": 49, "nme": "org/junit/platform/commons/util/AnnotationUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "isAnnotated", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Optional;Lja<PERSON>/lang/Class;)Z", "sig": "(L<PERSON><PERSON>/util/Optional<+Ljava/lang/reflect/AnnotatedElement;>;Ljava/lang/Class<+Ljava/lang/annotation/Annotation;>;)Z"}, {"nme": "isAnnotated", "acc": 9, "dsc": "(Lja<PERSON>/lang/reflect/Parameter;ILjava/lang/Class;)Z", "sig": "(Lja<PERSON>/lang/reflect/Parameter;ILjava/lang/Class<+Ljava/lang/annotation/Annotation;>;)Z"}, {"nme": "isAnnotated", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/AnnotatedElement;Ljava/lang/Class;)Z", "sig": "(L<PERSON><PERSON>/lang/reflect/AnnotatedElement;Ljava/lang/Class<+Ljava/lang/annotation/Annotation;>;)Z"}, {"nme": "findAnnotation", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Optional;L<PERSON><PERSON>/lang/Class;)Ljava/util/Optional;", "sig": "<A::Ljava/lang/annotation/Annotation;>(Ljava/util/Optional<+Ljava/lang/reflect/AnnotatedElement;>;Ljava/lang/Class<TA;>;)Ljava/util/Optional<TA;>;"}, {"nme": "findAnnotation", "acc": 9, "dsc": "(Lja<PERSON>/lang/reflect/Parameter;ILjava/lang/Class;)Ljava/util/Optional;", "sig": "<A::Ljava/lang/annotation/Annotation;>(Ljava/lang/reflect/Parameter;ILjava/lang/Class<TA;>;)Ljava/util/Optional<TA;>;"}, {"nme": "findAnnotation", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/AnnotatedElement;Ljava/lang/Class;)Ljava/util/Optional;", "sig": "<A::Ljava/lang/annotation/Annotation;>(Ljava/lang/reflect/AnnotatedElement;Ljava/lang/Class<TA;>;)Ljava/util/Optional<TA;>;"}, {"nme": "findAnnotation", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/AnnotatedElement;Ljava/lang/Class;Z<PERSON>java/util/Set;)Ljava/util/Optional;", "sig": "<A::Ljava/lang/annotation/Annotation;>(Ljava/lang/reflect/AnnotatedElement;Ljava/lang/Class<TA;>;ZLjava/util/Set<Ljava/lang/annotation/Annotation;>;)Ljava/util/Optional<TA;>;"}, {"nme": "findMetaAnnotation", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;[<PERSON><PERSON><PERSON>/lang/annotation/Annotation;Z<PERSON><PERSON><PERSON>/util/Set;)Ljava/util/Optional;", "sig": "<A::Ljava/lang/annotation/Annotation;>(Ljava/lang/Class<TA;>;[Ljava/lang/annotation/Annotation;ZLjava/util/Set<Ljava/lang/annotation/Annotation;>;)Ljava/util/Optional<TA;>;"}, {"nme": "findAnnotation", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/Class;Z)Ljava/util/Optional;", "sig": "<A::Ljava/lang/annotation/Annotation;>(Ljava/lang/Class<*>;Ljava/lang/Class<TA;>;Z)Ljava/util/Optional<TA;>;"}, {"nme": "findRepeatableAnnotations", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Optional;L<PERSON><PERSON>/lang/Class;)Ljava/util/List;", "sig": "<A::Ljava/lang/annotation/Annotation;>(Ljava/util/Optional<+Ljava/lang/reflect/AnnotatedElement;>;Ljava/lang/Class<TA;>;)Ljava/util/List<TA;>;"}, {"nme": "findRepeatableAnnotations", "acc": 9, "dsc": "(Lja<PERSON>/lang/reflect/Parameter;ILjava/lang/Class;)Ljava/util/List;", "sig": "<A::Ljava/lang/annotation/Annotation;>(Ljava/lang/reflect/Parameter;ILjava/lang/Class<TA;>;)Ljava/util/List<TA;>;"}, {"nme": "findRepeatableAnnotations", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/AnnotatedElement;Ljava/lang/Class;)Ljava/util/List;", "sig": "<A::Ljava/lang/annotation/Annotation;>(Ljava/lang/reflect/AnnotatedElement;Ljava/lang/Class<TA;>;)Ljava/util/List<TA;>;"}, {"nme": "findRepeatableAnnotations", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/AnnotatedElement;Ljava/lang/Class;Ljava/lang/Class;Z<PERSON>java/util/Set;<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "<A::Ljava/lang/annotation/Annotation;>(Ljava/lang/reflect/AnnotatedElement;Ljava/lang/Class<TA;>;Ljava/lang/Class<+Ljava/lang/annotation/Annotation;>;ZLjava/util/Set<TA;>;Ljava/util/Set<Ljava/lang/annotation/Annotation;>;)V"}, {"nme": "findRepeatableAnnotations", "acc": 10, "dsc": "([<PERSON><PERSON><PERSON>/lang/annotation/Annotation;Lja<PERSON>/lang/Class;Ljava/lang/Class;Z<PERSON>java/util/Set;<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "<A::Ljava/lang/annotation/Annotation;>([Ljava/lang/annotation/Annotation;Ljava/lang/Class<TA;>;Ljava/lang/Class<+Ljava/lang/annotation/Annotation;>;ZLjava/util/Set<TA;>;Ljava/util/Set<Ljava/lang/annotation/Annotation;>;)V"}, {"nme": "isRepeatableAnnotationContainer", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<+Ljava/lang/annotation/Annotation;>;)Z"}, {"nme": "getEffectiveAnnotatedParameter", "acc": 10, "dsc": "(Lja<PERSON>/lang/reflect/Parameter;I)Ljava/lang/reflect/AnnotatedElement;"}, {"nme": "findPublicAnnotatedFields", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;L<PERSON>va/lang/Class;Ljava/lang/Class;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Class<*>;Ljava/lang/Class<+Ljava/lang/annotation/Annotation;>;)Ljava/util/List<Ljava/lang/reflect/Field;>;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "acc": 9, "dsc": "(L<PERSON><PERSON>/lang/Class;L<PERSON><PERSON>/lang/Class;Lja<PERSON>/util/function/Predicate;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Class<+Ljava/lang/annotation/Annotation;>;Ljava/util/function/Predicate<Ljava/lang/reflect/Field;>;)Ljava/util/List<Ljava/lang/reflect/Field;>;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "acc": 9, "dsc": "(Ljava/lang/Class;Ljava/lang/Class;Ljava/util/function/Predicate;Lorg/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Class<+Ljava/lang/annotation/Annotation;>;Ljava/util/function/Predicate<Ljava/lang/reflect/Field;>;Lorg/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode;)Ljava/util/List<Ljava/lang/reflect/Field;>;"}, {"nme": "findAnnotatedMethods", "acc": 9, "dsc": "(Ljava/lang/Class;Ljava/lang/Class;Lorg/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Class<+Ljava/lang/annotation/Annotation;>;Lorg/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode;)Ljava/util/List<Ljava/lang/reflect/Method;>;"}, {"nme": "isInJavaLangAnnotationPackage", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<+Ljava/lang/annotation/Annotation;>;)Z"}, {"nme": "lambda$findAnnotatedMethods$7", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/reflect/Method;)Z"}, {"nme": "lambda$findAnnotatedFields$6", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/reflect/Field;)Z"}, {"nme": "lambda$findPublicAnnotatedFields$5", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/Class;Lja<PERSON>/lang/reflect/Field;)Z"}, {"nme": "lambda$isRepeatableAnnotationContainer$4", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON><PERSON><PERSON>/lang/Boolean;"}, {"nme": "lambda$isRepeatableAnnotationContainer$3", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)Ljava/lang/annotation/Repeatable;"}, {"nme": "lambda$isRepeatableAnnotationContainer$2", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)Z"}, {"nme": "lambda$findRepeatableAnnotations$1", "acc": 4106, "dsc": "(Lja<PERSON>/lang/Class;Ljava/lang/Class;Ljava/lang/Exception;)Lorg/junit/platform/commons/JUnitException;"}, {"nme": "lambda$findRepeatableAnnotations$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "repeatableAnnotationContainerCache", "dsc": "Ljava/util/concurrent/ConcurrentHashMap;", "sig": "Ljava/util/concurrent/ConcurrentHashMap<Ljava/lang/Class<+Ljava/lang/annotation/Annotation;>;Ljava/lang/Boolean;>;"}], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.0"]}]}, "org/junit/platform/commons/function/Try$Failure.class": {"ver": 52, "acc": 32, "nme": "org/junit/platform/commons/function/Try$Failure", "super": "org/junit/platform/commons/function/Try", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Exception;)V"}, {"nme": "andThenTry", "acc": 1, "dsc": "(Lorg/junit/platform/commons/function/Try$Transformer;)Lorg/junit/platform/commons/function/Try;", "sig": "<U:Ljava/lang/Object;>(Lorg/junit/platform/commons/function/Try$Transformer<TV;TU;>;)Lorg/junit/platform/commons/function/Try<TU;>;"}, {"nme": "and<PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Function;)Lorg/junit/platform/commons/function/Try;", "sig": "<U:Ljava/lang/Object;>(Ljava/util/function/Function<TV;Lorg/junit/platform/commons/function/Try<TU;>;>;)Lorg/junit/platform/commons/function/Try<TU;>;"}, {"nme": "uncheckedCast", "acc": 2, "dsc": "()Lorg/junit/platform/commons/function/Try;", "sig": "<U:Ljava/lang/Object;>()Lorg/junit/platform/commons/function/Try<TU;>;"}, {"nme": "orElseTry", "acc": 1, "dsc": "(<PERSON>java/util/concurrent/Callable;)Lorg/junit/platform/commons/function/Try;", "sig": "(Ljava/util/concurrent/Callable<TV;>;)Lorg/junit/platform/commons/function/Try<TV;>;"}, {"nme": "orElse", "acc": 1, "dsc": "(Ljava/util/function/Supplier;)Lorg/junit/platform/commons/function/Try;", "sig": "(Ljava/util/function/Supplier<Lorg/junit/platform/commons/function/Try<TV;>;>;)Lorg/junit/platform/commons/function/Try<TV;>;"}, {"nme": "get", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TV;", "exs": ["java/lang/Exception"]}, {"nme": "getOrThrow", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Function;)Ljava/lang/Object;", "sig": "<E:Ljava/lang/Exception;>(Ljava/util/function/Function<-Ljava/lang/Exception;TE;>;)TV;^TE;", "exs": ["java/lang/Exception"]}, {"nme": "ifSuccess", "acc": 1, "dsc": "(Ljava/util/function/Consumer;)Lorg/junit/platform/commons/function/Try;", "sig": "(Ljava/util/function/Consumer<TV;>;)Lorg/junit/platform/commons/function/Try<TV;>;"}, {"nme": "ifFailure", "acc": 1, "dsc": "(Ljava/util/function/Consumer;)Lorg/junit/platform/commons/function/Try;", "sig": "(Ljava/util/function/Consumer<Ljava/lang/Exception;>;)Lorg/junit/platform/commons/function/Try<TV;>;"}, {"nme": "toOptional", "acc": 1, "dsc": "()Ljava/util/Optional;", "sig": "()Ljava/util/Optional<TV;>;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "cause", "dsc": "<PERSON><PERSON><PERSON>/lang/Exception;"}]}, "META-INF/versions/9/org/junit/platform/commons/util/ModuleUtils.class": {"ver": 53, "acc": 33, "nme": "org/junit/platform/commons/util/ModuleUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "findAllNonSystemBootModuleNames", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "isJavaPlatformModuleSystemAvailable", "acc": 9, "dsc": "()Z"}, {"nme": "getModuleName", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/util/Optional;", "sig": "(Ljava/lang/Class<*>;)Ljava/util/Optional<Ljava/lang/String;>;"}, {"nme": "getModuleVersion", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/util/Optional;", "sig": "(Ljava/lang/Class<*>;)Ljava/util/Optional<Ljava/lang/String;>;"}, {"nme": "findAllClassesInModule", "acc": 9, "dsc": "(Ljava/lang/String;Lorg/junit/platform/commons/util/ClassFilter;)Ljava/util/List;", "sig": "(Ljava/lang/String;Lorg/junit/platform/commons/util/ClassFilter;)Ljava/util/List<Ljava/lang/Class<*>;>;"}, {"nme": "streamResolvedModules", "acc": 10, "dsc": "(Lja<PERSON>/util/function/Predicate;)Ljava/util/stream/Stream;", "sig": "(Ljava/util/function/Predicate<Ljava/lang/String;>;)Ljava/util/stream/Stream<Ljava/lang/module/ResolvedModule;>;"}, {"nme": "streamResolvedModules", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Predicate;<PERSON><PERSON><PERSON>/lang/Mo<PERSON>leLayer;)Ljava/util/stream/Stream;", "sig": "(Ljava/util/function/Predicate<Ljava/lang/String;>;Ljava/lang/ModuleLayer;)Ljava/util/stream/Stream<Ljava/lang/module/ResolvedModule;>;"}, {"nme": "scan", "acc": 10, "dsc": "(Ljava/util/Set;Lorg/junit/platform/commons/util/ClassFilter;Lja<PERSON>/lang/ClassLoader;)Ljava/util/List;", "sig": "(Ljava/util/Set<Ljava/lang/module/ModuleReference;>;Lorg/junit/platform/commons/util/ClassFilter;Ljava/lang/ClassLoader;)Ljava/util/List<Ljava/lang/Class<*>;>;"}, {"nme": "lambda$scan$8", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Ljava/lang/String;"}, {"nme": "lambda$scan$7", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)<PERSON>ja<PERSON>/lang/String;"}, {"nme": "lambda$streamResolvedModules$6", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Predicate;Ljava/lang/module/ResolvedModule;)Z"}, {"nme": "lambda$streamResolvedModules$5", "acc": 4106, "dsc": "(Lja<PERSON>/lang/module/Configuration;)Ljava/lang/String;"}, {"nme": "lambda$streamResolvedModules$4", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Mo<PERSON>leLayer;)<PERSON>ja<PERSON>/lang/String;"}, {"nme": "lambda$streamResolvedModules$3", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Module;)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$findAllClassesInModule$2", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "lambda$findAllNonSystemBootModuleNames$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "lambda$findAllNonSystemBootModuleNames$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/module/ModuleReference;)Ljava/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "logger", "dsc": "Lorg/junit/platform/commons/logging/Logger;"}], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.1"]}]}, "org/junit/platform/commons/function/Try$Success.class": {"ver": 52, "acc": 32, "nme": "org/junit/platform/commons/function/Try$Success", "super": "org/junit/platform/commons/function/Try", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TV;)V"}, {"nme": "andThenTry", "acc": 1, "dsc": "(Lorg/junit/platform/commons/function/Try$Transformer;)Lorg/junit/platform/commons/function/Try;", "sig": "<U:Ljava/lang/Object;>(Lorg/junit/platform/commons/function/Try$Transformer<TV;TU;>;)Lorg/junit/platform/commons/function/Try<TU;>;"}, {"nme": "and<PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Function;)Lorg/junit/platform/commons/function/Try;", "sig": "<U:Ljava/lang/Object;>(Ljava/util/function/Function<TV;Lorg/junit/platform/commons/function/Try<TU;>;>;)Lorg/junit/platform/commons/function/Try<TU;>;"}, {"nme": "orElseTry", "acc": 1, "dsc": "(<PERSON>java/util/concurrent/Callable;)Lorg/junit/platform/commons/function/Try;", "sig": "(Ljava/util/concurrent/Callable<TV;>;)Lorg/junit/platform/commons/function/Try<TV;>;"}, {"nme": "orElse", "acc": 1, "dsc": "(Ljava/util/function/Supplier;)Lorg/junit/platform/commons/function/Try;", "sig": "(Ljava/util/function/Supplier<Lorg/junit/platform/commons/function/Try<TV;>;>;)Lorg/junit/platform/commons/function/Try<TV;>;"}, {"nme": "get", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TV;"}, {"nme": "getOrThrow", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Function;)Ljava/lang/Object;", "sig": "<E:Ljava/lang/Exception;>(Ljava/util/function/Function<-Ljava/lang/Exception;TE;>;)TV;"}, {"nme": "ifSuccess", "acc": 1, "dsc": "(Ljava/util/function/Consumer;)Lorg/junit/platform/commons/function/Try;", "sig": "(Ljava/util/function/Consumer<TV;>;)Lorg/junit/platform/commons/function/Try<TV;>;"}, {"nme": "ifFailure", "acc": 1, "dsc": "(Ljava/util/function/Consumer;)Lorg/junit/platform/commons/function/Try;", "sig": "(Ljava/util/function/Consumer<Ljava/lang/Exception;>;)Lorg/junit/platform/commons/function/Try<TV;>;"}, {"nme": "toOptional", "acc": 1, "dsc": "()Ljava/util/Optional;", "sig": "()Ljava/util/Optional<TV;>;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "lambda$andThen$1", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Function;)Lorg/junit/platform/commons/function/Try;", "exs": ["java/lang/Exception"]}, {"nme": "lambda$andThenTry$0", "acc": 4098, "dsc": "(Lorg/junit/platform/commons/function/Try$Transformer;)Ljava/lang/Object;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 18, "nme": "value", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;", "sig": "TV;"}]}, "org/junit/platform/commons/util/RuntimeUtils.class": {"ver": 52, "acc": 49, "nme": "org/junit/platform/commons/util/RuntimeUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "isDebugMode", "acc": 9, "dsc": "()Z"}, {"nme": "getInputArguments", "acc": 8, "dsc": "()Ljava/util/Optional;", "sig": "()Ljava/util/Optional<Ljava/util/List<Ljava/lang/String;>;>;"}, {"nme": "lambda$isDebugMode$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)<PERSON><PERSON><PERSON>/lang/<PERSON>an;"}, {"nme": "lambda$isDebugMode$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}], "flds": [], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.6"]}]}, "org/junit/platform/commons/util/CloseablePath$ManagedFileSystem.class": {"ver": 52, "acc": 32, "nme": "org/junit/platform/commons/util/CloseablePath$ManagedFileSystem", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/net/URI;)V"}, {"nme": "retain", "acc": 2, "dsc": "()Lorg/junit/platform/commons/util/CloseablePath$ManagedFileSystem;"}, {"nme": "release", "acc": 2, "dsc": "()Lorg/junit/platform/commons/util/CloseablePath$ManagedFileSystem;"}, {"nme": "close", "acc": 2, "dsc": "()V"}, {"nme": "access$000", "acc": 4104, "dsc": "(Lorg/junit/platform/commons/util/CloseablePath$ManagedFileSystem;)Ljava/nio/file/FileSystem;"}, {"nme": "access$100", "acc": 4104, "dsc": "(Lorg/junit/platform/commons/util/CloseablePath$ManagedFileSystem;)Lorg/junit/platform/commons/util/CloseablePath$ManagedFileSystem;"}, {"nme": "access$200", "acc": 4104, "dsc": "(Lorg/junit/platform/commons/util/CloseablePath$ManagedFileSystem;)Lorg/junit/platform/commons/util/CloseablePath$ManagedFileSystem;"}], "flds": [{"acc": 18, "nme": "referenceCount", "dsc": "Ljava/util/concurrent/atomic/AtomicInteger;"}, {"acc": 18, "nme": "fileSystem", "dsc": "Ljava/nio/file/FileSystem;"}, {"acc": 18, "nme": "<PERSON><PERSON><PERSON>", "dsc": "Ljava/net/URI;"}]}, "org/junit/platform/commons/util/CollectionUtils.class": {"ver": 52, "acc": 49, "nme": "org/junit/platform/commons/util/CollectionUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getOnlyElement", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/util/Collection<TT;>;)TT;"}, {"nme": "toSet", "acc": 9, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/util/Set;", "sig": "<T:Ljava/lang/Object;>([TT;)Ljava/util/Set<TT;>;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.6"]}]}, {"nme": "toUnmodifiableList", "acc": 9, "dsc": "()Ljava/util/stream/Collector;", "sig": "<T:Ljava/lang/Object;>()Ljava/util/stream/Collector<TT;*Ljava/util/List<TT;>;>;"}, {"nme": "isConvertibleToStream", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.9.1"]}]}, {"nme": "toStream", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Ljava/util/stream/Stream;", "sig": "(<PERSON><PERSON><PERSON>/lang/Object;)Ljava/util/stream/Stream<*>;"}, {"nme": "lambda$toStream$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;I)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "lambda$getOnlyElement$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Ljava/lang/String;"}], "flds": [], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.0"]}]}, "org/junit/platform/commons/function/Try.class": {"ver": 52, "acc": 1057, "nme": "org/junit/platform/commons/function/Try", "super": "java/lang/Object", "mthds": [{"nme": "call", "acc": 9, "dsc": "(<PERSON>java/util/concurrent/Callable;)Lorg/junit/platform/commons/function/Try;", "sig": "<V:Ljava/lang/Object;>(Ljava/util/concurrent/Callable<TV;>;)Lorg/junit/platform/commons/function/Try<TV;>;"}, {"nme": "success", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/junit/platform/commons/function/Try;", "sig": "<V:Ljava/lang/Object;>(TV;)Lorg/junit/platform/commons/function/Try<TV;>;"}, {"nme": "failure", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Exception;)Lorg/junit/platform/commons/function/Try;", "sig": "<V:Ljava/lang/Object;>(Ljava/lang/Exception;)Lorg/junit/platform/commons/function/Try<TV;>;"}, {"nme": "checkNotNull", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(TT;Ljava/lang/String;)TT;"}, {"nme": "of", "acc": 10, "dsc": "(<PERSON>java/util/concurrent/Callable;)Lorg/junit/platform/commons/function/Try;", "sig": "<V:Ljava/lang/Object;>(Ljava/util/concurrent/Callable<Lorg/junit/platform/commons/function/Try<TV;>;>;)Lorg/junit/platform/commons/function/Try<TV;>;"}, {"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "andThenTry", "acc": 1025, "dsc": "(Lorg/junit/platform/commons/function/Try$Transformer;)Lorg/junit/platform/commons/function/Try;", "sig": "<U:Ljava/lang/Object;>(Lorg/junit/platform/commons/function/Try$Transformer<TV;TU;>;)Lorg/junit/platform/commons/function/Try<TU;>;"}, {"nme": "and<PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Function;)Lorg/junit/platform/commons/function/Try;", "sig": "<U:Ljava/lang/Object;>(Ljava/util/function/Function<TV;Lorg/junit/platform/commons/function/Try<TU;>;>;)Lorg/junit/platform/commons/function/Try<TU;>;"}, {"nme": "orElseTry", "acc": 1025, "dsc": "(<PERSON>java/util/concurrent/Callable;)Lorg/junit/platform/commons/function/Try;", "sig": "(Ljava/util/concurrent/Callable<TV;>;)Lorg/junit/platform/commons/function/Try<TV;>;"}, {"nme": "orElse", "acc": 1025, "dsc": "(Ljava/util/function/Supplier;)Lorg/junit/platform/commons/function/Try;", "sig": "(Ljava/util/function/Supplier<Lorg/junit/platform/commons/function/Try<TV;>;>;)Lorg/junit/platform/commons/function/Try<TV;>;"}, {"nme": "get", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TV;", "exs": ["java/lang/Exception"]}, {"nme": "getOrThrow", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Function;)Ljava/lang/Object;", "sig": "<E:Ljava/lang/Exception;>(Ljava/util/function/Function<-Ljava/lang/Exception;TE;>;)TV;^TE;", "exs": ["java/lang/Exception"]}, {"nme": "ifSuccess", "acc": 1025, "dsc": "(Ljava/util/function/Consumer;)Lorg/junit/platform/commons/function/Try;", "sig": "(Ljava/util/function/Consumer<TV;>;)Lorg/junit/platform/commons/function/Try<TV;>;"}, {"nme": "ifFailure", "acc": 1025, "dsc": "(Ljava/util/function/Consumer;)Lorg/junit/platform/commons/function/Try;", "sig": "(Ljava/util/function/Consumer<Ljava/lang/Exception;>;)Lorg/junit/platform/commons/function/Try<TV;>;"}, {"nme": "toOptional", "acc": 1025, "dsc": "()Ljava/util/Optional;", "sig": "()Ljava/util/Optional<TV;>;"}, {"nme": "lambda$call$0", "acc": 4106, "dsc": "(<PERSON>java/util/concurrent/Callable;)Lorg/junit/platform/commons/function/Try;", "exs": ["java/lang/Exception"]}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/junit/platform/commons/function/Try$1;)V"}, {"nme": "access$100", "acc": 4104, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "access$200", "acc": 4104, "dsc": "(<PERSON>java/util/concurrent/Callable;)Lorg/junit/platform/commons/function/Try;"}], "flds": [], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "MAINTAINED"], "since", "1.4"]}]}, "META-INF/versions/9/org/junit/platform/commons/util/ModuleUtils$ModuleReferenceScanner.class": {"ver": 53, "acc": 32, "nme": "org/junit/platform/commons/util/ModuleUtils$ModuleReferenceScanner", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/junit/platform/commons/util/ClassFilter;<PERSON>java/lang/ClassLoader;)V"}, {"nme": "scan", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/module/ModuleReference;)Ljava/util/List;", "sig": "(Ljava/lang/module/ModuleReference;)Ljava/util/List<Ljava/lang/Class<*>;>;"}, {"nme": "className", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "loadClassUnchecked", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class<*>;"}, {"nme": "lambda$scan$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "lambda$scan$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}], "flds": [{"acc": 18, "nme": "classFilter", "dsc": "Lorg/junit/platform/commons/util/ClassFilter;"}, {"acc": 18, "nme": "classLoader", "dsc": "<PERSON><PERSON><PERSON>/lang/ClassLoader;"}]}, "org/junit/platform/commons/util/ClasspathScanner.class": {"ver": 52, "acc": 32, "nme": "org/junit/platform/commons/util/ClasspathScanner", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lja<PERSON>/util/function/Supplier;Ljava/util/function/BiFunction;)V", "sig": "(Ljava/util/function/Supplier<Ljava/lang/ClassLoader;>;Ljava/util/function/BiFunction<Ljava/lang/String;Ljava/lang/ClassLoader;Lorg/junit/platform/commons/function/Try<Ljava/lang/Class<*>;>;>;)V"}, {"nme": "scanForClassesInPackage", "acc": 0, "dsc": "(Ljava/lang/String;Lorg/junit/platform/commons/util/ClassFilter;)Ljava/util/List;", "sig": "(Ljava/lang/String;Lorg/junit/platform/commons/util/ClassFilter;)Ljava/util/List<Ljava/lang/Class<*>;>;"}, {"nme": "scanForClassesInClasspathRoot", "acc": 0, "dsc": "(Ljava/net/URI;Lorg/junit/platform/commons/util/ClassFilter;)Ljava/util/List;", "sig": "(Ljava/net/URI;Lorg/junit/platform/commons/util/ClassFilter;)Ljava/util/List<Ljava/lang/Class<*>;>;"}, {"nme": "findClassesForUris", "acc": 2, "dsc": "(Ljava/util/List;Ljava/lang/String;Lorg/junit/platform/commons/util/ClassFilter;)Ljava/util/List;", "sig": "(Ljava/util/List<Ljava/net/URI;>;Ljava/lang/String;Lorg/junit/platform/commons/util/ClassFilter;)Ljava/util/List<Ljava/lang/Class<*>;>;"}, {"nme": "findClassesForUri", "acc": 2, "dsc": "(Ljava/net/URI;Ljava/lang/String;Lorg/junit/platform/commons/util/ClassFilter;)Ljava/util/List;", "sig": "(Ljava/net/URI;Ljava/lang/String;Lorg/junit/platform/commons/util/ClassFilter;)Ljava/util/List<Ljava/lang/Class<*>;>;"}, {"nme": "findClassesForPath", "acc": 2, "dsc": "(Ljava/nio/file/Path;Ljava/lang/String;Lorg/junit/platform/commons/util/ClassFilter;)Ljava/util/List;", "sig": "(Ljava/nio/file/Path;Ljava/lang/String;Lorg/junit/platform/commons/util/ClassFilter;)Ljava/util/List<Ljava/lang/Class<*>;>;"}, {"nme": "processClassFileSafely", "acc": 2, "dsc": "(Ljava/nio/file/Path;Ljava/lang/String;Lorg/junit/platform/commons/util/ClassFilter;Ljava/nio/file/Path;Ljava/util/function/Consumer;)V", "sig": "(Ljava/nio/file/Path;Ljava/lang/String;Lorg/junit/platform/commons/util/ClassFilter;Ljava/nio/file/Path;Ljava/util/function/Consumer<Ljava/lang/Class<*>;>;)V"}, {"nme": "determineFullyQualifiedClassName", "acc": 2, "dsc": "(Lja<PERSON>/nio/file/Path;Ljava/lang/String;Ljava/nio/file/Path;)Ljava/lang/String;"}, {"nme": "determineSimpleClassName", "acc": 2, "dsc": "(Lja<PERSON>/nio/file/Path;)Ljava/lang/String;"}, {"nme": "determineSubpackageName", "acc": 2, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/Path;)Ljava/lang/String;"}, {"nme": "handleInternalError", "acc": 2, "dsc": "(L<PERSON><PERSON>/nio/file/Path;Ljava/lang/String;Ljava/lang/InternalError;)V"}, {"nme": "handleThrowable", "acc": 2, "dsc": "(Lja<PERSON>/nio/file/Path;Ljava/lang/Throwable;)V"}, {"nme": "logMalformedClassName", "acc": 2, "dsc": "(L<PERSON><PERSON>/nio/file/Path;Ljava/lang/String;Ljava/lang/InternalError;)V"}, {"nme": "logGenericFileProcessingException", "acc": 2, "dsc": "(Lja<PERSON>/nio/file/Path;Ljava/lang/Throwable;)V"}, {"nme": "getClassLoader", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/ClassLoader;"}, {"nme": "getRootUrisForPackageNameOnClassPathAndModulePath", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/List<Ljava/net/URI;>;"}, {"nme": "removeTrailingClasspathResourcePathSeparator", "acc": 10, "dsc": "(Ljava/net/URI;)Ljava/net/URI;"}, {"nme": "packagePath", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getRootUrisForPackage", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/List<Ljava/net/URI;>;"}, {"nme": "lambda$getRootUrisForPackage$8", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "lambda$logGenericFileProcessingException$7", "acc": 4106, "dsc": "(Lja<PERSON>/nio/file/Path;)Ljava/lang/String;"}, {"nme": "lambda$logMalformedClassName$6", "acc": 4106, "dsc": "(Lja<PERSON>/nio/file/Path;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "lambda$determineFullyQualifiedClassName$5", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "lambda$findClassesForPath$4", "acc": 4106, "dsc": "(Lja<PERSON>/nio/file/Path;)Ljava/lang/String;"}, {"nme": "lambda$findClassesForPath$3", "acc": 4098, "dsc": "(Ljava/nio/file/Path;Ljava/lang/String;Lorg/junit/platform/commons/util/ClassFilter;Ljava/util/List;Ljava/nio/file/Path;)V"}, {"nme": "lambda$findClassesForPath$2", "acc": 4106, "dsc": "(Lja<PERSON>/nio/file/Path;)Ljava/lang/String;"}, {"nme": "lambda$findClassesForUri$1", "acc": 4106, "dsc": "(Ljava/net/URI;)Ljava/lang/String;"}, {"nme": "lambda$findClassesForUris$0", "acc": 4098, "dsc": "(Ljava/lang/String;Lorg/junit/platform/commons/util/ClassFilter;Ljava/net/URI;)Ljava/util/List;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "logger", "dsc": "Lorg/junit/platform/commons/logging/Logger;"}, {"acc": 26, "nme": "CLASSPATH_RESOURCE_PATH_SEPARATOR", "dsc": "C", "val": 47}, {"acc": 26, "nme": "CLASSPATH_RESOURCE_PATH_SEPARATOR_STRING", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "PACKAGE_SEPARATOR_CHAR", "dsc": "C", "val": 46}, {"acc": 26, "nme": "PACKAGE_SEPARATOR_STRING", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "MALFORMED_CLASS_NAME_ERROR_MESSAGE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "Malformed class name"}, {"acc": 18, "nme": "classLoaderSupplier", "dsc": "Ljava/util/function/Supplier;", "sig": "Ljava/util/function/Supplier<Ljava/lang/ClassLoader;>;"}, {"acc": 18, "nme": "loadClass", "dsc": "Lja<PERSON>/util/function/BiFunction;", "sig": "Ljava/util/function/BiFunction<Ljava/lang/String;Ljava/lang/ClassLoader;Lorg/junit/platform/commons/function/Try<Ljava/lang/Class<*>;>;>;"}]}, "org/junit/platform/commons/util/ClassFilter.class": {"ver": 52, "acc": 33, "nme": "org/junit/platform/commons/util/ClassFilter", "super": "java/lang/Object", "mthds": [{"nme": "of", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Predicate;)Lorg/junit/platform/commons/util/ClassFilter;", "sig": "(Ljava/util/function/Predicate<Ljava/lang/Class<*>;>;)Lorg/junit/platform/commons/util/ClassFilter;"}, {"nme": "of", "acc": 9, "dsc": "(Lja<PERSON>/util/function/Predicate;Ljava/util/function/Predicate;)Lorg/junit/platform/commons/util/ClassFilter;", "sig": "(Ljava/util/function/Predicate<Ljava/lang/String;>;Ljava/util/function/Predicate<Ljava/lang/Class<*>;>;)Lorg/junit/platform/commons/util/ClassFilter;"}, {"nme": "<init>", "acc": 2, "dsc": "(L<PERSON><PERSON>/util/function/Predicate;Ljava/util/function/Predicate;)V", "sig": "(Lja<PERSON>/util/function/Predicate<Ljava/lang/String;>;Ljava/util/function/Predicate<Ljava/lang/Class<*>;>;)V"}, {"nme": "match", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "match", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "test", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "test", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "lambda$of$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}], "flds": [{"acc": 18, "nme": "namePredicate", "dsc": "<PERSON><PERSON><PERSON>/util/function/Predicate;", "sig": "Ljava/util/function/Predicate<Ljava/lang/String;>;"}, {"acc": 18, "nme": "classPredicate", "dsc": "<PERSON><PERSON><PERSON>/util/function/Predicate;", "sig": "Ljava/util/function/Predicate<Ljava/lang/Class<*>;>;"}], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.1"]}]}, "org/junit/platform/commons/util/PackageUtils.class": {"ver": 52, "acc": 49, "nme": "org/junit/platform/commons/util/PackageUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getAttribute", "acc": 9, "dsc": "(L<PERSON><PERSON>/lang/Class;Ljava/util/function/Function;)Ljava/util/Optional;", "sig": "(Ljava/lang/Class<*>;Ljava/util/function/Function<Ljava/lang/Package;Ljava/lang/String;>;)Ljava/util/Optional<Ljava/lang/String;>;"}, {"nme": "getAttribute", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;L<PERSON><PERSON>/lang/String;)Ljava/util/Optional;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/String;)Ljava/util/Optional<Ljava/lang/String;>;"}], "flds": [{"acc": 24, "nme": "DEFAULT_PACKAGE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": ""}], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.0"]}]}, "org/junit/platform/commons/function/Try$Transformer.class": {"ver": 52, "acc": 1537, "nme": "org/junit/platform/commons/function/Try$Transformer", "super": "java/lang/Object", "mthds": [{"nme": "apply", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(TS;)TT;", "exs": ["java/lang/Exception"]}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/FunctionalInterface;"}]}, "org/junit/platform/commons/logging/Logger.class": {"ver": 52, "acc": 1537, "nme": "org/junit/platform/commons/logging/Logger", "super": "java/lang/Object", "mthds": [{"nme": "error", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Supplier;)V", "sig": "(Lja<PERSON>/util/function/Supplier<Ljava/lang/String;>;)V"}, {"nme": "error", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;Lja<PERSON>/util/function/Supplier;)V", "sig": "(L<PERSON><PERSON>/lang/Throwable;Ljava/util/function/Supplier<Ljava/lang/String;>;)V"}, {"nme": "warn", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Supplier;)V", "sig": "(Lja<PERSON>/util/function/Supplier<Ljava/lang/String;>;)V"}, {"nme": "warn", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;Lja<PERSON>/util/function/Supplier;)V", "sig": "(L<PERSON><PERSON>/lang/Throwable;Ljava/util/function/Supplier<Ljava/lang/String;>;)V"}, {"nme": "info", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Supplier;)V", "sig": "(Lja<PERSON>/util/function/Supplier<Ljava/lang/String;>;)V"}, {"nme": "info", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;Lja<PERSON>/util/function/Supplier;)V", "sig": "(L<PERSON><PERSON>/lang/Throwable;Ljava/util/function/Supplier<Ljava/lang/String;>;)V"}, {"nme": "config", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Supplier;)V", "sig": "(Lja<PERSON>/util/function/Supplier<Ljava/lang/String;>;)V"}, {"nme": "config", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;Lja<PERSON>/util/function/Supplier;)V", "sig": "(L<PERSON><PERSON>/lang/Throwable;Ljava/util/function/Supplier<Ljava/lang/String;>;)V"}, {"nme": "debug", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Supplier;)V", "sig": "(Lja<PERSON>/util/function/Supplier<Ljava/lang/String;>;)V"}, {"nme": "debug", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;Lja<PERSON>/util/function/Supplier;)V", "sig": "(L<PERSON><PERSON>/lang/Throwable;Ljava/util/function/Supplier<Ljava/lang/String;>;)V"}, {"nme": "trace", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Supplier;)V", "sig": "(Lja<PERSON>/util/function/Supplier<Ljava/lang/String;>;)V"}, {"nme": "trace", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;Lja<PERSON>/util/function/Supplier;)V", "sig": "(L<PERSON><PERSON>/lang/Throwable;Ljava/util/function/Supplier<Ljava/lang/String;>;)V"}], "flds": [], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "INTERNAL"], "since", "1.0"]}]}, "org/junit/platform/commons/support/ReflectionSupport.class": {"ver": 52, "acc": 49, "nme": "org/junit/platform/commons/support/ReflectionSupport", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "loadClass", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Optional;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/Optional<Ljava/lang/Class<*>;>;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "DEPRECATED"], "since", "1.4"]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "tryToLoadClass", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/junit/platform/commons/function/Try;", "sig": "(Ljava/lang/String;)Lorg/junit/platform/commons/function/Try<Ljava/lang/Class<*>;>;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "MAINTAINED"], "since", "1.4"]}]}, {"nme": "findAllClassesInClasspathRoot", "acc": 9, "dsc": "(Lja<PERSON>/net/URI;Ljava/util/function/Predicate;Ljava/util/function/Predicate;)Ljava/util/List;", "sig": "(Ljava/net/URI;Ljava/util/function/Predicate<Ljava/lang/Class<*>;>;Ljava/util/function/Predicate<Ljava/lang/String;>;)Ljava/util/List<Ljava/lang/Class<*>;>;"}, {"nme": "findAllClassesInPackage", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/function/Predicate;<PERSON><PERSON><PERSON>/util/function/Predicate;)Ljava/util/List;", "sig": "(Ljava/lang/String;Ljava/util/function/Predicate<Ljava/lang/Class<*>;>;Ljava/util/function/Predicate<Ljava/lang/String;>;)Ljava/util/List<Ljava/lang/Class<*>;>;"}, {"nme": "findAllClassesInModule", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/function/Predicate;<PERSON><PERSON><PERSON>/util/function/Predicate;)Ljava/util/List;", "sig": "(Ljava/lang/String;Ljava/util/function/Predicate<Ljava/lang/Class<*>;>;Ljava/util/function/Predicate<Ljava/lang/String;>;)Ljava/util/List<Ljava/lang/Class<*>;>;"}, {"nme": "newInstance", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;[<PERSON>ja<PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;[Ljava/lang/Object;)TT;"}, {"nme": "invoke<PERSON><PERSON><PERSON>", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;<PERSON><PERSON><PERSON>/lang/Object;[Ljava/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "findFields", "acc": 9, "dsc": "(Ljava/lang/Class;Ljava/util/function/Predicate;Lorg/junit/platform/commons/support/HierarchyTraversalMode;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;Ljava/util/function/Predicate<Ljava/lang/reflect/Field;>;Lorg/junit/platform/commons/support/HierarchyTraversalMode;)Ljava/util/List<Ljava/lang/reflect/Field;>;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "MAINTAINED"], "since", "1.4"]}]}, {"nme": "tryToReadFieldValue", "acc": 9, "dsc": "(Lja<PERSON>/lang/reflect/Field;Ljava/lang/Object;)Lorg/junit/platform/commons/function/Try;", "sig": "(Ljava/lang/reflect/Field;Ljava/lang/Object;)Lorg/junit/platform/commons/function/Try<Ljava/lang/Object;>;", "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "MAINTAINED"], "since", "1.4"]}]}, {"nme": "find<PERSON><PERSON><PERSON>", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Optional;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/String;Ljava/lang/String;)Ljava/util/Optional<Ljava/lang/reflect/Method;>;"}, {"nme": "find<PERSON><PERSON><PERSON>", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;L<PERSON><PERSON>/lang/String;[Ljava/lang/Class;)Ljava/util/Optional;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/String;[Ljava/lang/Class<*>;)Ljava/util/Optional<Ljava/lang/reflect/Method;>;"}, {"nme": "findMethods", "acc": 9, "dsc": "(Ljava/lang/Class;Ljava/util/function/Predicate;Lorg/junit/platform/commons/support/HierarchyTraversalMode;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;Ljava/util/function/Predicate<Ljava/lang/reflect/Method;>;Lorg/junit/platform/commons/support/HierarchyTraversalMode;)Ljava/util/List<Ljava/lang/reflect/Method;>;"}, {"nme": "findNestedClasses", "acc": 9, "dsc": "(L<PERSON><PERSON>/lang/Class;Ljava/util/function/Predicate;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;Ljava/util/function/Predicate<Ljava/lang/Class<*>;>;)Ljava/util/List<Ljava/lang/Class<*>;>;", "exs": ["org/junit/platform/commons/JUnitException"]}], "flds": [], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "MAINTAINED"], "since", "1.0"]}]}, "org/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode.class": {"ver": 52, "acc": 16433, "nme": "org/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lorg/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Lorg/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "TOP_DOWN", "dsc": "Lorg/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode;"}, {"acc": 16409, "nme": "BOTTOM_UP", "dsc": "Lorg/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lorg/junit/platform/commons/util/ReflectionUtils$HierarchyTraversalMode;"}]}, "org/junit/platform/commons/util/PreconditionViolationException.class": {"ver": 52, "acc": 131105, "nme": "org/junit/platform/commons/util/PreconditionViolationException", "super": "org/junit/platform/commons/JUnitException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}], "vanns": [{"dsc": "Lorg/apiguardian/api/API;", "vals": ["status", ["Lorg/apiguardian/api/API$Status;", "DEPRECATED"], "since", "1.5"]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}}}}