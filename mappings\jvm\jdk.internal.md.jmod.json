{"md5": "e4968176dae14d3ab30377820717e008", "sha2": "8506640479dcd8856269cd7c72a5045b06a1950a", "sha256": "dd7fe65d30bf631440c36419e049d5963b66d53af7b5b9a0b05a7da5874e1176", "contents": {"classes": {"classes/jdk/internal/org/commonmark/renderer/markdown/MarkdownWriter.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/renderer/markdown/MarkdownWriter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Appendable;)V"}, {"nme": "raw", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "raw", "acc": 1, "dsc": "(C)V"}, {"nme": "text", "acc": 1, "dsc": "(Ljava/lang/String;Ljdk/internal/org/commonmark/text/CharMatcher;)V"}, {"nme": "line", "acc": 1, "dsc": "()V"}, {"nme": "block", "acc": 1, "dsc": "()V"}, {"nme": "pushPrefix", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "writePrefix", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "popPrefix", "acc": 1, "dsc": "()V"}, {"nme": "pushTight", "acc": 1, "dsc": "(Z)V"}, {"nme": "popTight", "acc": 1, "dsc": "()V"}, {"nme": "pushRawEscape", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/text/CharMatcher;)V"}, {"nme": "popRawEscape", "acc": 1, "dsc": "()V"}, {"nme": "getLastChar", "acc": 1, "dsc": "()C"}, {"nme": "isAtLineStart", "acc": 1, "dsc": "()Z"}, {"nme": "write", "acc": 2, "dsc": "(Ljava/lang/String;Ljdk/internal/org/commonmark/text/CharMatcher;)V"}, {"nme": "write", "acc": 2, "dsc": "(C)V"}, {"nme": "writePrefixes", "acc": 2, "dsc": "()V"}, {"nme": "flushBlockSeparator", "acc": 2, "dsc": "()V"}, {"nme": "append", "acc": 2, "dsc": "(CLjdk/internal/org/commonmark/text/CharMatcher;)V", "exs": ["java/io/IOException"]}, {"nme": "isTight", "acc": 2, "dsc": "()Z"}, {"nme": "needsEscaping", "acc": 2, "dsc": "(CLjdk/internal/org/commonmark/text/CharMatcher;)Z"}, {"nme": "rawNeedsEscaping", "acc": 2, "dsc": "(C)Z"}], "flds": [{"acc": 18, "nme": "buffer", "dsc": "<PERSON><PERSON><PERSON>/lang/Appendable;"}, {"acc": 2, "nme": "blockSeparator", "dsc": "I"}, {"acc": 2, "nme": "lastChar", "dsc": "C"}, {"acc": 2, "nme": "atLineStart", "dsc": "Z"}, {"acc": 18, "nme": "prefixes", "dsc": "<PERSON><PERSON><PERSON>/util/LinkedList;", "sig": "Ljava/util/LinkedList<Ljava/lang/String;>;"}, {"acc": 18, "nme": "tight", "dsc": "<PERSON><PERSON><PERSON>/util/LinkedList;", "sig": "<PERSON>ja<PERSON>/util/LinkedList<Ljava/lang/Bo<PERSON>an;>;"}, {"acc": 18, "nme": "rawEscapes", "dsc": "<PERSON><PERSON><PERSON>/util/LinkedList;", "sig": "Ljava/util/LinkedList<Ljdk/internal/org/commonmark/text/CharMatcher;>;"}]}, "classes/jdk/internal/markdown/MarkdownTransformer.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/markdown/MarkdownTransformer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "name", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "transform", "acc": 1, "dsc": "(Lcom/sun/source/util/DocTrees;Lcom/sun/source/doctree/DocCommentTree;)Lcom/sun/source/doctree/DocCommentTree;"}, {"nme": "isMarkdown", "acc": 2, "dsc": "(Lcom/sun/source/doctree/DocCommentTree;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "isMarkdownVisitor", "dsc": "Lcom/sun/source/doctree/DocTreeVisitor;", "sig": "Lcom/sun/source/doctree/DocTreeVisitor<Ljava/lang/Boolean;Ljava/lang/Void;>;"}, {"acc": 26, "nme": "PLACEHOLDER", "dsc": "C", "val": 65532}]}, "classes/jdk/internal/org/commonmark/node/Delimited.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/node/Delimited", "super": "java/lang/Object", "mthds": [{"nme": "getOpeningDelimiter", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getClosingDelimiter", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "classes/jdk/internal/org/commonmark/internal/util/LinkScanner.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/util/LinkScanner", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "scanLinkLabelContent", "acc": 9, "dsc": "(Ljdk/internal/org/commonmark/parser/beta/Scanner;)Z"}, {"nme": "scanLinkDestination", "acc": 9, "dsc": "(Ljdk/internal/org/commonmark/parser/beta/Scanner;)Z"}, {"nme": "scanLinkTitle", "acc": 9, "dsc": "(Ljdk/internal/org/commonmark/parser/beta/Scanner;)Z"}, {"nme": "scanLinkTitleContent", "acc": 9, "dsc": "(Ljdk/internal/org/commonmark/parser/beta/Scanner;C)Z"}, {"nme": "scanLinkDestinationWithBalancedParens", "acc": 10, "dsc": "(Ljdk/internal/org/commonmark/parser/beta/Scanner;)Z"}, {"nme": "isEscapable", "acc": 10, "dsc": "(C)Z"}], "flds": []}, "classes/jdk/internal/org/commonmark/internal/HtmlBlockParser$Factory.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/HtmlBlockParser$Factory", "super": "jdk/internal/org/commonmark/parser/block/AbstractBlockParserFactory", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "tryStart", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/block/ParserState;Ljdk/internal/org/commonmark/parser/block/MatchedBlockParser;)Ljdk/internal/org/commonmark/parser/block/BlockStart;"}], "flds": []}, "classes/jdk/internal/org/commonmark/internal/inline/ParsedInline.class": {"ver": 68, "acc": 1057, "nme": "jdk/internal/org/commonmark/internal/inline/ParsedInline", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "none", "acc": 9, "dsc": "()Ljdk/internal/org/commonmark/internal/inline/ParsedInline;"}, {"nme": "of", "acc": 9, "dsc": "(Ljdk/internal/org/commonmark/node/Node;Ljdk/internal/org/commonmark/parser/beta/Position;)Ljdk/internal/org/commonmark/internal/inline/ParsedInline;"}], "flds": []}, "classes/jdk/internal/org/commonmark/text/CharMatcher.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/text/CharMatcher", "super": "java/lang/Object", "mthds": [{"nme": "matches", "acc": 1025, "dsc": "(C)Z"}], "flds": []}, "classes/jdk/internal/org/commonmark/renderer/html/HtmlNodeRendererFactory.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/renderer/html/HtmlNodeRendererFactory", "super": "java/lang/Object", "mthds": [{"nme": "create", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/renderer/html/HtmlNodeRendererContext;)Ljdk/internal/org/commonmark/renderer/NodeRenderer;"}], "flds": []}, "classes/jdk/internal/markdown/MarkdownTransformer$2.class": {"ver": 68, "acc": 4128, "nme": "jdk/internal/markdown/MarkdownTransformer$2", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$com$sun$source$doctree$DocTree$Kind", "dsc": "[I"}]}, "classes/jdk/internal/org/commonmark/node/IndentedCodeBlock.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/node/IndentedCodeBlock", "super": "jdk/internal/org/commonmark/node/Block", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "accept", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Visitor;)V"}, {"nme": "<PERSON><PERSON><PERSON>al", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLiteral", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 2, "nme": "literal", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/internal/org/commonmark/node/StrongEmphasis.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/node/StrongEmphasis", "super": "jdk/internal/org/commonmark/node/Node", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "set<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getOpeningDelimiter", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getClosingDelimiter", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "accept", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Visitor;)V"}], "flds": [{"acc": 2, "nme": "delimiter", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/internal/org/commonmark/parser/block/BlockContinue.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/parser/block/BlockContinue", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "none", "acc": 9, "dsc": "()Ljdk/internal/org/commonmark/parser/block/BlockContinue;"}, {"nme": "atIndex", "acc": 9, "dsc": "(I)Ljdk/internal/org/commonmark/parser/block/BlockContinue;"}, {"nme": "atColumn", "acc": 9, "dsc": "(I)Ljdk/internal/org/commonmark/parser/block/BlockContinue;"}, {"nme": "finished", "acc": 9, "dsc": "()Ljdk/internal/org/commonmark/parser/block/BlockContinue;"}], "flds": []}, "classes/jdk/internal/org/commonmark/node/HtmlInline.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/node/HtmlInline", "super": "jdk/internal/org/commonmark/node/Node", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "accept", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Visitor;)V"}, {"nme": "<PERSON><PERSON><PERSON>al", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLiteral", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 2, "nme": "literal", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/module-info.class": {"ver": 68, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "classes/jdk/internal/org/commonmark/node/SourceSpans.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/node/SourceSpans", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "empty", "acc": 9, "dsc": "()Ljdk/internal/org/commonmark/node/SourceSpans;"}, {"nme": "getSourceSpans", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljdk/internal/org/commonmark/node/SourceSpan;>;"}, {"nme": "addAllFrom", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)V", "sig": "(Ljava/lang/Iterable<+Ljdk/internal/org/commonmark/node/Node;>;)V"}, {"nme": "addAll", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Ljdk/internal/org/commonmark/node/SourceSpan;>;)V"}], "flds": [{"acc": 2, "nme": "sourceSpans", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/node/SourceSpan;>;"}]}, "classes/jdk/internal/org/commonmark/node/Visitor.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/node/Visitor", "super": "java/lang/Object", "mthds": [{"nme": "visit", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/BlockQuote;)V"}, {"nme": "visit", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/BulletList;)V"}, {"nme": "visit", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/Code;)V"}, {"nme": "visit", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/Document;)V"}, {"nme": "visit", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/Emphasis;)V"}, {"nme": "visit", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/FencedCodeBlock;)V"}, {"nme": "visit", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/HardLineBreak;)V"}, {"nme": "visit", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/Heading;)V"}, {"nme": "visit", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/ThematicBreak;)V"}, {"nme": "visit", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/HtmlInline;)V"}, {"nme": "visit", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/HtmlBlock;)V"}, {"nme": "visit", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/Image;)V"}, {"nme": "visit", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/IndentedCodeBlock;)V"}, {"nme": "visit", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/Link;)V"}, {"nme": "visit", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/ListItem;)V"}, {"nme": "visit", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/OrderedList;)V"}, {"nme": "visit", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/Paragraph;)V"}, {"nme": "visit", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/SoftLineBreak;)V"}, {"nme": "visit", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/StrongEmphasis;)V"}, {"nme": "visit", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/Text;)V"}, {"nme": "visit", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/LinkReferenceDefinition;)V"}, {"nme": "visit", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/CustomBlock;)V"}, {"nme": "visit", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/CustomNode;)V"}], "flds": []}, "classes/jdk/internal/org/commonmark/internal/BlockQuoteParser.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/BlockQuoteParser", "super": "jdk/internal/org/commonmark/parser/block/AbstractBlockParser", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Z"}, {"nme": "canContain", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Block;)Z"}, {"nme": "getBlock", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/node/BlockQuote;"}, {"nme": "tryContinue", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/block/ParserState;)Ljdk/internal/org/commonmark/parser/block/BlockContinue;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 10, "dsc": "(Ljdk/internal/org/commonmark/parser/block/ParserState;I)Z"}, {"nme": "getBlock", "acc": 4161, "dsc": "()Ljdk/internal/org/commonmark/node/Block;"}], "flds": [{"acc": 18, "nme": "block", "dsc": "Ljdk/internal/org/commonmark/node/BlockQuote;"}]}, "classes/jdk/internal/org/commonmark/renderer/html/CoreHtmlNodeRenderer.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/renderer/html/CoreHtmlNodeRenderer", "super": "jdk/internal/org/commonmark/node/AbstractVisitor", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/renderer/html/HtmlNodeRendererContext;)V"}, {"nme": "getNodeTypes", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljava/lang/Class<+Ljdk/internal/org/commonmark/node/Node;>;>;"}, {"nme": "render", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Document;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Heading;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Paragraph;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/BlockQuote;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/BulletList;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/FencedCodeBlock;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/HtmlBlock;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/ThematicBreak;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/IndentedCodeBlock;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Link;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/ListItem;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/OrderedList;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Image;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Emphasis;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/StrongEmphasis;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Text;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Code;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/HtmlInline;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/SoftLineBreak;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/HardLineBreak;)V"}, {"nme": "visit<PERSON><PERSON><PERSON><PERSON>", "acc": 4, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}, {"nme": "renderCodeBlock", "acc": 2, "dsc": "(Ljava/lang/String;Ljdk/internal/org/commonmark/node/Node;Ljava/util/Map;)V", "sig": "(Ljava/lang/String;Ljdk/internal/org/commonmark/node/Node;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "renderListBlock", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/node/ListBlock;Ljava/lang/String;Ljava/util/Map;)V", "sig": "(Ljdk/internal/org/commonmark/node/ListBlock;Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "isInTightList", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/node/Paragraph;)Z"}, {"nme": "getAttrs", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/node/Node;Ljava/lang/String;)Ljava/util/Map;", "sig": "(Ljdk/internal/org/commonmark/node/Node;Ljava/lang/String;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "getAttrs", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/node/Node;Ljava/lang/String;Ljava/util/Map;)Ljava/util/Map;", "sig": "(Ljdk/internal/org/commonmark/node/Node;Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}], "flds": [{"acc": 20, "nme": "context", "dsc": "Ljdk/internal/org/commonmark/renderer/html/HtmlNodeRendererContext;"}, {"acc": 18, "nme": "html", "dsc": "Ljdk/internal/org/commonmark/renderer/html/HtmlWriter;"}]}, "classes/jdk/internal/org/commonmark/internal/inline/AsteriskDelimiterProcessor.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/inline/AsteriskDelimiterProcessor", "super": "jdk/internal/org/commonmark/internal/inline/EmphasisDelimiterProcessor", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "classes/jdk/internal/org/commonmark/internal/DocumentBlockParser.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/DocumentBlockParser", "super": "jdk/internal/org/commonmark/parser/block/AbstractBlockParser", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Z"}, {"nme": "canContain", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Block;)Z"}, {"nme": "getBlock", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/node/Document;"}, {"nme": "tryContinue", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/block/ParserState;)Ljdk/internal/org/commonmark/parser/block/BlockContinue;"}, {"nme": "addLine", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/SourceLine;)V"}, {"nme": "getBlock", "acc": 4161, "dsc": "()Ljdk/internal/org/commonmark/node/Block;"}], "flds": [{"acc": 18, "nme": "document", "dsc": "Ljdk/internal/org/commonmark/node/Document;"}]}, "classes/jdk/internal/org/commonmark/internal/ThematicBreakParser.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/ThematicBreakParser", "super": "jdk/internal/org/commonmark/parser/block/AbstractBlockParser", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getBlock", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/node/Block;"}, {"nme": "tryContinue", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/block/ParserState;)Ljdk/internal/org/commonmark/parser/block/BlockContinue;"}, {"nme": "isThematicBreak", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;I)Z"}], "flds": [{"acc": 18, "nme": "block", "dsc": "Ljdk/internal/org/commonmark/node/ThematicBreak;"}]}, "classes/jdk/internal/org/commonmark/renderer/html/AttributeProvider.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/renderer/html/AttributeProvider", "super": "java/lang/Object", "mthds": [{"nme": "setAttributes", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/Node;Ljava/lang/String;Ljava/util/Map;)V", "sig": "(Ljdk/internal/org/commonmark/node/Node;Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}], "flds": []}, "classes/jdk/internal/org/commonmark/ext/gfm/tables/TableHead.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/ext/gfm/tables/TableHead", "super": "jdk/internal/org/commonmark/node/CustomNode", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "classes/jdk/internal/org/commonmark/internal/ThematicBreakParser$Factory.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/ThematicBreakParser$Factory", "super": "jdk/internal/org/commonmark/parser/block/AbstractBlockParserFactory", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "tryStart", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/block/ParserState;Ljdk/internal/org/commonmark/parser/block/MatchedBlockParser;)Ljdk/internal/org/commonmark/parser/block/BlockStart;"}], "flds": []}, "classes/jdk/internal/org/commonmark/node/Paragraph.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/node/Paragraph", "super": "jdk/internal/org/commonmark/node/Block", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "accept", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Visitor;)V"}], "flds": []}, "classes/jdk/internal/markdown/MarkdownTransformer$DCTransformer.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/markdown/MarkdownTransformer$DCTransformer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/sun/tools/javac/api/JavacTrees;)V"}, {"nme": "transform", "acc": 1, "dsc": "(Lcom/sun/tools/javac/tree/DCTree;)Lcom/sun/tools/javac/tree/DCTree;"}, {"nme": "transform", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Ljava/util/List;", "sig": "(Ljava/util/List<+Lcom/sun/tools/javac/tree/DCTree;>;)Ljava/util/List<+Lcom/sun/tools/javac/tree/DCTree;>;"}, {"nme": "transform", "acc": 2, "dsc": "(Lcom/sun/tools/javac/tree/DCTree$DCAuthor;)Lcom/sun/tools/javac/tree/DCTree$DCAuthor;"}, {"nme": "transform", "acc": 2, "dsc": "(Lcom/sun/tools/javac/tree/DCTree$DCDeprecated;)Lcom/sun/tools/javac/tree/DCTree$DCDeprecated;"}, {"nme": "transform", "acc": 1, "dsc": "(Lcom/sun/tools/javac/tree/DCTree$DCDocComment;)Lcom/sun/tools/javac/tree/DCTree$DCDocComment;"}, {"nme": "transform", "acc": 2, "dsc": "(Lcom/sun/tools/javac/tree/DCTree$DCHidden;)Lcom/sun/tools/javac/tree/DCTree$DCHidden;"}, {"nme": "transform", "acc": 2, "dsc": "(Lcom/sun/tools/javac/tree/DCTree$DCIndex;)Lcom/sun/tools/javac/tree/DCTree$DCIndex;"}, {"nme": "transform", "acc": 2, "dsc": "(Lcom/sun/tools/javac/tree/DCTree$DCLink;)Lcom/sun/tools/javac/tree/DCTree$DCLink;"}, {"nme": "transform", "acc": 2, "dsc": "(Lcom/sun/tools/javac/tree/DCTree$DCParam;)Lcom/sun/tools/javac/tree/DCTree$DCParam;"}, {"nme": "transform", "acc": 2, "dsc": "(Lcom/sun/tools/javac/tree/DCTree$DCProvides;)Lcom/sun/tools/javac/tree/DCTree$DCProvides;"}, {"nme": "transform", "acc": 2, "dsc": "(Lcom/sun/tools/javac/tree/DCTree$DCReturn;)Lcom/sun/tools/javac/tree/DCTree$DCReturn;"}, {"nme": "transform", "acc": 2, "dsc": "(Lcom/sun/tools/javac/tree/DCTree$DCSee;)Lcom/sun/tools/javac/tree/DCTree$DCSee;"}, {"nme": "transform", "acc": 2, "dsc": "(Lcom/sun/tools/javac/tree/DCTree$DCSerial;)Lcom/sun/tools/javac/tree/DCTree$DCSerial;"}, {"nme": "transform", "acc": 2, "dsc": "(Lcom/sun/tools/javac/tree/DCTree$DCSerialData;)Lcom/sun/tools/javac/tree/DCTree$DCSerialData;"}, {"nme": "transform", "acc": 2, "dsc": "(Lcom/sun/tools/javac/tree/DCTree$DCSerialField;)Lcom/sun/tools/javac/tree/DCTree$DCSerialField;"}, {"nme": "transform", "acc": 0, "dsc": "(Lcom/sun/tools/javac/tree/DCTree$DCSince;)Lcom/sun/tools/javac/tree/DCTree$DCSince;"}, {"nme": "transform", "acc": 2, "dsc": "(Lcom/sun/tools/javac/tree/DCTree$DCSpec;)Lcom/sun/tools/javac/tree/DCTree$DCSpec;"}, {"nme": "transform", "acc": 2, "dsc": "(Lcom/sun/tools/javac/tree/DCTree$DCSummary;)Lcom/sun/tools/javac/tree/DCTree$DCSummary;"}, {"nme": "transform", "acc": 2, "dsc": "(Lcom/sun/tools/javac/tree/DCTree$DCThrows;)Lcom/sun/tools/javac/tree/DCTree$DCThrows;"}, {"nme": "transform", "acc": 2, "dsc": "(Lcom/sun/tools/javac/tree/DCTree$DCUnknownBlockTag;)Lcom/sun/tools/javac/tree/DCTree$DCUnknownBlockTag;"}, {"nme": "transform", "acc": 2, "dsc": "(Lcom/sun/tools/javac/tree/DCTree$DCUnknownInlineTag;)Lcom/sun/tools/javac/tree/DCTree$DCUnknownInlineTag;"}, {"nme": "transform", "acc": 2, "dsc": "(Lcom/sun/tools/javac/tree/DCTree$DCUses;)Lcom/sun/tools/javac/tree/DCTree$DCUses;"}, {"nme": "transform", "acc": 2, "dsc": "(Lcom/sun/tools/javac/tree/DCTree$DCVersion;)Lcom/sun/tools/javac/tree/DCTree$DCVersion;"}, {"nme": "equal", "acc": 10, "dsc": "(Lcom/sun/source/doctree/DocTree;Lcom/sun/source/doctree/DocTree;)Z", "sig": "<T::Lcom/sun/source/doctree/DocTree;>(TT;TT;)Z"}, {"nme": "equal", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/util/List;)Z", "sig": "<T::Lcom/sun/source/doctree/DocTree;>(Ljava/util/List<+TT;>;Ljava/util/List<+TT;>;)Z"}, {"nme": "lambda$transform$0", "acc": 4106, "dsc": "(Lcom/sun/tools/javac/tree/DCTree;)Z"}], "flds": [{"acc": 18, "nme": "m", "dsc": "Lcom/sun/tools/javac/tree/DocTreeMaker;"}, {"acc": 18, "nme": "ref<PERSON><PERSON><PERSON>", "dsc": "Lcom/sun/tools/javac/parser/ReferenceParser;"}, {"acc": 18, "nme": "autorefScheme", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/internal/org/commonmark/internal/util/Escaping$2.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/org/commonmark/internal/util/Escaping$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "replace", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>java/lang/StringBuilder;)V"}], "flds": []}, "classes/jdk/internal/org/commonmark/node/HardLineBreak.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/node/HardLineBreak", "super": "jdk/internal/org/commonmark/node/Node", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "accept", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Visitor;)V"}], "flds": []}, "classes/jdk/internal/org/commonmark/parser/block/BlockParser.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/parser/block/BlockParser", "super": "java/lang/Object", "mthds": [{"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()Z"}, {"nme": "canHaveLazyContinuationLines", "acc": 1025, "dsc": "()Z"}, {"nme": "canContain", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/Block;)Z"}, {"nme": "getBlock", "acc": 1025, "dsc": "()Ljdk/internal/org/commonmark/node/Block;"}, {"nme": "tryContinue", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/parser/block/ParserState;)Ljdk/internal/org/commonmark/parser/block/BlockContinue;"}, {"nme": "addLine", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/parser/SourceLine;)V"}, {"nme": "addSourceSpan", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/SourceSpan;)V"}, {"nme": "closeBlock", "acc": 1025, "dsc": "()V"}, {"nme": "parseInlines", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/parser/InlineParser;)V"}], "flds": []}, "classes/jdk/internal/org/commonmark/ext/gfm/tables/TableBody.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/ext/gfm/tables/TableBody", "super": "jdk/internal/org/commonmark/node/CustomNode", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "classes/jdk/internal/org/commonmark/internal/BlockContent.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/org/commonmark/internal/BlockContent", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "add", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;)V"}, {"nme": "getString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "sb", "dsc": "<PERSON><PERSON><PERSON>/lang/StringBuilder;"}, {"acc": 2, "nme": "lineCount", "dsc": "I"}]}, "classes/jdk/internal/org/commonmark/internal/InlineParserImpl.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/InlineParserImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/InlineParserContext;)V"}, {"nme": "calculateSpecialCharacters", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;<PERSON><PERSON><PERSON>/util/Set;)<PERSON><PERSON><PERSON>/util/BitSet;", "sig": "(L<PERSON><PERSON>/util/Set<Ljava/lang/Character;>;Ljava/util/Set<Ljava/lang/Character;>;)Ljava/util/BitSet;"}, {"nme": "calculateDelimiterProcessors", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Ljava/util/Map;", "sig": "(Ljava/util/List<Ljdk/internal/org/commonmark/parser/delimiter/DelimiterProcessor;>;)Ljava/util/Map<Ljava/lang/Character;Ljdk/internal/org/commonmark/parser/delimiter/DelimiterProcessor;>;"}, {"nme": "scanner", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/parser/beta/Scanner;"}, {"nme": "addDelimiterProcessors", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;Ljava/util/Map;)V", "sig": "(Ljava/lang/Iterable<Ljdk/internal/org/commonmark/parser/delimiter/DelimiterProcessor;>;Ljava/util/Map<Ljava/lang/Character;Ljdk/internal/org/commonmark/parser/delimiter/DelimiterProcessor;>;)V"}, {"nme": "addDelimiterProcessorForChar", "acc": 10, "dsc": "(CLjdk/internal/org/commonmark/parser/delimiter/DelimiterProcessor;Ljava/util/Map;)V", "sig": "(CLjdk/internal/org/commonmark/parser/delimiter/DelimiterProcessor;Ljava/util/Map<Ljava/lang/Character;Ljdk/internal/org/commonmark/parser/delimiter/DelimiterProcessor;>;)V"}, {"nme": "parse", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/SourceLines;Ljdk/internal/org/commonmark/node/Node;)V"}, {"nme": "reset", "acc": 0, "dsc": "(Ljdk/internal/org/commonmark/parser/SourceLines;)V"}, {"nme": "text", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/parser/SourceLines;)Ljdk/internal/org/commonmark/node/Text;"}, {"nme": "parseInline", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Ljdk/internal/org/commonmark/node/Node;>;"}, {"nme": "parseDelimiters", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/parser/delimiter/DelimiterProcessor;C)Ljava/util/List;", "sig": "(Ljdk/internal/org/commonmark/parser/delimiter/DelimiterProcessor;C)Ljava/util/List<+Ljdk/internal/org/commonmark/node/Node;>;"}, {"nme": "parseOpenBracket", "acc": 2, "dsc": "()Ljdk/internal/org/commonmark/node/Node;"}, {"nme": "parseBang", "acc": 2, "dsc": "()Ljdk/internal/org/commonmark/node/Node;"}, {"nme": "parseCloseBracket", "acc": 2, "dsc": "()Ljdk/internal/org/commonmark/node/Node;"}, {"nme": "addBracket", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/internal/Bracket;)V"}, {"nme": "removeLastBracket", "acc": 2, "dsc": "()V"}, {"nme": "parseLinkDestination", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/parser/beta/Scanner;)Ljava/lang/String;"}, {"nme": "parseLinkTitle", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/parser/beta/Scanner;)Ljava/lang/String;"}, {"nme": "parseLinkLabel", "acc": 0, "dsc": "(Ljdk/internal/org/commonmark/parser/beta/Scanner;)Ljava/lang/String;"}, {"nme": "parseLineBreak", "acc": 2, "dsc": "()Ljdk/internal/org/commonmark/node/Node;"}, {"nme": "parseText", "acc": 2, "dsc": "()Ljdk/internal/org/commonmark/node/Node;"}, {"nme": "scanDelimiters", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/parser/delimiter/DelimiterProcessor;C)Ljdk/internal/org/commonmark/internal/InlineParserImpl$DelimiterData;"}, {"nme": "processDelimiters", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/internal/Delimiter;)V"}, {"nme": "removeDelimitersBetween", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/internal/Delimiter;Ljdk/internal/org/commonmark/internal/Delimiter;)V"}, {"nme": "removeDelimiterAndNodes", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/internal/Delimiter;)V"}, {"nme": "removeDelimiterKeepNode", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/internal/Delimiter;)V"}, {"nme": "remove<PERSON><PERSON><PERSON>r", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/internal/Delimiter;)V"}, {"nme": "mergeChildTextNodes", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}, {"nme": "mergeTextNodesInclusive", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/node/Node;Ljdk/internal/org/commonmark/node/Node;)V"}, {"nme": "mergeIfNeeded", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/node/Text;Ljdk/internal/org/commonmark/node/Text;I)V"}], "flds": [{"acc": 18, "nme": "specialCharacters", "dsc": "Ljava/util/BitSet;"}, {"acc": 18, "nme": "delimiterProcessors", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Character;Ljdk/internal/org/commonmark/parser/delimiter/DelimiterProcessor;>;"}, {"acc": 18, "nme": "context", "dsc": "Ljdk/internal/org/commonmark/parser/InlineParserContext;"}, {"acc": 18, "nme": "inlineParsers", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Character;Ljava/util/List<Ljdk/internal/org/commonmark/internal/inline/InlineContentParser;>;>;"}, {"acc": 2, "nme": "scanner", "dsc": "Ljdk/internal/org/commonmark/parser/beta/Scanner;"}, {"acc": 2, "nme": "includeSourceSpans", "dsc": "Z"}, {"acc": 2, "nme": "trailingSpaces", "dsc": "I"}, {"acc": 2, "nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dsc": "Ljdk/internal/org/commonmark/internal/Delimiter;"}, {"acc": 2, "nme": "lastBracket", "dsc": "Ljdk/internal/org/commonmark/internal/Bracket;"}]}, "classes/jdk/internal/org/commonmark/internal/inline/UnderscoreDelimiterProcessor.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/inline/UnderscoreDelimiterProcessor", "super": "jdk/internal/org/commonmark/internal/inline/EmphasisDelimiterProcessor", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "classes/jdk/internal/org/commonmark/node/ListBlock.class": {"ver": 68, "acc": 1057, "nme": "jdk/internal/org/commonmark/node/ListBlock", "super": "jdk/internal/org/commonmark/node/Block", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "isTight", "acc": 1, "dsc": "()Z"}, {"nme": "setTight", "acc": 1, "dsc": "(Z)V"}], "flds": [{"acc": 2, "nme": "tight", "dsc": "Z"}]}, "classes/jdk/internal/org/commonmark/renderer/markdown/CoreMarkdownNodeRenderer$OrderedListHolder.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/org/commonmark/renderer/markdown/CoreMarkdownNodeRenderer$OrderedListHolder", "super": "jdk/internal/org/commonmark/renderer/markdown/CoreMarkdownNodeRenderer$ListHolder", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Ljdk/internal/org/commonmark/renderer/markdown/CoreMarkdownNodeRenderer$ListHolder;Ljdk/internal/org/commonmark/node/OrderedList;)V"}], "flds": [{"acc": 16, "nme": "delimiter", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "number", "dsc": "I"}]}, "classes/jdk/internal/org/commonmark/internal/inline/BackticksInlineParser.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/inline/BackticksInlineParser", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "try<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/internal/inline/InlineParserState;)Ljdk/internal/org/commonmark/internal/inline/ParsedInline;"}], "flds": []}, "classes/jdk/internal/org/commonmark/internal/HtmlBlockParser.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/HtmlBlockParser", "super": "jdk/internal/org/commonmark/parser/block/AbstractBlockParser", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/regex/Pattern;)V"}, {"nme": "getBlock", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/node/Block;"}, {"nme": "tryContinue", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/block/ParserState;)Ljdk/internal/org/commonmark/parser/block/BlockContinue;"}, {"nme": "addLine", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/SourceLine;)V"}, {"nme": "closeBlock", "acc": 1, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "TAGNAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "[A-Za-z][A-Za-z0-9-]*"}, {"acc": 26, "nme": "ATTRIBUTENAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "[a-zA-Z_:][a-zA-Z0-9:._-]*"}, {"acc": 26, "nme": "UNQUOTEDVALUE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "[^\"'=<>`\\x00-\\x20]+"}, {"acc": 26, "nme": "SINGLEQUOTEDVALUE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "'[^']*'"}, {"acc": 26, "nme": "DOUBLEQUOTEDVALUE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "\"[^\"]*\""}, {"acc": 26, "nme": "ATTRIBUTEVALUE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "(?:[^\"'=<>`\\x00-\\x20]+|'[^']*'|\"[^\"]*\")"}, {"acc": 26, "nme": "ATTRIBUTEVALUESPEC", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "(?:\\s*=\\s*(?:[^\"'=<>`\\x00-\\x20]+|'[^']*'|\"[^\"]*\"))"}, {"acc": 26, "nme": "ATTRIBUTE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "(?:\\s+[a-zA-Z_:][a-zA-Z0-9:._-]*(?:\\s*=\\s*(?:[^\"'=<>`\\x00-\\x20]+|'[^']*'|\"[^\"]*\"))?)"}, {"acc": 26, "nme": "OPENTAG", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "<[A-Za-z][A-Za-z0-9-]*(?:\\s+[a-zA-Z_:][a-zA-Z0-9:._-]*(?:\\s*=\\s*(?:[^\"'=<>`\\x00-\\x20]+|'[^']*'|\"[^\"]*\"))?)*\\s*/?>"}, {"acc": 26, "nme": "CLOSETAG", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "</[A-Za-z][A-Za-z0-9-]*\\s*[>]"}, {"acc": 26, "nme": "BLOCK_PATTERNS", "dsc": "[[<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 18, "nme": "block", "dsc": "Ljdk/internal/org/commonmark/node/HtmlBlock;"}, {"acc": 18, "nme": "closingPattern", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 2, "nme": "finished", "dsc": "Z"}, {"acc": 2, "nme": "content", "dsc": "Ljdk/internal/org/commonmark/internal/BlockContent;"}]}, "classes/jdk/internal/org/commonmark/ext/gfm/tables/internal/TableTextContentNodeRenderer.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/ext/gfm/tables/internal/TableTextContentNodeRenderer", "super": "jdk/internal/org/commonmark/ext/gfm/tables/internal/TableNodeRenderer", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/renderer/text/TextContentNodeRendererContext;)V"}, {"nme": "renderBlock", "acc": 4, "dsc": "(Ljdk/internal/org/commonmark/ext/gfm/tables/TableBlock;)V"}, {"nme": "renderHead", "acc": 4, "dsc": "(Ljdk/internal/org/commonmark/ext/gfm/tables/TableHead;)V"}, {"nme": "renderBody", "acc": 4, "dsc": "(Ljdk/internal/org/commonmark/ext/gfm/tables/TableBody;)V"}, {"nme": "renderRow", "acc": 4, "dsc": "(Ljdk/internal/org/commonmark/ext/gfm/tables/TableRow;)V"}, {"nme": "renderCell", "acc": 4, "dsc": "(Ljdk/internal/org/commonmark/ext/gfm/tables/TableCell;)V"}, {"nme": "renderLastCell", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/ext/gfm/tables/TableCell;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}, {"nme": "render", "acc": 4161, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}, {"nme": "getNodeTypes", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;"}], "flds": [{"acc": 18, "nme": "textContentWriter", "dsc": "Ljdk/internal/org/commonmark/renderer/text/TextContentWriter;"}, {"acc": 18, "nme": "context", "dsc": "Ljdk/internal/org/commonmark/renderer/text/TextContentNodeRendererContext;"}]}, "classes/jdk/internal/org/commonmark/parser/block/AbstractBlockParserFactory.class": {"ver": 68, "acc": 1057, "nme": "jdk/internal/org/commonmark/parser/block/AbstractBlockParserFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "classes/jdk/internal/org/commonmark/renderer/html/HtmlRenderer$HtmlRendererExtension.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/renderer/html/HtmlRenderer$HtmlRendererExtension", "super": "java/lang/Object", "mthds": [{"nme": "extend", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/renderer/html/HtmlRenderer$Builder;)V"}], "flds": []}, "classes/jdk/internal/org/commonmark/node/CustomBlock.class": {"ver": 68, "acc": 1057, "nme": "jdk/internal/org/commonmark/node/CustomBlock", "super": "jdk/internal/org/commonmark/node/Block", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "accept", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Visitor;)V"}], "flds": []}, "classes/jdk/internal/org/commonmark/ext/gfm/tables/TableBlock.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/ext/gfm/tables/TableBlock", "super": "jdk/internal/org/commonmark/node/CustomBlock", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "classes/jdk/internal/org/commonmark/parser/delimiter/DelimiterRun.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/parser/delimiter/DelimiterRun", "super": "java/lang/Object", "mthds": [{"nme": "canOpen", "acc": 1025, "dsc": "()Z"}, {"nme": "canClose", "acc": 1025, "dsc": "()Z"}, {"nme": "length", "acc": 1025, "dsc": "()I"}, {"nme": "original<PERSON>ength", "acc": 1025, "dsc": "()I"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()Ljdk/internal/org/commonmark/node/Text;"}, {"nme": "getCloser", "acc": 1025, "dsc": "()Ljdk/internal/org/commonmark/node/Text;"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/Iterable;", "sig": "(I)Ljava/lang/Iterable<Ljdk/internal/org/commonmark/node/Text;>;"}, {"nme": "getClosers", "acc": 1025, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/Iterable;", "sig": "(I)Ljava/lang/Iterable<Ljdk/internal/org/commonmark/node/Text;>;"}], "flds": []}, "classes/jdk/internal/org/commonmark/node/Emphasis.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/node/Emphasis", "super": "jdk/internal/org/commonmark/node/Node", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "set<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getOpeningDelimiter", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getClosingDelimiter", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "accept", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Visitor;)V"}], "flds": [{"acc": 2, "nme": "delimiter", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/internal/org/commonmark/renderer/Renderer.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/renderer/Renderer", "super": "java/lang/Object", "mthds": [{"nme": "render", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/Node;Ljava/lang/Appendable;)V"}, {"nme": "render", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)Ljava/lang/String;"}], "flds": []}, "classes/jdk/internal/org/commonmark/ext/gfm/tables/internal/TableNodeRenderer.class": {"ver": 68, "acc": 1056, "nme": "jdk/internal/org/commonmark/ext/gfm/tables/internal/TableNodeRenderer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "getNodeTypes", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljava/lang/Class<+Ljdk/internal/org/commonmark/node/Node;>;>;"}, {"nme": "render", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}, {"nme": "renderBlock", "acc": 1028, "dsc": "(Ljdk/internal/org/commonmark/ext/gfm/tables/TableBlock;)V"}, {"nme": "renderHead", "acc": 1028, "dsc": "(Ljdk/internal/org/commonmark/ext/gfm/tables/TableHead;)V"}, {"nme": "renderBody", "acc": 1028, "dsc": "(Ljdk/internal/org/commonmark/ext/gfm/tables/TableBody;)V"}, {"nme": "renderRow", "acc": 1028, "dsc": "(Ljdk/internal/org/commonmark/ext/gfm/tables/TableRow;)V"}, {"nme": "renderCell", "acc": 1028, "dsc": "(Ljdk/internal/org/commonmark/ext/gfm/tables/TableCell;)V"}], "flds": []}, "classes/jdk/internal/org/commonmark/internal/BlockQuoteParser$Factory.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/BlockQuoteParser$Factory", "super": "jdk/internal/org/commonmark/parser/block/AbstractBlockParserFactory", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "tryStart", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/block/ParserState;Ljdk/internal/org/commonmark/parser/block/MatchedBlockParser;)Ljdk/internal/org/commonmark/parser/block/BlockStart;"}], "flds": []}, "classes/jdk/internal/org/commonmark/renderer/text/TextContentRenderer$TextContentRendererExtension.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/renderer/text/TextContentRenderer$TextContentRendererExtension", "super": "java/lang/Object", "mthds": [{"nme": "extend", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/renderer/text/TextContentRenderer$Builder;)V"}], "flds": []}, "classes/jdk/internal/org/commonmark/renderer/html/HtmlRenderer$RendererContext.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/org/commonmark/renderer/html/HtmlRenderer$RendererContext", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/renderer/html/HtmlRenderer;Ljdk/internal/org/commonmark/renderer/html/HtmlWriter;)V"}, {"nme": "shouldEscapeHtml", "acc": 1, "dsc": "()Z"}, {"nme": "shouldSanitizeUrls", "acc": 1, "dsc": "()Z"}, {"nme": "urlS<PERSON><PERSON>zer", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/renderer/html/UrlSanitizer;"}, {"nme": "encodeUrl", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "extendAttributes", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Node;Ljava/lang/String;Ljava/util/Map;)Ljava/util/Map;", "sig": "(Ljdk/internal/org/commonmark/node/Node;Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "getWriter", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/renderer/html/HtmlWriter;"}, {"nme": "getSoftbreak", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "render", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}, {"nme": "setCustomAttributes", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/node/Node;Ljava/lang/String;Ljava/util/Map;)V", "sig": "(Ljdk/internal/org/commonmark/node/Node;Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}], "flds": [{"acc": 18, "nme": "htmlWriter", "dsc": "Ljdk/internal/org/commonmark/renderer/html/HtmlWriter;"}, {"acc": 18, "nme": "attributeProviders", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/renderer/html/AttributeProvider;>;"}, {"acc": 18, "nme": "nodeRendererMap", "dsc": "Ljdk/internal/org/commonmark/internal/renderer/NodeRendererMap;"}, {"acc": 4112, "nme": "this$0", "dsc": "Ljdk/internal/org/commonmark/renderer/html/HtmlRenderer;"}]}, "classes/jdk/internal/org/commonmark/ext/gfm/tables/internal/TableBlockParser.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/ext/gfm/tables/internal/TableBlockParser", "super": "jdk/internal/org/commonmark/parser/block/AbstractBlockParser", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljava/util/List;Ljdk/internal/org/commonmark/parser/SourceLine;)V", "sig": "(Ljava/util/List<Ljdk/internal/org/commonmark/ext/gfm/tables/internal/TableBlockParser$TableCellInfo;>;Ljdk/internal/org/commonmark/parser/SourceLine;)V"}, {"nme": "canHaveLazyContinuationLines", "acc": 1, "dsc": "()Z"}, {"nme": "getBlock", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/node/Block;"}, {"nme": "tryContinue", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/block/ParserState;)Ljdk/internal/org/commonmark/parser/block/BlockContinue;"}, {"nme": "addLine", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/SourceLine;)V"}, {"nme": "parseInlines", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/InlineParser;)V"}, {"nme": "parseCell", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/parser/SourceLine;ILjdk/internal/org/commonmark/parser/InlineParser;)Ljdk/internal/org/commonmark/ext/gfm/tables/TableCell;"}, {"nme": "split", "acc": 10, "dsc": "(Ljdk/internal/org/commonmark/parser/SourceLine;)Ljava/util/List;", "sig": "(Ljdk/internal/org/commonmark/parser/SourceLine;)Ljava/util/List<Ljdk/internal/org/commonmark/parser/SourceLine;>;"}, {"nme": "parseSeparator", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;)Ljava/util/List;", "sig": "(Ljava/lang/CharSequence;)Ljava/util/List<Ljdk/internal/org/commonmark/ext/gfm/tables/internal/TableBlockParser$TableCellInfo;>;"}, {"nme": "getAlignment", "acc": 10, "dsc": "(ZZ)Ljdk/internal/org/commonmark/ext/gfm/tables/TableCell$Alignment;"}], "flds": [{"acc": 18, "nme": "block", "dsc": "Ljdk/internal/org/commonmark/ext/gfm/tables/TableBlock;"}, {"acc": 18, "nme": "rowLines", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/parser/SourceLine;>;"}, {"acc": 18, "nme": "columns", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/ext/gfm/tables/internal/TableBlockParser$TableCellInfo;>;"}, {"acc": 2, "nme": "canHaveLazyContinuationLines", "dsc": "Z"}]}, "classes/jdk/internal/markdown/MarkdownTransformer$Lower.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/markdown/MarkdownTransformer$Lower", "super": "jdk/internal/org/commonmark/node/AbstractVisitor", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/sun/tools/javac/tree/DocTreeMaker;Ljdk/internal/org/commonmark/node/Node;Ljava/lang/String;ILjava/util/List;Ljava/lang/String;)V", "sig": "(Lcom/sun/tools/javac/tree/DocTreeMaker;Ljdk/internal/org/commonmark/node/Node;Ljava/lang/String;ILjava/util/List<*>;Ljava/lang/String;)V"}, {"nme": "getTrees", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lcom/sun/tools/javac/tree/DCTree;>;"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Link;)V"}, {"nme": "getRefSpan", "acc": 2, "dsc": "(Ljava/lang/String;Ljdk/internal/org/commonmark/node/Link;)[I"}, {"nme": "sourcePosToTreePos", "acc": 2, "dsc": "(I)I"}, {"nme": "visit<PERSON><PERSON><PERSON><PERSON>", "acc": 4, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}, {"nme": "getStartPos", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)I"}, {"nme": "getEndPos", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)I"}, {"nme": "toSourcePos", "acc": 2, "dsc": "(II)I"}, {"nme": "copyTo", "acc": 2, "dsc": "(I)V"}, {"nme": "flushText", "acc": 2, "dsc": "(I)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "m", "dsc": "Lcom/sun/tools/javac/tree/DocTreeMaker;"}, {"acc": 18, "nme": "autorefScheme", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "document", "dsc": "Ljdk/internal/org/commonmark/node/Node;"}, {"acc": 18, "nme": "source", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "sourceLineOffsets", "dsc": "[I"}, {"acc": 18, "nme": "replaceIter", "dsc": "<PERSON><PERSON><PERSON>/util/Iterator;", "sig": "Ljava/util/Iterator<*>;"}, {"acc": 2, "nme": "trees", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lcom/sun/tools/javac/tree/DCTree;>;"}, {"acc": 18, "nme": "text", "dsc": "<PERSON><PERSON><PERSON>/lang/StringBuilder;"}, {"acc": 2, "nme": "mainStartPos", "dsc": "I"}, {"acc": 2, "nme": "copyStartPos", "dsc": "I"}, {"acc": 2, "nme": "replaceAdjustPos", "dsc": "I"}, {"acc": 26, "nme": "lineBreak", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 26, "nme": "NOSPAN", "dsc": "[I"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "classes/jdk/internal/org/commonmark/ext/gfm/tables/TableCell$Alignment.class": {"ver": 68, "acc": 16433, "nme": "jdk/internal/org/commonmark/ext/gfm/tables/TableCell$Alignment", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/internal/org/commonmark/ext/gfm/tables/TableCell$Alignment;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Ljdk/internal/org/commonmark/ext/gfm/tables/TableCell$Alignment;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/internal/org/commonmark/ext/gfm/tables/TableCell$Alignment;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "LEFT", "dsc": "Ljdk/internal/org/commonmark/ext/gfm/tables/TableCell$Alignment;"}, {"acc": 16409, "nme": "CENTER", "dsc": "Ljdk/internal/org/commonmark/ext/gfm/tables/TableCell$Alignment;"}, {"acc": 16409, "nme": "RIGHT", "dsc": "Ljdk/internal/org/commonmark/ext/gfm/tables/TableCell$Alignment;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/internal/org/commonmark/ext/gfm/tables/TableCell$Alignment;"}]}, "classes/jdk/internal/org/commonmark/ext/gfm/tables/internal/TableHtmlNodeRenderer$1.class": {"ver": 68, "acc": 4128, "nme": "jdk/internal/org/commonmark/ext/gfm/tables/internal/TableHtmlNodeRenderer$1", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$jdk$internal$org$commonmark$ext$gfm$tables$TableCell$Alignment", "dsc": "[I"}]}, "classes/jdk/internal/org/commonmark/parser/beta/Scanner.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/parser/beta/Scanner", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/List;II)V", "sig": "(Ljava/util/List<Ljdk/internal/org/commonmark/parser/SourceLine;>;II)V"}, {"nme": "of", "acc": 9, "dsc": "(Ljdk/internal/org/commonmark/parser/SourceLines;)Ljdk/internal/org/commonmark/parser/beta/Scanner;"}, {"nme": "peek", "acc": 1, "dsc": "()C"}, {"nme": "peekCodePoint", "acc": 1, "dsc": "()I"}, {"nme": "peekPreviousCodePoint", "acc": 1, "dsc": "()I"}, {"nme": "hasNext", "acc": 1, "dsc": "()Z"}, {"nme": "next", "acc": 1, "dsc": "()V"}, {"nme": "next", "acc": 1, "dsc": "(C)Z"}, {"nme": "next", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "matchMultiple", "acc": 1, "dsc": "(C)I"}, {"nme": "match", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/text/CharMatcher;)I"}, {"nme": "whitespace", "acc": 1, "dsc": "()I"}, {"nme": "find", "acc": 1, "dsc": "(C)I"}, {"nme": "find", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/text/CharMatcher;)I"}, {"nme": "position", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/parser/beta/Position;"}, {"nme": "setPosition", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/beta/Position;)V"}, {"nme": "getSource", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/beta/Position;Ljdk/internal/org/commonmark/parser/beta/Position;)Ljdk/internal/org/commonmark/parser/SourceLines;"}, {"nme": "setLine", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/parser/SourceLine;)V"}, {"nme": "checkPosition", "acc": 2, "dsc": "(II)V"}], "flds": [{"acc": 25, "nme": "END", "dsc": "C", "val": 0}, {"acc": 18, "nme": "lines", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/parser/SourceLine;>;"}, {"acc": 2, "nme": "lineIndex", "dsc": "I"}, {"acc": 2, "nme": "index", "dsc": "I"}, {"acc": 2, "nme": "line", "dsc": "Ljdk/internal/org/commonmark/parser/SourceLine;"}, {"acc": 2, "nme": "lineLength", "dsc": "I"}]}, "classes/jdk/internal/org/commonmark/ext/gfm/tables/TablesExtension$2.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/org/commonmark/ext/gfm/tables/TablesExtension$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/internal/org/commonmark/ext/gfm/tables/TablesExtension;)V"}, {"nme": "create", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/renderer/text/TextContentNodeRendererContext;)Ljdk/internal/org/commonmark/renderer/NodeRenderer;"}], "flds": []}, "classes/jdk/internal/org/commonmark/internal/Delimiter.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/Delimiter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/util/List;CZZLjdk/internal/org/commonmark/internal/Delimiter;)V", "sig": "(Ljava/util/List<Ljdk/internal/org/commonmark/node/Text;>;CZZLjdk/internal/org/commonmark/internal/Delimiter;)V"}, {"nme": "canOpen", "acc": 1, "dsc": "()Z"}, {"nme": "canClose", "acc": 1, "dsc": "()Z"}, {"nme": "length", "acc": 1, "dsc": "()I"}, {"nme": "original<PERSON>ength", "acc": 1, "dsc": "()I"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/node/Text;"}, {"nme": "getCloser", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/node/Text;"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/Iterable;", "sig": "(I)Ljava/lang/Iterable<Ljdk/internal/org/commonmark/node/Text;>;"}, {"nme": "getClosers", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/Iterable;", "sig": "(I)Ljava/lang/Iterable<Ljdk/internal/org/commonmark/node/Text;>;"}], "flds": [{"acc": 17, "nme": "characters", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/node/Text;>;"}, {"acc": 17, "nme": "delimiterChar", "dsc": "C"}, {"acc": 18, "nme": "original<PERSON>ength", "dsc": "I"}, {"acc": 18, "nme": "canOpen", "dsc": "Z"}, {"acc": 18, "nme": "canClose", "dsc": "Z"}, {"acc": 1, "nme": "previous", "dsc": "Ljdk/internal/org/commonmark/internal/Delimiter;"}, {"acc": 1, "nme": "next", "dsc": "Ljdk/internal/org/commonmark/internal/Delimiter;"}]}, "classes/jdk/internal/org/commonmark/internal/util/Parsing.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/util/Parsing", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "columnsToNextTabStop", "acc": 9, "dsc": "(I)I"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 9, "nme": "CODE_BLOCK_INDENT", "dsc": "I"}]}, "classes/jdk/internal/org/commonmark/text/AsciiMatcher.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/text/AsciiMatcher", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/text/AsciiMatcher$Builder;)V"}, {"nme": "matches", "acc": 1, "dsc": "(C)Z"}, {"nme": "newBuilder", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/text/AsciiMatcher$Builder;"}, {"nme": "builder", "acc": 9, "dsc": "()Ljdk/internal/org/commonmark/text/AsciiMatcher$Builder;"}, {"nme": "builder", "acc": 9, "dsc": "(Ljdk/internal/org/commonmark/text/AsciiMatcher;)Ljdk/internal/org/commonmark/text/AsciiMatcher$Builder;"}], "flds": [{"acc": 18, "nme": "set", "dsc": "Ljava/util/BitSet;"}]}, "classes/jdk/internal/org/commonmark/internal/util/CharMatcher.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/internal/util/CharMatcher", "super": "java/lang/Object", "mthds": [{"nme": "matches", "acc": 1025, "dsc": "(C)Z"}], "flds": []}, "classes/jdk/internal/org/commonmark/node/Heading.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/node/Heading", "super": "jdk/internal/org/commonmark/node/Block", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "accept", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Visitor;)V"}, {"nme": "getLevel", "acc": 1, "dsc": "()I"}, {"nme": "setLevel", "acc": 1, "dsc": "(I)V"}], "flds": [{"acc": 2, "nme": "level", "dsc": "I"}]}, "classes/jdk/internal/org/commonmark/node/Nodes$NodeIterable.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/org/commonmark/node/Nodes$NodeIterable", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/node/Node;Ljdk/internal/org/commonmark/node/Node;)V"}, {"nme": "iterator", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Iterator;", "sig": "()Ljava/util/Iterator<Ljdk/internal/org/commonmark/node/Node;>;"}], "flds": [{"acc": 18, "nme": "first", "dsc": "Ljdk/internal/org/commonmark/node/Node;"}, {"acc": 18, "nme": "end", "dsc": "Ljdk/internal/org/commonmark/node/Node;"}]}, "classes/jdk/internal/markdown/MarkdownTransformer$1.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/markdown/MarkdownTransformer$1", "super": "com/sun/source/util/DocTreeScanner", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "scan", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;<PERSON><PERSON><PERSON>/lang/Void;)<PERSON><PERSON><PERSON>/lang/<PERSON>an;", "sig": "(Ljava/lang/Iterable<+Lcom/sun/source/doctree/DocTree;>;Ljava/lang/Void;)Ljava/lang/Boolean;"}, {"nme": "scan", "acc": 1, "dsc": "(Lcom/sun/source/doctree/DocTree;Ljava/lang/Void;)Ljava/lang/<PERSON>an;"}, {"nme": "reduce", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/<PERSON>;<PERSON><PERSON><PERSON>/lang/<PERSON>;)<PERSON><PERSON><PERSON>/lang/<PERSON>an;"}, {"nme": "reduce", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "scan", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "scan", "acc": 4161, "dsc": "(Lcom/sun/source/doctree/DocTree;Ljava/lang/Object;)Ljava/lang/Object;"}], "flds": []}, "classes/jdk/internal/org/commonmark/renderer/text/TextContentRenderer.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/renderer/text/TextContentRenderer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/renderer/text/TextContentRenderer$Builder;)V"}, {"nme": "builder", "acc": 9, "dsc": "()Ljdk/internal/org/commonmark/renderer/text/TextContentRenderer$Builder;"}, {"nme": "render", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Node;Ljava/lang/Appendable;)V"}, {"nme": "render", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)Ljava/lang/String;"}], "flds": [{"acc": 18, "nme": "stripNewlines", "dsc": "Z"}, {"acc": 18, "nme": "nodeRendererFactories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/renderer/text/TextContentNodeRendererFactory;>;"}]}, "classes/jdk/internal/org/commonmark/renderer/markdown/CoreMarkdownNodeRenderer$LineBreakVisitor.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/org/commonmark/renderer/markdown/CoreMarkdownNodeRenderer$LineBreakVisitor", "super": "jdk/internal/org/commonmark/node/AbstractVisitor", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "hasLineBreak", "acc": 1, "dsc": "()Z"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/SoftLineBreak;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/HardLineBreak;)V"}], "flds": [{"acc": 2, "nme": "lineBreak", "dsc": "Z"}]}, "classes/jdk/internal/org/commonmark/node/Block.class": {"ver": 68, "acc": 1057, "nme": "jdk/internal/org/commonmark/node/Block", "super": "jdk/internal/org/commonmark/node/Node", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getParent", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/node/Block;"}, {"nme": "setParent", "acc": 4, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}, {"nme": "getParent", "acc": 4161, "dsc": "()Ljdk/internal/org/commonmark/node/Node;"}], "flds": []}, "classes/jdk/internal/org/commonmark/internal/DocumentParser.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/DocumentParser", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/util/List;Ljdk/internal/org/commonmark/parser/InlineParserFactory;Ljava/util/List;Ljdk/internal/org/commonmark/parser/IncludeSourceSpans;)V", "sig": "(Ljava/util/List<Ljdk/internal/org/commonmark/parser/block/BlockParserFactory;>;Ljdk/internal/org/commonmark/parser/InlineParserFactory;Ljava/util/List<Ljdk/internal/org/commonmark/parser/delimiter/DelimiterProcessor;>;Ljdk/internal/org/commonmark/parser/IncludeSourceSpans;)V"}, {"nme": "getDefaultBlockParserTypes", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljava/lang/Class<+Ljdk/internal/org/commonmark/node/Block;>;>;"}, {"nme": "calculateBlockParserFactories", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/util/Set;)<PERSON>java/util/List;", "sig": "(Ljava/util/List<Ljdk/internal/org/commonmark/parser/block/BlockParserFactory;>;Ljava/util/Set<Ljava/lang/Class<+Ljdk/internal/org/commonmark/node/Block;>;>;)Ljava/util/List<Ljdk/internal/org/commonmark/parser/block/BlockParserFactory;>;"}, {"nme": "checkEnabledBlockTypes", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(Ljava/util/Set<Ljava/lang/Class<+Ljdk/internal/org/commonmark/node/Block;>;>;)V"}, {"nme": "parse", "acc": 1, "dsc": "(Ljava/lang/String;)Ljdk/internal/org/commonmark/node/Document;"}, {"nme": "parse", "acc": 1, "dsc": "(Ljava/io/Reader;)Ljdk/internal/org/commonmark/node/Document;", "exs": ["java/io/IOException"]}, {"nme": "getLine", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/parser/SourceLine;"}, {"nme": "getIndex", "acc": 1, "dsc": "()I"}, {"nme": "getNextNonSpaceIndex", "acc": 1, "dsc": "()I"}, {"nme": "getColumn", "acc": 1, "dsc": "()I"}, {"nme": "getIndent", "acc": 1, "dsc": "()I"}, {"nme": "isBlank", "acc": 1, "dsc": "()Z"}, {"nme": "getActiveBlockParser", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/parser/block/BlockParser;"}, {"nme": "parseLine", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;)V"}, {"nme": "setLine", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;)V"}, {"nme": "findNextNonSpace", "acc": 2, "dsc": "()V"}, {"nme": "setNewIndex", "acc": 2, "dsc": "(I)V"}, {"nme": "setNewColumn", "acc": 2, "dsc": "(I)V"}, {"nme": "advance", "acc": 2, "dsc": "()V"}, {"nme": "addLine", "acc": 2, "dsc": "()V"}, {"nme": "addSourceSpans", "acc": 2, "dsc": "()V"}, {"nme": "findBlockStart", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/parser/block/BlockParser;)Ljdk/internal/org/commonmark/internal/BlockStartImpl;"}, {"nme": "finalize", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/parser/block/BlockParser;)V"}, {"nme": "addDefinitionsFrom", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/internal/ParagraphParser;)V"}, {"nme": "processInlines", "acc": 2, "dsc": "()V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/internal/DocumentParser$OpenBlockParser;)V"}, {"nme": "activateBlockParser", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/internal/DocumentParser$OpenBlockParser;)V"}, {"nme": "deactivateBlockParser", "acc": 2, "dsc": "()Ljdk/internal/org/commonmark/internal/DocumentParser$OpenBlockParser;"}, {"nme": "prepareActiveBlockParserForReplacement", "acc": 2, "dsc": "()Ljdk/internal/org/commonmark/node/Block;"}, {"nme": "finalizeAndProcess", "acc": 2, "dsc": "()Ljdk/internal/org/commonmark/node/Document;"}, {"nme": "closeBlockParsers", "acc": 2, "dsc": "(I)V"}, {"nme": "prepareLine", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;)<PERSON>java/lang/CharSequence;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CORE_FACTORY_TYPES", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/Class<+Ljdk/internal/org/commonmark/node/Block;>;>;"}, {"acc": 26, "nme": "NODES_TO_CORE_FACTORIES", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Class<+Ljdk/internal/org/commonmark/node/Block;>;Ljdk/internal/org/commonmark/parser/block/BlockParserFactory;>;"}, {"acc": 2, "nme": "line", "dsc": "Ljdk/internal/org/commonmark/parser/SourceLine;"}, {"acc": 2, "nme": "lineIndex", "dsc": "I"}, {"acc": 2, "nme": "index", "dsc": "I"}, {"acc": 2, "nme": "column", "dsc": "I"}, {"acc": 2, "nme": "columnIsInTab", "dsc": "Z"}, {"acc": 2, "nme": "nextNonSpace", "dsc": "I"}, {"acc": 2, "nme": "nextNonSpaceColumn", "dsc": "I"}, {"acc": 2, "nme": "indent", "dsc": "I"}, {"acc": 2, "nme": "blank", "dsc": "Z"}, {"acc": 18, "nme": "blockParserFactories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/parser/block/BlockParserFactory;>;"}, {"acc": 18, "nme": "inlineParserFactory", "dsc": "Ljdk/internal/org/commonmark/parser/InlineParserFactory;"}, {"acc": 18, "nme": "delimiterProcessors", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/parser/delimiter/DelimiterProcessor;>;"}, {"acc": 18, "nme": "includeSourceSpans", "dsc": "Ljdk/internal/org/commonmark/parser/IncludeSourceSpans;"}, {"acc": 18, "nme": "documentBlockParser", "dsc": "Ljdk/internal/org/commonmark/internal/DocumentBlockParser;"}, {"acc": 18, "nme": "definitions", "dsc": "Ljdk/internal/org/commonmark/internal/LinkReferenceDefinitions;"}, {"acc": 18, "nme": "openBlockParsers", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/internal/DocumentParser$OpenBlockParser;>;"}, {"acc": 18, "nme": "allBlockParsers", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/parser/block/BlockParser;>;"}]}, "classes/jdk/internal/org/commonmark/internal/ListBlockParser$ListData.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/org/commonmark/internal/ListBlockParser$ListData", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/internal/org/commonmark/node/ListBlock;I)V"}], "flds": [{"acc": 16, "nme": "listBlock", "dsc": "Ljdk/internal/org/commonmark/node/ListBlock;"}, {"acc": 16, "nme": "contentColumn", "dsc": "I"}]}, "classes/jdk/internal/org/commonmark/internal/LinkReferenceDefinitionParser$State.class": {"ver": 68, "acc": 16432, "nme": "jdk/internal/org/commonmark/internal/LinkReferenceDefinitionParser$State", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/internal/org/commonmark/internal/LinkReferenceDefinitionParser$State;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Ljdk/internal/org/commonmark/internal/LinkReferenceDefinitionParser$State;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/internal/org/commonmark/internal/LinkReferenceDefinitionParser$State;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "START_DEFINITION", "dsc": "Ljdk/internal/org/commonmark/internal/LinkReferenceDefinitionParser$State;"}, {"acc": 16409, "nme": "LABEL", "dsc": "Ljdk/internal/org/commonmark/internal/LinkReferenceDefinitionParser$State;"}, {"acc": 16409, "nme": "DESTINATION", "dsc": "Ljdk/internal/org/commonmark/internal/LinkReferenceDefinitionParser$State;"}, {"acc": 16409, "nme": "START_TITLE", "dsc": "Ljdk/internal/org/commonmark/internal/LinkReferenceDefinitionParser$State;"}, {"acc": 16409, "nme": "TITLE", "dsc": "Ljdk/internal/org/commonmark/internal/LinkReferenceDefinitionParser$State;"}, {"acc": 16409, "nme": "PARAGRAPH", "dsc": "Ljdk/internal/org/commonmark/internal/LinkReferenceDefinitionParser$State;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/internal/org/commonmark/internal/LinkReferenceDefinitionParser$State;"}]}, "classes/jdk/internal/org/commonmark/internal/ParagraphParser.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/ParagraphParser", "super": "jdk/internal/org/commonmark/parser/block/AbstractBlockParser", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "canHaveLazyContinuationLines", "acc": 1, "dsc": "()Z"}, {"nme": "getBlock", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/node/Block;"}, {"nme": "tryContinue", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/block/ParserState;)Ljdk/internal/org/commonmark/parser/block/BlockContinue;"}, {"nme": "addLine", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/SourceLine;)V"}, {"nme": "addSourceSpan", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/SourceSpan;)V"}, {"nme": "closeBlock", "acc": 1, "dsc": "()V"}, {"nme": "parseInlines", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/InlineParser;)V"}, {"nme": "getParagraphLines", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/parser/SourceLines;"}, {"nme": "getDefinitions", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljdk/internal/org/commonmark/node/LinkReferenceDefinition;>;"}], "flds": [{"acc": 18, "nme": "block", "dsc": "Ljdk/internal/org/commonmark/node/Paragraph;"}, {"acc": 18, "nme": "linkReferenceDefinitionParser", "dsc": "Ljdk/internal/org/commonmark/internal/LinkReferenceDefinitionParser;"}]}, "classes/jdk/internal/org/commonmark/renderer/html/HtmlRenderer$1.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/org/commonmark/renderer/html/HtmlRenderer$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/internal/org/commonmark/renderer/html/HtmlRenderer;)V"}, {"nme": "create", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/renderer/html/HtmlNodeRendererContext;)Ljdk/internal/org/commonmark/renderer/NodeRenderer;"}], "flds": []}, "classes/jdk/internal/org/commonmark/internal/inline/ParsedInlineImpl.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/inline/ParsedInlineImpl", "super": "jdk/internal/org/commonmark/internal/inline/ParsedInline", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/internal/org/commonmark/node/Node;Ljdk/internal/org/commonmark/parser/beta/Position;)V"}, {"nme": "getNode", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/node/Node;"}, {"nme": "getPosition", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/parser/beta/Position;"}], "flds": [{"acc": 18, "nme": "node", "dsc": "Ljdk/internal/org/commonmark/node/Node;"}, {"acc": 18, "nme": "position", "dsc": "Ljdk/internal/org/commonmark/parser/beta/Position;"}]}, "classes/jdk/internal/org/commonmark/renderer/markdown/CoreMarkdownNodeRenderer$BulletListHolder.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/org/commonmark/renderer/markdown/CoreMarkdownNodeRenderer$BulletListHolder", "super": "jdk/internal/org/commonmark/renderer/markdown/CoreMarkdownNodeRenderer$ListHolder", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/renderer/markdown/CoreMarkdownNodeRenderer$ListHolder;Ljdk/internal/org/commonmark/node/BulletList;)V"}], "flds": [{"acc": 16, "nme": "marker", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/internal/org/commonmark/renderer/markdown/MarkdownNodeRendererFactory.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/renderer/markdown/MarkdownNodeRendererFactory", "super": "java/lang/Object", "mthds": [{"nme": "create", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/renderer/markdown/MarkdownNodeRendererContext;)Ljdk/internal/org/commonmark/renderer/NodeRenderer;"}, {"nme": "getSpecialCharacters", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()L<PERSON><PERSON>/util/Set<Ljava/lang/Character;>;"}], "flds": []}, "classes/jdk/internal/org/commonmark/internal/LinkReferenceDefinitions.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/LinkReferenceDefinitions", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "add", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/LinkReferenceDefinition;)V"}, {"nme": "get", "acc": 1, "dsc": "(Ljava/lang/String;)Ljdk/internal/org/commonmark/node/LinkReferenceDefinition;"}], "flds": [{"acc": 18, "nme": "definitions", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljdk/internal/org/commonmark/node/LinkReferenceDefinition;>;"}]}, "classes/jdk/internal/org/commonmark/parser/InlineParserContext.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/parser/InlineParserContext", "super": "java/lang/Object", "mthds": [{"nme": "getCustomDelimiterProcessors", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljdk/internal/org/commonmark/parser/delimiter/DelimiterProcessor;>;"}, {"nme": "getLinkReferenceDefinition", "acc": 1025, "dsc": "(Ljava/lang/String;)Ljdk/internal/org/commonmark/node/LinkReferenceDefinition;"}], "flds": []}, "classes/jdk/internal/org/commonmark/internal/inline/Position.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/inline/Position", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(II)V"}], "flds": [{"acc": 16, "nme": "lineIndex", "dsc": "I"}, {"acc": 16, "nme": "index", "dsc": "I"}]}, "classes/jdk/internal/org/commonmark/parser/InlineParser.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/parser/InlineParser", "super": "java/lang/Object", "mthds": [{"nme": "parse", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/parser/SourceLines;Ljdk/internal/org/commonmark/node/Node;)V"}], "flds": []}, "classes/jdk/internal/org/commonmark/internal/inline/EmphasisDelimiterProcessor.class": {"ver": 68, "acc": 1057, "nme": "jdk/internal/org/commonmark/internal/inline/EmphasisDelimiterProcessor", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(C)V"}, {"nme": "getOpeningCharacter", "acc": 1, "dsc": "()C"}, {"nme": "getClosingCharacter", "acc": 1, "dsc": "()C"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()I"}, {"nme": "process", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/delimiter/DelimiterRun;Ljdk/internal/org/commonmark/parser/delimiter/DelimiterRun;)I"}], "flds": [{"acc": 18, "nme": "delimiterChar", "dsc": "C"}]}, "classes/jdk/internal/org/commonmark/node/Link.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/node/Link", "super": "jdk/internal/org/commonmark/node/Node", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "accept", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Visitor;)V"}, {"nme": "getDestination", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setDestination", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getTitle", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setTitle", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "toStringAttributes", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "destination", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "title", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/internal/org/commonmark/internal/DocumentParser$OpenBlockParser.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/org/commonmark/internal/DocumentParser$OpenBlockParser", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/internal/org/commonmark/parser/block/BlockParser;I)V"}], "flds": [{"acc": 18, "nme": "block<PERSON><PERSON><PERSON>", "dsc": "Ljdk/internal/org/commonmark/parser/block/BlockParser;"}, {"acc": 2, "nme": "sourceIndex", "dsc": "I"}]}, "classes/jdk/internal/org/commonmark/text/AsciiMatcher$Builder.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/text/AsciiMatcher$Builder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(L<PERSON>va/util/BitSet;)V"}, {"nme": "c", "acc": 1, "dsc": "(C)Ljdk/internal/org/commonmark/text/AsciiMatcher$Builder;"}, {"nme": "anyOf", "acc": 1, "dsc": "(Ljava/lang/String;)Ljdk/internal/org/commonmark/text/AsciiMatcher$Builder;"}, {"nme": "anyOf", "acc": 1, "dsc": "(Ljava/util/Set;)Ljdk/internal/org/commonmark/text/AsciiMatcher$Builder;", "sig": "(Ljava/util/Set<Ljava/lang/Character;>;)Ljdk/internal/org/commonmark/text/AsciiMatcher$Builder;"}, {"nme": "range", "acc": 1, "dsc": "(CC)Ljdk/internal/org/commonmark/text/AsciiMatcher$Builder;"}, {"nme": "build", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/text/AsciiMatcher;"}], "flds": [{"acc": 18, "nme": "set", "dsc": "Ljava/util/BitSet;"}]}, "classes/jdk/internal/org/commonmark/parser/Parser$Builder$1.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/org/commonmark/parser/Parser$Builder$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/internal/org/commonmark/parser/Parser$Builder;)V"}, {"nme": "create", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/InlineParserContext;)Ljdk/internal/org/commonmark/parser/InlineParser;"}], "flds": []}, "classes/jdk/internal/org/commonmark/parser/Parser$ParserExtension.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/parser/Parser$ParserExtension", "super": "java/lang/Object", "mthds": [{"nme": "extend", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/parser/Parser$Builder;)V"}], "flds": []}, "classes/jdk/internal/org/commonmark/internal/inline/EntityInlineParser.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/inline/EntityInlineParser", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "try<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/internal/inline/InlineParserState;)Ljdk/internal/org/commonmark/internal/inline/ParsedInline;"}, {"nme": "entity", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/parser/beta/Scanner;Ljdk/internal/org/commonmark/parser/beta/Position;)Ljdk/internal/org/commonmark/internal/inline/ParsedInline;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "hex", "dsc": "Ljdk/internal/org/commonmark/text/AsciiMatcher;"}, {"acc": 26, "nme": "dec", "dsc": "Ljdk/internal/org/commonmark/text/AsciiMatcher;"}, {"acc": 26, "nme": "entityStart", "dsc": "Ljdk/internal/org/commonmark/text/AsciiMatcher;"}, {"acc": 26, "nme": "entityContinue", "dsc": "Ljdk/internal/org/commonmark/text/AsciiMatcher;"}]}, "classes/jdk/internal/org/commonmark/node/Node.class": {"ver": 68, "acc": 1057, "nme": "jdk/internal/org/commonmark/node/Node", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "accept", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/Visitor;)V"}, {"nme": "getNext", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/node/Node;"}, {"nme": "getPrevious", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/node/Node;"}, {"nme": "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/node/Node;"}, {"nme": "getLastChild", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/node/Node;"}, {"nme": "getParent", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/node/Node;"}, {"nme": "setParent", "acc": 4, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}, {"nme": "append<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}, {"nme": "prepend<PERSON>hild", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}, {"nme": "unlink", "acc": 1, "dsc": "()V"}, {"nme": "insertAfter", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}, {"nme": "insertBefore", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}, {"nme": "getSourceSpans", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljdk/internal/org/commonmark/node/SourceSpan;>;"}, {"nme": "setSourceSpans", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Ljdk/internal/org/commonmark/node/SourceSpan;>;)V"}, {"nme": "addSourceSpan", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/SourceSpan;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toStringAttributes", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "parent", "dsc": "Ljdk/internal/org/commonmark/node/Node;"}, {"acc": 2, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "Ljdk/internal/org/commonmark/node/Node;"}, {"acc": 2, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "Ljdk/internal/org/commonmark/node/Node;"}, {"acc": 2, "nme": "prev", "dsc": "Ljdk/internal/org/commonmark/node/Node;"}, {"acc": 2, "nme": "next", "dsc": "Ljdk/internal/org/commonmark/node/Node;"}, {"acc": 2, "nme": "sourceSpans", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/node/SourceSpan;>;"}]}, "classes/jdk/internal/org/commonmark/renderer/markdown/MarkdownRenderer$Builder.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/renderer/markdown/MarkdownRenderer$Builder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "build", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/renderer/markdown/MarkdownRenderer;"}, {"nme": "nodeRendererFactory", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/renderer/markdown/MarkdownNodeRendererFactory;)Ljdk/internal/org/commonmark/renderer/markdown/MarkdownRenderer$Builder;"}, {"nme": "extensions", "acc": 1, "dsc": "(Ljava/lang/Iterable;)Ljdk/internal/org/commonmark/renderer/markdown/MarkdownRenderer$Builder;", "sig": "(Ljava/lang/Iterable<+Ljdk/internal/org/commonmark/Extension;>;)Ljdk/internal/org/commonmark/renderer/markdown/MarkdownRenderer$Builder;"}], "flds": [{"acc": 18, "nme": "nodeRendererFactories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/renderer/markdown/MarkdownNodeRendererFactory;>;"}]}, "classes/jdk/internal/org/commonmark/renderer/html/HtmlNodeRendererContext.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/renderer/html/HtmlNodeRendererContext", "super": "java/lang/Object", "mthds": [{"nme": "encodeUrl", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "extendAttributes", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/Node;Ljava/lang/String;Ljava/util/Map;)Ljava/util/Map;", "sig": "(Ljdk/internal/org/commonmark/node/Node;Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "getWriter", "acc": 1025, "dsc": "()Ljdk/internal/org/commonmark/renderer/html/HtmlWriter;"}, {"nme": "getSoftbreak", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "render", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}, {"nme": "shouldEscapeHtml", "acc": 1025, "dsc": "()Z"}, {"nme": "shouldSanitizeUrls", "acc": 1025, "dsc": "()Z"}, {"nme": "urlS<PERSON><PERSON>zer", "acc": 1025, "dsc": "()Ljdk/internal/org/commonmark/renderer/html/UrlSanitizer;"}], "flds": []}, "classes/jdk/internal/org/commonmark/internal/BlockContinueImpl.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/BlockContinueImpl", "super": "jdk/internal/org/commonmark/parser/block/BlockContinue", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(IIZ)V"}, {"nme": "getNewIndex", "acc": 1, "dsc": "()I"}, {"nme": "getNewColumn", "acc": 1, "dsc": "()I"}, {"nme": "isFinalize", "acc": 1, "dsc": "()Z"}], "flds": [{"acc": 18, "nme": "newIndex", "dsc": "I"}, {"acc": 18, "nme": "newColumn", "dsc": "I"}, {"acc": 18, "nme": "finalize", "dsc": "Z"}]}, "classes/jdk/internal/org/commonmark/Extension.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/Extension", "super": "java/lang/Object", "mthds": [], "flds": []}, "classes/jdk/internal/org/commonmark/node/Nodes$NodeIterator.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/org/commonmark/node/Nodes$NodeIterator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/node/Node;Ljdk/internal/org/commonmark/node/Node;)V"}, {"nme": "hasNext", "acc": 1, "dsc": "()Z"}, {"nme": "next", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/node/Node;"}, {"nme": "remove", "acc": 1, "dsc": "()V"}, {"nme": "next", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 2, "nme": "node", "dsc": "Ljdk/internal/org/commonmark/node/Node;"}, {"acc": 18, "nme": "end", "dsc": "Ljdk/internal/org/commonmark/node/Node;"}]}, "classes/jdk/internal/org/commonmark/parser/Parser$Builder.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/parser/Parser$Builder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "build", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/parser/Parser;"}, {"nme": "extensions", "acc": 1, "dsc": "(Ljava/lang/Iterable;)Ljdk/internal/org/commonmark/parser/Parser$Builder;", "sig": "(Ljava/lang/Iterable<+Ljdk/internal/org/commonmark/Extension;>;)Ljdk/internal/org/commonmark/parser/Parser$Builder;"}, {"nme": "enabledBlockTypes", "acc": 1, "dsc": "(Ljava/util/Set;)Ljdk/internal/org/commonmark/parser/Parser$Builder;", "sig": "(Ljava/util/Set<Ljava/lang/Class<+Ljdk/internal/org/commonmark/node/Block;>;>;)Ljdk/internal/org/commonmark/parser/Parser$Builder;"}, {"nme": "includeSourceSpans", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/IncludeSourceSpans;)Ljdk/internal/org/commonmark/parser/Parser$Builder;"}, {"nme": "customBlockParserFactory", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/block/BlockParserFactory;)Ljdk/internal/org/commonmark/parser/Parser$Builder;"}, {"nme": "customDelimiterProcessor", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/delimiter/DelimiterProcessor;)Ljdk/internal/org/commonmark/parser/Parser$Builder;"}, {"nme": "postProcessor", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/PostProcessor;)Ljdk/internal/org/commonmark/parser/Parser$Builder;"}, {"nme": "inlineParserFactory", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/InlineParserFactory;)Ljdk/internal/org/commonmark/parser/Parser$Builder;"}, {"nme": "getInlineParserFactory", "acc": 2, "dsc": "()Ljdk/internal/org/commonmark/parser/InlineParserFactory;"}], "flds": [{"acc": 18, "nme": "blockParserFactories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/parser/block/BlockParserFactory;>;"}, {"acc": 18, "nme": "delimiterProcessors", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/parser/delimiter/DelimiterProcessor;>;"}, {"acc": 18, "nme": "postProcessors", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/parser/PostProcessor;>;"}, {"acc": 2, "nme": "enabledBlockTypes", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/Class<+Ljdk/internal/org/commonmark/node/Block;>;>;"}, {"acc": 2, "nme": "inlineParserFactory", "dsc": "Ljdk/internal/org/commonmark/parser/InlineParserFactory;"}, {"acc": 2, "nme": "includeSourceSpans", "dsc": "Ljdk/internal/org/commonmark/parser/IncludeSourceSpans;"}]}, "classes/jdk/internal/org/commonmark/ext/gfm/tables/TablesExtension$1.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/org/commonmark/ext/gfm/tables/TablesExtension$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/internal/org/commonmark/ext/gfm/tables/TablesExtension;)V"}, {"nme": "create", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/renderer/html/HtmlNodeRendererContext;)Ljdk/internal/org/commonmark/renderer/NodeRenderer;"}], "flds": []}, "classes/jdk/internal/org/commonmark/internal/util/Escaping.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/util/Escaping", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "escapeHtml", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "unescapeString", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "percentEncodeUrl", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "normalize<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "replaceAll", "acc": 10, "dsc": "(Ljava/util/regex/Pattern;Ljava/lang/String;Ljdk/internal/org/commonmark/internal/util/Escaping$Replacer;)Ljava/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "ESCAPABLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "[!\"#$%&'()*+,./:;<=>?@\\[\\\\\\]^_`{|}~-]"}, {"acc": 25, "nme": "ENTITY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "&(?:#x[a-f0-9]{1,6}|#[0-9]{1,7}|[a-z][a-z0-9]{1,31});"}, {"acc": 26, "nme": "BACKSLASH_OR_AMP", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 26, "nme": "ENTITY_OR_ESCAPED_CHAR", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 26, "nme": "ESCAPE_IN_URI", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 26, "nme": "HEX_DIGITS", "dsc": "[C"}, {"acc": 26, "nme": "WHITESPACE", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 26, "nme": "UNESCAPE_REPLACER", "dsc": "Ljdk/internal/org/commonmark/internal/util/Escaping$Replacer;"}, {"acc": 26, "nme": "URI_REPLACER", "dsc": "Ljdk/internal/org/commonmark/internal/util/Escaping$Replacer;"}]}, "classes/jdk/internal/org/commonmark/node/FencedCodeBlock.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/node/FencedCodeBlock", "super": "jdk/internal/org/commonmark/node/Block", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "accept", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Visitor;)V"}, {"nme": "getFenceCharacter", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setFenceCharacter", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getOpeningFenceLength", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Integer;"}, {"nme": "setOpening<PERSON>ence<PERSON>ength", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Integer;)V"}, {"nme": "getClosingFenceLength", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Integer;"}, {"nme": "setClosing<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Integer;)V"}, {"nme": "getFenceIndent", "acc": 1, "dsc": "()I"}, {"nme": "setFenceIndent", "acc": 1, "dsc": "(I)V"}, {"nme": "getInfo", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setInfo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<PERSON><PERSON><PERSON>al", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLiteral", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getFenceChar", "acc": 131073, "dsc": "()C", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "setFenceChar", "acc": 131073, "dsc": "(C)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 131073, "dsc": "()I", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 131073, "dsc": "(I)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "checkFenceLengths", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Integer;<PERSON><PERSON><PERSON>/lang/Integer;)V"}], "flds": [{"acc": 2, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "opening<PERSON><PERSON><PERSON><PERSON><PERSON>", "dsc": "<PERSON><PERSON><PERSON>/lang/Integer;"}, {"acc": 2, "nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dsc": "<PERSON><PERSON><PERSON>/lang/Integer;"}, {"acc": 2, "nme": "fenceIndent", "dsc": "I"}, {"acc": 2, "nme": "info", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "literal", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/internal/org/commonmark/renderer/markdown/MarkdownNodeRendererContext.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/renderer/markdown/MarkdownNodeRendererContext", "super": "java/lang/Object", "mthds": [{"nme": "getWriter", "acc": 1025, "dsc": "()Ljdk/internal/org/commonmark/renderer/markdown/MarkdownWriter;"}, {"nme": "render", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}, {"nme": "getSpecialCharacters", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()L<PERSON><PERSON>/util/Set<Ljava/lang/Character;>;"}], "flds": []}, "classes/jdk/internal/org/commonmark/parser/InlineParserFactory.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/parser/InlineParserFactory", "super": "java/lang/Object", "mthds": [{"nme": "create", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/parser/InlineParserContext;)Ljdk/internal/org/commonmark/parser/InlineParser;"}], "flds": []}, "classes/jdk/internal/org/commonmark/parser/beta/Position.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/parser/beta/Position", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(II)V"}], "flds": [{"acc": 16, "nme": "lineIndex", "dsc": "I"}, {"acc": 16, "nme": "index", "dsc": "I"}]}, "classes/jdk/internal/org/commonmark/internal/InlineParserContextImpl.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/InlineParserContextImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/util/List;Ljdk/internal/org/commonmark/internal/LinkReferenceDefinitions;)V", "sig": "(Ljava/util/List<Ljdk/internal/org/commonmark/parser/delimiter/DelimiterProcessor;>;Ljdk/internal/org/commonmark/internal/LinkReferenceDefinitions;)V"}, {"nme": "getCustomDelimiterProcessors", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljdk/internal/org/commonmark/parser/delimiter/DelimiterProcessor;>;"}, {"nme": "getLinkReferenceDefinition", "acc": 1, "dsc": "(Ljava/lang/String;)Ljdk/internal/org/commonmark/node/LinkReferenceDefinition;"}], "flds": [{"acc": 18, "nme": "delimiterProcessors", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/parser/delimiter/DelimiterProcessor;>;"}, {"acc": 18, "nme": "linkReferenceDefinitions", "dsc": "Ljdk/internal/org/commonmark/internal/LinkReferenceDefinitions;"}]}, "classes/jdk/internal/org/commonmark/internal/LinkReferenceDefinitionParser.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/LinkReferenceDefinitionParser", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "parse", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/SourceLine;)V"}, {"nme": "addSourceSpan", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/SourceSpan;)V"}, {"nme": "getParagraphLines", "acc": 0, "dsc": "()Ljdk/internal/org/commonmark/parser/SourceLines;"}, {"nme": "getParagraphSourceSpans", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljdk/internal/org/commonmark/node/SourceSpan;>;"}, {"nme": "getDefinitions", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljdk/internal/org/commonmark/node/LinkReferenceDefinition;>;"}, {"nme": "getState", "acc": 0, "dsc": "()Ljdk/internal/org/commonmark/internal/LinkReferenceDefinitionParser$State;"}, {"nme": "startDefinition", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/parser/beta/Scanner;)Z"}, {"nme": "label", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/parser/beta/Scanner;)Z"}, {"nme": "destination", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/parser/beta/Scanner;)Z"}, {"nme": "startTitle", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/parser/beta/Scanner;)Z"}, {"nme": "title", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/parser/beta/Scanner;)Z"}, {"nme": "finishReference", "acc": 2, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "state", "dsc": "Ljdk/internal/org/commonmark/internal/LinkReferenceDefinitionParser$State;"}, {"acc": 18, "nme": "paragraphLines", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/parser/SourceLine;>;"}, {"acc": 18, "nme": "definitions", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/node/LinkReferenceDefinition;>;"}, {"acc": 18, "nme": "sourceSpans", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/node/SourceSpan;>;"}, {"acc": 2, "nme": "label", "dsc": "<PERSON><PERSON><PERSON>/lang/StringBuilder;"}, {"acc": 2, "nme": "destination", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "titleDelimiter", "dsc": "C"}, {"acc": 2, "nme": "title", "dsc": "<PERSON><PERSON><PERSON>/lang/StringBuilder;"}, {"acc": 2, "nme": "referenceValid", "dsc": "Z"}]}, "classes/jdk/internal/org/commonmark/renderer/text/TextContentRenderer$Builder.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/renderer/text/TextContentRenderer$Builder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "build", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/renderer/text/TextContentRenderer;"}, {"nme": "stripNewlines", "acc": 1, "dsc": "(Z)Ljdk/internal/org/commonmark/renderer/text/TextContentRenderer$Builder;"}, {"nme": "nodeRendererFactory", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/renderer/text/TextContentNodeRendererFactory;)Ljdk/internal/org/commonmark/renderer/text/TextContentRenderer$Builder;"}, {"nme": "extensions", "acc": 1, "dsc": "(Ljava/lang/Iterable;)Ljdk/internal/org/commonmark/renderer/text/TextContentRenderer$Builder;", "sig": "(Ljava/lang/Iterable<+Ljdk/internal/org/commonmark/Extension;>;)Ljdk/internal/org/commonmark/renderer/text/TextContentRenderer$Builder;"}], "flds": [{"acc": 2, "nme": "stripNewlines", "dsc": "Z"}, {"acc": 2, "nme": "nodeRendererFactories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/renderer/text/TextContentNodeRendererFactory;>;"}]}, "classes/jdk/internal/org/commonmark/internal/renderer/text/BulletListHolder.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/renderer/text/BulletListHolder", "super": "jdk/internal/org/commonmark/internal/renderer/text/ListHolder", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/internal/renderer/text/ListHolder;Ljdk/internal/org/commonmark/node/BulletList;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "marker", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/internal/org/commonmark/internal/BlockStartImpl.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/BlockStartImpl", "super": "jdk/internal/org/commonmark/parser/block/BlockStart", "mthds": [{"nme": "<init>", "acc": 129, "dsc": "([Ljdk/internal/org/commonmark/parser/block/BlockParser;)V"}, {"nme": "getBlockParsers", "acc": 1, "dsc": "()[Ljdk/internal/org/commonmark/parser/block/BlockParser;"}, {"nme": "getNewIndex", "acc": 1, "dsc": "()I"}, {"nme": "getNewColumn", "acc": 1, "dsc": "()I"}, {"nme": "isReplaceActiveBlockParser", "acc": 1, "dsc": "()Z"}, {"nme": "atIndex", "acc": 1, "dsc": "(I)Ljdk/internal/org/commonmark/parser/block/BlockStart;"}, {"nme": "atColumn", "acc": 1, "dsc": "(I)Ljdk/internal/org/commonmark/parser/block/BlockStart;"}, {"nme": "replaceActiveBlockParser", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/parser/block/BlockStart;"}], "flds": [{"acc": 18, "nme": "blockParsers", "dsc": "[Ljdk/internal/org/commonmark/parser/block/BlockParser;"}, {"acc": 2, "nme": "newIndex", "dsc": "I"}, {"acc": 2, "nme": "newColumn", "dsc": "I"}, {"acc": 2, "nme": "replaceActiveBlockParser", "dsc": "Z"}]}, "classes/jdk/internal/org/commonmark/internal/InlineParserImpl$DelimiterData.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/org/commonmark/internal/InlineParserImpl$DelimiterData", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/util/List;ZZ)V", "sig": "(Ljava/util/List<Ljdk/internal/org/commonmark/node/Text;>;ZZ)V"}], "flds": [{"acc": 16, "nme": "characters", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/node/Text;>;"}, {"acc": 16, "nme": "canClose", "dsc": "Z"}, {"acc": 16, "nme": "canOpen", "dsc": "Z"}]}, "classes/jdk/internal/org/commonmark/node/SoftLineBreak.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/node/SoftLineBreak", "super": "jdk/internal/org/commonmark/node/Node", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "accept", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Visitor;)V"}], "flds": []}, "classes/jdk/internal/org/commonmark/internal/ListItemParser.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/ListItemParser", "super": "jdk/internal/org/commonmark/parser/block/AbstractBlockParser", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(II)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Z"}, {"nme": "canContain", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Block;)Z"}, {"nme": "getBlock", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/node/Block;"}, {"nme": "tryContinue", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/block/ParserState;)Ljdk/internal/org/commonmark/parser/block/BlockContinue;"}], "flds": [{"acc": 18, "nme": "block", "dsc": "Ljdk/internal/org/commonmark/node/ListItem;"}, {"acc": 2, "nme": "contentIndent", "dsc": "I"}, {"acc": 2, "nme": "hadBlankLine", "dsc": "Z"}]}, "classes/jdk/internal/org/commonmark/node/Code.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/node/Code", "super": "jdk/internal/org/commonmark/node/Node", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "accept", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Visitor;)V"}, {"nme": "<PERSON><PERSON><PERSON>al", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLiteral", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 2, "nme": "literal", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/internal/org/commonmark/renderer/markdown/MarkdownRenderer$MarkdownRendererExtension.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/renderer/markdown/MarkdownRenderer$MarkdownRendererExtension", "super": "java/lang/Object", "mthds": [{"nme": "extend", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/renderer/markdown/MarkdownRenderer$Builder;)V"}], "flds": []}, "classes/jdk/internal/org/commonmark/renderer/markdown/MarkdownRenderer.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/renderer/markdown/MarkdownRenderer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/renderer/markdown/MarkdownRenderer$Builder;)V"}, {"nme": "builder", "acc": 9, "dsc": "()Ljdk/internal/org/commonmark/renderer/markdown/MarkdownRenderer$Builder;"}, {"nme": "render", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Node;Ljava/lang/Appendable;)V"}, {"nme": "render", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)Ljava/lang/String;"}], "flds": [{"acc": 18, "nme": "nodeRendererFactories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/renderer/markdown/MarkdownNodeRendererFactory;>;"}]}, "classes/jdk/internal/org/commonmark/parser/SourceLines.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/parser/SourceLines", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "empty", "acc": 9, "dsc": "()Ljdk/internal/org/commonmark/parser/SourceLines;"}, {"nme": "of", "acc": 9, "dsc": "(Ljdk/internal/org/commonmark/parser/SourceLine;)Ljdk/internal/org/commonmark/parser/SourceLines;"}, {"nme": "of", "acc": 9, "dsc": "(Ljava/util/List;)Ljdk/internal/org/commonmark/parser/SourceLines;", "sig": "(Ljava/util/List<Ljdk/internal/org/commonmark/parser/SourceLine;>;)Ljdk/internal/org/commonmark/parser/SourceLines;"}, {"nme": "addLine", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/SourceLine;)V"}, {"nme": "getLines", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljdk/internal/org/commonmark/parser/SourceLine;>;"}, {"nme": "isEmpty", "acc": 1, "dsc": "()Z"}, {"nme": "get<PERSON>ontent", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getSourceSpans", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljdk/internal/org/commonmark/node/SourceSpan;>;"}], "flds": [{"acc": 18, "nme": "lines", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/parser/SourceLine;>;"}]}, "classes/jdk/internal/org/commonmark/node/Text.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/node/Text", "super": "jdk/internal/org/commonmark/node/Node", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "accept", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Visitor;)V"}, {"nme": "<PERSON><PERSON><PERSON>al", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLiteral", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "toStringAttributes", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "literal", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/internal/org/commonmark/internal/ListBlockParser$Factory.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/ListBlockParser$Factory", "super": "jdk/internal/org/commonmark/parser/block/AbstractBlockParserFactory", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "tryStart", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/block/ParserState;Ljdk/internal/org/commonmark/parser/block/MatchedBlockParser;)Ljdk/internal/org/commonmark/parser/block/BlockStart;"}], "flds": []}, "classes/jdk/internal/org/commonmark/renderer/text/TextContentNodeRendererContext.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/renderer/text/TextContentNodeRendererContext", "super": "java/lang/Object", "mthds": [{"nme": "stripNewlines", "acc": 1025, "dsc": "()Z"}, {"nme": "getWriter", "acc": 1025, "dsc": "()Ljdk/internal/org/commonmark/renderer/text/TextContentWriter;"}, {"nme": "render", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}], "flds": []}, "classes/jdk/internal/org/commonmark/node/Document.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/node/Document", "super": "jdk/internal/org/commonmark/node/Block", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "accept", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Visitor;)V"}], "flds": []}, "classes/jdk/internal/org/commonmark/internal/inline/BackslashInlineParser.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/inline/BackslashInlineParser", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "try<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/internal/inline/InlineParserState;)Ljdk/internal/org/commonmark/internal/inline/ParsedInline;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "ESCAPABLE", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}]}, "classes/jdk/internal/org/commonmark/node/Image.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/node/Image", "super": "jdk/internal/org/commonmark/node/Node", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "accept", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Visitor;)V"}, {"nme": "getDestination", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setDestination", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getTitle", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setTitle", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "toStringAttributes", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "destination", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "title", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/internal/org/commonmark/renderer/text/CoreTextContentNodeRenderer.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/renderer/text/CoreTextContentNodeRenderer", "super": "jdk/internal/org/commonmark/node/AbstractVisitor", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/renderer/text/TextContentNodeRendererContext;)V"}, {"nme": "getNodeTypes", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljava/lang/Class<+Ljdk/internal/org/commonmark/node/Node;>;>;"}, {"nme": "render", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Document;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/BlockQuote;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/BulletList;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Code;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/FencedCodeBlock;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/HardLineBreak;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Heading;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/ThematicBreak;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/HtmlInline;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/HtmlBlock;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Image;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/IndentedCodeBlock;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Link;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/ListItem;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/OrderedList;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Paragraph;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/SoftLineBreak;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Text;)V"}, {"nme": "visit<PERSON><PERSON><PERSON><PERSON>", "acc": 4, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}, {"nme": "writeText", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "writeLink", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/node/Node;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "writeEndOfLineIfNeeded", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/node/Node;Ljava/lang/Character;)V"}, {"nme": "writeEndOfLine", "acc": 2, "dsc": "()V"}], "flds": [{"acc": 20, "nme": "context", "dsc": "Ljdk/internal/org/commonmark/renderer/text/TextContentNodeRendererContext;"}, {"acc": 18, "nme": "textContent", "dsc": "Ljdk/internal/org/commonmark/renderer/text/TextContentWriter;"}, {"acc": 2, "nme": "listHolder", "dsc": "Ljdk/internal/org/commonmark/internal/renderer/text/ListHolder;"}]}, "classes/jdk/internal/org/commonmark/internal/IndentedCodeBlockParser$Factory.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/IndentedCodeBlockParser$Factory", "super": "jdk/internal/org/commonmark/parser/block/AbstractBlockParserFactory", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "tryStart", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/block/ParserState;Ljdk/internal/org/commonmark/parser/block/MatchedBlockParser;)Ljdk/internal/org/commonmark/parser/block/BlockStart;"}], "flds": []}, "classes/jdk/internal/org/commonmark/ext/gfm/tables/TableRow.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/ext/gfm/tables/TableRow", "super": "jdk/internal/org/commonmark/node/CustomNode", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "classes/jdk/internal/org/commonmark/renderer/text/TextContentWriter.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/renderer/text/TextContentWriter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Appendable;)V"}, {"nme": "whitespace", "acc": 1, "dsc": "()V"}, {"nme": "colon", "acc": 1, "dsc": "()V"}, {"nme": "line", "acc": 1, "dsc": "()V"}, {"nme": "writeStripped", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "write", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "write", "acc": 1, "dsc": "(C)V"}, {"nme": "append", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "append", "acc": 2, "dsc": "(C)V"}], "flds": [{"acc": 18, "nme": "buffer", "dsc": "<PERSON><PERSON><PERSON>/lang/Appendable;"}, {"acc": 2, "nme": "lastChar", "dsc": "C"}]}, "classes/jdk/internal/org/commonmark/internal/FencedCodeBlockParser.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/FencedCodeBlockParser", "super": "jdk/internal/org/commonmark/parser/block/AbstractBlockParser", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(CII)V"}, {"nme": "getBlock", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/node/Block;"}, {"nme": "tryContinue", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/block/ParserState;)Ljdk/internal/org/commonmark/parser/block/BlockContinue;"}, {"nme": "addLine", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/SourceLine;)V"}, {"nme": "closeBlock", "acc": 1, "dsc": "()V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 10, "dsc": "(Ljava/lang/CharSequence;II)Ljdk/internal/org/commonmark/internal/FencedCodeBlockParser;"}, {"nme": "tryClosing", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;I)Z"}], "flds": [{"acc": 18, "nme": "block", "dsc": "Ljdk/internal/org/commonmark/node/FencedCodeBlock;"}, {"acc": 18, "nme": "fenceChar", "dsc": "C"}, {"acc": 18, "nme": "opening<PERSON><PERSON><PERSON><PERSON><PERSON>", "dsc": "I"}, {"acc": 2, "nme": "firstLine", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "otherLines", "dsc": "<PERSON><PERSON><PERSON>/lang/StringBuilder;"}]}, "classes/jdk/internal/org/commonmark/internal/ListBlockParser$ListMarkerData.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/org/commonmark/internal/ListBlockParser$ListMarkerData", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/internal/org/commonmark/node/ListBlock;I)V"}], "flds": [{"acc": 16, "nme": "listBlock", "dsc": "Ljdk/internal/org/commonmark/node/ListBlock;"}, {"acc": 16, "nme": "indexAfterMarker", "dsc": "I"}]}, "classes/jdk/internal/org/commonmark/internal/util/AsciiMatcher$Builder.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/util/AsciiMatcher$Builder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(L<PERSON>va/util/BitSet;)V"}, {"nme": "c", "acc": 1, "dsc": "(C)Ljdk/internal/org/commonmark/internal/util/AsciiMatcher$Builder;"}, {"nme": "range", "acc": 1, "dsc": "(CC)Ljdk/internal/org/commonmark/internal/util/AsciiMatcher$Builder;"}, {"nme": "build", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/internal/util/AsciiMatcher;"}], "flds": [{"acc": 18, "nme": "set", "dsc": "Ljava/util/BitSet;"}]}, "classes/jdk/internal/org/commonmark/node/AbstractVisitor.class": {"ver": 68, "acc": 1057, "nme": "jdk/internal/org/commonmark/node/AbstractVisitor", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/BlockQuote;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/BulletList;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Code;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Document;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Emphasis;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/FencedCodeBlock;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/HardLineBreak;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Heading;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/ThematicBreak;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/HtmlInline;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/HtmlBlock;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Image;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/IndentedCodeBlock;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Link;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/ListItem;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/OrderedList;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Paragraph;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/SoftLineBreak;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/StrongEmphasis;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Text;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/LinkReferenceDefinition;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/CustomBlock;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/CustomNode;)V"}, {"nme": "visit<PERSON><PERSON><PERSON><PERSON>", "acc": 4, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}], "flds": []}, "classes/jdk/internal/org/commonmark/internal/FencedCodeBlockParser$Factory.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/FencedCodeBlockParser$Factory", "super": "jdk/internal/org/commonmark/parser/block/AbstractBlockParserFactory", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "tryStart", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/block/ParserState;Ljdk/internal/org/commonmark/parser/block/MatchedBlockParser;)Ljdk/internal/org/commonmark/parser/block/BlockStart;"}], "flds": []}, "classes/jdk/internal/org/commonmark/internal/HeadingParser.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/HeadingParser", "super": "jdk/internal/org/commonmark/parser/block/AbstractBlockParser", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(ILjdk/internal/org/commonmark/parser/SourceLines;)V"}, {"nme": "getBlock", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/node/Block;"}, {"nme": "tryContinue", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/block/ParserState;)Ljdk/internal/org/commonmark/parser/block/BlockContinue;"}, {"nme": "parseInlines", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/InlineParser;)V"}, {"nme": "getAtxHeading", "acc": 10, "dsc": "(Ljdk/internal/org/commonmark/parser/SourceLine;)Ljdk/internal/org/commonmark/internal/HeadingParser;"}, {"nme": "getSetextHeadingLevel", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;I)I"}, {"nme": "isSetextHeadingRest", "acc": 10, "dsc": "(L<PERSON><PERSON>/lang/CharSequence;IC)Z"}], "flds": [{"acc": 18, "nme": "block", "dsc": "Ljdk/internal/org/commonmark/node/Heading;"}, {"acc": 18, "nme": "content", "dsc": "Ljdk/internal/org/commonmark/parser/SourceLines;"}]}, "classes/jdk/internal/org/commonmark/internal/inline/AutolinkInlineParser.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/inline/AutolinkInlineParser", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "try<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/internal/inline/InlineParserState;)Ljdk/internal/org/commonmark/internal/inline/ParsedInline;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "URI", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 26, "nme": "EMAIL", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}]}, "classes/jdk/internal/org/commonmark/internal/StaggeredDelimiterProcessor.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/org/commonmark/internal/StaggeredDelimiterProcessor", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(C)V"}, {"nme": "getOpeningCharacter", "acc": 1, "dsc": "()C"}, {"nme": "getClosingCharacter", "acc": 1, "dsc": "()C"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()I"}, {"nme": "add", "acc": 0, "dsc": "(Ljdk/internal/org/commonmark/parser/delimiter/DelimiterProcessor;)V"}, {"nme": "findProcessor", "acc": 2, "dsc": "(I)Ljdk/internal/org/commonmark/parser/delimiter/DelimiterProcessor;"}, {"nme": "process", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/delimiter/DelimiterRun;Ljdk/internal/org/commonmark/parser/delimiter/DelimiterRun;)I"}], "flds": [{"acc": 18, "nme": "delim", "dsc": "C"}, {"acc": 2, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "I"}, {"acc": 2, "nme": "processors", "dsc": "<PERSON><PERSON><PERSON>/util/LinkedList;", "sig": "Ljava/util/LinkedList<Ljdk/internal/org/commonmark/parser/delimiter/DelimiterProcessor;>;"}]}, "classes/jdk/internal/org/commonmark/parser/block/ParserState.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/parser/block/ParserState", "super": "java/lang/Object", "mthds": [{"nme": "getLine", "acc": 1025, "dsc": "()Ljdk/internal/org/commonmark/parser/SourceLine;"}, {"nme": "getIndex", "acc": 1025, "dsc": "()I"}, {"nme": "getNextNonSpaceIndex", "acc": 1025, "dsc": "()I"}, {"nme": "getColumn", "acc": 1025, "dsc": "()I"}, {"nme": "getIndent", "acc": 1025, "dsc": "()I"}, {"nme": "isBlank", "acc": 1025, "dsc": "()Z"}, {"nme": "getActiveBlockParser", "acc": 1025, "dsc": "()Ljdk/internal/org/commonmark/parser/block/BlockParser;"}], "flds": []}, "classes/jdk/internal/org/commonmark/internal/ListBlockParser.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/ListBlockParser", "super": "jdk/internal/org/commonmark/parser/block/AbstractBlockParser", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/ListBlock;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Z"}, {"nme": "canContain", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Block;)Z"}, {"nme": "getBlock", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/node/Block;"}, {"nme": "tryContinue", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/block/ParserState;)Ljdk/internal/org/commonmark/parser/block/BlockContinue;"}, {"nme": "parseList", "acc": 10, "dsc": "(Ljava/lang/CharSequence;IIZ)Ljdk/internal/org/commonmark/internal/ListBlockParser$ListData;"}, {"nme": "parse<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 10, "dsc": "(Ljava/lang/CharSequence;I)Ljdk/internal/org/commonmark/internal/ListBlockParser$ListMarkerData;"}, {"nme": "parseOrderedList", "acc": 10, "dsc": "(Ljava/lang/CharSequence;I)Ljdk/internal/org/commonmark/internal/ListBlockParser$ListMarkerData;"}, {"nme": "isSpaceTabOrEnd", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;I)Z"}, {"nme": "listsMatch", "acc": 10, "dsc": "(Ljdk/internal/org/commonmark/node/ListBlock;Ljdk/internal/org/commonmark/node/ListBlock;)Z"}], "flds": [{"acc": 18, "nme": "block", "dsc": "Ljdk/internal/org/commonmark/node/ListBlock;"}, {"acc": 2, "nme": "hadBlankLine", "dsc": "Z"}, {"acc": 2, "nme": "linesAfterBlank", "dsc": "I"}]}, "classes/jdk/internal/org/commonmark/internal/util/Html5Entities.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/util/Html5Entities", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "entityToString", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "readEntities", "acc": 10, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "NAMED_CHARACTER_REFERENCES", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"acc": 26, "nme": "ENTITY_PATH", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "/jdk/internal/org/commonmark/internal/util/entities.txt"}]}, "classes/jdk/internal/org/commonmark/internal/inline/InlineParserState.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/internal/inline/InlineParserState", "super": "java/lang/Object", "mthds": [{"nme": "scanner", "acc": 1025, "dsc": "()Ljdk/internal/org/commonmark/parser/beta/Scanner;"}], "flds": []}, "classes/jdk/internal/org/commonmark/node/LinkReferenceDefinition.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/node/LinkReferenceDefinition", "super": "jdk/internal/org/commonmark/node/Node", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;)V"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getDestination", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setDestination", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getTitle", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setTitle", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "accept", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Visitor;)V"}], "flds": [{"acc": 2, "nme": "label", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "destination", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "title", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/internal/org/commonmark/renderer/html/HtmlRenderer$Builder.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/renderer/html/HtmlRenderer$Builder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "build", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/renderer/html/HtmlRenderer;"}, {"nme": "softbreak", "acc": 1, "dsc": "(Ljava/lang/String;)Ljdk/internal/org/commonmark/renderer/html/HtmlRenderer$Builder;"}, {"nme": "escapeHtml", "acc": 1, "dsc": "(Z)Ljdk/internal/org/commonmark/renderer/html/HtmlRenderer$Builder;"}, {"nme": "sanitizeUrls", "acc": 1, "dsc": "(Z)Ljdk/internal/org/commonmark/renderer/html/HtmlRenderer$Builder;"}, {"nme": "urlS<PERSON><PERSON>zer", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/renderer/html/UrlSanitizer;)Ljdk/internal/org/commonmark/renderer/html/HtmlRenderer$Builder;"}, {"nme": "percentEncodeUrls", "acc": 1, "dsc": "(Z)Ljdk/internal/org/commonmark/renderer/html/HtmlRenderer$Builder;"}, {"nme": "attributeProviderFactory", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/renderer/html/AttributeProviderFactory;)Ljdk/internal/org/commonmark/renderer/html/HtmlRenderer$Builder;"}, {"nme": "nodeRendererFactory", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/renderer/html/HtmlNodeRendererFactory;)Ljdk/internal/org/commonmark/renderer/html/HtmlRenderer$Builder;"}, {"nme": "extensions", "acc": 1, "dsc": "(Ljava/lang/Iterable;)Ljdk/internal/org/commonmark/renderer/html/HtmlRenderer$Builder;", "sig": "(Ljava/lang/Iterable<+Ljdk/internal/org/commonmark/Extension;>;)Ljdk/internal/org/commonmark/renderer/html/HtmlRenderer$Builder;"}], "flds": [{"acc": 2, "nme": "softbreak", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "escapeHtml", "dsc": "Z"}, {"acc": 2, "nme": "sanitizeUrls", "dsc": "Z"}, {"acc": 2, "nme": "urlS<PERSON><PERSON>zer", "dsc": "Ljdk/internal/org/commonmark/renderer/html/UrlSanitizer;"}, {"acc": 2, "nme": "percentEncodeUrls", "dsc": "Z"}, {"acc": 2, "nme": "attributeProviderFactories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/renderer/html/AttributeProviderFactory;>;"}, {"acc": 2, "nme": "nodeRendererFactories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/renderer/html/HtmlNodeRendererFactory;>;"}]}, "classes/jdk/internal/org/commonmark/ext/gfm/tables/TablesExtension$3.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/org/commonmark/ext/gfm/tables/TablesExtension$3", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/internal/org/commonmark/ext/gfm/tables/TablesExtension;)V"}, {"nme": "create", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/renderer/markdown/MarkdownNodeRendererContext;)Ljdk/internal/org/commonmark/renderer/NodeRenderer;"}, {"nme": "getSpecialCharacters", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()L<PERSON><PERSON>/util/Set<Ljava/lang/Character;>;"}], "flds": []}, "classes/jdk/internal/org/commonmark/internal/util/Escaping$1.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/org/commonmark/internal/util/Escaping$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "replace", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>java/lang/StringBuilder;)V"}], "flds": []}, "classes/jdk/internal/org/commonmark/node/OrderedList.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/node/OrderedList", "super": "jdk/internal/org/commonmark/node/ListBlock", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "accept", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Visitor;)V"}, {"nme": "getMarkerStartNumber", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Integer;"}, {"nme": "setMarkerStartNumber", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Integer;)V"}, {"nme": "getMarkerDelimiter", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setMarkerDelimiter", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getStartNumber", "acc": 131073, "dsc": "()I", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "setStartNumber", "acc": 131073, "dsc": "(I)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getDelimiter", "acc": 131073, "dsc": "()C", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "set<PERSON><PERSON><PERSON><PERSON>", "acc": 131073, "dsc": "(C)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}], "flds": [{"acc": 2, "nme": "markerDelimiter", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "markerStartNumber", "dsc": "<PERSON><PERSON><PERSON>/lang/Integer;"}]}, "classes/jdk/internal/org/commonmark/internal/renderer/text/OrderedListHolder.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/renderer/text/OrderedListHolder", "super": "jdk/internal/org/commonmark/internal/renderer/text/ListHolder", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/internal/renderer/text/ListHolder;Ljdk/internal/org/commonmark/node/OrderedList;)V"}, {"nme": "getDelimiter", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "get<PERSON>ounter", "acc": 1, "dsc": "()I"}, {"nme": "increaseCounter", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "delimiter", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "counter", "dsc": "I"}]}, "classes/jdk/internal/org/commonmark/parser/block/MatchedBlockParser.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/parser/block/MatchedBlockParser", "super": "java/lang/Object", "mthds": [{"nme": "getMatchedBlockParser", "acc": 1025, "dsc": "()Ljdk/internal/org/commonmark/parser/block/BlockParser;"}, {"nme": "getParagraphLines", "acc": 1025, "dsc": "()Ljdk/internal/org/commonmark/parser/SourceLines;"}], "flds": []}, "classes/jdk/internal/markdown/MarkdownTransformer$AutoRefInlineParserFactory.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/markdown/MarkdownTransformer$AutoRefInlineParserFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/sun/tools/javac/parser/ReferenceParser;Ljava/lang/String;)V"}, {"nme": "create", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/InlineParserContext;)Ljdk/internal/org/commonmark/parser/InlineParser;"}, {"nme": "isReference", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}], "flds": [{"acc": 18, "nme": "ref<PERSON><PERSON><PERSON>", "dsc": "Lcom/sun/tools/javac/parser/ReferenceParser;"}, {"acc": 18, "nme": "autorefScheme", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/internal/org/commonmark/internal/inline/Scanner.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/inline/Scanner", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/List;II)V", "sig": "(Ljava/util/List<Ljdk/internal/org/commonmark/parser/SourceLine;>;II)V"}, {"nme": "of", "acc": 9, "dsc": "(Ljdk/internal/org/commonmark/parser/SourceLines;)Ljdk/internal/org/commonmark/internal/inline/Scanner;"}, {"nme": "peek", "acc": 1, "dsc": "()C"}, {"nme": "peekCodePoint", "acc": 1, "dsc": "()I"}, {"nme": "peekPreviousCodePoint", "acc": 1, "dsc": "()I"}, {"nme": "hasNext", "acc": 1, "dsc": "()Z"}, {"nme": "next", "acc": 1, "dsc": "()V"}, {"nme": "next", "acc": 1, "dsc": "(C)Z"}, {"nme": "next", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "matchMultiple", "acc": 1, "dsc": "(C)I"}, {"nme": "match", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/internal/util/CharMatcher;)I"}, {"nme": "whitespace", "acc": 1, "dsc": "()I"}, {"nme": "find", "acc": 1, "dsc": "(C)I"}, {"nme": "find", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/internal/util/CharMatcher;)I"}, {"nme": "position", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/internal/inline/Position;"}, {"nme": "setPosition", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/internal/inline/Position;)V"}, {"nme": "getSource", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/internal/inline/Position;Ljdk/internal/org/commonmark/internal/inline/Position;)Ljdk/internal/org/commonmark/parser/SourceLines;"}, {"nme": "setLine", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/parser/SourceLine;)V"}, {"nme": "checkPosition", "acc": 2, "dsc": "(II)V"}], "flds": [{"acc": 25, "nme": "END", "dsc": "C", "val": 0}, {"acc": 18, "nme": "lines", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/parser/SourceLine;>;"}, {"acc": 2, "nme": "lineIndex", "dsc": "I"}, {"acc": 2, "nme": "index", "dsc": "I"}, {"acc": 2, "nme": "line", "dsc": "Ljdk/internal/org/commonmark/parser/SourceLine;"}, {"acc": 2, "nme": "lineLength", "dsc": "I"}]}, "classes/jdk/internal/org/commonmark/internal/util/AsciiMatcher.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/util/AsciiMatcher", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/internal/util/AsciiMatcher$Builder;)V"}, {"nme": "matches", "acc": 1, "dsc": "(C)Z"}, {"nme": "newBuilder", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/internal/util/AsciiMatcher$Builder;"}, {"nme": "builder", "acc": 9, "dsc": "()Ljdk/internal/org/commonmark/internal/util/AsciiMatcher$Builder;"}], "flds": [{"acc": 18, "nme": "set", "dsc": "Ljava/util/BitSet;"}]}, "classes/jdk/internal/org/commonmark/internal/renderer/NodeRendererMap.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/renderer/NodeRendererMap", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "add", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/renderer/NodeRenderer;)V"}, {"nme": "render", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}], "flds": [{"acc": 18, "nme": "renderers", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Class<+Ljdk/internal/org/commonmark/node/Node;>;Ljdk/internal/org/commonmark/renderer/NodeRenderer;>;"}]}, "classes/jdk/internal/org/commonmark/parser/PostProcessor.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/parser/PostProcessor", "super": "java/lang/Object", "mthds": [{"nme": "process", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)Ljdk/internal/org/commonmark/node/Node;"}], "flds": []}, "classes/jdk/internal/org/commonmark/renderer/markdown/MarkdownRenderer$1.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/org/commonmark/renderer/markdown/MarkdownRenderer$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/internal/org/commonmark/renderer/markdown/MarkdownRenderer;)V"}, {"nme": "create", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/renderer/markdown/MarkdownNodeRendererContext;)Ljdk/internal/org/commonmark/renderer/NodeRenderer;"}, {"nme": "getSpecialCharacters", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()L<PERSON><PERSON>/util/Set<Ljava/lang/Character;>;"}], "flds": []}, "classes/jdk/internal/org/commonmark/internal/inline/InlineContentParser.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/internal/inline/InlineContentParser", "super": "java/lang/Object", "mthds": [{"nme": "try<PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/internal/inline/InlineParserState;)Ljdk/internal/org/commonmark/internal/inline/ParsedInline;"}], "flds": []}, "classes/jdk/internal/org/commonmark/renderer/html/HtmlRenderer.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/renderer/html/HtmlRenderer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/renderer/html/HtmlRenderer$Builder;)V"}, {"nme": "builder", "acc": 9, "dsc": "()Ljdk/internal/org/commonmark/renderer/html/HtmlRenderer$Builder;"}, {"nme": "render", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Node;Ljava/lang/Appendable;)V"}, {"nme": "render", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)Ljava/lang/String;"}], "flds": [{"acc": 18, "nme": "softbreak", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "escapeHtml", "dsc": "Z"}, {"acc": 18, "nme": "sanitizeUrls", "dsc": "Z"}, {"acc": 18, "nme": "urlS<PERSON><PERSON>zer", "dsc": "Ljdk/internal/org/commonmark/renderer/html/UrlSanitizer;"}, {"acc": 18, "nme": "percentEncodeUrls", "dsc": "Z"}, {"acc": 18, "nme": "attributeProviderFactories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/renderer/html/AttributeProviderFactory;>;"}, {"acc": 18, "nme": "nodeRendererFactories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/renderer/html/HtmlNodeRendererFactory;>;"}]}, "classes/jdk/internal/org/commonmark/text/Characters.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/text/Characters", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "find", "acc": 9, "dsc": "(C<PERSON><PERSON><PERSON>/lang/CharSequence;I)I"}, {"nme": "findLineBreak", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;I)I"}, {"nme": "isBlank", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;)Z"}, {"nme": "hasNonSpace", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;)Z"}, {"nme": "isLetter", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;I)Z"}, {"nme": "isSpaceOrTab", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;I)Z"}, {"nme": "isPunctuationCodePoint", "acc": 9, "dsc": "(I)Z"}, {"nme": "isWhitespaceCodePoint", "acc": 9, "dsc": "(I)Z"}, {"nme": "skip", "acc": 9, "dsc": "(C<PERSON><PERSON><PERSON>/lang/CharSequence;II)I"}, {"nme": "skip<PERSON><PERSON><PERSON>s", "acc": 9, "dsc": "(C<PERSON><PERSON><PERSON>/lang/CharSequence;II)I"}, {"nme": "skipSpaceTab", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;II)I"}, {"nme": "skipSpaceTabBackwards", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;II)I"}], "flds": []}, "classes/jdk/internal/org/commonmark/parser/block/AbstractBlockParser.class": {"ver": 68, "acc": 1057, "nme": "jdk/internal/org/commonmark/parser/block/AbstractBlockParser", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Z"}, {"nme": "canHaveLazyContinuationLines", "acc": 1, "dsc": "()Z"}, {"nme": "canContain", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Block;)Z"}, {"nme": "addLine", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/SourceLine;)V"}, {"nme": "addSourceSpan", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/SourceSpan;)V"}, {"nme": "closeBlock", "acc": 1, "dsc": "()V"}, {"nme": "parseInlines", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/InlineParser;)V"}], "flds": []}, "classes/jdk/internal/org/commonmark/node/CustomNode.class": {"ver": 68, "acc": 1057, "nme": "jdk/internal/org/commonmark/node/CustomNode", "super": "jdk/internal/org/commonmark/node/Node", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "accept", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Visitor;)V"}], "flds": []}, "classes/jdk/internal/org/commonmark/internal/DocumentParser$MatchedBlockParserImpl.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/org/commonmark/internal/DocumentParser$MatchedBlockParserImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/block/BlockParser;)V"}, {"nme": "getMatchedBlockParser", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/parser/block/BlockParser;"}, {"nme": "getParagraphLines", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/parser/SourceLines;"}], "flds": [{"acc": 18, "nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dsc": "Ljdk/internal/org/commonmark/parser/block/BlockParser;"}]}, "classes/jdk/internal/org/commonmark/renderer/text/TextContentNodeRendererFactory.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/renderer/text/TextContentNodeRendererFactory", "super": "java/lang/Object", "mthds": [{"nme": "create", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/renderer/text/TextContentNodeRendererContext;)Ljdk/internal/org/commonmark/renderer/NodeRenderer;"}], "flds": []}, "classes/jdk/internal/org/commonmark/node/ListItem.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/node/ListItem", "super": "jdk/internal/org/commonmark/node/Block", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "accept", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Visitor;)V"}, {"nme": "getMarkerIndent", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Integer;"}, {"nme": "setMarkerIndent", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Integer;)V"}, {"nme": "getContentIndent", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Integer;"}, {"nme": "setContentIndent", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Integer;)V"}], "flds": [{"acc": 2, "nme": "markerIndent", "dsc": "<PERSON><PERSON><PERSON>/lang/Integer;"}, {"acc": 2, "nme": "contentIndent", "dsc": "<PERSON><PERSON><PERSON>/lang/Integer;"}]}, "classes/jdk/internal/org/commonmark/ext/gfm/tables/TableCell.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/ext/gfm/tables/TableCell", "super": "jdk/internal/org/commonmark/node/CustomNode", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Z"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Z)V"}, {"nme": "getAlignment", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/ext/gfm/tables/TableCell$Alignment;"}, {"nme": "setAlignment", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/ext/gfm/tables/TableCell$Alignment;)V"}, {"nme": "getWidth", "acc": 1, "dsc": "()I"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(I)V"}], "flds": [{"acc": 2, "nme": "header", "dsc": "Z"}, {"acc": 2, "nme": "alignment", "dsc": "Ljdk/internal/org/commonmark/ext/gfm/tables/TableCell$Alignment;"}, {"acc": 2, "nme": "width", "dsc": "I"}]}, "classes/jdk/internal/org/commonmark/ext/gfm/tables/internal/TableBlockParser$TableCellInfo.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/org/commonmark/ext/gfm/tables/internal/TableBlockParser$TableCellInfo", "super": "java/lang/Object", "mthds": [{"nme": "getAlignment", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/ext/gfm/tables/TableCell$Alignment;"}, {"nme": "getWidth", "acc": 1, "dsc": "()I"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/ext/gfm/tables/TableCell$Alignment;I)V"}], "flds": [{"acc": 18, "nme": "alignment", "dsc": "Ljdk/internal/org/commonmark/ext/gfm/tables/TableCell$Alignment;"}, {"acc": 18, "nme": "width", "dsc": "I"}]}, "classes/jdk/internal/org/commonmark/internal/HeadingParser$Factory.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/HeadingParser$Factory", "super": "jdk/internal/org/commonmark/parser/block/AbstractBlockParserFactory", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "tryStart", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/block/ParserState;Ljdk/internal/org/commonmark/parser/block/MatchedBlockParser;)Ljdk/internal/org/commonmark/parser/block/BlockStart;"}], "flds": []}, "classes/jdk/internal/org/commonmark/parser/IncludeSourceSpans.class": {"ver": 68, "acc": 16433, "nme": "jdk/internal/org/commonmark/parser/IncludeSourceSpans", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/internal/org/commonmark/parser/IncludeSourceSpans;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Ljdk/internal/org/commonmark/parser/IncludeSourceSpans;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/internal/org/commonmark/parser/IncludeSourceSpans;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "NONE", "dsc": "Ljdk/internal/org/commonmark/parser/IncludeSourceSpans;"}, {"acc": 16409, "nme": "BLOCKS", "dsc": "Ljdk/internal/org/commonmark/parser/IncludeSourceSpans;"}, {"acc": 16409, "nme": "BLOCKS_AND_INLINES", "dsc": "Ljdk/internal/org/commonmark/parser/IncludeSourceSpans;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/internal/org/commonmark/parser/IncludeSourceSpans;"}]}, "classes/jdk/internal/org/commonmark/internal/Bracket.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/Bracket", "super": "java/lang/Object", "mthds": [{"nme": "link", "acc": 9, "dsc": "(Ljdk/internal/org/commonmark/node/Text;Ljdk/internal/org/commonmark/parser/beta/Position;Ljdk/internal/org/commonmark/parser/beta/Position;Ljdk/internal/org/commonmark/internal/Bracket;Ljdk/internal/org/commonmark/internal/Delimiter;)Ljdk/internal/org/commonmark/internal/Bracket;"}, {"nme": "image", "acc": 9, "dsc": "(Ljdk/internal/org/commonmark/node/Text;Ljdk/internal/org/commonmark/parser/beta/Position;Ljdk/internal/org/commonmark/parser/beta/Position;Ljdk/internal/org/commonmark/internal/Bracket;Ljdk/internal/org/commonmark/internal/Delimiter;)Ljdk/internal/org/commonmark/internal/Bracket;"}, {"nme": "<init>", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/node/Text;Ljdk/internal/org/commonmark/parser/beta/Position;Ljdk/internal/org/commonmark/parser/beta/Position;Ljdk/internal/org/commonmark/internal/Bracket;Ljdk/internal/org/commonmark/internal/Delimiter;Z)V"}], "flds": [{"acc": 17, "nme": "node", "dsc": "Ljdk/internal/org/commonmark/node/Text;"}, {"acc": 17, "nme": "markerPosition", "dsc": "Ljdk/internal/org/commonmark/parser/beta/Position;"}, {"acc": 17, "nme": "contentPosition", "dsc": "Ljdk/internal/org/commonmark/parser/beta/Position;"}, {"acc": 17, "nme": "image", "dsc": "Z"}, {"acc": 17, "nme": "previous", "dsc": "Ljdk/internal/org/commonmark/internal/Bracket;"}, {"acc": 17, "nme": "previousDelimiter", "dsc": "Ljdk/internal/org/commonmark/internal/Delimiter;"}, {"acc": 1, "nme": "allowed", "dsc": "Z"}, {"acc": 1, "nme": "bracketAfter", "dsc": "Z"}]}, "classes/jdk/internal/org/commonmark/parser/block/BlockParserFactory.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/parser/block/BlockParserFactory", "super": "java/lang/Object", "mthds": [{"nme": "tryStart", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/parser/block/ParserState;Ljdk/internal/org/commonmark/parser/block/MatchedBlockParser;)Ljdk/internal/org/commonmark/parser/block/BlockStart;"}], "flds": []}, "classes/jdk/internal/org/commonmark/renderer/text/TextContentRenderer$RendererContext.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/org/commonmark/renderer/text/TextContentRenderer$RendererContext", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/renderer/text/TextContentRenderer;Ljdk/internal/org/commonmark/renderer/text/TextContentWriter;)V"}, {"nme": "stripNewlines", "acc": 1, "dsc": "()Z"}, {"nme": "getWriter", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/renderer/text/TextContentWriter;"}, {"nme": "render", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}], "flds": [{"acc": 18, "nme": "textContentWriter", "dsc": "Ljdk/internal/org/commonmark/renderer/text/TextContentWriter;"}, {"acc": 18, "nme": "nodeRendererMap", "dsc": "Ljdk/internal/org/commonmark/internal/renderer/NodeRendererMap;"}, {"acc": 4112, "nme": "this$0", "dsc": "Ljdk/internal/org/commonmark/renderer/text/TextContentRenderer;"}]}, "classes/jdk/internal/org/commonmark/renderer/html/DefaultUrlSanitizer.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/renderer/html/DefaultUrlSanitizer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)V", "sig": "(Lja<PERSON>/util/Collection<Ljava/lang/String;>;)V"}, {"nme": "sanitizeLinkUrl", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "sanitizeImageUrl", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "stripHtmlSpaces", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "isHtmlSpace", "acc": 2, "dsc": "(I)Z"}], "flds": [{"acc": 2, "nme": "protocols", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}]}, "classes/jdk/internal/org/commonmark/renderer/text/TextContentRenderer$1.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/org/commonmark/renderer/text/TextContentRenderer$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/internal/org/commonmark/renderer/text/TextContentRenderer;)V"}, {"nme": "create", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/renderer/text/TextContentNodeRendererContext;)Ljdk/internal/org/commonmark/renderer/NodeRenderer;"}], "flds": []}, "classes/jdk/internal/markdown/MarkdownTransformer$AutoRefInlineParserFactory$1.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/markdown/MarkdownTransformer$AutoRefInlineParserFactory$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/internal/markdown/MarkdownTransformer$AutoRefInlineParserFactory;Ljdk/internal/org/commonmark/parser/InlineParserContext;)V", "sig": "()V"}, {"nme": "getCustomDelimiterProcessors", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljdk/internal/org/commonmark/parser/delimiter/DelimiterProcessor;>;"}, {"nme": "getLinkReferenceDefinition", "acc": 1, "dsc": "(Ljava/lang/String;)Ljdk/internal/org/commonmark/node/LinkReferenceDefinition;"}], "flds": [{"acc": 4112, "nme": "val$inlineParserContext", "dsc": "Ljdk/internal/org/commonmark/parser/InlineParserContext;"}, {"acc": 4112, "nme": "this$0", "dsc": "Ljdk/internal/markdown/MarkdownTransformer$AutoRefInlineParserFactory;"}]}, "classes/jdk/internal/org/commonmark/internal/util/Escaping$Replacer.class": {"ver": 68, "acc": 1536, "nme": "jdk/internal/org/commonmark/internal/util/Escaping$Replacer", "super": "java/lang/Object", "mthds": [{"nme": "replace", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>java/lang/StringBuilder;)V"}], "flds": []}, "classes/jdk/internal/org/commonmark/internal/IndentedCodeBlockParser.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/IndentedCodeBlockParser", "super": "jdk/internal/org/commonmark/parser/block/AbstractBlockParser", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getBlock", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/node/Block;"}, {"nme": "tryContinue", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/block/ParserState;)Ljdk/internal/org/commonmark/parser/block/BlockContinue;"}, {"nme": "addLine", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/SourceLine;)V"}, {"nme": "closeBlock", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "block", "dsc": "Ljdk/internal/org/commonmark/node/IndentedCodeBlock;"}, {"acc": 18, "nme": "lines", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/CharSequence;>;"}]}, "classes/jdk/internal/org/commonmark/renderer/html/UrlSanitizer.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/renderer/html/UrlSanitizer", "super": "java/lang/Object", "mthds": [{"nme": "sanitizeLinkUrl", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "sanitizeImageUrl", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": []}, "classes/jdk/internal/org/commonmark/node/Nodes.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/node/Nodes", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "between", "acc": 9, "dsc": "(Ljdk/internal/org/commonmark/node/Node;Ljdk/internal/org/commonmark/node/Node;)Ljava/lang/Iterable;", "sig": "(Ljdk/internal/org/commonmark/node/Node;Ljdk/internal/org/commonmark/node/Node;)Ljava/lang/Iterable<Ljdk/internal/org/commonmark/node/Node;>;"}], "flds": []}, "classes/jdk/internal/org/commonmark/parser/block/BlockStart.class": {"ver": 68, "acc": 1057, "nme": "jdk/internal/org/commonmark/parser/block/BlockStart", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "none", "acc": 9, "dsc": "()Ljdk/internal/org/commonmark/parser/block/BlockStart;"}, {"nme": "of", "acc": 137, "dsc": "([Ljdk/internal/org/commonmark/parser/block/BlockParser;)Ljdk/internal/org/commonmark/parser/block/BlockStart;"}, {"nme": "atIndex", "acc": 1025, "dsc": "(I)Ljdk/internal/org/commonmark/parser/block/BlockStart;"}, {"nme": "atColumn", "acc": 1025, "dsc": "(I)Ljdk/internal/org/commonmark/parser/block/BlockStart;"}, {"nme": "replaceActiveBlockParser", "acc": 1025, "dsc": "()Ljdk/internal/org/commonmark/parser/block/BlockStart;"}], "flds": []}, "classes/jdk/internal/org/commonmark/node/SourceSpan.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/node/SourceSpan", "super": "java/lang/Object", "mthds": [{"nme": "of", "acc": 9, "dsc": "(III)Ljdk/internal/org/commonmark/node/SourceSpan;"}, {"nme": "<init>", "acc": 2, "dsc": "(III)V"}, {"nme": "getLineIndex", "acc": 1, "dsc": "()I"}, {"nme": "getColumnIndex", "acc": 1, "dsc": "()I"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "lineIndex", "dsc": "I"}, {"acc": 18, "nme": "columnIndex", "dsc": "I"}, {"acc": 18, "nme": "length", "dsc": "I"}]}, "classes/jdk/internal/org/commonmark/ext/gfm/tables/internal/TableHtmlNodeRenderer.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/ext/gfm/tables/internal/TableHtmlNodeRenderer", "super": "jdk/internal/org/commonmark/ext/gfm/tables/internal/TableNodeRenderer", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/renderer/html/HtmlNodeRendererContext;)V"}, {"nme": "renderBlock", "acc": 4, "dsc": "(Ljdk/internal/org/commonmark/ext/gfm/tables/TableBlock;)V"}, {"nme": "renderHead", "acc": 4, "dsc": "(Ljdk/internal/org/commonmark/ext/gfm/tables/TableHead;)V"}, {"nme": "renderBody", "acc": 4, "dsc": "(Ljdk/internal/org/commonmark/ext/gfm/tables/TableBody;)V"}, {"nme": "renderRow", "acc": 4, "dsc": "(Ljdk/internal/org/commonmark/ext/gfm/tables/TableRow;)V"}, {"nme": "renderCell", "acc": 4, "dsc": "(Ljdk/internal/org/commonmark/ext/gfm/tables/TableCell;)V"}, {"nme": "getAttributes", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/node/Node;Ljava/lang/String;)Ljava/util/Map;", "sig": "(Ljdk/internal/org/commonmark/node/Node;Ljava/lang/String;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "getCellAttributes", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/ext/gfm/tables/TableCell;Ljava/lang/String;)Ljava/util/Map;", "sig": "(Ljdk/internal/org/commonmark/ext/gfm/tables/TableCell;Ljava/lang/String;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "getAlignValue", "acc": 10, "dsc": "(Ljdk/internal/org/commonmark/ext/gfm/tables/TableCell$Alignment;)Ljava/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}, {"nme": "render", "acc": 4161, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}, {"nme": "getNodeTypes", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;"}], "flds": [{"acc": 18, "nme": "htmlWriter", "dsc": "Ljdk/internal/org/commonmark/renderer/html/HtmlWriter;"}, {"acc": 18, "nme": "context", "dsc": "Ljdk/internal/org/commonmark/renderer/html/HtmlNodeRendererContext;"}]}, "classes/jdk/internal/org/commonmark/parser/delimiter/DelimiterProcessor.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/parser/delimiter/DelimiterProcessor", "super": "java/lang/Object", "mthds": [{"nme": "getOpeningCharacter", "acc": 1025, "dsc": "()C"}, {"nme": "getClosingCharacter", "acc": 1025, "dsc": "()C"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()I"}, {"nme": "process", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/parser/delimiter/DelimiterRun;Ljdk/internal/org/commonmark/parser/delimiter/DelimiterRun;)I"}], "flds": []}, "classes/jdk/internal/org/commonmark/internal/inline/HtmlInlineParser.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/internal/inline/HtmlInlineParser", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "try<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/internal/inline/InlineParserState;)Ljdk/internal/org/commonmark/internal/inline/ParsedInline;"}, {"nme": "htmlInline", "acc": 10, "dsc": "(Ljdk/internal/org/commonmark/parser/beta/Position;Ljdk/internal/org/commonmark/parser/beta/Scanner;)Ljdk/internal/org/commonmark/internal/inline/ParsedInline;"}, {"nme": "tryOpenTag", "acc": 10, "dsc": "(Ljdk/internal/org/commonmark/parser/beta/Scanner;)Z"}, {"nme": "tryClosingTag", "acc": 10, "dsc": "(Ljdk/internal/org/commonmark/parser/beta/Scanner;)Z"}, {"nme": "tryProcessingInstruction", "acc": 10, "dsc": "(Ljdk/internal/org/commonmark/parser/beta/Scanner;)Z"}, {"nme": "tryComment", "acc": 10, "dsc": "(Ljdk/internal/org/commonmark/parser/beta/Scanner;)Z"}, {"nme": "tryCdata", "acc": 10, "dsc": "(Ljdk/internal/org/commonmark/parser/beta/Scanner;)Z"}, {"nme": "tryDeclaration", "acc": 10, "dsc": "(Ljdk/internal/org/commonmark/parser/beta/Scanner;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "asciiLetter", "dsc": "Ljdk/internal/org/commonmark/text/AsciiMatcher;"}, {"acc": 26, "nme": "tagNameStart", "dsc": "Ljdk/internal/org/commonmark/text/AsciiMatcher;"}, {"acc": 26, "nme": "tagNameContinue", "dsc": "Ljdk/internal/org/commonmark/text/AsciiMatcher;"}, {"acc": 26, "nme": "attributeStart", "dsc": "Ljdk/internal/org/commonmark/text/AsciiMatcher;"}, {"acc": 26, "nme": "attributeContinue", "dsc": "Ljdk/internal/org/commonmark/text/AsciiMatcher;"}, {"acc": 26, "nme": "attributeValueEnd", "dsc": "Ljdk/internal/org/commonmark/text/AsciiMatcher;"}]}, "classes/jdk/internal/org/commonmark/renderer/NodeRenderer.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/renderer/NodeRenderer", "super": "java/lang/Object", "mthds": [{"nme": "getNodeTypes", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljava/lang/Class<+Ljdk/internal/org/commonmark/node/Node;>;>;"}, {"nme": "render", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}], "flds": []}, "classes/jdk/internal/org/commonmark/node/BulletList.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/node/BulletList", "super": "jdk/internal/org/commonmark/node/ListBlock", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "accept", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Visitor;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getBulletMarker", "acc": 131073, "dsc": "()C", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "setBulletMarker", "acc": 131073, "dsc": "(C)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}], "flds": [{"acc": 2, "nme": "marker", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/internal/org/commonmark/parser/Parser.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/parser/Parser", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/parser/Parser$Builder;)V"}, {"nme": "builder", "acc": 9, "dsc": "()Ljdk/internal/org/commonmark/parser/Parser$Builder;"}, {"nme": "parse", "acc": 1, "dsc": "(Ljava/lang/String;)Ljdk/internal/org/commonmark/node/Node;"}, {"nme": "parse<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Ljava/io/Reader;)Ljdk/internal/org/commonmark/node/Node;", "exs": ["java/io/IOException"]}, {"nme": "createDocumentParser", "acc": 2, "dsc": "()Ljdk/internal/org/commonmark/internal/DocumentParser;"}, {"nme": "postProcess", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)Ljdk/internal/org/commonmark/node/Node;"}], "flds": [{"acc": 18, "nme": "blockParserFactories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/parser/block/BlockParserFactory;>;"}, {"acc": 18, "nme": "delimiterProcessors", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/parser/delimiter/DelimiterProcessor;>;"}, {"acc": 18, "nme": "inlineParserFactory", "dsc": "Ljdk/internal/org/commonmark/parser/InlineParserFactory;"}, {"acc": 18, "nme": "postProcessors", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/parser/PostProcessor;>;"}, {"acc": 18, "nme": "includeSourceSpans", "dsc": "Ljdk/internal/org/commonmark/parser/IncludeSourceSpans;"}]}, "classes/jdk/internal/org/commonmark/renderer/html/AttributeProviderContext.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/renderer/html/AttributeProviderContext", "super": "java/lang/Object", "mthds": [], "flds": []}, "classes/jdk/internal/org/commonmark/renderer/html/AttributeProviderFactory.class": {"ver": 68, "acc": 1537, "nme": "jdk/internal/org/commonmark/renderer/html/AttributeProviderFactory", "super": "java/lang/Object", "mthds": [{"nme": "create", "acc": 1025, "dsc": "(Ljdk/internal/org/commonmark/renderer/html/AttributeProviderContext;)Ljdk/internal/org/commonmark/renderer/html/AttributeProvider;"}], "flds": []}, "classes/jdk/internal/org/commonmark/renderer/html/CoreHtmlNodeRenderer$AltTextVisitor.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/org/commonmark/renderer/html/CoreHtmlNodeRenderer$AltTextVisitor", "super": "jdk/internal/org/commonmark/node/AbstractVisitor", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getAltText", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Text;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/SoftLineBreak;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/HardLineBreak;)V"}], "flds": [{"acc": 18, "nme": "sb", "dsc": "<PERSON><PERSON><PERSON>/lang/StringBuilder;"}]}, "classes/jdk/internal/org/commonmark/internal/renderer/text/ListHolder.class": {"ver": 68, "acc": 1057, "nme": "jdk/internal/org/commonmark/internal/renderer/text/ListHolder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/internal/org/commonmark/internal/renderer/text/ListHolder;)V"}, {"nme": "getParent", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/internal/renderer/text/ListHolder;"}, {"nme": "getIndent", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 26, "nme": "INDENT_DEFAULT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "   "}, {"acc": 26, "nme": "INDENT_EMPTY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": ""}, {"acc": 18, "nme": "parent", "dsc": "Ljdk/internal/org/commonmark/internal/renderer/text/ListHolder;"}, {"acc": 18, "nme": "indent", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/internal/org/commonmark/parser/SourceLine.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/parser/SourceLine", "super": "java/lang/Object", "mthds": [{"nme": "of", "acc": 9, "dsc": "(Ljava/lang/CharSequence;Ljdk/internal/org/commonmark/node/SourceSpan;)Ljdk/internal/org/commonmark/parser/SourceLine;"}, {"nme": "<init>", "acc": 2, "dsc": "(Ljava/lang/CharSequence;Ljdk/internal/org/commonmark/node/SourceSpan;)V"}, {"nme": "get<PERSON>ontent", "acc": 1, "dsc": "()L<PERSON><PERSON>/lang/CharSequence;"}, {"nme": "getSourceSpan", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/node/SourceSpan;"}, {"nme": "substring", "acc": 1, "dsc": "(II)Ljdk/internal/org/commonmark/parser/SourceLine;"}], "flds": [{"acc": 18, "nme": "content", "dsc": "<PERSON><PERSON><PERSON>/lang/CharSequence;"}, {"acc": 18, "nme": "sourceSpan", "dsc": "Ljdk/internal/org/commonmark/node/SourceSpan;"}]}, "classes/jdk/internal/org/commonmark/node/BlockQuote.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/node/BlockQuote", "super": "jdk/internal/org/commonmark/node/Block", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "accept", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Visitor;)V"}], "flds": []}, "classes/jdk/internal/org/commonmark/renderer/markdown/CoreMarkdownNodeRenderer$ListHolder.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/org/commonmark/renderer/markdown/CoreMarkdownNodeRenderer$ListHolder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Ljdk/internal/org/commonmark/renderer/markdown/CoreMarkdownNodeRenderer$ListHolder;)V"}], "flds": [{"acc": 16, "nme": "parent", "dsc": "Ljdk/internal/org/commonmark/renderer/markdown/CoreMarkdownNodeRenderer$ListHolder;"}]}, "classes/jdk/internal/org/commonmark/renderer/markdown/MarkdownRenderer$RendererContext.class": {"ver": 68, "acc": 32, "nme": "jdk/internal/org/commonmark/renderer/markdown/MarkdownRenderer$RendererContext", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/renderer/markdown/MarkdownRenderer;Ljdk/internal/org/commonmark/renderer/markdown/MarkdownWriter;)V"}, {"nme": "getWriter", "acc": 1, "dsc": "()Ljdk/internal/org/commonmark/renderer/markdown/MarkdownWriter;"}, {"nme": "render", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}, {"nme": "getSpecialCharacters", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()L<PERSON><PERSON>/util/Set<Ljava/lang/Character;>;"}], "flds": [{"acc": 18, "nme": "writer", "dsc": "Ljdk/internal/org/commonmark/renderer/markdown/MarkdownWriter;"}, {"acc": 18, "nme": "nodeRendererMap", "dsc": "Ljdk/internal/org/commonmark/internal/renderer/NodeRendererMap;"}, {"acc": 18, "nme": "additionalTextEscapes", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/Character;>;"}]}, "classes/jdk/internal/org/commonmark/ext/gfm/tables/internal/TableBlockParser$Factory.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/ext/gfm/tables/internal/TableBlockParser$Factory", "super": "jdk/internal/org/commonmark/parser/block/AbstractBlockParserFactory", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "tryStart", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/block/ParserState;Ljdk/internal/org/commonmark/parser/block/MatchedBlockParser;)Ljdk/internal/org/commonmark/parser/block/BlockStart;"}], "flds": []}, "classes/jdk/internal/org/commonmark/renderer/html/HtmlWriter.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/renderer/html/HtmlWriter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Appendable;)V"}, {"nme": "raw", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "text", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "tag", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "tag", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/util/Map;)V", "sig": "(Lja<PERSON>/lang/String;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "tag", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/util/Map;Z)V", "sig": "(Lja<PERSON>/lang/String;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;Z)V"}, {"nme": "line", "acc": 1, "dsc": "()V"}, {"nme": "append", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "NO_ATTRIBUTES", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"acc": 18, "nme": "buffer", "dsc": "<PERSON><PERSON><PERSON>/lang/Appendable;"}, {"acc": 2, "nme": "lastChar", "dsc": "C"}]}, "classes/jdk/internal/org/commonmark/node/ThematicBreak.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/node/ThematicBreak", "super": "jdk/internal/org/commonmark/node/Block", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "accept", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Visitor;)V"}, {"nme": "<PERSON><PERSON><PERSON>al", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLiteral", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 2, "nme": "literal", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/internal/org/commonmark/ext/gfm/tables/TablesExtension.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/ext/gfm/tables/TablesExtension", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "create", "acc": 9, "dsc": "()Ljdk/internal/org/commonmark/Extension;"}, {"nme": "extend", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/parser/Parser$Builder;)V"}, {"nme": "extend", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/renderer/html/HtmlRenderer$Builder;)V"}, {"nme": "extend", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/renderer/text/TextContentRenderer$Builder;)V"}, {"nme": "extend", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/renderer/markdown/MarkdownRenderer$Builder;)V"}], "flds": []}, "classes/jdk/internal/org/commonmark/ext/gfm/tables/internal/TableMarkdownNodeRenderer.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/ext/gfm/tables/internal/TableMarkdownNodeRenderer", "super": "jdk/internal/org/commonmark/ext/gfm/tables/internal/TableNodeRenderer", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/renderer/markdown/MarkdownNodeRendererContext;)V"}, {"nme": "renderBlock", "acc": 4, "dsc": "(Ljdk/internal/org/commonmark/ext/gfm/tables/TableBlock;)V"}, {"nme": "renderHead", "acc": 4, "dsc": "(Ljdk/internal/org/commonmark/ext/gfm/tables/TableHead;)V"}, {"nme": "renderBody", "acc": 4, "dsc": "(Ljdk/internal/org/commonmark/ext/gfm/tables/TableBody;)V"}, {"nme": "renderRow", "acc": 4, "dsc": "(Ljdk/internal/org/commonmark/ext/gfm/tables/TableRow;)V"}, {"nme": "renderCell", "acc": 4, "dsc": "(Ljdk/internal/org/commonmark/ext/gfm/tables/TableCell;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}, {"nme": "render", "acc": 4161, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}, {"nme": "getNodeTypes", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;"}], "flds": [{"acc": 18, "nme": "writer", "dsc": "Ljdk/internal/org/commonmark/renderer/markdown/MarkdownWriter;"}, {"acc": 18, "nme": "context", "dsc": "Ljdk/internal/org/commonmark/renderer/markdown/MarkdownNodeRendererContext;"}, {"acc": 18, "nme": "pipe", "dsc": "Ljdk/internal/org/commonmark/text/AsciiMatcher;"}, {"acc": 18, "nme": "columns", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/internal/org/commonmark/ext/gfm/tables/TableCell$Alignment;>;"}]}, "classes/jdk/internal/org/commonmark/node/HtmlBlock.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/node/HtmlBlock", "super": "jdk/internal/org/commonmark/node/Block", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "accept", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Visitor;)V"}, {"nme": "<PERSON><PERSON><PERSON>al", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLiteral", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 2, "nme": "literal", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/internal/org/commonmark/renderer/markdown/CoreMarkdownNodeRenderer.class": {"ver": 68, "acc": 33, "nme": "jdk/internal/org/commonmark/renderer/markdown/CoreMarkdownNodeRenderer", "super": "jdk/internal/org/commonmark/node/AbstractVisitor", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/renderer/markdown/MarkdownNodeRendererContext;)V"}, {"nme": "getNodeTypes", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljava/lang/Class<+Ljdk/internal/org/commonmark/node/Node;>;>;"}, {"nme": "render", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Document;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/ThematicBreak;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Heading;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/IndentedCodeBlock;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/FencedCodeBlock;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/HtmlBlock;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Paragraph;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/BlockQuote;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/BulletList;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/OrderedList;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/ListItem;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Code;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Emphasis;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/StrongEmphasis;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Link;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Image;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/HtmlInline;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/HardLineBreak;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/SoftLineBreak;)V"}, {"nme": "visit", "acc": 1, "dsc": "(Ljdk/internal/org/commonmark/node/Text;)V"}, {"nme": "visit<PERSON><PERSON><PERSON><PERSON>", "acc": 4, "dsc": "(Ljdk/internal/org/commonmark/node/Node;)V"}, {"nme": "findMaxRun<PERSON>ength", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "contains", "acc": 10, "dsc": "(Ljava/lang/String;Ljdk/internal/org/commonmark/text/CharMatcher;)Z"}, {"nme": "repeat", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Ljava/lang/String;"}, {"nme": "getLines", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "writeLinkLike", "acc": 2, "dsc": "(Ljava/lang/String;Ljava/lang/String;Ljdk/internal/org/commonmark/node/Node;Ljava/lang/String;)V"}], "flds": [{"acc": 18, "nme": "textEscape", "dsc": "Ljdk/internal/org/commonmark/text/AsciiMatcher;"}, {"acc": 18, "nme": "textEscapeInHeading", "dsc": "Ljdk/internal/org/commonmark/text/CharMatcher;"}, {"acc": 18, "nme": "linkDestinationNeedsAngleBrackets", "dsc": "Ljdk/internal/org/commonmark/text/CharMatcher;"}, {"acc": 18, "nme": "linkDestinationEscapeInAngleBrackets", "dsc": "Ljdk/internal/org/commonmark/text/CharMatcher;"}, {"acc": 18, "nme": "linkTitleEscapeInQuotes", "dsc": "Ljdk/internal/org/commonmark/text/CharMatcher;"}, {"acc": 18, "nme": "orderedListMarkerPattern", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 20, "nme": "context", "dsc": "Ljdk/internal/org/commonmark/renderer/markdown/MarkdownNodeRendererContext;"}, {"acc": 18, "nme": "writer", "dsc": "Ljdk/internal/org/commonmark/renderer/markdown/MarkdownWriter;"}, {"acc": 2, "nme": "listHolder", "dsc": "Ljdk/internal/org/commonmark/renderer/markdown/CoreMarkdownNodeRenderer$ListHolder;"}]}}}}