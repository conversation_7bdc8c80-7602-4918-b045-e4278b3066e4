2025-07-31 13:48:35 WARN  Skidfuscator:76 - Attempting to auto-resolve missing classes...
List of missing classes:
   -->   org/graalvm/nativeimage/hosted/Feature
   -->   org/conscrypt/ConscryptHostnameVerifier

2025-07-31 13:48:35 WARN  Skidfuscator:76 - Found common dependency: CONSCRYPT...

2025-07-31 13:48:35 INFO  Skidfuscator:85 - Downloading dependency CONSCRYPT from https://raw.githubusercontent.com/skidfuscatordev/mappings/refs/heads/main/conscrypt/2.5.2/org.conscrypt.conscrypt-openjdk-uber-2.5.2.json

2025-07-31 13:48:37 INFO  Skidfuscator:85 - Downloaded dependency CONSCRYPT to mappings-cloud\conscrypt\download.mappings

2025-07-31 13:48:37 INFO  Skidfuscator:85 - Extracted JSON particular dependency CONSCRYPT to C:\Users\<USER>\Desktop\SoulMC Dev\SkillFork\SbmagicHook\mappings-cloud

2025-07-31 13:48:37 INFO  Skidfuscator:85 - Importing C:\Users\<USER>\Desktop\SoulMC Dev\SkillFork\SbmagicHook\mappings-cloud\conscrypt.json... please wait...

2025-07-31 13:48:37 WARN  Skidfuscator:76 - Resolved 1 common dependencies... retrying verification...

2025-07-31 13:48:37 WARN  Skidfuscator:76 - Attempting to auto-resolve missing classes...
List of missing classes:
   -->   org/graalvm/nativeimage/hosted/Feature

2025-07-31 13:48:37 WARN  Skidfuscator:76 - 
-----------------------------------------------------
/!\ Skidfuscator failed to compute some libraries!
PLEASE READ THE FOLLOWING WITH MUCH ATTENTION
-----------------------------------------------------
It it advised to read https://skidfuscator.dev/docs/libraries.html

The following classes were NOT found. This means they are 
either not present in the libraries or the libraries are 
corrupted. Libraries themselves can have dependencies

List of missing classes:
   -->   org/graalvm/nativeimage/hosted/Feature
-----------------------------------------------------

