{"md5": "39e0c47988faa590ef8a26182853647c", "sha2": "593fecb9c42688eebc7d8da5d6ea127f4d4c92a2", "sha256": "97b69b4b17dfe02217c9131ad342564cbc9aebd04c75eb689639b5f78fd4b11c", "contents": {"classes": {"net/kyori/option/OptionSchemaImpl$MutableImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/option/OptionSchemaImpl$MutableImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lnet/kyori/option/OptionSchemaImpl;)V"}, {"nme": "register", "acc": 0, "dsc": "(Ljava/lang/String;Lnet/kyori/option/value/ValueType;Ljava/lang/Object;)Lnet/kyori/option/Option;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/String;Lnet/kyori/option/value/ValueType<TT;>;TT;)Lnet/kyori/option/Option<TT;>;"}, {"nme": "stringOption", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;)Lnet/kyori/option/Option;", "sig": "(Ljava/lang/String;Ljava/lang/String;)Lnet/kyori/option/Option<Ljava/lang/String;>;"}, {"nme": "booleanOption", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)Lnet/kyori/option/Option;", "sig": "(<PERSON>ja<PERSON>/lang/String;Z)Lnet/kyori/option/Option<Ljava/lang/Boolean;>;"}, {"nme": "intOption", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Lnet/kyori/option/Option;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;I)Lnet/kyori/option/Option<Ljava/lang/Integer;>;"}, {"nme": "doubleOption", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;D)Lnet/kyori/option/Option;", "sig": "(Ljava/lang/String;D)Lnet/kyori/option/Option<Ljava/lang/Double;>;"}, {"nme": "enumOption", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Class;<PERSON>ja<PERSON>/lang/Enum;)Lnet/kyori/option/Option;", "sig": "<E:Lja<PERSON>/lang/Enum<TE;>;>(Ljava/lang/String;Ljava/lang/Class<TE;>;TE;)Lnet/kyori/option/Option<TE;>;"}, {"nme": "frozenView", "acc": 1, "dsc": "()Lnet/kyori/option/OptionSchema;"}, {"nme": "knownOptions", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Lnet/kyori/option/Option<*>;>;"}, {"nme": "has", "acc": 1, "dsc": "(Lnet/kyori/option/Option;)Z", "sig": "(Lnet/kyori/option/Option<*>;)Z"}, {"nme": "stateBuilder", "acc": 1, "dsc": "()Lnet/kyori/option/OptionState$Builder;"}, {"nme": "versionedStateBuilder", "acc": 1, "dsc": "()Lnet/kyori/option/OptionState$VersionedBuilder;"}, {"nme": "emptyState", "acc": 1, "dsc": "()Lnet/kyori/option/OptionState;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lnet/kyori/option/OptionSchemaImpl;"}]}, "net/kyori/option/value/ValueTypeImpl$Types$2.class": {"ver": 52, "acc": 32, "nme": "net/kyori/option/value/ValueTypeImpl$Types$2", "super": "net/kyori/option/value/ValueTypeImpl", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V"}, {"nme": "parse", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Bo<PERSON>an;", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "parse", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/IllegalArgumentException"]}], "flds": []}, "net/kyori/option/package-info.class": {"ver": 52, "acc": 5632, "nme": "net/kyori/option/package-info", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Lorg/jspecify/annotations/NullMarked;"}]}, "net/kyori/option/value/ValueTypeImpl$EnumType.class": {"ver": 52, "acc": 48, "nme": "net/kyori/option/value/ValueTypeImpl$EnumType", "super": "net/kyori/option/value/ValueTypeImpl", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(Lja<PERSON>/lang/Class<TE;>;)V"}, {"nme": "parse", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Enum;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;)TE;", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "parse", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/IllegalArgumentException"]}], "flds": [{"acc": 18, "nme": "values", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;TE;>;"}]}, "net/kyori/option/OptionImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/option/OptionImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/lang/String;Lnet/kyori/option/value/ValueType;Ljava/lang/Object;)V", "sig": "(Ljava/lang/String;Lnet/kyori/option/value/ValueType<TV;>;TV;)V"}, {"nme": "id", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "valueType", "acc": 1, "dsc": "()Lnet/kyori/option/value/ValueType;", "sig": "()Lnet/kyori/option/value/ValueType<TV;>;"}, {"nme": "defaultValue", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TV;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "id", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "type", "dsc": "Lnet/kyori/option/value/ValueType;", "sig": "Lnet/kyori/option/value/ValueType<TV;>;"}, {"acc": 18, "nme": "defaultValue", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;", "sig": "TV;"}]}, "net/kyori/option/OptionSchemaImpl$Instances.class": {"ver": 52, "acc": 48, "nme": "net/kyori/option/OptionSchemaImpl$Instances", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 8, "nme": "GLOBAL", "dsc": "Lnet/kyori/option/OptionSchemaImpl$MutableImpl;"}]}, "net/kyori/option/OptionState$Versioned.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/option/OptionState$Versioned", "super": "java/lang/Object", "mthds": [{"nme": "childStates", "acc": 1025, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/Integer;Lnet/kyori/option/OptionState;>;"}, {"nme": "at", "acc": 1025, "dsc": "(I)Lnet/kyori/option/OptionState$Versioned;"}], "flds": [], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$NonExtendable;"}]}, "net/kyori/option/OptionStateImpl$VersionedImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/option/OptionStateImpl$VersionedImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lnet/kyori/option/OptionSchema;Ljava/util/SortedMap;ILnet/kyori/option/OptionState;)V", "sig": "(Lnet/kyori/option/OptionSchema;Ljava/util/SortedMap<Ljava/lang/Integer;Lnet/kyori/option/OptionState;>;ILnet/kyori/option/OptionState;)V"}, {"nme": "schema", "acc": 1, "dsc": "()Lnet/kyori/option/OptionSchema;"}, {"nme": "has", "acc": 1, "dsc": "(Lnet/kyori/option/Option;)Z", "sig": "(Lnet/kyori/option/Option<*>;)Z"}, {"nme": "value", "acc": 1, "dsc": "(Lnet/kyori/option/Option;)Ljava/lang/Object;", "sig": "<V:Ljava/lang/Object;>(Lnet/kyori/option/Option<TV;>;)TV;"}, {"nme": "childStates", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/Integer;Lnet/kyori/option/OptionState;>;"}, {"nme": "at", "acc": 1, "dsc": "(I)Lnet/kyori/option/OptionState$Versioned;"}, {"nme": "flattened", "acc": 9, "dsc": "(Lnet/kyori/option/OptionSchema;Ljava/util/SortedMap;I)Lnet/kyori/option/OptionState;", "sig": "(Lnet/kyori/option/OptionSchema;Ljava/util/SortedMap<Ljava/lang/Integer;Lnet/kyori/option/OptionState;>;I)Lnet/kyori/option/OptionState;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "access$100", "acc": 4104, "dsc": "(Lnet/kyori/option/OptionStateImpl$VersionedImpl;)Lnet/kyori/option/OptionState;"}], "flds": [{"acc": 18, "nme": "schema", "dsc": "Lnet/kyori/option/OptionSchema;"}, {"acc": 18, "nme": "sets", "dsc": "Ljava/util/SortedMap;", "sig": "Ljava/util/SortedMap<Ljava/lang/Integer;Lnet/kyori/option/OptionState;>;"}, {"acc": 18, "nme": "targetVersion", "dsc": "I"}, {"acc": 18, "nme": "filtered", "dsc": "Lnet/kyori/option/OptionState;"}]}, "net/kyori/option/value/ValueTypeImpl$Types$4.class": {"ver": 52, "acc": 32, "nme": "net/kyori/option/value/ValueTypeImpl$Types$4", "super": "net/kyori/option/value/ValueTypeImpl", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V"}, {"nme": "parse", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Double;", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "parse", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/IllegalArgumentException"]}], "flds": []}, "net/kyori/option/OptionSchema$Mutable.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/option/OptionSchema$Mutable", "super": "java/lang/Object", "mthds": [{"nme": "stringOption", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;)Lnet/kyori/option/Option;", "sig": "(Ljava/lang/String;Ljava/lang/String;)Lnet/kyori/option/Option<Ljava/lang/String;>;"}, {"nme": "booleanOption", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)Lnet/kyori/option/Option;", "sig": "(<PERSON>ja<PERSON>/lang/String;Z)Lnet/kyori/option/Option<Ljava/lang/Boolean;>;"}, {"nme": "intOption", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Lnet/kyori/option/Option;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;I)Lnet/kyori/option/Option<Ljava/lang/Integer;>;"}, {"nme": "doubleOption", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;D)Lnet/kyori/option/Option;", "sig": "(Ljava/lang/String;D)Lnet/kyori/option/Option<Ljava/lang/Double;>;"}, {"nme": "enumOption", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Class;<PERSON>ja<PERSON>/lang/Enum;)Lnet/kyori/option/Option;", "sig": "<E:Lja<PERSON>/lang/Enum<TE;>;>(Ljava/lang/String;Ljava/lang/Class<TE;>;TE;)Lnet/kyori/option/Option<TE;>;"}, {"nme": "frozenView", "acc": 1025, "dsc": "()Lnet/kyori/option/OptionSchema;"}], "flds": [], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$NonExtendable;"}]}, "net/kyori/option/OptionStateImpl$BuilderImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/option/OptionStateImpl$BuilderImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lnet/kyori/option/OptionSchema;)V"}, {"nme": "build", "acc": 1, "dsc": "()Lnet/kyori/option/OptionState;"}, {"nme": "value", "acc": 1, "dsc": "(Lnet/kyori/option/Option;Ljava/lang/Object;)Lnet/kyori/option/OptionState$Builder;", "sig": "<V:Ljava/lang/Object;>(Lnet/kyori/option/Option<TV;>;TV;)Lnet/kyori/option/OptionState$Builder;"}, {"nme": "putAll", "acc": 2, "dsc": "(Ljava/util/Map;)V", "sig": "(Ljava/util/Map<Lnet/kyori/option/Option<*>;Ljava/lang/Object;>;)V"}, {"nme": "values", "acc": 1, "dsc": "(Lnet/kyori/option/OptionState;)Lnet/kyori/option/OptionState$Builder;"}, {"nme": "values", "acc": 1, "dsc": "(Lnet/kyori/option/value/ValueSource;)Lnet/kyori/option/OptionState$Builder;"}], "flds": [{"acc": 18, "nme": "schema", "dsc": "Lnet/kyori/option/OptionSchema;"}, {"acc": 18, "nme": "values", "dsc": "Ljava/util/IdentityHashMap;", "sig": "Ljava/util/IdentityHashMap<Lnet/kyori/option/Option<*>;Ljava/lang/Object;>;"}]}, "net/kyori/option/OptionSchemaImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/option/OptionSchemaImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lnet/kyori/option/OptionSchemaImpl;)V"}, {"nme": "knownOptions", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Lnet/kyori/option/Option<*>;>;"}, {"nme": "has", "acc": 1, "dsc": "(Lnet/kyori/option/Option;)Z", "sig": "(Lnet/kyori/option/Option<*>;)Z"}, {"nme": "stateBuilder", "acc": 1, "dsc": "()Lnet/kyori/option/OptionState$Builder;"}, {"nme": "versionedStateBuilder", "acc": 1, "dsc": "()Lnet/kyori/option/OptionState$VersionedBuilder;"}, {"nme": "emptyState", "acc": 1, "dsc": "()Lnet/kyori/option/OptionState;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 16, "nme": "emptyState", "dsc": "Lnet/kyori/option/OptionState;"}, {"acc": 16, "nme": "options", "dsc": "Ljava/util/concurrent/ConcurrentMap;", "sig": "Ljava/util/concurrent/ConcurrentMap<Ljava/lang/String;Lnet/kyori/option/Option<*>;>;"}]}, "net/kyori/option/value/ValueType.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/option/value/ValueType", "super": "java/lang/Object", "mthds": [{"nme": "stringType", "acc": 9, "dsc": "()Lnet/kyori/option/value/ValueType;", "sig": "()Lnet/kyori/option/value/ValueType<Ljava/lang/String;>;"}, {"nme": "booleanType", "acc": 9, "dsc": "()Lnet/kyori/option/value/ValueType;", "sig": "()Lnet/kyori/option/value/ValueType<Ljava/lang/Boolean;>;"}, {"nme": "integerType", "acc": 9, "dsc": "()Lnet/kyori/option/value/ValueType;", "sig": "()Lnet/kyori/option/value/ValueType<Ljava/lang/Integer;>;"}, {"nme": "doubleType", "acc": 9, "dsc": "()Lnet/kyori/option/value/ValueType;", "sig": "()Lnet/kyori/option/value/ValueType<Ljava/lang/Double;>;"}, {"nme": "enumType", "acc": 9, "dsc": "(Ljava/lang/Class;)Lnet/kyori/option/value/ValueType;", "sig": "<E:Ljava/lang/Enum<TE;>;>(Ljava/lang/Class<TE;>;)Lnet/kyori/option/value/ValueType<TE;>;"}, {"nme": "type", "acc": 1025, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<TT;>;"}, {"nme": "parse", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;)TT;", "exs": ["java/lang/IllegalArgumentException"]}], "flds": []}, "net/kyori/option/OptionState$VersionedBuilder.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/option/OptionState$VersionedBuilder", "super": "java/lang/Object", "mthds": [{"nme": "version", "acc": 1025, "dsc": "(ILjava/util/function/Consumer;)Lnet/kyori/option/OptionState$VersionedBuilder;", "sig": "(ILjava/util/function/Consumer<Lnet/kyori/option/OptionState$Builder;>;)Lnet/kyori/option/OptionState$VersionedBuilder;"}, {"nme": "build", "acc": 1025, "dsc": "()Lnet/kyori/option/OptionState$Versioned;"}], "flds": [], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$NonExtendable;"}]}, "net/kyori/option/OptionSchema.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/option/OptionSchema", "super": "java/lang/Object", "mthds": [{"nme": "globalSchema", "acc": 9, "dsc": "()Lnet/kyori/option/OptionSchema$Mutable;"}, {"nme": "childSchema", "acc": 9, "dsc": "(Lnet/kyori/option/OptionSchema;)Lnet/kyori/option/OptionSchema$Mutable;"}, {"nme": "emptySchema", "acc": 9, "dsc": "()Lnet/kyori/option/OptionSchema$Mutable;"}, {"nme": "knownOptions", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Lnet/kyori/option/Option<*>;>;"}, {"nme": "has", "acc": 1025, "dsc": "(Lnet/kyori/option/Option;)Z", "sig": "(Lnet/kyori/option/Option<*>;)Z"}, {"nme": "stateBuilder", "acc": 1025, "dsc": "()Lnet/kyori/option/OptionState$Builder;"}, {"nme": "versionedStateBuilder", "acc": 1025, "dsc": "()Lnet/kyori/option/OptionState$VersionedBuilder;"}, {"nme": "emptyState", "acc": 1025, "dsc": "()Lnet/kyori/option/OptionState;"}], "flds": [], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$NonExtendable;"}]}, "net/kyori/option/value/package-info.class": {"ver": 52, "acc": 5632, "nme": "net/kyori/option/value/package-info", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Lorg/jspecify/annotations/NullMarked;"}]}, "net/kyori/option/OptionState.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/option/OptionState", "super": "java/lang/Object", "mthds": [{"nme": "emptyOptionState", "acc": 131081, "dsc": "()Lnet/kyori/option/OptionState;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "optionState", "acc": 131081, "dsc": "()Lnet/kyori/option/OptionState$Builder;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "versionedOptionState", "acc": 131081, "dsc": "()Lnet/kyori/option/OptionState$VersionedBuilder;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "schema", "acc": 1025, "dsc": "()Lnet/kyori/option/OptionSchema;"}, {"nme": "has", "acc": 1025, "dsc": "(Lnet/kyori/option/Option;)Z", "sig": "(Lnet/kyori/option/Option<*>;)Z"}, {"nme": "value", "acc": 1025, "dsc": "(Lnet/kyori/option/Option;)Ljava/lang/Object;", "sig": "<V:Ljava/lang/Object;>(Lnet/kyori/option/Option<TV;>;)TV;"}], "flds": [], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$NonExtendable;"}]}, "net/kyori/option/OptionState$Builder.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/option/OptionState$Builder", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "(Lnet/kyori/option/Option;Ljava/lang/Object;)Lnet/kyori/option/OptionState$Builder;", "sig": "<V:Ljava/lang/Object;>(Lnet/kyori/option/Option<TV;>;TV;)Lnet/kyori/option/OptionState$Builder;"}, {"nme": "values", "acc": 1025, "dsc": "(Lnet/kyori/option/OptionState;)Lnet/kyori/option/OptionState$Builder;"}, {"nme": "values", "acc": 1025, "dsc": "(Lnet/kyori/option/value/ValueSource;)Lnet/kyori/option/OptionState$Builder;"}, {"nme": "build", "acc": 1025, "dsc": "()Lnet/kyori/option/OptionState;"}], "flds": [], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$NonExtendable;"}]}, "net/kyori/option/value/ValueSources$EnvironmentVariable.class": {"ver": 52, "acc": 48, "nme": "net/kyori/option/value/ValueSources$EnvironmentVariable", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "value", "acc": 1, "dsc": "(Lnet/kyori/option/Option;)Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Lnet/kyori/option/Option<TT;>;)TT;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "ENVIRONMENT_SUBST_PATTERN", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 26, "nme": "ENVIRONMENT_VAR_SEPARATOR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "_"}, {"acc": 18, "nme": "prefix", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "net/kyori/option/value/ValueSource.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/option/value/ValueSource", "super": "java/lang/Object", "mthds": [{"nme": "environmentVariable", "acc": 9, "dsc": "()Lnet/kyori/option/value/ValueSource;"}, {"nme": "environmentVariable", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;)Lnet/kyori/option/value/ValueSource;"}, {"nme": "systemProperty", "acc": 9, "dsc": "()Lnet/kyori/option/value/ValueSource;"}, {"nme": "systemProperty", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;)Lnet/kyori/option/value/ValueSource;"}, {"nme": "value", "acc": 1025, "dsc": "(Lnet/kyori/option/Option;)Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Lnet/kyori/option/Option<TT;>;)TT;"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/FunctionalInterface;"}]}, "META-INF/versions/9/module-info.class": {"ver": 53, "acc": 32768, "nme": "module-info", "mthds": [], "flds": [], "vanns": [{"dsc": "Lorg/jspecify/annotations/NullMarked;"}]}, "net/kyori/option/value/ValueSources$SystemProperty.class": {"ver": 52, "acc": 48, "nme": "net/kyori/option/value/ValueSources$SystemProperty", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "value", "acc": 1, "dsc": "(Lnet/kyori/option/Option;)Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Lnet/kyori/option/Option<TT;>;)TT;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "SYSTEM_PROP_SUBST_PATTERN", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 26, "nme": "SYSTEM_PROPERTY_SEPARATOR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "."}, {"acc": 18, "nme": "prefix", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "net/kyori/option/value/ValueTypeImpl$Types$1.class": {"ver": 52, "acc": 32, "nme": "net/kyori/option/value/ValueTypeImpl$Types$1", "super": "net/kyori/option/value/ValueTypeImpl", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V"}, {"nme": "parse", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "parse", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/IllegalArgumentException"]}], "flds": []}, "net/kyori/option/value/ValueSources.class": {"ver": 52, "acc": 48, "nme": "net/kyori/option/value/ValueSources", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "ENVIRONMENT", "dsc": "Lnet/kyori/option/value/ValueSource;"}, {"acc": 24, "nme": "SYSTEM_PROPERTIES", "dsc": "Lnet/kyori/option/value/ValueSource;"}]}, "net/kyori/option/OptionStateImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/option/OptionStateImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lnet/kyori/option/OptionSchema;Ljava/util/IdentityHashMap;)V", "sig": "(Lnet/kyori/option/OptionSchema;Ljava/util/IdentityHashMap<Lnet/kyori/option/Option<*>;Ljava/lang/Object;>;)V"}, {"nme": "schema", "acc": 1, "dsc": "()Lnet/kyori/option/OptionSchema;"}, {"nme": "has", "acc": 1, "dsc": "(Lnet/kyori/option/Option;)Z", "sig": "(Lnet/kyori/option/Option<*>;)Z"}, {"nme": "value", "acc": 1, "dsc": "(Lnet/kyori/option/Option;)Ljava/lang/Object;", "sig": "<V:Ljava/lang/Object;>(Lnet/kyori/option/Option<TV;>;)TV;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "access$000", "acc": 4104, "dsc": "(Lnet/kyori/option/OptionStateImpl;)Ljava/util/IdentityHashMap;"}], "flds": [{"acc": 18, "nme": "schema", "dsc": "Lnet/kyori/option/OptionSchema;"}, {"acc": 18, "nme": "values", "dsc": "Ljava/util/IdentityHashMap;", "sig": "Ljava/util/IdentityHashMap<Lnet/kyori/option/Option<*>;Ljava/lang/Object;>;"}]}, "net/kyori/option/value/ValueTypeImpl$Types.class": {"ver": 52, "acc": 48, "nme": "net/kyori/option/value/ValueTypeImpl$Types", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 8, "nme": "STRING", "dsc": "Lnet/kyori/option/value/ValueType;", "sig": "Lnet/kyori/option/value/ValueType<Ljava/lang/String;>;"}, {"acc": 8, "nme": "BOOLEAN", "dsc": "Lnet/kyori/option/value/ValueType;", "sig": "Lnet/kyori/option/value/ValueType<Ljava/lang/Boolean;>;"}, {"acc": 8, "nme": "INT", "dsc": "Lnet/kyori/option/value/ValueType;", "sig": "Lnet/kyori/option/value/ValueType<Ljava/lang/Integer;>;"}, {"acc": 8, "nme": "DOUBLE", "dsc": "Lnet/kyori/option/value/ValueType;", "sig": "Lnet/kyori/option/value/ValueType<Ljava/lang/Double;>;"}]}, "net/kyori/option/OptionStateImpl$VersionedBuilderImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/option/OptionStateImpl$VersionedBuilderImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lnet/kyori/option/OptionSchema;)V"}, {"nme": "build", "acc": 1, "dsc": "()Lnet/kyori/option/OptionState$Versioned;"}, {"nme": "version", "acc": 1, "dsc": "(ILjava/util/function/Consumer;)Lnet/kyori/option/OptionState$VersionedBuilder;", "sig": "(ILjava/util/function/Consumer<Lnet/kyori/option/OptionState$Builder;>;)Lnet/kyori/option/OptionState$VersionedBuilder;"}, {"nme": "lambda$version$0", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/Integer;)Lnet/kyori/option/OptionStateImpl$BuilderImpl;"}], "flds": [{"acc": 18, "nme": "schema", "dsc": "Lnet/kyori/option/OptionSchema;"}, {"acc": 18, "nme": "builders", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Integer;Lnet/kyori/option/OptionStateImpl$BuilderImpl;>;"}]}, "net/kyori/option/Option.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/option/Option", "super": "java/lang/Object", "mthds": [{"nme": "booleanOption", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)Lnet/kyori/option/Option;", "sig": "(<PERSON>ja<PERSON>/lang/String;Z)Lnet/kyori/option/Option<Ljava/lang/Boolean;>;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "enumOption", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Class;<PERSON>ja<PERSON>/lang/Enum;)Lnet/kyori/option/Option;", "sig": "<E:Lja<PERSON>/lang/Enum<TE;>;>(Ljava/lang/String;Ljava/lang/Class<TE;>;TE;)Lnet/kyori/option/Option<TE;>;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "id", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "type", "acc": 131073, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<TV;>;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "valueType", "acc": 1025, "dsc": "()Lnet/kyori/option/value/ValueType;", "sig": "()Lnet/kyori/option/value/ValueType<TV;>;"}, {"nme": "defaultValue", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TV;"}], "flds": [], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$NonExtendable;"}]}, "net/kyori/option/value/ValueTypeImpl$Types$3.class": {"ver": 52, "acc": 32, "nme": "net/kyori/option/value/ValueTypeImpl$Types$3", "super": "net/kyori/option/value/ValueTypeImpl", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V"}, {"nme": "parse", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Integer;", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "parse", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/IllegalArgumentException"]}], "flds": []}, "net/kyori/option/value/ValueTypeImpl.class": {"ver": 52, "acc": 1056, "nme": "net/kyori/option/value/ValueTypeImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(Ljava/lang/Class<TT;>;)V"}, {"nme": "type", "acc": 1, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<TT;>;"}, {"nme": "doNotKnowHowToTurn", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Class;Lja<PERSON>/lang/String;)Ljava/lang/IllegalArgumentException;", "sig": "(L<PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/Class<*>;Ljava/lang/String;)Ljava/lang/IllegalArgumentException;"}], "flds": [{"acc": 18, "nme": "type", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<TT;>;"}]}}}}