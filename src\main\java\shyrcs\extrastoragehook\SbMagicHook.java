package shyrcs.extrastoragehook;

import org.bukkit.plugin.java.JavaPlugin;
import shyrcs.extrastoragehook.application.PluginBoot;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * SbMagicHook - Plugin chính kết nối ExtraStorage với Discord
 * Dựa trên HyperHook nhưng được thiết kế để hoạt động với ExtraStorage API
 * 
 * <AUTHOR>
 * @version 1.0.0-BETA
 */
public class SbMagicHook extends JavaPlugin {
    
    public static Logger console = Logger.getLogger("Minecraft");
    
    @Override
    public void onEnable() {
        try {
            // License validation (uncomment for production)
            /*
            String licenseKey = getConfig().getString("license-key", "");
            if (!shyrcs.extrastoragehook.security.LicenseValidator.validateLicense(licenseKey)) {
                getLogger().severe("=================================");
                getLogger().severe("   INVALID OR MISSING LICENSE");
                getLogger().severe("=================================");
                getLogger().severe("Please contact the developer for a valid license key.");
                getLogger().severe("Hardware ID: " + shyrcs.extrastoragehook.security.LicenseValidator.getHardwareId());
                getServer().getPluginManager().disablePlugin(this);
                return;
            }
            */

            PluginBoot.start(this);
            info("SbMagicHook đã được kích hoạt thành công!");
        } catch (Exception e) {
            error("Lỗi khi khởi động plugin: " + e.getMessage());
            e.printStackTrace();
            getServer().getPluginManager().disablePlugin(this);
        }
    }
    
    @Override
    public void onDisable() {
        try {
            PluginBoot.stop();
            info("SbMagicHook đã được tắt thành công!");
        } catch (Exception e) {
            error("Lỗi khi tắt plugin: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Ghi log thông tin
     */
    public static void info(String message) {
        console.log(Level.INFO, "[SbMagicHook] " + message);
    }
    
    /**
     * Ghi log cảnh báo
     */
    public static void warn(String message) {
        console.log(Level.WARNING, "[SbMagicHook] " + message);
    }
    
    /**
     * Ghi log lỗi
     */
    public static void error(String message) {
        console.log(Level.SEVERE, "[SbMagicHook] " + message);
    }
    
    /**
     * Chuyển đổi color codes
     */
    public static String color(String message) {
        return message.replace('&', '§');
    }
}
