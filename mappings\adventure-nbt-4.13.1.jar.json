{"md5": "0230235f910934f9020a880c470dc1f3", "sha2": "bdbd32e78fc7fdc669fba5d34cda63be78e51bdb", "sha256": "a0a7a7f4428352d29696da9c8833093d5d16ddccf53ae88a35c76a0795e7358e", "contents": {"classes": {"net/kyori/adventure/nbt/CompoundTagSetter.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/nbt/CompoundTagSetter", "super": "java/lang/Object", "mthds": [{"nme": "put", "acc": 1025, "dsc": "(Lja<PERSON>/lang/String;Lnet/kyori/adventure/nbt/BinaryTag;)Ljava/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;Lnet/kyori/adventure/nbt/BinaryTag;)TR;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "put", "acc": 1025, "dsc": "(Lnet/kyori/adventure/nbt/CompoundBinaryTag;)Ljava/lang/Object;", "sig": "(Lnet/kyori/adventure/nbt/CompoundBinaryTag;)TR;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "put", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/Object;", "sig": "(Ljava/util/Map<Ljava/lang/String;+Lnet/kyori/adventure/nbt/BinaryTag;>;)TR;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "remove", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;)TR;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "remove", "acc": 1025, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/function/Consumer;)Ljava/lang/Object;", "sig": "(Ljava/lang/String;Ljava/util/function/Consumer<-Lnet/kyori/adventure/nbt/BinaryTag;>;)TR;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "putBoolean", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)Ljava/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;Z)TR;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "putByte", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;B)Ljava/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;B)TR;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "putShort", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;S)Ljava/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;S)TR;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "putInt", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;I)TR;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "putLong", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;J)Ljava/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;J)TR;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "putFloat", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;F)Ljava/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;F)TR;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "putDouble", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;D)Ljava/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;D)TR;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "putByteArray", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[B)<PERSON>ja<PERSON>/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;[B)TR;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "putString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;)TR;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "putIntArray", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[I)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;[I)TR;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "putLongArray", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[J)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;[J)TR;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": []}, "net/kyori/adventure/nbt/IntArrayBinaryTagImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/nbt/IntArrayBinaryTagImpl", "super": "net/kyori/adventure/nbt/ArrayBinaryTagImpl", "mthds": [{"nme": "<init>", "acc": 128, "dsc": "([I)V"}, {"nme": "value", "acc": 1, "dsc": "()[I"}, {"nme": "size", "acc": 1, "dsc": "()I"}, {"nme": "get", "acc": 1, "dsc": "(I)I"}, {"nme": "iterator", "acc": 1, "dsc": "()Ljava/util/PrimitiveIterator$OfInt;"}, {"nme": "spliterator", "acc": 1, "dsc": "()Ljava/util/Spliterator$OfInt;"}, {"nme": "stream", "acc": 1, "dsc": "()Ljava/util/stream/IntStream;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "forEachInt", "acc": 1, "dsc": "(L<PERSON><PERSON>/util/function/IntConsumer;)V"}, {"nme": "value", "acc": 8, "dsc": "(Lnet/kyori/adventure/nbt/IntArrayBinaryTag;)[I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "examinableProperties", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<+Lnet/kyori/examination/ExaminableProperty;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "spliterator", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/util/Spliterator;"}, {"nme": "iterator", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/util/Iterator;"}], "flds": [{"acc": 16, "nme": "value", "dsc": "[I"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/Debug$Renderer;", "vals": ["text", "\"int[\" + this.value.length + \"]\"", "childrenA<PERSON>y", "this.value", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "this.value.length > 0"]}]}, "net/kyori/adventure/nbt/IOStreamUtil$2.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/nbt/IOStreamUtil$2", "super": "java/io/OutputStream", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/io/OutputStream;)V"}, {"nme": "write", "acc": 1, "dsc": "(I)V", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "([B)V", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "([BII)V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 4112, "nme": "val$stream", "dsc": "Ljava/io/OutputStream;"}]}, "net/kyori/adventure/nbt/CompoundBinaryTagImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/nbt/CompoundBinaryTagImpl", "super": "net/kyori/adventure/nbt/AbstractBinaryTag", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/util/Map;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;Lnet/kyori/adventure/nbt/BinaryTag;>;)V"}, {"nme": "contains", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lnet/kyori/adventure/nbt/BinaryTagType;)Z", "sig": "(L<PERSON><PERSON>/lang/String;Lnet/kyori/adventure/nbt/BinaryTagType<*>;)Z"}, {"nme": "keySet", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "get", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/nbt/BinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "put", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lnet/kyori/adventure/nbt/BinaryTag;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "put", "acc": 1, "dsc": "(Lnet/kyori/adventure/nbt/CompoundBinaryTag;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "put", "acc": 1, "dsc": "(Ljava/util/Map;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "sig": "(Ljava/util/Map<Ljava/lang/String;+Lnet/kyori/adventure/nbt/BinaryTag;>;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "remove", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/function/Consumer;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "sig": "(Ljava/lang/String;Ljava/util/function/Consumer<-Lnet/kyori/adventure/nbt/BinaryTag;>;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "getByte", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;B)B"}, {"nme": "getShort", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;S)S"}, {"nme": "getInt", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)I"}, {"nme": "getLong", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;J)J"}, {"nme": "getFloat", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;F)F"}, {"nme": "getDouble", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;D)D"}, {"nme": "getByteArray", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[B"}, {"nme": "getByteArray", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[B)[B"}, {"nme": "getString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "getList", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lnet/kyori/adventure/nbt/ListBinaryTag;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "getList", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Lnet/kyori/adventure/nbt/BinaryTagType;Lnet/kyori/adventure/nbt/ListBinaryTag;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "sig": "(Ljava/lang/String;Lnet/kyori/adventure/nbt/BinaryTagType<+Lnet/kyori/adventure/nbt/BinaryTag;>;Lnet/kyori/adventure/nbt/ListBinaryTag;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "getCompound", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lnet/kyori/adventure/nbt/CompoundBinaryTag;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "getIntArray", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[I"}, {"nme": "getIntArray", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[I)[I"}, {"nme": "getLongArray", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[J"}, {"nme": "getLongArray", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[J)[J"}, {"nme": "edit", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Consumer;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "sig": "(Ljava/util/function/Consumer<Ljava/util/Map<Ljava/lang/String;Lnet/kyori/adventure/nbt/BinaryTag;>;>;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "examinableProperties", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<+Lnet/kyori/examination/ExaminableProperty;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "iterator", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Iterator;", "sig": "()Ljava/util/Iterator<Ljava/util/Map$Entry<Ljava/lang/String;+Lnet/kyori/adventure/nbt/BinaryTag;>;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "for<PERSON>ach", "acc": 1, "dsc": "(Ljava/util/function/Consumer;)V", "sig": "(Ljava/util/function/Consumer<-Ljava/util/Map$Entry<Ljava/lang/String;+Lnet/kyori/adventure/nbt/BinaryTag;>;>;)V"}, {"nme": "remove", "acc": 4161, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/function/Consumer;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "put", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "put", "acc": 4161, "dsc": "(Lnet/kyori/adventure/nbt/CompoundBinaryTag;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "put", "acc": 4161, "dsc": "(Lja<PERSON>/lang/String;Lnet/kyori/adventure/nbt/BinaryTag;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "lambda$remove$3", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/function/Consumer;Ljava/util/Map;)V"}, {"nme": "lambda$put$2", "acc": 4106, "dsc": "(Ljava/util/Map;Ljava/util/Map;)V"}, {"nme": "lambda$put$1", "acc": 4106, "dsc": "(Lnet/kyori/adventure/nbt/CompoundBinaryTag;Ljava/util/Map;)V"}, {"nme": "lambda$put$0", "acc": 4106, "dsc": "(L<PERSON><PERSON>/lang/String;Lnet/kyori/adventure/nbt/BinaryTag;Ljava/util/Map;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "EMPTY", "dsc": "Lnet/kyori/adventure/nbt/CompoundBinaryTag;"}, {"acc": 18, "nme": "tags", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lnet/kyori/adventure/nbt/BinaryTag;>;"}, {"acc": 18, "nme": "hashCode", "dsc": "I"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/Debug$Renderer;", "vals": ["text", "\"CompoundBinaryTag[length=\" + this.tags.size() + \"]\"", "childrenA<PERSON>y", "this.tags.entrySet().toArray()", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "!this.tags.isEmpty()"]}]}, "net/kyori/adventure/nbt/BinaryTagReaderImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/nbt/BinaryTagReaderImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(J)V"}, {"nme": "read", "acc": 1, "dsc": "(Ljava/nio/file/Path;Lnet/kyori/adventure/nbt/BinaryTagIO$Compression;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "read", "acc": 1, "dsc": "(Ljava/io/InputStream;Lnet/kyori/adventure/nbt/BinaryTagIO$Compression;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "read", "acc": 1, "dsc": "(Ljava/io/DataInput;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "readNamed", "acc": 1, "dsc": "(Ljava/nio/file/Path;Lnet/kyori/adventure/nbt/BinaryTagIO$Compression;)Ljava/util/Map$Entry;", "sig": "(Ljava/nio/file/Path;Lnet/kyori/adventure/nbt/BinaryTagIO$Compression;)Ljava/util/Map$Entry<Ljava/lang/String;Lnet/kyori/adventure/nbt/CompoundBinaryTag;>;", "exs": ["java/io/IOException"]}, {"nme": "readNamed", "acc": 1, "dsc": "(Ljava/io/InputStream;Lnet/kyori/adventure/nbt/BinaryTagIO$Compression;)Ljava/util/Map$Entry;", "sig": "(Ljava/io/InputStream;Lnet/kyori/adventure/nbt/BinaryTagIO$Compression;)Ljava/util/Map$Entry<Ljava/lang/String;Lnet/kyori/adventure/nbt/CompoundBinaryTag;>;", "exs": ["java/io/IOException"]}, {"nme": "readNamed", "acc": 1, "dsc": "(Ljava/io/DataInput;)Ljava/util/Map$Entry;", "sig": "(Ljava/io/DataInput;)Ljava/util/Map$Entry<Ljava/lang/String;Lnet/kyori/adventure/nbt/CompoundBinaryTag;>;", "exs": ["java/io/IOException"]}, {"nme": "requireCompound", "acc": 10, "dsc": "(Lnet/kyori/adventure/nbt/BinaryTagType;)V", "sig": "(Lnet/kyori/adventure/nbt/BinaryTagType<+Lnet/kyori/adventure/nbt/BinaryTag;>;)V", "exs": ["java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "maxBytes", "dsc": "J"}, {"acc": 24, "nme": "UNLIMITED", "dsc": "Lnet/kyori/adventure/nbt/BinaryTagIO$Reader;"}, {"acc": 24, "nme": "DEFAULT_LIMIT", "dsc": "Lnet/kyori/adventure/nbt/BinaryTagIO$Reader;"}]}, "net/kyori/adventure/nbt/BinaryTagType$Writer.class": {"ver": 52, "acc": 1536, "nme": "net/kyori/adventure/nbt/BinaryTagType$Writer", "super": "java/lang/Object", "mthds": [{"nme": "write", "acc": 1025, "dsc": "(Lnet/kyori/adventure/nbt/BinaryTag;Ljava/io/DataOutput;)V", "sig": "(TT;Ljava/io/DataOutput;)V", "exs": ["java/io/IOException"]}], "flds": []}, "net/kyori/adventure/nbt/LongArrayBinaryTagImpl$1.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/nbt/LongArrayBinaryTagImpl$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lnet/kyori/adventure/nbt/LongArrayBinaryTagImpl;)V"}, {"nme": "hasNext", "acc": 1, "dsc": "()Z"}, {"nme": "nextLong", "acc": 1, "dsc": "()J"}], "flds": [{"acc": 2, "nme": "index", "dsc": "I"}, {"acc": 4112, "nme": "this$0", "dsc": "Lnet/kyori/adventure/nbt/LongArrayBinaryTagImpl;"}]}, "net/kyori/adventure/nbt/ShortBinaryTagImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/nbt/ShortBinaryTagImpl", "super": "net/kyori/adventure/nbt/AbstractBinaryTag", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(S)V"}, {"nme": "value", "acc": 1, "dsc": "()S"}, {"nme": "byteValue", "acc": 1, "dsc": "()B"}, {"nme": "doubleValue", "acc": 1, "dsc": "()D"}, {"nme": "floatValue", "acc": 1, "dsc": "()F"}, {"nme": "intValue", "acc": 1, "dsc": "()I"}, {"nme": "longValue", "acc": 1, "dsc": "()J"}, {"nme": "shortValue", "acc": 1, "dsc": "()S"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "examinableProperties", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<+Lnet/kyori/examination/ExaminableProperty;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": [{"acc": 18, "nme": "value", "dsc": "S"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/Debug$Renderer;", "vals": ["text", "String.valueOf(this.value) + \"s\"", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "false"]}]}, "net/kyori/adventure/nbt/TagStringIO$1.class": {"ver": 52, "acc": 4128, "nme": "net/kyori/adventure/nbt/TagStringIO$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "net/kyori/adventure/nbt/BinaryTagType$Impl.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/nbt/BinaryTagType$Impl", "super": "net/kyori/adventure/nbt/BinaryTagType", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/lang/Class;BLnet/kyori/adventure/nbt/BinaryTagType$Reader;Lnet/kyori/adventure/nbt/BinaryTagType$Writer;)V", "sig": "(Ljava/lang/Class<TT;>;BLnet/kyori/adventure/nbt/BinaryTagType$Reader<TT;>;Lnet/kyori/adventure/nbt/BinaryTagType$Writer<TT;>;)V"}, {"nme": "read", "acc": 17, "dsc": "(Ljava/io/DataInput;)Lnet/kyori/adventure/nbt/BinaryTag;", "sig": "(Ljava/io/DataInput;)TT;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "write", "acc": 17, "dsc": "(Lnet/kyori/adventure/nbt/BinaryTag;Ljava/io/DataOutput;)V", "sig": "(TT;Ljava/io/DataOutput;)V", "exs": ["java/io/IOException"]}, {"nme": "id", "acc": 17, "dsc": "()B"}, {"nme": "numeric", "acc": 0, "dsc": "()Z"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "test", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "flds": [{"acc": 16, "nme": "type", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<TT;>;"}, {"acc": 16, "nme": "id", "dsc": "B"}, {"acc": 18, "nme": "reader", "dsc": "Lnet/kyori/adventure/nbt/BinaryTagType$Reader;", "sig": "Lnet/kyori/adventure/nbt/BinaryTagType$Reader<TT;>;"}, {"acc": 18, "nme": "writer", "dsc": "Lnet/kyori/adventure/nbt/BinaryTagType$Writer;", "sig": "Lnet/kyori/adventure/nbt/BinaryTagType$Writer<TT;>;"}]}, "net/kyori/adventure/nbt/ListBinaryTag$Builder.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/nbt/ListBinaryTag$Builder", "super": "java/lang/Object", "mthds": [{"nme": "build", "acc": 1025, "dsc": "()Lnet/kyori/adventure/nbt/ListBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": []}, "net/kyori/adventure/nbt/ListBinaryTagImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/nbt/ListBinaryTagImpl", "super": "net/kyori/adventure/nbt/AbstractBinaryTag", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lnet/kyori/adventure/nbt/BinaryTagType;Ljava/util/List;)V", "sig": "(Lnet/kyori/adventure/nbt/BinaryTagType<+Lnet/kyori/adventure/nbt/BinaryTag;>;Ljava/util/List<Lnet/kyori/adventure/nbt/BinaryTag;>;)V"}, {"nme": "elementType", "acc": 1, "dsc": "()Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "()Lnet/kyori/adventure/nbt/BinaryTagType<+Lnet/kyori/adventure/nbt/BinaryTag;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "size", "acc": 1, "dsc": "()I"}, {"nme": "get", "acc": 1, "dsc": "(I)Lnet/kyori/adventure/nbt/BinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "set", "acc": 1, "dsc": "(ILnet/kyori/adventure/nbt/BinaryTag;Ljava/util/function/Consumer;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "sig": "(ILnet/kyori/adventure/nbt/BinaryTag;Ljava/util/function/Consumer<-Lnet/kyori/adventure/nbt/BinaryTag;>;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "remove", "acc": 1, "dsc": "(ILjava/util/function/Consumer;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "sig": "(ILjava/util/function/Consumer<-Lnet/kyori/adventure/nbt/BinaryTag;>;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "add", "acc": 1, "dsc": "(Lnet/kyori/adventure/nbt/BinaryTag;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "add", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "sig": "(Lja<PERSON>/lang/Iterable<+Lnet/kyori/adventure/nbt/BinaryTag;>;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "noAddEnd", "acc": 8, "dsc": "(Lnet/kyori/adventure/nbt/BinaryTag;)V"}, {"nme": "mustBeSameType", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "(Ljava/lang/Iterable<+Lnet/kyori/adventure/nbt/BinaryTag;>;)Lnet/kyori/adventure/nbt/BinaryTagType<*>;"}, {"nme": "mustBeSameType", "acc": 8, "dsc": "(Lnet/kyori/adventure/nbt/BinaryTag;Lnet/kyori/adventure/nbt/BinaryTagType;)V", "sig": "(Lnet/kyori/adventure/nbt/BinaryTag;Lnet/kyori/adventure/nbt/BinaryTagType<+Lnet/kyori/adventure/nbt/BinaryTag;>;)V"}, {"nme": "edit", "acc": 2, "dsc": "(Ljava/util/function/Consumer;Lnet/kyori/adventure/nbt/BinaryTagType;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "sig": "(Ljava/util/function/Consumer<Ljava/util/List<Lnet/kyori/adventure/nbt/BinaryTag;>;>;Lnet/kyori/adventure/nbt/BinaryTagType<+Lnet/kyori/adventure/nbt/BinaryTag;>;)Lnet/kyori/adventure/nbt/ListBinaryTag;"}, {"nme": "stream", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<Lnet/kyori/adventure/nbt/BinaryTag;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "iterator", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Iterator;", "sig": "()Ljava/util/Iterator<Lnet/kyori/adventure/nbt/BinaryTag;>;"}, {"nme": "for<PERSON>ach", "acc": 1, "dsc": "(Ljava/util/function/Consumer;)V", "sig": "(Ljava/util/function/Consumer<-Lnet/kyori/adventure/nbt/BinaryTag;>;)V"}, {"nme": "spliterator", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Spliterator;", "sig": "()Ljava/util/Spliterator<Lnet/kyori/adventure/nbt/BinaryTag;>;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "examinableProperties", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<+Lnet/kyori/examination/ExaminableProperty;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "add", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)<PERSON><PERSON><PERSON>/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "add", "acc": 4161, "dsc": "(Lnet/kyori/adventure/nbt/BinaryTag;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "lambda$add$3", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;<PERSON><PERSON><PERSON>/util/List;)V"}, {"nme": "lambda$add$2", "acc": 4106, "dsc": "(Lnet/kyori/adventure/nbt/BinaryTag;Ljava/util/List;)V"}, {"nme": "lambda$remove$1", "acc": 4106, "dsc": "(ILjava/util/function/Consumer;Ljava/util/List;)V"}, {"nme": "lambda$set$0", "acc": 4106, "dsc": "(ILnet/kyori/adventure/nbt/BinaryTag;Ljava/util/function/Consumer;Ljava/util/List;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "EMPTY", "dsc": "Lnet/kyori/adventure/nbt/ListBinaryTag;"}, {"acc": 18, "nme": "tags", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lnet/kyori/adventure/nbt/BinaryTag;>;"}, {"acc": 18, "nme": "elementType", "dsc": "Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "Lnet/kyori/adventure/nbt/BinaryTagType<+Lnet/kyori/adventure/nbt/BinaryTag;>;"}, {"acc": 18, "nme": "hashCode", "dsc": "I"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/Debug$Renderer;", "vals": ["text", "\"ListBinaryTag[type=\" + this.type.toString() + \"]\"", "childrenA<PERSON>y", "this.tags.toArray()", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "!this.tags.isEmpty()"]}]}, "net/kyori/adventure/nbt/StringBinaryTag.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/nbt/StringBinaryTag", "super": "java/lang/Object", "mthds": [{"nme": "of", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/nbt/StringBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "type", "acc": 1, "dsc": "()Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "()Lnet/kyori/adventure/nbt/BinaryTagType<Lnet/kyori/adventure/nbt/StringBinaryTag;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "value", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": []}, "net/kyori/adventure/nbt/BinaryTagWriterImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/nbt/BinaryTagWriterImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "write", "acc": 1, "dsc": "(Lnet/kyori/adventure/nbt/CompoundBinaryTag;Ljava/nio/file/Path;Lnet/kyori/adventure/nbt/BinaryTagIO$Compression;)V", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lnet/kyori/adventure/nbt/CompoundBinaryTag;Ljava/io/OutputStream;Lnet/kyori/adventure/nbt/BinaryTagIO$Compression;)V", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lnet/kyori/adventure/nbt/CompoundBinaryTag;Ljava/io/DataOutput;)V", "exs": ["java/io/IOException"]}, {"nme": "writeNamed", "acc": 1, "dsc": "(Ljava/util/Map$Entry;Ljava/nio/file/Path;Lnet/kyori/adventure/nbt/BinaryTagIO$Compression;)V", "sig": "(Ljava/util/Map$Entry<Ljava/lang/String;Lnet/kyori/adventure/nbt/CompoundBinaryTag;>;Ljava/nio/file/Path;Lnet/kyori/adventure/nbt/BinaryTagIO$Compression;)V", "exs": ["java/io/IOException"]}, {"nme": "writeNamed", "acc": 1, "dsc": "(Ljava/util/Map$Entry;Ljava/io/OutputStream;Lnet/kyori/adventure/nbt/BinaryTagIO$Compression;)V", "sig": "(Ljava/util/Map$Entry<Ljava/lang/String;Lnet/kyori/adventure/nbt/CompoundBinaryTag;>;Ljava/io/OutputStream;Lnet/kyori/adventure/nbt/BinaryTagIO$Compression;)V", "exs": ["java/io/IOException"]}, {"nme": "writeNamed", "acc": 1, "dsc": "(Ljava/util/Map$Entry;Ljava/io/DataOutput;)V", "sig": "(Ljava/util/Map$Entry<Ljava/lang/String;Lnet/kyori/adventure/nbt/CompoundBinaryTag;>;Ljava/io/DataOutput;)V", "exs": ["java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "INSTANCE", "dsc": "Lnet/kyori/adventure/nbt/BinaryTagIO$Writer;"}]}, "net/kyori/adventure/nbt/ListBinaryTag.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/nbt/ListBinaryTag", "super": "java/lang/Object", "mthds": [{"nme": "empty", "acc": 9, "dsc": "()Lnet/kyori/adventure/nbt/ListBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "from", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "sig": "(Lja<PERSON>/lang/Iterable<+Lnet/kyori/adventure/nbt/BinaryTag;>;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "builder", "acc": 9, "dsc": "()Lnet/kyori/adventure/nbt/ListBinaryTag$Builder;", "sig": "()Lnet/kyori/adventure/nbt/ListBinaryTag$Builder<Lnet/kyori/adventure/nbt/BinaryTag;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "builder", "acc": 9, "dsc": "(Lnet/kyori/adventure/nbt/BinaryTagType;)Lnet/kyori/adventure/nbt/ListBinaryTag$Builder;", "sig": "<T::Lnet/kyori/adventure/nbt/BinaryTag;>(Lnet/kyori/adventure/nbt/BinaryTagType<TT;>;)Lnet/kyori/adventure/nbt/ListBinaryTag$Builder<TT;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "of", "acc": 9, "dsc": "(Lnet/kyori/adventure/nbt/BinaryTagType;Ljava/util/List;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "sig": "(Lnet/kyori/adventure/nbt/BinaryTagType<+Lnet/kyori/adventure/nbt/BinaryTag;>;Ljava/util/List<Lnet/kyori/adventure/nbt/BinaryTag;>;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "type", "acc": 1, "dsc": "()Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "()Lnet/kyori/adventure/nbt/BinaryTagType<Lnet/kyori/adventure/nbt/ListBinaryTag;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "listType", "acc": 131073, "dsc": "()Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "()Lnet/kyori/adventure/nbt/BinaryTagType<+Lnet/kyori/adventure/nbt/BinaryTag;>;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "elementType", "acc": 1025, "dsc": "()Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "()Lnet/kyori/adventure/nbt/BinaryTagType<+Lnet/kyori/adventure/nbt/BinaryTag;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "size", "acc": 1025, "dsc": "()I"}, {"nme": "get", "acc": 1025, "dsc": "(I)Lnet/kyori/adventure/nbt/BinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "set", "acc": 1025, "dsc": "(ILnet/kyori/adventure/nbt/BinaryTag;Ljava/util/function/Consumer;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "sig": "(ILnet/kyori/adventure/nbt/BinaryTag;Ljava/util/function/Consumer<-Lnet/kyori/adventure/nbt/BinaryTag;>;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "remove", "acc": 1025, "dsc": "(ILjava/util/function/Consumer;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "sig": "(ILjava/util/function/Consumer<-Lnet/kyori/adventure/nbt/BinaryTag;>;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "getByte", "acc": 1, "dsc": "(I)B"}, {"nme": "getByte", "acc": 1, "dsc": "(IB)B"}, {"nme": "getShort", "acc": 1, "dsc": "(I)S"}, {"nme": "getShort", "acc": 1, "dsc": "(IS)S"}, {"nme": "getInt", "acc": 1, "dsc": "(I)I"}, {"nme": "getInt", "acc": 1, "dsc": "(II)I"}, {"nme": "getLong", "acc": 1, "dsc": "(I)J"}, {"nme": "getLong", "acc": 1, "dsc": "(IJ)J"}, {"nme": "getFloat", "acc": 1, "dsc": "(I)F"}, {"nme": "getFloat", "acc": 1, "dsc": "(IF)F"}, {"nme": "getDouble", "acc": 1, "dsc": "(I)D"}, {"nme": "getDouble", "acc": 1, "dsc": "(ID)D"}, {"nme": "getByteArray", "acc": 1, "dsc": "(I)[B"}, {"nme": "getByteArray", "acc": 1, "dsc": "(I[B)[B"}, {"nme": "getString", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "getString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "getList", "acc": 1, "dsc": "(I)Lnet/kyori/adventure/nbt/ListBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "getList", "acc": 1, "dsc": "(ILnet/kyori/adventure/nbt/BinaryTagType;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "sig": "(ILnet/kyori/adventure/nbt/BinaryTagType<*>;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "getList", "acc": 1, "dsc": "(ILnet/kyori/adventure/nbt/ListBinaryTag;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "getList", "acc": 1, "dsc": "(ILnet/kyori/adventure/nbt/BinaryTagType;Lnet/kyori/adventure/nbt/ListBinaryTag;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "sig": "(ILnet/kyori/adventure/nbt/BinaryTagType<*>;Lnet/kyori/adventure/nbt/ListBinaryTag;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "getCompound", "acc": 1, "dsc": "(I)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "getCompound", "acc": 1, "dsc": "(ILnet/kyori/adventure/nbt/CompoundBinaryTag;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "getIntArray", "acc": 1, "dsc": "(I)[I"}, {"nme": "getIntArray", "acc": 1, "dsc": "(I[I)[I"}, {"nme": "getLongArray", "acc": 1, "dsc": "(I)[J"}, {"nme": "getLongArray", "acc": 1, "dsc": "(I[J)[J"}, {"nme": "stream", "acc": 1025, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<Lnet/kyori/adventure/nbt/BinaryTag;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": []}, "net/kyori/adventure/nbt/BinaryTagIO$Compression$1.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/nbt/BinaryTagIO$Compression$1", "super": "net/kyori/adventure/nbt/BinaryTagIO$Compression", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "decompress", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;)<PERSON>java/io/InputStream;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "compress", "acc": 0, "dsc": "(Lja<PERSON>/io/OutputStream;)Ljava/io/OutputStream;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "net/kyori/adventure/nbt/ByteBinaryTag.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/nbt/ByteBinaryTag", "super": "java/lang/Object", "mthds": [{"nme": "of", "acc": 9, "dsc": "(B)Lnet/kyori/adventure/nbt/ByteBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "type", "acc": 1, "dsc": "()Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "()Lnet/kyori/adventure/nbt/BinaryTagType<Lnet/kyori/adventure/nbt/ByteBinaryTag;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "value", "acc": 1025, "dsc": "()B"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "ZERO", "dsc": "Lnet/kyori/adventure/nbt/ByteBinaryTag;"}, {"acc": 25, "nme": "ONE", "dsc": "Lnet/kyori/adventure/nbt/ByteBinaryTag;"}]}, "net/kyori/adventure/nbt/ListTagSetter.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/nbt/ListTagSetter", "super": "java/lang/Object", "mthds": [{"nme": "add", "acc": 1025, "dsc": "(Lnet/kyori/adventure/nbt/BinaryTag;)Ljava/lang/Object;", "sig": "(TT;)TR;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "add", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(Lja<PERSON>/lang/Iterable<+TT;>;)TR;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": []}, "net/kyori/adventure/nbt/FloatBinaryTagImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/nbt/FloatBinaryTagImpl", "super": "net/kyori/adventure/nbt/AbstractBinaryTag", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(F)V"}, {"nme": "value", "acc": 1, "dsc": "()F"}, {"nme": "byteValue", "acc": 1, "dsc": "()B"}, {"nme": "doubleValue", "acc": 1, "dsc": "()D"}, {"nme": "floatValue", "acc": 1, "dsc": "()F"}, {"nme": "intValue", "acc": 1, "dsc": "()I"}, {"nme": "longValue", "acc": 1, "dsc": "()J"}, {"nme": "shortValue", "acc": 1, "dsc": "()S"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "examinableProperties", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<+Lnet/kyori/examination/ExaminableProperty;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": [{"acc": 18, "nme": "value", "dsc": "F"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/Debug$Renderer;", "vals": ["text", "String.valueOf(this.value) + \"f\"", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "false"]}]}, "net/kyori/adventure/nbt/ShortBinaryTag.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/nbt/ShortBinaryTag", "super": "java/lang/Object", "mthds": [{"nme": "of", "acc": 9, "dsc": "(S)Lnet/kyori/adventure/nbt/ShortBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "type", "acc": 1, "dsc": "()Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "()Lnet/kyori/adventure/nbt/BinaryTagType<Lnet/kyori/adventure/nbt/ShortBinaryTag;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "value", "acc": 1025, "dsc": "()S"}], "flds": []}, "net/kyori/adventure/nbt/BinaryTagType$Impl$Numeric.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/nbt/BinaryTagType$Impl$Numeric", "super": "net/kyori/adventure/nbt/BinaryTagType$Impl", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/lang/Class;BLnet/kyori/adventure/nbt/BinaryTagType$Reader;Lnet/kyori/adventure/nbt/BinaryTagType$Writer;)V", "sig": "(Ljava/lang/Class<TT;>;BLnet/kyori/adventure/nbt/BinaryTagType$Reader<TT;>;Lnet/kyori/adventure/nbt/BinaryTagType$Writer<TT;>;)V"}, {"nme": "numeric", "acc": 0, "dsc": "()Z"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "test", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "flds": []}, "net/kyori/adventure/nbt/DoubleBinaryTag.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/nbt/DoubleBinaryTag", "super": "java/lang/Object", "mthds": [{"nme": "of", "acc": 9, "dsc": "(D)Lnet/kyori/adventure/nbt/DoubleBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "type", "acc": 1, "dsc": "()Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "()Lnet/kyori/adventure/nbt/BinaryTagType<Lnet/kyori/adventure/nbt/DoubleBinaryTag;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "value", "acc": 1025, "dsc": "()D"}], "flds": []}, "net/kyori/adventure/nbt/StringTagParseException.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/nbt/StringTagParseException", "super": "java/io/IOException", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/CharSequence;I)V"}, {"nme": "getMessage", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -3001637554903912905}, {"acc": 18, "nme": "buffer", "dsc": "<PERSON><PERSON><PERSON>/lang/CharSequence;"}, {"acc": 18, "nme": "position", "dsc": "I"}]}, "net/kyori/adventure/nbt/LongBinaryTag.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/nbt/LongBinaryTag", "super": "java/lang/Object", "mthds": [{"nme": "of", "acc": 9, "dsc": "(J)Lnet/kyori/adventure/nbt/LongBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "type", "acc": 1, "dsc": "()Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "()Lnet/kyori/adventure/nbt/BinaryTagType<Lnet/kyori/adventure/nbt/LongBinaryTag;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "value", "acc": 1025, "dsc": "()J"}], "flds": []}, "net/kyori/adventure/nbt/IOStreamUtil$1.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/nbt/IOStreamUtil$1", "super": "java/io/InputStream", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON>va/io/InputStream;)V"}, {"nme": "read", "acc": 1, "dsc": "()I", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 1, "dsc": "([B)I", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 1, "dsc": "([BII)I", "exs": ["java/io/IOException"]}], "flds": [{"acc": 4112, "nme": "val$stream", "dsc": "Ljava/io/InputStream;"}]}, "net/kyori/adventure/nbt/ListTagBuilder.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/nbt/ListTagBuilder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "<init>", "acc": 0, "dsc": "(Lnet/kyori/adventure/nbt/BinaryTagType;)V", "sig": "(Lnet/kyori/adventure/nbt/BinaryTagType<+Lnet/kyori/adventure/nbt/BinaryTag;>;)V"}, {"nme": "add", "acc": 1, "dsc": "(Lnet/kyori/adventure/nbt/BinaryTag;)Lnet/kyori/adventure/nbt/ListBinaryTag$Builder;", "sig": "(Lnet/kyori/adventure/nbt/BinaryTag;)Lnet/kyori/adventure/nbt/ListBinaryTag$Builder<TT;>;"}, {"nme": "add", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)Lnet/kyori/adventure/nbt/ListBinaryTag$Builder;", "sig": "(Ljava/lang/Iterable<+TT;>;)Lnet/kyori/adventure/nbt/ListBinaryTag$Builder<TT;>;"}, {"nme": "build", "acc": 1, "dsc": "()Lnet/kyori/adventure/nbt/ListBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "add", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "add", "acc": 4161, "dsc": "(Lnet/kyori/adventure/nbt/BinaryTag;)Ljava/lang/Object;"}], "flds": [{"acc": 2, "nme": "tags", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lnet/kyori/adventure/nbt/BinaryTag;>;"}, {"acc": 2, "nme": "elementType", "dsc": "Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "Lnet/kyori/adventure/nbt/BinaryTagType<+Lnet/kyori/adventure/nbt/BinaryTag;>;"}]}, "net/kyori/adventure/nbt/LongBinaryTagImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/nbt/LongBinaryTagImpl", "super": "net/kyori/adventure/nbt/AbstractBinaryTag", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(J)V"}, {"nme": "value", "acc": 1, "dsc": "()J"}, {"nme": "byteValue", "acc": 1, "dsc": "()B"}, {"nme": "doubleValue", "acc": 1, "dsc": "()D"}, {"nme": "floatValue", "acc": 1, "dsc": "()F"}, {"nme": "intValue", "acc": 1, "dsc": "()I"}, {"nme": "longValue", "acc": 1, "dsc": "()J"}, {"nme": "shortValue", "acc": 1, "dsc": "()S"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "examinableProperties", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<+Lnet/kyori/examination/ExaminableProperty;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": [{"acc": 18, "nme": "value", "dsc": "J"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/Debug$Renderer;", "vals": ["text", "String.valueOf(this.value) + \"l\"", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "false"]}]}, "net/kyori/adventure/nbt/BinaryTagType$Reader.class": {"ver": 52, "acc": 1536, "nme": "net/kyori/adventure/nbt/BinaryTagType$Reader", "super": "java/lang/Object", "mthds": [{"nme": "read", "acc": 1025, "dsc": "(Ljava/io/DataInput;)Lnet/kyori/adventure/nbt/BinaryTag;", "sig": "(Ljava/io/DataInput;)TT;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": []}, "net/kyori/adventure/nbt/TagStringReader.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/nbt/TagStringReader", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lnet/kyori/adventure/nbt/CharBuffer;)V"}, {"nme": "compound", "acc": 1, "dsc": "()Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "exs": ["net/kyori/adventure/nbt/StringTagParseException"]}, {"nme": "list", "acc": 1, "dsc": "()Lnet/kyori/adventure/nbt/ListBinaryTag;", "exs": ["net/kyori/adventure/nbt/StringTagParseException"]}, {"nme": "array", "acc": 1, "dsc": "(C)Lnet/kyori/adventure/nbt/BinaryTag;", "exs": ["net/kyori/adventure/nbt/StringTagParseException"]}, {"nme": "byteArray", "acc": 2, "dsc": "()[B", "exs": ["net/kyori/adventure/nbt/StringTagParseException"]}, {"nme": "intArray", "acc": 2, "dsc": "()[I", "exs": ["net/kyori/adventure/nbt/StringTagParseException"]}, {"nme": "longArray", "acc": 2, "dsc": "()[J", "exs": ["net/kyori/adventure/nbt/StringTagParseException"]}, {"nme": "key", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["net/kyori/adventure/nbt/StringTagParseException"]}, {"nme": "tag", "acc": 1, "dsc": "()Lnet/kyori/adventure/nbt/BinaryTag;", "exs": ["net/kyori/adventure/nbt/StringTagParseException"]}, {"nme": "scalar", "acc": 2, "dsc": "()Lnet/kyori/adventure/nbt/BinaryTag;"}, {"nme": "separatorOrCompleteWith", "acc": 2, "dsc": "(C)Z", "exs": ["net/kyori/adventure/nbt/StringTagParseException"]}, {"nme": "unescape", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "legacy", "acc": 1, "dsc": "(Z)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "MAX_DEPTH", "dsc": "I", "val": 512}, {"acc": 26, "nme": "EMPTY_BYTE_ARRAY", "dsc": "[B"}, {"acc": 26, "nme": "EMPTY_INT_ARRAY", "dsc": "[I"}, {"acc": 26, "nme": "EMPTY_LONG_ARRAY", "dsc": "[J"}, {"acc": 18, "nme": "buffer", "dsc": "Lnet/kyori/adventure/nbt/Char<PERSON><PERSON>er;"}, {"acc": 2, "nme": "acceptLegacy", "dsc": "Z"}, {"acc": 2, "nme": "depth", "dsc": "I"}]}, "net/kyori/adventure/nbt/AbstractBinaryTag.class": {"ver": 52, "acc": 1056, "nme": "net/kyori/adventure/nbt/AbstractBinaryTag", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "examinableName", "acc": 17, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "toString", "acc": 17, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "net/kyori/adventure/nbt/StringBinaryTagImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/nbt/StringBinaryTagImpl", "super": "net/kyori/adventure/nbt/AbstractBinaryTag", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "value", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "examinableProperties", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<+Lnet/kyori/examination/ExaminableProperty;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": [{"acc": 18, "nme": "value", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/Debug$Renderer;", "vals": ["text", "\"\\\"\" + this.value + \"\\\"\"", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "false"]}]}, "net/kyori/adventure/nbt/ByteBinaryTagImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/nbt/ByteBinaryTagImpl", "super": "net/kyori/adventure/nbt/AbstractBinaryTag", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(B)V"}, {"nme": "value", "acc": 1, "dsc": "()B"}, {"nme": "byteValue", "acc": 1, "dsc": "()B"}, {"nme": "doubleValue", "acc": 1, "dsc": "()D"}, {"nme": "floatValue", "acc": 1, "dsc": "()F"}, {"nme": "intValue", "acc": 1, "dsc": "()I"}, {"nme": "longValue", "acc": 1, "dsc": "()J"}, {"nme": "shortValue", "acc": 1, "dsc": "()S"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "examinableProperties", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<+Lnet/kyori/examination/ExaminableProperty;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": [{"acc": 18, "nme": "value", "dsc": "B"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/Debug$Renderer;", "vals": ["text", "\"0x\" + Integer.toString(this.value, 16)", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "false"]}]}, "net/kyori/adventure/nbt/EndBinaryTagImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/nbt/EndBinaryTagImpl", "super": "net/kyori/adventure/nbt/AbstractBinaryTag", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "INSTANCE", "dsc": "Lnet/kyori/adventure/nbt/EndBinaryTagImpl;"}]}, "net/kyori/adventure/nbt/ByteArrayBinaryTagImpl$1.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/nbt/ByteArrayBinaryTagImpl$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lnet/kyori/adventure/nbt/ByteArrayBinaryTagImpl;)V"}, {"nme": "hasNext", "acc": 1, "dsc": "()Z"}, {"nme": "next", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Byte;"}, {"nme": "next", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 2, "nme": "index", "dsc": "I"}, {"acc": 4112, "nme": "this$0", "dsc": "Lnet/kyori/adventure/nbt/ByteArrayBinaryTagImpl;"}]}, "net/kyori/adventure/nbt/Tokens.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/nbt/Tokens", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "id", "acc": 8, "dsc": "(C)Z"}, {"nme": "numeric", "acc": 8, "dsc": "(C)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "COMPOUND_BEGIN", "dsc": "C", "val": 123}, {"acc": 24, "nme": "COMPOUND_END", "dsc": "C", "val": 125}, {"acc": 24, "nme": "COMPOUND_KEY_TERMINATOR", "dsc": "C", "val": 58}, {"acc": 24, "nme": "ARRAY_BEGIN", "dsc": "C", "val": 91}, {"acc": 24, "nme": "ARRAY_END", "dsc": "C", "val": 93}, {"acc": 24, "nme": "ARRAY_SIGNATURE_SEPARATOR", "dsc": "C", "val": 59}, {"acc": 24, "nme": "VALUE_SEPARATOR", "dsc": "C", "val": 44}, {"acc": 24, "nme": "SINGLE_QUOTE", "dsc": "C", "val": 39}, {"acc": 24, "nme": "DOUBLE_QUOTE", "dsc": "C", "val": 34}, {"acc": 24, "nme": "ESCAPE_MARKER", "dsc": "C", "val": 92}, {"acc": 24, "nme": "TYPE_BYTE", "dsc": "C", "val": 98}, {"acc": 24, "nme": "TYPE_SHORT", "dsc": "C", "val": 115}, {"acc": 24, "nme": "TYPE_INT", "dsc": "C", "val": 105}, {"acc": 24, "nme": "TYPE_LONG", "dsc": "C", "val": 108}, {"acc": 24, "nme": "TYPE_FLOAT", "dsc": "C", "val": 102}, {"acc": 24, "nme": "TYPE_DOUBLE", "dsc": "C", "val": 100}, {"acc": 24, "nme": "LITERAL_TRUE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "true"}, {"acc": 24, "nme": "LITERAL_FALSE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "false"}, {"acc": 24, "nme": "NEWLINE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 24, "nme": "EOF", "dsc": "C", "val": 0}]}, "net/kyori/adventure/nbt/BinaryTagIO$Reader.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/nbt/BinaryTagIO$Reader", "super": "java/lang/Object", "mthds": [{"nme": "read", "acc": 1, "dsc": "(Ljava/nio/file/Path;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "read", "acc": 1025, "dsc": "(Ljava/nio/file/Path;Lnet/kyori/adventure/nbt/BinaryTagIO$Compression;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "read", "acc": 1, "dsc": "(Ljava/io/InputStream;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "read", "acc": 1025, "dsc": "(Ljava/io/InputStream;Lnet/kyori/adventure/nbt/BinaryTagIO$Compression;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "read", "acc": 1025, "dsc": "(Ljava/io/DataInput;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "readNamed", "acc": 1, "dsc": "(Ljava/nio/file/Path;)Ljava/util/Map$Entry;", "sig": "(Ljava/nio/file/Path;)Ljava/util/Map$Entry<Ljava/lang/String;Lnet/kyori/adventure/nbt/CompoundBinaryTag;>;", "exs": ["java/io/IOException"]}, {"nme": "readNamed", "acc": 1025, "dsc": "(Ljava/nio/file/Path;Lnet/kyori/adventure/nbt/BinaryTagIO$Compression;)Ljava/util/Map$Entry;", "sig": "(Ljava/nio/file/Path;Lnet/kyori/adventure/nbt/BinaryTagIO$Compression;)Ljava/util/Map$Entry<Ljava/lang/String;Lnet/kyori/adventure/nbt/CompoundBinaryTag;>;", "exs": ["java/io/IOException"]}, {"nme": "readNamed", "acc": 1, "dsc": "(Ljava/io/InputStream;)Ljava/util/Map$Entry;", "sig": "(Ljava/io/InputStream;)Ljava/util/Map$Entry<Ljava/lang/String;Lnet/kyori/adventure/nbt/CompoundBinaryTag;>;", "exs": ["java/io/IOException"]}, {"nme": "readNamed", "acc": 1025, "dsc": "(Ljava/io/InputStream;Lnet/kyori/adventure/nbt/BinaryTagIO$Compression;)Ljava/util/Map$Entry;", "sig": "(Ljava/io/InputStream;Lnet/kyori/adventure/nbt/BinaryTagIO$Compression;)Ljava/util/Map$Entry<Ljava/lang/String;Lnet/kyori/adventure/nbt/CompoundBinaryTag;>;", "exs": ["java/io/IOException"]}, {"nme": "readNamed", "acc": 1025, "dsc": "(Ljava/io/DataInput;)Ljava/util/Map$Entry;", "sig": "(Ljava/io/DataInput;)Ljava/util/Map$Entry<Ljava/lang/String;Lnet/kyori/adventure/nbt/CompoundBinaryTag;>;", "exs": ["java/io/IOException"]}], "flds": []}, "net/kyori/adventure/nbt/BinaryTagScope$NoOp.class": {"ver": 52, "acc": 49, "nme": "net/kyori/adventure/nbt/BinaryTagScope$NoOp", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "close", "acc": 1, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "INSTANCE", "dsc": "Lnet/kyori/adventure/nbt/BinaryTagScope$NoOp;"}]}, "net/kyori/adventure/nbt/CompoundTagBuilder.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/nbt/CompoundTagBuilder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "tags", "acc": 2, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Lnet/kyori/adventure/nbt/BinaryTag;>;"}, {"nme": "put", "acc": 1, "dsc": "(L<PERSON><PERSON>/lang/String;Lnet/kyori/adventure/nbt/BinaryTag;)Lnet/kyori/adventure/nbt/CompoundBinaryTag$Builder;"}, {"nme": "put", "acc": 1, "dsc": "(Lnet/kyori/adventure/nbt/CompoundBinaryTag;)Lnet/kyori/adventure/nbt/CompoundBinaryTag$Builder;"}, {"nme": "put", "acc": 1, "dsc": "(Ljava/util/Map;)Lnet/kyori/adventure/nbt/CompoundBinaryTag$Builder;", "sig": "(Ljava/util/Map<Ljava/lang/String;+Lnet/kyori/adventure/nbt/BinaryTag;>;)Lnet/kyori/adventure/nbt/CompoundBinaryTag$Builder;"}, {"nme": "remove", "acc": 1, "dsc": "(Ljava/lang/String;Ljava/util/function/Consumer;)Lnet/kyori/adventure/nbt/CompoundBinaryTag$Builder;", "sig": "(Ljava/lang/String;Ljava/util/function/Consumer<-Lnet/kyori/adventure/nbt/BinaryTag;>;)Lnet/kyori/adventure/nbt/CompoundBinaryTag$Builder;"}, {"nme": "build", "acc": 1, "dsc": "()Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "remove", "acc": 4161, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/function/Consumer;)Ljava/lang/Object;"}, {"nme": "put", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/Object;"}, {"nme": "put", "acc": 4161, "dsc": "(Lnet/kyori/adventure/nbt/CompoundBinaryTag;)Ljava/lang/Object;"}, {"nme": "put", "acc": 4161, "dsc": "(Lja<PERSON>/lang/String;Lnet/kyori/adventure/nbt/BinaryTag;)Ljava/lang/Object;"}], "flds": [{"acc": 2, "nme": "tags", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lnet/kyori/adventure/nbt/BinaryTag;>;"}]}, "net/kyori/adventure/nbt/CompoundBinaryTag.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/nbt/CompoundBinaryTag", "super": "java/lang/Object", "mthds": [{"nme": "empty", "acc": 9, "dsc": "()Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "from", "acc": 9, "dsc": "(Ljava/util/Map;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "sig": "(Ljava/util/Map<Ljava/lang/String;+Lnet/kyori/adventure/nbt/BinaryTag;>;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "builder", "acc": 9, "dsc": "()Lnet/kyori/adventure/nbt/CompoundBinaryTag$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "type", "acc": 1, "dsc": "()Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "()Lnet/kyori/adventure/nbt/BinaryTagType<Lnet/kyori/adventure/nbt/CompoundBinaryTag;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "keySet", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "get", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/nbt/BinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "getBoolean", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "getBoolean", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)Z"}, {"nme": "getByte", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)B"}, {"nme": "getByte", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;B)B"}, {"nme": "getShort", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)S"}, {"nme": "getShort", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;S)S"}, {"nme": "getInt", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "getInt", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)I"}, {"nme": "getLong", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)J"}, {"nme": "getLong", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;J)J"}, {"nme": "getFloat", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)F"}, {"nme": "getFloat", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;F)F"}, {"nme": "getDouble", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)D"}, {"nme": "getDouble", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;D)D"}, {"nme": "getByteArray", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[B"}, {"nme": "getByteArray", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[B)[B"}, {"nme": "getString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "getString", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "getList", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "getList", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lnet/kyori/adventure/nbt/ListBinaryTag;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "getList", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Lnet/kyori/adventure/nbt/BinaryTagType;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "sig": "(Ljava/lang/String;Lnet/kyori/adventure/nbt/BinaryTagType<+Lnet/kyori/adventure/nbt/BinaryTag;>;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "getList", "acc": 1025, "dsc": "(Lja<PERSON>/lang/String;Lnet/kyori/adventure/nbt/BinaryTagType;Lnet/kyori/adventure/nbt/ListBinaryTag;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "sig": "(Ljava/lang/String;Lnet/kyori/adventure/nbt/BinaryTagType<+Lnet/kyori/adventure/nbt/BinaryTag;>;Lnet/kyori/adventure/nbt/ListBinaryTag;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "getCompound", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "getCompound", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lnet/kyori/adventure/nbt/CompoundBinaryTag;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "getIntArray", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[I"}, {"nme": "getIntArray", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[I)[I"}, {"nme": "getLongArray", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[J"}, {"nme": "getLongArray", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[J)[J"}], "flds": []}, "net/kyori/adventure/nbt/NumberBinaryTag.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/nbt/NumberBinaryTag", "super": "java/lang/Object", "mthds": [{"nme": "type", "acc": 1025, "dsc": "()Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "()Lnet/kyori/adventure/nbt/BinaryTagType<+Lnet/kyori/adventure/nbt/NumberBinaryTag;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "byteValue", "acc": 1025, "dsc": "()B"}, {"nme": "doubleValue", "acc": 1025, "dsc": "()D"}, {"nme": "floatValue", "acc": 1025, "dsc": "()F"}, {"nme": "intValue", "acc": 1025, "dsc": "()I"}, {"nme": "longValue", "acc": 1025, "dsc": "()J"}, {"nme": "shortValue", "acc": 1025, "dsc": "()S"}], "flds": []}, "net/kyori/adventure/nbt/TagStringIO.class": {"ver": 52, "acc": 49, "nme": "net/kyori/adventure/nbt/TagStringIO", "super": "java/lang/Object", "mthds": [{"nme": "get", "acc": 9, "dsc": "()Lnet/kyori/adventure/nbt/TagStringIO;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "builder", "acc": 9, "dsc": "()Lnet/kyori/adventure/nbt/TagStringIO$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "<init>", "acc": 2, "dsc": "(Lnet/kyori/adventure/nbt/TagStringIO$Builder;)V"}, {"nme": "asCompound", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "exs": ["java/io/IOException"]}, {"nme": "asString", "acc": 1, "dsc": "(Lnet/kyori/adventure/nbt/CompoundBinaryTag;)Ljava/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "toWriter", "acc": 1, "dsc": "(Lnet/kyori/adventure/nbt/CompoundBinaryTag;<PERSON><PERSON><PERSON>/io/Writer;)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 4096, "dsc": "(Lnet/kyori/adventure/nbt/TagStringIO$Builder;Lnet/kyori/adventure/nbt/TagStringIO$1;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "INSTANCE", "dsc": "Lnet/kyori/adventure/nbt/TagStringIO;"}, {"acc": 18, "nme": "acceptLegacy", "dsc": "Z"}, {"acc": 18, "nme": "emitLegacy", "dsc": "Z"}, {"acc": 18, "nme": "indent", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "net/kyori/adventure/nbt/BinaryTagIO$Writer.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/nbt/BinaryTagIO$Writer", "super": "java/lang/Object", "mthds": [{"nme": "write", "acc": 1, "dsc": "(Lnet/kyori/adventure/nbt/CompoundBinaryTag;Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1025, "dsc": "(Lnet/kyori/adventure/nbt/CompoundBinaryTag;Ljava/nio/file/Path;Lnet/kyori/adventure/nbt/BinaryTagIO$Compression;)V", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lnet/kyori/adventure/nbt/CompoundBinaryTag;Ljava/io/OutputStream;)V", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1025, "dsc": "(Lnet/kyori/adventure/nbt/CompoundBinaryTag;Ljava/io/OutputStream;Lnet/kyori/adventure/nbt/BinaryTagIO$Compression;)V", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1025, "dsc": "(Lnet/kyori/adventure/nbt/CompoundBinaryTag;Ljava/io/DataOutput;)V", "exs": ["java/io/IOException"]}, {"nme": "writeNamed", "acc": 1, "dsc": "(Ljava/util/Map$Entry;Ljava/nio/file/Path;)V", "sig": "(Ljava/util/Map$Entry<Ljava/lang/String;Lnet/kyori/adventure/nbt/CompoundBinaryTag;>;Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "writeNamed", "acc": 1025, "dsc": "(Ljava/util/Map$Entry;Ljava/nio/file/Path;Lnet/kyori/adventure/nbt/BinaryTagIO$Compression;)V", "sig": "(Ljava/util/Map$Entry<Ljava/lang/String;Lnet/kyori/adventure/nbt/CompoundBinaryTag;>;Ljava/nio/file/Path;Lnet/kyori/adventure/nbt/BinaryTagIO$Compression;)V", "exs": ["java/io/IOException"]}, {"nme": "writeNamed", "acc": 1, "dsc": "(Ljava/util/Map$Entry;Ljava/io/OutputStream;)V", "sig": "(Ljava/util/Map$Entry<Ljava/lang/String;Lnet/kyori/adventure/nbt/CompoundBinaryTag;>;Ljava/io/OutputStream;)V", "exs": ["java/io/IOException"]}, {"nme": "writeNamed", "acc": 1025, "dsc": "(Ljava/util/Map$Entry;Ljava/io/OutputStream;Lnet/kyori/adventure/nbt/BinaryTagIO$Compression;)V", "sig": "(Ljava/util/Map$Entry<Ljava/lang/String;Lnet/kyori/adventure/nbt/CompoundBinaryTag;>;Ljava/io/OutputStream;Lnet/kyori/adventure/nbt/BinaryTagIO$Compression;)V", "exs": ["java/io/IOException"]}, {"nme": "writeNamed", "acc": 1025, "dsc": "(Ljava/util/Map$Entry;Ljava/io/DataOutput;)V", "sig": "(Ljava/util/Map$Entry<Ljava/lang/String;Lnet/kyori/adventure/nbt/CompoundBinaryTag;>;Ljava/io/DataOutput;)V", "exs": ["java/io/IOException"]}], "flds": []}, "net/kyori/adventure/nbt/BinaryTagIO$Compression$2.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/nbt/BinaryTagIO$Compression$2", "super": "net/kyori/adventure/nbt/BinaryTagIO$Compression", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "decompress", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;)<PERSON>java/io/InputStream;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "compress", "acc": 0, "dsc": "(Lja<PERSON>/io/OutputStream;)Ljava/io/OutputStream;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "net/kyori/adventure/nbt/BinaryTagIO.class": {"ver": 52, "acc": 49, "nme": "net/kyori/adventure/nbt/BinaryTagIO", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "unlimitedReader", "acc": 9, "dsc": "()Lnet/kyori/adventure/nbt/BinaryTagIO$Reader;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "reader", "acc": 9, "dsc": "()Lnet/kyori/adventure/nbt/BinaryTagIO$Reader;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "reader", "acc": 9, "dsc": "(J)Lnet/kyori/adventure/nbt/BinaryTagIO$Reader;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "writer", "acc": 9, "dsc": "()Lnet/kyori/adventure/nbt/BinaryTagIO$Writer;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "readPath", "acc": 131081, "dsc": "(Ljava/nio/file/Path;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "exs": ["java/io/IOException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "readInputStream", "acc": 131081, "dsc": "(Ljava/io/InputStream;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "exs": ["java/io/IOException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "readCompressedPath", "acc": 131081, "dsc": "(Ljava/nio/file/Path;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "exs": ["java/io/IOException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "readCompressedInputStream", "acc": 131081, "dsc": "(Ljava/io/InputStream;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "exs": ["java/io/IOException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "readDataInput", "acc": 131081, "dsc": "(Ljava/io/DataInput;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "exs": ["java/io/IOException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "writePath", "acc": 131081, "dsc": "(Lnet/kyori/adventure/nbt/CompoundBinaryTag;Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "writeOutputStream", "acc": 131081, "dsc": "(Lnet/kyori/adventure/nbt/CompoundBinaryTag;Ljava/io/OutputStream;)V", "exs": ["java/io/IOException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "writeCompressedPath", "acc": 131081, "dsc": "(Lnet/kyori/adventure/nbt/CompoundBinaryTag;Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "writeCompressedOutputStream", "acc": 131081, "dsc": "(Lnet/kyori/adventure/nbt/CompoundBinaryTag;Ljava/io/OutputStream;)V", "exs": ["java/io/IOException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "writeDataOutput", "acc": 131081, "dsc": "(Lnet/kyori/adventure/nbt/CompoundBinaryTag;Ljava/io/DataOutput;)V", "exs": ["java/io/IOException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": []}, "net/kyori/adventure/nbt/ShadyPines.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/nbt/ShadyPines", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "floor", "acc": 8, "dsc": "(D)I"}, {"nme": "floor", "acc": 8, "dsc": "(F)I"}], "flds": []}, "net/kyori/adventure/nbt/BinaryTagType.class": {"ver": 52, "acc": 1057, "nme": "net/kyori/adventure/nbt/BinaryTagType", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "id", "acc": 1025, "dsc": "()B"}, {"nme": "numeric", "acc": 1024, "dsc": "()Z"}, {"nme": "read", "acc": 1025, "dsc": "(Ljava/io/DataInput;)Lnet/kyori/adventure/nbt/BinaryTag;", "sig": "(Ljava/io/DataInput;)TT;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "write", "acc": 1025, "dsc": "(Lnet/kyori/adventure/nbt/BinaryTag;Ljava/io/DataOutput;)V", "sig": "(TT;Ljava/io/DataOutput;)V", "exs": ["java/io/IOException"]}, {"nme": "writeUntyped", "acc": 8, "dsc": "(Lnet/kyori/adventure/nbt/BinaryTagType;Lnet/kyori/adventure/nbt/BinaryTag;Ljava/io/DataOutput;)V", "sig": "<T::Lnet/kyori/adventure/nbt/BinaryTag;>(Lnet/kyori/adventure/nbt/BinaryTagType<+Lnet/kyori/adventure/nbt/BinaryTag;>;TT;Ljava/io/DataOutput;)V", "exs": ["java/io/IOException"]}, {"nme": "of", "acc": 8, "dsc": "(B)Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "(B)Lnet/kyori/adventure/nbt/BinaryTagType<+Lnet/kyori/adventure/nbt/BinaryTag;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "register", "acc": 8, "dsc": "(Ljava/lang/Class;BLnet/kyori/adventure/nbt/BinaryTagType$Reader;Lnet/kyori/adventure/nbt/BinaryTagType$Writer;)Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "<T::Lnet/kyori/adventure/nbt/BinaryTag;>(Ljava/lang/Class<TT;>;BLnet/kyori/adventure/nbt/BinaryTagType$Reader<TT;>;Lnet/kyori/adventure/nbt/BinaryTagType$Writer<TT;>;)Lnet/kyori/adventure/nbt/BinaryTagType<TT;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "registerNumeric", "acc": 8, "dsc": "(Ljava/lang/Class;BLnet/kyori/adventure/nbt/BinaryTagType$Reader;Lnet/kyori/adventure/nbt/BinaryTagType$Writer;)Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "<T::Lnet/kyori/adventure/nbt/NumberBinaryTag;>(Ljava/lang/Class<TT;>;BLnet/kyori/adventure/nbt/BinaryTagType$Reader<TT;>;Lnet/kyori/adventure/nbt/BinaryTagType$Writer<TT;>;)Lnet/kyori/adventure/nbt/BinaryTagType<TT;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "register", "acc": 10, "dsc": "(Lnet/kyori/adventure/nbt/BinaryTagType;)Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "<T::Lnet/kyori/adventure/nbt/BinaryTag;Y:Lnet/kyori/adventure/nbt/BinaryTagType<TT;>;>(TY;)TY;"}, {"nme": "test", "acc": 1, "dsc": "(Lnet/kyori/adventure/nbt/BinaryTagType;)Z", "sig": "(Lnet/kyori/adventure/nbt/BinaryTagType<+Lnet/kyori/adventure/nbt/BinaryTag;>;)Z"}, {"nme": "test", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "TYPES", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lnet/kyori/adventure/nbt/BinaryTagType<+Lnet/kyori/adventure/nbt/BinaryTag;>;>;"}]}, "net/kyori/adventure/nbt/LongArrayBinaryTag.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/nbt/LongArrayBinaryTag", "super": "java/lang/Object", "mthds": [{"nme": "of", "acc": 137, "dsc": "([J)Lnet/kyori/adventure/nbt/LongArrayBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "type", "acc": 1, "dsc": "()Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "()Lnet/kyori/adventure/nbt/BinaryTagType<Lnet/kyori/adventure/nbt/LongArrayBinaryTag;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "value", "acc": 1025, "dsc": "()[J"}, {"nme": "size", "acc": 1025, "dsc": "()I"}, {"nme": "get", "acc": 1025, "dsc": "(I)J"}, {"nme": "iterator", "acc": 1025, "dsc": "()Ljava/util/PrimitiveIterator$OfLong;"}, {"nme": "spliterator", "acc": 1025, "dsc": "()Ljava/util/Spliterator$OfLong;"}, {"nme": "stream", "acc": 1025, "dsc": "()Ljava/util/stream/LongStream;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "forEachLong", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/function/LongConsumer;)V"}, {"nme": "spliterator", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/util/Spliterator;"}, {"nme": "iterator", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/util/Iterator;"}], "flds": []}, "net/kyori/adventure/nbt/IntBinaryTagImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/nbt/IntBinaryTagImpl", "super": "net/kyori/adventure/nbt/AbstractBinaryTag", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(I)V"}, {"nme": "value", "acc": 1, "dsc": "()I"}, {"nme": "byteValue", "acc": 1, "dsc": "()B"}, {"nme": "doubleValue", "acc": 1, "dsc": "()D"}, {"nme": "floatValue", "acc": 1, "dsc": "()F"}, {"nme": "intValue", "acc": 1, "dsc": "()I"}, {"nme": "longValue", "acc": 1, "dsc": "()J"}, {"nme": "shortValue", "acc": 1, "dsc": "()S"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "examinableProperties", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<+Lnet/kyori/examination/ExaminableProperty;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": [{"acc": 18, "nme": "value", "dsc": "I"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/Debug$Renderer;", "vals": ["text", "String.valueOf(this.value) + \"i\"", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "false"]}]}, "net/kyori/adventure/nbt/ArrayBinaryTagImpl.class": {"ver": 52, "acc": 1056, "nme": "net/kyori/adventure/nbt/ArrayBinaryTagImpl", "super": "net/kyori/adventure/nbt/AbstractBinaryTag", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "checkIndex", "acc": 8, "dsc": "(II)V"}], "flds": []}, "net/kyori/adventure/nbt/TagStringIO$Builder.class": {"ver": 52, "acc": 33, "nme": "net/kyori/adventure/nbt/TagStringIO$Builder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "indent", "acc": 1, "dsc": "(I)Lnet/kyori/adventure/nbt/TagStringIO$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "indentTab", "acc": 1, "dsc": "(I)Lnet/kyori/adventure/nbt/TagStringIO$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "acceptLegacy", "acc": 1, "dsc": "(Z)Lnet/kyori/adventure/nbt/TagStringIO$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "emitLegacy", "acc": 1, "dsc": "(Z)Lnet/kyori/adventure/nbt/TagStringIO$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "build", "acc": 1, "dsc": "()Lnet/kyori/adventure/nbt/TagStringIO;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "access$000", "acc": 4104, "dsc": "(Lnet/kyori/adventure/nbt/TagStringIO$Builder;)Z"}, {"nme": "access$100", "acc": 4104, "dsc": "(Lnet/kyori/adventure/nbt/TagStringIO$Builder;)Z"}, {"nme": "access$200", "acc": 4104, "dsc": "(Lnet/kyori/adventure/nbt/TagStringIO$Builder;)Ljava/lang/String;"}], "flds": [{"acc": 2, "nme": "acceptLegacy", "dsc": "Z"}, {"acc": 2, "nme": "emitLegacy", "dsc": "Z"}, {"acc": 2, "nme": "indent", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "net/kyori/adventure/nbt/TrackingDataInput.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/nbt/TrackingDataInput", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/io/DataInput;J)V"}, {"nme": "enter", "acc": 9, "dsc": "(Ljava/io/DataInput;)Lnet/kyori/adventure/nbt/BinaryTagScope;", "exs": ["java/io/IOException"]}, {"nme": "enter", "acc": 9, "dsc": "(Ljava/io/DataInput;J)Lnet/kyori/adventure/nbt/BinaryTagScope;", "exs": ["java/io/IOException"]}, {"nme": "input", "acc": 1, "dsc": "()Ljava/io/DataInput;"}, {"nme": "enter", "acc": 1, "dsc": "(J)Lnet/kyori/adventure/nbt/TrackingDataInput;", "exs": ["java/io/IOException"]}, {"nme": "enter", "acc": 1, "dsc": "()Lnet/kyori/adventure/nbt/TrackingDataInput;", "exs": ["java/io/IOException"]}, {"nme": "exit", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "ensureMaxLength", "acc": 2, "dsc": "(J)V", "exs": ["java/io/IOException"]}, {"nme": "readFully", "acc": 1, "dsc": "([B)V", "exs": ["java/io/IOException"]}, {"nme": "readFully", "acc": 1, "dsc": "([BII)V", "exs": ["java/io/IOException"]}, {"nme": "skipBytes", "acc": 1, "dsc": "(I)I", "exs": ["java/io/IOException"]}, {"nme": "readBoolean", "acc": 1, "dsc": "()Z", "exs": ["java/io/IOException"]}, {"nme": "readByte", "acc": 1, "dsc": "()B", "exs": ["java/io/IOException"]}, {"nme": "readUnsignedByte", "acc": 1, "dsc": "()I", "exs": ["java/io/IOException"]}, {"nme": "readShort", "acc": 1, "dsc": "()S", "exs": ["java/io/IOException"]}, {"nme": "readUnsignedShort", "acc": 1, "dsc": "()I", "exs": ["java/io/IOException"]}, {"nme": "readChar", "acc": 1, "dsc": "()C", "exs": ["java/io/IOException"]}, {"nme": "readInt", "acc": 1, "dsc": "()I", "exs": ["java/io/IOException"]}, {"nme": "readLong", "acc": 1, "dsc": "()J", "exs": ["java/io/IOException"]}, {"nme": "readFloat", "acc": 1, "dsc": "()F", "exs": ["java/io/IOException"]}, {"nme": "readDouble", "acc": 1, "dsc": "()D", "exs": ["java/io/IOException"]}, {"nme": "readLine", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "readUTF", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 26, "nme": "MAX_DEPTH", "dsc": "I", "val": 512}, {"acc": 18, "nme": "input", "dsc": "Ljava/io/DataInput;"}, {"acc": 18, "nme": "max<PERSON><PERSON><PERSON>", "dsc": "J"}, {"acc": 2, "nme": "counter", "dsc": "J"}, {"acc": 2, "nme": "depth", "dsc": "I"}]}, "net/kyori/adventure/nbt/BinaryTagIO$Compression.class": {"ver": 52, "acc": 1057, "nme": "net/kyori/adventure/nbt/BinaryTagIO$Compression", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "decompress", "acc": 1024, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;)<PERSON>java/io/InputStream;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "compress", "acc": 1024, "dsc": "(Lja<PERSON>/io/OutputStream;)Ljava/io/OutputStream;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "NONE", "dsc": "Lnet/kyori/adventure/nbt/BinaryTagIO$Compression;"}, {"acc": 25, "nme": "GZIP", "dsc": "Lnet/kyori/adventure/nbt/BinaryTagIO$Compression;"}, {"acc": 25, "nme": "ZLIB", "dsc": "Lnet/kyori/adventure/nbt/BinaryTagIO$Compression;"}]}, "net/kyori/adventure/nbt/BinaryTagTypes.class": {"ver": 52, "acc": 49, "nme": "net/kyori/adventure/nbt/BinaryTagTypes", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "lambda$static$24", "acc": 4106, "dsc": "(Lnet/kyori/adventure/nbt/LongArrayBinaryTag;Ljava/io/DataOutput;)V", "exs": ["java/io/IOException"]}, {"nme": "lambda$static$23", "acc": 4106, "dsc": "(Ljava/io/DataInput;)Lnet/kyori/adventure/nbt/LongArrayBinaryTag;", "exs": ["java/io/IOException"]}, {"nme": "lambda$static$22", "acc": 4106, "dsc": "(Lnet/kyori/adventure/nbt/IntArrayBinaryTag;Ljava/io/DataOutput;)V", "exs": ["java/io/IOException"]}, {"nme": "lambda$static$21", "acc": 4106, "dsc": "(Ljava/io/DataInput;)Lnet/kyori/adventure/nbt/IntArrayBinaryTag;", "exs": ["java/io/IOException"]}, {"nme": "lambda$static$20", "acc": 4106, "dsc": "(Lnet/kyori/adventure/nbt/CompoundBinaryTag;Ljava/io/DataOutput;)V", "exs": ["java/io/IOException"]}, {"nme": "lambda$static$19", "acc": 4106, "dsc": "(Ljava/io/DataInput;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "exs": ["java/io/IOException"]}, {"nme": "lambda$static$18", "acc": 4106, "dsc": "(Lnet/kyori/adventure/nbt/ListBinaryTag;Ljava/io/DataOutput;)V", "exs": ["java/io/IOException"]}, {"nme": "lambda$static$17", "acc": 4106, "dsc": "(Ljava/io/DataInput;)Lnet/kyori/adventure/nbt/ListBinaryTag;", "exs": ["java/io/IOException"]}, {"nme": "lambda$static$16", "acc": 4106, "dsc": "(Lnet/kyori/adventure/nbt/StringBinaryTag;Ljava/io/DataOutput;)V", "exs": ["java/io/IOException"]}, {"nme": "lambda$static$15", "acc": 4106, "dsc": "(Ljava/io/DataInput;)Lnet/kyori/adventure/nbt/StringBinaryTag;", "exs": ["java/io/IOException"]}, {"nme": "lambda$static$14", "acc": 4106, "dsc": "(Lnet/kyori/adventure/nbt/ByteArrayBinaryTag;Ljava/io/DataOutput;)V", "exs": ["java/io/IOException"]}, {"nme": "lambda$static$13", "acc": 4106, "dsc": "(Ljava/io/DataInput;)Lnet/kyori/adventure/nbt/ByteArrayBinaryTag;", "exs": ["java/io/IOException"]}, {"nme": "lambda$static$12", "acc": 4106, "dsc": "(Lnet/kyori/adventure/nbt/DoubleBinaryTag;Ljava/io/DataOutput;)V", "exs": ["java/io/IOException"]}, {"nme": "lambda$static$11", "acc": 4106, "dsc": "(Ljava/io/DataInput;)Lnet/kyori/adventure/nbt/DoubleBinaryTag;", "exs": ["java/io/IOException"]}, {"nme": "lambda$static$10", "acc": 4106, "dsc": "(Lnet/kyori/adventure/nbt/FloatBinaryTag;Ljava/io/DataOutput;)V", "exs": ["java/io/IOException"]}, {"nme": "lambda$static$9", "acc": 4106, "dsc": "(Ljava/io/DataInput;)Lnet/kyori/adventure/nbt/FloatBinaryTag;", "exs": ["java/io/IOException"]}, {"nme": "lambda$static$8", "acc": 4106, "dsc": "(Lnet/kyori/adventure/nbt/LongBinaryTag;Ljava/io/DataOutput;)V", "exs": ["java/io/IOException"]}, {"nme": "lambda$static$7", "acc": 4106, "dsc": "(Ljava/io/DataInput;)Lnet/kyori/adventure/nbt/LongBinaryTag;", "exs": ["java/io/IOException"]}, {"nme": "lambda$static$6", "acc": 4106, "dsc": "(Lnet/kyori/adventure/nbt/IntBinaryTag;Ljava/io/DataOutput;)V", "exs": ["java/io/IOException"]}, {"nme": "lambda$static$5", "acc": 4106, "dsc": "(Ljava/io/DataInput;)Lnet/kyori/adventure/nbt/IntBinaryTag;", "exs": ["java/io/IOException"]}, {"nme": "lambda$static$4", "acc": 4106, "dsc": "(Lnet/kyori/adventure/nbt/ShortBinaryTag;Ljava/io/DataOutput;)V", "exs": ["java/io/IOException"]}, {"nme": "lambda$static$3", "acc": 4106, "dsc": "(Ljava/io/DataInput;)Lnet/kyori/adventure/nbt/ShortBinaryTag;", "exs": ["java/io/IOException"]}, {"nme": "lambda$static$2", "acc": 4106, "dsc": "(Lnet/kyori/adventure/nbt/ByteBinaryTag;Ljava/io/DataOutput;)V", "exs": ["java/io/IOException"]}, {"nme": "lambda$static$1", "acc": 4106, "dsc": "(Ljava/io/DataInput;)Lnet/kyori/adventure/nbt/ByteBinaryTag;", "exs": ["java/io/IOException"]}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "(Ljava/io/DataInput;)Lnet/kyori/adventure/nbt/EndBinaryTag;", "exs": ["java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "END", "dsc": "Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "Lnet/kyori/adventure/nbt/BinaryTagType<Lnet/kyori/adventure/nbt/EndBinaryTag;>;"}, {"acc": 25, "nme": "BYTE", "dsc": "Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "Lnet/kyori/adventure/nbt/BinaryTagType<Lnet/kyori/adventure/nbt/ByteBinaryTag;>;"}, {"acc": 25, "nme": "SHORT", "dsc": "Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "Lnet/kyori/adventure/nbt/BinaryTagType<Lnet/kyori/adventure/nbt/ShortBinaryTag;>;"}, {"acc": 25, "nme": "INT", "dsc": "Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "Lnet/kyori/adventure/nbt/BinaryTagType<Lnet/kyori/adventure/nbt/IntBinaryTag;>;"}, {"acc": 25, "nme": "LONG", "dsc": "Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "Lnet/kyori/adventure/nbt/BinaryTagType<Lnet/kyori/adventure/nbt/LongBinaryTag;>;"}, {"acc": 25, "nme": "FLOAT", "dsc": "Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "Lnet/kyori/adventure/nbt/BinaryTagType<Lnet/kyori/adventure/nbt/FloatBinaryTag;>;"}, {"acc": 25, "nme": "DOUBLE", "dsc": "Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "Lnet/kyori/adventure/nbt/BinaryTagType<Lnet/kyori/adventure/nbt/DoubleBinaryTag;>;"}, {"acc": 25, "nme": "BYTE_ARRAY", "dsc": "Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "Lnet/kyori/adventure/nbt/BinaryTagType<Lnet/kyori/adventure/nbt/ByteArrayBinaryTag;>;"}, {"acc": 25, "nme": "STRING", "dsc": "Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "Lnet/kyori/adventure/nbt/BinaryTagType<Lnet/kyori/adventure/nbt/StringBinaryTag;>;"}, {"acc": 25, "nme": "LIST", "dsc": "Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "Lnet/kyori/adventure/nbt/BinaryTagType<Lnet/kyori/adventure/nbt/ListBinaryTag;>;"}, {"acc": 25, "nme": "COMPOUND", "dsc": "Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "Lnet/kyori/adventure/nbt/BinaryTagType<Lnet/kyori/adventure/nbt/CompoundBinaryTag;>;"}, {"acc": 25, "nme": "INT_ARRAY", "dsc": "Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "Lnet/kyori/adventure/nbt/BinaryTagType<Lnet/kyori/adventure/nbt/IntArrayBinaryTag;>;"}, {"acc": 25, "nme": "LONG_ARRAY", "dsc": "Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "Lnet/kyori/adventure/nbt/BinaryTagType<Lnet/kyori/adventure/nbt/LongArrayBinaryTag;>;"}]}, "net/kyori/adventure/nbt/ListBinaryTagImpl$1.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/nbt/ListBinaryTagImpl$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lnet/kyori/adventure/nbt/ListBinaryTagImpl;Ljava/util/Iterator;)V"}, {"nme": "hasNext", "acc": 1, "dsc": "()Z"}, {"nme": "next", "acc": 1, "dsc": "()Lnet/kyori/adventure/nbt/BinaryTag;"}, {"nme": "forEachRemaining", "acc": 1, "dsc": "(Ljava/util/function/Consumer;)V", "sig": "(Ljava/util/function/Consumer<-Lnet/kyori/adventure/nbt/BinaryTag;>;)V"}, {"nme": "next", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 4112, "nme": "val$iterator", "dsc": "<PERSON><PERSON><PERSON>/util/Iterator;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lnet/kyori/adventure/nbt/ListBinaryTagImpl;"}]}, "net/kyori/adventure/nbt/IntArrayBinaryTag.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/nbt/IntArrayBinaryTag", "super": "java/lang/Object", "mthds": [{"nme": "of", "acc": 137, "dsc": "([I)Lnet/kyori/adventure/nbt/IntArrayBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "type", "acc": 1, "dsc": "()Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "()Lnet/kyori/adventure/nbt/BinaryTagType<Lnet/kyori/adventure/nbt/IntArrayBinaryTag;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "value", "acc": 1025, "dsc": "()[I"}, {"nme": "size", "acc": 1025, "dsc": "()I"}, {"nme": "get", "acc": 1025, "dsc": "(I)I"}, {"nme": "iterator", "acc": 1025, "dsc": "()Ljava/util/PrimitiveIterator$OfInt;"}, {"nme": "spliterator", "acc": 1025, "dsc": "()Ljava/util/Spliterator$OfInt;"}, {"nme": "stream", "acc": 1025, "dsc": "()Ljava/util/stream/IntStream;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "forEachInt", "acc": 1025, "dsc": "(L<PERSON><PERSON>/util/function/IntConsumer;)V"}, {"nme": "spliterator", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/util/Spliterator;"}, {"nme": "iterator", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/util/Iterator;"}], "flds": []}, "net/kyori/adventure/nbt/IntBinaryTag.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/nbt/IntBinaryTag", "super": "java/lang/Object", "mthds": [{"nme": "of", "acc": 9, "dsc": "(I)Lnet/kyori/adventure/nbt/IntBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "type", "acc": 1, "dsc": "()Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "()Lnet/kyori/adventure/nbt/BinaryTagType<Lnet/kyori/adventure/nbt/IntBinaryTag;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "value", "acc": 1025, "dsc": "()I"}], "flds": []}, "net/kyori/adventure/nbt/ByteArrayBinaryTagImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/nbt/ByteArrayBinaryTagImpl", "super": "net/kyori/adventure/nbt/ArrayBinaryTagImpl", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "([B)V"}, {"nme": "value", "acc": 1, "dsc": "()[B"}, {"nme": "size", "acc": 1, "dsc": "()I"}, {"nme": "get", "acc": 1, "dsc": "(I)B"}, {"nme": "value", "acc": 8, "dsc": "(Lnet/kyori/adventure/nbt/ByteArrayBinaryTag;)[B"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "examinableProperties", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<+Lnet/kyori/examination/ExaminableProperty;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "iterator", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Iterator;", "sig": "()Ljava/util/Iterator<Ljava/lang/Byte;>;"}], "flds": [{"acc": 16, "nme": "value", "dsc": "[B"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/Debug$Renderer;", "vals": ["text", "\"byte[\" + this.value.length + \"]\"", "childrenA<PERSON>y", "this.value", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "this.value.length > 0"]}]}, "net/kyori/adventure/nbt/CharBuffer.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/nbt/Char<PERSON>uffer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;)V"}, {"nme": "peek", "acc": 1, "dsc": "()C"}, {"nme": "peek", "acc": 1, "dsc": "(I)C"}, {"nme": "take", "acc": 1, "dsc": "()C"}, {"nme": "advance", "acc": 1, "dsc": "()Z"}, {"nme": "hasMore", "acc": 1, "dsc": "()Z"}, {"nme": "hasMore", "acc": 1, "dsc": "(I)Z"}, {"nme": "takeUntil", "acc": 1, "dsc": "(C)Ljava/lang/CharSequence;", "exs": ["net/kyori/adventure/nbt/StringTagParseException"]}, {"nme": "expect", "acc": 1, "dsc": "(C)Lnet/kyori/adventure/nbt/Char<PERSON><PERSON>er;", "exs": ["net/kyori/adventure/nbt/StringTagParseException"]}, {"nme": "takeIf", "acc": 1, "dsc": "(C)Z"}, {"nme": "skipWhitespace", "acc": 1, "dsc": "()Lnet/kyori/adventure/nbt/CharBuffer;"}, {"nme": "makeError", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/nbt/StringTagParseException;"}], "flds": [{"acc": 18, "nme": "sequence", "dsc": "<PERSON><PERSON><PERSON>/lang/CharSequence;"}, {"acc": 2, "nme": "index", "dsc": "I"}]}, "net/kyori/adventure/nbt/FloatBinaryTag.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/nbt/FloatBinaryTag", "super": "java/lang/Object", "mthds": [{"nme": "of", "acc": 9, "dsc": "(F)Lnet/kyori/adventure/nbt/FloatBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "type", "acc": 1, "dsc": "()Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "()Lnet/kyori/adventure/nbt/BinaryTagType<Lnet/kyori/adventure/nbt/FloatBinaryTag;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "value", "acc": 1025, "dsc": "()F"}], "flds": []}, "net/kyori/adventure/nbt/BinaryTag.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/nbt/BinaryTag", "super": "java/lang/Object", "mthds": [{"nme": "type", "acc": 1025, "dsc": "()Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "()Lnet/kyori/adventure/nbt/BinaryTagType<+Lnet/kyori/adventure/nbt/BinaryTag;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "asBinaryTag", "acc": 1, "dsc": "()Lnet/kyori/adventure/nbt/BinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": []}, "net/kyori/adventure/nbt/LongArrayBinaryTagImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/nbt/LongArrayBinaryTagImpl", "super": "net/kyori/adventure/nbt/ArrayBinaryTagImpl", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "([J)V"}, {"nme": "value", "acc": 1, "dsc": "()[J"}, {"nme": "size", "acc": 1, "dsc": "()I"}, {"nme": "get", "acc": 1, "dsc": "(I)J"}, {"nme": "iterator", "acc": 1, "dsc": "()Ljava/util/PrimitiveIterator$OfLong;"}, {"nme": "spliterator", "acc": 1, "dsc": "()Ljava/util/Spliterator$OfLong;"}, {"nme": "stream", "acc": 1, "dsc": "()Ljava/util/stream/LongStream;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "forEachLong", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/function/LongConsumer;)V"}, {"nme": "value", "acc": 8, "dsc": "(Lnet/kyori/adventure/nbt/LongArrayBinaryTag;)[J"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "examinableProperties", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<+Lnet/kyori/examination/ExaminableProperty;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "spliterator", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/util/Spliterator;"}, {"nme": "iterator", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/util/Iterator;"}], "flds": [{"acc": 16, "nme": "value", "dsc": "[J"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/Debug$Renderer;", "vals": ["text", "\"long[\" + this.value.length + \"]\"", "childrenA<PERSON>y", "this.value", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "this.value.length > 0"]}]}, "net/kyori/adventure/nbt/ArrayBinaryTag.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/nbt/ArrayBinaryTag", "super": "java/lang/Object", "mthds": [{"nme": "type", "acc": 1025, "dsc": "()Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "()Lnet/kyori/adventure/nbt/BinaryTagType<+Lnet/kyori/adventure/nbt/ArrayBinaryTag;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": []}, "net/kyori/adventure/nbt/DoubleBinaryTagImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/nbt/DoubleBinaryTagImpl", "super": "net/kyori/adventure/nbt/AbstractBinaryTag", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(D)V"}, {"nme": "value", "acc": 1, "dsc": "()D"}, {"nme": "byteValue", "acc": 1, "dsc": "()B"}, {"nme": "doubleValue", "acc": 1, "dsc": "()D"}, {"nme": "floatValue", "acc": 1, "dsc": "()F"}, {"nme": "intValue", "acc": 1, "dsc": "()I"}, {"nme": "longValue", "acc": 1, "dsc": "()J"}, {"nme": "shortValue", "acc": 1, "dsc": "()S"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "examinableProperties", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<+Lnet/kyori/examination/ExaminableProperty;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": [{"acc": 18, "nme": "value", "dsc": "D"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/Debug$Renderer;", "vals": ["text", "String.valueOf(this.value) + \"d\"", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "false"]}]}, "net/kyori/adventure/nbt/CompoundBinaryTag$Builder.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/nbt/CompoundBinaryTag$Builder", "super": "java/lang/Object", "mthds": [{"nme": "build", "acc": 1025, "dsc": "()Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": []}, "net/kyori/adventure/nbt/IOStreamUtil.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/nbt/IOStreamUtil", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "closeShield", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;)<PERSON>java/io/InputStream;"}, {"nme": "closeShield", "acc": 8, "dsc": "(Lja<PERSON>/io/OutputStream;)Ljava/io/OutputStream;"}], "flds": []}, "net/kyori/adventure/nbt/TagStringWriter.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/nbt/TagStringWriter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Appendable;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "legacy", "acc": 1, "dsc": "(Z)Lnet/kyori/adventure/nbt/TagStringWriter;"}, {"nme": "writeTag", "acc": 1, "dsc": "(Lnet/kyori/adventure/nbt/BinaryTag;)Lnet/kyori/adventure/nbt/TagStringWriter;", "exs": ["java/io/IOException"]}, {"nme": "writeCompound", "acc": 2, "dsc": "(Lnet/kyori/adventure/nbt/CompoundBinaryTag;)Lnet/kyori/adventure/nbt/TagStringWriter;", "exs": ["java/io/IOException"]}, {"nme": "writeList", "acc": 2, "dsc": "(Lnet/kyori/adventure/nbt/ListBinaryTag;)Lnet/kyori/adventure/nbt/TagStringWriter;", "exs": ["java/io/IOException"]}, {"nme": "writeByteArray", "acc": 2, "dsc": "(Lnet/kyori/adventure/nbt/ByteArrayBinaryTag;)Lnet/kyori/adventure/nbt/TagStringWriter;", "exs": ["java/io/IOException"]}, {"nme": "writeIntArray", "acc": 2, "dsc": "(Lnet/kyori/adventure/nbt/IntArrayBinaryTag;)Lnet/kyori/adventure/nbt/TagStringWriter;", "exs": ["java/io/IOException"]}, {"nme": "writeLongArray", "acc": 2, "dsc": "(Lnet/kyori/adventure/nbt/LongArrayBinaryTag;)Lnet/kyori/adventure/nbt/TagStringWriter;", "exs": ["java/io/IOException"]}, {"nme": "beginCompound", "acc": 1, "dsc": "()Lnet/kyori/adventure/nbt/TagStringWriter;", "exs": ["java/io/IOException"]}, {"nme": "endCompound", "acc": 1, "dsc": "()Lnet/kyori/adventure/nbt/TagStringWriter;", "exs": ["java/io/IOException"]}, {"nme": "key", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/nbt/TagStringWriter;", "exs": ["java/io/IOException"]}, {"nme": "value", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;C)Lnet/kyori/adventure/nbt/TagStringWriter;", "exs": ["java/io/IOException"]}, {"nme": "beginList", "acc": 1, "dsc": "()Lnet/kyori/adventure/nbt/TagStringWriter;", "exs": ["java/io/IOException"]}, {"nme": "endList", "acc": 1, "dsc": "(Z)Lnet/kyori/adventure/nbt/TagStringWriter;", "exs": ["java/io/IOException"]}, {"nme": "beginArray", "acc": 2, "dsc": "(C)Lnet/kyori/adventure/nbt/TagStringWriter;", "exs": ["java/io/IOException"]}, {"nme": "endArray", "acc": 2, "dsc": "()Lnet/kyori/adventure/nbt/TagStringWriter;", "exs": ["java/io/IOException"]}, {"nme": "writeMaybeQuoted", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)V", "exs": ["java/io/IOException"]}, {"nme": "escape", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;C)Ljava/lang/String;"}, {"nme": "printAndResetSeparator", "acc": 2, "dsc": "(Z)V", "exs": ["java/io/IOException"]}, {"nme": "breakListElement", "acc": 2, "dsc": "(Lnet/kyori/adventure/nbt/BinaryTagType;)Z", "sig": "(Lnet/kyori/adventure/nbt/BinaryTagType<*>;)Z"}, {"nme": "prettyPrinting", "acc": 2, "dsc": "()Z"}, {"nme": "newlineIndent", "acc": 2, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "appendSeparator", "acc": 2, "dsc": "(C)<PERSON><PERSON><PERSON>/lang/Appendable;", "exs": ["java/io/IOException"]}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 18, "nme": "out", "dsc": "<PERSON><PERSON><PERSON>/lang/Appendable;"}, {"acc": 18, "nme": "indent", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "level", "dsc": "I"}, {"acc": 2, "nme": "needsSeparator", "dsc": "Z"}, {"acc": 2, "nme": "legacy", "dsc": "Z"}]}, "net/kyori/adventure/nbt/BinaryTagIO$Compression$3.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/nbt/BinaryTagIO$Compression$3", "super": "net/kyori/adventure/nbt/BinaryTagIO$Compression", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "decompress", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;)<PERSON>java/io/InputStream;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "compress", "acc": 0, "dsc": "(Lja<PERSON>/io/OutputStream;)Ljava/io/OutputStream;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "net/kyori/adventure/nbt/EndBinaryTag.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/nbt/EndBinaryTag", "super": "java/lang/Object", "mthds": [{"nme": "get", "acc": 9, "dsc": "()Lnet/kyori/adventure/nbt/EndBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "type", "acc": 1, "dsc": "()Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "()Lnet/kyori/adventure/nbt/BinaryTagType<Lnet/kyori/adventure/nbt/EndBinaryTag;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": []}, "net/kyori/adventure/nbt/BinaryTagScope.class": {"ver": 52, "acc": 1536, "nme": "net/kyori/adventure/nbt/BinaryTagScope", "super": "java/lang/Object", "mthds": [{"nme": "close", "acc": 1025, "dsc": "()V", "exs": ["java/io/IOException"]}], "flds": []}, "net/kyori/adventure/nbt/BinaryTagLike.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/nbt/BinaryTagLike", "super": "java/lang/Object", "mthds": [{"nme": "asBinaryTag", "acc": 1025, "dsc": "()Lnet/kyori/adventure/nbt/BinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": []}, "net/kyori/adventure/nbt/ByteArrayBinaryTag.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/nbt/ByteArrayBinaryTag", "super": "java/lang/Object", "mthds": [{"nme": "of", "acc": 137, "dsc": "([B)Lnet/kyori/adventure/nbt/ByteArrayBinaryTag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "type", "acc": 1, "dsc": "()Lnet/kyori/adventure/nbt/BinaryTagType;", "sig": "()Lnet/kyori/adventure/nbt/BinaryTagType<Lnet/kyori/adventure/nbt/ByteArrayBinaryTag;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "value", "acc": 1025, "dsc": "()[B"}, {"nme": "size", "acc": 1025, "dsc": "()I"}, {"nme": "get", "acc": 1025, "dsc": "(I)B"}], "flds": []}, "net/kyori/adventure/nbt/IntArrayBinaryTagImpl$1.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/nbt/IntArrayBinaryTagImpl$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lnet/kyori/adventure/nbt/IntArrayBinaryTagImpl;)V"}, {"nme": "hasNext", "acc": 1, "dsc": "()Z"}, {"nme": "nextInt", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 2, "nme": "index", "dsc": "I"}, {"acc": 4112, "nme": "this$0", "dsc": "Lnet/kyori/adventure/nbt/IntArrayBinaryTagImpl;"}]}}}}