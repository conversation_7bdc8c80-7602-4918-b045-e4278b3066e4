{"md5": "27cf8d475bf9dcf035e0966810bdded2", "sha2": "1b66d37914b232cef45e56ec8c01907620de9e3a", "sha256": "eed0fb6f5ef5db17d087039f1e829cfe827363b2863265258a96f0ed323313b7", "contents": {"classes": {"com/neovisionaries/ws/client/Misc.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/Misc", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getBytesUTF8", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[B"}, {"nme": "toStringUTF8", "acc": 9, "dsc": "([B)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toStringUTF8", "acc": 9, "dsc": "([BII)Ljava/lang/String;"}, {"nme": "nextBytes", "acc": 9, "dsc": "([B)[B"}, {"nme": "nextBytes", "acc": 9, "dsc": "(I)[B"}, {"nme": "toOpcodeName", "acc": 9, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "readLine", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;<PERSON><PERSON><PERSON>/lang/String;)<PERSON>java/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "min", "acc": 9, "dsc": "([I)I"}, {"nme": "max", "acc": 9, "dsc": "([I)I"}, {"nme": "join", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "sig": "(L<PERSON><PERSON>/util/Collection<*>;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "join", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;<PERSON><PERSON><PERSON>/util/Collection;Ljava/lang/String;)V", "sig": "(L<PERSON><PERSON>/lang/StringBuilder;Ljava/util/Collection<*>;Ljava/lang/String;)V"}, {"nme": "extractHost", "acc": 9, "dsc": "(Ljava/net/URI;)Ljava/lang/String;"}, {"nme": "extractHostFromAuthorityPart", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "extractHostFromEntireUri", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getConstructor", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/Class;)Lja<PERSON>/lang/reflect/Constructor;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/Class<*>;)Ljava/lang/reflect/Constructor<*>;"}, {"nme": "newInstance", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Constructor;[<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/reflect/Constructor<*>;[<PERSON>ja<PERSON>/lang/Object;)<PERSON>ja<PERSON>/lang/Object;"}, {"nme": "getMethod", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/Class<*>;)Ljava/lang/reflect/Method;"}, {"nme": "invoke", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;<PERSON><PERSON><PERSON>/lang/Object;[Ljava/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "sRandom", "dsc": "Ljava/security/SecureRandom;"}]}, "com/neovisionaries/ws/client/FormatException.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/FormatException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}]}, "com/neovisionaries/ws/client/PeriodicalFrameSender.class": {"ver": 49, "acc": 1056, "nme": "com/neovisionaries/ws/client/PeriodicalFrameSender", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Ljava/lang/String;Lcom/neovisionaries/ws/client/PayloadGenerator;)V"}, {"nme": "start", "acc": 1, "dsc": "()V"}, {"nme": "stop", "acc": 1, "dsc": "()V"}, {"nme": "getInterval", "acc": 1, "dsc": "()J"}, {"nme": "setInterval", "acc": 1, "dsc": "(J)V"}, {"nme": "getPayloadGenerator", "acc": 1, "dsc": "()Lcom/neovisionaries/ws/client/PayloadGenerator;"}, {"nme": "setPayloadGenerator", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/PayloadGenerator;)V"}, {"nme": "getTimerName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setTimerName", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "doTask", "acc": 2, "dsc": "()V"}, {"nme": "createFrame", "acc": 2, "dsc": "()Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"nme": "generatePayload", "acc": 2, "dsc": "()[B"}, {"nme": "schedule", "acc": 10, "dsc": "(Ljava/util/Timer;Lcom/neovisionaries/ws/client/PeriodicalFrameSender$Task;J)Z"}, {"nme": "createFrame", "acc": 1028, "dsc": "([B)Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"nme": "access$100", "acc": 4104, "dsc": "(Lcom/neovisionaries/ws/client/PeriodicalFrameSender;)V"}], "flds": [{"acc": 18, "nme": "mWebSocket", "dsc": "Lcom/neovisionaries/ws/client/WebSocket;"}, {"acc": 2, "nme": "mTimerName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "mTimer", "dsc": "<PERSON><PERSON><PERSON>/util/Timer;"}, {"acc": 2, "nme": "mScheduled", "dsc": "Z"}, {"acc": 2, "nme": "mInterval", "dsc": "J"}, {"acc": 2, "nme": "mGenerator", "dsc": "Lcom/neovisionaries/ws/client/PayloadGenerator;"}]}, "com/neovisionaries/ws/client/PingSender.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/PingSender", "super": "com/neovisionaries/ws/client/PeriodicalFrameSender", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/PayloadGenerator;)V"}, {"nme": "createFrame", "acc": 4, "dsc": "([B)Lcom/neovisionaries/ws/client/WebSocketFrame;"}], "flds": [{"acc": 26, "nme": "TIMER_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "<PERSON><PERSON><PERSON>"}]}, "com/neovisionaries/ws/client/SocketInitiator$Signal.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/SocketInitiator$Signal", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/neovisionaries/ws/client/SocketInitiator;I)V"}, {"nme": "isDone", "acc": 0, "dsc": "()Z"}, {"nme": "await", "acc": 0, "dsc": "()V", "exs": ["java/lang/InterruptedException"]}, {"nme": "done", "acc": 0, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "mLatch", "dsc": "<PERSON><PERSON>va/util/concurrent/CountDownLatch;"}, {"acc": 18, "nme": "mMaxDelay", "dsc": "I"}, {"acc": 4112, "nme": "this$0", "dsc": "Lcom/neovisionaries/ws/client/SocketInitiator;"}]}, "com/neovisionaries/ws/client/SocketInitiator.class": {"ver": 49, "acc": 33, "nme": "com/neovisionaries/ws/client/SocketInitiator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/net/SocketFactory;Lcom/neovisionaries/ws/client/Address;I[Ljava/lang/String;Lcom/neovisionaries/ws/client/DualStackMode;I)V"}, {"nme": "establish", "acc": 1, "dsc": "([Ljava/net/InetAddress;)Ljava/net/Socket;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 18, "nme": "mSocketFactory", "dsc": "Ljavax/net/SocketFactory;"}, {"acc": 18, "nme": "m<PERSON><PERSON><PERSON>", "dsc": "Lcom/neovisionaries/ws/client/Address;"}, {"acc": 18, "nme": "mConnectTimeout", "dsc": "I"}, {"acc": 18, "nme": "mServerNames", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "mMode", "dsc": "Lcom/neovisionaries/ws/client/DualStackMode;"}, {"acc": 18, "nme": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dsc": "I"}]}, "com/neovisionaries/ws/client/ReadingThread$CloseTask.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/ReadingThread$CloseTask", "super": "java/util/TimerTask", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/ReadingThread;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lcom/neovisionaries/ws/client/ReadingThread;Lcom/neovisionaries/ws/client/ReadingThread$1;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lcom/neovisionaries/ws/client/ReadingThread;"}]}, "com/neovisionaries/ws/client/WebSocketInputStream.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/WebSocketInputStream", "super": "java/io/FilterInputStream", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON>va/io/InputStream;)V"}, {"nme": "readLine", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "readFrame", "acc": 1, "dsc": "()Lcom/neovisionaries/ws/client/WebSocketFrame;", "exs": ["java/io/IOException", "com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "readBytes", "acc": 0, "dsc": "([BI)V", "exs": ["java/io/IOException", "com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(J)V"}, {"nme": "readPayload", "acc": 2, "dsc": "(JZ[B)[B", "exs": ["java/io/IOException", "com/neovisionaries/ws/client/WebSocketException"]}], "flds": []}, "com/neovisionaries/ws/client/PeriodicalFrameSender$Task.class": {"ver": 49, "acc": 48, "nme": "com/neovisionaries/ws/client/PeriodicalFrameSender$Task", "super": "java/util/TimerTask", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/PeriodicalFrameSender;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lcom/neovisionaries/ws/client/PeriodicalFrameSender;Lcom/neovisionaries/ws/client/PeriodicalFrameSender$1;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lcom/neovisionaries/ws/client/PeriodicalFrameSender;"}]}, "com/neovisionaries/ws/client/PerMessageCompressionExtension.class": {"ver": 49, "acc": 1056, "nme": "com/neovisionaries/ws/client/PerMessageCompressionExtension", "super": "com/neovisionaries/ws/client/WebSocketExtension", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketExtension;)V"}, {"nme": "decompress", "acc": 1028, "dsc": "([B)[B", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "compress", "acc": 1028, "dsc": "([B)[B", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}], "flds": []}, "com/neovisionaries/ws/client/WebSocketFrame.class": {"ver": 49, "acc": 33, "nme": "com/neovisionaries/ws/client/WebSocketFrame", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getFin", "acc": 1, "dsc": "()Z"}, {"nme": "setFin", "acc": 1, "dsc": "(Z)Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"nme": "getRsv1", "acc": 1, "dsc": "()Z"}, {"nme": "setRsv1", "acc": 1, "dsc": "(Z)Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"nme": "getRsv2", "acc": 1, "dsc": "()Z"}, {"nme": "setRsv2", "acc": 1, "dsc": "(Z)Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"nme": "getRsv3", "acc": 1, "dsc": "()Z"}, {"nme": "setRsv3", "acc": 1, "dsc": "(Z)Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"nme": "getOpcode", "acc": 1, "dsc": "()I"}, {"nme": "setOpcode", "acc": 1, "dsc": "(I)Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"nme": "isContinuationFrame", "acc": 1, "dsc": "()Z"}, {"nme": "isTextFrame", "acc": 1, "dsc": "()Z"}, {"nme": "isBinaryFrame", "acc": 1, "dsc": "()Z"}, {"nme": "isCloseFrame", "acc": 1, "dsc": "()Z"}, {"nme": "isPingFrame", "acc": 1, "dsc": "()Z"}, {"nme": "isPongFrame", "acc": 1, "dsc": "()Z"}, {"nme": "isDataFrame", "acc": 1, "dsc": "()Z"}, {"nme": "isControlFrame", "acc": 1, "dsc": "()Z"}, {"nme": "getMask", "acc": 0, "dsc": "()Z"}, {"nme": "setMask", "acc": 0, "dsc": "(Z)Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"nme": "hasPayload", "acc": 1, "dsc": "()Z"}, {"nme": "getPayloadLength", "acc": 1, "dsc": "()I"}, {"nme": "getPayload", "acc": 1, "dsc": "()[B"}, {"nme": "getPayloadText", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setPayload", "acc": 1, "dsc": "([B)Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"nme": "setPayload", "acc": 1, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"nme": "setCloseFramePayload", "acc": 1, "dsc": "(ILjava/lang/String;)Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"nme": "getCloseCode", "acc": 1, "dsc": "()I"}, {"nme": "getCloseReason", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "appendPayloadCommon", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;)Z"}, {"nme": "appendPayloadText", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;)V"}, {"nme": "appendPayloadClose", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;)V"}, {"nme": "appendPayloadBinary", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;)V"}, {"nme": "createContinuationFrame", "acc": 9, "dsc": "()Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"nme": "createContinuationFrame", "acc": 9, "dsc": "([B)Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"nme": "createContinuationFrame", "acc": 9, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"nme": "createTextFrame", "acc": 9, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"nme": "createBinaryFrame", "acc": 9, "dsc": "([B)Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"nme": "createCloseFrame", "acc": 9, "dsc": "()Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"nme": "createCloseFrame", "acc": 9, "dsc": "(I)Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"nme": "createCloseFrame", "acc": 9, "dsc": "(ILjava/lang/String;)Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"nme": "createPingFrame", "acc": 9, "dsc": "()Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"nme": "createPingFrame", "acc": 9, "dsc": "([B)Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"nme": "createPingFrame", "acc": 9, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"nme": "createPongFrame", "acc": 9, "dsc": "()Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"nme": "createPongFrame", "acc": 9, "dsc": "([B)Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"nme": "createPongFrame", "acc": 9, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"nme": "mask", "acc": 8, "dsc": "([B[B)[B"}, {"nme": "compressFrame", "acc": 8, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;Lcom/neovisionaries/ws/client/PerMessageCompressionExtension;)Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"nme": "compress", "acc": 10, "dsc": "([BLcom/neovisionaries/ws/client/PerMessageCompressionExtension;)[B"}, {"nme": "splitIfNecessary", "acc": 8, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;ILcom/neovisionaries/ws/client/PerMessageCompressionExtension;)Ljava/util/List;", "sig": "(Lcom/neovisionaries/ws/client/WebSocketFrame;ILcom/neovisionaries/ws/client/PerMessageCompressionExtension;)Ljava/util/List<Lcom/neovisionaries/ws/client/WebSocketFrame;>;"}, {"nme": "split", "acc": 10, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;I)Ljava/util/List;", "sig": "(Lcom/neovisionaries/ws/client/WebSocketFrame;I)Ljava/util/List<Lcom/neovisionaries/ws/client/WebSocketFrame;>;"}], "flds": [{"acc": 2, "nme": "mFin", "dsc": "Z"}, {"acc": 2, "nme": "mRsv1", "dsc": "Z"}, {"acc": 2, "nme": "mRsv2", "dsc": "Z"}, {"acc": 2, "nme": "mRsv3", "dsc": "Z"}, {"acc": 2, "nme": "mOpcode", "dsc": "I"}, {"acc": 2, "nme": "mMask", "dsc": "Z"}, {"acc": 2, "nme": "mPayload", "dsc": "[B"}]}, "com/neovisionaries/ws/client/SocketConnector.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/SocketConnector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljavax/net/SocketFactory;Lcom/neovisionaries/ws/client/Address;I[Ljava/lang/String;I)V"}, {"nme": "<init>", "acc": 0, "dsc": "(Ljavax/net/SocketFactory;Lcom/neovisionaries/ws/client/Address;II[Ljava/lang/String;Lcom/neovisionaries/ws/client/ProxyHandshaker;Ljavax/net/ssl/SSLSocketFactory;Ljava/lang/String;I)V"}, {"nme": "getConnectionTimeout", "acc": 1, "dsc": "()I"}, {"nme": "getSocket", "acc": 1, "dsc": "()Ljava/net/Socket;"}, {"nme": "getConnectedSocket", "acc": 1, "dsc": "()Ljava/net/Socket;", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "connectSocket", "acc": 2, "dsc": "()V", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "resolveHostname", "acc": 2, "dsc": "()[Ljava/net/InetAddress;", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "connect", "acc": 1, "dsc": "()Ljava/net/Socket;", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "setDualStackSettings", "acc": 0, "dsc": "(Lcom/neovisionaries/ws/client/DualStackMode;I)Lcom/neovisionaries/ws/client/SocketConnector;"}, {"nme": "setVerifyHostname", "acc": 0, "dsc": "(Z)Lcom/neovisionaries/ws/client/SocketConnector;"}, {"nme": "doConnect", "acc": 2, "dsc": "()V", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "setSoTimeout", "acc": 2, "dsc": "(I)V", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "verifyHostname", "acc": 2, "dsc": "(Ljavax/net/ssl/SSLSocket;Ljava/lang/String;)V", "exs": ["com/neovisionaries/ws/client/HostnameUnverifiedException"]}, {"nme": "handshake", "acc": 2, "dsc": "()V", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "closeSilently", "acc": 0, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "mSocketFactory", "dsc": "Ljavax/net/SocketFactory;"}, {"acc": 18, "nme": "m<PERSON><PERSON><PERSON>", "dsc": "Lcom/neovisionaries/ws/client/Address;"}, {"acc": 18, "nme": "mConnectionTimeout", "dsc": "I"}, {"acc": 18, "nme": "mSocketTimeout", "dsc": "I"}, {"acc": 18, "nme": "mServerNames", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "mProxyHandshaker", "dsc": "Lcom/neovisionaries/ws/client/ProxyHandshaker;"}, {"acc": 18, "nme": "mSSLSocketFactory", "dsc": "Ljavax/net/ssl/SSLSocketFactory;"}, {"acc": 18, "nme": "mHost", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "mPort", "dsc": "I"}, {"acc": 2, "nme": "mDualStackMode", "dsc": "Lcom/neovisionaries/ws/client/DualStackMode;"}, {"acc": 2, "nme": "mDualStackFallbackDelay", "dsc": "I"}, {"acc": 2, "nme": "mVerifyHostname", "dsc": "Z"}, {"acc": 2, "nme": "mSocket", "dsc": "Ljava/net/Socket;"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "com/neovisionaries/ws/client/SocketFactorySettings.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/SocketFactorySettings", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/SocketFactorySettings;)V"}, {"nme": "getSocketFactory", "acc": 1, "dsc": "()Ljavax/net/SocketFactory;"}, {"nme": "setSocketFactory", "acc": 1, "dsc": "(Ljavax/net/SocketFactory;)V"}, {"nme": "getSSLSocketFactory", "acc": 1, "dsc": "()Ljavax/net/ssl/SSLSocketFactory;"}, {"nme": "setSSLSocketFactory", "acc": 1, "dsc": "(Ljavax/net/ssl/SSLSocketFactory;)V"}, {"nme": "getSSLContext", "acc": 1, "dsc": "()Ljavax/net/ssl/SSLContext;"}, {"nme": "setSSLContext", "acc": 1, "dsc": "(Ljavax/net/ssl/SSLContext;)V"}, {"nme": "selectSocketFactory", "acc": 1, "dsc": "(Z)Ljavax/net/SocketFactory;"}], "flds": [{"acc": 2, "nme": "mSocketFactory", "dsc": "Ljavax/net/SocketFactory;"}, {"acc": 2, "nme": "mSSLSocketFactory", "dsc": "Ljavax/net/ssl/SSLSocketFactory;"}, {"acc": 2, "nme": "mSSLContext", "dsc": "Ljavax/net/ssl/SSLContext;"}]}, "com/neovisionaries/ws/client/StatusLine.class": {"ver": 49, "acc": 33, "nme": "com/neovisionaries/ws/client/StatusLine", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getHttpVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getStatusCode", "acc": 1, "dsc": "()I"}, {"nme": "getReasonPhrase", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "mHttpVersion", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "mStatusCode", "dsc": "I"}, {"acc": 18, "nme": "mReasonPhrase", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "mString", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "com/neovisionaries/ws/client/StateManager.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/StateManager", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getState", "acc": 1, "dsc": "()Lcom/neovisionaries/ws/client/WebSocketState;"}, {"nme": "setState", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketState;)V"}, {"nme": "changeToClosing", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/StateManager$CloseInitiator;)V"}, {"nme": "getClosedByServer", "acc": 1, "dsc": "()Z"}], "flds": [{"acc": 2, "nme": "mState", "dsc": "Lcom/neovisionaries/ws/client/WebSocketState;"}, {"acc": 2, "nme": "mCloseInitiator", "dsc": "Lcom/neovisionaries/ws/client/StateManager$CloseInitiator;"}]}, "com/neovisionaries/ws/client/OpeningHandshakeException.class": {"ver": 49, "acc": 33, "nme": "com/neovisionaries/ws/client/OpeningHandshakeException", "super": "com/neovisionaries/ws/client/WebSocketException", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketError;Ljava/lang/String;Lcom/neovisionaries/ws/client/StatusLine;Ljava/util/Map;)V", "sig": "(Lcom/neovisionaries/ws/client/WebSocketError;Ljava/lang/String;Lcom/neovisionaries/ws/client/StatusLine;Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;>;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketError;Ljava/lang/String;Lcom/neovisionaries/ws/client/StatusLine;Ljava/util/Map;[B)V", "sig": "(Lcom/neovisionaries/ws/client/WebSocketError;Ljava/lang/String;Lcom/neovisionaries/ws/client/StatusLine;Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;>;[B)V"}, {"nme": "getStatusLine", "acc": 1, "dsc": "()Lcom/neovisionaries/ws/client/StatusLine;"}, {"nme": "getHeaders", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;>;"}, {"nme": "getBody", "acc": 1, "dsc": "()[B"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}, {"acc": 18, "nme": "mStatusLine", "dsc": "Lcom/neovisionaries/ws/client/StatusLine;"}, {"acc": 18, "nme": "mHeaders", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;>;"}, {"acc": 18, "nme": "mBody", "dsc": "[B"}]}, "com/neovisionaries/ws/client/PongSender.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/PongSender", "super": "com/neovisionaries/ws/client/PeriodicalFrameSender", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/PayloadGenerator;)V"}, {"nme": "createFrame", "acc": 4, "dsc": "([B)Lcom/neovisionaries/ws/client/WebSocketFrame;"}], "flds": [{"acc": 26, "nme": "TIMER_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "Pong<PERSON>ender"}]}, "com/neovisionaries/ws/client/SocketConnector$1.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/SocketConnector$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/neovisionaries/ws/client/SocketConnector;)V"}, {"nme": "compare", "acc": 1, "dsc": "(Ljava/net/InetAddress;Ljava/net/InetAddress;)I"}, {"nme": "compare", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)I"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lcom/neovisionaries/ws/client/SocketConnector;"}]}, "com/neovisionaries/ws/client/ThreadType.class": {"ver": 49, "acc": 16433, "nme": "com/neovisionaries/ws/client/ThreadType", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lcom/neovisionaries/ws/client/ThreadType;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/ThreadType;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "READING_THREAD", "dsc": "Lcom/neovisionaries/ws/client/ThreadType;"}, {"acc": 16409, "nme": "WRITING_THREAD", "dsc": "Lcom/neovisionaries/ws/client/ThreadType;"}, {"acc": 16409, "nme": "CONNECT_THREAD", "dsc": "Lcom/neovisionaries/ws/client/ThreadType;"}, {"acc": 16409, "nme": "FINISH_THREAD", "dsc": "Lcom/neovisionaries/ws/client/ThreadType;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lcom/neovisionaries/ws/client/ThreadType;"}]}, "com/neovisionaries/ws/client/PerMessageDeflateExtension.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/PerMessageDeflateExtension", "super": "com/neovisionaries/ws/client/PerMessageCompressionExtension", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "validate", "acc": 0, "dsc": "()V", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "isServerNoContextTakeover", "acc": 1, "dsc": "()Z"}, {"nme": "isClientNoContextTakeover", "acc": 1, "dsc": "()Z"}, {"nme": "getServerWindowSize", "acc": 1, "dsc": "()I"}, {"nme": "getClientWindowSize", "acc": 1, "dsc": "()I"}, {"nme": "validateParameter", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "computeWindowSize", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)I", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "extractMaxWindowBits", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)I", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "parseMaxWindowBits", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "decompress", "acc": 4, "dsc": "([B)[B", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "compress", "acc": 4, "dsc": "([B)[B", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "canCompress", "acc": 2, "dsc": "([B)Z"}, {"nme": "adjustCompressedData", "acc": 10, "dsc": "([B)[B", "exs": ["com/neovisionaries/ws/client/FormatException"]}, {"nme": "appendEmptyBlock", "acc": 10, "dsc": "(Lcom/neovisionaries/ws/client/ByteArray;[I)V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 10, "dsc": "(Lcom/neovisionaries/ws/client/ByteArray;[I[Z)Z", "exs": ["com/neovisionaries/ws/client/FormatException"]}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 10, "dsc": "(Lcom/neovisionaries/ws/client/ByteArray;[I)I"}, {"nme": "skipFixed<PERSON><PERSON>", "acc": 10, "dsc": "(Lcom/neovisionaries/ws/client/ByteArray;[I)V", "exs": ["com/neovisionaries/ws/client/FormatException"]}, {"nme": "skipD<PERSON><PERSON><PERSON>", "acc": 10, "dsc": "(Lcom/neovisionaries/ws/client/ByteArray;[I)V", "exs": ["com/neovisionaries/ws/client/FormatException"]}, {"nme": "skipData", "acc": 10, "dsc": "(Lcom/neovisionaries/ws/client/ByteArray;[ILcom/neovisionaries/ws/client/<PERSON><PERSON>man;Lcom/neovisionaries/ws/client/<PERSON>ffman;)V", "exs": ["com/neovisionaries/ws/client/FormatException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "SERVER_NO_CONTEXT_TAKEOVER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "server_no_context_takeover"}, {"acc": 26, "nme": "CLIENT_NO_CONTEXT_TAKEOVER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "client_no_context_takeover"}, {"acc": 26, "nme": "SERVER_MAX_WINDOW_BITS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "server_max_window_bits"}, {"acc": 26, "nme": "CLIENT_MAX_WINDOW_BITS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "client_max_window_bits"}, {"acc": 26, "nme": "COMPRESSION_TERMINATOR", "dsc": "[B"}, {"acc": 26, "nme": "MIN_BITS", "dsc": "I", "val": 8}, {"acc": 26, "nme": "MAX_BITS", "dsc": "I", "val": 15}, {"acc": 26, "nme": "MIN_WINDOW_SIZE", "dsc": "I", "val": 256}, {"acc": 26, "nme": "MAX_WINDOW_SIZE", "dsc": "I", "val": 32768}, {"acc": 26, "nme": "INCOMING_SLIDING_WINDOW_MARGIN", "dsc": "I", "val": 1024}, {"acc": 2, "nme": "mServerNoContextTakeover", "dsc": "Z"}, {"acc": 2, "nme": "mClientNoContextTakeover", "dsc": "Z"}, {"acc": 2, "nme": "mServerWindowSize", "dsc": "I"}, {"acc": 2, "nme": "mClientWindowSize", "dsc": "I"}, {"acc": 2, "nme": "mIncomingSlidingWindowBufferSize", "dsc": "I"}, {"acc": 2, "nme": "mIncomingSlidingWindow", "dsc": "Lcom/neovisionaries/ws/client/ByteArray;"}]}, "com/neovisionaries/ws/client/WebSocketState.class": {"ver": 49, "acc": 16433, "nme": "com/neovisionaries/ws/client/WebSocketState", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lcom/neovisionaries/ws/client/WebSocketState;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;)Lcom/neovisionaries/ws/client/WebSocketState;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "CREATED", "dsc": "Lcom/neovisionaries/ws/client/WebSocketState;"}, {"acc": 16409, "nme": "CONNECTING", "dsc": "Lcom/neovisionaries/ws/client/WebSocketState;"}, {"acc": 16409, "nme": "OPEN", "dsc": "Lcom/neovisionaries/ws/client/WebSocketState;"}, {"acc": 16409, "nme": "CLOSING", "dsc": "Lcom/neovisionaries/ws/client/WebSocketState;"}, {"acc": 16409, "nme": "CLOSED", "dsc": "Lcom/neovisionaries/ws/client/WebSocketState;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lcom/neovisionaries/ws/client/WebSocketState;"}]}, "com/neovisionaries/ws/client/FinishThread.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/FinishThread", "super": "com/neovisionaries/ws/client/WebSocketThread", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;)V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()V"}], "flds": []}, "com/neovisionaries/ws/client/WebSocket.class": {"ver": 49, "acc": 33, "nme": "com/neovisionaries/ws/client/WebSocket", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFactory;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/neovisionaries/ws/client/SocketConnector;)V"}, {"nme": "recreate", "acc": 1, "dsc": "()Lcom/neovisionaries/ws/client/WebSocket;", "exs": ["java/io/IOException"]}, {"nme": "recreate", "acc": 1, "dsc": "(I)Lcom/neovisionaries/ws/client/WebSocket;", "exs": ["java/io/IOException"]}, {"nme": "finalize", "acc": 4, "dsc": "()V", "exs": ["java/lang/Throwable"]}, {"nme": "getState", "acc": 1, "dsc": "()Lcom/neovisionaries/ws/client/WebSocketState;"}, {"nme": "isOpen", "acc": 1, "dsc": "()Z"}, {"nme": "isInState", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketState;)Z"}, {"nme": "addProtocol", "acc": 1, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "removeProtocol", "acc": 1, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "clearProtocols", "acc": 1, "dsc": "()Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "addExtension", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketExtension;)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "addExtension", "acc": 1, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "removeExtension", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketExtension;)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "removeExtensions", "acc": 1, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "clearExtensions", "acc": 1, "dsc": "()Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "addHeader", "acc": 1, "dsc": "(Ljava/lang/String;Ljava/lang/String;)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "removeHeaders", "acc": 1, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "clearHeaders", "acc": 1, "dsc": "()Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "setUserInfo", "acc": 1, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "setUserInfo", "acc": 1, "dsc": "(Ljava/lang/String;Ljava/lang/String;)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "clearUserInfo", "acc": 1, "dsc": "()Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "isExtended", "acc": 1, "dsc": "()Z"}, {"nme": "setExtended", "acc": 1, "dsc": "(Z)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "isAutoFlush", "acc": 1, "dsc": "()Z"}, {"nme": "setAutoFlush", "acc": 1, "dsc": "(Z)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "isMissingCloseFrameAllowed", "acc": 1, "dsc": "()Z"}, {"nme": "setMissingCloseFrameAllowed", "acc": 1, "dsc": "(Z)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "isDirectTextMessage", "acc": 1, "dsc": "()Z"}, {"nme": "setDirectTextMessage", "acc": 1, "dsc": "(Z)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "flush", "acc": 1, "dsc": "()Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "getFrameQueueSize", "acc": 1, "dsc": "()I"}, {"nme": "setFrameQueueSize", "acc": 1, "dsc": "(I)Lcom/neovisionaries/ws/client/WebSocket;", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "getMaxPayloadSize", "acc": 1, "dsc": "()I"}, {"nme": "setMaxPayloadSize", "acc": 1, "dsc": "(I)Lcom/neovisionaries/ws/client/WebSocket;", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "getPingInterval", "acc": 1, "dsc": "()J"}, {"nme": "setPingInterval", "acc": 1, "dsc": "(J)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "getPongInterval", "acc": 1, "dsc": "()J"}, {"nme": "setPongInterval", "acc": 1, "dsc": "(J)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "getPingPayloadGenerator", "acc": 1, "dsc": "()Lcom/neovisionaries/ws/client/PayloadGenerator;"}, {"nme": "setPingPayloadGenerator", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/PayloadGenerator;)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "getPongPayloadGenerator", "acc": 1, "dsc": "()Lcom/neovisionaries/ws/client/PayloadGenerator;"}, {"nme": "setPongPayloadGenerator", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/PayloadGenerator;)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "getPingSenderName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setPingSenderName", "acc": 1, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "getPongSenderName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setPongSenderName", "acc": 1, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "addListener", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketListener;)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "addListeners", "acc": 1, "dsc": "(Ljava/util/List;)Lcom/neovisionaries/ws/client/WebSocket;", "sig": "(Ljava/util/List<Lcom/neovisionaries/ws/client/WebSocketListener;>;)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "removeListener", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketListener;)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "removeListeners", "acc": 1, "dsc": "(Ljava/util/List;)Lcom/neovisionaries/ws/client/WebSocket;", "sig": "(Ljava/util/List<Lcom/neovisionaries/ws/client/WebSocketListener;>;)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "clearListeners", "acc": 1, "dsc": "()Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "getSocket", "acc": 1, "dsc": "()Ljava/net/Socket;"}, {"nme": "getConnectedSocket", "acc": 1, "dsc": "()Ljava/net/Socket;", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "getURI", "acc": 1, "dsc": "()Ljava/net/URI;"}, {"nme": "connect", "acc": 1, "dsc": "()Lcom/neovisionaries/ws/client/WebSocket;", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "connect", "acc": 1, "dsc": "(Ljava/util/concurrent/ExecutorService;)Ljava/util/concurrent/Future;", "sig": "(Ljava/util/concurrent/ExecutorService;)Ljava/util/concurrent/Future<Lcom/neovisionaries/ws/client/WebSocket;>;"}, {"nme": "connectable", "acc": 1, "dsc": "()Ljava/util/concurrent/Callable;", "sig": "()Ljava/util/concurrent/Callable<Lcom/neovisionaries/ws/client/WebSocket;>;"}, {"nme": "connectAsynchronously", "acc": 1, "dsc": "()Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "disconnect", "acc": 1, "dsc": "()Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "disconnect", "acc": 1, "dsc": "(I)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "disconnect", "acc": 1, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "disconnect", "acc": 1, "dsc": "(ILjava/lang/String;)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "disconnect", "acc": 1, "dsc": "(ILjava/lang/String;J)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "getAgreedExtensions", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lcom/neovisionaries/ws/client/WebSocketExtension;>;"}, {"nme": "getAgreedProtocol", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "sendFrame", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "splitIfNecessary", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)Ljava/util/List;", "sig": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)Ljava/util/List<Lcom/neovisionaries/ws/client/WebSocketFrame;>;"}, {"nme": "sendContinuation", "acc": 1, "dsc": "()Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "sendContinuation", "acc": 1, "dsc": "(Z)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "sendContinuation", "acc": 1, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "sendContinuation", "acc": 1, "dsc": "(<PERSON>java/lang/String;Z)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "sendContinuation", "acc": 1, "dsc": "([B)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "sendContinuation", "acc": 1, "dsc": "([BZ)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "sendText", "acc": 1, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "sendText", "acc": 1, "dsc": "(<PERSON>java/lang/String;Z)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "sendBinary", "acc": 1, "dsc": "([B)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "sendBinary", "acc": 1, "dsc": "([BZ)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "sendClose", "acc": 1, "dsc": "()Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "sendClose", "acc": 1, "dsc": "(I)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "sendClose", "acc": 1, "dsc": "(ILjava/lang/String;)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "sendPing", "acc": 1, "dsc": "()Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "sendPing", "acc": 1, "dsc": "([B)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "sendPing", "acc": 1, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "sendPong", "acc": 1, "dsc": "()Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "sendPong", "acc": 1, "dsc": "([B)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "sendPong", "acc": 1, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/WebSocket;"}, {"nme": "changeStateOnConnect", "acc": 2, "dsc": "()V", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "shakeHands", "acc": 2, "dsc": "(Ljava/net/Socket;)Ljava/util/Map;", "sig": "(Ljava/net/Socket;)Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;>;", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "openInputStream", "acc": 2, "dsc": "(Ljava/net/Socket;)Lcom/neovisionaries/ws/client/WebSocketInputStream;", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "openOutputStream", "acc": 2, "dsc": "(Ljava/net/Socket;)Lcom/neovisionaries/ws/client/WebSocketOutputStream;", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "generateWebSocketKey", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "writeHandshake", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketOutputStream;Ljava/lang/String;)V", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "readHandshake", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketInputStream;Ljava/lang/String;)Ljava/util/Map;", "sig": "(Lcom/neovisionaries/ws/client/WebSocketInputStream;Ljava/lang/String;)Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;>;", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "startThreads", "acc": 2, "dsc": "()V"}, {"nme": "stopThreads", "acc": 2, "dsc": "(J)V"}, {"nme": "getInput", "acc": 0, "dsc": "()Lcom/neovisionaries/ws/client/WebSocketInputStream;"}, {"nme": "getOutput", "acc": 0, "dsc": "()Lcom/neovisionaries/ws/client/WebSocketOutputStream;"}, {"nme": "getStateManager", "acc": 0, "dsc": "()Lcom/neovisionaries/ws/client/StateManager;"}, {"nme": "getListenerManager", "acc": 0, "dsc": "()Lcom/neovisionaries/ws/client/ListenerManager;"}, {"nme": "getHandshakeBuilder", "acc": 0, "dsc": "()Lcom/neovisionaries/ws/client/HandshakeBuilder;"}, {"nme": "setAgreedExtensions", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Lcom/neovisionaries/ws/client/WebSocketExtension;>;)V"}, {"nme": "setAgreedProtocol", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "onReadingThreadStarted", "acc": 0, "dsc": "()V"}, {"nme": "onWritingThreadStarted", "acc": 0, "dsc": "()V"}, {"nme": "callOnConnectedIfNotYet", "acc": 2, "dsc": "()V"}, {"nme": "onThreadsStarted", "acc": 2, "dsc": "()V"}, {"nme": "onReadingThreadFinished", "acc": 0, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V"}, {"nme": "onWritingThreadFinished", "acc": 0, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V"}, {"nme": "onThreadsFinished", "acc": 2, "dsc": "()V"}, {"nme": "finish", "acc": 0, "dsc": "()V"}, {"nme": "finishAsynchronously", "acc": 2, "dsc": "()V"}, {"nme": "findAgreedPerMessageCompressionExtension", "acc": 2, "dsc": "()Lcom/neovisionaries/ws/client/PerMessageCompressionExtension;"}, {"nme": "getPerMessageCompressionExtension", "acc": 0, "dsc": "()Lcom/neovisionaries/ws/client/PerMessageCompressionExtension;"}], "flds": [{"acc": 26, "nme": "DEFAULT_CLOSE_DELAY", "dsc": "J", "val": 10000}, {"acc": 18, "nme": "mWebSocketFactory", "dsc": "Lcom/neovisionaries/ws/client/WebSocketFactory;"}, {"acc": 18, "nme": "mSocketConnector", "dsc": "Lcom/neovisionaries/ws/client/SocketConnector;"}, {"acc": 18, "nme": "mStateManager", "dsc": "Lcom/neovisionaries/ws/client/StateManager;"}, {"acc": 2, "nme": "mHandshakeBuilder", "dsc": "Lcom/neovisionaries/ws/client/HandshakeBuilder;"}, {"acc": 18, "nme": "mListenerManager", "dsc": "Lcom/neovisionaries/ws/client/ListenerManager;"}, {"acc": 18, "nme": "mPingSender", "dsc": "Lcom/neovisionaries/ws/client/PingSender;"}, {"acc": 18, "nme": "mPongSender", "dsc": "Lcom/neovisionaries/ws/client/PongSender;"}, {"acc": 18, "nme": "mThreadsLock", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 2, "nme": "mInput", "dsc": "Lcom/neovisionaries/ws/client/WebSocketInputStream;"}, {"acc": 2, "nme": "mOutput", "dsc": "Lcom/neovisionaries/ws/client/WebSocketOutputStream;"}, {"acc": 2, "nme": "mReadingThread", "dsc": "Lcom/neovisionaries/ws/client/ReadingThread;"}, {"acc": 2, "nme": "mWritingThread", "dsc": "Lcom/neovisionaries/ws/client/WritingThread;"}, {"acc": 2, "nme": "mServerHeaders", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;>;"}, {"acc": 2, "nme": "mAgreedExtensions", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lcom/neovisionaries/ws/client/WebSocketExtension;>;"}, {"acc": 2, "nme": "mAgreedProtocol", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "mExtended", "dsc": "Z"}, {"acc": 2, "nme": "mAutoFlush", "dsc": "Z"}, {"acc": 2, "nme": "mMissingCloseFrameAllowed", "dsc": "Z"}, {"acc": 2, "nme": "mDirectTextMessage", "dsc": "Z"}, {"acc": 2, "nme": "mFrameQueueSize", "dsc": "I"}, {"acc": 2, "nme": "mMaxPayloadSize", "dsc": "I"}, {"acc": 2, "nme": "mOnConnectedCalled", "dsc": "Z"}, {"acc": 2, "nme": "mOnConnectedCalledLock", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 2, "nme": "mReadingThreadStarted", "dsc": "Z"}, {"acc": 2, "nme": "mWritingThreadStarted", "dsc": "Z"}, {"acc": 2, "nme": "mReadingThreadFinished", "dsc": "Z"}, {"acc": 2, "nme": "mWritingThreadFinished", "dsc": "Z"}, {"acc": 2, "nme": "mServerCloseFrame", "dsc": "Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"acc": 2, "nme": "mClientCloseFrame", "dsc": "Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"acc": 2, "nme": "mPerMessageCompressionExtension", "dsc": "Lcom/neovisionaries/ws/client/PerMessageCompressionExtension;"}]}, "com/neovisionaries/ws/client/WritingThread.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/WritingThread", "super": "com/neovisionaries/ws/client/WebSocketThread", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;)V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()V"}, {"nme": "main", "acc": 2, "dsc": "()V"}, {"nme": "requestStop", "acc": 1, "dsc": "()V"}, {"nme": "queueFrame", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)Z"}, {"nme": "isHighPriorityFrame", "acc": 10, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)Z"}, {"nme": "addHighPriorityFrame", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V"}, {"nme": "queueFlush", "acc": 1, "dsc": "()V"}, {"nme": "flushIgnoreError", "acc": 2, "dsc": "()V"}, {"nme": "flush", "acc": 2, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "waitF<PERSON><PERSON><PERSON>es", "acc": 2, "dsc": "()I"}, {"nme": "sendFrames", "acc": 2, "dsc": "(Z)V", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "isFlushNeeded", "acc": 2, "dsc": "(Z)Z"}, {"nme": "flushIfLongInterval", "acc": 2, "dsc": "(J)J", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "do<PERSON><PERSON><PERSON>", "acc": 2, "dsc": "()V", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "sendFrame", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "changeToClosing", "acc": 2, "dsc": "()V"}, {"nme": "notifyFinished", "acc": 2, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "SHOULD_SEND", "dsc": "I", "val": 0}, {"acc": 26, "nme": "SHOULD_STOP", "dsc": "I", "val": 1}, {"acc": 26, "nme": "SHOULD_CONTINUE", "dsc": "I", "val": 2}, {"acc": 26, "nme": "SHOULD_FLUSH", "dsc": "I", "val": 3}, {"acc": 26, "nme": "FLUSH_THRESHOLD", "dsc": "I", "val": 1000}, {"acc": 18, "nme": "mFrames", "dsc": "<PERSON><PERSON><PERSON>/util/LinkedList;", "sig": "Ljava/util/LinkedList<Lcom/neovisionaries/ws/client/WebSocketFrame;>;"}, {"acc": 18, "nme": "mPMCE", "dsc": "Lcom/neovisionaries/ws/client/PerMessageCompressionExtension;"}, {"acc": 2, "nme": "mStopRequested", "dsc": "Z"}, {"acc": 2, "nme": "mCloseFrame", "dsc": "Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"acc": 2, "nme": "mFlush<PERSON><PERSON>ed", "dsc": "Z"}, {"acc": 2, "nme": "mStopped", "dsc": "Z"}]}, "com/neovisionaries/ws/client/Huffman.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/<PERSON><PERSON>man", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "([I)V"}, {"nme": "createIntArray", "acc": 10, "dsc": "(II)[I"}, {"nme": "createCountsFromCodeLen", "acc": 10, "dsc": "([II)[I"}, {"nme": "createMaxCodeValsFromCodeLen", "acc": 10, "dsc": "([II[<PERSON><PERSON><PERSON>/lang/Object;)[I"}, {"nme": "createSymsFromCodeVal", "acc": 10, "dsc": "([I[II)[I"}, {"nme": "readSym", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/ByteArray;[I)I", "exs": ["com/neovisionaries/ws/client/FormatException"]}], "flds": [{"acc": 18, "nme": "mMinCodeLen", "dsc": "I"}, {"acc": 18, "nme": "mMaxCodeLen", "dsc": "I"}, {"acc": 18, "nme": "mMaxCodeValsFromCodeLen", "dsc": "[I"}, {"acc": 18, "nme": "mSymsFromCodeVal", "dsc": "[I"}]}, "com/neovisionaries/ws/client/ConnectThread.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/ConnectThread", "super": "com/neovisionaries/ws/client/WebSocketThread", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;)V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()V"}, {"nme": "handleError", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketException;)V"}], "flds": []}, "com/neovisionaries/ws/client/HostnameUnverifiedException.class": {"ver": 49, "acc": 33, "nme": "com/neovisionaries/ws/client/HostnameUnverifiedException", "super": "com/neovisionaries/ws/client/WebSocketException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/net/ssl/SSLSocket;Ljava/lang/String;)V"}, {"nme": "stringifyPrincipal", "acc": 10, "dsc": "(Ljavax/net/ssl/SSLSocket;)Ljava/lang/String;"}, {"nme": "getSSLSocket", "acc": 1, "dsc": "()Ljavax/net/ssl/SSLSocket;"}, {"nme": "getHostname", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}, {"acc": 18, "nme": "mSSLSocket", "dsc": "Ljavax/net/ssl/SSLSocket;"}, {"acc": 18, "nme": "mHostname", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "com/neovisionaries/ws/client/SocketInitiator$1.class": {"ver": 49, "acc": 4128, "nme": "com/neovisionaries/ws/client/SocketInitiator$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "com/neovisionaries/ws/client/WebSocketOutputStream.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/WebSocketOutputStream", "super": "java/io/BufferedOutputStream", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/io/OutputStream;)V"}, {"nme": "write", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["java/io/IOException"]}, {"nme": "writeFrame0", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["java/io/IOException"]}, {"nme": "writeFrame1", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["java/io/IOException"]}, {"nme": "writeFrameExtendedPayloadLength", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["java/io/IOException"]}, {"nme": "writeFramePayload", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;[B)V", "exs": ["java/io/IOException"]}], "flds": []}, "com/neovisionaries/ws/client/SocketInitiator$SocketRacer.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/SocketInitiator$SocketRacer", "super": "java/lang/Thread", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/neovisionaries/ws/client/SocketInitiator;Lcom/neovisionaries/ws/client/SocketInitiator$SocketFuture;Ljavax/net/SocketFactory;Ljava/net/SocketAddress;[Ljava/lang/String;ILcom/neovisionaries/ws/client/SocketInitiator$Signal;Lcom/neovisionaries/ws/client/SocketInitiator$Signal;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}, {"nme": "complete", "acc": 2, "dsc": "(Ljava/net/Socket;)V"}, {"nme": "abort", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Exception;)V"}], "flds": [{"acc": 18, "nme": "mFuture", "dsc": "Lcom/neovisionaries/ws/client/SocketInitiator$SocketFuture;"}, {"acc": 18, "nme": "mSocketFactory", "dsc": "Ljavax/net/SocketFactory;"}, {"acc": 18, "nme": "mSocketAddress", "dsc": "Ljava/net/SocketAddress;"}, {"acc": 2, "nme": "mServerNames", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "mConnectTimeout", "dsc": "I"}, {"acc": 18, "nme": "mStartSignal", "dsc": "Lcom/neovisionaries/ws/client/SocketInitiator$Signal;"}, {"acc": 18, "nme": "mDoneSignal", "dsc": "Lcom/neovisionaries/ws/client/SocketInitiator$Signal;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lcom/neovisionaries/ws/client/SocketInitiator;"}]}, "com/neovisionaries/ws/client/ReadingThread.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/ReadingThread", "super": "com/neovisionaries/ws/client/WebSocketThread", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;)V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()V"}, {"nme": "main", "acc": 2, "dsc": "()V"}, {"nme": "requestStop", "acc": 0, "dsc": "(J)V"}, {"nme": "callOnFrame", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V"}, {"nme": "callOnContinuationFrame", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V"}, {"nme": "callOnTextFrame", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V"}, {"nme": "callOnBinaryFrame", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V"}, {"nme": "callOnCloseFrame", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V"}, {"nme": "callOnPingFrame", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V"}, {"nme": "callOnPongFrame", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V"}, {"nme": "callOnTextMessage", "acc": 2, "dsc": "([B)V"}, {"nme": "callOnTextMessage", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "callOnBinaryMessage", "acc": 2, "dsc": "([B)V"}, {"nme": "callOnError", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketException;)V"}, {"nme": "callOnFrameError", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketException;Lcom/neovisionaries/ws/client/WebSocketFrame;)V"}, {"nme": "callOnMessageError", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketException;Ljava/util/List;)V", "sig": "(Lcom/neovisionaries/ws/client/WebSocketException;Ljava/util/List<Lcom/neovisionaries/ws/client/WebSocketFrame;>;)V"}, {"nme": "callOnMessageDecompressionError", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketException;[B)V"}, {"nme": "callOnTextMessageError", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketException;[B)V"}, {"nme": "readFrame", "acc": 2, "dsc": "()Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"nme": "verifyFrame", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "verifyReservedBits", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "verifyReservedBit1", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "verifyReservedBit1ForPMCE", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)Z", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "verifyReservedBit2", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "verifyReservedBit3", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "verifyFrameOpcode", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "verifyFrameMask", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "verifyFrameFragmentation", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "verifyFrameSize", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "createCloseFrame", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketException;)Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"nme": "handleFrame", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)Z"}, {"nme": "handleContinuationFrame", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)Z"}, {"nme": "getMessage", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)[B", "sig": "(Ljava/util/List<Lcom/neovisionaries/ws/client/WebSocketFrame;>;)[B"}, {"nme": "concatenatePayloads", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)[B", "sig": "(Ljava/util/List<Lcom/neovisionaries/ws/client/WebSocketFrame;>;)[B"}, {"nme": "getMessage", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)[B"}, {"nme": "decompress", "acc": 2, "dsc": "([B)[B"}, {"nme": "handleTextFrame", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)Z"}, {"nme": "handleBinaryFrame", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)Z"}, {"nme": "handleCloseFrame", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)Z"}, {"nme": "handlePingFrame", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)Z"}, {"nme": "handlePongFrame", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)Z"}, {"nme": "waitForCloseFrame", "acc": 2, "dsc": "()V"}, {"nme": "notifyFinished", "acc": 2, "dsc": "()V"}, {"nme": "scheduleClose", "acc": 2, "dsc": "()V"}, {"nme": "scheduleCloseTask", "acc": 2, "dsc": "()V"}, {"nme": "cancelClose", "acc": 2, "dsc": "()V"}, {"nme": "cancelCloseTask", "acc": 2, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "mStopRequested", "dsc": "Z"}, {"acc": 2, "nme": "mCloseFrame", "dsc": "Lcom/neovisionaries/ws/client/WebSocketFrame;"}, {"acc": 2, "nme": "mContinuation", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lcom/neovisionaries/ws/client/WebSocketFrame;>;"}, {"acc": 18, "nme": "mPMCE", "dsc": "Lcom/neovisionaries/ws/client/PerMessageCompressionExtension;"}, {"acc": 2, "nme": "mCloseLock", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 2, "nme": "mCloseTimer", "dsc": "<PERSON><PERSON><PERSON>/util/Timer;"}, {"acc": 2, "nme": "mCloseTask", "dsc": "Lcom/neovisionaries/ws/client/ReadingThread$CloseTask;"}, {"acc": 2, "nme": "mClose<PERSON>elay", "dsc": "J"}, {"acc": 2, "nme": "mNotWaitForCloseFrame", "dsc": "Z"}]}, "com/neovisionaries/ws/client/DualStackMode.class": {"ver": 49, "acc": 16433, "nme": "com/neovisionaries/ws/client/DualStackMode", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lcom/neovisionaries/ws/client/DualStackMode;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;)Lcom/neovisionaries/ws/client/DualStackMode;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "BOTH", "dsc": "Lcom/neovisionaries/ws/client/DualStackMode;"}, {"acc": 16409, "nme": "IPV4_ONLY", "dsc": "Lcom/neovisionaries/ws/client/DualStackMode;"}, {"acc": 16409, "nme": "IPV6_ONLY", "dsc": "Lcom/neovisionaries/ws/client/DualStackMode;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lcom/neovisionaries/ws/client/DualStackMode;"}]}, "com/neovisionaries/ws/client/WebSocketExtension.class": {"ver": 49, "acc": 33, "nme": "com/neovisionaries/ws/client/WebSocketExtension", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketExtension;)V"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getParameters", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "containsParameter", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "getParameter", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "setParameter", "acc": 1, "dsc": "(Ljava/lang/String;Ljava/lang/String;)Lcom/neovisionaries/ws/client/WebSocketExtension;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "validate", "acc": 0, "dsc": "()V", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "parse", "acc": 9, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/WebSocketExtension;"}, {"nme": "extractValue", "acc": 10, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "createInstance", "acc": 10, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/WebSocketExtension;"}], "flds": [{"acc": 25, "nme": "PERMESSAGE_DEFLATE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "permessage-deflate"}, {"acc": 18, "nme": "mName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "mParameters", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}]}, "com/neovisionaries/ws/client/WebSocketAdapter.class": {"ver": 49, "acc": 33, "nme": "com/neovisionaries/ws/client/WebSocketAdapter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "onStateChanged", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketState;)V", "exs": ["java/lang/Exception"]}, {"nme": "onConnected", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Ljava/util/Map;)V", "sig": "(Lcom/neovisionaries/ws/client/WebSocket;Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;>;)V", "exs": ["java/lang/Exception"]}, {"nme": "onConnectError", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketException;)V", "exs": ["java/lang/Exception"]}, {"nme": "onDisconnected", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketFrame;Lcom/neovisionaries/ws/client/WebSocketFrame;Z)V", "exs": ["java/lang/Exception"]}, {"nme": "onFrame", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["java/lang/Exception"]}, {"nme": "onContinuationFrame", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["java/lang/Exception"]}, {"nme": "onTextFrame", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["java/lang/Exception"]}, {"nme": "onBinaryFrame", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["java/lang/Exception"]}, {"nme": "onCloseFrame", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["java/lang/Exception"]}, {"nme": "onPingFrame", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["java/lang/Exception"]}, {"nme": "onPongFrame", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["java/lang/Exception"]}, {"nme": "onTextMessage", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Ljava/lang/String;)V", "exs": ["java/lang/Exception"]}, {"nme": "onTextMessage", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;[B)V", "exs": ["java/lang/Exception"]}, {"nme": "onBinaryMessage", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;[B)V", "exs": ["java/lang/Exception"]}, {"nme": "onSendingFrame", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["java/lang/Exception"]}, {"nme": "onFrameSent", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["java/lang/Exception"]}, {"nme": "onFrameUnsent", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["java/lang/Exception"]}, {"nme": "onError", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketException;)V", "exs": ["java/lang/Exception"]}, {"nme": "onFrameError", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketException;Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["java/lang/Exception"]}, {"nme": "onMessageError", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketException;Ljava/util/List;)V", "sig": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketException;Ljava/util/List<Lcom/neovisionaries/ws/client/WebSocketFrame;>;)V", "exs": ["java/lang/Exception"]}, {"nme": "onMessageDecompressionError", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketException;[B)V", "exs": ["java/lang/Exception"]}, {"nme": "onTextMessageError", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketException;[B)V", "exs": ["java/lang/Exception"]}, {"nme": "onSendError", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketException;Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["java/lang/Exception"]}, {"nme": "onUnexpectedError", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketException;)V", "exs": ["java/lang/Exception"]}, {"nme": "handleCallbackError", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Ljava/lang/Throwable;)V", "exs": ["java/lang/Exception"]}, {"nme": "onSendingHandshake", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Ljava/lang/String;Ljava/util/List;)V", "sig": "(Lcom/neovisionaries/ws/client/WebSocket;Ljava/lang/String;Ljava/util/List<[Ljava/lang/String;>;)V", "exs": ["java/lang/Exception"]}, {"nme": "onThreadCreated", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/ThreadType;Ljava/lang/Thread;)V", "exs": ["java/lang/Exception"]}, {"nme": "onThreadStarted", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/ThreadType;Ljava/lang/Thread;)V", "exs": ["java/lang/Exception"]}, {"nme": "onThreadStopping", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/ThreadType;Ljava/lang/Thread;)V", "exs": ["java/lang/Exception"]}], "flds": []}, "com/neovisionaries/ws/client/HandshakeBuilder.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/HandshakeBuilder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/HandshakeBuilder;)V"}, {"nme": "addProtocol", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "removeProtocol", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "clearProtocols", "acc": 1, "dsc": "()V"}, {"nme": "isValidProtocol", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "containsProtocol", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "addExtension", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketExtension;)V"}, {"nme": "addExtension", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "removeExtension", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketExtension;)V"}, {"nme": "removeExtensions", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "clearExtensions", "acc": 1, "dsc": "()V"}, {"nme": "containsExtension", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketExtension;)Z"}, {"nme": "containsExtension", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "addHeader", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "removeHeaders", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "clearHeaders", "acc": 1, "dsc": "()V"}, {"nme": "setUserInfo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setUserInfo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "clearUserInfo", "acc": 1, "dsc": "()V"}, {"nme": "getURI", "acc": 1, "dsc": "()Ljava/net/URI;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "buildRequestLine", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "buildHeaders", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<[Ljava/lang/String;>;"}, {"nme": "build", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/List;)Ljava/lang/String;", "sig": "(Lja<PERSON>/lang/String;Ljava/util/List<[Ljava/lang/String;>;)Ljava/lang/String;"}, {"nme": "copyProtocols", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)<PERSON><PERSON><PERSON>/util/Set;", "sig": "(L<PERSON><PERSON>/util/Set<Ljava/lang/String;>;)Ljava/util/Set<Ljava/lang/String;>;"}, {"nme": "copyExtensions", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Ljava/util/List;", "sig": "(Ljava/util/List<Lcom/neovisionaries/ws/client/WebSocketExtension;>;)Ljava/util/List<Lcom/neovisionaries/ws/client/WebSocketExtension;>;"}, {"nme": "copyHeaders", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Ljava/util/List;", "sig": "(Ljava/util/List<[Ljava/lang/String;>;)Ljava/util/List<[Ljava/lang/String;>;"}, {"nme": "copyHeader", "acc": 10, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)[Ljava/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CONNECTION_HEADER", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "UPGRADE_HEADER", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "VERSION_HEADER", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "RN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "\r\n"}, {"acc": 2, "nme": "mSecure", "dsc": "Z"}, {"acc": 2, "nme": "mUserInfo", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "mHost", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "mPath", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "m<PERSON><PERSON>", "dsc": "Ljava/net/URI;"}, {"acc": 2, "nme": "m<PERSON>ey", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "mProtocols", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 2, "nme": "mExtensions", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lcom/neovisionaries/ws/client/WebSocketExtension;>;"}, {"acc": 2, "nme": "mHeaders", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<[Ljava/lang/String;>;"}]}, "com/neovisionaries/ws/client/WebSocket$1.class": {"ver": 49, "acc": 4128, "nme": "com/neovisionaries/ws/client/WebSocket$1", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$com$neovisionaries$ws$client$WebSocketState", "dsc": "[I"}]}, "com/neovisionaries/ws/client/SNIHelper.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/SNIHelper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "initialize", "acc": 10, "dsc": "()V", "exs": ["java/lang/Exception"]}, {"nme": "createSNIHostName", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "createSNIHostNames", "acc": 10, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;", "sig": "([<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List<Ljava/lang/Object;>;"}, {"nme": "setServerNames", "acc": 10, "dsc": "(Ljavax/net/ssl/SSLParameters;[Ljava/lang/String;)V"}, {"nme": "setServerNames", "acc": 8, "dsc": "(L<PERSON><PERSON>/net/Socket;[Ljava/lang/String;)V"}, {"nme": "getAndroidSDKVersion", "acc": 9, "dsc": "()I"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 10, "nme": "sSNIHostNameConstructor", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Constructor;", "sig": "<PERSON><PERSON><PERSON>/lang/reflect/Constructor<*>;"}, {"acc": 10, "nme": "sSetServerNamesMethod", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}]}, "com/neovisionaries/ws/client/ByteArray.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/ByteArray", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(I)V"}, {"nme": "<init>", "acc": 1, "dsc": "([B)V"}, {"nme": "length", "acc": 1, "dsc": "()I"}, {"nme": "get", "acc": 1, "dsc": "(I)B", "exs": ["java/lang/IndexOutOfBoundsException"]}, {"nme": "expandBuffer", "acc": 2, "dsc": "(I)V"}, {"nme": "put", "acc": 1, "dsc": "(I)V"}, {"nme": "put", "acc": 1, "dsc": "([B)V"}, {"nme": "put", "acc": 1, "dsc": "([BII)V"}, {"nme": "put", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/ByteArray;II)V"}, {"nme": "toBytes", "acc": 1, "dsc": "()[B"}, {"nme": "toBytes", "acc": 1, "dsc": "(I)[B"}, {"nme": "toBytes", "acc": 1, "dsc": "(II)[B"}, {"nme": "clear", "acc": 1, "dsc": "()V"}, {"nme": "shrink", "acc": 1, "dsc": "(I)V"}, {"nme": "getBit", "acc": 1, "dsc": "(I)Z"}, {"nme": "getBits", "acc": 1, "dsc": "(II)I"}, {"nme": "getHuffmanBits", "acc": 1, "dsc": "(II)I"}, {"nme": "readBit", "acc": 1, "dsc": "([I)Z"}, {"nme": "readBits", "acc": 1, "dsc": "([II)I"}, {"nme": "setBit", "acc": 1, "dsc": "(IZ)V"}, {"nme": "clearBit", "acc": 1, "dsc": "(I)V"}], "flds": [{"acc": 26, "nme": "ADDITIONAL_BUFFER_SIZE", "dsc": "I", "val": 1024}, {"acc": 2, "nme": "m<PERSON><PERSON><PERSON>", "dsc": "<PERSON><PERSON><PERSON>/nio/<PERSON>te<PERSON>er;"}, {"acc": 2, "nme": "m<PERSON><PERSON><PERSON>", "dsc": "I"}]}, "com/neovisionaries/ws/client/Address.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/Address", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"nme": "getHostname", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getPort", "acc": 0, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "mHost", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "mPort", "dsc": "I"}, {"acc": 130, "nme": "mString", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "com/neovisionaries/ws/client/WebSocketException.class": {"ver": 49, "acc": 33, "nme": "com/neovisionaries/ws/client/WebSocketException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketError;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketError;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketError;Ljava/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketError;Ljava/lang/String;Ljava/lang/Throwable;)V"}, {"nme": "getError", "acc": 1, "dsc": "()Lcom/neovisionaries/ws/client/WebSocketError;"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}, {"acc": 18, "nme": "m<PERSON><PERSON><PERSON>", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}]}, "com/neovisionaries/ws/client/WebSocketCloseCode.class": {"ver": 49, "acc": 33, "nme": "com/neovisionaries/ws/client/WebSocketCloseCode", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "NORMAL", "dsc": "I", "val": 1000}, {"acc": 25, "nme": "AWAY", "dsc": "I", "val": 1001}, {"acc": 25, "nme": "UNCONFORMED", "dsc": "I", "val": 1002}, {"acc": 25, "nme": "UNACCEPTABLE", "dsc": "I", "val": 1003}, {"acc": 25, "nme": "NONE", "dsc": "I", "val": 1005}, {"acc": 25, "nme": "ABNORMAL", "dsc": "I", "val": 1006}, {"acc": 25, "nme": "INCONSISTENT", "dsc": "I", "val": 1007}, {"acc": 25, "nme": "VIOLATED", "dsc": "I", "val": 1008}, {"acc": 25, "nme": "OVERSIZE", "dsc": "I", "val": 1009}, {"acc": 25, "nme": "UNEXTENDED", "dsc": "I", "val": 1010}, {"acc": 25, "nme": "UNEXPECTED", "dsc": "I", "val": 1011}, {"acc": 25, "nme": "INSECURE", "dsc": "I", "val": 1015}]}, "com/neovisionaries/ws/client/NoMoreFrameException.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/NoMoreFrameException", "super": "com/neovisionaries/ws/client/WebSocketException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}]}, "com/neovisionaries/ws/client/OkHostnameVerifier.class": {"ver": 49, "acc": 48, "nme": "com/neovisionaries/ws/client/OkHostnameVerifier", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "verify", "acc": 1, "dsc": "(Ljava/lang/String;Ljavax/net/ssl/SSLSession;)Z"}, {"nme": "verify", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Ljava/security/cert/X509Certificate;)Z"}, {"nme": "verifyAsIpAddress", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "verifyIpAddress", "acc": 2, "dsc": "(Lja<PERSON>/lang/String;Ljava/security/cert/X509Certificate;)Z"}, {"nme": "verifyHostName", "acc": 2, "dsc": "(Lja<PERSON>/lang/String;Ljava/security/cert/X509Certificate;)Z"}, {"nme": "allSubjectAltNames", "acc": 9, "dsc": "(Ljava/security/cert/X509Certificate;)Ljava/util/List;", "sig": "(Ljava/security/cert/X509Certificate;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "getSubjectAltNames", "acc": 10, "dsc": "(Ljava/security/cert/X509Certificate;I)Ljava/util/List;", "sig": "(Ljava/security/cert/X509Certificate;I)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "verifyHostName", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "INSTANCE", "dsc": "Lcom/neovisionaries/ws/client/OkHostnameVerifier;"}, {"acc": 26, "nme": "VERIFY_AS_IP_ADDRESS", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 26, "nme": "ALT_DNS_NAME", "dsc": "I", "val": 2}, {"acc": 26, "nme": "ALT_IPA_NAME", "dsc": "I", "val": 7}]}, "com/neovisionaries/ws/client/WebSocketError.class": {"ver": 49, "acc": 16433, "nme": "com/neovisionaries/ws/client/WebSocketError", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lcom/neovisionaries/ws/client/WebSocketError;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/WebSocketError;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "NOT_IN_CREATED_STATE", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "SOCKET_INPUT_STREAM_FAILURE", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "SOCKET_OUTPUT_STREAM_FAILURE", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "OPENING_H<PERSON>HDSHAKE_REQUEST_FAILURE", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "OPENING_<PERSON><PERSON><PERSON><PERSON><PERSON>_RESPONSE_FAILURE", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "STATUS_LINE_EMPTY", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "STATUS_LINE_BAD_FORMAT", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "NOT_SWITCHING_PROTOCOLS", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "HTTP_HEADER_FAILURE", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "NO_UPGRADE_HEADER", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "NO_WEBSOCKET_IN_UPGRADE_HEADER", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "NO_CONNECTION_HEADER", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "NO_UPGRADE_IN_CONNECTION_HEADER", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "NO_SEC_WEBSOCKET_ACCEPT_HEADER", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "UNEXPECTED_SEC_WEBSOCKET_ACCEPT_HEADER", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "EXTENSION_PARSE_ERROR", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "UNSUPPORTED_EXTENSION", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "EXTENSIONS_CONFLICT", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "UNSUPPORTED_PROTOCOL", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "INSUFFICENT_DATA", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "INVALID_PAYLOAD_LENGTH", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "TOO_LONG_PAYLOAD", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "INSUFFICIENT_MEMORY_FOR_PAYLOAD", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "INTERRUPTED_IN_READING", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "IO_ERROR_IN_READING", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "IO_ERROR_IN_WRITING", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "FLUSH_ERROR", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "NON_ZERO_RESERVED_BITS", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "UNEXPECTED_RESERVED_BIT", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "FRAME_MASKED", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "UNKNOWN_OPCODE", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "FRAGMENTED_CONTROL_FRAME", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "UNEXPECTED_CONTINUATION_FRAME", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "CONTINUATION_NOT_CLOSED", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "TOO_LONG_CONTROL_FRAME_PAYLOAD", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "MESSAGE_CONSTRUCTION_ERROR", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "TEXT_MESSAGE_CONSTRUCTION_ERROR", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "UNEXPECTED_ERROR_IN_READING_THREAD", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "UNEXPECTED_ERROR_IN_WRITING_THREAD", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "PERMESSAGE_DEFLATE_UNSUPPORTED_PARAMETER", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "PERMESSAGE_DEFLATE_INVALID_MAX_WINDOW_BITS", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "COMPRESSION_ERROR", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "DECOMPRESSION_ERROR", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "SOCKET_CONNECT_ERROR", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "PROXY_HANDSHAKE_ERROR", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "SOCKET_OVERLAY_ERROR", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "SSL_HANDSHAKE_ERROR", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "NO_MORE_FRAME", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 16409, "nme": "HOSTNAME_UNVERIFIED", "dsc": "Lcom/neovisionaries/ws/client/WebSocketError;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lcom/neovisionaries/ws/client/WebSocketError;"}]}, "com/neovisionaries/ws/client/Base64.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/Base64", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "encode", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "encode", "acc": 9, "dsc": "([B)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "extractBits", "acc": 10, "dsc": "([BI)I"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "INDEX_TABLE", "dsc": "[B"}]}, "com/neovisionaries/ws/client/WebSocketOpcode.class": {"ver": 49, "acc": 33, "nme": "com/neovisionaries/ws/client/WebSocketOpcode", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "CONTINUATION", "dsc": "I", "val": 0}, {"acc": 25, "nme": "TEXT", "dsc": "I", "val": 1}, {"acc": 25, "nme": "BINARY", "dsc": "I", "val": 2}, {"acc": 25, "nme": "CLOSE", "dsc": "I", "val": 8}, {"acc": 25, "nme": "PING", "dsc": "I", "val": 9}, {"acc": 25, "nme": "PONG", "dsc": "I", "val": 10}]}, "com/neovisionaries/ws/client/InsufficientDataException.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/InsufficientDataException", "super": "com/neovisionaries/ws/client/WebSocketException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(II)V"}, {"nme": "getRequestedByteCount", "acc": 1, "dsc": "()I"}, {"nme": "getReadByteCount", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}, {"acc": 18, "nme": "mRequestedByteCount", "dsc": "I"}, {"acc": 18, "nme": "mReadByteCount", "dsc": "I"}]}, "com/neovisionaries/ws/client/DeflateCompressor.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/DeflateCompressor", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "compress", "acc": 9, "dsc": "([B)[B", "exs": ["java/io/IOException"]}, {"nme": "createDeflater", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/util/zip/Deflater;"}], "flds": []}, "com/neovisionaries/ws/client/FixedDistanceHuffman.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/FixedDistanceHuffman", "super": "com/neovisionaries/ws/client/<PERSON><PERSON>man", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "buildCodeLensFromSym", "acc": 10, "dsc": "()[I"}, {"nme": "getInstance", "acc": 9, "dsc": "()Lcom/neovisionaries/ws/client/FixedDistanceHuffman;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "INSTANCE", "dsc": "Lcom/neovisionaries/ws/client/FixedDistanceHuffman;"}]}, "com/neovisionaries/ws/client/WebSocketListener.class": {"ver": 49, "acc": 1537, "nme": "com/neovisionaries/ws/client/WebSocketListener", "super": "java/lang/Object", "mthds": [{"nme": "onStateChanged", "acc": 1025, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketState;)V", "exs": ["java/lang/Exception"]}, {"nme": "onConnected", "acc": 1025, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Ljava/util/Map;)V", "sig": "(Lcom/neovisionaries/ws/client/WebSocket;Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;>;)V", "exs": ["java/lang/Exception"]}, {"nme": "onConnectError", "acc": 1025, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketException;)V", "exs": ["java/lang/Exception"]}, {"nme": "onDisconnected", "acc": 1025, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketFrame;Lcom/neovisionaries/ws/client/WebSocketFrame;Z)V", "exs": ["java/lang/Exception"]}, {"nme": "onFrame", "acc": 1025, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["java/lang/Exception"]}, {"nme": "onContinuationFrame", "acc": 1025, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["java/lang/Exception"]}, {"nme": "onTextFrame", "acc": 1025, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["java/lang/Exception"]}, {"nme": "onBinaryFrame", "acc": 1025, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["java/lang/Exception"]}, {"nme": "onCloseFrame", "acc": 1025, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["java/lang/Exception"]}, {"nme": "onPingFrame", "acc": 1025, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["java/lang/Exception"]}, {"nme": "onPongFrame", "acc": 1025, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["java/lang/Exception"]}, {"nme": "onTextMessage", "acc": 1025, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Ljava/lang/String;)V", "exs": ["java/lang/Exception"]}, {"nme": "onTextMessage", "acc": 1025, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;[B)V", "exs": ["java/lang/Exception"]}, {"nme": "onBinaryMessage", "acc": 1025, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;[B)V", "exs": ["java/lang/Exception"]}, {"nme": "onSendingFrame", "acc": 1025, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["java/lang/Exception"]}, {"nme": "onFrameSent", "acc": 1025, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["java/lang/Exception"]}, {"nme": "onFrameUnsent", "acc": 1025, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["java/lang/Exception"]}, {"nme": "onThreadCreated", "acc": 1025, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/ThreadType;Ljava/lang/Thread;)V", "exs": ["java/lang/Exception"]}, {"nme": "onThreadStarted", "acc": 1025, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/ThreadType;Ljava/lang/Thread;)V", "exs": ["java/lang/Exception"]}, {"nme": "onThreadStopping", "acc": 1025, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/ThreadType;Ljava/lang/Thread;)V", "exs": ["java/lang/Exception"]}, {"nme": "onError", "acc": 1025, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketException;)V", "exs": ["java/lang/Exception"]}, {"nme": "onFrameError", "acc": 1025, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketException;Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["java/lang/Exception"]}, {"nme": "onMessageError", "acc": 1025, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketException;Ljava/util/List;)V", "sig": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketException;Ljava/util/List<Lcom/neovisionaries/ws/client/WebSocketFrame;>;)V", "exs": ["java/lang/Exception"]}, {"nme": "onMessageDecompressionError", "acc": 1025, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketException;[B)V", "exs": ["java/lang/Exception"]}, {"nme": "onTextMessageError", "acc": 1025, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketException;[B)V", "exs": ["java/lang/Exception"]}, {"nme": "onSendError", "acc": 1025, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketException;Lcom/neovisionaries/ws/client/WebSocketFrame;)V", "exs": ["java/lang/Exception"]}, {"nme": "onUnexpectedError", "acc": 1025, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/WebSocketException;)V", "exs": ["java/lang/Exception"]}, {"nme": "handleCallbackError", "acc": 1025, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Ljava/lang/Throwable;)V", "exs": ["java/lang/Exception"]}, {"nme": "onSendingHandshake", "acc": 1025, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;Ljava/lang/String;Ljava/util/List;)V", "sig": "(Lcom/neovisionaries/ws/client/WebSocket;Ljava/lang/String;Ljava/util/List<[Ljava/lang/String;>;)V", "exs": ["java/lang/Exception"]}], "flds": []}, "com/neovisionaries/ws/client/Connectable.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/Connectable", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;)V"}, {"nme": "call", "acc": 1, "dsc": "()Lcom/neovisionaries/ws/client/WebSocket;", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "call", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 18, "nme": "mWebSocket", "dsc": "Lcom/neovisionaries/ws/client/WebSocket;"}]}, "com/neovisionaries/ws/client/HandshakeReader.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/Handshake<PERSON>eader", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;)V"}, {"nme": "readHandshake", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketInputStream;Ljava/lang/String;)Ljava/util/Map;", "sig": "(Lcom/neovisionaries/ws/client/WebSocketInputStream;Ljava/lang/String;)Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;>;", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "readStatusLine", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketInputStream;)Lcom/neovisionaries/ws/client/StatusLine;", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "readHttpHeaders", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketInputStream;)Ljava/util/Map;", "sig": "(Lcom/neovisionaries/ws/client/WebSocketInputStream;)Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;>;", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "parseHttpHeader", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;Ljava/lang/String;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;>;Ljava/lang/String;)V"}, {"nme": "validateStatusLine", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/StatusLine;Ljava/util/Map;Lcom/neovisionaries/ws/client/WebSocketInputStream;)V", "sig": "(Lcom/neovisionaries/ws/client/StatusLine;Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;>;Lcom/neovisionaries/ws/client/WebSocketInputStream;)V", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "readBody", "acc": 2, "dsc": "(Ljava/util/Map;Lcom/neovisionaries/ws/client/WebSocketInputStream;)[B", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;>;Lcom/neovisionaries/ws/client/WebSocketInputStream;)[B"}, {"nme": "getContentLength", "acc": 2, "dsc": "(Ljava/util/Map;)I", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;>;)I"}, {"nme": "validateUpgrade", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/StatusLine;Ljava/util/Map;)V", "sig": "(Lcom/neovisionaries/ws/client/StatusLine;Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;>;)V", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "validateConnection", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/StatusLine;Ljava/util/Map;)V", "sig": "(Lcom/neovisionaries/ws/client/StatusLine;Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;>;)V", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "validateAccept", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/StatusLine;Ljava/util/Map;Ljava/lang/String;)V", "sig": "(Lcom/neovisionaries/ws/client/StatusLine;Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;>;Ljava/lang/String;)V", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "validateExtensions", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/StatusLine;Ljava/util/Map;)V", "sig": "(Lcom/neovisionaries/ws/client/StatusLine;Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;>;)V", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "validateExtensionCombination", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/StatusLine;Ljava/util/Map;Ljava/util/List;)V", "sig": "(Lcom/neovisionaries/ws/client/StatusLine;Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;>;Ljava/util/List<Lcom/neovisionaries/ws/client/WebSocketExtension;>;)V", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}, {"nme": "validateProtocol", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/StatusLine;Ljava/util/Map;)V", "sig": "(Lcom/neovisionaries/ws/client/StatusLine;Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;>;)V", "exs": ["com/neovisionaries/ws/client/WebSocketException"]}], "flds": [{"acc": 26, "nme": "ACCEPT_MAGIC", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "258EAFA5-E914-47DA-95CA-C5AB0DC85B11"}, {"acc": 18, "nme": "mWebSocket", "dsc": "Lcom/neovisionaries/ws/client/WebSocket;"}]}, "com/neovisionaries/ws/client/PeriodicalFrameSender$1.class": {"ver": 49, "acc": 4128, "nme": "com/neovisionaries/ws/client/PeriodicalFrameSender$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "com/neovisionaries/ws/client/FixedLiteralLengthHuffman.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/FixedLiteralLengthHuffman", "super": "com/neovisionaries/ws/client/<PERSON><PERSON>man", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "buildCodeLensFromSym", "acc": 10, "dsc": "()[I"}, {"nme": "getInstance", "acc": 9, "dsc": "()Lcom/neovisionaries/ws/client/FixedLiteralLengthHuffman;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "INSTANCE", "dsc": "Lcom/neovisionaries/ws/client/FixedLiteralLengthHuffman;"}]}, "com/neovisionaries/ws/client/DeflateDecompressor.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/DeflateDecompressor", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "decompress", "acc": 9, "dsc": "(Lcom/neovisionaries/ws/client/ByteArray;Lcom/neovisionaries/ws/client/ByteArray;)V", "exs": ["com/neovisionaries/ws/client/FormatException"]}, {"nme": "decompress", "acc": 10, "dsc": "(Lcom/neovisionaries/ws/client/ByteArray;ILcom/neovisionaries/ws/client/ByteArray;)V", "exs": ["com/neovisionaries/ws/client/FormatException"]}, {"nme": "inflateBlock", "acc": 10, "dsc": "(Lcom/neovisionaries/ws/client/ByteArray;[ILcom/neovisionaries/ws/client/ByteArray;)Z", "exs": ["com/neovisionaries/ws/client/FormatException"]}, {"nme": "inflatePlainBlock", "acc": 10, "dsc": "(Lcom/neovisionaries/ws/client/ByteArray;[ILcom/neovisionaries/ws/client/ByteArray;)V"}, {"nme": "inflateFixedBlock", "acc": 10, "dsc": "(Lcom/neovisionaries/ws/client/ByteArray;[ILcom/neovisionaries/ws/client/ByteArray;)V", "exs": ["com/neovisionaries/ws/client/FormatException"]}, {"nme": "inflateDynamicBlock", "acc": 10, "dsc": "(Lcom/neovisionaries/ws/client/ByteArray;[ILcom/neovisionaries/ws/client/ByteArray;)V", "exs": ["com/neovisionaries/ws/client/FormatException"]}, {"nme": "inflateData", "acc": 10, "dsc": "(Lcom/neovisionaries/ws/client/ByteArray;[ILcom/neovisionaries/ws/client/ByteArray;Lcom/neovisionaries/ws/client/<PERSON><PERSON>man;Lcom/neovisionaries/ws/client/Huffman;)V", "exs": ["com/neovisionaries/ws/client/FormatException"]}, {"nme": "duplicate", "acc": 10, "dsc": "(IILcom/neovisionaries/ws/client/ByteArray;)V"}], "flds": []}, "com/neovisionaries/ws/client/DeflateUtil.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/DeflateUtil", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "readDynamicTables", "acc": 9, "dsc": "(Lcom/neovisionaries/ws/client/ByteArray;[I[Lcom/neovisionaries/ws/client/<PERSON><PERSON><PERSON>;)V", "exs": ["com/neovisionaries/ws/client/FormatException"]}, {"nme": "readCodeLengths", "acc": 10, "dsc": "(Lcom/neovisionaries/ws/client/ByteArray;[I[ILcom/neovisionaries/ws/client/<PERSON><PERSON><PERSON>;)V", "exs": ["com/neovisionaries/ws/client/FormatException"]}, {"nme": "codeLengthOrderToIndex", "acc": 10, "dsc": "(I)I"}, {"nme": "readLength", "acc": 9, "dsc": "(Lcom/neovisionaries/ws/client/ByteArray;[II)I", "exs": ["com/neovisionaries/ws/client/FormatException"]}, {"nme": "readDistance", "acc": 9, "dsc": "(Lcom/neovisionaries/ws/client/ByteArray;[ILcom/neovisionaries/ws/client/<PERSON><PERSON><PERSON>;)I", "exs": ["com/neovisionaries/ws/client/FormatException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 10, "nme": "INDICES_FROM_CODE_LENGTH_ORDER", "dsc": "[I"}]}, "com/neovisionaries/ws/client/StateManager$CloseInitiator.class": {"ver": 49, "acc": 16432, "nme": "com/neovisionaries/ws/client/StateManager$CloseInitiator", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lcom/neovisionaries/ws/client/StateManager$CloseInitiator;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/StateManager$CloseInitiator;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "NONE", "dsc": "Lcom/neovisionaries/ws/client/StateManager$CloseInitiator;"}, {"acc": 16409, "nme": "SERVER", "dsc": "Lcom/neovisionaries/ws/client/StateManager$CloseInitiator;"}, {"acc": 16409, "nme": "CLIENT", "dsc": "Lcom/neovisionaries/ws/client/StateManager$CloseInitiator;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lcom/neovisionaries/ws/client/StateManager$CloseInitiator;"}]}, "com/neovisionaries/ws/client/CounterPayloadGenerator.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/CounterPayloadGenerator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "generate", "acc": 1, "dsc": "()[B"}, {"nme": "increment", "acc": 2, "dsc": "()J"}], "flds": [{"acc": 2, "nme": "mCount", "dsc": "J"}]}, "com/neovisionaries/ws/client/ListenerManager.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/ListenerManager", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocket;)V"}, {"nme": "getListeners", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lcom/neovisionaries/ws/client/WebSocketListener;>;"}, {"nme": "addListener", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketListener;)V"}, {"nme": "addListeners", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Lcom/neovisionaries/ws/client/WebSocketListener;>;)V"}, {"nme": "removeListener", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketListener;)V"}, {"nme": "removeListeners", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Lcom/neovisionaries/ws/client/WebSocketListener;>;)V"}, {"nme": "clearListeners", "acc": 1, "dsc": "()V"}, {"nme": "getSynchronizedListeners", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lcom/neovisionaries/ws/client/WebSocketListener;>;"}, {"nme": "callOnStateChanged", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketState;)V"}, {"nme": "callOnConnected", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;>;)V"}, {"nme": "callOnConnectError", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketException;)V"}, {"nme": "callOnDisconnected", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;Lcom/neovisionaries/ws/client/WebSocketFrame;Z)V"}, {"nme": "callOnFrame", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V"}, {"nme": "callOnContinuationFrame", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V"}, {"nme": "callOnTextFrame", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V"}, {"nme": "callOnBinaryFrame", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V"}, {"nme": "callOnCloseFrame", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V"}, {"nme": "callOnPingFrame", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V"}, {"nme": "callOnPongFrame", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V"}, {"nme": "callOnTextMessage", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "callOnTextMessage", "acc": 1, "dsc": "([B)V"}, {"nme": "callOnBinaryMessage", "acc": 1, "dsc": "([B)V"}, {"nme": "callOnSendingFrame", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V"}, {"nme": "callOnFrameSent", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V"}, {"nme": "callOnFrameUnsent", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFrame;)V"}, {"nme": "callOnThreadCreated", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/ThreadType;Ljava/lang/Thread;)V"}, {"nme": "callOnThreadStarted", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/ThreadType;Ljava/lang/Thread;)V"}, {"nme": "callOnThreadStopping", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/ThreadType;Ljava/lang/Thread;)V"}, {"nme": "callOnError", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketException;)V"}, {"nme": "callOnFrameError", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketException;Lcom/neovisionaries/ws/client/WebSocketFrame;)V"}, {"nme": "callOnMessageError", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketException;Ljava/util/List;)V", "sig": "(Lcom/neovisionaries/ws/client/WebSocketException;Ljava/util/List<Lcom/neovisionaries/ws/client/WebSocketFrame;>;)V"}, {"nme": "callOnMessageDecompressionError", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketException;[B)V"}, {"nme": "callOnTextMessageError", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketException;[B)V"}, {"nme": "callOnSendError", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketException;Lcom/neovisionaries/ws/client/WebSocketFrame;)V"}, {"nme": "callOnUnexpectedError", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketException;)V"}, {"nme": "callHandleCallbackError", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketListener;Ljava/lang/Throwable;)V"}, {"nme": "callOnSendingHandshake", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(L<PERSON><PERSON>/lang/String;Ljava/util/List<[Ljava/lang/String;>;)V"}], "flds": [{"acc": 18, "nme": "mWebSocket", "dsc": "Lcom/neovisionaries/ws/client/WebSocket;"}, {"acc": 18, "nme": "mListeners", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lcom/neovisionaries/ws/client/WebSocketListener;>;"}, {"acc": 2, "nme": "mSyncNeeded", "dsc": "Z"}, {"acc": 2, "nme": "mCopiedListeners", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lcom/neovisionaries/ws/client/WebSocketListener;>;"}]}, "com/neovisionaries/ws/client/ReadingThread$1.class": {"ver": 49, "acc": 4128, "nme": "com/neovisionaries/ws/client/ReadingThread$1", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$com$neovisionaries$ws$client$WebSocketError", "dsc": "[I"}]}, "com/neovisionaries/ws/client/WebSocketFactory.class": {"ver": 49, "acc": 33, "nme": "com/neovisionaries/ws/client/WebSocketFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFactory;)V"}, {"nme": "getSocketFactory", "acc": 1, "dsc": "()Ljavax/net/SocketFactory;"}, {"nme": "setSocketFactory", "acc": 1, "dsc": "(Ljavax/net/SocketFactory;)Lcom/neovisionaries/ws/client/WebSocketFactory;"}, {"nme": "getSSLSocketFactory", "acc": 1, "dsc": "()Ljavax/net/ssl/SSLSocketFactory;"}, {"nme": "setSSLSocketFactory", "acc": 1, "dsc": "(Ljavax/net/ssl/SSLSocketFactory;)Lcom/neovisionaries/ws/client/WebSocketFactory;"}, {"nme": "getSSLContext", "acc": 1, "dsc": "()Ljavax/net/ssl/SSLContext;"}, {"nme": "setSSLContext", "acc": 1, "dsc": "(Ljavax/net/ssl/SSLContext;)Lcom/neovisionaries/ws/client/WebSocketFactory;"}, {"nme": "getProxySettings", "acc": 1, "dsc": "()Lcom/neovisionaries/ws/client/ProxySettings;"}, {"nme": "getConnectionTimeout", "acc": 1, "dsc": "()I"}, {"nme": "setConnectionTimeout", "acc": 1, "dsc": "(I)Lcom/neovisionaries/ws/client/WebSocketFactory;"}, {"nme": "getSocketTimeout", "acc": 1, "dsc": "()I"}, {"nme": "setSocketTimeout", "acc": 1, "dsc": "(I)Lcom/neovisionaries/ws/client/WebSocketFactory;"}, {"nme": "getDualStackMode", "acc": 1, "dsc": "()Lcom/neovisionaries/ws/client/DualStackMode;"}, {"nme": "setDualStackMode", "acc": 1, "dsc": "(Lcom/neovisionaries/ws/client/DualStackMode;)Lcom/neovisionaries/ws/client/WebSocketFactory;"}, {"nme": "getDualStackFallbackDelay", "acc": 1, "dsc": "()I"}, {"nme": "setDualStackFallbackDelay", "acc": 1, "dsc": "(I)Lcom/neovisionaries/ws/client/WebSocketFactory;"}, {"nme": "getVerifyHostname", "acc": 1, "dsc": "()Z"}, {"nme": "setVerifyHostname", "acc": 1, "dsc": "(Z)Lcom/neovisionaries/ws/client/WebSocketFactory;"}, {"nme": "getServerNames", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setServerNames", "acc": 1, "dsc": "([Lja<PERSON>/lang/String;)Lcom/neovisionaries/ws/client/WebSocketFactory;"}, {"nme": "setServerName", "acc": 1, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/WebSocketFactory;"}, {"nme": "createSocket", "acc": 1, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/WebSocket;", "exs": ["java/io/IOException"]}, {"nme": "createSocket", "acc": 1, "dsc": "(<PERSON>ja<PERSON>/lang/String;I)Lcom/neovisionaries/ws/client/WebSocket;", "exs": ["java/io/IOException"]}, {"nme": "createSocket", "acc": 1, "dsc": "(Ljava/net/URL;)Lcom/neovisionaries/ws/client/WebSocket;", "exs": ["java/io/IOException"]}, {"nme": "createSocket", "acc": 1, "dsc": "(Ljava/net/URL;I)Lcom/neovisionaries/ws/client/WebSocket;", "exs": ["java/io/IOException"]}, {"nme": "createSocket", "acc": 1, "dsc": "(Ljava/net/URI;)Lcom/neovisionaries/ws/client/WebSocket;", "exs": ["java/io/IOException"]}, {"nme": "createSocket", "acc": 1, "dsc": "(Ljava/net/URI;I)Lcom/neovisionaries/ws/client/WebSocket;", "exs": ["java/io/IOException"]}, {"nme": "createSocket", "acc": 2, "dsc": "(Lja<PERSON>/lang/String;<PERSON>java/lang/String;Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;I)Lcom/neovisionaries/ws/client/WebSocket;", "exs": ["java/io/IOException"]}, {"nme": "isSecureConnectionRequired", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "determinePath", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "createRawSocket", "acc": 2, "dsc": "(Ljava/lang/String;IZI)Lcom/neovisionaries/ws/client/SocketConnector;", "exs": ["java/io/IOException"]}, {"nme": "createProxiedRawSocket", "acc": 2, "dsc": "(Ljava/lang/String;IZI)Lcom/neovisionaries/ws/client/SocketConnector;"}, {"nme": "createDirectRawSocket", "acc": 2, "dsc": "(Ljava/lang/String;IZI)Lcom/neovisionaries/ws/client/SocketConnector;"}, {"nme": "determinePort", "acc": 10, "dsc": "(IZ)I"}, {"nme": "createWebSocket", "acc": 2, "dsc": "(ZLjava/lang/String;Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;Lcom/neovisionaries/ws/client/SocketConnector;)Lcom/neovisionaries/ws/client/WebSocket;"}], "flds": [{"acc": 18, "nme": "mSocketFactorySettings", "dsc": "Lcom/neovisionaries/ws/client/SocketFactorySettings;"}, {"acc": 18, "nme": "mProxySettings", "dsc": "Lcom/neovisionaries/ws/client/ProxySettings;"}, {"acc": 2, "nme": "mConnectionTimeout", "dsc": "I"}, {"acc": 2, "nme": "mSocketTimeout", "dsc": "I"}, {"acc": 2, "nme": "mDualStackMode", "dsc": "Lcom/neovisionaries/ws/client/DualStackMode;"}, {"acc": 2, "nme": "mDualStackFallbackDelay", "dsc": "I"}, {"acc": 2, "nme": "mVerifyHostname", "dsc": "Z"}, {"acc": 2, "nme": "mServerNames", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}]}, "com/neovisionaries/ws/client/Token.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/Token", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isSeparator", "acc": 9, "dsc": "(C)Z"}, {"nme": "unquote", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "unescape", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": []}, "com/neovisionaries/ws/client/DistinguishedNameParser.class": {"ver": 49, "acc": 48, "nme": "com/neovisionaries/ws/client/DistinguishedNameParser", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/security/auth/x500/X500Principal;)V"}, {"nme": "nextAT", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "quotedAV", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "hexAV", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "escapedAV", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getEscaped", "acc": 2, "dsc": "()C"}, {"nme": "getUTF8", "acc": 2, "dsc": "()C"}, {"nme": "getByte", "acc": 2, "dsc": "(I)I"}, {"nme": "findMostSpecific", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": [{"acc": 18, "nme": "dn", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "length", "dsc": "I"}, {"acc": 2, "nme": "pos", "dsc": "I"}, {"acc": 2, "nme": "beg", "dsc": "I"}, {"acc": 2, "nme": "end", "dsc": "I"}, {"acc": 2, "nme": "cur", "dsc": "I"}, {"acc": 2, "nme": "chars", "dsc": "[C"}]}, "com/neovisionaries/ws/client/ProxySettings.class": {"ver": 49, "acc": 33, "nme": "com/neovisionaries/ws/client/ProxySettings", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFactory;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(Lcom/neovisionaries/ws/client/WebSocketFactory;Lcom/neovisionaries/ws/client/ProxySettings;)V"}, {"nme": "getWebSocketFactory", "acc": 1, "dsc": "()Lcom/neovisionaries/ws/client/WebSocketFactory;"}, {"nme": "reset", "acc": 1, "dsc": "()Lcom/neovisionaries/ws/client/ProxySettings;"}, {"nme": "isSecure", "acc": 1, "dsc": "()Z"}, {"nme": "setSecure", "acc": 1, "dsc": "(Z)Lcom/neovisionaries/ws/client/ProxySettings;"}, {"nme": "getHost", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setHost", "acc": 1, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/ProxySettings;"}, {"nme": "getPort", "acc": 1, "dsc": "()I"}, {"nme": "setPort", "acc": 1, "dsc": "(I)Lcom/neovisionaries/ws/client/ProxySettings;"}, {"nme": "getId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setId", "acc": 1, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/ProxySettings;"}, {"nme": "getPassword", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setPassword", "acc": 1, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/ProxySettings;"}, {"nme": "setCredentials", "acc": 1, "dsc": "(Ljava/lang/String;Ljava/lang/String;)Lcom/neovisionaries/ws/client/ProxySettings;"}, {"nme": "setServer", "acc": 1, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/ProxySettings;"}, {"nme": "setServer", "acc": 1, "dsc": "(Ljava/net/URL;)Lcom/neovisionaries/ws/client/ProxySettings;"}, {"nme": "setServer", "acc": 1, "dsc": "(Ljava/net/URI;)Lcom/neovisionaries/ws/client/ProxySettings;"}, {"nme": "setServer", "acc": 2, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;I)Lcom/neovisionaries/ws/client/ProxySettings;"}, {"nme": "setByScheme", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setByUserInfo", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getHeaders", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;>;"}, {"nme": "addHeader", "acc": 1, "dsc": "(Ljava/lang/String;Ljava/lang/String;)Lcom/neovisionaries/ws/client/ProxySettings;"}, {"nme": "getSocketFactory", "acc": 1, "dsc": "()Ljavax/net/SocketFactory;"}, {"nme": "setSocketFactory", "acc": 1, "dsc": "(Ljavax/net/SocketFactory;)Lcom/neovisionaries/ws/client/ProxySettings;"}, {"nme": "getSSLSocketFactory", "acc": 1, "dsc": "()Ljavax/net/ssl/SSLSocketFactory;"}, {"nme": "setSSLSocketFactory", "acc": 1, "dsc": "(Ljavax/net/ssl/SSLSocketFactory;)Lcom/neovisionaries/ws/client/ProxySettings;"}, {"nme": "getSSLContext", "acc": 1, "dsc": "()Ljavax/net/ssl/SSLContext;"}, {"nme": "setSSLContext", "acc": 1, "dsc": "(Ljavax/net/ssl/SSLContext;)Lcom/neovisionaries/ws/client/ProxySettings;"}, {"nme": "selectSocketFactory", "acc": 0, "dsc": "()Ljavax/net/SocketFactory;"}, {"nme": "getServerNames", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setServerNames", "acc": 1, "dsc": "([Lja<PERSON>/lang/String;)Lcom/neovisionaries/ws/client/ProxySettings;"}, {"nme": "setServerName", "acc": 1, "dsc": "(Ljava/lang/String;)Lcom/neovisionaries/ws/client/ProxySettings;"}], "flds": [{"acc": 18, "nme": "mWebSocketFactory", "dsc": "Lcom/neovisionaries/ws/client/WebSocketFactory;"}, {"acc": 18, "nme": "mHeaders", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;>;"}, {"acc": 18, "nme": "mSocketFactorySettings", "dsc": "Lcom/neovisionaries/ws/client/SocketFactorySettings;"}, {"acc": 2, "nme": "mSecure", "dsc": "Z"}, {"acc": 2, "nme": "mHost", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "mPort", "dsc": "I"}, {"acc": 2, "nme": "mId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "mPassword", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "mServerNames", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}]}, "com/neovisionaries/ws/client/PayloadGenerator.class": {"ver": 49, "acc": 1537, "nme": "com/neovisionaries/ws/client/PayloadGenerator", "super": "java/lang/Object", "mthds": [{"nme": "generate", "acc": 1025, "dsc": "()[B"}], "flds": []}, "com/neovisionaries/ws/client/ProxyHandshaker.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/ProxyHandshaker", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/lang/String;ILcom/neovisionaries/ws/client/ProxySettings;)V"}, {"nme": "perform", "acc": 1, "dsc": "(Ljava/net/Socket;)V", "exs": ["java/io/IOException"]}, {"nme": "sendRequest", "acc": 2, "dsc": "(Ljava/net/Socket;)V", "exs": ["java/io/IOException"]}, {"nme": "buildRequest", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "addHeaders", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;)V"}, {"nme": "addProxyAuthorization", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;)V"}, {"nme": "receiveResponse", "acc": 2, "dsc": "(Ljava/net/Socket;)V", "exs": ["java/io/IOException"]}, {"nme": "readStatusLine", "acc": 2, "dsc": "(<PERSON><PERSON>va/io/InputStream;)V", "exs": ["java/io/IOException"]}, {"nme": "skipHeaders", "acc": 2, "dsc": "(<PERSON><PERSON>va/io/InputStream;)V", "exs": ["java/io/IOException"]}, {"nme": "getProxiedHostname", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 26, "nme": "RN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "\r\n"}, {"acc": 18, "nme": "mHost", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "mPort", "dsc": "I"}, {"acc": 18, "nme": "mSettings", "dsc": "Lcom/neovisionaries/ws/client/ProxySettings;"}]}, "com/neovisionaries/ws/client/SocketInitiator$SocketFuture.class": {"ver": 49, "acc": 32, "nme": "com/neovisionaries/ws/client/SocketInitiator$SocketFuture", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lcom/neovisionaries/ws/client/SocketInitiator;)V"}, {"nme": "hasSocket", "acc": 32, "dsc": "()Z"}, {"nme": "setSocket", "acc": 32, "dsc": "(Lcom/neovisionaries/ws/client/SocketInitiator$SocketRacer;Ljava/net/Socket;)V"}, {"nme": "setException", "acc": 32, "dsc": "(<PERSON><PERSON><PERSON>/lang/Exception;)V"}, {"nme": "await", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Ljava/net/Socket;", "sig": "(Ljava/util/List<Lcom/neovisionaries/ws/client/SocketInitiator$SocketRacer;>;)Ljava/net/Socket;", "exs": ["java/lang/Exception"]}, {"nme": "<init>", "acc": 4096, "dsc": "(Lcom/neovisionaries/ws/client/SocketInitiator;Lcom/neovisionaries/ws/client/SocketInitiator$1;)V"}], "flds": [{"acc": 2, "nme": "mLatch", "dsc": "<PERSON><PERSON>va/util/concurrent/CountDownLatch;"}, {"acc": 2, "nme": "mRacers", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lcom/neovisionaries/ws/client/SocketInitiator$SocketRacer;>;"}, {"acc": 2, "nme": "mSocket", "dsc": "Ljava/net/Socket;"}, {"acc": 2, "nme": "mException", "dsc": "<PERSON><PERSON><PERSON>/lang/Exception;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lcom/neovisionaries/ws/client/SocketInitiator;"}]}, "com/neovisionaries/ws/client/WebSocketThread.class": {"ver": 49, "acc": 1056, "nme": "com/neovisionaries/ws/client/WebSocketThread", "super": "java/lang/Thread", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/lang/String;Lcom/neovisionaries/ws/client/WebSocket;Lcom/neovisionaries/ws/client/ThreadType;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}, {"nme": "callOnThreadCreated", "acc": 1, "dsc": "()V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1028, "dsc": "()V"}], "flds": [{"acc": 20, "nme": "mWebSocket", "dsc": "Lcom/neovisionaries/ws/client/WebSocket;"}, {"acc": 18, "nme": "mThreadType", "dsc": "Lcom/neovisionaries/ws/client/ThreadType;"}]}}}}