{"md5": "e07e1ca647a920dbecd27421b7aee494", "sha2": "8b691084f45895751f2a932a4c0b54d7ee068322", "sha256": "8f71fe165f4bfaaec5ed2d9b3a6ae32cfa953d980667fd5e4bf6711fcc9f653e", "contents": {"classes": {"net/brcdev/shopgui/spawner/SpawnerManager.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/spawner/SpawnerManager", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getSpawnerItem", "acc": 1, "dsc": "(Lorg/bukkit/entity/EntityType;)Lorg/bukkit/inventory/ItemStack;"}, {"nme": "getEntityType", "acc": 1, "dsc": "(Lorg/bukkit/inventory/ItemStack;)Lorg/bukkit/entity/EntityType;"}, {"nme": "registerExternalSpawnerProvider", "acc": 1, "dsc": "(Lnet/brcdev/shopgui/spawner/external/provider/ExternalSpawnerProvider;)V"}], "flds": []}, "net/brcdev/shopgui/shop/ShopTransactionResult.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/shop/ShopTransactionResult", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getShopAction", "acc": 1, "dsc": "()Lnet/brcdev/shopgui/shop/ShopManager$ShopAction;"}, {"nme": "getResult", "acc": 1, "dsc": "()Lnet/brcdev/shopgui/shop/ShopTransactionResult$ShopTransactionResultType;"}, {"nme": "getShopItem", "acc": 1, "dsc": "()Lnet/brcdev/shopgui/shop/item/ShopItem;"}, {"nme": "getPlayer", "acc": 1, "dsc": "()Lorg/bukkit/entity/Player;"}, {"nme": "getAmount", "acc": 1, "dsc": "()I"}, {"nme": "getPrice", "acc": 1, "dsc": "()D"}], "flds": []}, "net/brcdev/shopgui/modifier/PriceModifierType.class": {"ver": 52, "acc": 16433, "nme": "net/brcdev/shopgui/modifier/PriceModifierType", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lnet/brcdev/shopgui/modifier/PriceModifierType;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/brcdev/shopgui/modifier/PriceModifierType;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "BOTH", "dsc": "Lnet/brcdev/shopgui/modifier/PriceModifierType;"}, {"acc": 16409, "nme": "COMMAND", "dsc": "Lnet/brcdev/shopgui/modifier/PriceModifierType;"}, {"acc": 16409, "nme": "PERMISSION", "dsc": "Lnet/brcdev/shopgui/modifier/PriceModifierType;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lnet/brcdev/shopgui/modifier/PriceModifierType;"}]}, "net/brcdev/shopgui/player/PlayerManager.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/player/PlayerManager", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "isPlayerLoaded", "acc": 1, "dsc": "(Lorg/bukkit/OfflinePlayer;)Z"}, {"nme": "getPlayerData", "acc": 1, "dsc": "(Lorg/bukkit/OfflinePlayer;)Lnet/brcdev/shopgui/player/PlayerData;", "exs": ["net/brcdev/shopgui/exception/player/PlayerDataNotLoadedException"]}], "flds": []}, "net/brcdev/shopgui/ShopGuiPlugin.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/ShopGuiPlugin", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getConfigMain", "acc": 1, "dsc": "()Lnet/brcdev/shopgui/core/BConfig;"}, {"nme": "getConfigLang", "acc": 1, "dsc": "()Lnet/brcdev/shopgui/core/BConfig;"}, {"nme": "getConfigPriceModifiers", "acc": 1, "dsc": "()Lnet/brcdev/shopgui/core/BConfig;"}, {"nme": "getConfigShops", "acc": 1, "dsc": "()Lnet/brcdev/shopgui/core/BConfig;"}, {"nme": "getDataManager", "acc": 1, "dsc": "()Lnet/brcdev/shopgui/database/DataManager;"}, {"nme": "getEconomyManager", "acc": 1, "dsc": "()Lnet/brcdev/shopgui/economy/EconomyManager;"}, {"nme": "getItemManager", "acc": 1, "dsc": "()Lnet/brcdev/shopgui/item/ItemManager;"}, {"nme": "getPlayerManager", "acc": 1, "dsc": "()Lnet/brcdev/shopgui/player/PlayerManager;"}, {"nme": "getPriceModifierManager", "acc": 1, "dsc": "()Lnet/brcdev/shopgui/modifier/PriceModifierManager;"}, {"nme": "getShopManager", "acc": 1, "dsc": "()Lnet/brcdev/shopgui/shop/ShopManager;"}, {"nme": "getSpawnerManager", "acc": 1, "dsc": "()Lnet/brcdev/shopgui/spawner/SpawnerManager;"}], "flds": []}, "net/brcdev/shopgui/event/ShopPreTransactionEvent.class": {"ver": 52, "acc": 1057, "nme": "net/brcdev/shopgui/event/ShopPreTransactionEvent", "super": "org/bukkit/event/Event", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getHandlerList", "acc": 9, "dsc": "()Lorg/bukkit/event/HandlerList;"}, {"nme": "isCancelled", "acc": 1025, "dsc": "()Z"}, {"nme": "setCancelled", "acc": 1025, "dsc": "(Z)V"}, {"nme": "getHandlers", "acc": 1, "dsc": "()Lorg/bukkit/event/HandlerList;"}, {"nme": "getShopItem", "acc": 1025, "dsc": "()Lnet/brcdev/shopgui/shop/item/ShopItem;"}, {"nme": "getShopAction", "acc": 1025, "dsc": "()Lnet/brcdev/shopgui/shop/ShopManager$ShopAction;"}, {"nme": "getPlayer", "acc": 1025, "dsc": "()Lorg/bukkit/entity/Player;"}, {"nme": "getAmount", "acc": 1025, "dsc": "()I"}, {"nme": "setAmount", "acc": 1025, "dsc": "(I)V"}, {"nme": "getPrice", "acc": 1025, "dsc": "()D"}, {"nme": "setPrice", "acc": 1025, "dsc": "(D)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "handlers", "dsc": "Lorg/bukkit/event/HandlerList;"}]}, "net/brcdev/shopgui/modifier/PriceModifierEntry.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/modifier/PriceModifierEntry", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "get", "acc": 1, "dsc": "(Lnet/brcdev/shopgui/modifier/PriceModifierActionType;)D"}, {"nme": "set", "acc": 1, "dsc": "(Lnet/brcdev/shopgui/modifier/PriceModifierActionType;D)V"}, {"nme": "reset", "acc": 1, "dsc": "(Lnet/brcdev/shopgui/modifier/PriceModifierActionType;)V"}, {"nme": "isPrimary", "acc": 1, "dsc": "()Z"}, {"nme": "formatPlaceholders", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": []}, "net/brcdev/shopgui/event/ShopsPostLoadEvent.class": {"ver": 52, "acc": 1057, "nme": "net/brcdev/shopgui/event/ShopsPostLoadEvent", "super": "org/bukkit/event/Event", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getHandlerList", "acc": 9, "dsc": "()Lorg/bukkit/event/HandlerList;"}, {"nme": "getHandlers", "acc": 1, "dsc": "()Lorg/bukkit/event/HandlerList;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "handlers", "dsc": "Lorg/bukkit/event/HandlerList;"}]}, "net/brcdev/shopgui/modifier/PriceModifierActionType.class": {"ver": 52, "acc": 16433, "nme": "net/brcdev/shopgui/modifier/PriceModifierActionType", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lnet/brcdev/shopgui/modifier/PriceModifierActionType;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/brcdev/shopgui/modifier/PriceModifierActionType;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "getType", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/brcdev/shopgui/modifier/PriceModifierActionType;"}, {"nme": "getTranslation", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "BUY", "dsc": "Lnet/brcdev/shopgui/modifier/PriceModifierActionType;"}, {"acc": 16409, "nme": "SELL", "dsc": "Lnet/brcdev/shopgui/modifier/PriceModifierActionType;"}, {"acc": 16409, "nme": "BOTH", "dsc": "Lnet/brcdev/shopgui/modifier/PriceModifierActionType;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lnet/brcdev/shopgui/modifier/PriceModifierActionType;"}]}, "net/brcdev/shopgui/exception/UnsupportedMinecraftVersionException.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/exception/UnsupportedMinecraftVersionException", "super": "java/lang/RuntimeException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "net/brcdev/shopgui/modifier/command/CommandPriceModifiers.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/modifier/command/CommandPriceModifiers", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getGlobalModifier", "acc": 1, "dsc": "()Lnet/brcdev/shopgui/modifier/PriceModifierEntry;"}, {"nme": "getShopModifiers", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Lnet/brcdev/shopgui/modifier/PriceModifierEntry;>;"}, {"nme": "getItemModifiers", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Lnet/brcdev/shopgui/modifier/PriceModifierEntry;>;>;"}, {"nme": "getItemModifier", "acc": 1, "dsc": "(Lnet/brcdev/shopgui/shop/item/ShopItem;Lnet/brcdev/shopgui/modifier/PriceModifierActionType;)D"}, {"nme": "setItemModifier", "acc": 1, "dsc": "(Lnet/brcdev/shopgui/shop/item/ShopItem;Lnet/brcdev/shopgui/modifier/PriceModifierActionType;D)V"}, {"nme": "resetItemModifier", "acc": 1, "dsc": "(Lnet/brcdev/shopgui/shop/item/ShopItem;Lnet/brcdev/shopgui/modifier/PriceModifierActionType;)V"}, {"nme": "getShopModifier", "acc": 1, "dsc": "(Lnet/brcdev/shopgui/shop/Shop;Lnet/brcdev/shopgui/modifier/PriceModifierActionType;)D"}, {"nme": "setShopModifier", "acc": 1, "dsc": "(Lnet/brcdev/shopgui/shop/Shop;Lnet/brcdev/shopgui/modifier/PriceModifierActionType;D)V"}, {"nme": "resetShopModifier", "acc": 1, "dsc": "(Lnet/brcdev/shopgui/shop/Shop;Lnet/brcdev/shopgui/modifier/PriceModifierActionType;)V"}, {"nme": "getGlobalModifier", "acc": 1, "dsc": "(Lnet/brcdev/shopgui/modifier/PriceModifierActionType;)D"}, {"nme": "setGlobalModifier", "acc": 1, "dsc": "(Lnet/brcdev/shopgui/modifier/PriceModifierActionType;D)V"}, {"nme": "resetGlobalModifier", "acc": 1, "dsc": "(Lnet/brcdev/shopgui/modifier/PriceModifierActionType;)V"}], "flds": []}, "net/brcdev/shopgui/exception/item/ItemLoadException.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/exception/item/ItemLoadException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "net/brcdev/shopgui/exception/shop/ShopsNotLoadedException.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/exception/shop/ShopsNotLoadedException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "net/brcdev/shopgui/modifier/PriceModifierManager.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/modifier/PriceModifierManager", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getPriceModifier", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Lnet/brcdev/shopgui/shop/item/ShopItem;Lnet/brcdev/shopgui/modifier/PriceModifierActionType;)Lnet/brcdev/shopgui/modifier/PriceModifier;", "exs": ["net/brcdev/shopgui/exception/player/PlayerDataNotLoadedException"]}, {"nme": "setPriceModifier", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Lnet/brcdev/shopgui/shop/item/ShopItem;Lnet/brcdev/shopgui/modifier/PriceModifierActionType;D)V", "exs": ["net/brcdev/shopgui/exception/player/PlayerDataNotLoadedException"]}, {"nme": "resetPriceModifier", "acc": 1, "dsc": "(Lorg/bukkit/OfflinePlayer;Lnet/brcdev/shopgui/shop/item/ShopItem;Lnet/brcdev/shopgui/modifier/PriceModifierActionType;)V", "exs": ["net/brcdev/shopgui/exception/player/PlayerDataNotLoadedException"]}, {"nme": "getPriceModifier", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Lnet/brcdev/shopgui/shop/Shop;Lnet/brcdev/shopgui/modifier/PriceModifierActionType;)Lnet/brcdev/shopgui/modifier/PriceModifier;", "exs": ["net/brcdev/shopgui/exception/player/PlayerDataNotLoadedException"]}, {"nme": "setPriceModifier", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Lnet/brcdev/shopgui/shop/Shop;Lnet/brcdev/shopgui/modifier/PriceModifierActionType;D)V", "exs": ["net/brcdev/shopgui/exception/player/PlayerDataNotLoadedException"]}, {"nme": "resetPriceModifier", "acc": 1, "dsc": "(Lorg/bukkit/OfflinePlayer;Lnet/brcdev/shopgui/shop/Shop;Lnet/brcdev/shopgui/modifier/PriceModifierActionType;)V", "exs": ["net/brcdev/shopgui/exception/player/PlayerDataNotLoadedException"]}, {"nme": "getPriceModifier", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Lnet/brcdev/shopgui/modifier/PriceModifierActionType;)Lnet/brcdev/shopgui/modifier/PriceModifier;", "exs": ["net/brcdev/shopgui/exception/player/PlayerDataNotLoadedException"]}, {"nme": "setPriceModifier", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Lnet/brcdev/shopgui/modifier/PriceModifierActionType;D)V", "exs": ["net/brcdev/shopgui/exception/player/PlayerDataNotLoadedException"]}, {"nme": "resetPriceModifier", "acc": 1, "dsc": "(Lorg/bukkit/OfflinePlayer;Lnet/brcdev/shopgui/modifier/PriceModifierActionType;)V", "exs": ["net/brcdev/shopgui/exception/player/PlayerDataNotLoadedException"]}], "flds": []}, "net/brcdev/shopgui/exception/item/InvalidDyeColorException.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/exception/item/InvalidDyeColorException", "super": "net/brcdev/shopgui/exception/item/ItemLoadException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "net/brcdev/shopgui/database/DataManager.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/database/DataManager", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "net/brcdev/shopgui/exception/item/spawner/InvalidSpawnerEntityTypeException.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/exception/item/spawner/InvalidSpawnerEntityTypeException", "super": "net/brcdev/shopgui/exception/item/ItemLoadException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "net/brcdev/shopgui/player/PlayerData.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/player/PlayerData", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getId", "acc": 1, "dsc": "()Ljava/util/UUID;"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getPriceModifiers", "acc": 1, "dsc": "()Lnet/brcdev/shopgui/modifier/command/CommandPriceModifiers;"}, {"nme": "getLastGuiClick", "acc": 1, "dsc": "()J"}, {"nme": "getLastAmountSelectionGuiClick", "acc": 1, "dsc": "()J"}, {"nme": "getOpenGui", "acc": 1, "dsc": "()Lnet/brcdev/shopgui/gui/gui/OpenGui;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Z"}, {"nme": "isSwitchingGui", "acc": 1, "dsc": "()Z"}], "flds": []}, "net/brcdev/shopgui/event/ShopGUIPlusPostEnableEvent.class": {"ver": 52, "acc": 1057, "nme": "net/brcdev/shopgui/event/ShopGUIPlusPostEnableEvent", "super": "org/bukkit/event/Event", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getHandlerList", "acc": 9, "dsc": "()Lorg/bukkit/event/HandlerList;"}, {"nme": "getHandlers", "acc": 1, "dsc": "()Lorg/bukkit/event/HandlerList;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "handlers", "dsc": "Lorg/bukkit/event/HandlerList;"}]}, "net/brcdev/shopgui/shop/Shop.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/shop/Shop", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/lang/String;Ljava/lang/String;Ljava/util/Map;IZZLnet/brcdev/shopgui/economy/EconomyType;Lorg/bukkit/inventory/ItemStack;Ljava/util/List;Ljava/util/List;Ljava/util/List;)V", "sig": "(Ljava/lang/String;Ljava/lang/String;Ljava/util/Map<Ljava/lang/Integer;Ljava/lang/String;>;IZZLnet/brcdev/shopgui/economy/EconomyType;Lorg/bukkit/inventory/ItemStack;Ljava/util/List<Ljava/lang/String;>;Ljava/util/List<Ljava/lang/String;>;Ljava/util/List<Lnet/brcdev/shopgui/shop/item/ShopItem;>;)V"}, {"nme": "getId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setName", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getNamePerPage", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/Integer;Ljava/lang/String;>;"}, {"nme": "setNamePerPage", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(<PERSON><PERSON><PERSON>/util/Map<Ljava/lang/Integer;Ljava/lang/String;>;)V"}, {"nme": "getName", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getSize", "acc": 1, "dsc": "()I"}, {"nme": "setSize", "acc": 1, "dsc": "(I)V"}, {"nme": "isEnablePerItemPermissions", "acc": 1, "dsc": "()Z"}, {"nme": "setEnablePerItemPermissions", "acc": 1, "dsc": "(Z)V"}, {"nme": "isDenyDirectAccess", "acc": 1, "dsc": "()Z"}, {"nme": "setDenyDirectAccess", "acc": 1, "dsc": "(Z)V"}, {"nme": "getEconomyType", "acc": 1, "dsc": "()Lnet/brcdev/shopgui/economy/EconomyType;"}, {"nme": "setEconomyType", "acc": 1, "dsc": "(Lnet/brcdev/shopgui/economy/EconomyType;)V"}, {"nme": "getFillItem", "acc": 1, "dsc": "()Lorg/bukkit/inventory/ItemStack;"}, {"nme": "setFillItem", "acc": 1, "dsc": "(Lorg/bukkit/inventory/ItemStack;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/String;>;)V"}, {"nme": "getWorldsBlacklist", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "setWorldsBlacklist", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/String;>;)V"}, {"nme": "getShopItems", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lnet/brcdev/shopgui/shop/item/ShopItem;>;"}, {"nme": "setShopItems", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Lnet/brcdev/shopgui/shop/item/ShopItem;>;)V"}, {"nme": "getShopItem", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;)Lnet/brcdev/shopgui/shop/item/ShopItem;"}, {"nme": "getShopItem", "acc": 1, "dsc": "(II)Lnet/brcdev/shopgui/shop/item/ShopItem;"}, {"nme": "getButtonGoBack", "acc": 1, "dsc": "()Lnet/brcdev/shopgui/gui/element/button/GuiButton;"}, {"nme": "setButtonGoBack", "acc": 1, "dsc": "(Lnet/brcdev/shopgui/gui/element/button/GuiButton;)V"}, {"nme": "getButtonPreviousPage", "acc": 1, "dsc": "()Lnet/brcdev/shopgui/gui/element/button/GuiButton;"}, {"nme": "setButtonPreviousPage", "acc": 1, "dsc": "(Lnet/brcdev/shopgui/gui/element/button/GuiButton;)V"}, {"nme": "getButtonNextPage", "acc": 1, "dsc": "()Lnet/brcdev/shopgui/gui/element/button/GuiButton;"}, {"nme": "setButtonNextPage", "acc": 1, "dsc": "(Lnet/brcdev/shopgui/gui/element/button/GuiButton;)V"}, {"nme": "hasAccess", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Lnet/brcdev/shopgui/shop/item/ShopItem;Z)Z"}, {"nme": "findShopItem", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Lnet/brcdev/shopgui/player/PlayerData;Lorg/bukkit/inventory/ItemStack;Z)Lnet/brcdev/shopgui/shop/item/ShopItem;"}, {"nme": "findShopItem", "acc": 1, "dsc": "(Lorg/bukkit/inventory/ItemStack;Z)Lnet/brcdev/shopgui/shop/item/ShopItem;"}, {"nme": "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Lnet/brcdev/shopgui/provider/economy/EconomyProvider;"}], "flds": []}, "net/brcdev/shopgui/economy/EconomyManager.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/economy/EconomyManager", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "registerCustomEconomyProvider", "acc": 1, "dsc": "(Lnet/brcdev/shopgui/provider/economy/EconomyProvider;)V"}, {"nme": "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Lnet/brcdev/shopgui/economy/EconomyType;)Lnet/brcdev/shopgui/provider/economy/EconomyProvider;"}, {"nme": "getDefaultEconomy<PERSON>rovider", "acc": 1, "dsc": "()Lnet/brcdev/shopgui/provider/economy/EconomyProvider;"}], "flds": []}, "net/brcdev/shopgui/exception/player/PlayerDataNotLoadedException.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/exception/player/PlayerDataNotLoadedException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/bukkit/OfflinePlayer;)V"}], "flds": []}, "net/brcdev/shopgui/exception/item/InvalidItemFlagException.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/exception/item/InvalidItemFlagException", "super": "net/brcdev/shopgui/exception/item/ItemLoadException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "net/brcdev/shopgui/spawner/external/provider/ExternalSpawnerProvider.class": {"ver": 52, "acc": 1537, "nme": "net/brcdev/shopgui/spawner/external/provider/ExternalSpawnerProvider", "super": "java/lang/Object", "mthds": [{"nme": "getName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getSpawnerItem", "acc": 1025, "dsc": "(Lorg/bukkit/entity/EntityType;)Lorg/bukkit/inventory/ItemStack;"}, {"nme": "getSpawnerEntityType", "acc": 1025, "dsc": "(Lorg/bukkit/inventory/ItemStack;)Lorg/bukkit/entity/EntityType;"}], "flds": []}, "net/brcdev/shopgui/exception/item/InvalidMaterialException.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/exception/item/InvalidMaterialException", "super": "net/brcdev/shopgui/exception/item/ItemLoadException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "net/brcdev/shopgui/gui/gui/OpenGui.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/gui/gui/OpenGui", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "net/brcdev/shopgui/shop/item/ShopItemType.class": {"ver": 52, "acc": 16433, "nme": "net/brcdev/shopgui/shop/item/ShopItemType", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lnet/brcdev/shopgui/shop/item/ShopItemType;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;)Lnet/brcdev/shopgui/shop/item/ShopItemType;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "getType", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;)Lnet/brcdev/shopgui/shop/item/ShopItemType;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "ITEM", "dsc": "Lnet/brcdev/shopgui/shop/item/ShopItemType;"}, {"acc": 16409, "nme": "PERMISSION", "dsc": "Lnet/brcdev/shopgui/shop/item/ShopItemType;"}, {"acc": 16409, "nme": "ENCHANTMENT", "dsc": "Lnet/brcdev/shopgui/shop/item/ShopItemType;"}, {"acc": 16409, "nme": "COMMAND", "dsc": "Lnet/brcdev/shopgui/shop/item/ShopItemType;"}, {"acc": 16409, "nme": "SPECIAL", "dsc": "Lnet/brcdev/shopgui/shop/item/ShopItemType;"}, {"acc": 16409, "nme": "SHOP_LINK", "dsc": "Lnet/brcdev/shopgui/shop/item/ShopItemType;"}, {"acc": 16409, "nme": "DUMMY", "dsc": "Lnet/brcdev/shopgui/shop/item/ShopItemType;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lnet/brcdev/shopgui/shop/item/ShopItemType;"}]}, "net/brcdev/shopgui/exception/item/MissingItemDefinitionException.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/exception/item/MissingItemDefinitionException", "super": "net/brcdev/shopgui/exception/item/ItemLoadException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "net/brcdev/shopgui/shop/ShopManager.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/shop/ShopManager", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getShops", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Lnet/brcdev/shopgui/shop/Shop;>;", "exs": ["net/brcdev/shopgui/exception/shop/ShopsNotLoadedException"]}, {"nme": "areShopsLoaded", "acc": 1, "dsc": "()Z"}, {"nme": "load", "acc": 1, "dsc": "()V"}, {"nme": "openMainMenu", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;)V"}, {"nme": "openShopMenu", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;<PERSON>java/lang/String;IZ)V"}, {"nme": "findShopItemByItemStack", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Lorg/bukkit/inventory/ItemStack;Z)Lnet/brcdev/shopgui/shop/item/ShopItem;"}, {"nme": "findShopItemByItemStack", "acc": 1, "dsc": "(Lorg/bukkit/inventory/ItemStack;Z)Lnet/brcdev/shopgui/shop/item/ShopItem;"}, {"nme": "getShopById", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/brcdev/shopgui/shop/Shop;"}], "flds": []}, "net/brcdev/shopgui/exception/item/InvalidModelException.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/exception/item/InvalidModelException", "super": "net/brcdev/shopgui/exception/item/ItemLoadException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "net/brcdev/shopgui/gui/element/button/GuiButton.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/gui/element/button/GuiButton", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "net/brcdev/shopgui/ShopGuiPlusApi.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/ShopGuiPlusApi", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getShop", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/brcdev/shopgui/shop/Shop;"}, {"nme": "openMainMenu", "acc": 9, "dsc": "(Lorg/bukkit/entity/Player;)V", "exs": ["net/brcdev/shopgui/exception/player/PlayerDataNotLoadedException"]}, {"nme": "openShop", "acc": 9, "dsc": "(Lorg/bukkit/entity/Player;<PERSON><PERSON><PERSON>/lang/String;I)V", "exs": ["net/brcdev/shopgui/exception/player/PlayerDataNotLoadedException"]}, {"nme": "getItemStackShop", "acc": 9, "dsc": "(Lorg/bukkit/entity/Player;Lorg/bukkit/inventory/ItemStack;)Lnet/brcdev/shopgui/shop/Shop;"}, {"nme": "getItemStackShop", "acc": 9, "dsc": "(Lorg/bukkit/inventory/ItemStack;)Lnet/brcdev/shopgui/shop/Shop;"}, {"nme": "getItemStackShopItem", "acc": 9, "dsc": "(Lorg/bukkit/entity/Player;Lorg/bukkit/inventory/ItemStack;)Lnet/brcdev/shopgui/shop/item/ShopItem;"}, {"nme": "getItemStackShopItem", "acc": 9, "dsc": "(Lorg/bukkit/inventory/ItemStack;)Lnet/brcdev/shopgui/shop/item/ShopItem;"}, {"nme": "getItemStackPriceBuy", "acc": 9, "dsc": "(Lorg/bukkit/entity/Player;Lorg/bukkit/inventory/ItemStack;)D"}, {"nme": "getItemStackPriceBuy", "acc": 9, "dsc": "(Lorg/bukkit/inventory/ItemStack;)D"}, {"nme": "getItemStackPriceSell", "acc": 9, "dsc": "(Lorg/bukkit/entity/Player;Lorg/bukkit/inventory/ItemStack;)D"}, {"nme": "getItemStackPriceSell", "acc": 9, "dsc": "(Lorg/bukkit/inventory/ItemStack;)D"}, {"nme": "getPriceModifier", "acc": 9, "dsc": "(Lorg/bukkit/entity/Player;Lnet/brcdev/shopgui/shop/item/ShopItem;Lnet/brcdev/shopgui/modifier/PriceModifierActionType;)Lnet/brcdev/shopgui/modifier/PriceModifier;", "exs": ["net/brcdev/shopgui/exception/player/PlayerDataNotLoadedException"]}, {"nme": "setPriceModifier", "acc": 9, "dsc": "(Lorg/bukkit/entity/Player;Lnet/brcdev/shopgui/shop/item/ShopItem;Lnet/brcdev/shopgui/modifier/PriceModifierActionType;D)V", "exs": ["net/brcdev/shopgui/exception/player/PlayerDataNotLoadedException"]}, {"nme": "resetPriceModifier", "acc": 9, "dsc": "(Lorg/bukkit/entity/Player;Lnet/brcdev/shopgui/shop/item/ShopItem;Lnet/brcdev/shopgui/modifier/PriceModifierActionType;)V", "exs": ["net/brcdev/shopgui/exception/player/PlayerDataNotLoadedException"]}, {"nme": "getPriceModifier", "acc": 9, "dsc": "(Lorg/bukkit/entity/Player;Lnet/brcdev/shopgui/shop/Shop;Lnet/brcdev/shopgui/modifier/PriceModifierActionType;)Lnet/brcdev/shopgui/modifier/PriceModifier;", "exs": ["net/brcdev/shopgui/exception/player/PlayerDataNotLoadedException"]}, {"nme": "setPriceModifier", "acc": 9, "dsc": "(Lorg/bukkit/entity/Player;Lnet/brcdev/shopgui/shop/Shop;Lnet/brcdev/shopgui/modifier/PriceModifierActionType;D)V", "exs": ["net/brcdev/shopgui/exception/player/PlayerDataNotLoadedException"]}, {"nme": "resetPriceModifier", "acc": 9, "dsc": "(Lorg/bukkit/entity/Player;Lnet/brcdev/shopgui/shop/Shop;Lnet/brcdev/shopgui/modifier/PriceModifierActionType;)V", "exs": ["net/brcdev/shopgui/exception/player/PlayerDataNotLoadedException"]}, {"nme": "getPriceModifier", "acc": 9, "dsc": "(Lorg/bukkit/entity/Player;Lnet/brcdev/shopgui/modifier/PriceModifierActionType;)Lnet/brcdev/shopgui/modifier/PriceModifier;", "exs": ["net/brcdev/shopgui/exception/player/PlayerDataNotLoadedException"]}, {"nme": "setPriceModifier", "acc": 9, "dsc": "(Lorg/bukkit/entity/Player;Lnet/brcdev/shopgui/modifier/PriceModifierActionType;D)V", "exs": ["net/brcdev/shopgui/exception/player/PlayerDataNotLoadedException"]}, {"nme": "resetPriceModifier", "acc": 9, "dsc": "(Lorg/bukkit/entity/Player;Lnet/brcdev/shopgui/modifier/PriceModifierActionType;)V", "exs": ["net/brcdev/shopgui/exception/player/PlayerDataNotLoadedException"]}, {"nme": "registerSpawnerProvider", "acc": 9, "dsc": "(Lnet/brcdev/shopgui/spawner/external/provider/ExternalSpawnerProvider;)V", "exs": ["net/brcdev/shopgui/exception/api/ExternalSpawnerProviderNameConflictException"]}, {"nme": "registerEconomyProvider", "acc": 9, "dsc": "(Lnet/brcdev/shopgui/provider/economy/EconomyProvider;)V"}, {"nme": "registerItemProvider", "acc": 9, "dsc": "(Lnet/brcdev/shopgui/provider/item/ItemProvider;)V"}, {"nme": "getPlugin", "acc": 9, "dsc": "()Lnet/brcdev/shopgui/ShopGuiPlugin;"}, {"nme": "setPlugin", "acc": 8, "dsc": "(Lnet/brcdev/shopgui/ShopGuiPlugin;)V"}, {"nme": "getShopItem", "acc": 10, "dsc": "(Lorg/bukkit/entity/Player;Lorg/bukkit/inventory/ItemStack;)Lnet/brcdev/shopgui/shop/item/ShopItem;"}, {"nme": "getShopItem", "acc": 10, "dsc": "(Lorg/bukkit/inventory/ItemStack;)Lnet/brcdev/shopgui/shop/item/ShopItem;"}], "flds": [{"acc": 10, "nme": "shopGuiPlugin", "dsc": "Lnet/brcdev/shopgui/ShopGuiPlugin;"}]}, "net/brcdev/shopgui/provider/item/ItemProvider.class": {"ver": 52, "acc": 1057, "nme": "net/brcdev/shopgui/provider/item/ItemProvider", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "isValidItem", "acc": 1025, "dsc": "(Lorg/bukkit/inventory/ItemStack;)Z"}, {"nme": "loadItem", "acc": 1025, "dsc": "(Lorg/bukkit/configuration/ConfigurationSection;)Lorg/bukkit/inventory/ItemStack;"}, {"nme": "compare", "acc": 1025, "dsc": "(Lorg/bukkit/inventory/ItemStack;Lorg/bukkit/inventory/ItemStack;)Z"}, {"nme": "isReady", "acc": 1, "dsc": "()Z"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 4, "nme": "ready", "dsc": "Z"}, {"acc": 4, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "net/brcdev/shopgui/exception/item/MissingSpawnEggTypeException.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/exception/item/MissingSpawnEggTypeException", "super": "net/brcdev/shopgui/exception/item/ItemLoadException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "net/brcdev/shopgui/exception/item/InvalidEnchantmentException.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/exception/item/InvalidEnchantmentException", "super": "net/brcdev/shopgui/exception/item/ItemLoadException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "net/brcdev/shopgui/provider/economy/EconomyProvider.class": {"ver": 52, "acc": 1057, "nme": "net/brcdev/shopgui/provider/economy/EconomyProvider", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getCurrencyPrefix", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCurrencyPrefix", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getCurrencySuffix", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setCurrencySuffix", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getBalance", "acc": 1025, "dsc": "(Lorg/bukkit/entity/Player;)D"}, {"nme": "deposit", "acc": 1025, "dsc": "(Lorg/bukkit/entity/Player;D)V"}, {"nme": "withdraw", "acc": 1025, "dsc": "(Lorg/bukkit/entity/Player;D)V"}, {"nme": "has", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;D)Z"}], "flds": [{"acc": 4, "nme": "currencyPrefix", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4, "nme": "currencySuffix", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "net/brcdev/shopgui/modifier/PriceModifier.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/modifier/PriceModifier", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lnet/brcdev/shopgui/modifier/PriceModifierType;D)V"}, {"nme": "getPriceModifierType", "acc": 1, "dsc": "()Lnet/brcdev/shopgui/modifier/PriceModifierType;"}, {"nme": "setPriceModifierType", "acc": 1, "dsc": "(Lnet/brcdev/shopgui/modifier/PriceModifierType;)V"}, {"nme": "getModifier", "acc": 1, "dsc": "()D"}, {"nme": "setModifier", "acc": 1, "dsc": "(D)V"}], "flds": []}, "net/brcdev/shopgui/core/BConfig.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/core/BConfig", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getConfig", "acc": 1, "dsc": "()Lorg/bukkit/configuration/file/FileConfiguration;"}], "flds": []}, "net/brcdev/shopgui/exception/item/PotionLoadException.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/exception/item/PotionLoadException", "super": "net/brcdev/shopgui/exception/item/ItemLoadException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "net/brcdev/shopgui/event/ShopPostTransactionEvent.class": {"ver": 52, "acc": 1057, "nme": "net/brcdev/shopgui/event/ShopPostTransactionEvent", "super": "org/bukkit/event/Event", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getHandlerList", "acc": 9, "dsc": "()Lorg/bukkit/event/HandlerList;"}, {"nme": "getHandlers", "acc": 1025, "dsc": "()Lorg/bukkit/event/HandlerList;"}, {"nme": "getResult", "acc": 1025, "dsc": "()Lnet/brcdev/shopgui/shop/ShopTransactionResult;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "handlers", "dsc": "Lorg/bukkit/event/HandlerList;"}]}, "net/brcdev/shopgui/exception/api/ExternalSpawnerProviderNameConflictException.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/exception/api/ExternalSpawnerProviderNameConflictException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "net/brcdev/shopgui/economy/EconomyType.class": {"ver": 52, "acc": 16433, "nme": "net/brcdev/shopgui/economy/EconomyType", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lnet/brcdev/shopgui/economy/EconomyType;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;)Lnet/brcdev/shopgui/economy/EconomyType;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "CUSTOM", "dsc": "Lnet/brcdev/shopgui/economy/EconomyType;"}, {"acc": 16409, "nme": "EXP", "dsc": "Lnet/brcdev/shopgui/economy/EconomyType;"}, {"acc": 16409, "nme": "MYSQL_TOKENS", "dsc": "Lnet/brcdev/shopgui/economy/EconomyType;"}, {"acc": 16409, "nme": "PLAYER_POINTS", "dsc": "Lnet/brcdev/shopgui/economy/EconomyType;"}, {"acc": 16409, "nme": "TOKEN_ENCHANT", "dsc": "Lnet/brcdev/shopgui/economy/EconomyType;"}, {"acc": 16409, "nme": "TOKEN_MANAGER", "dsc": "Lnet/brcdev/shopgui/economy/EconomyType;"}, {"acc": 16409, "nme": "VAULT", "dsc": "Lnet/brcdev/shopgui/economy/EconomyType;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lnet/brcdev/shopgui/economy/EconomyType;"}]}, "net/brcdev/shopgui/shop/ShopManager$ShopAction.class": {"ver": 52, "acc": 16433, "nme": "net/brcdev/shopgui/shop/ShopManager$ShopAction", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lnet/brcdev/shopgui/shop/ShopManager$ShopAction;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;)Lnet/brcdev/shopgui/shop/ShopManager$ShopAction;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "BUY", "dsc": "Lnet/brcdev/shopgui/shop/ShopManager$ShopAction;"}, {"acc": 16409, "nme": "SELL", "dsc": "Lnet/brcdev/shopgui/shop/ShopManager$ShopAction;"}, {"acc": 16409, "nme": "SELL_ALL", "dsc": "Lnet/brcdev/shopgui/shop/ShopManager$ShopAction;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lnet/brcdev/shopgui/shop/ShopManager$ShopAction;"}]}, "net/brcdev/shopgui/shop/item/ShopItem.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/shop/item/ShopItem", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lnet/brcdev/shopgui/shop/Shop;Ljava/lang/String;Lnet/brcdev/shopgui/shop/item/ShopItemType;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lnet/brcdev/shopgui/shop/Shop;Ljava/lang/String;Lnet/brcdev/shopgui/shop/item/ShopItemType;Lorg/bukkit/inventory/ItemStack;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lnet/brcdev/shopgui/shop/Shop;Ljava/lang/String;Lnet/brcdev/shopgui/shop/item/ShopItemType;Lorg/bukkit/inventory/ItemStack;ZZIDDI)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lnet/brcdev/shopgui/shop/Shop;Ljava/lang/String;Lnet/brcdev/shopgui/shop/item/ShopItemType;Lorg/bukkit/inventory/ItemStack;ZIIDDI)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lnet/brcdev/shopgui/shop/item/ShopItem;)V"}, {"nme": "getShop", "acc": 1, "dsc": "()Lnet/brcdev/shopgui/shop/Shop;"}, {"nme": "setShop", "acc": 1, "dsc": "(Lnet/brcdev/shopgui/shop/Shop;)V"}, {"nme": "getId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getItem", "acc": 1, "dsc": "()Lorg/bukkit/inventory/ItemStack;"}, {"nme": "setItem", "acc": 1, "dsc": "(Lorg/bukkit/inventory/ItemStack;)V"}, {"nme": "getPlaceholder", "acc": 1, "dsc": "()Lorg/bukkit/inventory/ItemStack;"}, {"nme": "setPlaceholder", "acc": 1, "dsc": "(Lorg/bukkit/inventory/ItemStack;)V"}, {"nme": "getType", "acc": 1, "dsc": "()Lnet/brcdev/shopgui/shop/item/ShopItemType;"}, {"nme": "setType", "acc": 1, "dsc": "(Lnet/brcdev/shopgui/shop/item/ShopItemType;)V"}, {"nme": "getPage", "acc": 1, "dsc": "()I"}, {"nme": "setPage", "acc": 1, "dsc": "(I)V"}, {"nme": "getSlot", "acc": 1, "dsc": "()I"}, {"nme": "setSlot", "acc": 1, "dsc": "(I)V"}, {"nme": "getBuyPrice", "acc": 1, "dsc": "()D"}, {"nme": "setBuyPrice", "acc": 1, "dsc": "(D)V"}, {"nme": "getBuyPrice", "acc": 131073, "dsc": "(Lorg/bukkit/entity/Player;Lnet/brcdev/shopgui/player/PlayerData;)D", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getBuyPrice", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;)D"}, {"nme": "getSellPrice", "acc": 1, "dsc": "()D"}, {"nme": "setSellPrice", "acc": 1, "dsc": "(D)V"}, {"nme": "getSellPrice", "acc": 131073, "dsc": "(Lorg/bukkit/entity/Player;Lnet/brcdev/shopgui/player/PlayerData;)D", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getSellPrice", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;)D"}, {"nme": "getBuyPriceForAmount", "acc": 131073, "dsc": "(Lorg/bukkit/entity/Player;Lnet/brcdev/shopgui/player/PlayerData;I)D", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getBuyPriceForAmount", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;I)D"}, {"nme": "getBuyPriceForAmount", "acc": 1, "dsc": "(I)D"}, {"nme": "getSellPriceForAmount", "acc": 131073, "dsc": "(Lorg/bukkit/entity/Player;Lnet/brcdev/shopgui/player/PlayerData;I)D", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getSellPriceForAmount", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;I)D"}, {"nme": "getSellPriceForAmount", "acc": 1, "dsc": "(I)D"}], "flds": []}, "net/brcdev/shopgui/shop/ShopTransactionResult$ShopTransactionResultType.class": {"ver": 52, "acc": 16433, "nme": "net/brcdev/shopgui/shop/ShopTransactionResult$ShopTransactionResultType", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lnet/brcdev/shopgui/shop/ShopTransactionResult$ShopTransactionResultType;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;)Lnet/brcdev/shopgui/shop/ShopTransactionResult$ShopTransactionResultType;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "SUCCESS", "dsc": "Lnet/brcdev/shopgui/shop/ShopTransactionResult$ShopTransactionResultType;"}, {"acc": 16409, "nme": "FAILURE_CANCELLED", "dsc": "Lnet/brcdev/shopgui/shop/ShopTransactionResult$ShopTransactionResultType;"}, {"acc": 16409, "nme": "FAILURE_NO_MONEY", "dsc": "Lnet/brcdev/shopgui/shop/ShopTransactionResult$ShopTransactionResultType;"}, {"acc": 16409, "nme": "FAILURE_NO_ITEMS", "dsc": "Lnet/brcdev/shopgui/shop/ShopTransactionResult$ShopTransactionResultType;"}, {"acc": 16409, "nme": "FAILURE_FULL_INVENTORY", "dsc": "Lnet/brcdev/shopgui/shop/ShopTransactionResult$ShopTransactionResultType;"}, {"acc": 16409, "nme": "FAILURE_ENCHANTMENT_INAPPLICABLE", "dsc": "Lnet/brcdev/shopgui/shop/ShopTransactionResult$ShopTransactionResultType;"}, {"acc": 16409, "nme": "FAILURE_ENCHANTMENT_ALREADY_APPLIED", "dsc": "Lnet/brcdev/shopgui/shop/ShopTransactionResult$ShopTransactionResultType;"}, {"acc": 16409, "nme": "FAILURE_ENCHANTMENT_MAX_AMOUNT", "dsc": "Lnet/brcdev/shopgui/shop/ShopTransactionResult$ShopTransactionResultType;"}, {"acc": 16409, "nme": "FAILURE_ENCHANTMENT_LEVEL_DIFF", "dsc": "Lnet/brcdev/shopgui/shop/ShopTransactionResult$ShopTransactionResultType;"}, {"acc": 16409, "nme": "FAILURE_ENCHANTMENT_TOO_MANY_ITEMS", "dsc": "Lnet/brcdev/shopgui/shop/ShopTransactionResult$ShopTransactionResultType;"}, {"acc": 16409, "nme": "FAILURE_PERMISSION_DISABLED", "dsc": "Lnet/brcdev/shopgui/shop/ShopTransactionResult$ShopTransactionResultType;"}, {"acc": 16409, "nme": "FAILURE_PERMISSION_ALREADY_HAVE", "dsc": "Lnet/brcdev/shopgui/shop/ShopTransactionResult$ShopTransactionResultType;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lnet/brcdev/shopgui/shop/ShopTransactionResult$ShopTransactionResultType;"}]}, "net/brcdev/shopgui/item/ItemManager.class": {"ver": 52, "acc": 33, "nme": "net/brcdev/shopgui/item/ItemManager", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "registerDefaultItemProviders", "acc": 1, "dsc": "()V"}, {"nme": "registerItemProvider", "acc": 1, "dsc": "(Lnet/brcdev/shopgui/provider/item/ItemProvider;)V"}, {"nme": "areAllProvidersReady", "acc": 1, "dsc": "()Z"}, {"nme": "loadItem", "acc": 1, "dsc": "(Lorg/bukkit/configuration/ConfigurationSection;)Lorg/bukkit/inventory/ItemStack;"}, {"nme": "compare", "acc": 1, "dsc": "(Lorg/bukkit/inventory/ItemStack;Lorg/bukkit/inventory/ItemStack;)Z"}, {"nme": "setup", "acc": 1, "dsc": "()V"}], "flds": []}}}}