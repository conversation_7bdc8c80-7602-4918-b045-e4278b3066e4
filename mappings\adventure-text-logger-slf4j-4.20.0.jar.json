{"md5": "63830449ed7c785299ba8a1cf8272cf0", "sha2": "90fecb2515daf2811497af215e88f3afb92535f6", "sha256": "16591283fe6e4ee9add522ba3a002858d55bd193c7ea79fcd4e3d64ae3fd6d93", "contents": {"classes": {"net/kyori/adventure/text/logger/slf4j/ComponentLoggerProvider$LoggerHelper.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/text/logger/slf4j/ComponentLoggerProvider$LoggerHelper", "super": "java/lang/Object", "mthds": [{"nme": "plainSerializer", "acc": 1025, "dsc": "()Ljava/util/function/Function;", "sig": "()Ljava/util/function/Function<Lnet/kyori/adventure/text/Component;Ljava/lang/String;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "delegating", "acc": 1025, "dsc": "(Lorg/slf4j/Logger;<PERSON><PERSON><PERSON>/util/function/Function;)Lnet/kyori/adventure/text/logger/slf4j/ComponentLogger;", "sig": "(Lorg/slf4j/Logger;Ljava/util/function/Function<Lnet/kyori/adventure/text/Component;Ljava/lang/String;>;)Lnet/kyori/adventure/text/logger/slf4j/ComponentLogger;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": [], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$NonExtendable;"}]}, "net/kyori/adventure/text/logger/slf4j/Handler$DefaultProvider.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/logger/slf4j/Handler$DefaultProvider", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "logger", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/logger/slf4j/ComponentLoggerProvider$LoggerHelper;Lja<PERSON>/lang/String;)Lnet/kyori/adventure/text/logger/slf4j/ComponentLogger;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": [{"acc": 18, "nme": "loggers", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lnet/kyori/adventure/text/logger/slf4j/ComponentLogger;>;"}]}, "net/kyori/adventure/text/logger/slf4j/Handler$LoggerHelperImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/logger/slf4j/Handler$LoggerHelperImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "plainSerializer", "acc": 1, "dsc": "()Ljava/util/function/Function;", "sig": "()Ljava/util/function/Function<Lnet/kyori/adventure/text/Component;Ljava/lang/String;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "delegating", "acc": 1, "dsc": "(Lorg/slf4j/Logger;<PERSON><PERSON><PERSON>/util/function/Function;)Lnet/kyori/adventure/text/logger/slf4j/ComponentLogger;", "sig": "(Lorg/slf4j/Logger;Ljava/util/function/Function<Lnet/kyori/adventure/text/Component;Ljava/lang/String;>;)Lnet/kyori/adventure/text/logger/slf4j/ComponentLogger;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "lambda$plainSerializer$0", "acc": 4106, "dsc": "(Lnet/kyori/adventure/text/Component;)Ljava/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "INSTANCE", "dsc": "Lnet/kyori/adventure/text/logger/slf4j/Handler$LoggerHelperImpl;"}]}, "net/kyori/adventure/text/logger/slf4j/UnpackedComponentThrowable.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/logger/slf4j/UnpackedComponentThrowable", "super": "java/lang/Throwable", "mthds": [{"nme": "unpack", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;<PERSON><PERSON><PERSON>/util/function/Function;)Ljava/lang/Throwable;", "sig": "(Lja<PERSON>/lang/Throwable;Ljava/util/function/Function<Lnet/kyori/adventure/text/Component;Ljava/lang/String;>;)Ljava/lang/Throwable;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<+Ljava/lang/Throwable;>;Ljava/lang/String;Ljava/lang/Throwable;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "fillInStackTrace", "acc": 33, "dsc": "()<PERSON><PERSON><PERSON>/lang/Throwable;"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -1}, {"acc": 18, "nme": "backingType", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<+Ljava/lang/Throwable;>;"}]}, "net/kyori/adventure/text/logger/slf4j/WrappingComponentLoggerImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/logger/slf4j/WrappingComponentLoggerImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/slf4j/Logger;<PERSON><PERSON><PERSON>/util/function/Function;)V", "sig": "(Lorg/slf4j/Logger;Ljava/util/function/Function<Lnet/kyori/adventure/text/Component;Ljava/lang/String;>;)V"}, {"nme": "serialize", "acc": 2, "dsc": "(Lnet/kyori/adventure/text/Component;)Ljava/lang/String;"}, {"nme": "maybeSerialize", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "maybeSerialize", "acc": 130, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;)[<PERSON>ja<PERSON>/lang/Object;"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isTraceEnabled", "acc": 1, "dsc": "()Z"}, {"nme": "isTraceEnabled", "acc": 1, "dsc": "(Lorg/slf4j/Marker;)Z"}, {"nme": "isDebugEnabled", "acc": 1, "dsc": "()Z"}, {"nme": "isDebugEnabled", "acc": 1, "dsc": "(Lorg/slf4j/Marker;)Z"}, {"nme": "isInfoEnabled", "acc": 1, "dsc": "()Z"}, {"nme": "isInfoEnabled", "acc": 1, "dsc": "(Lorg/slf4j/Marker;)Z"}, {"nme": "is<PERSON>arn<PERSON>nabled", "acc": 1, "dsc": "()Z"}, {"nme": "is<PERSON>arn<PERSON>nabled", "acc": 1, "dsc": "(Lorg/slf4j/Marker;)Z"}, {"nme": "isErrorEnabled", "acc": 1, "dsc": "()Z"}, {"nme": "isErrorEnabled", "acc": 1, "dsc": "(Lorg/slf4j/Marker;)Z"}, {"nme": "isEnabledForLevel", "acc": 1, "dsc": "(Lorg/slf4j/event/Level;)Z"}, {"nme": "makeLoggingEventBuilder", "acc": 1, "dsc": "(Lorg/slf4j/event/Level;)Lorg/slf4j/spi/LoggingEventBuilder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "atLevel", "acc": 1, "dsc": "(Lorg/slf4j/event/Level;)Lorg/slf4j/spi/LoggingEventBuilder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "trace", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "trace", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "trace", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "trace", "acc": 129, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Lja<PERSON>/lang/Object;)V"}, {"nme": "trace", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "trace", "acc": 1, "dsc": "(<PERSON><PERSON>/slf4j/Marker;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "trace", "acc": 1, "dsc": "(Lo<PERSON>/slf4j/Marker;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "trace", "acc": 1, "dsc": "(Lo<PERSON>/slf4j/Marker;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "trace", "acc": 129, "dsc": "(Lorg/slf4j/Marker;<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "trace", "acc": 1, "dsc": "(Lo<PERSON>/slf4j/Marker;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "debug", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "debug", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "debug", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "debug", "acc": 129, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Lja<PERSON>/lang/Object;)V"}, {"nme": "debug", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "debug", "acc": 1, "dsc": "(<PERSON><PERSON>/slf4j/Marker;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "debug", "acc": 1, "dsc": "(Lo<PERSON>/slf4j/Marker;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "debug", "acc": 1, "dsc": "(Lo<PERSON>/slf4j/Marker;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "debug", "acc": 129, "dsc": "(Lorg/slf4j/Marker;<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "debug", "acc": 1, "dsc": "(Lo<PERSON>/slf4j/Marker;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "info", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "info", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "info", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "info", "acc": 129, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Lja<PERSON>/lang/Object;)V"}, {"nme": "info", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "info", "acc": 1, "dsc": "(<PERSON><PERSON>/slf4j/Marker;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "info", "acc": 1, "dsc": "(Lo<PERSON>/slf4j/Marker;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "info", "acc": 1, "dsc": "(Lo<PERSON>/slf4j/Marker;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "info", "acc": 129, "dsc": "(Lorg/slf4j/Marker;<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "info", "acc": 1, "dsc": "(Lo<PERSON>/slf4j/Marker;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "warn", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "warn", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "warn", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "warn", "acc": 129, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Lja<PERSON>/lang/Object;)V"}, {"nme": "warn", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "warn", "acc": 1, "dsc": "(<PERSON><PERSON>/slf4j/Marker;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "warn", "acc": 1, "dsc": "(Lo<PERSON>/slf4j/Marker;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "warn", "acc": 1, "dsc": "(Lo<PERSON>/slf4j/Marker;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "warn", "acc": 129, "dsc": "(Lorg/slf4j/Marker;<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "warn", "acc": 1, "dsc": "(Lo<PERSON>/slf4j/Marker;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "error", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "error", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "error", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "error", "acc": 129, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Lja<PERSON>/lang/Object;)V"}, {"nme": "error", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "error", "acc": 1, "dsc": "(<PERSON><PERSON>/slf4j/Marker;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "error", "acc": 1, "dsc": "(Lo<PERSON>/slf4j/Marker;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "error", "acc": 1, "dsc": "(Lo<PERSON>/slf4j/Marker;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "error", "acc": 129, "dsc": "(Lorg/slf4j/Marker;<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "error", "acc": 1, "dsc": "(Lo<PERSON>/slf4j/Marker;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "trace", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;)V"}, {"nme": "trace", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;Ljava/lang/Object;)V"}, {"nme": "trace", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;Ljava/lang/Object;Ljava/lang/Object;)V"}, {"nme": "trace", "acc": 129, "dsc": "(Lnet/kyori/adventure/text/Component;[<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "trace", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;Lja<PERSON>/lang/Throwable;)V"}, {"nme": "trace", "acc": 1, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;)V"}, {"nme": "trace", "acc": 1, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "trace", "acc": 1, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;L<PERSON><PERSON>/lang/Object;<PERSON>ja<PERSON>/lang/Object;)V"}, {"nme": "trace", "acc": 129, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;[<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "trace", "acc": 1, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "debug", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;)V"}, {"nme": "debug", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;Ljava/lang/Object;)V"}, {"nme": "debug", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;Ljava/lang/Object;Ljava/lang/Object;)V"}, {"nme": "debug", "acc": 129, "dsc": "(Lnet/kyori/adventure/text/Component;[<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "debug", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;Lja<PERSON>/lang/Throwable;)V"}, {"nme": "debug", "acc": 1, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;)V"}, {"nme": "debug", "acc": 1, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "debug", "acc": 1, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;L<PERSON><PERSON>/lang/Object;<PERSON>ja<PERSON>/lang/Object;)V"}, {"nme": "debug", "acc": 129, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;[<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "debug", "acc": 1, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "info", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;)V"}, {"nme": "info", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;Ljava/lang/Object;)V"}, {"nme": "info", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;Ljava/lang/Object;Ljava/lang/Object;)V"}, {"nme": "info", "acc": 129, "dsc": "(Lnet/kyori/adventure/text/Component;[<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "info", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;Lja<PERSON>/lang/Throwable;)V"}, {"nme": "info", "acc": 1, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;)V"}, {"nme": "info", "acc": 1, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "info", "acc": 1, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;L<PERSON><PERSON>/lang/Object;<PERSON>ja<PERSON>/lang/Object;)V"}, {"nme": "info", "acc": 129, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;[<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "info", "acc": 1, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "warn", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;)V"}, {"nme": "warn", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;Ljava/lang/Object;)V"}, {"nme": "warn", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;Ljava/lang/Object;Ljava/lang/Object;)V"}, {"nme": "warn", "acc": 129, "dsc": "(Lnet/kyori/adventure/text/Component;[<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "warn", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;Lja<PERSON>/lang/Throwable;)V"}, {"nme": "warn", "acc": 1, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;)V"}, {"nme": "warn", "acc": 1, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "warn", "acc": 1, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;L<PERSON><PERSON>/lang/Object;<PERSON>ja<PERSON>/lang/Object;)V"}, {"nme": "warn", "acc": 129, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;[<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "warn", "acc": 1, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "error", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;)V"}, {"nme": "error", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;Ljava/lang/Object;)V"}, {"nme": "error", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;Ljava/lang/Object;Ljava/lang/Object;)V"}, {"nme": "error", "acc": 129, "dsc": "(Lnet/kyori/adventure/text/Component;[<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "error", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;Lja<PERSON>/lang/Throwable;)V"}, {"nme": "error", "acc": 1, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;)V"}, {"nme": "error", "acc": 1, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "error", "acc": 1, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;L<PERSON><PERSON>/lang/Object;<PERSON>ja<PERSON>/lang/Object;)V"}, {"nme": "error", "acc": 129, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;[<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "error", "acc": 1, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "FQCN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "logger", "dsc": "Lorg/slf4j/Logger;"}, {"acc": 18, "nme": "isLocationAware", "dsc": "Z"}, {"acc": 18, "nme": "serializer", "dsc": "Ljava/util/function/Function;", "sig": "Ljava/util/function/Function<Lnet/kyori/adventure/text/Component;Ljava/lang/String;>;"}]}, "META-INF/versions/9/net/kyori/adventure/text/logger/slf4j/CallerClassFinder.class": {"ver": 53, "acc": 48, "nme": "net/kyori/adventure/text/logger/slf4j/CallerClassFinder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "callingClassName", "acc": 8, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "callingClassName", "acc": 8, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$callingClassName$1", "acc": 4106, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/IllegalArgumentException;"}, {"nme": "lambda$callingClassName$0", "acc": 4106, "dsc": "(ILjava/util/stream/Stream;)Ljava/util/Optional;"}], "flds": []}, "net/kyori/adventure/text/logger/slf4j/ComponentLogger.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/text/logger/slf4j/ComponentLogger", "super": "java/lang/Object", "mthds": [{"nme": "logger", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/logger/slf4j/ComponentLogger;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "logger", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/logger/slf4j/ComponentLogger;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "logger", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Lnet/kyori/adventure/text/logger/slf4j/ComponentLogger;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Lnet/kyori/adventure/text/logger/slf4j/ComponentLogger;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "trace", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/Component;)V"}, {"nme": "trace", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/Component;Ljava/lang/Object;)V"}, {"nme": "trace", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/Component;Ljava/lang/Object;Ljava/lang/Object;)V"}, {"nme": "trace", "acc": 1153, "dsc": "(Lnet/kyori/adventure/text/Component;[<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "trace", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/Component;Lja<PERSON>/lang/Throwable;)V"}, {"nme": "trace", "acc": 1025, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;)V"}, {"nme": "trace", "acc": 1025, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "trace", "acc": 1025, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;L<PERSON><PERSON>/lang/Object;<PERSON>ja<PERSON>/lang/Object;)V"}, {"nme": "trace", "acc": 1153, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;[<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "trace", "acc": 1025, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "debug", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/Component;)V"}, {"nme": "debug", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/Component;Ljava/lang/Object;)V"}, {"nme": "debug", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/Component;Ljava/lang/Object;Ljava/lang/Object;)V"}, {"nme": "debug", "acc": 1153, "dsc": "(Lnet/kyori/adventure/text/Component;[<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "debug", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/Component;Lja<PERSON>/lang/Throwable;)V"}, {"nme": "debug", "acc": 1025, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;)V"}, {"nme": "debug", "acc": 1025, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "debug", "acc": 1025, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;L<PERSON><PERSON>/lang/Object;<PERSON>ja<PERSON>/lang/Object;)V"}, {"nme": "debug", "acc": 1153, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;[<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "debug", "acc": 1025, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "info", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/Component;)V"}, {"nme": "info", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/Component;Ljava/lang/Object;)V"}, {"nme": "info", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/Component;Ljava/lang/Object;Ljava/lang/Object;)V"}, {"nme": "info", "acc": 1153, "dsc": "(Lnet/kyori/adventure/text/Component;[<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "info", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/Component;Lja<PERSON>/lang/Throwable;)V"}, {"nme": "info", "acc": 1025, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;)V"}, {"nme": "info", "acc": 1025, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "info", "acc": 1025, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;L<PERSON><PERSON>/lang/Object;<PERSON>ja<PERSON>/lang/Object;)V"}, {"nme": "info", "acc": 1153, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;[<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "info", "acc": 1025, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "warn", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/Component;)V"}, {"nme": "warn", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/Component;Ljava/lang/Object;)V"}, {"nme": "warn", "acc": 1153, "dsc": "(Lnet/kyori/adventure/text/Component;[<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "warn", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/Component;Ljava/lang/Object;Ljava/lang/Object;)V"}, {"nme": "warn", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/Component;Lja<PERSON>/lang/Throwable;)V"}, {"nme": "warn", "acc": 1025, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;)V"}, {"nme": "warn", "acc": 1025, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "warn", "acc": 1025, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;L<PERSON><PERSON>/lang/Object;<PERSON>ja<PERSON>/lang/Object;)V"}, {"nme": "warn", "acc": 1153, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;[<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "warn", "acc": 1025, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "error", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/Component;)V"}, {"nme": "error", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/Component;Ljava/lang/Object;)V"}, {"nme": "error", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/Component;Ljava/lang/Object;Ljava/lang/Object;)V"}, {"nme": "error", "acc": 1153, "dsc": "(Lnet/kyori/adventure/text/Component;[<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "error", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/Component;Lja<PERSON>/lang/Throwable;)V"}, {"nme": "error", "acc": 1025, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;)V"}, {"nme": "error", "acc": 1025, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "error", "acc": 1025, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;L<PERSON><PERSON>/lang/Object;<PERSON>ja<PERSON>/lang/Object;)V"}, {"nme": "error", "acc": 1153, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;[<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "error", "acc": 1025, "dsc": "(Lorg/slf4j/Marker;Lnet/kyori/adventure/text/Component;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}], "flds": []}, "net/kyori/adventure/text/logger/slf4j/ComponentLoggerProvider.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/text/logger/slf4j/ComponentLoggerProvider", "super": "java/lang/Object", "mthds": [{"nme": "logger", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/logger/slf4j/ComponentLoggerProvider$LoggerHelper;Lja<PERSON>/lang/String;)Lnet/kyori/adventure/text/logger/slf4j/ComponentLogger;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": [], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$Internal;"}]}, "net/kyori/adventure/text/logger/slf4j/Handler.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/logger/slf4j/Handler", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "logger", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/logger/slf4j/ComponentLogger;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "PROVIDER", "dsc": "Lnet/kyori/adventure/text/logger/slf4j/ComponentLoggerProvider;"}]}, "net/kyori/adventure/text/logger/slf4j/CallerClassFinder.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/logger/slf4j/CallerClassFinder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "callingClassName", "acc": 8, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "callingClassName", "acc": 8, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}}}}