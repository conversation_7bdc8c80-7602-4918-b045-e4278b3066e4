{"md5": "74c2e9695842a46c2c2c1cd5179266c4", "sha2": "848c45d334f6cc5c8dd602b0e58fd4482964eddc", "sha256": "73b00b244b7b9e285654a45e765892bf5d369da77d42b5b4b5429122ed198a33", "contents": {"classes": {"org/apache/maven/repository/internal/DefaultArtifactDescriptorReader.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/repository/internal/DefaultArtifactDescriptorReader", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/RemoteRepositoryManager;Lorg/eclipse/aether/impl/VersionResolver;Lorg/eclipse/aether/impl/VersionRangeResolver;Lorg/eclipse/aether/impl/ArtifactResolver;Lorg/apache/maven/model/building/ModelBuilder;Lorg/eclipse/aether/impl/RepositoryEventDispatcher;Lorg/apache/maven/repository/internal/ModelCacheFactory;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "initService", "acc": 131073, "dsc": "(Lorg/eclipse/aether/spi/locator/ServiceLocator;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "setRemoteRepositoryManager", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/RemoteRepositoryManager;)Lorg/apache/maven/repository/internal/DefaultArtifactDescriptorReader;"}, {"nme": "setVersionResolver", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/VersionResolver;)Lorg/apache/maven/repository/internal/DefaultArtifactDescriptorReader;"}, {"nme": "setVersionRangeResolver", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/VersionRangeResolver;)Lorg/apache/maven/repository/internal/DefaultArtifactDescriptorReader;"}, {"nme": "setArtifactResolver", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/ArtifactResolver;)Lorg/apache/maven/repository/internal/DefaultArtifactDescriptorReader;"}, {"nme": "setRepositoryEventDispatcher", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/RepositoryEventDispatcher;)Lorg/apache/maven/repository/internal/DefaultArtifactDescriptorReader;"}, {"nme": "setModelBuilder", "acc": 1, "dsc": "(Lorg/apache/maven/model/building/ModelBuilder;)Lorg/apache/maven/repository/internal/DefaultArtifactDescriptorReader;"}, {"nme": "setModelCacheFactory", "acc": 1, "dsc": "(Lorg/apache/maven/repository/internal/ModelCacheFactory;)Lorg/apache/maven/repository/internal/DefaultArtifactDescriptorReader;"}, {"nme": "readArtifactDescriptor", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/ArtifactDescriptorRequest;)Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;", "exs": ["org/eclipse/aether/resolution/ArtifactDescriptorException"]}, {"nme": "loadPom", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/ArtifactDescriptorRequest;Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;)Lorg/apache/maven/model/Model;", "exs": ["org/eclipse/aether/resolution/ArtifactDescriptorException"]}, {"nme": "toProperties", "acc": 2, "dsc": "(Ljava/util/Map;Ljava/util/Map;)Ljava/util/Properties;", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)Ljava/util/Properties;"}, {"nme": "getRelocation", "acc": 2, "dsc": "(Lorg/apache/maven/model/Model;)Lorg/apache/maven/model/Relocation;"}, {"nme": "missingDescriptor", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/artifact/Artifact;<PERSON><PERSON><PERSON>/lang/Exception;)V"}, {"nme": "invalidDescriptor", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/artifact/Artifact;<PERSON><PERSON><PERSON>/lang/Exception;)V"}, {"nme": "getPolicy", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/resolution/ArtifactDescriptorRequest;)I"}], "flds": [{"acc": 2, "nme": "remoteRepositoryManager", "dsc": "Lorg/eclipse/aether/impl/RemoteRepositoryManager;"}, {"acc": 2, "nme": "versionResolver", "dsc": "Lorg/eclipse/aether/impl/VersionResolver;"}, {"acc": 2, "nme": "versionRangeResolver", "dsc": "Lorg/eclipse/aether/impl/VersionRangeResolver;"}, {"acc": 2, "nme": "artifactResolver", "dsc": "Lorg/eclipse/aether/impl/ArtifactResolver;"}, {"acc": 2, "nme": "repositoryEventDispatcher", "dsc": "Lorg/eclipse/aether/impl/RepositoryEventDispatcher;"}, {"acc": 2, "nme": "modelBuilder", "dsc": "Lorg/apache/maven/model/building/ModelBuilder;"}, {"acc": 2, "nme": "modelCacheFactory", "dsc": "Lorg/apache/maven/repository/internal/ModelCacheFactory;"}, {"acc": 18, "nme": "artifactDescriptorReaderDelegate", "dsc": "Lorg/apache/maven/repository/internal/ArtifactDescriptorReaderDelegate;"}], "vanns": [{"dsc": "Ljavax/inject/Named;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/repository/internal/DefaultVersionResolver$VersionInfo.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/repository/internal/DefaultVersionResolver$VersionInfo", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;Lorg/eclipse/aether/repository/ArtifactRepository;)V"}, {"nme": "isOutdated", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}], "flds": [{"acc": 0, "nme": "timestamp", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "repository", "dsc": "Lorg/eclipse/aether/repository/ArtifactRepository;"}]}, "org/apache/maven/repository/internal/PluginsMetadata.class": {"ver": 52, "acc": 48, "nme": "org/apache/maven/repository/internal/PluginsMetadata", "super": "org/apache/maven/repository/internal/MavenMetadata", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/apache/maven/repository/internal/PluginsMetadata$PluginInfo;Ljava/util/Date;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(Lorg/apache/maven/repository/internal/PluginsMetadata$PluginInfo;Ljava/io/File;Ljava/util/Date;)V"}, {"nme": "createRepositoryMetadata", "acc": 10, "dsc": "(Lorg/apache/maven/repository/internal/PluginsMetadata$PluginInfo;)Lorg/apache/maven/artifact/repository/metadata/Metadata;"}, {"nme": "merge", "acc": 4, "dsc": "(Lorg/apache/maven/artifact/repository/metadata/Metadata;)V"}, {"nme": "setFile", "acc": 1, "dsc": "(Ljava/io/File;)Lorg/apache/maven/repository/internal/MavenMetadata;"}, {"nme": "getGroupId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getArtifactId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getNature", "acc": 1, "dsc": "()Lorg/eclipse/aether/metadata/Metadata$Nature;"}, {"nme": "setFile", "acc": 4161, "dsc": "(Ljava/io/File;)Lorg/eclipse/aether/metadata/Metadata;"}, {"nme": "lambda$merge$1", "acc": 4106, "dsc": "(Ljava/util/LinkedHashMap;Lorg/apache/maven/artifact/repository/metadata/Plugin;)V"}, {"nme": "lambda$merge$0", "acc": 4106, "dsc": "(Ljava/util/LinkedHashMap;Lorg/apache/maven/artifact/repository/metadata/Plugin;)V"}], "flds": [{"acc": 18, "nme": "pluginInfo", "dsc": "Lorg/apache/maven/repository/internal/PluginsMetadata$PluginInfo;"}]}, "org/apache/maven/repository/internal/ArtifactDescriptorReaderDelegate.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/repository/internal/ArtifactDescriptorReaderDelegate", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "populate<PERSON><PERSON>ult", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;Lorg/apache/maven/model/Model;)V"}, {"nme": "convert", "acc": 2, "dsc": "(Lorg/apache/maven/model/Dependency;Lorg/eclipse/aether/artifact/ArtifactTypeRegistry;)Lorg/eclipse/aether/graph/Dependency;"}, {"nme": "convert", "acc": 2, "dsc": "(Lorg/apache/maven/model/Exclusion;)Lorg/eclipse/aether/graph/Exclusion;"}, {"nme": "setArtifactProperties", "acc": 2, "dsc": "(Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;Lorg/apache/maven/model/Model;)V"}], "flds": []}, "org/apache/maven/repository/internal/MavenResolverModule.class": {"ver": 52, "acc": 131121, "nme": "org/apache/maven/repository/internal/MavenResolverModule", "super": "com/google/inject/AbstractModule", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "configure", "acc": 4, "dsc": "()V"}, {"nme": "provideMetadataGeneratorFactories", "acc": 0, "dsc": "(Lorg/eclipse/aether/impl/MetadataGeneratorFactory;Lorg/eclipse/aether/impl/MetadataGeneratorFactory;)Ljava/util/Set;", "sig": "(Lorg/eclipse/aether/impl/MetadataGeneratorFactory;Lorg/eclipse/aether/impl/MetadataGeneratorFactory;)Ljava/util/Set<Lorg/eclipse/aether/impl/MetadataGeneratorFactory;>;", "vanns": [{"dsc": "Lcom/google/inject/Provides;"}, {"dsc": "Ljavax/inject/Singleton;"}]}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "org/apache/maven/repository/internal/PluginsMetadataGeneratorFactory.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/repository/internal/PluginsMetadataGeneratorFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "newInstance", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/installation/InstallRequest;)Lorg/eclipse/aether/impl/MetadataGenerator;"}, {"nme": "newInstance", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/deployment/DeployRequest;)Lorg/eclipse/aether/impl/MetadataGenerator;"}, {"nme": "getPriority", "acc": 1, "dsc": "()F"}], "flds": [{"acc": 25, "nme": "NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "plugins"}], "vanns": [{"dsc": "Ljavax/inject/Named;", "vals": ["value", "plugins"]}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/repository/internal/ModelCacheFactory.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/repository/internal/ModelCacheFactory", "super": "java/lang/Object", "mthds": [{"nme": "createCache", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lorg/apache/maven/model/building/ModelCache;"}], "flds": []}, "org/apache/maven/repository/internal/RemoteSnapshotMetadataGenerator.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/repository/internal/RemoteSnapshotMetadataGenerator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/deployment/DeployRequest;)V"}, {"nme": "prepare", "acc": 1, "dsc": "(L<PERSON><PERSON>/util/Collection;)Ljava/util/Collection;", "sig": "(Ljava/util/Collection<+Lorg/eclipse/aether/artifact/Artifact;>;)Ljava/util/Collection<+Lorg/eclipse/aether/metadata/Metadata;>;"}, {"nme": "transformArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "finish", "acc": 1, "dsc": "(L<PERSON><PERSON>/util/Collection;)Ljava/util/Collection;", "sig": "(Ljava/util/Collection<+Lorg/eclipse/aether/artifact/Artifact;>;)Ljava/util/Collection<+Lorg/eclipse/aether/metadata/Metadata;>;"}], "flds": [{"acc": 18, "nme": "snapshots", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/repository/internal/RemoteSnapshotMetadata;>;"}, {"acc": 18, "nme": "legacyFormat", "dsc": "Z"}, {"acc": 18, "nme": "timestamp", "dsc": "<PERSON><PERSON><PERSON>/util/Date;"}]}, "org/apache/maven/repository/internal/LocalSnapshotMetadataGenerator.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/repository/internal/LocalSnapshotMetadataGenerator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/installation/InstallRequest;)V"}, {"nme": "prepare", "acc": 1, "dsc": "(L<PERSON><PERSON>/util/Collection;)Ljava/util/Collection;", "sig": "(Ljava/util/Collection<+Lorg/eclipse/aether/artifact/Artifact;>;)Ljava/util/Collection<+Lorg/eclipse/aether/metadata/Metadata;>;"}, {"nme": "transformArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "finish", "acc": 1, "dsc": "(L<PERSON><PERSON>/util/Collection;)Ljava/util/Collection;", "sig": "(Ljava/util/Collection<+Lorg/eclipse/aether/artifact/Artifact;>;)Ljava/util/Collection<+Lorg/eclipse/aether/metadata/Metadata;>;"}], "flds": [{"acc": 2, "nme": "snapshots", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/repository/internal/LocalSnapshotMetadata;>;"}, {"acc": 18, "nme": "legacyFormat", "dsc": "Z"}, {"acc": 18, "nme": "timestamp", "dsc": "<PERSON><PERSON><PERSON>/util/Date;"}]}, "org/apache/maven/repository/internal/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/apache/maven/repository/internal/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/apache/maven/repository/internal/DefaultVersionResolver$Record.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/repository/internal/DefaultVersionResolver$Record", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/eclipse/aether/repository/ArtifactRepository;)V"}], "flds": [{"acc": 16, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 16, "nme": "repoId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 16, "nme": "repoClass", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}]}, "org/apache/maven/repository/internal/MavenWorkspaceReader.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/repository/internal/MavenWorkspaceReader", "super": "java/lang/Object", "mthds": [{"nme": "findModel", "acc": 1025, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/apache/maven/model/Model;"}], "flds": []}, "org/apache/maven/repository/internal/DefaultModelCache.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/repository/internal/DefaultModelCache", "super": "java/lang/Object", "mthds": [{"nme": "newInstance", "acc": 9, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lorg/apache/maven/model/building/ModelCache;"}, {"nme": "<init>", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)V"}, {"nme": "get", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;L<PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "put", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;L<PERSON><PERSON>/lang/Object;)V"}], "flds": [{"acc": 18, "nme": "session", "dsc": "Lorg/eclipse/aether/RepositorySystemSession;"}, {"acc": 18, "nme": "cache", "dsc": "Lorg/eclipse/aether/RepositoryCache;"}]}, "org/apache/maven/repository/internal/VersionsMetadata.class": {"ver": 52, "acc": 48, "nme": "org/apache/maven/repository/internal/VersionsMetadata", "super": "org/apache/maven/repository/internal/MavenMetadata", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/util/Date;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/io/File;Ljava/util/Date;)V"}, {"nme": "createRepositoryMetadata", "acc": 10, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/apache/maven/artifact/repository/metadata/Metadata;"}, {"nme": "merge", "acc": 4, "dsc": "(Lorg/apache/maven/artifact/repository/metadata/Metadata;)V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 9, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "setFile", "acc": 1, "dsc": "(Ljava/io/File;)Lorg/apache/maven/repository/internal/MavenMetadata;"}, {"nme": "getGroupId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getArtifactId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getNature", "acc": 1, "dsc": "()Lorg/eclipse/aether/metadata/Metadata$Nature;"}, {"nme": "setFile", "acc": 4161, "dsc": "(Ljava/io/File;)Lorg/eclipse/aether/metadata/Metadata;"}], "flds": [{"acc": 18, "nme": "artifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}]}, "org/apache/maven/repository/internal/RemoteSnapshotMetadata.class": {"ver": 52, "acc": 48, "nme": "org/apache/maven/repository/internal/RemoteSnapshotMetadata", "super": "org/apache/maven/repository/internal/MavenSnapshotMetadata", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;ZLjava/util/Date;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(Lorg/apache/maven/artifact/repository/metadata/Metadata;Ljava/io/File;ZLjava/util/Date;)V"}, {"nme": "setFile", "acc": 1, "dsc": "(Ljava/io/File;)Lorg/apache/maven/repository/internal/MavenMetadata;"}, {"nme": "getExpandedVersion", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lja<PERSON>/lang/String;"}, {"nme": "merge", "acc": 4, "dsc": "(Lorg/apache/maven/artifact/repository/metadata/Metadata;)V"}, {"nme": "getBuildNumber", "acc": 10, "dsc": "(Lorg/apache/maven/artifact/repository/metadata/Metadata;)I"}, {"nme": "setFile", "acc": 4161, "dsc": "(Ljava/io/File;)Lorg/eclipse/aether/metadata/Metadata;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "DEFAULT_SNAPSHOT_TIMESTAMP_FORMAT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "yyyyMMdd.HHmmss"}, {"acc": 25, "nme": "DEFAULT_SNAPSHOT_TIME_ZONE", "dsc": "Ljava/util/TimeZone;"}, {"acc": 18, "nme": "versions", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lorg/apache/maven/artifact/repository/metadata/SnapshotVersion;>;"}]}, "org/apache/maven/repository/internal/VersionsMetadataGeneratorFactory.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/repository/internal/VersionsMetadataGeneratorFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "newInstance", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/installation/InstallRequest;)Lorg/eclipse/aether/impl/MetadataGenerator;"}, {"nme": "newInstance", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/deployment/DeployRequest;)Lorg/eclipse/aether/impl/MetadataGenerator;"}, {"nme": "getPriority", "acc": 1, "dsc": "()F"}], "flds": [{"acc": 25, "nme": "NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "versions"}], "vanns": [{"dsc": "Ljavax/inject/Named;", "vals": ["value", "versions"]}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/repository/internal/DefaultModelCacheFactory.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/repository/internal/DefaultModelCacheFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "createCache", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lorg/apache/maven/model/building/ModelCache;"}], "flds": [], "vanns": [{"dsc": "Ljavax/inject/Singleton;"}, {"dsc": "Ljavax/inject/Named;"}]}, "org/apache/maven/repository/internal/SnapshotMetadataGeneratorFactory.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/repository/internal/SnapshotMetadataGeneratorFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "newInstance", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/installation/InstallRequest;)Lorg/eclipse/aether/impl/MetadataGenerator;"}, {"nme": "newInstance", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/deployment/DeployRequest;)Lorg/eclipse/aether/impl/MetadataGenerator;"}, {"nme": "getPriority", "acc": 1, "dsc": "()F"}], "flds": [{"acc": 25, "nme": "NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "snapshot"}], "vanns": [{"dsc": "Ljavax/inject/Named;", "vals": ["value", "snapshot"]}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/repository/internal/VersionsMetadataGenerator.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/repository/internal/VersionsMetadataGenerator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/installation/InstallRequest;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/deployment/DeployRequest;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection;)V", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection<+Lorg/eclipse/aether/metadata/Metadata;>;)V"}, {"nme": "prepare", "acc": 1, "dsc": "(L<PERSON><PERSON>/util/Collection;)Ljava/util/Collection;", "sig": "(Ljava/util/Collection<+Lorg/eclipse/aether/artifact/Artifact;>;)Ljava/util/Collection<+Lorg/eclipse/aether/metadata/Metadata;>;"}, {"nme": "transformArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "finish", "acc": 1, "dsc": "(L<PERSON><PERSON>/util/Collection;)Ljava/util/Collection;", "sig": "(Ljava/util/Collection<+Lorg/eclipse/aether/artifact/Artifact;>;)Ljava/util/Collection<+Lorg/eclipse/aether/metadata/Metadata;>;"}], "flds": [{"acc": 18, "nme": "versions", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/repository/internal/VersionsMetadata;>;"}, {"acc": 18, "nme": "processedVersions", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/repository/internal/VersionsMetadata;>;"}, {"acc": 18, "nme": "timestamp", "dsc": "<PERSON><PERSON><PERSON>/util/Date;"}]}, "org/apache/maven/repository/internal/PluginsMetadataGenerator.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/repository/internal/PluginsMetadataGenerator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/installation/InstallRequest;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/deployment/DeployRequest;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection;)V", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/Collection<+Lorg/eclipse/aether/metadata/Metadata;>;)V"}, {"nme": "prepare", "acc": 1, "dsc": "(L<PERSON><PERSON>/util/Collection;)Ljava/util/Collection;", "sig": "(Ljava/util/Collection<+Lorg/eclipse/aether/artifact/Artifact;>;)Ljava/util/Collection<+Lorg/eclipse/aether/metadata/Metadata;>;"}, {"nme": "transformArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "finish", "acc": 1, "dsc": "(L<PERSON><PERSON>/util/Collection;)Ljava/util/Collection;", "sig": "(Ljava/util/Collection<+Lorg/eclipse/aether/artifact/Artifact;>;)Ljava/util/Collection<+Lorg/eclipse/aether/metadata/Metadata;>;"}, {"nme": "extractPluginInfo", "acc": 2, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/apache/maven/repository/internal/PluginsMetadata$PluginInfo;"}], "flds": [{"acc": 26, "nme": "PLUGIN_DESCRIPTOR_LOCATION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "META-INF/maven/plugin.xml"}, {"acc": 18, "nme": "processedPlugins", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/repository/internal/PluginsMetadata;>;"}, {"acc": 18, "nme": "timestamp", "dsc": "<PERSON><PERSON><PERSON>/util/Date;"}]}, "org/apache/maven/repository/internal/RelocatedArtifact.class": {"ver": 52, "acc": 49, "nme": "org/apache/maven/repository/internal/RelocatedArtifact", "super": "org/eclipse/aether/artifact/AbstractArtifact", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "getGroupId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getArtifactId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setVersion", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "setFile", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "setProperties", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Lorg/eclipse/aether/artifact/Artifact;", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "getClassifier", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getExtension", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getFile", "acc": 1, "dsc": "()Ljava/io/File;"}, {"nme": "getProperty", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getProperties", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "getMessage", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "artifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}, {"acc": 18, "nme": "groupId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "artifactId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "message", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/apache/maven/repository/internal/LocalSnapshotMetadata.class": {"ver": 52, "acc": 48, "nme": "org/apache/maven/repository/internal/LocalSnapshotMetadata", "super": "org/apache/maven/repository/internal/MavenMetadata", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;ZLjava/util/Date;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(Lorg/apache/maven/artifact/repository/metadata/Metadata;Ljava/io/File;ZLjava/util/Date;)V"}, {"nme": "createMetadata", "acc": 10, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Z)Lorg/apache/maven/artifact/repository/metadata/Metadata;"}, {"nme": "bind", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)V"}, {"nme": "setFile", "acc": 1, "dsc": "(Ljava/io/File;)Lorg/apache/maven/repository/internal/MavenMetadata;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 9, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "merge", "acc": 4, "dsc": "(Lorg/apache/maven/artifact/repository/metadata/Metadata;)V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getGroupId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getArtifactId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getNature", "acc": 1, "dsc": "()Lorg/eclipse/aether/metadata/Metadata$Nature;"}, {"nme": "setFile", "acc": 4161, "dsc": "(Ljava/io/File;)Lorg/eclipse/aether/metadata/Metadata;"}], "flds": [{"acc": 18, "nme": "artifacts", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lorg/eclipse/aether/artifact/Artifact;>;"}, {"acc": 18, "nme": "legacyFormat", "dsc": "Z"}]}, "org/apache/maven/repository/internal/DefaultModelResolver.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/repository/internal/DefaultModelResolver", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lja<PERSON>/lang/String;Lorg/eclipse/aether/impl/ArtifactResolver;Lorg/eclipse/aether/impl/VersionRangeResolver;Lorg/eclipse/aether/impl/RemoteRepositoryManager;Ljava/util/List;)V", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lja<PERSON>/lang/String;Lorg/eclipse/aether/impl/ArtifactResolver;Lorg/eclipse/aether/impl/VersionRangeResolver;Lorg/eclipse/aether/impl/RemoteRepositoryManager;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(Lorg/apache/maven/repository/internal/DefaultModelResolver;)V"}, {"nme": "addRepository", "acc": 1, "dsc": "(Lorg/apache/maven/model/Repository;)V", "exs": ["org/apache/maven/model/resolution/InvalidRepositoryException"]}, {"nme": "addRepository", "acc": 1, "dsc": "(Lorg/apache/maven/model/Repository;Z)V", "exs": ["org/apache/maven/model/resolution/InvalidRepositoryException"]}, {"nme": "removeMatchingRepository", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;<PERSON><PERSON><PERSON>/lang/String;)V", "sig": "(Ljava/lang/Iterable<Lorg/eclipse/aether/repository/RemoteRepository;>;Ljava/lang/String;)V"}, {"nme": "newCopy", "acc": 1, "dsc": "()Lorg/apache/maven/model/resolution/ModelResolver;"}, {"nme": "resolveModel", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;)Lorg/apache/maven/model/building/ModelSource;", "exs": ["org/apache/maven/model/resolution/UnresolvableModelException"]}, {"nme": "resolveModel", "acc": 1, "dsc": "(Lorg/apache/maven/model/Parent;)Lorg/apache/maven/model/building/ModelSource;", "exs": ["org/apache/maven/model/resolution/UnresolvableModelException"]}, {"nme": "resolveModel", "acc": 1, "dsc": "(Lorg/apache/maven/model/Dependency;)Lorg/apache/maven/model/building/ModelSource;", "exs": ["org/apache/maven/model/resolution/UnresolvableModelException"]}], "flds": [{"acc": 18, "nme": "session", "dsc": "Lorg/eclipse/aether/RepositorySystemSession;"}, {"acc": 18, "nme": "trace", "dsc": "Lorg/eclipse/aether/RequestTrace;"}, {"acc": 18, "nme": "context", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "repositories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"acc": 18, "nme": "externalRepositories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"acc": 18, "nme": "resolver", "dsc": "Lorg/eclipse/aether/impl/ArtifactResolver;"}, {"acc": 18, "nme": "versionRangeResolver", "dsc": "Lorg/eclipse/aether/impl/VersionRangeResolver;"}, {"acc": 18, "nme": "remoteRepositoryManager", "dsc": "Lorg/eclipse/aether/impl/RemoteRepositoryManager;"}, {"acc": 18, "nme": "repositoryIds", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}]}, "org/apache/maven/repository/internal/PluginsMetadata$PluginInfo.class": {"ver": 52, "acc": 48, "nme": "org/apache/maven/repository/internal/PluginsMetadata$PluginInfo", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "access$000", "acc": 4104, "dsc": "(Lorg/apache/maven/repository/internal/PluginsMetadata$PluginInfo;)Ljava/lang/String;"}, {"nme": "access$100", "acc": 4104, "dsc": "(Lorg/apache/maven/repository/internal/PluginsMetadata$PluginInfo;)Ljava/lang/String;"}, {"nme": "access$200", "acc": 4104, "dsc": "(Lorg/apache/maven/repository/internal/PluginsMetadata$PluginInfo;)Ljava/lang/String;"}], "flds": [{"acc": 16, "nme": "groupId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "artifactId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "goalPrefix", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/apache/maven/repository/internal/DefaultVersionRangeResolver.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/repository/internal/DefaultVersionRangeResolver", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/MetadataResolver;Lorg/eclipse/aether/spi/synccontext/SyncContextFactory;Lorg/eclipse/aether/impl/RepositoryEventDispatcher;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "initService", "acc": 131073, "dsc": "(Lorg/eclipse/aether/spi/locator/ServiceLocator;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "setMetadataResolver", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/MetadataResolver;)Lorg/apache/maven/repository/internal/DefaultVersionRangeResolver;"}, {"nme": "setSyncContextFactory", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/synccontext/SyncContextFactory;)Lorg/apache/maven/repository/internal/DefaultVersionRangeResolver;"}, {"nme": "setRepositoryEventDispatcher", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/RepositoryEventDispatcher;)Lorg/apache/maven/repository/internal/DefaultVersionRangeResolver;"}, {"nme": "resolveVersionRange", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/VersionRangeRequest;)Lorg/eclipse/aether/resolution/VersionRangeResult;", "exs": ["org/eclipse/aether/resolution/VersionRangeResolutionException"]}, {"nme": "getVersions", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/VersionRangeResult;Lorg/eclipse/aether/resolution/VersionRangeRequest;)Ljava/util/Map;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/VersionRangeResult;Lorg/eclipse/aether/resolution/VersionRangeRequest;)Ljava/util/Map<Ljava/lang/String;Lorg/eclipse/aether/repository/ArtifactRepository;>;"}, {"nme": "readVersions", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/ArtifactRepository;Lorg/eclipse/aether/resolution/VersionRangeResult;)Lorg/apache/maven/artifact/repository/metadata/Versioning;"}, {"nme": "filterVersionsByRepositoryType", "acc": 2, "dsc": "(Lorg/apache/maven/artifact/repository/metadata/Versioning;Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/apache/maven/artifact/repository/metadata/Versioning;"}, {"nme": "invalidMetadata", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/ArtifactRepository;Ljava/lang/Exception;)V"}], "flds": [{"acc": 26, "nme": "MAVEN_METADATA_XML", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "maven-metadata.xml"}, {"acc": 2, "nme": "metadataResolver", "dsc": "Lorg/eclipse/aether/impl/MetadataResolver;"}, {"acc": 2, "nme": "syncContextFactory", "dsc": "Lorg/eclipse/aether/spi/synccontext/SyncContextFactory;"}, {"acc": 2, "nme": "repositoryEventDispatcher", "dsc": "Lorg/eclipse/aether/impl/RepositoryEventDispatcher;"}], "vanns": [{"dsc": "Ljavax/inject/Named;"}, {"dsc": "Ljavax/inject/Singleton;"}]}, "org/apache/maven/repository/internal/ArtifactDescriptorUtils.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/repository/internal/ArtifactDescriptorUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "toPomArtifact", "acc": 9, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "toRemoteRepository", "acc": 9, "dsc": "(Lorg/apache/maven/model/Repository;)Lorg/eclipse/aether/repository/RemoteRepository;"}, {"nme": "toRepositoryPolicy", "acc": 9, "dsc": "(Lorg/apache/maven/model/RepositoryPolicy;)Lorg/eclipse/aether/repository/RepositoryPolicy;"}], "flds": []}, "org/apache/maven/repository/internal/MavenMetadata.class": {"ver": 52, "acc": 1056, "nme": "org/apache/maven/repository/internal/MavenMetadata", "super": "org/eclipse/aether/metadata/AbstractMetadata", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Lorg/apache/maven/artifact/repository/metadata/Metadata;Ljava/io/File;Ljava/util/Date;)V"}, {"nme": "getType", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getFile", "acc": 1, "dsc": "()Ljava/io/File;"}, {"nme": "merge", "acc": 1, "dsc": "(Ljava/io/File;Ljava/io/File;)V", "exs": ["org/eclipse/aether/RepositoryException"]}, {"nme": "isMerged", "acc": 1, "dsc": "()Z"}, {"nme": "merge", "acc": 1028, "dsc": "(Lorg/apache/maven/artifact/repository/metadata/Metadata;)V"}, {"nme": "read", "acc": 8, "dsc": "(Ljava/io/File;)Lorg/apache/maven/artifact/repository/metadata/Metadata;", "exs": ["org/eclipse/aether/RepositoryException"]}, {"nme": "write", "acc": 2, "dsc": "(Ljava/io/File;Lorg/apache/maven/artifact/repository/metadata/Metadata;)V", "exs": ["org/eclipse/aether/RepositoryException"]}, {"nme": "getProperties", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "setProperties", "acc": 1, "dsc": "(Ljava/util/Map;)Lorg/eclipse/aether/metadata/Metadata;", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)Lorg/eclipse/aether/metadata/Metadata;"}], "flds": [{"acc": 24, "nme": "MAVEN_METADATA_XML", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "maven-metadata.xml"}, {"acc": 4, "nme": "metadata", "dsc": "Lorg/apache/maven/artifact/repository/metadata/Metadata;"}, {"acc": 18, "nme": "file", "dsc": "Ljava/io/File;"}, {"acc": 20, "nme": "timestamp", "dsc": "<PERSON><PERSON><PERSON>/util/Date;"}, {"acc": 2, "nme": "merged", "dsc": "Z"}]}, "org/apache/maven/repository/internal/MavenRepositorySystemUtils.class": {"ver": 52, "acc": 49, "nme": "org/apache/maven/repository/internal/MavenRepositorySystemUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "newServiceLocator", "acc": 131081, "dsc": "()Lorg/eclipse/aether/impl/DefaultServiceLocator;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "newSession", "acc": 9, "dsc": "()Lorg/eclipse/aether/DefaultRepositorySystemSession;"}], "flds": []}, "org/apache/maven/repository/internal/DefaultModelCache$Key.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/repository/internal/DefaultModelCache$Key", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "groupId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "artifactId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "tag", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "hash", "dsc": "I"}]}, "org/apache/maven/repository/internal/DefaultVersionResolver$Key.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/repository/internal/DefaultVersionResolver$Key", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/VersionRequest;)V"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "groupId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "artifactId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "classifier", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "extension", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "context", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "localRepo", "dsc": "Ljava/io/File;"}, {"acc": 18, "nme": "workspace", "dsc": "Lorg/eclipse/aether/repository/WorkspaceRepository;"}, {"acc": 18, "nme": "repositories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"acc": 18, "nme": "hashCode", "dsc": "I"}]}, "org/apache/maven/repository/internal/MavenSnapshotMetadata.class": {"ver": 52, "acc": 1056, "nme": "org/apache/maven/repository/internal/MavenSnapshotMetadata", "super": "org/apache/maven/repository/internal/MavenMetadata", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Lorg/apache/maven/artifact/repository/metadata/Metadata;Ljava/io/File;ZLjava/util/Date;)V"}, {"nme": "createRepositoryMetadata", "acc": 12, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Z)Lorg/apache/maven/artifact/repository/metadata/Metadata;"}, {"nme": "bind", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 9, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getGroupId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getArtifactId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getNature", "acc": 1, "dsc": "()Lorg/eclipse/aether/metadata/Metadata$Nature;"}], "flds": [{"acc": 24, "nme": "SNAPSHOT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "SNAPSHOT"}, {"acc": 20, "nme": "artifacts", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lorg/eclipse/aether/artifact/Artifact;>;"}, {"acc": 20, "nme": "legacyFormat", "dsc": "Z"}]}, "org/apache/maven/repository/internal/DefaultVersionResolver.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/repository/internal/DefaultVersionResolver", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/MetadataResolver;Lorg/eclipse/aether/spi/synccontext/SyncContextFactory;Lorg/eclipse/aether/impl/RepositoryEventDispatcher;)V", "vanns": [{"dsc": "Ljavax/inject/Inject;"}]}, {"nme": "initService", "acc": 131073, "dsc": "(Lorg/eclipse/aether/spi/locator/ServiceLocator;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "setMetadataResolver", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/MetadataResolver;)Lorg/apache/maven/repository/internal/DefaultVersionResolver;"}, {"nme": "setSyncContextFactory", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/synccontext/SyncContextFactory;)Lorg/apache/maven/repository/internal/DefaultVersionResolver;"}, {"nme": "setRepositoryEventDispatcher", "acc": 1, "dsc": "(Lorg/eclipse/aether/impl/RepositoryEventDispatcher;)Lorg/apache/maven/repository/internal/DefaultVersionResolver;"}, {"nme": "resolveVersion", "acc": 1, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/resolution/VersionRequest;)Lorg/eclipse/aether/resolution/VersionResult;", "exs": ["org/eclipse/aether/resolution/VersionResolutionException"]}, {"nme": "resolve", "acc": 2, "dsc": "(Lorg/eclipse/aether/resolution/VersionResult;Ljava/util/Map;Ljava/lang/String;)Z", "sig": "(Lorg/eclipse/aether/resolution/VersionResult;Ljava/util/Map<Ljava/lang/String;Lorg/apache/maven/repository/internal/DefaultVersionResolver$VersionInfo;>;Ljava/lang/String;)Z"}, {"nme": "readVersions", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/ArtifactRepository;Lorg/eclipse/aether/resolution/VersionResult;)Lorg/apache/maven/artifact/repository/metadata/Versioning;"}, {"nme": "invalidMetadata", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/RequestTrace;Lorg/eclipse/aether/metadata/Metadata;Lorg/eclipse/aether/repository/ArtifactRepository;Ljava/lang/Exception;)V"}, {"nme": "merge", "acc": 2, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/util/Map;Lorg/apache/maven/artifact/repository/metadata/Versioning;Lorg/eclipse/aether/repository/ArtifactRepository;)V", "sig": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/util/Map<Ljava/lang/String;Lorg/apache/maven/repository/internal/DefaultVersionResolver$VersionInfo;>;Lorg/apache/maven/artifact/repository/metadata/Versioning;Lorg/eclipse/aether/repository/ArtifactRepository;)V"}, {"nme": "merge", "acc": 2, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/Map;Ljava/lang/String;Ljava/lang/String;Lorg/eclipse/aether/repository/ArtifactRepository;)V", "sig": "(Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Lorg/apache/maven/repository/internal/DefaultVersionResolver$VersionInfo;>;Ljava/lang/String;Ljava/lang/String;Lorg/eclipse/aether/repository/ArtifactRepository;)V"}, {"nme": "merge", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;<PERSON>java/lang/String;Lja<PERSON>/lang/String;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;Lorg/apache/maven/repository/internal/DefaultVersionResolver$VersionInfo;>;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getRepository", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/List;Ljava/lang/Class;Ljava/lang/String;)Lorg/eclipse/aether/repository/ArtifactRepository;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;Ljava/lang/Class<*>;Ljava/lang/String;)Lorg/eclipse/aether/repository/ArtifactRepository;"}, {"nme": "isSafelyCacheable", "acc": 2, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;)Z"}], "flds": [{"acc": 26, "nme": "MAVEN_METADATA_XML", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "maven-metadata.xml"}, {"acc": 26, "nme": "RELEASE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "RELEASE"}, {"acc": 26, "nme": "LATEST", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "LATEST"}, {"acc": 26, "nme": "SNAPSHOT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "SNAPSHOT"}, {"acc": 2, "nme": "metadataResolver", "dsc": "Lorg/eclipse/aether/impl/MetadataResolver;"}, {"acc": 2, "nme": "syncContextFactory", "dsc": "Lorg/eclipse/aether/spi/synccontext/SyncContextFactory;"}, {"acc": 2, "nme": "repositoryEventDispatcher", "dsc": "Lorg/eclipse/aether/impl/RepositoryEventDispatcher;"}], "vanns": [{"dsc": "Ljavax/inject/Named;"}, {"dsc": "Ljavax/inject/Singleton;"}]}}}}