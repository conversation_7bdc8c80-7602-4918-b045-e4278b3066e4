{"md5": "f1c2157458ce8a9ea2aef4d23b986f11", "sha2": "be5c9943891ccee0980ac9becb34e50539e42c6b", "sha256": "20f8e6db34d722a4acccbedcc9b6c02e8ee6b3cab9350b06ab3d1c0b0b8b454f", "contents": {"classes": {"net/kyori/adventure/platform/facet/Facet$Chat.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/platform/facet/Facet$Chat", "super": "java/lang/Object", "mthds": [{"nme": "sendMessage", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/identity/Identity;Lja<PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TV;Lnet/kyori/adventure/identity/Identity;TM;Ljava/lang/Object;)V"}], "flds": []}, "net/kyori/adventure/platform/facet/FacetBase.class": {"ver": 52, "acc": 1057, "nme": "net/kyori/adventure/platform/facet/FacetBase", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(Ljava/lang/Class<+TV;>;)V"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "isApplicable", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z", "sig": "(TV;)Z"}], "flds": [{"acc": 20, "nme": "viewerClass", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<+TV;>;"}]}, "net/kyori/adventure/platform/facet/FacetAudienceProvider.class": {"ver": 52, "acc": 1057, "nme": "net/kyori/adventure/platform/facet/FacetAudienceProvider", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Lnet/kyori/adventure/text/renderer/ComponentRenderer;)V", "sig": "(Lnet/kyori/adventure/text/renderer/ComponentRenderer<Lnet/kyori/adventure/pointer/Pointered;>;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TV;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TV;)V"}, {"nme": "refresh<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TV;)V"}, {"nme": "createAudience", "acc": 1028, "dsc": "(Ljava/util/Collection;)Lnet/kyori/adventure/platform/facet/FacetAudience;", "sig": "(Ljava/util/Collection<TV;>;)TA;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "audiences", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Iterable;", "sig": "()Ljava/lang/Iterable<+Lnet/kyori/adventure/audience/Audience;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "all", "acc": 1, "dsc": "()Lnet/kyori/adventure/audience/Audience;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "console", "acc": 1, "dsc": "()Lnet/kyori/adventure/audience/Audience;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "players", "acc": 1, "dsc": "()Lnet/kyori/adventure/audience/Audience;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "player", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/UUID;)Lnet/kyori/adventure/audience/Audience;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "empty", "acc": 2, "dsc": "()Lnet/kyori/adventure/platform/facet/FacetAudience;", "sig": "()TA;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "filter", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Predicate;)Lnet/kyori/adventure/audience/Audience;", "sig": "(<PERSON><PERSON><PERSON>/util/function/Predicate<TV;>;)Lnet/kyori/adventure/audience/Audience;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "filterPointers", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Predicate;)Lnet/kyori/adventure/audience/Audience;", "sig": "(Lja<PERSON>/util/function/Predicate<Lnet/kyori/adventure/pointer/Pointered;>;)Lnet/kyori/adventure/audience/Audience;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "permission", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/audience/Audience;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "world", "acc": 1, "dsc": "(Lnet/kyori/adventure/key/Key;)Lnet/kyori/adventure/audience/Audience;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "server", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/audience/Audience;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "close", "acc": 1, "dsc": "()V"}, {"nme": "filter", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;<PERSON><PERSON><PERSON>/util/function/Predicate;<PERSON><PERSON><PERSON>/util/function/Function;)Ljava/lang/Iterable;", "sig": "<T:Ljava/lang/Object;V:Ljava/lang/Object;>(Ljava/lang/Iterable<TT;>;Ljava/util/function/Predicate<TT;>;Ljava/util/function/Function<TT;TV;>;)Ljava/lang/Iterable<TV;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "lambda$server$5", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lnet/kyori/adventure/pointer/Pointered;)Z"}, {"nme": "lambda$world$4", "acc": 4106, "dsc": "(Lnet/kyori/adventure/key/Key;Lnet/kyori/adventure/pointer/Pointered;)Z"}, {"nme": "lambda$permission$3", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lnet/kyori/adventure/pointer/Pointered;)Z"}, {"nme": "lambda$filterPointers$2", "acc": 4106, "dsc": "(Lja<PERSON>/util/function/Predicate;Ljava/util/Map$Entry;)Z"}, {"nme": "lambda$filter$1", "acc": 4106, "dsc": "(Lja<PERSON>/util/function/Predicate;Ljava/util/Map$Entry;)Z"}, {"nme": "lambda$addViewer$0", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lnet/kyori/adventure/platform/facet/FacetAudience;"}, {"nme": "access$000", "acc": 4104, "dsc": "(Lnet/kyori/adventure/platform/facet/FacetAudienceProvider;)Ljava/util/Set;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 28, "nme": "DEFAULT_LOCALE", "dsc": "Ljava/util/Locale;"}, {"acc": 20, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "Lnet/kyori/adventure/text/renderer/ComponentRenderer;", "sig": "Lnet/kyori/adventure/text/renderer/ComponentRenderer<Lnet/kyori/adventure/pointer/Pointered;>;"}, {"acc": 18, "nme": "console", "dsc": "Lnet/kyori/adventure/audience/Audience;"}, {"acc": 18, "nme": "player", "dsc": "Lnet/kyori/adventure/audience/Audience;"}, {"acc": 20, "nme": "viewers", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<TV;TA;>;"}, {"acc": 18, "nme": "players", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/util/UUID;TA;>;"}, {"acc": 18, "nme": "consoles", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<TA;>;"}, {"acc": 2, "nme": "empty", "dsc": "Lnet/kyori/adventure/platform/facet/FacetAudience;", "sig": "TA;"}, {"acc": 66, "nme": "closed", "dsc": "Z"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$Internal;"}]}, "net/kyori/adventure/platform/facet/FacetAudienceProvider$2.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/facet/FacetAudienceProvider$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;<PERSON><PERSON><PERSON>/util/function/Predicate;Ljava/util/function/Function;)V"}, {"nme": "iterator", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Iterator;", "sig": "()Ljava/util/Iterator<TV;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "for<PERSON>ach", "acc": 1, "dsc": "(Ljava/util/function/Consumer;)V", "sig": "(Ljava/util/function/Consumer<-TV;>;)V"}], "flds": [{"acc": 4112, "nme": "val$input", "dsc": "<PERSON><PERSON><PERSON>/lang/Iterable;"}, {"acc": 4112, "nme": "val$filter", "dsc": "<PERSON><PERSON><PERSON>/util/function/Predicate;"}, {"acc": 4112, "nme": "val$transformer", "dsc": "Ljava/util/function/Function;"}]}, "net/kyori/adventure/platform/facet/Facet$Book.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/platform/facet/Facet$Book", "super": "java/lang/Object", "mthds": [{"nme": "createBook", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Iterable;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>java/lang/String;Ljava/lang/Iterable<TM;>;)TB;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "openBook", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TV;TB;)V"}], "flds": []}, "net/kyori/adventure/platform/facet/Facet$FakeEntity.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/platform/facet/Facet$FakeEntity", "super": "java/lang/Object", "mthds": [{"nme": "teleport", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TV;TP;)V"}, {"nme": "metadata", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "invisible", "acc": 1025, "dsc": "(Z)V"}, {"nme": "health", "acc": 1025, "dsc": "(F)V"}, {"nme": "name", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/Component;)V"}, {"nme": "close", "acc": 1025, "dsc": "()V"}], "flds": []}, "net/kyori/adventure/platform/facet/Facet$ActionBar.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/platform/facet/Facet$ActionBar", "super": "java/lang/Object", "mthds": [{"nme": "sendMessage", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TV;TM;)V"}], "flds": []}, "net/kyori/adventure/platform/facet/Facet$BossBar$Builder.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/platform/facet/Facet$BossBar$Builder", "super": "java/lang/Object", "mthds": [{"nme": "createBossBar", "acc": 1025, "dsc": "(Ljava/util/Collection;)Lnet/kyori/adventure/platform/facet/Facet$BossBar;", "sig": "(Ljava/util/Collection<TV;>;)TB;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/FunctionalInterface;"}]}, "net/kyori/adventure/platform/facet/Facet$Position.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/platform/facet/Facet$Position", "super": "java/lang/Object", "mthds": [{"nme": "createPosition", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(TV;)TP;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "createPosition", "acc": 1025, "dsc": "(DDD)Ljava/lang/Object;", "sig": "(DDD)TP;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": []}, "net/kyori/adventure/platform/facet/package-info.class": {"ver": 52, "acc": 5632, "nme": "net/kyori/adventure/platform/facet/package-info", "super": "java/lang/Object", "mthds": [], "flds": [], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$Internal;"}]}, "net/kyori/adventure/platform/facet/FacetPointers$Type.class": {"ver": 52, "acc": 16433, "nme": "net/kyori/adventure/platform/facet/FacetPointers$Type", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lnet/kyori/adventure/platform/facet/FacetPointers$Type;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;)Lnet/kyori/adventure/platform/facet/FacetPointers$Type;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "PLAYER", "dsc": "Lnet/kyori/adventure/platform/facet/FacetPointers$Type;"}, {"acc": 16409, "nme": "CONSOLE", "dsc": "Lnet/kyori/adventure/platform/facet/FacetPointers$Type;"}, {"acc": 16409, "nme": "OTHER", "dsc": "Lnet/kyori/adventure/platform/facet/FacetPointers$Type;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lnet/kyori/adventure/platform/facet/FacetPointers$Type;"}]}, "net/kyori/adventure/platform/facet/Facet$TitlePacket.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/platform/facet/Facet$TitlePacket", "super": "java/lang/Object", "mthds": [], "flds": [{"acc": 25, "nme": "ACTION_TITLE", "dsc": "I", "val": 0}, {"acc": 25, "nme": "ACTION_SUBTITLE", "dsc": "I", "val": 1}, {"acc": 25, "nme": "ACTION_ACTIONBAR", "dsc": "I", "val": 2}, {"acc": 25, "nme": "ACTION_TIMES", "dsc": "I", "val": 3}, {"acc": 25, "nme": "ACTION_CLEAR", "dsc": "I", "val": 4}, {"acc": 25, "nme": "ACTION_RESET", "dsc": "I", "val": 5}]}, "net/kyori/adventure/platform/facet/Facet$EntitySound.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/platform/facet/Facet$EntitySound", "super": "java/lang/Object", "mthds": [{"nme": "createForSelf", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/sound/Sound;)<PERSON>java/lang/Object;", "sig": "(TV;Lnet/kyori/adventure/sound/Sound;)TM;"}, {"nme": "createForEmitter", "acc": 1025, "dsc": "(Lnet/kyori/adventure/sound/Sound;Lnet/kyori/adventure/sound/Sound$Emitter;)Ljava/lang/Object;", "sig": "(Lnet/kyori/adventure/sound/Sound;Lnet/kyori/adventure/sound/Sound$Emitter;)TM;"}, {"nme": "playSound", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TV;TM;)V"}], "flds": []}, "net/kyori/adventure/platform/facet/Facet$Sound.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/platform/facet/Facet$Sound", "super": "java/lang/Object", "mthds": [{"nme": "playSound", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/sound/Sound;<PERSON>java/lang/Object;)V", "sig": "(TV;Lnet/kyori/adventure/sound/Sound;TP;)V"}, {"nme": "stopSound", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/sound/SoundStop;)V", "sig": "(TV;Lnet/kyori/adventure/sound/SoundStop;)V"}], "flds": []}, "net/kyori/adventure/platform/facet/FacetPointers.class": {"ver": 52, "acc": 49, "nme": "net/kyori/adventure/platform/facet/FacetPointers", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "NAMESPACE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "adventure_platform"}, {"acc": 25, "nme": "SERVER", "dsc": "Lnet/kyori/adventure/pointer/Pointer;", "sig": "Lnet/kyori/adventure/pointer/Pointer<Ljava/lang/String;>;"}, {"acc": 25, "nme": "WORLD", "dsc": "Lnet/kyori/adventure/pointer/Pointer;", "sig": "Lnet/kyori/adventure/pointer/Pointer<Lnet/kyori/adventure/key/Key;>;"}, {"acc": 25, "nme": "TYPE", "dsc": "Lnet/kyori/adventure/pointer/Pointer;", "sig": "Lnet/kyori/adventure/pointer/Pointer<Lnet/kyori/adventure/platform/facet/FacetPointers$Type;>;"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$Internal;"}]}, "net/kyori/adventure/platform/facet/Facet$Title.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/platform/facet/Facet$Title", "super": "java/lang/Object", "mthds": [{"nme": "createTitleCollection", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TC;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "contributeTitle", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TC;TM;)V"}, {"nme": "contributeSubtitle", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TC;TM;)V"}, {"nme": "contributeTimes", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;III)V", "sig": "(TC;III)V"}, {"nme": "completeTitle", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(TC;)TT;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "showTitle", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TV;TT;)V"}, {"nme": "clearTitle", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TV;)V"}, {"nme": "resetTitle", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TV;)V"}, {"nme": "toTicks", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/time/Duration;)I"}], "flds": [{"acc": 25, "nme": "PROTOCOL_ACTION_BAR", "dsc": "I", "val": 310}, {"acc": 25, "nme": "MAX_SECONDS", "dsc": "J", "val": 461168601842738790}]}, "net/kyori/adventure/platform/facet/Facet.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/platform/facet/Facet", "super": "java/lang/Object", "mthds": [{"nme": "of", "acc": 137, "dsc": "([L<PERSON><PERSON>/util/function/Supplier;)Ljava/util/Collection;", "sig": "<V:Ljava/lang/Object;F::Lnet/kyori/adventure/platform/facet/Facet<+TV;>;>([Ljava/util/function/Supplier<TF;>;)Ljava/util/Collection<TF;>;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Safe<PERSON>rgs;"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "of", "acc": 9, "dsc": "(Lja<PERSON>/util/Collection;Ljava/lang/Object;)Lnet/kyori/adventure/platform/facet/Facet;", "sig": "<V:Ljava/lang/Object;F::Lnet/kyori/adventure/platform/facet/Facet<TV;>;>(Ljava/util/Collection<TF;>;TV;)TF;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "isApplicable", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z", "sig": "(TV;)Z"}], "flds": [], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$Internal;"}]}, "net/kyori/adventure/platform/facet/Knob.class": {"ver": 52, "acc": 49, "nme": "net/kyori/adventure/platform/facet/Knob", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "isEnabled", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)Z"}, {"nme": "logError", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/Object;)V"}, {"nme": "logMessage", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Lja<PERSON>/lang/Object;)V"}, {"nme": "logUnsupported", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "NAMESPACE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 25, "nme": "DEBUG", "dsc": "Z"}, {"acc": 26, "nme": "UNSUPPORTED", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/Object;>;"}, {"acc": 73, "nme": "OUT", "dsc": "Ljava/util/function/Consumer;", "sig": "Ljava/util/function/Consumer<Ljava/lang/String;>;"}, {"acc": 73, "nme": "ERR", "dsc": "Ljava/util/function/BiConsumer;", "sig": "Ljava/util/function/BiConsumer<Ljava/lang/String;Ljava/lang/Throwable;>;"}]}, "net/kyori/adventure/platform/facet/Facet$BossBarPacket.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/platform/facet/Facet$BossBarPacket", "super": "java/lang/Object", "mthds": [{"nme": "createColor", "acc": 1, "dsc": "(Lnet/kyori/adventure/bossbar/BossBar$Color;)I"}, {"nme": "createOverlay", "acc": 1, "dsc": "(Lnet/kyori/adventure/bossbar/BossBar$Overlay;)I"}, {"nme": "createFlag", "acc": 1, "dsc": "(B<PERSON><PERSON><PERSON>/util/Set;<PERSON><PERSON><PERSON>/util/Set;)B", "sig": "(BLjava/util/Set<Lnet/kyori/adventure/bossbar/BossBar$Flag;>;Ljava/util/Set<Lnet/kyori/adventure/bossbar/BossBar$Flag;>;)B"}], "flds": [{"acc": 25, "nme": "ACTION_ADD", "dsc": "I", "val": 0}, {"acc": 25, "nme": "ACTION_REMOVE", "dsc": "I", "val": 1}, {"acc": 25, "nme": "ACTION_HEALTH", "dsc": "I", "val": 2}, {"acc": 25, "nme": "ACTION_TITLE", "dsc": "I", "val": 3}, {"acc": 25, "nme": "ACTION_STYLE", "dsc": "I", "val": 4}, {"acc": 25, "nme": "ACTION_FLAG", "dsc": "I", "val": 5}]}, "net/kyori/adventure/platform/facet/Facet$BossBar.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/platform/facet/Facet$BossBar", "super": "java/lang/Object", "mthds": [{"nme": "bossBarInitialized", "acc": 1, "dsc": "(Lnet/kyori/adventure/bossbar/BossB<PERSON>;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TV;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TV;)V"}, {"nme": "isEmpty", "acc": 1025, "dsc": "()Z"}, {"nme": "close", "acc": 1025, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "PROTOCOL_BOSS_BAR", "dsc": "I", "val": 356}]}, "net/kyori/adventure/platform/facet/FacetAudienceProvider$1.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/facet/FacetAudienceProvider$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lnet/kyori/adventure/platform/facet/FacetAudienceProvider;)V"}, {"nme": "audiences", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Iterable;", "sig": "()Ljava/lang/Iterable<+Lnet/kyori/adventure/audience/Audience;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "pointers", "acc": 1, "dsc": "()Lnet/kyori/adventure/pointer/Pointers;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lnet/kyori/adventure/platform/facet/FacetAudienceProvider;"}]}, "net/kyori/adventure/platform/facet/FacetAudienceProvider$2$1.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/facet/FacetAudienceProvider$2$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lnet/kyori/adventure/platform/facet/FacetAudienceProvider$2;)V"}, {"nme": "populate", "acc": 2, "dsc": "()V"}, {"nme": "hasNext", "acc": 1, "dsc": "()Z"}, {"nme": "next", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TV;"}], "flds": [{"acc": 18, "nme": "parent", "dsc": "<PERSON><PERSON><PERSON>/util/Iterator;", "sig": "Ljava/util/Iterator<TT;>;"}, {"acc": 2, "nme": "next", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;", "sig": "TV;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lnet/kyori/adventure/platform/facet/FacetAudienceProvider$2;"}]}, "net/kyori/adventure/platform/facet/Facet$Pointers.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/platform/facet/Facet$Pointers", "super": "java/lang/Object", "mthds": [{"nme": "contributePointers", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/pointer/Pointers$Builder;)V", "sig": "(TV;Lnet/kyori/adventure/pointer/Pointers$Builder;)V"}], "flds": []}, "net/kyori/adventure/platform/facet/Facet$TabList.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/platform/facet/Facet$TabList", "super": "java/lang/Object", "mthds": [{"nme": "send", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TV;TM;TM;)V"}], "flds": []}, "net/kyori/adventure/platform/facet/Facet$ChatPacket.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/platform/facet/Facet$ChatPacket", "super": "java/lang/Object", "mthds": [{"nme": "createMessageType", "acc": 1, "dsc": "(Lnet/kyori/adventure/audience/MessageType;)B"}], "flds": [{"acc": 25, "nme": "TYPE_CHAT", "dsc": "B", "val": 0}, {"acc": 25, "nme": "TYPE_SYSTEM", "dsc": "B", "val": 1}, {"acc": 25, "nme": "TYPE_ACTION_BAR", "dsc": "B", "val": 2}]}, "net/kyori/adventure/platform/facet/FacetBossBarListener.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/facet/FacetBossBarListener", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lnet/kyori/adventure/platform/facet/Facet$BossBar;Ljava/util/function/Function;)V", "sig": "(Lnet/kyori/adventure/platform/facet/Facet$BossBar<TV;>;Ljava/util/function/Function<Lnet/kyori/adventure/text/Component;Lnet/kyori/adventure/text/Component;>;)V"}, {"nme": "bossBarInitialized", "acc": 1, "dsc": "(Lnet/kyori/adventure/bossbar/BossB<PERSON>;)V"}, {"nme": "bossBarNameChanged", "acc": 1, "dsc": "(Lnet/kyori/adventure/bossbar/BossBar;Lnet/kyori/adventure/text/Component;Lnet/kyori/adventure/text/Component;)V"}, {"nme": "bossBarProgressChanged", "acc": 1, "dsc": "(Lnet/kyori/adventure/bossbar/BossBar;FF)V"}, {"nme": "bossBarColorChanged", "acc": 1, "dsc": "(Lnet/kyori/adventure/bossbar/BossBar;Lnet/kyori/adventure/bossbar/BossBar$Color;Lnet/kyori/adventure/bossbar/BossBar$Color;)V"}, {"nme": "bossBarOverlayChanged", "acc": 1, "dsc": "(Lnet/kyori/adventure/bossbar/BossBar;Lnet/kyori/adventure/bossbar/BossBar$Overlay;Lnet/kyori/adventure/bossbar/BossBar$Overlay;)V"}, {"nme": "bossBarFlagsChanged", "acc": 1, "dsc": "(Lnet/kyori/adventure/bossbar/BossBar;<PERSON><PERSON><PERSON>/util/Set;<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(Lnet/kyori/adventure/bossbar/BossBar;Ljava/util/Set<Lnet/kyori/adventure/bossbar/BossBar$Flag;>;Ljava/util/Set<Lnet/kyori/adventure/bossbar/BossBar$Flag;>;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TV;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TV;)V"}, {"nme": "isEmpty", "acc": 1, "dsc": "()Z"}, {"nme": "close", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "facet", "dsc": "Lnet/kyori/adventure/platform/facet/Facet$BossBar;", "sig": "Lnet/kyori/adventure/platform/facet/Facet$BossBar<TV;>;"}, {"acc": 18, "nme": "translator", "dsc": "Ljava/util/function/Function;", "sig": "Ljava/util/function/Function<Lnet/kyori/adventure/text/Component;Lnet/kyori/adventure/text/Component;>;"}]}, "net/kyori/adventure/platform/facet/FacetComponentFlattener$Translator.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/platform/facet/FacetComponentFlattener$Translator", "super": "java/lang/Object", "mthds": [{"nme": "valueOrDefault", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "sig": "(TV;<PERSON><PERSON><PERSON>/lang/String;)<PERSON>java/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": []}, "net/kyori/adventure/platform/facet/FacetAudience.class": {"ver": 52, "acc": 33, "nme": "net/kyori/adventure/platform/facet/FacetAudience", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lnet/kyori/adventure/platform/facet/FacetAudienceProvider;Ljava/util/Collection;Ljava/util/Collection;Ljava/util/Collection;Ljava/util/Collection;Ljava/util/Collection;Ljava/util/Collection;Ljava/util/Collection;Ljava/util/Collection;Ljava/util/Collection;Ljava/util/Collection;)V", "sig": "(Lnet/kyori/adventure/platform/facet/FacetAudienceProvider;Ljava/util/Collection<+TV;>;Ljava/util/Collection<+Lnet/kyori/adventure/platform/facet/Facet$Chat;>;Ljava/util/Collection<+Lnet/kyori/adventure/platform/facet/Facet$ActionBar;>;Ljava/util/Collection<+Lnet/kyori/adventure/platform/facet/Facet$Title;>;Ljava/util/Collection<+Lnet/kyori/adventure/platform/facet/Facet$Sound;>;Ljava/util/Collection<+Lnet/kyori/adventure/platform/facet/Facet$EntitySound;>;Ljava/util/Collection<+Lnet/kyori/adventure/platform/facet/Facet$Book;>;Ljava/util/Collection<+Lnet/kyori/adventure/platform/facet/Facet$BossBar$Builder;>;Ljava/util/Collection<+Lnet/kyori/adventure/platform/facet/Facet$TabList;>;Ljava/util/Collection<+Lnet/kyori/adventure/platform/facet/Facet$Pointers;>;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TV;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TV;)V"}, {"nme": "refresh", "acc": 1, "dsc": "()V"}, {"nme": "sendMessage", "acc": 1, "dsc": "(Lnet/kyori/adventure/identity/Identity;Lnet/kyori/adventure/text/Component;Lnet/kyori/adventure/audience/MessageType;)V"}, {"nme": "sendMessage", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;Lnet/kyori/adventure/chat/ChatType$Bound;)V"}, {"nme": "sendMessage", "acc": 1, "dsc": "(Lnet/kyori/adventure/chat/SignedMessage;Lnet/kyori/adventure/chat/ChatType$Bound;)V"}, {"nme": "sendActionBar", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;)V"}, {"nme": "playSound", "acc": 1, "dsc": "(Lnet/kyori/adventure/sound/Sound;)V"}, {"nme": "playSound", "acc": 1, "dsc": "(Lnet/kyori/adventure/sound/Sound;Lnet/kyori/adventure/sound/Sound$Emitter;)V"}, {"nme": "playSound", "acc": 1, "dsc": "(Lnet/kyori/adventure/sound/Sound;DDD)V"}, {"nme": "stopSound", "acc": 1, "dsc": "(Lnet/kyori/adventure/sound/SoundStop;)V"}, {"nme": "openBook", "acc": 1, "dsc": "(Lnet/kyori/adventure/inventory/Book;)V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(Lnet/kyori/adventure/text/Component;)Ljava/lang/String;"}, {"nme": "showTitle", "acc": 1, "dsc": "(Lnet/kyori/adventure/title/Title;)V"}, {"nme": "sendTitlePart", "acc": 1, "dsc": "(Lnet/kyori/adventure/title/TitlePart;Ljava/lang/Object;)V", "sig": "<T:Ljava/lang/Object;>(Lnet/kyori/adventure/title/TitlePart<TT;>;TT;)V"}, {"nme": "clearTitle", "acc": 1, "dsc": "()V"}, {"nme": "resetTitle", "acc": 1, "dsc": "()V"}, {"nme": "showBossBar", "acc": 1, "dsc": "(Lnet/kyori/adventure/bossbar/BossB<PERSON>;)V"}, {"nme": "hide<PERSON><PERSON><PERSON>ar", "acc": 1, "dsc": "(Lnet/kyori/adventure/bossbar/BossB<PERSON>;)V"}, {"nme": "sendPlayerListHeader", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;)V"}, {"nme": "sendPlayerListFooter", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;)V"}, {"nme": "sendPlayerListHeaderAndFooter", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;Lnet/kyori/adventure/text/Component;)V"}, {"nme": "pointers", "acc": 1, "dsc": "()Lnet/kyori/adventure/pointer/Pointers;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "contributePointers", "acc": 4, "dsc": "(Lnet/kyori/adventure/pointer/Pointers$Builder;)V", "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$OverrideOnly;"}]}, {"nme": "close", "acc": 1, "dsc": "()V"}, {"nme": "createMessage", "acc": 2, "dsc": "(Lnet/kyori/adventure/text/Component;Lnet/kyori/adventure/platform/facet/Facet$Message;)Ljava/lang/Object;", "sig": "(Lnet/kyori/adventure/text/Component;Lnet/kyori/adventure/platform/facet/Facet$Message<TV;Ljava/lang/Object;>;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "lambda$showBossBar$0", "acc": 4098, "dsc": "(Lnet/kyori/adventure/text/Component;)Lnet/kyori/adventure/text/Component;"}], "flds": [{"acc": 20, "nme": "provider", "dsc": "Lnet/kyori/adventure/platform/facet/FacetAudienceProvider;", "sig": "Lnet/kyori/adventure/platform/facet/FacetAudienceProvider<TV;Lnet/kyori/adventure/platform/facet/FacetAudience<TV;>;>;"}, {"acc": 18, "nme": "viewers", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<TV;>;"}, {"acc": 2, "nme": "viewer", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;", "sig": "TV;"}, {"acc": 66, "nme": "pointers", "dsc": "Lnet/kyori/adventure/pointer/Pointers;"}, {"acc": 18, "nme": "chat", "dsc": "Lnet/kyori/adventure/platform/facet/Facet$Chat;", "sig": "Lnet/kyori/adventure/platform/facet/Facet$Chat<TV;Ljava/lang/Object;>;"}, {"acc": 18, "nme": "actionBar", "dsc": "Lnet/kyori/adventure/platform/facet/Facet$ActionBar;", "sig": "Lnet/kyori/adventure/platform/facet/Facet$ActionBar<TV;Ljava/lang/Object;>;"}, {"acc": 18, "nme": "title", "dsc": "Lnet/kyori/adventure/platform/facet/Facet$Title;", "sig": "Lnet/kyori/adventure/platform/facet/Facet$Title<TV;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;>;"}, {"acc": 18, "nme": "sound", "dsc": "Lnet/kyori/adventure/platform/facet/Facet$Sound;", "sig": "Lnet/kyori/adventure/platform/facet/Facet$Sound<TV;Ljava/lang/Object;>;"}, {"acc": 18, "nme": "entitySound", "dsc": "Lnet/kyori/adventure/platform/facet/Facet$EntitySound;", "sig": "Lnet/kyori/adventure/platform/facet/Facet$EntitySound<TV;Ljava/lang/Object;>;"}, {"acc": 18, "nme": "book", "dsc": "Lnet/kyori/adventure/platform/facet/Facet$Book;", "sig": "Lnet/kyori/adventure/platform/facet/Facet$Book<TV;Ljava/lang/Object;Ljava/lang/Object;>;"}, {"acc": 18, "nme": "<PERSON><PERSON><PERSON>", "dsc": "Lnet/kyori/adventure/platform/facet/Facet$BossBar$Builder;", "sig": "Lnet/kyori/adventure/platform/facet/Facet$BossBar$Builder<TV;Lnet/kyori/adventure/platform/facet/Facet$BossBar<TV;>;>;"}, {"acc": 18, "nme": "bossBars", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Lnet/kyori/adventure/bossbar/BossBar;Lnet/kyori/adventure/platform/facet/Facet$BossBar<TV;>;>;"}, {"acc": 18, "nme": "tabList", "dsc": "Lnet/kyori/adventure/platform/facet/Facet$TabList;", "sig": "Lnet/kyori/adventure/platform/facet/Facet$TabList<TV;Ljava/lang/Object;>;"}, {"acc": 18, "nme": "pointerProviders", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<+Lnet/kyori/adventure/platform/facet/Facet$Pointers<TV;>;>;"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$Internal;"}]}, "net/kyori/adventure/platform/facet/FacetComponentFlattener.class": {"ver": 52, "acc": 49, "nme": "net/kyori/adventure/platform/facet/FacetComponentFlattener", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "get", "acc": 9, "dsc": "(L<PERSON><PERSON>/lang/Object;Ljava/util/Collection;)Lnet/kyori/adventure/text/flattener/ComponentFlattener;", "sig": "<V:Ljava/lang/Object;>(TV;Ljava/util/Collection<+Lnet/kyori/adventure/platform/facet/FacetComponentFlattener$Translator<TV;>;>;)Lnet/kyori/adventure/text/flattener/ComponentFlattener;"}, {"nme": "lambda$get$0", "acc": 4106, "dsc": "(Lnet/kyori/adventure/platform/facet/FacetComponentFlattener$Translator;Ljava/lang/Object;Lnet/kyori/adventure/text/TranslatableComponent;Ljava/util/function/Consumer;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "LOCALIZATION_PATTERN", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$Internal;"}]}, "net/kyori/adventure/platform/facet/Facet$BossBarEntity.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/platform/facet/Facet$BossBarEntity", "super": "java/lang/Object", "mthds": [{"nme": "bossBarProgressChanged", "acc": 1, "dsc": "(Lnet/kyori/adventure/bossbar/BossBar;FF)V"}, {"nme": "bossBarNameChanged", "acc": 1, "dsc": "(Lnet/kyori/adventure/bossbar/BossBar;Lnet/kyori/adventure/text/Component;Lnet/kyori/adventure/text/Component;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TV;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TV;)V"}], "flds": [{"acc": 25, "nme": "OFFSET_PITCH", "dsc": "I", "val": 30}, {"acc": 25, "nme": "OFFSET_YAW", "dsc": "I", "val": 0}, {"acc": 25, "nme": "OFFSET_MAGNITUDE", "dsc": "I", "val": 40}, {"acc": 25, "nme": "INVULNERABLE_KEY", "dsc": "I", "val": 20}, {"acc": 25, "nme": "INVULNERABLE_TICKS", "dsc": "I", "val": 890}]}, "net/kyori/adventure/platform/facet/Facet$Message.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/platform/facet/Facet$Message", "super": "java/lang/Object", "mthds": [{"nme": "createMessage", "acc": 1025, "dsc": "(L<PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/text/Component;)Ljava/lang/Object;", "sig": "(TV;Lnet/kyori/adventure/text/Component;)TM;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}], "flds": [{"acc": 25, "nme": "PROTOCOL_HEX_COLOR", "dsc": "I", "val": 713}, {"acc": 25, "nme": "PROTOCOL_JSON", "dsc": "I", "val": 5}]}}}}