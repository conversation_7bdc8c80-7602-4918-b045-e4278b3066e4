{"md5": "c4740ba5f062143367e3ecb2b9ce7d83", "sha2": "3bf92be5293b1ab58266d3118a7bb2934eac1442", "sha256": "58b8a3047285d11fb9c017a157c0d2e13435d73115ce50045eb55893b4d6e1a6", "contents": {"classes": {"net/kyori/adventure/platform/bukkit/PaperFacet$Title.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/bukkit/PaperFacet$Title", "super": "net/kyori/adventure/platform/bukkit/SpigotFacet$Message", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "createTitleCollection", "acc": 1, "dsc": "()Lcom/destroystokyo/paper/Title$Builder;"}, {"nme": "contributeTitle", "acc": 1, "dsc": "(Lcom/destroystokyo/paper/Title$Builder;[Lnet/md_5/bungee/api/chat/BaseComponent;)V"}, {"nme": "contributeSubtitle", "acc": 1, "dsc": "(Lcom/destroystokyo/paper/Title$Builder;[Lnet/md_5/bungee/api/chat/BaseComponent;)V"}, {"nme": "contributeTimes", "acc": 1, "dsc": "(Lcom/destroystokyo/paper/Title$Builder;III)V"}, {"nme": "completeTitle", "acc": 1, "dsc": "(Lcom/destroystokyo/paper/Title$Builder;)Lcom/destroystokyo/paper/Title;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "showTitle", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Lcom/destroystokyo/paper/Title;)V"}, {"nme": "clearTitle", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;)V"}, {"nme": "resetTitle", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;)V"}, {"nme": "resetTitle", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "clearTitle", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "showTitle", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "completeTitle", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "contributeTimes", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;III)V"}, {"nme": "contributeSubtitle", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "contributeTitle", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "createTitleCollection", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "SUPPORTED", "dsc": "Z"}]}, "net/kyori/adventure/platform/bukkit/SpigotFacet$Translator.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/bukkit/SpigotFacet$Translator", "super": "net/kyori/adventure/platform/facet/FacetBase", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "valueOrDefault", "acc": 1, "dsc": "(Lorg/bukkit/Server;L<PERSON><PERSON>/lang/String;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "valueOrDefault", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "SUPPORTED", "dsc": "Z"}]}, "net/kyori/adventure/platform/bukkit/BukkitAudience.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/platform/bukkit/BukkitAudience", "super": "net/kyori/adventure/platform/facet/FacetAudience", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/bukkit/plugin/Plugin;Lnet/kyori/adventure/platform/facet/FacetAudienceProvider;Ljava/util/Collection;)V", "sig": "(Lorg/bukkit/plugin/Plugin;Lnet/kyori/adventure/platform/facet/FacetAudienceProvider<**>;Ljava/util/Collection<Lorg/bukkit/command/CommandSender;>;)V"}, {"nme": "locale", "acc": 0, "dsc": "(Ljava/util/Locale;)V"}, {"nme": "locale", "acc": 0, "dsc": "()Ljava/util/Locale;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "contributePointers", "acc": 4, "dsc": "(Lnet/kyori/adventure/pointer/Pointers$Builder;)V"}, {"nme": "showBossBar", "acc": 1, "dsc": "(Lnet/kyori/adventure/bossbar/BossB<PERSON>;)V"}, {"nme": "lambda$static$30", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$Pointers;"}, {"nme": "lambda$static$29", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$Pointers;"}, {"nme": "lambda$static$28", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$Pointers;"}, {"nme": "lambda$static$27", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$TabList;"}, {"nme": "lambda$static$26", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$TabList;"}, {"nme": "lambda$static$25", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$TabList;"}, {"nme": "lambda$static$24", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$TabList;"}, {"nme": "lambda$static$23", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$BossBar$Builder;"}, {"nme": "lambda$static$22", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$BossBar$Builder;"}, {"nme": "lambda$static$21", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$BossBar$Builder;"}, {"nme": "lambda$static$20", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$BossBar$Builder;"}, {"nme": "lambda$static$19", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$BossBar$Builder;"}, {"nme": "lambda$static$18", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$Book;"}, {"nme": "lambda$static$17", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$Book;"}, {"nme": "lambda$static$16", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$Book;"}, {"nme": "lambda$static$15", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$EntitySound;"}, {"nme": "lambda$static$14", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$EntitySound;"}, {"nme": "lambda$static$13", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$Sound;"}, {"nme": "lambda$static$12", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$Sound;"}, {"nme": "lambda$static$11", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$Title;"}, {"nme": "lambda$static$10", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$Title;"}, {"nme": "lambda$static$9", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$Title;"}, {"nme": "lambda$static$8", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$ActionBar;"}, {"nme": "lambda$static$7", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$ActionBar;"}, {"nme": "lambda$static$6", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$ActionBar;"}, {"nme": "lambda$static$5", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$ActionBar;"}, {"nme": "lambda$static$4", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$ActionBar;"}, {"nme": "lambda$static$3", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$Chat;"}, {"nme": "lambda$static$2", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$Chat;"}, {"nme": "lambda$static$1", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$Chat;"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "()Lnet/kyori/adventure/platform/facet/Facet$Chat;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "PLUGIN", "dsc": "<PERSON><PERSON><PERSON>/lang/ThreadLocal;", "sig": "Ljava/lang/ThreadLocal<Lorg/bukkit/plugin/Plugin;>;"}, {"acc": 26, "nme": "VIA", "dsc": "Ljava/util/function/Function;", "sig": "Ljava/util/function/Function<Lorg/bukkit/entity/Player;Lcom/viaversion/viaversion/api/connection/UserConnection;>;"}, {"acc": 26, "nme": "CHAT", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lnet/kyori/adventure/platform/facet/Facet$Chat<+Lorg/bukkit/command/CommandSender;*>;>;"}, {"acc": 26, "nme": "ACTION_BAR", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lnet/kyori/adventure/platform/facet/Facet$ActionBar<Lorg/bukkit/entity/Player;*>;>;"}, {"acc": 26, "nme": "TITLE", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lnet/kyori/adventure/platform/facet/Facet$Title<Lorg/bukkit/entity/Player;***>;>;"}, {"acc": 26, "nme": "SOUND", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lnet/kyori/adventure/platform/facet/Facet$Sound<Lorg/bukkit/entity/Player;Lorg/bukkit/util/Vector;>;>;"}, {"acc": 26, "nme": "ENTITY_SOUND", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lnet/kyori/adventure/platform/facet/Facet$EntitySound<Lorg/bukkit/entity/Player;Ljava/lang/Object;>;>;"}, {"acc": 26, "nme": "BOOK", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lnet/kyori/adventure/platform/facet/Facet$Book<Lorg/bukkit/entity/Player;**>;>;"}, {"acc": 26, "nme": "BOSS_BAR", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lnet/kyori/adventure/platform/facet/Facet$BossBar$Builder<Lorg/bukkit/entity/Player;*>;>;"}, {"acc": 26, "nme": "TAB_LIST", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lnet/kyori/adventure/platform/facet/Facet$TabList<Lorg/bukkit/entity/Player;*>;>;"}, {"acc": 26, "nme": "POINTERS", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lnet/kyori/adventure/platform/facet/Facet$Pointers<+Lorg/bukkit/command/CommandSender;>;>;"}, {"acc": 18, "nme": "plugin", "dsc": "Lorg/bukkit/plugin/Plugin;"}, {"acc": 2, "nme": "locale", "dsc": "Ljava/util/Locale;"}]}, "net/kyori/adventure/platform/bukkit/BukkitFacet$ConsoleCommandSenderPointers.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/platform/bukkit/BukkitFacet$ConsoleCommandSenderPointers", "super": "net/kyori/adventure/platform/bukkit/BukkitFacet", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "contributePointers", "acc": 1, "dsc": "(Lorg/bukkit/command/ConsoleCommandSender;Lnet/kyori/adventure/pointer/Pointers$Builder;)V"}, {"nme": "contributePointers", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/pointer/Pointers$Builder;)V"}], "flds": []}, "net/kyori/adventure/platform/bukkit/BukkitFacet$SoundWithCategory.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/bukkit/BukkitFacet$SoundWithCategory", "super": "net/kyori/adventure/platform/bukkit/BukkitFacet$Sound", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "playSound", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Lnet/kyori/adventure/sound/Sound;Lorg/bukkit/util/Vector;)V"}, {"nme": "stopSound", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Lnet/kyori/adventure/sound/SoundStop;)V"}, {"nme": "category", "acc": 2, "dsc": "(Lnet/kyori/adventure/sound/Sound$Source;)Lorg/bukkit/SoundCategory;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "stopSound", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/sound/SoundStop;)V"}, {"nme": "playSound", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/sound/Sound;<PERSON>java/lang/Object;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "SUPPORTED", "dsc": "Z"}]}, "net/kyori/adventure/platform/bukkit/CraftBukkitFacet.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet", "super": "net/kyori/adventure/platform/facet/FacetBase", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(Ljava/lang/Class<+TV;>;)V"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "access$000", "acc": 4104, "dsc": "()<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"nme": "access$100", "acc": 4104, "dsc": "()<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"nme": "access$200", "acc": 4104, "dsc": "()<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"nme": "access$300", "acc": 4104, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "access$400", "acc": 4104, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "access$500", "acc": 4104, "dsc": "()<PERSON><PERSON>va/lang/Class;"}, {"nme": "access$600", "acc": 4104, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "access$700", "acc": 4104, "dsc": "()<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"nme": "access$800", "acc": 4104, "dsc": "()<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"nme": "access$900", "acc": 4104, "dsc": "()<PERSON><PERSON>va/lang/Class;"}, {"nme": "access$1000", "acc": 4104, "dsc": "()<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"nme": "access$1100", "acc": 4104, "dsc": "()<PERSON><PERSON>va/lang/Class;"}, {"nme": "access$1200", "acc": 4104, "dsc": "()<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"nme": "access$1300", "acc": 4104, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "access$1400", "acc": 4104, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "access$1500", "acc": 4104, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "access$1600", "acc": 4104, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CLASS_NMS_ENTITY", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "CLASS_CRAFT_ENTITY", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "CRAFT_ENTITY_GET_HANDLE", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 24, "nme": "CLASS_CRAFT_PLAYER", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<+Lorg/bukkit/entity/Player;>;"}, {"acc": 24, "nme": "CRAFT_PLAYER_GET_HANDLE", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "ENTITY_PLAYER_GET_CONNECTION", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "PLAYER_CONNECTION_SEND_PACKET", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "SUPPORTED", "dsc": "Z"}, {"acc": 26, "nme": "CLASS_CHAT_COMPONENT", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "CLASS_MESSAGE_TYPE", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "MESSAGE_TYPE_CHAT", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 26, "nme": "MESSAGE_TYPE_SYSTEM", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 26, "nme": "MESSAGE_TYPE_ACTIONBAR", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 26, "nme": "LEGACY_CHAT_PACKET_CONSTRUCTOR", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "CHAT_PACKET_CONSTRUCTOR", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "CLASS_TITLE_PACKET", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "CLASS_TITLE_ACTION", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "CONSTRUCTOR_TITLE_MESSAGE", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "CONSTRUCTOR_TITLE_TIMES", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "TITLE_ACTION_TITLE", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 26, "nme": "TITLE_ACTION_SUBTITLE", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 26, "nme": "TITLE_ACTION_ACTIONBAR", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 26, "nme": "TITLE_ACTION_CLEAR", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 26, "nme": "TITLE_ACTION_RESET", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "net/kyori/adventure/platform/bukkit/SpigotFacet$ActionBar.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/platform/bukkit/SpigotFacet$ActionBar", "super": "net/kyori/adventure/platform/bukkit/SpigotFacet$ChatWithType", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "sendMessage", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;[Lnet/md_5/bungee/api/chat/BaseComponent;)V"}, {"nme": "sendMessage", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}], "flds": []}, "net/kyori/adventure/platform/bukkit/BukkitAudiencesImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/platform/bukkit/BukkitAudiencesImpl", "super": "net/kyori/adventure/platform/facet/FacetAudienceProvider", "mthds": [{"nme": "builder", "acc": 8, "dsc": "(Lorg/bukkit/plugin/Plugin;)Lnet/kyori/adventure/platform/bukkit/BukkitAudiencesImpl$Builder;"}, {"nme": "instanceFor", "acc": 8, "dsc": "(Lorg/bukkit/plugin/Plugin;)Lnet/kyori/adventure/platform/bukkit/BukkitAudiences;"}, {"nme": "<init>", "acc": 0, "dsc": "(Lorg/bukkit/plugin/Plugin;Lnet/kyori/adventure/text/renderer/ComponentRenderer;)V", "sig": "(Lorg/bukkit/plugin/Plugin;Lnet/kyori/adventure/text/renderer/ComponentRenderer<Lnet/kyori/adventure/pointer/Pointered;>;)V"}, {"nme": "sender", "acc": 1, "dsc": "(Lorg/bukkit/command/CommandSender;)Lnet/kyori/adventure/audience/Audience;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "player", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;)Lnet/kyori/adventure/audience/Audience;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "createAudience", "acc": 4, "dsc": "(Ljava/util/Collection;)Lnet/kyori/adventure/platform/bukkit/BukkitAudience;", "sig": "(Ljava/util/Collection<Lorg/bukkit/command/CommandSender;>;)Lnet/kyori/adventure/platform/bukkit/BukkitAudience;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "close", "acc": 1, "dsc": "()V"}, {"nme": "flattener", "acc": 1, "dsc": "()Lnet/kyori/adventure/text/flattener/ComponentFlattener;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "registerEvent", "acc": 2, "dsc": "(Ljava/lang/Class;Lorg/bukkit/event/EventPriority;Ljava/util/function/Consumer;)V", "sig": "<T:Lorg/bukkit/event/Event;>(Ljava/lang/Class<TT;>;Lorg/bukkit/event/EventPriority;Ljava/util/function/Consumer<TT;>;)V"}, {"nme": "registerLocaleEvent", "acc": 2, "dsc": "(Lorg/bukkit/event/EventPriority;Ljava/util/function/BiConsumer;)V", "sig": "(Lorg/bukkit/event/EventPriority;Ljava/util/function/BiConsumer<Lorg/bukkit/entity/Player;Ljava/util/Locale;>;)V"}, {"nme": "toLocale", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Locale;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "createAudience", "acc": 4164, "dsc": "(Ljava/util/Collection;)Lnet/kyori/adventure/platform/facet/FacetAudience;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "lambda$registerLocaleEvent$6", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;Ljava/lang/Class;Ljava/util/function/BiConsumer;Lorg/bukkit/event/player/PlayerEvent;)V"}, {"nme": "lambda$registerEvent$5", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Consumer;Lorg/bukkit/event/Listener;Lorg/bukkit/event/Event;)V", "exs": ["org/bukkit/event/EventException"]}, {"nme": "lambda$new$4", "acc": 4098, "dsc": "(Lorg/bukkit/entity/Player;Ljava/util/Locale;)V"}, {"nme": "lambda$new$3", "acc": 4098, "dsc": "(Lorg/bukkit/event/player/PlayerQuitEvent;)V"}, {"nme": "lambda$new$2", "acc": 4098, "dsc": "(Lorg/bukkit/event/player/PlayerJoin<PERSON>vent;)V"}, {"nme": "lambda$static$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "access$000", "acc": 4104, "dsc": "()Ljava/util/Map;"}, {"nme": "access$100", "acc": 4104, "dsc": "()Ljava/util/Locale;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "INSTANCES", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lnet/kyori/adventure/platform/bukkit/BukkitAudiences;>;"}, {"acc": 18, "nme": "plugin", "dsc": "Lorg/bukkit/plugin/Plugin;"}]}, "net/kyori/adventure/platform/bukkit/BukkitFacet$BossBarBuilder.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/bukkit/BukkitFacet$BossBarBuilder", "super": "net/kyori/adventure/platform/bukkit/BukkitFacet", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "createBossBar", "acc": 1, "dsc": "(Ljava/util/Collection;)Lnet/kyori/adventure/platform/bukkit/BukkitFacet$BossBar;", "sig": "(Ljava/util/Collection<Lorg/bukkit/entity/Player;>;)Lnet/kyori/adventure/platform/bukkit/BukkitFacet$BossBar;"}, {"nme": "createBossBar", "acc": 4161, "dsc": "(Ljava/util/Collection;)Lnet/kyori/adventure/platform/facet/Facet$BossBar;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "SUPPORTED", "dsc": "Z"}]}, "net/kyori/adventure/platform/bukkit/CraftBukkitAccess$Chat1_19_3.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/platform/bukkit/CraftBukkitAccess$Chat1_19_3", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "isSupported", "acc": 8, "dsc": "()Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "NEW_RESOURCE_LOCATION", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 24, "nme": "RESOURCE_KEY_CREATE", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 24, "nme": "SERVER_PLAYER_GET_LEVEL", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 24, "nme": "SERVER_LEVEL_GET_REGISTRY_ACCESS", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 24, "nme": "LEVEL_GET_REGISTRY_ACCESS", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 24, "nme": "ACTUAL_GET_REGISTRY_ACCESS", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 24, "nme": "REGISTRY_ACCESS_GET_REGISTRY_OPTIONAL", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 24, "nme": "REGISTRY_GET_OPTIONAL", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 24, "nme": "REGISTRY_GET_ID", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 24, "nme": "DISGUISED_CHAT_PACKET_CONSTRUCTOR", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 24, "nme": "CHAT_TYPE_BOUND_NETWORK_CONSTRUCTOR", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 24, "nme": "CHAT_TYPE_RESOURCE_KEY", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$ActionBar_1_17.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$ActionBar_1_17", "super": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$PacketFacet", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "createMessage", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Lnet/kyori/adventure/text/Component;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "createMessage", "acc": 4161, "dsc": "(Lorg/bukkit/command/CommandSender;Lnet/kyori/adventure/text/Component;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "createMessage", "acc": 4161, "dsc": "(L<PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/text/Component;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "sendMessage", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CLASS_SET_ACTION_BAR_TEXT_PACKET", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "CONSTRUCTOR_ACTION_BAR", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}]}, "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$PartialEntitySound.class": {"ver": 52, "acc": 1536, "nme": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$PartialEntitySound", "super": "java/lang/Object", "mthds": [{"nme": "createForSelf", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Lnet/kyori/adventure/sound/Sound;)Ljava/lang/Object;"}, {"nme": "createForEmitter", "acc": 1, "dsc": "(Lnet/kyori/adventure/sound/Sound;Lnet/kyori/adventure/sound/Sound$Emitter;)Ljava/lang/Object;"}, {"nme": "toNativeEntity", "acc": 1, "dsc": "(Lorg/bukkit/entity/Entity;)Ljava/lang/Object;", "exs": ["java/lang/Throwable"]}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Lnet/kyori/adventure/sound/Sound$Source;)Ljava/lang/Object;", "exs": ["java/lang/Throwable"]}, {"nme": "createForEntity", "acc": 1025, "dsc": "(Lnet/kyori/adventure/sound/Sound;Lorg/bukkit/entity/Entity;)Ljava/lang/Object;"}, {"nme": "createForSelf", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/sound/Sound;)<PERSON>java/lang/Object;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "MC_SOUND_SOURCE_BY_NAME", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;"}]}, "net/kyori/adventure/platform/bukkit/BukkitFacet$Sound.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/bukkit/BukkitFacet$Sound", "super": "net/kyori/adventure/platform/bukkit/BukkitFacet$Position", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "playSound", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Lnet/kyori/adventure/sound/Sound;Lorg/bukkit/util/Vector;)V"}, {"nme": "stopSound", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Lnet/kyori/adventure/sound/SoundStop;)V"}, {"nme": "name", "acc": 12, "dsc": "(Lnet/kyori/adventure/key/Key;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "stopSound", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/sound/SoundStop;)V"}, {"nme": "playSound", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/sound/Sound;<PERSON>java/lang/Object;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "KEY_SUPPORTED", "dsc": "Z"}, {"acc": 26, "nme": "STOP_SUPPORTED", "dsc": "Z"}, {"acc": 26, "nme": "STOP_ALL_SUPPORTED", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}]}, "net/kyori/adventure/platform/bukkit/BukkitAudiencesImpl$Builder.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/platform/bukkit/BukkitAudiencesImpl$Builder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/bukkit/plugin/Plugin;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/renderer/ComponentRenderer;)Lnet/kyori/adventure/platform/bukkit/BukkitAudiencesImpl$Builder;", "sig": "(Lnet/kyori/adventure/text/renderer/ComponentRenderer<Lnet/kyori/adventure/pointer/Pointered;>;)Lnet/kyori/adventure/platform/bukkit/BukkitAudiencesImpl$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "partition", "acc": 1, "dsc": "(Ljava/util/function/Function;)Lnet/kyori/adventure/platform/bukkit/BukkitAudiences$Builder;", "sig": "(Ljava/util/function/Function<Lnet/kyori/adventure/pointer/Pointered;*>;)Lnet/kyori/adventure/platform/bukkit/BukkitAudiences$Builder;"}, {"nme": "build", "acc": 1, "dsc": "()Lnet/kyori/adventure/platform/bukkit/BukkitAudiences;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "softDepend", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "build", "acc": 4161, "dsc": "()Lnet/kyori/adventure/platform/AudienceProvider;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "partition", "acc": 4161, "dsc": "(Ljava/util/function/Function;)Lnet/kyori/adventure/platform/AudienceProvider$Builder;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 4161, "dsc": "(Lnet/kyori/adventure/text/renderer/ComponentRenderer;)Lnet/kyori/adventure/platform/AudienceProvider$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "lambda$build$1", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/platform/bukkit/BukkitAudiences;"}, {"nme": "lambda$new$0", "acc": 4106, "dsc": "(Lnet/kyori/adventure/pointer/Pointered;)Ljava/util/Locale;"}], "flds": [{"acc": 18, "nme": "plugin", "dsc": "Lorg/bukkit/plugin/Plugin;"}, {"acc": 2, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "Lnet/kyori/adventure/text/renderer/ComponentRenderer;", "sig": "Lnet/kyori/adventure/text/renderer/ComponentRenderer<Lnet/kyori/adventure/pointer/Pointered;>;"}]}, "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$BossBar$Builder.class": {"ver": 52, "acc": 33, "nme": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$BossBar$Builder", "super": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "createBossBar", "acc": 1, "dsc": "(Ljava/util/Collection;)Lnet/kyori/adventure/platform/bukkit/CraftBukkitFacet$BossBar;", "sig": "(Ljava/util/Collection<Lorg/bukkit/entity/Player;>;)Lnet/kyori/adventure/platform/bukkit/CraftBukkitFacet$BossBar;"}, {"nme": "createBossBar", "acc": 4161, "dsc": "(Ljava/util/Collection;)Lnet/kyori/adventure/platform/facet/Facet$BossBar;"}], "flds": []}, "net/kyori/adventure/platform/bukkit/CraftBukkitAccess$EntitySound.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/platform/bukkit/CraftBukkitAccess$EntitySound", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "isSupported", "acc": 8, "dsc": "()Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "CLASS_CLIENTBOUND_ENTITY_SOUND", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 24, "nme": "CLASS_SOUND_SOURCE", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 24, "nme": "CLASS_SOUND_EVENT", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 24, "nme": "SOUND_SOURCE_GET_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}]}, "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$AbstractBook$TrustedByteArrayOutputStream.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$AbstractBook$TrustedByteArrayOutputStream", "super": "java/io/ByteArrayOutputStream", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "toInputStream", "acc": 1, "dsc": "()Ljava/io/InputStream;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lnet/kyori/adventure/platform/bukkit/CraftBukkitFacet$1;)V"}], "flds": []}, "net/kyori/adventure/platform/bukkit/CraftBukkitAccess.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/platform/bukkit/CraftBukkitAccess", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "CLASS_CHAT_COMPONENT", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 24, "nme": "CLASS_REGISTRY", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 24, "nme": "CLASS_SERVER_LEVEL", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 24, "nme": "CLASS_LEVEL", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 24, "nme": "CLASS_REGISTRY_ACCESS", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 24, "nme": "CLASS_RESOURCE_KEY", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 24, "nme": "CLASS_RESOURCE_LOCATION", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 24, "nme": "CLASS_NMS_ENTITY", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 24, "nme": "CLASS_BUILT_IN_REGISTRIES", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 24, "nme": "CLASS_HOLDER", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 24, "nme": "CLASS_WRITABLE_REGISTRY", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}]}, "net/kyori/adventure/platform/bukkit/SpigotFacet$Message.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/bukkit/SpigotFacet$Message", "super": "net/kyori/adventure/platform/bukkit/SpigotFacet", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(Ljava/lang/Class<+TV;>;)V"}, {"nme": "createMessage", "acc": 1, "dsc": "(Lorg/bukkit/command/CommandSender;Lnet/kyori/adventure/text/Component;)[Lnet/md_5/bungee/api/chat/BaseComponent;", "sig": "(TV;Lnet/kyori/adventure/text/Component;)[Lnet/md_5/bungee/api/chat/BaseComponent;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "createMessage", "acc": 4161, "dsc": "(L<PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/text/Component;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "SERIALIZER", "dsc": "Lnet/kyori/adventure/text/serializer/bungeecord/BungeeComponentSerializer;"}]}, "net/kyori/adventure/platform/bukkit/PaperFacet$TabList.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/bukkit/PaperFacet$TabList", "super": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$TabList", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "createBoundNativeDeserializeMethodHandle", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "create117Packet", "acc": 4, "dsc": "(Lorg/bukkit/entity/Player;<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Throwable"]}, {"nme": "createMessage", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Lnet/kyori/adventure/text/Component;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "createMessage", "acc": 4161, "dsc": "(L<PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/text/Component;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "createMessage", "acc": 4161, "dsc": "(Lorg/bukkit/command/CommandSender;Lnet/kyori/adventure/text/Component;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "SUPPORTED", "dsc": "Z"}, {"acc": 26, "nme": "NATIVE_GSON_COMPONENT_SERIALIZER_DESERIALIZE_METHOD_BOUND", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}]}, "net/kyori/adventure/platform/bukkit/BukkitFacet$Message.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/bukkit/BukkitFacet$Message", "super": "net/kyori/adventure/platform/bukkit/BukkitFacet", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(Ljava/lang/Class<+TV;>;)V"}, {"nme": "createMessage", "acc": 1, "dsc": "(Lorg/bukkit/command/CommandSender;Lnet/kyori/adventure/text/Component;)Ljava/lang/String;", "sig": "(TV;Lnet/kyori/adventure/text/Component;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "createMessage", "acc": 4161, "dsc": "(L<PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/text/Component;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": []}, "net/kyori/adventure/platform/bukkit/CraftBukkitAccess$EntitySound_1_19_3.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/platform/bukkit/CraftBukkitAccess$EntitySound_1_19_3", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "isSupported", "acc": 8, "dsc": "()Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "NEW_RESOURCE_LOCATION", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 24, "nme": "REGISTRY_GET_OPTIONAL", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 24, "nme": "REGISTRY_WRAP_AS_HOLDER", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 24, "nme": "SOUND_EVENT_CREATE_VARIABLE_RANGE", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 24, "nme": "NEW_CLIENTBOUND_ENTITY_SOUND", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 24, "nme": "SOUND_EVENT_REGISTRY", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "net/kyori/adventure/platform/bukkit/BukkitAudiences.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/platform/bukkit/BukkitAudiences", "super": "java/lang/Object", "mthds": [{"nme": "create", "acc": 9, "dsc": "(Lorg/bukkit/plugin/Plugin;)Lnet/kyori/adventure/platform/bukkit/BukkitAudiences;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "builder", "acc": 9, "dsc": "(Lorg/bukkit/plugin/Plugin;)Lnet/kyori/adventure/platform/bukkit/BukkitAudiences$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "asEmitter", "acc": 9, "dsc": "(Lorg/bukkit/entity/Entity;)Lnet/kyori/adventure/sound/Sound$Emitter;"}, {"nme": "sender", "acc": 1025, "dsc": "(Lorg/bukkit/command/CommandSender;)Lnet/kyori/adventure/audience/Audience;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "player", "acc": 1025, "dsc": "(Lorg/bukkit/entity/Player;)Lnet/kyori/adventure/audience/Audience;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "filter", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Predicate;)Lnet/kyori/adventure/audience/Audience;", "sig": "(<PERSON><PERSON><PERSON>/util/function/Predicate<Lorg/bukkit/command/CommandSender;>;)Lnet/kyori/adventure/audience/Audience;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": []}, "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$1.class": {"ver": 52, "acc": 4128, "nme": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$PacketFacet.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$PacketFacet", "super": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "sendPacket", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "sendMessage", "acc": 1, "dsc": "(Lorg/bukkit/command/CommandSender;<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TV;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "createMessage", "acc": 1, "dsc": "(Lorg/bukkit/command/CommandSender;Lnet/kyori/adventure/text/Component;)Ljava/lang/Object;", "sig": "(TV;Lnet/kyori/adventure/text/Component;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "createMessage", "acc": 4161, "dsc": "(L<PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/text/Component;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}], "flds": []}, "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$Book1_13.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$Book1_13", "super": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$AbstractBook", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "sendOpenPacket", "acc": 4, "dsc": "(Lorg/bukkit/entity/Player;)V", "exs": ["java/lang/Throwable"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CLASS_BYTE_BUF", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "CLASS_PACKET_CUSTOM_PAYLOAD", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "CLASS_FRIENDLY_BYTE_BUF", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "CLASS_RESOURCE_LOCATION", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "PACKET_TYPE_BOOK_OPEN", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 26, "nme": "NEW_PACKET_CUSTOM_PAYLOAD", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "NEW_FRIENDLY_BYTE_BUF", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}]}, "net/kyori/adventure/platform/bukkit/BukkitFacet$BossBar.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/bukkit/BukkitFacet$BossBar", "super": "net/kyori/adventure/platform/bukkit/BukkitFacet$Message", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)V", "sig": "(Ljava/util/Collection<Lorg/bukkit/entity/Player;>;)V"}, {"nme": "bossBarInitialized", "acc": 1, "dsc": "(Lnet/kyori/adventure/bossbar/BossB<PERSON>;)V"}, {"nme": "bossBarNameChanged", "acc": 1, "dsc": "(Lnet/kyori/adventure/bossbar/BossBar;Lnet/kyori/adventure/text/Component;Lnet/kyori/adventure/text/Component;)V"}, {"nme": "bossBarProgressChanged", "acc": 1, "dsc": "(Lnet/kyori/adventure/bossbar/BossBar;FF)V"}, {"nme": "bossBarColorChanged", "acc": 1, "dsc": "(Lnet/kyori/adventure/bossbar/BossBar;Lnet/kyori/adventure/bossbar/BossBar$Color;Lnet/kyori/adventure/bossbar/BossBar$Color;)V"}, {"nme": "color", "acc": 2, "dsc": "(Lnet/kyori/adventure/bossbar/BossBar$Color;)Lorg/bukkit/boss/BarColor;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "bossBarOverlayChanged", "acc": 1, "dsc": "(Lnet/kyori/adventure/bossbar/BossBar;Lnet/kyori/adventure/bossbar/BossBar$Overlay;Lnet/kyori/adventure/bossbar/BossBar$Overlay;)V"}, {"nme": "style", "acc": 2, "dsc": "(Lnet/kyori/adventure/bossbar/BossBar$Overlay;)Lorg/bukkit/boss/BarStyle;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "bossBarFlagsChanged", "acc": 1, "dsc": "(Lnet/kyori/adventure/bossbar/BossBar;<PERSON><PERSON><PERSON>/util/Set;<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(Lnet/kyori/adventure/bossbar/BossBar;Ljava/util/Set<Lnet/kyori/adventure/bossbar/BossBar$Flag;>;Ljava/util/Set<Lnet/kyori/adventure/bossbar/BossBar$Flag;>;)V"}, {"nme": "flag", "acc": 2, "dsc": "(Lnet/kyori/adventure/bossbar/BossBar$Flag;)Lorg/bukkit/boss/BarFlag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;)V"}, {"nme": "isEmpty", "acc": 1, "dsc": "()Z"}, {"nme": "close", "acc": 1, "dsc": "()V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}], "flds": [{"acc": 20, "nme": "bar", "dsc": "<PERSON>rg/bukkit/boss/<PERSON><PERSON><PERSON>;"}]}, "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$BookPost1_13.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$BookPost1_13", "super": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$AbstractBook", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "sendOpenPacket", "acc": 4, "dsc": "(Lorg/bukkit/entity/Player;)V", "exs": ["java/lang/Throwable"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CLASS_ENUM_HAND", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "HAND_MAIN", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 26, "nme": "PACKET_OPEN_BOOK", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "NEW_PACKET_OPEN_BOOK", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}]}, "net/kyori/adventure/text/serializer/craftbukkit/BukkitComponentSerializer.class": {"ver": 52, "acc": 131121, "nme": "net/kyori/adventure/text/serializer/craftbukkit/BukkitComponentSerializer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "legacy", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/serializer/legacy/LegacyComponentSerializer;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "gson", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/serializer/gson/GsonComponentSerializer;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "net/kyori/adventure/platform/bukkit/SpigotFacet$ChatWithType.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/bukkit/SpigotFacet$ChatWithType", "super": "net/kyori/adventure/platform/bukkit/SpigotFacet$Message", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "createType", "acc": 2, "dsc": "(Lnet/kyori/adventure/audience/MessageType;)Lnet/md_5/bungee/api/ChatMessageType;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "sendMessage", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Lnet/kyori/adventure/identity/Identity;[Lnet/md_5/bungee/api/chat/BaseComponent;Ljava/lang/Object;)V"}, {"nme": "sendMessage", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/identity/Identity;Lja<PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "PLAYER_CLASS", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "SUPPORTED", "dsc": "Z"}]}, "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$TabList.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$TabList", "super": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$PacketFacet", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "first", "acc": 138, "dsc": "([<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;)<PERSON>ja<PERSON>/lang/invoke/MethodHandle;"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "create117Packet", "acc": 4, "dsc": "(Lorg/bukkit/entity/Player;<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Throwable"]}, {"nme": "send", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;<PERSON><PERSON><PERSON>/lang/Object;<PERSON>ja<PERSON>/lang/Object;)V"}, {"nme": "send", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CLIENTBOUND_TAB_LIST_PACKET", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "CLIENTBOUND_TAB_LIST_PACKET_CTOR_PRE_1_17", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 28, "nme": "CLIENTBOUND_TAB_LIST_PACKET_CTOR", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "CRAFT_PLAYER_TAB_LIST_HEADER", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Field;"}, {"acc": 26, "nme": "CRAFT_PLAYER_TAB_LIST_FOOTER", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Field;"}, {"acc": 28, "nme": "CLIENTBOUND_TAB_LIST_PACKET_SET_HEADER", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 28, "nme": "CLIENTBOUND_TAB_LIST_PACKET_SET_FOOTER", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}]}, "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$BossBarWither$Builder.class": {"ver": 52, "acc": 33, "nme": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$BossBarWither$Builder", "super": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "createBossBar", "acc": 1, "dsc": "(Ljava/util/Collection;)Lnet/kyori/adventure/platform/bukkit/CraftBukkitFacet$BossBarWither;", "sig": "(Ljava/util/Collection<Lorg/bukkit/entity/Player;>;)Lnet/kyori/adventure/platform/bukkit/CraftBukkitFacet$BossBarWither;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "isSupported", "acc": 4161, "dsc": "()Z"}, {"nme": "createBossBar", "acc": 4161, "dsc": "(Ljava/util/Collection;)Lnet/kyori/adventure/platform/facet/Facet$BossBar;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": []}, "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$Title.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$Title", "super": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$PacketFacet", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "createTitleCollection", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/Object;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "contributeTitle", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/Object;>;Ljava/lang/Object;)V"}, {"nme": "contributeSubtitle", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/Object;>;Ljava/lang/Object;)V"}, {"nme": "contributeTimes", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;III)V", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/Object;>;III)V"}, {"nme": "completeTitle", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Ljava/util/List;", "sig": "(Ljava/util/List<Ljava/lang/Object;>;)Ljava/util/List<*>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "showTitle", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Ljava/util/List;)V", "sig": "(Lorg/bukkit/entity/Player;Ljava/util/List<*>;)V"}, {"nme": "clearTitle", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;)V"}, {"nme": "resetTitle", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;)V"}, {"nme": "resetTitle", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "clearTitle", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "showTitle", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "completeTitle", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "contributeTimes", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;III)V"}, {"nme": "contributeSubtitle", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "contributeTitle", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "createTitleCollection", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": []}, "net/kyori/adventure/text/serializer/craftbukkit/MinecraftComponentSerializer.class": {"ver": 52, "acc": 131121, "nme": "net/kyori/adventure/text/serializer/craftbukkit/MinecraftComponentSerializer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "isSupported", "acc": 9, "dsc": "()Z"}, {"nme": "get", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/serializer/craftbukkit/MinecraftComponentSerializer;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "deserialize", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "serialize", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "INSTANCE", "dsc": "Lnet/kyori/adventure/text/serializer/craftbukkit/MinecraftComponentSerializer;"}, {"acc": 18, "nme": "realSerial", "dsc": "Lnet/kyori/adventure/platform/bukkit/MinecraftComponentSerializer;"}], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$BossBar.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$BossBar", "super": "net/kyori/adventure/platform/bukkit/BukkitFacet$BossBar", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)V", "sig": "(Ljava/util/Collection<Lorg/bukkit/entity/Player;>;)V"}, {"nme": "bossBarNameChanged", "acc": 1, "dsc": "(Lnet/kyori/adventure/bossbar/BossBar;Lnet/kyori/adventure/text/Component;Lnet/kyori/adventure/text/Component;)V"}, {"nme": "access$1800", "acc": 4104, "dsc": "()<PERSON><PERSON>va/lang/Class;"}, {"nme": "access$1900", "acc": 4104, "dsc": "()<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"nme": "access$2000", "acc": 4104, "dsc": "()<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"nme": "access$2100", "acc": 4104, "dsc": "()<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Ljava/util/Collection;Lnet/kyori/adventure/platform/bukkit/CraftBukkitFacet$1;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CLASS_CRAFT_BOSS_BAR", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "CLASS_BOSS_BAR_ACTION", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "BOSS_BAR_ACTION_TITLE", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 26, "nme": "CRAFT_BOSS_BAR_HANDLE", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "NMS_BOSS_BATTLE_SET_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "NMS_BOSS_BATTLE_SEND_UPDATE", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}]}, "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$BookPre1_13.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$BookPre1_13", "super": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$AbstractBook", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "sendOpenPacket", "acc": 4, "dsc": "(Lorg/bukkit/entity/Player;)V", "exs": ["java/lang/Throwable"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "PACKET_TYPE_BOOK_OPEN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "MC|BOpen"}, {"acc": 26, "nme": "CLASS_BYTE_BUF", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "CLASS_PACKET_CUSTOM_PAYLOAD", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "CLASS_PACKET_DATA_SERIALIZER", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "NEW_PACKET_CUSTOM_PAYLOAD", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "NEW_PACKET_BYTE_BUF", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}]}, "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$Chat.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$Chat", "super": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$PacketFacet", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "sendMessage", "acc": 1, "dsc": "(Lorg/bukkit/command/CommandSender;Lnet/kyori/adventure/identity/Identity;Ljava/lang/Object;Ljava/lang/Object;)V"}, {"nme": "sendMessage", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/identity/Identity;Lja<PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}], "flds": []}, "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$BossBarWither.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$BossBarWither", "super": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$FakeEntity", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)V", "sig": "(Ljava/util/Collection<Lorg/bukkit/entity/Player;>;)V"}, {"nme": "bossBarInitialized", "acc": 1, "dsc": "(Lnet/kyori/adventure/bossbar/BossB<PERSON>;)V"}, {"nme": "createPosition", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;)Lorg/bukkit/Location;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "isEmpty", "acc": 1, "dsc": "()Z"}, {"nme": "createPosition", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "<init>", "acc": 4096, "dsc": "(Ljava/util/Collection;Lnet/kyori/adventure/platform/bukkit/CraftBukkitFacet$1;)V"}], "flds": [{"acc": 66, "nme": "initialized", "dsc": "Z"}]}, "net/kyori/adventure/platform/bukkit/BukkitFacet.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/bukkit/BukkitFacet", "super": "net/kyori/adventure/platform/facet/FacetBase", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(Ljava/lang/Class<+TV;>;)V"}], "flds": []}, "net/kyori/adventure/platform/bukkit/BukkitFacet$Chat.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/bukkit/BukkitFacet$Chat", "super": "net/kyori/adventure/platform/bukkit/BukkitFacet$Message", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "sendMessage", "acc": 1, "dsc": "(Lorg/bukkit/command/CommandSender;Lnet/kyori/adventure/identity/Identity;Ljava/lang/String;Ljava/lang/Object;)V"}, {"nme": "sendMessage", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/identity/Identity;Lja<PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}], "flds": []}, "net/kyori/adventure/platform/bukkit/SpigotFacet$Book.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/platform/bukkit/SpigotFacet$Book", "super": "net/kyori/adventure/platform/bukkit/SpigotFacet$Message", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "createBook", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/Iterable;)Lorg/bukkit/inventory/ItemStack;", "sig": "(<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/Iterable<[Lnet/md_5/bungee/api/chat/BaseComponent;>;)Lorg/bukkit/inventory/ItemStack;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "openBook", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Lorg/bukkit/inventory/ItemStack;)V"}, {"nme": "openBook", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "createBook", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Iterable;)<PERSON><PERSON><PERSON>/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "SUPPORTED", "dsc": "Z"}]}, "net/kyori/adventure/platform/bukkit/BukkitFacet$CommandSenderPointers.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/platform/bukkit/BukkitFacet$CommandSenderPointers", "super": "net/kyori/adventure/platform/bukkit/BukkitFacet", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "contributePointers", "acc": 1, "dsc": "(Lorg/bukkit/command/CommandSender;Lnet/kyori/adventure/pointer/Pointers$Builder;)V"}, {"nme": "contributePointers", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/pointer/Pointers$Builder;)V"}, {"nme": "lambda$contributePointers$0", "acc": 4106, "dsc": "(Lorg/bukkit/command/CommandSender;Ljava/lang/String;)Lnet/kyori/adventure/util/TriState;"}], "flds": []}, "net/kyori/adventure/platform/bukkit/PaperFacet.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/bukkit/PaperFacet", "super": "net/kyori/adventure/platform/facet/FacetBase", "mthds": [{"nme": "findNativeDeserializeMethod", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(Ljava/lang/Class<+TV;>;)V"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "access$000", "acc": 4104, "dsc": "()<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"nme": "access$100", "acc": 4104, "dsc": "()<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "SUPPORTED", "dsc": "Z"}, {"acc": 24, "nme": "NATIVE_COMPONENT_CLASS", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "NATIVE_GSON_COMPONENT_SERIALIZER_CLASS", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "NATIVE_GSON_COMPONENT_SERIALIZER_IMPL_CLASS", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "NATIVE_GSON_COMPONENT_SERIALIZER_GSON_GETTER", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "NATIVE_GSON_COMPONENT_SERIALIZER_DESERIALIZE_METHOD", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}]}, "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$EntitySound.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$EntitySound", "super": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$PacketFacet", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "createForEntity", "acc": 1, "dsc": "(Lnet/kyori/adventure/sound/Sound;Lorg/bukkit/entity/Entity;)Ljava/lang/Object;"}, {"nme": "playSound", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "playSound", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "lambda$createForEntity$0", "acc": 4106, "dsc": "()J"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CLASS_CLIENTBOUND_CUSTOM_SOUND", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "CLASS_VEC3", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "NEW_CLIENTBOUND_ENTITY_SOUND", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "NEW_CLIENTBOUND_CUSTOM_SOUND", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "NEW_VEC3", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "NEW_RESOURCE_LOCATION", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "REGISTRY_GET_OPTIONAL", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "REGISTRY_SOUND_EVENT", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$EntitySound_1_19_3.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$EntitySound_1_19_3", "super": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$PacketFacet", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "createForEntity", "acc": 1, "dsc": "(Lnet/kyori/adventure/sound/Sound;Lorg/bukkit/entity/Entity;)Ljava/lang/Object;"}, {"nme": "playSound", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "playSound", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "lambda$createForEntity$0", "acc": 4106, "dsc": "()J"}], "flds": []}, "net/kyori/adventure/platform/bukkit/SpigotFacet.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/bukkit/SpigotFacet", "super": "net/kyori/adventure/platform/facet/FacetBase", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(Ljava/lang/Class<+TV;>;)V"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "access$000", "acc": 4104, "dsc": "()<PERSON><PERSON>va/lang/Class;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "SUPPORTED", "dsc": "Z"}, {"acc": 26, "nme": "BUNGEE_CHAT_MESSAGE_TYPE", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 24, "nme": "BUNGEE_COMPONENT_TYPE", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}]}, "net/kyori/adventure/platform/bukkit/BukkitComponentSerializer.class": {"ver": 52, "acc": 49, "nme": "net/kyori/adventure/platform/bukkit/BukkitComponentSerializer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "legacy", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/serializer/legacy/LegacyComponentSerializer;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "gson", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/serializer/gson/GsonComponentSerializer;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "IS_1_16", "dsc": "Z"}, {"acc": 26, "nme": "TRANSLATORS", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Lnet/kyori/adventure/platform/facet/FacetComponentFlattener$Translator<Lorg/bukkit/Server;>;>;"}, {"acc": 26, "nme": "LEGACY_SERIALIZER", "dsc": "Lnet/kyori/adventure/text/serializer/legacy/LegacyComponentSerializer;"}, {"acc": 26, "nme": "GSON_SERIALIZER", "dsc": "Lnet/kyori/adventure/text/serializer/gson/GsonComponentSerializer;"}, {"acc": 24, "nme": "FLATTENER", "dsc": "Lnet/kyori/adventure/text/flattener/ComponentFlattener;"}]}, "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$ActionBar.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$ActionBar", "super": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$PacketFacet", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "createMessage", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Lnet/kyori/adventure/text/Component;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "createMessage", "acc": 4161, "dsc": "(Lorg/bukkit/command/CommandSender;Lnet/kyori/adventure/text/Component;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "createMessage", "acc": 4161, "dsc": "(L<PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/text/Component;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "sendMessage", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}], "flds": []}, "net/kyori/adventure/platform/bukkit/BukkitFacet$TabList.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/platform/bukkit/BukkitFacet$TabList", "super": "net/kyori/adventure/platform/bukkit/BukkitFacet$Message", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "send", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "send", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "SUPPORTED", "dsc": "Z"}]}, "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$FakeEntity.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$FakeEntity", "super": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$PacketFacet", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Lorg/bukkit/Location;)V", "sig": "(L<PERSON>va/lang/Class<TE;>;Lorg/bukkit/Location;)V"}, {"nme": "<init>", "acc": 4, "dsc": "(Lorg/bukkit/plugin/Plugin;<PERSON><PERSON><PERSON>/lang/Class;Lorg/bukkit/Location;)V", "sig": "(Lorg/bukkit/plugin/Plugin;Ljava/lang/Class<TE;>;Lorg/bukkit/Location;)V"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "onPlayerMove", "acc": 1, "dsc": "(Lorg/bukkit/event/player/PlayerMoveEvent;)V", "vanns": [{"dsc": "Lorg/bukkit/event/EventHandler;", "vals": ["ignoreCancelled", false, "priority", ["Lorg/bukkit/event/EventPriority;", "MONITOR"]]}]}, {"nme": "createSpawnPacket", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "createDespawnPacket", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "createMetadataPacket", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "createLocationPacket", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "broadcastPacket", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "createPosition", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;)Lorg/bukkit/Location;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "createPosition", "acc": 1, "dsc": "(DDD)Lorg/bukkit/Location;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "teleport", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Lorg/bukkit/Location;)V"}, {"nme": "metadata", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "invisible", "acc": 1, "dsc": "(Z)V"}, {"nme": "health", "acc": 131073, "dsc": "(F)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "name", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;)V"}, {"nme": "close", "acc": 1, "dsc": "()V"}, {"nme": "teleport", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "createPosition", "acc": 4161, "dsc": "(DDD)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "createPosition", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CLASS_CRAFT_WORLD", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<+Lorg/bukkit/World;>;"}, {"acc": 26, "nme": "CLASS_NMS_LIVING_ENTITY", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "CLASS_DATA_WATCHER", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "CRAFT_WORLD_CREATE_ENTITY", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "NMS_ENTITY_GET_BUKKIT_ENTITY", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "NMS_ENTITY_GET_DATA_WATCHER", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "NMS_ENTITY_SET_LOCATION", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "NMS_ENTITY_SET_INVISIBLE", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "DATA_WATCHER_WATCH", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "CLASS_SPAWN_LIVING_PACKET", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "NEW_SPAWN_LIVING_PACKET", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "CLASS_ENTITY_DESTROY_PACKET", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "NEW_ENTITY_DESTROY_PACKET", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "CLASS_ENTITY_METADATA_PACKET", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "NEW_ENTITY_METADATA_PACKET", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "CLASS_ENTITY_TELEPORT_PACKET", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "NEW_ENTITY_TELEPORT_PACKET", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "CLASS_ENTITY_WITHER", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "CLASS_WORLD", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "CLASS_WORLD_SERVER", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "CRAFT_WORLD_GET_HANDLE", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "NEW_ENTITY_WITHER", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "SUPPORTED", "dsc": "Z"}, {"acc": 18, "nme": "entity", "dsc": "Lorg/bukkit/entity/Entity;", "sig": "TE;"}, {"acc": 18, "nme": "entityHandle", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 20, "nme": "viewers", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Lorg/bukkit/entity/Player;>;"}]}, "net/kyori/adventure/platform/bukkit/BukkitFacet$Position.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/bukkit/BukkitFacet$Position", "super": "net/kyori/adventure/platform/bukkit/BukkitFacet", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "createPosition", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;)Lorg/bukkit/util/Vector;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "createPosition", "acc": 1, "dsc": "(DDD)Lorg/bukkit/util/Vector;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "createPosition", "acc": 4161, "dsc": "(DDD)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "createPosition", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": []}, "net/kyori/adventure/platform/bukkit/BukkitFacet$ViaHook.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/platform/bukkit/BukkitFacet$ViaHook", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "apply", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;)Lcom/viaversion/viaversion/api/connection/UserConnection;"}, {"nme": "apply", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$AbstractBook.class": {"ver": 52, "acc": 1057, "nme": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$AbstractBook", "super": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$PacketFacet", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "sendOpenPacket", "acc": 1028, "dsc": "(Lorg/bukkit/entity/Player;)V", "exs": ["java/lang/Throwable"]}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "createMessage", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Lnet/kyori/adventure/text/Component;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "createBook", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/Iterable;)Lorg/bukkit/inventory/ItemStack;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;Ljava/lang/Iterable<Ljava/lang/Object;>;)Lorg/bukkit/inventory/ItemStack;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "openBook", "acc": 131073, "dsc": "(Lorg/bukkit/entity/Player;Lorg/bukkit/inventory/ItemStack;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "tagFor", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/Iterable;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;", "sig": "(Lja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/Iterable<Ljava/lang/Object;>;)Lnet/kyori/adventure/nbt/CompoundBinaryTag;"}, {"nme": "createTag", "acc": 2, "dsc": "(Lnet/kyori/adventure/nbt/CompoundBinaryTag;)Ljava/lang/Object;", "exs": ["java/io/IOException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "applyTag", "acc": 2, "dsc": "(Lorg/bukkit/inventory/ItemStack;Lnet/kyori/adventure/nbt/CompoundBinaryTag;)Lorg/bukkit/inventory/ItemStack;"}, {"nme": "createMessage", "acc": 4161, "dsc": "(Lorg/bukkit/command/CommandSender;Lnet/kyori/adventure/text/Component;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "createMessage", "acc": 4161, "dsc": "(L<PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/text/Component;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "openBook", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "createBook", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Iterable;)<PERSON><PERSON><PERSON>/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 28, "nme": "HAND_MAIN", "dsc": "I", "val": 0}, {"acc": 26, "nme": "BOOK_TYPE", "dsc": "Lorg/bukkit/Material;"}, {"acc": 26, "nme": "BOOK_STACK", "dsc": "Lorg/bukkit/inventory/ItemStack;"}, {"acc": 26, "nme": "BOOK_TITLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "title"}, {"acc": 26, "nme": "BOOK_AUTHOR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "author"}, {"acc": 26, "nme": "BOOK_PAGES", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "pages"}, {"acc": 26, "nme": "BOOK_RESOLVED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "resolved"}, {"acc": 26, "nme": "CLASS_NBT_TAG_COMPOUND", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "CLASS_NBT_IO", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "NBT_IO_DESERIALIZE", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "CLASS_CRAFT_ITEMSTACK", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "CLASS_MC_ITEMSTACK", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "MC_ITEMSTACK_SET_TAG", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "CRAFT_ITEMSTACK_NMS_COPY", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "CRAFT_ITEMSTACK_CRAFT_MIRROR", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}]}, "net/kyori/adventure/platform/bukkit/MinecraftReflection.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/platform/bukkit/MinecraftReflection", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "findClass", "acc": 137, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class;", "sig": "([<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class<*>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "needClass", "acc": 137, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class;", "sig": "([<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class<*>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "hasClass", "acc": 137, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "find<PERSON><PERSON><PERSON>", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/Class;[Ljava/lang/Class;)<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/String;Ljava/lang/Class<*>;[Ljava/lang/Class<*>;)Ljava/lang/invoke/MethodHandle;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "find<PERSON><PERSON><PERSON>", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;[Ljava/lang/String;Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/invoke/MethodHandle;", "sig": "(Lja<PERSON>/lang/Class<*>;[Ljava/lang/String;Ljava/lang/Class<*>;[Ljava/lang/Class<*>;)Ljava/lang/invoke/MethodHandle;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "searchMethod", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/Integer;<PERSON><PERSON><PERSON>/lang/String;L<PERSON><PERSON>/lang/Class;[Ljava/lang/Class;)L<PERSON><PERSON>/lang/invoke/MethodHandle;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;<PERSON><PERSON><PERSON>/lang/Integer;<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/Class<*>;[Ljava/lang/Class<*>;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "searchMethod", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/Integer;[Lja<PERSON>/lang/String;Ljava/lang/Class;[Ljava/lang/Class;)L<PERSON><PERSON>/lang/invoke/MethodHandle;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;<PERSON><PERSON><PERSON>/lang/Integer;[Ljava/lang/String;Ljava/lang/Class<*>;[Ljava/lang/Class<*>;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "findStaticMethod", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/Class;[Ljava/lang/Class;)<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/String;Ljava/lang/Class<*>;[Ljava/lang/Class<*>;)Ljava/lang/invoke/MethodHandle;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "findStaticMethod", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;[Ljava/lang/String;Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/invoke/MethodHandle;", "sig": "(Lja<PERSON>/lang/Class<*>;[Ljava/lang/String;Ljava/lang/Class<*>;[Ljava/lang/Class<*>;)Ljava/lang/invoke/MethodHandle;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/Class;[Lja<PERSON>/lang/String;)Z", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/Class<*>;[Ljava/lang/String;)Z"}, {"nme": "has<PERSON><PERSON><PERSON>", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/Class;)Z", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/String;[Ljava/lang/Class<*>;)Z"}, {"nme": "has<PERSON><PERSON><PERSON>", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;[<PERSON>ja<PERSON>/lang/String;[Ljava/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;[Ljava/lang/String;[Ljava/lang/Class<*>;)Z"}, {"nme": "findConstructor", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;[Ljava/lang/Class;)Ljava/lang/invoke/MethodHandle;", "sig": "(Lja<PERSON>/lang/Class<*>;[Ljava/lang/Class<*>;)Ljava/lang/invoke/MethodHandle;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "needField", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;L<PERSON><PERSON>/lang/String;)Ljava/lang/reflect/Field;", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/String;)Ljava/lang/reflect/Field;", "exs": ["java/lang/NoSuchFieldException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "findField", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;[Ljava/lang/String;)Ljava/lang/reflect/Field;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;[Ljava/lang/String;)Ljava/lang/reflect/Field;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "findField", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/Class;[Ljava/lang/String;)Ljava/lang/reflect/Field;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Class<*>;[Ljava/lang/String;)Ljava/lang/reflect/Field;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "findSetterOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)Ljava/lang/invoke/MethodHandle;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "findGetterOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)Ljava/lang/invoke/MethodHandle;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "findEnum", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(L<PERSON><PERSON>/lang/Class<*>;Ljava/lang/String;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "findEnum", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/String;I)Ljava/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;Ljava/lang/String;I)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "isCraftBukkit", "acc": 9, "dsc": "()Z"}, {"nme": "findCraftClassName", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "findCraftClass", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class<*>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "findCraftClass", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/Class;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/String;Ljava/lang/Class<TT;>;)Ljava/lang/Class<+TT;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "needCraftClass", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class<*>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "findNmsClassName", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "findNmsClass", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class<*>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "needNmsClass", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class<*>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "findMcClassName", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "findMcClass", "acc": 137, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class;", "sig": "([<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class<*>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "needMcClass", "acc": 137, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class;", "sig": "([<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class<*>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "lookup", "acc": 9, "dsc": "()Ljava/lang/invoke/MethodHandles$Lookup;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "LOOKUP", "dsc": "Ljava/lang/invoke/MethodHandles$Lookup;"}, {"acc": 26, "nme": "PREFIX_NMS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "net.minecraft.server"}, {"acc": 26, "nme": "PREFIX_MC", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "net.minecraft."}, {"acc": 26, "nme": "PREFIX_CRAFTBUKKIT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "org.bukkit.craftbukkit"}, {"acc": 26, "nme": "CRAFT_SERVER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "CraftServer"}, {"acc": 26, "nme": "VERSION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$Translator.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$Translator", "super": "net/kyori/adventure/platform/facet/FacetBase", "mthds": [{"nme": "unreflectUnchecked", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "valueOrDefault", "acc": 1, "dsc": "(Lorg/bukkit/Server;L<PERSON><PERSON>/lang/String;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "valueOrDefault", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "lambda$static$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)Z"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CLASS_LANGUAGE", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "LANGUAGE_GET_INSTANCE", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "LANGUAGE_GET_OR_DEFAULT", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}]}, "net/kyori/adventure/platform/bukkit/BukkitEmitter.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/platform/bukkit/BukkitEmitter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/bukkit/entity/Entity;)V"}], "flds": [{"acc": 16, "nme": "entity", "dsc": "Lorg/bukkit/entity/Entity;"}]}, "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$ActionBarLegacy.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$ActionBarLegacy", "super": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$PacketFacet", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "createMessage", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Lnet/kyori/adventure/text/Component;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "createMessage", "acc": 4161, "dsc": "(Lorg/bukkit/command/CommandSender;Lnet/kyori/adventure/text/Component;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "createMessage", "acc": 4161, "dsc": "(L<PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/text/Component;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "sendMessage", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}], "flds": []}, "net/kyori/adventure/platform/bukkit/SpigotFacet$Chat.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/platform/bukkit/SpigotFacet$Chat", "super": "net/kyori/adventure/platform/bukkit/SpigotFacet$Message", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "sendMessage", "acc": 1, "dsc": "(Lorg/bukkit/command/CommandSender;Lnet/kyori/adventure/identity/Identity;[Lnet/md_5/bungee/api/chat/BaseComponent;Ljava/lang/Object;)V"}, {"nme": "sendMessage", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/identity/Identity;Lja<PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "SUPPORTED", "dsc": "Z"}]}, "net/kyori/adventure/platform/bukkit/BukkitAudiences$Builder.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/platform/bukkit/BukkitAudiences$Builder", "super": "java/lang/Object", "mthds": [], "flds": []}, "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$Chat1_19_3.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$Chat1_19_3", "super": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$Chat", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "sendMessage", "acc": 1, "dsc": "(Lorg/bukkit/command/CommandSender;Lnet/kyori/adventure/identity/Identity;Ljava/lang/Object;Ljava/lang/Object;)V"}, {"nme": "sendMessage", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/identity/Identity;Lja<PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}], "flds": []}, "net/kyori/adventure/platform/bukkit/MinecraftComponentSerializer.class": {"ver": 52, "acc": 49, "nme": "net/kyori/adventure/platform/bukkit/MinecraftComponentSerializer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "isSupported", "acc": 9, "dsc": "()Z"}, {"nme": "get", "acc": 9, "dsc": "()Lnet/kyori/adventure/platform/bukkit/MinecraftComponentSerializer;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "deserialize", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "serialize", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "lambda$static$8", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)Z"}, {"nme": "lambda$static$7", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)Z"}, {"nme": "lambda$static$6", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)Z"}, {"nme": "lambda$static$5", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)Z"}, {"nme": "lambda$static$4", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)Z"}, {"nme": "lambda$static$3", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)Z"}, {"nme": "lambda$static$2", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)Z"}, {"nme": "lambda$static$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)Z"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "INSTANCE", "dsc": "Lnet/kyori/adventure/platform/bukkit/MinecraftComponentSerializer;"}, {"acc": 26, "nme": "CLASS_JSON_DESERIALIZER", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "CLASS_CHAT_COMPONENT", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "INITIALIZATION_ERROR", "dsc": "Ljava/util/concurrent/atomic/AtomicReference;", "sig": "Ljava/util/concurrent/atomic/AtomicReference<Ljava/lang/RuntimeException;>;"}, {"acc": 26, "nme": "MC_TEXT_GSON", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 26, "nme": "TEXT_SERIALIZER_DESERIALIZE", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "TEXT_SERIALIZER_SERIALIZE", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "SUPPORTED", "dsc": "Z"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$Experimental;"}]}, "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$Title_1_17.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$Title_1_17", "super": "net/kyori/adventure/platform/bukkit/CraftBukkitFacet$PacketFacet", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "isSupported", "acc": 1, "dsc": "()Z"}, {"nme": "createTitleCollection", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/Object;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "contributeTitle", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/Object;>;Ljava/lang/Object;)V"}, {"nme": "contributeSubtitle", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/Object;>;Ljava/lang/Object;)V"}, {"nme": "contributeTimes", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;III)V", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/Object;>;III)V"}, {"nme": "completeTitle", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Ljava/util/List;", "sig": "(Ljava/util/List<Ljava/lang/Object;>;)Ljava/util/List<*>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "showTitle", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Ljava/util/List;)V", "sig": "(Lorg/bukkit/entity/Player;Ljava/util/List<*>;)V"}, {"nme": "clearTitle", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;)V"}, {"nme": "resetTitle", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;)V"}, {"nme": "resetTitle", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "clearTitle", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "showTitle", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "completeTitle", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "contributeTimes", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;III)V"}, {"nme": "contributeSubtitle", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "contributeTitle", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "createTitleCollection", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "PACKET_SET_TITLE", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "PACKET_SET_SUBTITLE", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "PACKET_SET_TITLE_ANIMATION", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "PACKET_CLEAR_TITLES", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "CONSTRUCTOR_SET_TITLE", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "CONSTRUCTOR_SET_SUBTITLE", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "CONSTRUCTOR_SET_TITLE_ANIMATION", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "CONSTRUCTOR_CLEAR_TITLES", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}]}, "net/kyori/adventure/platform/bukkit/BukkitFacet$PlayerPointers.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/platform/bukkit/BukkitFacet$PlayerPointers", "super": "net/kyori/adventure/platform/bukkit/BukkitFacet", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "contributePointers", "acc": 1, "dsc": "(Lorg/bukkit/entity/Player;Lnet/kyori/adventure/pointer/Pointers$Builder;)V"}, {"nme": "contributePointers", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/pointer/Pointers$Builder;)V"}, {"nme": "lambda$contributePointers$1", "acc": 4106, "dsc": "(Lorg/bukkit/entity/Player;)Lnet/kyori/adventure/key/Key;"}, {"nme": "lambda$contributePointers$0", "acc": 4106, "dsc": "(Lorg/bukkit/entity/Player;)Lnet/kyori/adventure/text/Component;"}], "flds": []}}}}