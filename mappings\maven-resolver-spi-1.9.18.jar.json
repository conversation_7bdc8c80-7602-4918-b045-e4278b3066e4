{"md5": "b99c631156da02290d0c95cf40c4917b", "sha2": "7fa176b3353ef6d78d02db39e025f3c27a983158", "sha256": "d364fce9a17b0e0b073c26efa92af95b29c00c42943dced4a1168a7923fd3fe1", "contents": {"classes": {"org/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactorySupport.class": {"ver": 52, "acc": 1057, "nme": "org/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactorySupport", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getFileExtension", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "fileExtension", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/eclipse/aether/spi/connector/RepositoryConnector.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/spi/connector/RepositoryConnector", "super": "java/lang/Object", "mthds": [{"nme": "get", "acc": 1025, "dsc": "(Lja<PERSON>/util/Collection;Ljava/util/Collection;)V", "sig": "(Ljava/util/Collection<+Lorg/eclipse/aether/spi/connector/ArtifactDownload;>;Ljava/util/Collection<+Lorg/eclipse/aether/spi/connector/MetadataDownload;>;)V"}, {"nme": "put", "acc": 1025, "dsc": "(Lja<PERSON>/util/Collection;Ljava/util/Collection;)V", "sig": "(Ljava/util/Collection<+Lorg/eclipse/aether/spi/connector/ArtifactUpload;>;Ljava/util/Collection<+Lorg/eclipse/aether/spi/connector/MetadataUpload;>;)V"}, {"nme": "close", "acc": 1025, "dsc": "()V"}], "flds": []}, "org/eclipse/aether/spi/connector/transport/TransporterFactory.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/spi/connector/transport/TransporterFactory", "super": "java/lang/Object", "mthds": [{"nme": "newInstance", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/spi/connector/transport/Transporter;", "exs": ["org/eclipse/aether/transfer/NoTransporterException"]}, {"nme": "getPriority", "acc": 1025, "dsc": "()F"}], "flds": []}, "org/eclipse/aether/spi/connector/transport/AbstractTransporter.class": {"ver": 52, "acc": 1057, "nme": "org/eclipse/aether/spi/connector/transport/AbstractTransporter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "peek", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/connector/transport/PeekTask;)V", "exs": ["java/lang/Exception"]}, {"nme": "implPeek", "acc": 1028, "dsc": "(Lorg/eclipse/aether/spi/connector/transport/PeekTask;)V", "exs": ["java/lang/Exception"]}, {"nme": "get", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/connector/transport/GetTask;)V", "exs": ["java/lang/Exception"]}, {"nme": "implGet", "acc": 1028, "dsc": "(Lorg/eclipse/aether/spi/connector/transport/GetTask;)V", "exs": ["java/lang/Exception"]}, {"nme": "utilGet", "acc": 4, "dsc": "(Lorg/eclipse/aether/spi/connector/transport/GetTask;Ljava/io/InputStream;ZJZ)V", "exs": ["java/io/IOException", "org/eclipse/aether/transfer/TransferCancelledException"]}, {"nme": "put", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/connector/transport/PutTask;)V", "exs": ["java/lang/Exception"]}, {"nme": "implPut", "acc": 1028, "dsc": "(Lorg/eclipse/aether/spi/connector/transport/PutTask;)V", "exs": ["java/lang/Exception"]}, {"nme": "utilPut", "acc": 4, "dsc": "(Lorg/eclipse/aether/spi/connector/transport/PutTask;Ljava/io/OutputStream;Z)V", "exs": ["java/io/IOException", "org/eclipse/aether/transfer/TransferCancelledException"]}, {"nme": "close", "acc": 1, "dsc": "()V"}, {"nme": "implClose", "acc": 1028, "dsc": "()V"}, {"nme": "failIfClosed", "acc": 2, "dsc": "(Lorg/eclipse/aether/spi/connector/transport/TransportTask;)V"}, {"nme": "copy", "acc": 10, "dsc": "(Ljava/io/OutputStream;Ljava/io/InputStream;Lorg/eclipse/aether/spi/connector/transport/TransportListener;)V", "exs": ["java/io/IOException", "org/eclipse/aether/transfer/TransferCancelledException"]}], "flds": [{"acc": 18, "nme": "closed", "dsc": "Ljava/util/concurrent/atomic/AtomicBoolean;"}]}, "org/eclipse/aether/spi/log/LoggerFactory.class": {"ver": 52, "acc": 132609, "nme": "org/eclipse/aether/spi/log/LoggerFactory", "super": "java/lang/Object", "mthds": [{"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/spi/log/Logger;"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "org/eclipse/aether/spi/locator/Service.class": {"ver": 52, "acc": 132609, "nme": "org/eclipse/aether/spi/locator/Service", "super": "java/lang/Object", "mthds": [{"nme": "initService", "acc": 1025, "dsc": "(Lorg/eclipse/aether/spi/locator/ServiceLocator;)V"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "org/eclipse/aether/spi/connector/checksum/ChecksumAlgorithm.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/spi/connector/checksum/ChecksumAlgorithm", "super": "java/lang/Object", "mthds": [{"nme": "update", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/nio/<PERSON>;)V"}, {"nme": "checksum", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "org/eclipse/aether/spi/connector/transport/PeekTask.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/spi/connector/transport/PeekTask", "super": "org/eclipse/aether/spi/connector/transport/TransportTask", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/net/URI;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "org/eclipse/aether/spi/connector/transport/PutTask.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/spi/connector/transport/PutTask", "super": "org/eclipse/aether/spi/connector/transport/TransportTask", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/net/URI;)V"}, {"nme": "newInputStream", "acc": 1, "dsc": "()Ljava/io/InputStream;", "exs": ["java/io/IOException"]}, {"nme": "getDataLength", "acc": 1, "dsc": "()J"}, {"nme": "getDataFile", "acc": 1, "dsc": "()Ljava/io/File;"}, {"nme": "setDataFile", "acc": 1, "dsc": "(Ljava/io/File;)Lorg/eclipse/aether/spi/connector/transport/PutTask;"}, {"nme": "setDataBytes", "acc": 1, "dsc": "([B)Lorg/eclipse/aether/spi/connector/transport/PutTask;"}, {"nme": "setDataString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/spi/connector/transport/PutTask;"}, {"nme": "setListener", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/connector/transport/TransportListener;)Lorg/eclipse/aether/spi/connector/transport/PutTask;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setListener", "acc": 4161, "dsc": "(Lorg/eclipse/aether/spi/connector/transport/TransportListener;)Lorg/eclipse/aether/spi/connector/transport/TransportTask;"}], "flds": [{"acc": 2, "nme": "dataFile", "dsc": "Ljava/io/File;"}, {"acc": 2, "nme": "dataBytes", "dsc": "[B"}]}, "org/eclipse/aether/spi/connector/layout/RepositoryLayout$ChecksumLocation.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/spi/connector/layout/RepositoryLayout$ChecksumLocation", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/net/URI;Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;)V"}, {"nme": "forLocation", "acc": 9, "dsc": "(Ljava/net/URI;Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;)Lorg/eclipse/aether/spi/connector/layout/RepositoryLayout$ChecksumLocation;"}, {"nme": "verify", "acc": 10, "dsc": "(Ljava/net/URI;Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;)V"}, {"nme": "getChecksumAlgorithmFactory", "acc": 1, "dsc": "()Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;"}, {"nme": "getLocation", "acc": 1, "dsc": "()Ljava/net/URI;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "location", "dsc": "Ljava/net/URI;"}, {"acc": 18, "nme": "checksumAlgorithmFactory", "dsc": "Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;"}]}, "org/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactorySelector.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactorySelector", "super": "java/lang/Object", "mthds": [{"nme": "select", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;"}, {"nme": "selectList", "acc": 1025, "dsc": "(L<PERSON><PERSON>/util/Collection;)Ljava/util/List;", "sig": "(Ljava/util/Collection<Ljava/lang/String;>;)Ljava/util/List<Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;>;"}, {"nme": "getChecksumAlgorithmFactories", "acc": 1025, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;>;"}, {"nme": "isChecksumExtension", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}], "flds": []}, "org/eclipse/aether/spi/connector/layout/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/spi/connector/layout/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/spi/connector/filter/RemoteRepositoryFilterSource.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/spi/connector/filter/RemoteRepositoryFilterSource", "super": "java/lang/Object", "mthds": [{"nme": "getRemoteRepositoryFilter", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter;"}], "flds": []}, "org/eclipse/aether/spi/connector/layout/RepositoryLayoutProvider.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/spi/connector/layout/RepositoryLayoutProvider", "super": "java/lang/Object", "mthds": [{"nme": "newRepositoryLayout", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/spi/connector/layout/RepositoryLayout;", "exs": ["org/eclipse/aether/transfer/NoRepositoryLayoutException"]}], "flds": []}, "org/eclipse/aether/spi/log/NullLoggerFactory.class": {"ver": 52, "acc": 131121, "nme": "org/eclipse/aether/spi/log/NullLoggerFactory", "super": "java/lang/Object", "mthds": [{"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/spi/log/Logger;"}, {"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getSafeLogger", "acc": 9, "dsc": "(Lorg/eclipse/aether/spi/log/LoggerFactory;<PERSON><PERSON><PERSON>/lang/Class;)Lorg/eclipse/aether/spi/log/Logger;", "sig": "(Lorg/eclipse/aether/spi/log/LoggerFactory;Lja<PERSON>/lang/Class<*>;)Lorg/eclipse/aether/spi/log/Logger;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "INSTANCE", "dsc": "Lorg/eclipse/aether/spi/log/LoggerFactory;"}, {"acc": 25, "nme": "LOGGER", "dsc": "Lorg/eclipse/aether/spi/log/Logger;"}], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "org/eclipse/aether/spi/connector/checksum/ChecksumPolicyProvider.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/spi/connector/checksum/ChecksumPolicyProvider", "super": "java/lang/Object", "mthds": [{"nme": "newChecksumPolicy", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/transfer/TransferResource;Ljava/lang/String;)Lorg/eclipse/aether/spi/connector/checksum/ChecksumPolicy;"}, {"nme": "getEffectiveChecksumPolicy", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;"}], "flds": []}, "org/eclipse/aether/spi/connector/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/spi/connector/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/spi/connector/checksum/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/spi/connector/checksum/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/spi/log/Logger.class": {"ver": 52, "acc": 132609, "nme": "org/eclipse/aether/spi/log/Logger", "super": "java/lang/Object", "mthds": [{"nme": "isDebugEnabled", "acc": 1025, "dsc": "()Z"}, {"nme": "debug", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "debug", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "is<PERSON>arn<PERSON>nabled", "acc": 1025, "dsc": "()Z"}, {"nme": "warn", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "warn", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "org/eclipse/aether/spi/resolution/ArtifactResolverPostProcessor.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/spi/resolution/ArtifactResolverPostProcessor", "super": "java/lang/Object", "mthds": [{"nme": "postProcess", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Ljava/util/List<Lorg/eclipse/aether/resolution/ArtifactResult;>;)V"}], "flds": []}, "org/eclipse/aether/spi/synccontext/SyncContextFactory.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/spi/synccontext/SyncContextFactory", "super": "java/lang/Object", "mthds": [{"nme": "newInstance", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Z)Lorg/eclipse/aether/SyncContext;"}], "flds": []}, "org/eclipse/aether/spi/connector/transport/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/spi/connector/transport/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory", "super": "java/lang/Object", "mthds": [{"nme": "getName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getFileExtension", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getAlgorithm", "acc": 1025, "dsc": "()Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithm;"}], "flds": []}, "org/eclipse/aether/spi/connector/transport/TransportListener.class": {"ver": 52, "acc": 1057, "nme": "org/eclipse/aether/spi/connector/transport/TransportListener", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "transportStarted", "acc": 1, "dsc": "(JJ)V", "exs": ["org/eclipse/aether/transfer/TransferCancelledException"]}, {"nme": "transportProgressed", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/nio/<PERSON>;)V", "exs": ["org/eclipse/aether/transfer/TransferCancelledException"]}], "flds": []}, "org/eclipse/aether/spi/localrepo/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/spi/localrepo/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/spi/connector/checksum/ProvidedChecksumsSource.class": {"ver": 52, "acc": 132609, "nme": "org/eclipse/aether/spi/connector/checksum/ProvidedChecksumsSource", "super": "java/lang/Object", "mthds": [{"nme": "getProvidedArtifactChecksums", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/spi/connector/ArtifactDownload;Ljava/util/List;)Ljava/util/Map;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/spi/connector/ArtifactDownload;Ljava/util/List<Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;>;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "org/eclipse/aether/spi/connector/MetadataDownload.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/spi/connector/MetadataDownload", "super": "org/eclipse/aether/spi/connector/MetadataTransfer", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;Ljava/lang/String;Ljava/io/File;Ljava/lang/String;)V"}, {"nme": "setMetadata", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;)Lorg/eclipse/aether/spi/connector/MetadataDownload;"}, {"nme": "setFile", "acc": 1, "dsc": "(Ljava/io/File;)Lorg/eclipse/aether/spi/connector/MetadataDownload;"}, {"nme": "getChecksumPolicy", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setChecksumPolicy", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/spi/connector/MetadataDownload;"}, {"nme": "getRequestContext", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setRequestContext", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/spi/connector/MetadataDownload;"}, {"nme": "getRepositories", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"nme": "setRepositories", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/eclipse/aether/spi/connector/MetadataDownload;", "sig": "(Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;)Lorg/eclipse/aether/spi/connector/MetadataDownload;"}, {"nme": "setException", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/MetadataTransferException;)Lorg/eclipse/aether/spi/connector/MetadataDownload;"}, {"nme": "setListener", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferListener;)Lorg/eclipse/aether/spi/connector/MetadataDownload;"}, {"nme": "setTrace", "acc": 1, "dsc": "(Lorg/eclipse/aether/RequestTrace;)Lorg/eclipse/aether/spi/connector/MetadataDownload;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setException", "acc": 4161, "dsc": "(Lorg/eclipse/aether/transfer/MetadataTransferException;)Lorg/eclipse/aether/spi/connector/MetadataTransfer;"}, {"nme": "setFile", "acc": 4161, "dsc": "(Ljava/io/File;)Lorg/eclipse/aether/spi/connector/MetadataTransfer;"}, {"nme": "setMetadata", "acc": 4161, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;)Lorg/eclipse/aether/spi/connector/MetadataTransfer;"}, {"nme": "setTrace", "acc": 4161, "dsc": "(Lorg/eclipse/aether/RequestTrace;)Lorg/eclipse/aether/spi/connector/Transfer;"}, {"nme": "setListener", "acc": 4161, "dsc": "(Lorg/eclipse/aether/transfer/TransferListener;)Lorg/eclipse/aether/spi/connector/Transfer;"}], "flds": [{"acc": 2, "nme": "checksumPolicy", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "context", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "repositories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}]}, "org/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter", "super": "java/lang/Object", "mthds": [{"nme": "acceptArtifact", "acc": 1025, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter$Result;"}, {"nme": "acceptMetadata", "acc": 1025, "dsc": "(Lorg/eclipse/aether/repository/RemoteRepository;Lorg/eclipse/aether/metadata/Metadata;)Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter$Result;"}], "flds": []}, "org/eclipse/aether/spi/connector/transport/Transporter.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/spi/connector/transport/Transporter", "super": "java/lang/Object", "mthds": [{"nme": "classify", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)I"}, {"nme": "peek", "acc": 1025, "dsc": "(Lorg/eclipse/aether/spi/connector/transport/PeekTask;)V", "exs": ["java/lang/Exception"]}, {"nme": "get", "acc": 1025, "dsc": "(Lorg/eclipse/aether/spi/connector/transport/GetTask;)V", "exs": ["java/lang/Exception"]}, {"nme": "put", "acc": 1025, "dsc": "(Lorg/eclipse/aether/spi/connector/transport/PutTask;)V", "exs": ["java/lang/Exception"]}, {"nme": "close", "acc": 1025, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "ERROR_OTHER", "dsc": "I", "val": 0}, {"acc": 25, "nme": "ERROR_NOT_FOUND", "dsc": "I", "val": 1}]}, "org/eclipse/aether/spi/synccontext/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/spi/synccontext/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmHelper.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmHelper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "calculate", "acc": 9, "dsc": "([B<PERSON>java/util/List;)Ljava/util/Map;", "sig": "([BLjava/util/List<Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;>;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;", "exs": ["java/io/IOException"]}, {"nme": "calculate", "acc": 9, "dsc": "(Ljava/io/File;Ljava/util/List;)Ljava/util/Map;", "sig": "(Ljava/io/File;Ljava/util/List<Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;>;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;", "exs": ["java/io/IOException"]}, {"nme": "calculate", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;L<PERSON><PERSON>/util/List;)Ljava/util/Map;", "sig": "(Ljava/io/InputStream;Ljava/util/List<Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;>;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;", "exs": ["java/io/IOException"]}, {"nme": "lambda$calculate$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/LinkedHashMap;Ljava/lang/String;Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithm;)V"}, {"nme": "lambda$calculate$0", "acc": 4106, "dsc": "(L<PERSON><PERSON>/util/LinkedHashMap;Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;)V"}], "flds": []}, "org/eclipse/aether/spi/io/FileProcessor.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/spi/io/FileProcessor", "super": "java/lang/Object", "mthds": [{"nme": "mkdirs", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)Z"}, {"nme": "write", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/io/File;Ljava/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1025, "dsc": "(Lja<PERSON>/io/File;Ljava/io/InputStream;)V", "exs": ["java/io/IOException"]}, {"nme": "move", "acc": 1025, "dsc": "(Ljava/io/File;Ljava/io/File;)V", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 1025, "dsc": "(Ljava/io/File;Ljava/io/File;)V", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 1025, "dsc": "(Ljava/io/File;Ljava/io/File;Lorg/eclipse/aether/spi/io/FileProcessor$ProgressListener;)J", "exs": ["java/io/IOException"]}, {"nme": "readChecksum", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)Ljava/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "writeChecksum", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/io/File;Ljava/lang/String;)V", "exs": ["java/io/IOException"]}], "flds": []}, "org/eclipse/aether/spi/connector/RepositoryConnectorFactory.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/spi/connector/RepositoryConnectorFactory", "super": "java/lang/Object", "mthds": [{"nme": "newInstance", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/spi/connector/RepositoryConnector;", "exs": ["org/eclipse/aether/transfer/NoRepositoryConnectorException"]}, {"nme": "getPriority", "acc": 1025, "dsc": "()F"}], "flds": []}, "org/eclipse/aether/spi/connector/ArtifactTransfer.class": {"ver": 52, "acc": 1057, "nme": "org/eclipse/aether/spi/connector/ArtifactTransfer", "super": "org/eclipse/aether/spi/connector/Transfer", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "getArtifact", "acc": 1, "dsc": "()Lorg/eclipse/aether/artifact/Artifact;"}, {"nme": "setArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/spi/connector/ArtifactTransfer;"}, {"nme": "getFile", "acc": 1, "dsc": "()Ljava/io/File;"}, {"nme": "setFile", "acc": 1, "dsc": "(Ljava/io/File;)Lorg/eclipse/aether/spi/connector/ArtifactTransfer;"}, {"nme": "getException", "acc": 1, "dsc": "()Lorg/eclipse/aether/transfer/ArtifactTransferException;"}, {"nme": "setException", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/ArtifactTransferException;)Lorg/eclipse/aether/spi/connector/ArtifactTransfer;"}, {"nme": "getException", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Exception;"}], "flds": [{"acc": 2, "nme": "artifact", "dsc": "Lorg/eclipse/aether/artifact/Artifact;"}, {"acc": 2, "nme": "file", "dsc": "Ljava/io/File;"}, {"acc": 2, "nme": "exception", "dsc": "Lorg/eclipse/aether/transfer/ArtifactTransferException;"}]}, "org/eclipse/aether/spi/connector/ArtifactDownload.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/spi/connector/ArtifactDownload", "super": "org/eclipse/aether/spi/connector/ArtifactTransfer", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/lang/String;Ljava/io/File;Ljava/lang/String;)V"}, {"nme": "setArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/spi/connector/ArtifactDownload;"}, {"nme": "setFile", "acc": 1, "dsc": "(Ljava/io/File;)Lorg/eclipse/aether/spi/connector/ArtifactDownload;"}, {"nme": "isExistenceCheck", "acc": 1, "dsc": "()Z"}, {"nme": "setExistenceCheck", "acc": 1, "dsc": "(Z)Lorg/eclipse/aether/spi/connector/ArtifactDownload;"}, {"nme": "getChecksumPolicy", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setChecksumPolicy", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/spi/connector/ArtifactDownload;"}, {"nme": "getRequestContext", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setRequestContext", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/spi/connector/ArtifactDownload;"}, {"nme": "getSupportedContexts", "acc": 1, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Ljava/lang/String;>;"}, {"nme": "setSupportedContexts", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Lorg/eclipse/aether/spi/connector/ArtifactDownload;", "sig": "(Ljava/util/Collection<Ljava/lang/String;>;)Lorg/eclipse/aether/spi/connector/ArtifactDownload;"}, {"nme": "getRepositories", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}, {"nme": "setRepositories", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/eclipse/aether/spi/connector/ArtifactDownload;", "sig": "(Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;)Lorg/eclipse/aether/spi/connector/ArtifactDownload;"}, {"nme": "setException", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/ArtifactTransferException;)Lorg/eclipse/aether/spi/connector/ArtifactDownload;"}, {"nme": "setListener", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferListener;)Lorg/eclipse/aether/spi/connector/ArtifactDownload;"}, {"nme": "setTrace", "acc": 1, "dsc": "(Lorg/eclipse/aether/RequestTrace;)Lorg/eclipse/aether/spi/connector/ArtifactDownload;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setException", "acc": 4161, "dsc": "(Lorg/eclipse/aether/transfer/ArtifactTransferException;)Lorg/eclipse/aether/spi/connector/ArtifactTransfer;"}, {"nme": "setFile", "acc": 4161, "dsc": "(Ljava/io/File;)Lorg/eclipse/aether/spi/connector/ArtifactTransfer;"}, {"nme": "setArtifact", "acc": 4161, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/spi/connector/ArtifactTransfer;"}, {"nme": "setTrace", "acc": 4161, "dsc": "(Lorg/eclipse/aether/RequestTrace;)Lorg/eclipse/aether/spi/connector/Transfer;"}, {"nme": "setListener", "acc": 4161, "dsc": "(Lorg/eclipse/aether/transfer/TransferListener;)Lorg/eclipse/aether/spi/connector/Transfer;"}], "flds": [{"acc": 2, "nme": "existenceCheck", "dsc": "Z"}, {"acc": 2, "nme": "checksumPolicy", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "context", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "contexts", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Ljava/lang/String;>;"}, {"acc": 2, "nme": "repositories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/eclipse/aether/repository/RemoteRepository;>;"}]}, "org/eclipse/aether/spi/connector/transport/TransporterProvider.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/spi/connector/transport/TransporterProvider", "super": "java/lang/Object", "mthds": [{"nme": "newTransporter", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/spi/connector/transport/Transporter;", "exs": ["org/eclipse/aether/transfer/NoTransporterException"]}], "flds": []}, "org/eclipse/aether/spi/checksums/TrustedChecksumsSource$Writer.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/spi/checksums/TrustedChecksumsSource$Writer", "super": "java/lang/Object", "mthds": [{"nme": "addTrustedArtifactChecksums", "acc": 1025, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/ArtifactRepository;Ljava/util/List;Ljava/util/Map;)V", "sig": "(Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/ArtifactRepository;Ljava/util/List<Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;>;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V", "exs": ["java/io/IOException"]}], "flds": []}, "org/eclipse/aether/spi/connector/checksum/ChecksumPolicy$ChecksumKind.class": {"ver": 52, "acc": 16433, "nme": "org/eclipse/aether/spi/connector/checksum/ChecksumPolicy$ChecksumKind", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lorg/eclipse/aether/spi/connector/checksum/ChecksumPolicy$ChecksumKind;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/eclipse/aether/spi/connector/checksum/ChecksumPolicy$ChecksumKind;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Lorg/eclipse/aether/spi/connector/checksum/ChecksumPolicy$ChecksumKind;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "REMOTE_EXTERNAL", "dsc": "Lorg/eclipse/aether/spi/connector/checksum/ChecksumPolicy$ChecksumKind;"}, {"acc": 16409, "nme": "REMOTE_INCLUDED", "dsc": "Lorg/eclipse/aether/spi/connector/checksum/ChecksumPolicy$ChecksumKind;"}, {"acc": 16409, "nme": "PROVIDED", "dsc": "Lorg/eclipse/aether/spi/connector/checksum/ChecksumPolicy$ChecksumKind;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lorg/eclipse/aether/spi/connector/checksum/ChecksumPolicy$ChecksumKind;"}]}, "org/eclipse/aether/spi/connector/layout/RepositoryLayout.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/spi/connector/layout/RepositoryLayout", "super": "java/lang/Object", "mthds": [{"nme": "getChecksumAlgorithmFactories", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;>;"}, {"nme": "hasChecksums", "acc": 1025, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Z"}, {"nme": "getLocation", "acc": 1025, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Z)Ljava/net/URI;"}, {"nme": "getLocation", "acc": 1025, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;Z)Ljava/net/URI;"}, {"nme": "getChecksumLocations", "acc": 1025, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;ZLjava/net/URI;)Ljava/util/List;", "sig": "(Lorg/eclipse/aether/artifact/Artifact;ZLjava/net/URI;)Ljava/util/List<Lorg/eclipse/aether/spi/connector/layout/RepositoryLayout$ChecksumLocation;>;"}, {"nme": "getChecksumLocations", "acc": 1025, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;ZLjava/net/URI;)Ljava/util/List;", "sig": "(Lorg/eclipse/aether/metadata/Metadata;ZLjava/net/URI;)Ljava/util/List<Lorg/eclipse/aether/spi/connector/layout/RepositoryLayout$ChecksumLocation;>;"}], "flds": []}, "org/eclipse/aether/spi/connector/MetadataTransfer.class": {"ver": 52, "acc": 1057, "nme": "org/eclipse/aether/spi/connector/MetadataTransfer", "super": "org/eclipse/aether/spi/connector/Transfer", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "getMetadata", "acc": 1, "dsc": "()Lorg/eclipse/aether/metadata/Metadata;"}, {"nme": "setMetadata", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;)Lorg/eclipse/aether/spi/connector/MetadataTransfer;"}, {"nme": "getFile", "acc": 1, "dsc": "()Ljava/io/File;"}, {"nme": "setFile", "acc": 1, "dsc": "(Ljava/io/File;)Lorg/eclipse/aether/spi/connector/MetadataTransfer;"}, {"nme": "getException", "acc": 1, "dsc": "()Lorg/eclipse/aether/transfer/MetadataTransferException;"}, {"nme": "setException", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/MetadataTransferException;)Lorg/eclipse/aether/spi/connector/MetadataTransfer;"}, {"nme": "getException", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Exception;"}], "flds": [{"acc": 2, "nme": "metadata", "dsc": "Lorg/eclipse/aether/metadata/Metadata;"}, {"acc": 2, "nme": "file", "dsc": "Ljava/io/File;"}, {"acc": 2, "nme": "exception", "dsc": "Lorg/eclipse/aether/transfer/MetadataTransferException;"}]}, "org/eclipse/aether/spi/connector/Transfer.class": {"ver": 52, "acc": 1057, "nme": "org/eclipse/aether/spi/connector/Transfer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "getException", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/Exception;"}, {"nme": "getListener", "acc": 1, "dsc": "()Lorg/eclipse/aether/transfer/TransferListener;"}, {"nme": "setListener", "acc": 0, "dsc": "(Lorg/eclipse/aether/transfer/TransferListener;)Lorg/eclipse/aether/spi/connector/Transfer;"}, {"nme": "getTrace", "acc": 1, "dsc": "()Lorg/eclipse/aether/RequestTrace;"}, {"nme": "setTrace", "acc": 0, "dsc": "(Lorg/eclipse/aether/RequestTrace;)Lorg/eclipse/aether/spi/connector/Transfer;"}], "flds": [{"acc": 2, "nme": "listener", "dsc": "Lorg/eclipse/aether/transfer/TransferListener;"}, {"acc": 2, "nme": "trace", "dsc": "Lorg/eclipse/aether/RequestTrace;"}]}, "org/eclipse/aether/spi/checksums/ProvidedChecksumsSource.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/spi/checksums/ProvidedChecksumsSource", "super": "java/lang/Object", "mthds": [{"nme": "getProvidedArtifactChecksums", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/spi/connector/ArtifactDownload;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/util/List;)Ljava/util/Map;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/spi/connector/ArtifactDownload;Lorg/eclipse/aether/repository/RemoteRepository;Ljava/util/List<Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;>;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}], "flds": []}, "org/eclipse/aether/spi/locator/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/spi/locator/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/spi/io/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/spi/io/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/spi/log/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/eclipse/aether/spi/log/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/eclipse/aether/spi/connector/MetadataUpload.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/spi/connector/MetadataUpload", "super": "org/eclipse/aether/spi/connector/MetadataTransfer", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;Ljava/io/File;)V"}, {"nme": "setMetadata", "acc": 1, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;)Lorg/eclipse/aether/spi/connector/MetadataUpload;"}, {"nme": "setFile", "acc": 1, "dsc": "(Ljava/io/File;)Lorg/eclipse/aether/spi/connector/MetadataUpload;"}, {"nme": "setException", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/MetadataTransferException;)Lorg/eclipse/aether/spi/connector/MetadataUpload;"}, {"nme": "setListener", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferListener;)Lorg/eclipse/aether/spi/connector/MetadataUpload;"}, {"nme": "setTrace", "acc": 1, "dsc": "(Lorg/eclipse/aether/RequestTrace;)Lorg/eclipse/aether/spi/connector/MetadataUpload;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setException", "acc": 4161, "dsc": "(Lorg/eclipse/aether/transfer/MetadataTransferException;)Lorg/eclipse/aether/spi/connector/MetadataTransfer;"}, {"nme": "setFile", "acc": 4161, "dsc": "(Ljava/io/File;)Lorg/eclipse/aether/spi/connector/MetadataTransfer;"}, {"nme": "setMetadata", "acc": 4161, "dsc": "(Lorg/eclipse/aether/metadata/Metadata;)Lorg/eclipse/aether/spi/connector/MetadataTransfer;"}, {"nme": "setTrace", "acc": 4161, "dsc": "(Lorg/eclipse/aether/RequestTrace;)Lorg/eclipse/aether/spi/connector/Transfer;"}, {"nme": "setListener", "acc": 4161, "dsc": "(Lorg/eclipse/aether/transfer/TransferListener;)Lorg/eclipse/aether/spi/connector/Transfer;"}], "flds": []}, "org/eclipse/aether/spi/checksums/TrustedChecksumsSource.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/spi/checksums/TrustedChecksumsSource", "super": "java/lang/Object", "mthds": [{"nme": "getTrustedArtifactChecksums", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/ArtifactRepository;Ljava/util/List;)Ljava/util/Map;", "sig": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/repository/ArtifactRepository;Ljava/util/List<Lorg/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory;>;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "getTrustedArtifactChecksumsWriter", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;)Lorg/eclipse/aether/spi/checksums/TrustedChecksumsSource$Writer;"}], "flds": []}, "org/eclipse/aether/spi/io/FileProcessor$ProgressListener.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/spi/io/FileProcessor$ProgressListener", "super": "java/lang/Object", "mthds": [{"nme": "progressed", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/nio/<PERSON>;)V", "exs": ["java/io/IOException"]}], "flds": []}, "org/eclipse/aether/spi/connector/layout/RepositoryLayoutFactory.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/spi/connector/layout/RepositoryLayoutFactory", "super": "java/lang/Object", "mthds": [{"nme": "newInstance", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/RemoteRepository;)Lorg/eclipse/aether/spi/connector/layout/RepositoryLayout;", "exs": ["org/eclipse/aether/transfer/NoRepositoryLayoutException"]}, {"nme": "getPriority", "acc": 1025, "dsc": "()F"}], "flds": []}, "org/eclipse/aether/spi/connector/transport/TransportTask.class": {"ver": 52, "acc": 1057, "nme": "org/eclipse/aether/spi/connector/transport/TransportTask", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "getLocation", "acc": 1, "dsc": "()Ljava/net/URI;"}, {"nme": "setLocation", "acc": 0, "dsc": "(Ljava/net/URI;)Lorg/eclipse/aether/spi/connector/transport/TransportTask;"}, {"nme": "getListener", "acc": 1, "dsc": "()Lorg/eclipse/aether/spi/connector/transport/TransportListener;"}, {"nme": "setListener", "acc": 0, "dsc": "(Lorg/eclipse/aether/spi/connector/transport/TransportListener;)Lorg/eclipse/aether/spi/connector/transport/TransportTask;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "NOOP", "dsc": "Lorg/eclipse/aether/spi/connector/transport/TransportListener;"}, {"acc": 24, "nme": "EMPTY", "dsc": "[B"}, {"acc": 2, "nme": "location", "dsc": "Ljava/net/URI;"}, {"acc": 2, "nme": "listener", "dsc": "Lorg/eclipse/aether/spi/connector/transport/TransportListener;"}]}, "org/eclipse/aether/spi/localrepo/LocalRepositoryManagerFactory.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/spi/localrepo/LocalRepositoryManagerFactory", "super": "java/lang/Object", "mthds": [{"nme": "newInstance", "acc": 1025, "dsc": "(Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/repository/LocalRepository;)Lorg/eclipse/aether/repository/LocalRepositoryManager;", "exs": ["org/eclipse/aether/repository/NoLocalRepositoryManagerException"]}, {"nme": "getPriority", "acc": 1025, "dsc": "()F"}], "flds": []}, "org/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter$Result.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter$Result", "super": "java/lang/Object", "mthds": [{"nme": "isAccepted", "acc": 1025, "dsc": "()Z"}, {"nme": "reasoning", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "org/eclipse/aether/spi/log/NullLogger.class": {"ver": 52, "acc": 131120, "nme": "org/eclipse/aether/spi/log/NullLogger", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "isDebugEnabled", "acc": 1, "dsc": "()Z"}, {"nme": "debug", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "debug", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "is<PERSON>arn<PERSON>nabled", "acc": 1, "dsc": "()Z"}, {"nme": "warn", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "warn", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "org/eclipse/aether/spi/connector/ArtifactUpload.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/spi/connector/ArtifactUpload", "super": "org/eclipse/aether/spi/connector/ArtifactTransfer", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/io/File;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;Ljava/io/File;Lorg/eclipse/aether/transform/FileTransformer;)V"}, {"nme": "setArtifact", "acc": 1, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/spi/connector/ArtifactUpload;"}, {"nme": "setFile", "acc": 1, "dsc": "(Ljava/io/File;)Lorg/eclipse/aether/spi/connector/ArtifactUpload;"}, {"nme": "setException", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/ArtifactTransferException;)Lorg/eclipse/aether/spi/connector/ArtifactUpload;"}, {"nme": "setListener", "acc": 1, "dsc": "(Lorg/eclipse/aether/transfer/TransferListener;)Lorg/eclipse/aether/spi/connector/ArtifactUpload;"}, {"nme": "setTrace", "acc": 1, "dsc": "(Lorg/eclipse/aether/RequestTrace;)Lorg/eclipse/aether/spi/connector/ArtifactUpload;"}, {"nme": "setFileTransformer", "acc": 1, "dsc": "(Lorg/eclipse/aether/transform/FileTransformer;)Lorg/eclipse/aether/spi/connector/ArtifactUpload;"}, {"nme": "getFileTransformer", "acc": 1, "dsc": "()Lorg/eclipse/aether/transform/FileTransformer;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setException", "acc": 4161, "dsc": "(Lorg/eclipse/aether/transfer/ArtifactTransferException;)Lorg/eclipse/aether/spi/connector/ArtifactTransfer;"}, {"nme": "setFile", "acc": 4161, "dsc": "(Ljava/io/File;)Lorg/eclipse/aether/spi/connector/ArtifactTransfer;"}, {"nme": "setArtifact", "acc": 4161, "dsc": "(Lorg/eclipse/aether/artifact/Artifact;)Lorg/eclipse/aether/spi/connector/ArtifactTransfer;"}, {"nme": "setTrace", "acc": 4161, "dsc": "(Lorg/eclipse/aether/RequestTrace;)Lorg/eclipse/aether/spi/connector/Transfer;"}, {"nme": "setListener", "acc": 4161, "dsc": "(Lorg/eclipse/aether/transfer/TransferListener;)Lorg/eclipse/aether/spi/connector/Transfer;"}], "flds": [{"acc": 2, "nme": "fileTransformer", "dsc": "Lorg/eclipse/aether/transform/FileTransformer;"}]}, "org/eclipse/aether/spi/connector/transport/TransportTask$1.class": {"ver": 52, "acc": 32, "nme": "org/eclipse/aether/spi/connector/transport/TransportTask$1", "super": "org/eclipse/aether/spi/connector/transport/TransportListener", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}], "flds": []}, "org/eclipse/aether/spi/locator/ServiceLocator.class": {"ver": 52, "acc": 132609, "nme": "org/eclipse/aether/spi/locator/ServiceLocator", "super": "java/lang/Object", "mthds": [{"nme": "getService", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>ja<PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;)TT;"}, {"nme": "getServices", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/util/List;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;)Ljava/util/List<TT;>;"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "org/eclipse/aether/spi/connector/transport/GetTask.class": {"ver": 52, "acc": 49, "nme": "org/eclipse/aether/spi/connector/transport/GetTask", "super": "org/eclipse/aether/spi/connector/transport/TransportTask", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/net/URI;)V"}, {"nme": "newOutputStream", "acc": 1, "dsc": "()Ljava/io/OutputStream;", "exs": ["java/io/IOException"]}, {"nme": "newOutputStream", "acc": 1, "dsc": "(Z)Ljava/io/OutputStream;", "exs": ["java/io/IOException"]}, {"nme": "getDataFile", "acc": 1, "dsc": "()Ljava/io/File;"}, {"nme": "setDataFile", "acc": 1, "dsc": "(Ljava/io/File;)Lorg/eclipse/aether/spi/connector/transport/GetTask;"}, {"nme": "setDataFile", "acc": 1, "dsc": "(Ljava/io/File;Z)Lorg/eclipse/aether/spi/connector/transport/GetTask;"}, {"nme": "getResumeOffset", "acc": 1, "dsc": "()J"}, {"nme": "getDataBytes", "acc": 1, "dsc": "()[B"}, {"nme": "getDataString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setListener", "acc": 1, "dsc": "(Lorg/eclipse/aether/spi/connector/transport/TransportListener;)Lorg/eclipse/aether/spi/connector/transport/GetTask;"}, {"nme": "getChecksums", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;)Lorg/eclipse/aether/spi/connector/transport/GetTask;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setListener", "acc": 4161, "dsc": "(Lorg/eclipse/aether/spi/connector/transport/TransportListener;)Lorg/eclipse/aether/spi/connector/transport/TransportTask;"}], "flds": [{"acc": 2, "nme": "dataFile", "dsc": "Ljava/io/File;"}, {"acc": 2, "nme": "resume", "dsc": "Z"}, {"acc": 2, "nme": "dataBytes", "dsc": "Ljava/io/ByteArrayOutputStream;"}, {"acc": 2, "nme": "checksums", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}]}, "org/eclipse/aether/spi/connector/checksum/ChecksumPolicy.class": {"ver": 52, "acc": 1537, "nme": "org/eclipse/aether/spi/connector/checksum/ChecksumPolicy", "super": "java/lang/Object", "mthds": [{"nme": "onChecksumMatch", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/eclipse/aether/spi/connector/checksum/ChecksumPolicy$ChecksumKind;)Z"}, {"nme": "onChecksumMismatch", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/eclipse/aether/spi/connector/checksum/ChecksumPolicy$ChecksumKind;Lorg/eclipse/aether/transfer/ChecksumFailureException;)V", "exs": ["org/eclipse/aether/transfer/ChecksumFailureException"]}, {"nme": "onChecksumError", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/eclipse/aether/spi/connector/checksum/ChecksumPolicy$ChecksumKind;Lorg/eclipse/aether/transfer/ChecksumFailureException;)V", "exs": ["org/eclipse/aether/transfer/ChecksumFailureException"]}, {"nme": "onNoMoreChecksums", "acc": 1025, "dsc": "()V", "exs": ["org/eclipse/aether/transfer/ChecksumFailureException"]}, {"nme": "onTransferRetry", "acc": 1025, "dsc": "()V"}, {"nme": "onTransferChecksumFailure", "acc": 1025, "dsc": "(Lorg/eclipse/aether/transfer/ChecksumFailureException;)Z"}], "flds": []}}}}