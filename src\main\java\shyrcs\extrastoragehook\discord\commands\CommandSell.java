package shyrcs.extrastoragehook.discord.commands;


import net.dv8tion.jda.api.entities.Message;
import net.dv8tion.jda.api.entities.channel.middleman.MessageChannel;
import net.dv8tion.jda.api.events.interaction.command.SlashCommandInteractionEvent;
import net.dv8tion.jda.api.events.message.MessageReceivedEvent;
import net.dv8tion.jda.api.interactions.commands.OptionMapping;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitRunnable;
import shyrcs.extrastoragehook.application.Library;
import shyrcs.extrastoragehook.application.PluginBoot;
import shyrcs.extrastoragehook.economy.EconomyProvider;
import shyrcs.extrastoragehook.executor.DiscordExecutor;
import shyrcs.extrastoragehook.SbMagicHook;

import java.text.DecimalFormat;
import java.util.*;
import java.util.List;
import java.util.Map;

/**
 * Discord command để bán items từ kho ExtraStorage
 */
public class CommandSell extends DiscordExecutor {
    
    private static final DecimalFormat formatter = new DecimalFormat("#,###.##");
    
    public CommandSell() {
        super("sell", Library.config.getCommand("sell"));
    }
    
    @Override
    public void onSlashCommand(SlashCommandInteractionEvent event) {
        final String authorId = Objects.requireNonNull(event.getMember()).getId();
        
        if (!Library.storage.userConnected(authorId)) {
            event.reply(Library.config.getMessage("not-connected"))
                .setEphemeral(true).queue();
            return;
        }
        
        // Lấy parameters
        OptionMapping itemOption = event.getOption("item");
        OptionMapping amountOption = event.getOption("amount");

        final String itemName = itemOption != null ? itemOption.getAsString() : "all";
        final String amountStr = amountOption != null ? String.valueOf(amountOption.getAsInt()) : "all";

        event.deferReply().queue();

        new BukkitRunnable() {
            @Override
            public void run() {
                processSellCommand(event, null, authorId, itemName, amountStr);
            }
        }.runTaskAsynchronously(PluginBoot.main);
    }
    
    @Override
    public void onChatCommand(MessageReceivedEvent event) {
        final MessageChannel channel = event.getChannel();
        final Message message = event.getMessage();
        final String authorId = Objects.requireNonNull(event.getMember()).getId();
        
        if (!Library.storage.userConnected(authorId)) {
            channel.sendMessage(Library.config.getMessage("not-connected"))
                .setMessageReference(message).queue();
            return;
        }
        
        // Parse arguments
        String[] args = message.getContentRaw().split("\\s+");

        final String itemName = args.length >= 2 ? args[1] : "all";
        final String amountStr = args.length > 2 ? args[2] : "all";

        // Nếu chỉ có !sell thì mặc định là sell all
        if (args.length == 1) {
            channel.sendMessage("💰 Đang bán tất cả items trong kho...")
                .setMessageReference(message).queue();
        }

        new BukkitRunnable() {
            @Override
            public void run() {
                processSellCommand(null, event, authorId, itemName, amountStr);
            }
        }.runTaskAsynchronously(PluginBoot.main);
    }
    
    /**
     * Xử lý lệnh sell
     */
    private void processSellCommand(SlashCommandInteractionEvent slashEvent, MessageReceivedEvent chatEvent, 
                                  String authorId, String itemName, String amountStr) {
        try {
            UUID playerUuid = Library.storage.getMinecraftUUID(authorId);
            if (playerUuid == null) {
                sendErrorMessage(slashEvent, chatEvent, Library.config.getMessage("not-connected"));
                return;
            }
            
            OfflinePlayer player = Bukkit.getOfflinePlayer(playerUuid);
            Object storage = Library.extraStorageHook.getStorage(playerUuid);

            if (storage == null) {
                sendErrorMessage(slashEvent, chatEvent, "❌ Không thể truy cập kho của bạn!");
                return;
            }
            
            // Kiểm tra nếu là sell all
            if (itemName.equalsIgnoreCase("all")) {
                sellAllItems(slashEvent, chatEvent, player, playerUuid);
                return;
            }

            // Parse material
            Material material = parseMaterial(itemName);
            if (material == null) {
                sendErrorMessage(slashEvent, chatEvent, "❌ Item không hợp lệ: " + itemName);
                return;
            }

            String materialKey = material.name();

            // Kiểm tra item có trong kho không
            long availableAmount = Library.extraStorageHook.getItemAmount(playerUuid, materialKey);
            if (availableAmount <= 0) {
                sendErrorMessage(slashEvent, chatEvent, "❌ Bạn không có " + formatMaterialName(materialKey) + " trong kho!");
                return;
            }
            
            // Parse amount
            long sellAmount;
            if (amountStr.equalsIgnoreCase("all") || amountStr.equalsIgnoreCase("max")) {
                sellAmount = availableAmount;
            } else {
                try {
                    sellAmount = Long.parseLong(amountStr);
                    if (sellAmount <= 0) {
                        sendErrorMessage(slashEvent, chatEvent, "❌ Số lượng phải lớn hơn 0!");
                        return;
                    }
                } catch (NumberFormatException e) {
                    sendErrorMessage(slashEvent, chatEvent, "❌ Số lượng không hợp lệ: " + amountStr);
                    return;
                }
            }
            
            // Kiểm tra số lượng có đủ không
            if (sellAmount > availableAmount) {
                sendErrorMessage(slashEvent, chatEvent, 
                    "❌ Bạn chỉ có " + formatter.format(availableAmount) + " " + formatMaterialName(materialKey) + " trong kho!");
                return;
            }
            
            // Kiểm tra giới hạn sell
            int maxSellAmount = Library.config.getMaxSellAmount();
            if (sellAmount > maxSellAmount) {
                sendErrorMessage(slashEvent, chatEvent, 
                    "❌ Bạn chỉ có thể bán tối đa " + formatter.format(maxSellAmount) + " items mỗi lần!");
                return;
            }
            
            // Tạo ItemStack để tính giá
            ItemStack item = new ItemStack(material, (int) Math.min(sellAmount, Integer.MAX_VALUE));
            
            // Lấy economy provider và tính giá
            if (!Library.economyManager.hasProvider()) {
                sendErrorMessage(slashEvent, chatEvent, "❌ Hệ thống economy không khả dụng!");
                return;
            }

            EconomyProvider economyProvider = Library.economyManager.getCurrentProvider();
            
            // Sell item
            economyProvider.sellItem(player.getPlayer(), item, (int) sellAmount, result -> {
                if (!result.isSuccess()) {
                    sendErrorMessage(slashEvent, chatEvent, "❌ Không thể bán item này!");
                    return;
                }
                
                // Trừ item từ kho
                Library.extraStorageHook.removeItem(playerUuid, materialKey, (int) sellAmount);
                
                // Gửi thông báo thành công
                String successMessage = Library.config.getMessage("sell-output")
                    .replace("{amount}", formatter.format(sellAmount))
                    .replace("{item}", Library.config.getEmote(materialKey) + " " + formatMaterialName(materialKey))
                    .replace("{price}", formatter.format(result.getPrice()));

                sendSuccessMessage(slashEvent, chatEvent, successMessage);

                // Gửi thông báo vào Minecraft server
                sendMinecraftMessage(player, "minecraft-sell-output",
                    formatter.format(sellAmount),
                    formatMaterialName(materialKey),
                    formatter.format(result.getPrice()));
            });
            
        } catch (Exception e) {
            sendErrorMessage(slashEvent, chatEvent, Library.config.getMessage("error"));
        }
    }
    
    /**
     * Parse material từ string
     */
    private Material parseMaterial(String input) {
        try {
            // Thử parse trực tiếp
            return Material.valueOf(input.toUpperCase());
        } catch (IllegalArgumentException e) {
            // Thử với underscore
            try {
                return Material.valueOf(input.toUpperCase().replace(" ", "_"));
            } catch (IllegalArgumentException e2) {
                // Thử tìm material tương tự
                for (Material material : Material.values()) {
                    if (material.name().toLowerCase().contains(input.toLowerCase()) ||
                        material.name().toLowerCase().replace("_", "").equals(input.toLowerCase().replace(" ", ""))) {
                        return material;
                    }
                }
                return null;
            }
        }
    }
    

    
    /**
     * Format tên material
     */
    private String formatMaterialName(String materialKey) {
        String[] words = materialKey.toLowerCase().replace("_", " ").split(" ");
        StringBuilder result = new StringBuilder();
        for (String word : words) {
            if (word.length() > 0) {
                result.append(Character.toUpperCase(word.charAt(0)))
                      .append(word.substring(1))
                      .append(" ");
            }
        }
        return result.toString().trim();
    }
    
    /**
     * Gửi error message
     */
    private void sendErrorMessage(SlashCommandInteractionEvent slashEvent, MessageReceivedEvent chatEvent, String message) {
        if (slashEvent != null) {
            // Sử dụng editOriginal vì đã defer trong SlashListener
            slashEvent.getHook().editOriginal(message).queue();
        } else if (chatEvent != null) {
            chatEvent.getChannel().sendMessage(message)
                .setMessageReference(chatEvent.getMessage()).queue();
        }
    }

    /**
     * Gửi success message
     */
    private void sendSuccessMessage(SlashCommandInteractionEvent slashEvent, MessageReceivedEvent chatEvent, String message) {
        if (slashEvent != null) {
            // Sử dụng editOriginal vì đã defer trong SlashListener
            slashEvent.getHook().editOriginal(message).queue();
        } else if (chatEvent != null) {
            chatEvent.getChannel().sendMessage(message)
                .setMessageReference(chatEvent.getMessage()).queue();
        }
    }

    /**
     * Bán tất cả items trong kho
     */
    private void sellAllItems(SlashCommandInteractionEvent slashEvent, MessageReceivedEvent chatEvent,
                             OfflinePlayer player, UUID playerUuid) {
        try {
            // Lấy tất cả items trong kho
            Map<String, Object> allItems = Library.extraStorageHook.getAllItems(playerUuid);
            if (allItems == null || allItems.isEmpty()) {
                sendErrorMessage(slashEvent, chatEvent, Library.config.getMessage("no-sellable-items"));
                return;
            }

            // Lấy filter từ config nếu có
            List<String> itemFilter = Library.config.getConfig().getStringList("extrastorage.display.item-filter");
            boolean sellFilterOnly = Library.config.getConfig().getBoolean("extrastorage.display.sell-filter-only", true);

            // Kiểm tra economy provider
            if (!Library.economyManager.hasProvider()) {
                sendErrorMessage(slashEvent, chatEvent, "❌ Hệ thống economy không khả dụng!");
                return;
            }

            EconomyProvider economyProvider = Library.economyManager.getCurrentProvider();

            // Tạo danh sách items cần bán
            List<SellItem> itemsToSell = new ArrayList<>();

            for (Map.Entry<String, Object> entry : allItems.entrySet()) {
                String materialKey = entry.getKey();

                try {
                    long quantity = Library.extraStorageHook.getItemAmount(playerUuid, materialKey);
                    if (quantity <= 0) continue;

                    // Kiểm tra filter nếu bật
                    if (sellFilterOnly && !itemFilter.isEmpty() && !itemFilter.contains(materialKey)) {
                        continue;
                    }

                    // Parse material
                    Material material = parseMaterial(materialKey);
                    if (material == null) continue;

                    itemsToSell.add(new SellItem(materialKey, material, (int) quantity));
                } catch (Exception e) {
                    continue;
                }
            }

            if (itemsToSell.isEmpty()) {
                sendErrorMessage(slashEvent, chatEvent, Library.config.getMessage("no-sellable-items"));
                return;
            }

            // Bán tất cả items đồng bộ
            double totalPrice = 0.0;
            int totalItemsSold = 0;

            for (SellItem sellItem : itemsToSell) {
                try {
                    ItemStack item = new ItemStack(sellItem.material, sellItem.quantity);

                    // Tính giá đồng bộ
                    String priceStr = economyProvider.getPrice(player.getPlayer(), item, sellItem.quantity);
                    if (priceStr != null && !priceStr.isEmpty()) {
                        try {
                            // Parse price từ string (loại bỏ dấu phẩy)
                            double price = Double.parseDouble(priceStr.replace(",", ""));

                            if (price > 0) {
                                // Trừ item từ kho
                                Library.extraStorageHook.removeItem(playerUuid, sellItem.materialKey, sellItem.quantity);

                                // Thêm tiền cho player
                                if (player.getPlayer() != null) {
                                    // Sử dụng Vault để thêm tiền trực tiếp
                                    org.bukkit.Bukkit.getServer().getServicesManager()
                                        .getRegistration(net.milkbowl.vault.economy.Economy.class)
                                        .getProvider().depositPlayer(player.getPlayer(), price);
                                }

                                totalPrice += price;
                                totalItemsSold += sellItem.quantity;
                            }
                        } catch (NumberFormatException e) {
                            continue;
                        }
                    }
                } catch (Exception e) {
                    continue;
                }
            }

            // Gửi thông báo kết quả (chỉ 1 lần)
            if (totalPrice > 0) {
                String successMessage = Library.config.getMessage("sell-all-output")
                    .replace("{total}", formatter.format(totalPrice));

                sendSuccessMessage(slashEvent, chatEvent, successMessage);

                // Gửi thông báo vào Minecraft server
                sendMinecraftMessage(player, "minecraft-sell-all-output",
                    formatter.format(totalPrice));
            } else {
                sendErrorMessage(slashEvent, chatEvent, Library.config.getMessage("no-sellable-items"));
            }

        } catch (Exception e) {
            sendErrorMessage(slashEvent, chatEvent, Library.config.getMessage("error"));
        }
    }

    /**
     * Class helper để lưu thông tin item cần bán
     */
    private static class SellItem {
        final String materialKey;
        final Material material;
        final int quantity;

        SellItem(String materialKey, Material material, int quantity) {
            this.materialKey = materialKey;
            this.material = material;
            this.quantity = quantity;
        }
    }

    /**
     * Gửi thông báo vào Minecraft server cho single item
     */
    private void sendMinecraftMessage(OfflinePlayer player, String messageKey, String amount, String item, String price) {
        if (player == null || !player.isOnline()) {
            return;
        }

        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    Player onlinePlayer = player.getPlayer();
                    if (onlinePlayer != null && onlinePlayer.isOnline()) {
                        String message = Library.config.getMinecraftMessage(messageKey)
                            .replace("{amount}", amount)
                            .replace("{item}", item)
                            .replace("{price}", price);

                        onlinePlayer.sendMessage(message);
                    }
                } catch (Exception e) {
                    // Bỏ qua lỗi gửi message
                }
            }
        }.runTask(PluginBoot.main);
    }

    /**
     * Gửi thông báo vào Minecraft server cho sell all
     */
    private void sendMinecraftMessage(OfflinePlayer player, String messageKey, String total) {
        if (player == null || !player.isOnline()) {
            return;
        }

        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    Player onlinePlayer = player.getPlayer();
                    if (onlinePlayer != null && onlinePlayer.isOnline()) {
                        String message = Library.config.getMinecraftMessage(messageKey)
                            .replace("{total}", total);

                        onlinePlayer.sendMessage(message);
                    }
                } catch (Exception e) {
                    // Bỏ qua lỗi gửi message
                }
            }
        }.runTask(PluginBoot.main);
    }

}
