#!/bin/bash

echo "========================================"
echo "   SbMagicHook Obfuscated Build Script"
echo "========================================"

echo
echo "[1/4] Building project with Maven..."
mvn clean package -DskipTests
if [ $? -ne 0 ]; then
    echo "ERROR: Maven build failed!"
    exit 1
fi

echo
echo "[2/4] Checking if Skidfuscator exists..."
if [ ! -f "skidfuscator.jar" ]; then
    echo "ERROR: skidfuscator.jar not found!"
    echo "Please download it from: https://github.com/skidfuscatordev/skidfuscator-java-obfuscator/releases"
    exit 1
fi

echo
echo "[3/4] Running Skidfuscator obfuscation..."
java -jar skidfuscator.jar obfuscate target/SbMagicHook.jar -li=target/libs -o=target/SbMagicHook-protected.jar

if [ $? -ne 0 ]; then
    echo "ERROR: Obfuscation failed!"
    exit 1
fi

echo
echo "[4/4] Obfuscation completed!"
echo "========================================"
echo "   Build completed successfully!"
echo "   Protected JAR: target/SbMagicHook-protected.jar"
echo "========================================"
