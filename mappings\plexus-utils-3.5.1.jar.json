{"md5": "cdec471a77f52e687d0df4c43f392a71", "sha2": "c6bfb17c97ecc8863e88778ea301be742c62b06d", "sha256": "86e0255d4c879c61b4833ed7f13124e8bb679df47debb127326e7db7dd49a07b", "contents": {"classes": {"org/codehaus/plexus/util/xml/pull/EntityReplacementMap.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/xml/pull/EntityReplacementMap", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "([[<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "defineEntityReplacementText", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "newString", "acc": 2, "dsc": "([CII)Ljava/lang/String;"}, {"nme": "fastHash", "acc": 10, "dsc": "([CII)I"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16, "nme": "entityName", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 16, "nme": "entityNameBuf", "dsc": "[[C"}, {"acc": 16, "nme": "entityReplacement", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 16, "nme": "entityReplacementBuf", "dsc": "[[C"}, {"acc": 0, "nme": "entityEnd", "dsc": "I"}, {"acc": 16, "nme": "entityNameHash", "dsc": "[I"}, {"acc": 25, "nme": "defaultEntityReplacementMap", "dsc": "Lorg/codehaus/plexus/util/xml/pull/EntityReplacementMap;"}]}, "org/codehaus/plexus/util/introspection/ReflectionValueExtractor.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/introspection/ReflectionValueExtractor", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "evaluate", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}, {"nme": "evaluate", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;Z)Ljava/lang/Object;", "exs": ["java/lang/Exception"]}, {"nme": "getMappedValue", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;II<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}, {"nme": "getIndexedValue", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;II<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}, {"nme": "getPropertyValue", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}, {"nme": "getClassMap", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Lorg/codehaus/plexus/util/introspection/ClassMap;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Lorg/codehaus/plexus/util/introspection/ClassMap;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CLASS_ARGS", "dsc": "[Ljava/lang/Class;", "sig": "[Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "OBJECT_ARGS", "dsc": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 26, "nme": "classMaps", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Class<*>;Ljava/lang/ref/WeakReference<Lorg/codehaus/plexus/util/introspection/ClassMap;>;>;"}, {"acc": 24, "nme": "EOF", "dsc": "I", "val": -1}, {"acc": 24, "nme": "PROPERTY_START", "dsc": "C", "val": 46}, {"acc": 24, "nme": "INDEXED_START", "dsc": "C", "val": 91}, {"acc": 24, "nme": "INDEXED_END", "dsc": "C", "val": 93}, {"acc": 24, "nme": "MAPPED_START", "dsc": "C", "val": 40}, {"acc": 24, "nme": "MAPPED_END", "dsc": "C", "val": 41}]}, "org/codehaus/plexus/util/InterpolationFilterReader.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/InterpolationFilterReader", "super": "java/io/FilterReader", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;<PERSON><PERSON><PERSON>/util/Map;Lja<PERSON>/lang/String;Lja<PERSON>/lang/String;)V", "sig": "(<PERSON><PERSON><PERSON>/io/Reader;Ljava/util/Map<*Ljava/lang/Object;>;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;<PERSON>ja<PERSON>/util/Map;)V", "sig": "(<PERSON><PERSON><PERSON>/io/Reader;Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;)V"}, {"nme": "skip", "acc": 1, "dsc": "(J)J", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 1, "dsc": "([CII)I", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 1, "dsc": "()I", "exs": ["java/io/IOException"]}], "flds": [{"acc": 2, "nme": "replaceData", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "replaceIndex", "dsc": "I"}, {"acc": 2, "nme": "previousIndex", "dsc": "I"}, {"acc": 2, "nme": "variables", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<*Ljava/lang/Object;>;"}, {"acc": 2, "nme": "beginToken", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "endToken", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "beginToken<PERSON>ength", "dsc": "I"}, {"acc": 2, "nme": "endT<PERSON><PERSON><PERSON>th", "dsc": "I"}, {"acc": 26, "nme": "DEFAULT_BEGIN_TOKEN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "${"}, {"acc": 26, "nme": "DEFAULT_END_TOKEN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "}"}]}, "org/codehaus/plexus/util/cli/shell/CmdShell.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/cli/shell/CmdShell", "super": "org/codehaus/plexus/util/cli/shell/Shell", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getCommandLine", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;)Ljava/util/List;", "sig": "(Lja<PERSON>/lang/String;[Ljava/lang/String;)Ljava/util/List<Ljava/lang/String;>;"}], "flds": []}, "org/codehaus/plexus/util/cli/shell/Shell.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/cli/shell/Shell", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "setUnconditionalQuoting", "acc": 1, "dsc": "(Z)V"}, {"nme": "setShellCommand", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getShellCommand", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setShellArgs", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getShellArgs", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getCommandLine", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;)Ljava/util/List;", "sig": "(Lja<PERSON>/lang/String;[Ljava/lang/String;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "quoteOneItem", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)Ljava/lang/String;"}, {"nme": "getRawCommandLine", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;)Ljava/util/List;", "sig": "(Lja<PERSON>/lang/String;[Ljava/lang/String;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "getQuotingTriggerChars", "acc": 4, "dsc": "()[C"}, {"nme": "getExecutionPreamble", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getEscapeChars", "acc": 4, "dsc": "(ZZ)[C"}, {"nme": "isDoubleQuotedArgumentEscaped", "acc": 4, "dsc": "()Z"}, {"nme": "isSingleQuotedArgumentEscaped", "acc": 4, "dsc": "()Z"}, {"nme": "isDoubleQuotedExecutableEscaped", "acc": 4, "dsc": "()Z"}, {"nme": "isSingleQuotedExecutableEscaped", "acc": 4, "dsc": "()Z"}, {"nme": "setArgumentQuoteDelimiter", "acc": 4, "dsc": "(C)V"}, {"nme": "getArgumentQuoteDelimiter", "acc": 4, "dsc": "()C"}, {"nme": "setExecutableQuoteDelimiter", "acc": 4, "dsc": "(C)V"}, {"nme": "getExecutableQuoteDelimiter", "acc": 4, "dsc": "()C"}, {"nme": "setArgumentEscapePattern", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getArgumentEscapePattern", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getShellCommandLine", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;", "sig": "([<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "getShellArgsList", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "addShellArg", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setQuotedArgumentsEnabled", "acc": 1, "dsc": "(Z)V"}, {"nme": "isQuotedArgumentsEnabled", "acc": 1, "dsc": "()Z"}, {"nme": "setQuotedExecutableEnabled", "acc": 1, "dsc": "(Z)V"}, {"nme": "isQuotedExecutableEnabled", "acc": 1, "dsc": "()Z"}, {"nme": "setExecutable", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getExecutable", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setWorkingDirectory", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setWorkingDirectory", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V"}, {"nme": "getWorkingDirectory", "acc": 1, "dsc": "()Ljava/io/File;"}, {"nme": "getWorkingDirectoryAsString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "clearArguments", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getOriginalExecutable", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getOriginalCommandLine", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;)Ljava/util/List;", "sig": "(Lja<PERSON>/lang/String;[Ljava/lang/String;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "setDoubleQuotedArgumentEscaped", "acc": 4, "dsc": "(Z)V"}, {"nme": "setDoubleQuotedExecutableEscaped", "acc": 4, "dsc": "(Z)V"}, {"nme": "setSingleQuotedArgumentEscaped", "acc": 4, "dsc": "(Z)V"}, {"nme": "setSingleQuotedExecutableEscaped", "acc": 4, "dsc": "(Z)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "DEFAULT_QUOTING_TRIGGER_CHARS", "dsc": "[C"}, {"acc": 2, "nme": "shellCommand", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "shellArgs", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 2, "nme": "quotedArgumentsEnabled", "dsc": "Z"}, {"acc": 2, "nme": "unconditionallyQuote", "dsc": "Z"}, {"acc": 2, "nme": "executable", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "workingDir", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "quotedExecutableEnabled", "dsc": "Z"}, {"acc": 2, "nme": "doubleQuotedArgumentEscaped", "dsc": "Z"}, {"acc": 2, "nme": "singleQuotedArgumentEscaped", "dsc": "Z"}, {"acc": 2, "nme": "doubleQuotedExecutableEscaped", "dsc": "Z"}, {"acc": 2, "nme": "singleQuotedExecutableEscaped", "dsc": "Z"}, {"acc": 2, "nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dsc": "C"}, {"acc": 2, "nme": "exeQuoteDelimiter", "dsc": "C"}, {"acc": 2, "nme": "argumentEscapePattern", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/codehaus/plexus/util/Os.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/Os", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setValidFamilies", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "setFamily", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setName", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setArch", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setVersion", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "eval", "acc": 1, "dsc": "()Z", "exs": ["java/lang/Exception"]}, {"nme": "isFamily", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isName", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isArch", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isVersion", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isOs", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "getOsFamily", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isValidFamily", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "getValidFamilies", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "FAMILY_DOS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "dos"}, {"acc": 25, "nme": "FAMILY_MAC", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "mac"}, {"acc": 25, "nme": "FAMILY_NETWARE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "netware"}, {"acc": 25, "nme": "FAMILY_OS2", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "os/2"}, {"acc": 25, "nme": "FAMILY_TANDEM", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "tandem"}, {"acc": 25, "nme": "FAMILY_UNIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "unix"}, {"acc": 25, "nme": "FAMILY_WINDOWS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "windows"}, {"acc": 25, "nme": "FAMILY_WIN9X", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "win9x"}, {"acc": 25, "nme": "FAMILY_ZOS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "z/os"}, {"acc": 25, "nme": "FAMILY_OS400", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "os/400"}, {"acc": 25, "nme": "FAMILY_OPENVMS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "openvms"}, {"acc": 26, "nme": "validFamilies", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 26, "nme": "PATH_SEP", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 25, "nme": "OS_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 25, "nme": "OS_ARCH", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 25, "nme": "OS_VERSION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 25, "nme": "OS_FAMILY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "family", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "arch", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/codehaus/plexus/util/xml/pull/XmlSerializer.class": {"ver": 52, "acc": 1537, "nme": "org/codehaus/plexus/util/xml/pull/XmlSerializer", "super": "java/lang/Object", "mthds": [{"nme": "setFeature", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)V", "exs": ["java/lang/IllegalArgumentException", "java/lang/IllegalStateException"]}, {"nme": "getFeature", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "setProperty", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V", "exs": ["java/lang/IllegalArgumentException", "java/lang/IllegalStateException"]}, {"nme": "getProperty", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "setOutput", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/io/OutputStream;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException", "java/lang/IllegalArgumentException", "java/lang/IllegalStateException"]}, {"nme": "setOutput", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/io/Writer;)V", "exs": ["java/io/IOException", "java/lang/IllegalArgumentException", "java/lang/IllegalStateException"]}, {"nme": "startDocument", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Boolean;)V", "exs": ["java/io/IOException", "java/lang/IllegalArgumentException", "java/lang/IllegalStateException"]}, {"nme": "endDocument", "acc": 1025, "dsc": "()V", "exs": ["java/io/IOException", "java/lang/IllegalArgumentException", "java/lang/IllegalStateException"]}, {"nme": "setPrefix", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException", "java/lang/IllegalArgumentException", "java/lang/IllegalStateException"]}, {"nme": "getPrefix", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)Ljava/lang/String;", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "<PERSON><PERSON><PERSON>h", "acc": 1025, "dsc": "()I"}, {"nme": "getNamespace", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "startTag", "acc": 1025, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;)Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;", "exs": ["java/io/IOException", "java/lang/IllegalArgumentException", "java/lang/IllegalStateException"]}, {"nme": "attribute", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>java/lang/String;Ljava/lang/String;)Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;", "exs": ["java/io/IOException", "java/lang/IllegalArgumentException", "java/lang/IllegalStateException"]}, {"nme": "endTag", "acc": 1025, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;)Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;", "exs": ["java/io/IOException", "java/lang/IllegalArgumentException", "java/lang/IllegalStateException"]}, {"nme": "text", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;", "exs": ["java/io/IOException", "java/lang/IllegalArgumentException", "java/lang/IllegalStateException"]}, {"nme": "text", "acc": 1025, "dsc": "([CII)Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;", "exs": ["java/io/IOException", "java/lang/IllegalArgumentException", "java/lang/IllegalStateException"]}, {"nme": "cdsect", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException", "java/lang/IllegalArgumentException", "java/lang/IllegalStateException"]}, {"nme": "entityRef", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException", "java/lang/IllegalArgumentException", "java/lang/IllegalStateException"]}, {"nme": "processingInstruction", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException", "java/lang/IllegalArgumentException", "java/lang/IllegalStateException"]}, {"nme": "comment", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException", "java/lang/IllegalArgumentException", "java/lang/IllegalStateException"]}, {"nme": "docdecl", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException", "java/lang/IllegalArgumentException", "java/lang/IllegalStateException"]}, {"nme": "ignorableWhitespace", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException", "java/lang/IllegalArgumentException", "java/lang/IllegalStateException"]}, {"nme": "flush", "acc": 1025, "dsc": "()V", "exs": ["java/io/IOException"]}], "flds": []}, "META-INF/versions/9/org/codehaus/plexus/util/BaseIOUtil.class": {"ver": 53, "acc": 1056, "nme": "org/codehaus/plexus/util/BaseIOUtil", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "copy", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;Ljava/io/OutputStream;)V", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;<PERSON><PERSON><PERSON>/io/Writer;)V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 26, "nme": "DEFAULT_BUFFER_SIZE", "dsc": "I", "val": 16384}]}, "org/codehaus/plexus/util/cli/CommandLineUtils$2.class": {"ver": 52, "acc": 32, "nme": "org/codehaus/plexus/util/cli/CommandLineUtils$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/io/InputStream;Ljava/lang/Process;Lorg/codehaus/plexus/util/cli/StreamConsumer;Lorg/codehaus/plexus/util/cli/StreamConsumer;ILjava/lang/Thread;)V"}, {"nme": "call", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Integer;", "exs": ["org/codehaus/plexus/util/cli/CommandLineException"]}, {"nme": "call", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 4112, "nme": "val$systemIn", "dsc": "Ljava/io/InputStream;"}, {"acc": 4112, "nme": "val$p", "dsc": "<PERSON><PERSON><PERSON>/lang/Process;"}, {"acc": 4112, "nme": "val$systemOut", "dsc": "Lorg/codehaus/plexus/util/cli/StreamConsumer;"}, {"acc": 4112, "nme": "val$systemErr", "dsc": "Lorg/codehaus/plexus/util/cli/StreamConsumer;"}, {"acc": 4112, "nme": "val$timeoutInSeconds", "dsc": "I"}, {"acc": 4112, "nme": "val$processHook", "dsc": "<PERSON><PERSON><PERSON>/lang/Thread;"}]}, "org/codehaus/plexus/util/FastMap.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/FastMap", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/util/Map;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(I)V"}, {"nme": "size", "acc": 1, "dsc": "()I"}, {"nme": "capacity", "acc": 1, "dsc": "()I"}, {"nme": "isEmpty", "acc": 1, "dsc": "()Z"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "containsValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "get", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/Object;)TV;"}, {"nme": "getEntry", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Ljava/util/Map$Entry;"}, {"nme": "put", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "putAll", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Ljava/util/Map<+TK;+TV;>;)V"}, {"nme": "remove", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/Object;)TV;"}, {"nme": "clear", "acc": 1, "dsc": "()V"}, {"nme": "setCapacity", "acc": 1, "dsc": "(I)V"}, {"nme": "clone", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "values", "acc": 1, "dsc": "()Ljava/util/Collection;"}, {"nme": "entrySet", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;"}, {"nme": "keySet", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;"}, {"nme": "sizeChanged", "acc": 4, "dsc": "()V"}, {"nme": "keyHash", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}, {"nme": "addEntry", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "removeEntry", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/FastMap$EntryImpl;)V"}, {"nme": "initialize", "acc": 2, "dsc": "(I)V"}, {"nme": "readObject", "acc": 2, "dsc": "(Ljava/io/ObjectInputStream;)V", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}, {"nme": "writeObject", "acc": 2, "dsc": "(Ljava/io/ObjectOutputStream;)V", "exs": ["java/io/IOException"]}, {"nme": "access$800", "acc": 4104, "dsc": "(Lorg/codehaus/plexus/util/FastMap;)Lorg/codehaus/plexus/util/FastMap$EntryImpl;"}, {"nme": "access$900", "acc": 4104, "dsc": "(Lorg/codehaus/plexus/util/FastMap;Lorg/codehaus/plexus/util/FastMap$EntryImpl;)V"}, {"nme": "access$1000", "acc": 4104, "dsc": "(Lorg/codehaus/plexus/util/FastMap;)I"}], "flds": [{"acc": 130, "nme": "_entries", "dsc": "[Lorg/codehaus/plexus/util/FastMap$EntryImpl;"}, {"acc": 130, "nme": "_capacity", "dsc": "I"}, {"acc": 130, "nme": "_mask", "dsc": "I"}, {"acc": 130, "nme": "_pool<PERSON>irst", "dsc": "Lorg/codehaus/plexus/util/FastMap$EntryImpl;"}, {"acc": 130, "nme": "_mapFirst", "dsc": "Lorg/codehaus/plexus/util/FastMap$EntryImpl;"}, {"acc": 130, "nme": "_mapLast", "dsc": "Lorg/codehaus/plexus/util/FastMap$EntryImpl;"}, {"acc": 130, "nme": "_size", "dsc": "I"}, {"acc": 130, "nme": "_values", "dsc": "Lorg/codehaus/plexus/util/FastMap$Values;", "sig": "Lorg/codehaus/plexus/util/FastMap<TK;TV;>.Values;"}, {"acc": 130, "nme": "_entrySet", "dsc": "Lorg/codehaus/plexus/util/FastMap$EntrySet;", "sig": "Lorg/codehaus/plexus/util/FastMap<TK;TV;>.EntrySet;"}, {"acc": 130, "nme": "_keySet", "dsc": "Lorg/codehaus/plexus/util/FastMap$KeySet;", "sig": "Lorg/codehaus/plexus/util/FastMap<TK;TV;>.KeySet;"}]}, "org/codehaus/plexus/util/xml/SerializerXMLWriter.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/xml/SerializerXMLWriter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V"}, {"nme": "startElement", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "addAttribute", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "writeText", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "writeMarkup", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "endElement", "acc": 1, "dsc": "()V"}, {"nme": "storeException", "acc": 2, "dsc": "(<PERSON>java/io/IOException;)V"}, {"nme": "getExceptions", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/Exception;>;"}], "flds": [{"acc": 18, "nme": "serializer", "dsc": "Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;"}, {"acc": 18, "nme": "namespace", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "elements", "dsc": "<PERSON><PERSON><PERSON>/util/<PERSON>ack;", "sig": "<PERSON>ja<PERSON>/util/Stack<Ljava/lang/String;>;"}, {"acc": 2, "nme": "exceptions", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/Exception;>;"}]}, "org/codehaus/plexus/util/ReflectionUtils.class": {"ver": 52, "acc": 49, "nme": "org/codehaus/plexus/util/ReflectionUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getFieldByNameIncludingSuperclasses", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/reflect/Field;", "sig": "(L<PERSON><PERSON>/lang/String;Ljava/lang/Class<*>;)Ljava/lang/reflect/Field;"}, {"nme": "getFieldsIncludingSuperclasses", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;)Ljava/util/List<Ljava/lang/reflect/Field;>;"}, {"nme": "getSetter", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/reflect/Method;", "sig": "(L<PERSON><PERSON>/lang/String;Ljava/lang/Class<*>;)Ljava/lang/reflect/Method;"}, {"nme": "getSetters", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;)Ljava/util/List<Ljava/lang/reflect/Method;>;"}, {"nme": "getSetterType", "acc": 9, "dsc": "(L<PERSON><PERSON>/lang/reflect/Method;)Ljava/lang/Class;", "sig": "(Lja<PERSON>/lang/reflect/Method;)Ljava/lang/Class<*>;"}, {"nme": "setVariableValueInObject", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V", "exs": ["java/lang/IllegalAccessException"]}, {"nme": "getValueIncludingSuperclasses", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/IllegalAccessException"]}, {"nme": "getVariablesAndValuesIncludingSuperclasses", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Ljava/util/Map;", "sig": "(L<PERSON><PERSON>/lang/Object;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;", "exs": ["java/lang/IllegalAccessException"]}, {"nme": "isSetter", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)Z"}, {"nme": "gatherVariablesAndValuesIncludingSuperclasses", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Ljava/util/Map;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Object;Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;)V", "exs": ["java/lang/IllegalAccessException"]}], "flds": []}, "org/codehaus/plexus/util/cli/Arg.class": {"ver": 52, "acc": 1537, "nme": "org/codehaus/plexus/util/cli/Arg", "super": "java/lang/Object", "mthds": [{"nme": "setValue", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setLine", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setFile", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V"}, {"nme": "getParts", "acc": 1025, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "org/codehaus/plexus/util/cli/StreamPumper.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/cli/StreamPumper", "super": "org/codehaus/plexus/util/cli/AbstractStreamHandler", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON>va/io/InputStream;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/io/InputStream;Lorg/codehaus/plexus/util/cli/StreamConsumer;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/io/InputStream;Ljava/io/PrintWriter;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/io/InputStream;Ljava/io/PrintWriter;Lorg/codehaus/plexus/util/cli/StreamConsumer;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}, {"nme": "flush", "acc": 1, "dsc": "()V"}, {"nme": "close", "acc": 1, "dsc": "()V"}, {"nme": "getException", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Exception;"}], "flds": [{"acc": 18, "nme": "in", "dsc": "<PERSON><PERSON><PERSON>/io/BufferedReader;"}, {"acc": 18, "nme": "consumer", "dsc": "Lorg/codehaus/plexus/util/cli/StreamConsumer;"}, {"acc": 18, "nme": "out", "dsc": "Ljava/io/PrintWriter;"}, {"acc": 66, "nme": "exception", "dsc": "<PERSON><PERSON><PERSON>/lang/Exception;"}, {"acc": 26, "nme": "SIZE", "dsc": "I", "val": 1024}]}, "org/codehaus/plexus/util/cli/StreamFeeder.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/cli/StreamFeeder", "super": "org/codehaus/plexus/util/cli/AbstractStreamHandler", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;Ljava/io/OutputStream;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}, {"nme": "close", "acc": 1, "dsc": "()V"}, {"nme": "getException", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Throwable;"}, {"nme": "feed", "acc": 2, "dsc": "()V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 2, "nme": "input", "dsc": "Ljava/io/InputStream;"}, {"acc": 2, "nme": "output", "dsc": "Ljava/io/OutputStream;"}, {"acc": 66, "nme": "exception", "dsc": "<PERSON><PERSON><PERSON>/lang/Throwable;"}]}, "org/codehaus/plexus/util/introspection/ClassMap$CacheMiss.class": {"ver": 52, "acc": 48, "nme": "org/codehaus/plexus/util/introspection/ClassMap$CacheMiss", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/codehaus/plexus/util/introspection/ClassMap$1;)V"}], "flds": []}, "org/codehaus/plexus/util/xml/Xpp3DomBuilder$InputLocationBuilder.class": {"ver": 52, "acc": 1537, "nme": "org/codehaus/plexus/util/xml/Xpp3DomBuilder$InputLocationBuilder", "super": "java/lang/Object", "mthds": [{"nme": "toInputLocation", "acc": 1025, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;)Ljava/lang/Object;"}], "flds": []}, "org/codehaus/plexus/util/DirectoryScanner.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/DirectoryScanner", "super": "org/codehaus/plexus/util/AbstractScanner", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "setBasedir", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setBasedir", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V"}, {"nme": "getBasedir", "acc": 1, "dsc": "()Ljava/io/File;"}, {"nme": "setFollowSymlinks", "acc": 1, "dsc": "(Z)V"}, {"nme": "isEverythingIncluded", "acc": 1, "dsc": "()Z"}, {"nme": "scan", "acc": 1, "dsc": "()V", "exs": ["java/lang/IllegalStateException"]}, {"nme": "slowScan", "acc": 4, "dsc": "()V"}, {"nme": "scandir", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/io/File;<PERSON>ja<PERSON>/lang/String;Z)V"}, {"nme": "isSelected", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/io/File;)Z"}, {"nme": "getIncludedFiles", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getNotIncludedFiles", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getExcludedFiles", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDeselectedFiles", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getIncludedDirectories", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getNotIncludedDirectories", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getExcludedDirectories", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDeselectedDirectories", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isSymbolicLink", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;Lja<PERSON>/lang/String;)Z", "exs": ["java/io/IOException"]}, {"nme": "isParentSymbolicLink", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;Lja<PERSON>/lang/String;)Z", "exs": ["java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "EMPTY_STRING_ARRAY", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4, "nme": "basedir", "dsc": "Ljava/io/File;"}, {"acc": 4, "nme": "filesIncluded", "dsc": "<PERSON><PERSON><PERSON>/util/ArrayList;", "sig": "Ljava/util/ArrayList<Ljava/lang/String;>;"}, {"acc": 4, "nme": "filesNotIncluded", "dsc": "<PERSON><PERSON><PERSON>/util/ArrayList;", "sig": "Ljava/util/ArrayList<Ljava/lang/String;>;"}, {"acc": 4, "nme": "filesExcluded", "dsc": "<PERSON><PERSON><PERSON>/util/ArrayList;", "sig": "Ljava/util/ArrayList<Ljava/lang/String;>;"}, {"acc": 4, "nme": "dirsIncluded", "dsc": "<PERSON><PERSON><PERSON>/util/ArrayList;", "sig": "Ljava/util/ArrayList<Ljava/lang/String;>;"}, {"acc": 4, "nme": "dirsNotIncluded", "dsc": "<PERSON><PERSON><PERSON>/util/ArrayList;", "sig": "Ljava/util/ArrayList<Ljava/lang/String;>;"}, {"acc": 4, "nme": "dirsExcluded", "dsc": "<PERSON><PERSON><PERSON>/util/ArrayList;", "sig": "Ljava/util/ArrayList<Ljava/lang/String;>;"}, {"acc": 4, "nme": "filesDeselected", "dsc": "<PERSON><PERSON><PERSON>/util/ArrayList;", "sig": "Ljava/util/ArrayList<Ljava/lang/String;>;"}, {"acc": 4, "nme": "dir<PERSON><PERSON><PERSON><PERSON>", "dsc": "<PERSON><PERSON><PERSON>/util/ArrayList;", "sig": "Ljava/util/ArrayList<Ljava/lang/String;>;"}, {"acc": 4, "nme": "haveSlowResults", "dsc": "Z"}, {"acc": 2, "nme": "followSymlinks", "dsc": "Z"}, {"acc": 4, "nme": "everythingIncluded", "dsc": "Z"}, {"acc": 18, "nme": "tokenizedEmpty", "dsc": "[[C"}]}, "org/codehaus/plexus/util/cli/ShutdownHookUtils.class": {"ver": 52, "acc": 32, "nme": "org/codehaus/plexus/util/cli/ShutdownHookUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "addShutDownHook", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Thread;)V"}, {"nme": "removeShutdownHook", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Thread;)V"}], "flds": []}, "org/codehaus/plexus/util/dag/DAG.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/dag/DAG", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getVertices", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/codehaus/plexus/util/dag/Vertex;>;"}, {"nme": "getVerticies", "acc": 131073, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/codehaus/plexus/util/dag/Vertex;>;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "addVertex", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/codehaus/plexus/util/dag/Vertex;"}, {"nme": "addEdge", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/codehaus/plexus/util/dag/CycleDetectedException"]}, {"nme": "addEdge", "acc": 1, "dsc": "(Lorg/codehaus/plexus/util/dag/Vertex;Lorg/codehaus/plexus/util/dag/Vertex;)V", "exs": ["org/codehaus/plexus/util/dag/CycleDetectedException"]}, {"nme": "removeEdge", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "removeEdge", "acc": 1, "dsc": "(Lorg/codehaus/plexus/util/dag/Vertex;Lorg/codehaus/plexus/util/dag/Vertex;)V"}, {"nme": "getVertex", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/codehaus/plexus/util/dag/Vertex;"}, {"nme": "hasEdge", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "getParentLabels", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "clone", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}, {"nme": "isConnected", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "getSuccessorLabels", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/List<Ljava/lang/String;>;"}], "flds": [{"acc": 2, "nme": "vertexMap", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lorg/codehaus/plexus/util/dag/Vertex;>;"}, {"acc": 2, "nme": "vertexList", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/codehaus/plexus/util/dag/Vertex;>;"}]}, "org/codehaus/plexus/util/IOUtil.class": {"ver": 52, "acc": 49, "nme": "org/codehaus/plexus/util/IOUtil", "super": "org/codehaus/plexus/util/BaseIOUtil", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "copy", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;Ljava/io/OutputStream;)V", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;Ljava/io/OutputStream;I)V", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;<PERSON><PERSON><PERSON>/io/Writer;)V", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;<PERSON><PERSON><PERSON>/io/Writer;I)V", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;<PERSON><PERSON><PERSON>/io/Writer;)V", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;<PERSON><PERSON><PERSON>/io/Writer;I)V", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;<PERSON><PERSON><PERSON>/io/Writer;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;<PERSON><PERSON><PERSON>/io/Writer;<PERSON><PERSON><PERSON>/lang/String;I)V", "exs": ["java/io/IOException"]}, {"nme": "toString", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;)<PERSON>ja<PERSON>/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "toString", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;I)Ljava/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "toString", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;<PERSON><PERSON><PERSON>/lang/String;)<PERSON>java/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "toString", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;<PERSON><PERSON><PERSON>/lang/String;I)Ljava/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "toByteArray", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;)[B", "exs": ["java/io/IOException"]}, {"nme": "toByteArray", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;I)[B", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;<PERSON><PERSON><PERSON>/io/OutputStream;)V", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;<PERSON><PERSON><PERSON>/io/OutputStream;I)V", "exs": ["java/io/IOException"]}, {"nme": "toString", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;)<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "toString", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;I)<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "toByteArray", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;)[B", "exs": ["java/io/IOException"]}, {"nme": "toByteArray", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;I)[B", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/io/OutputStream;)V", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/io/OutputStream;I)V", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/io/Writer;)V", "exs": ["java/io/IOException"]}, {"nme": "bufferedCopy", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;Ljava/io/OutputStream;)V", "exs": ["java/io/IOException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "toByteArray", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[B", "exs": ["java/io/IOException"]}, {"nme": "toByteArray", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)[B", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 9, "dsc": "([<PERSON><PERSON><PERSON><PERSON>/io/Writer;)V", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 9, "dsc": "([<PERSON><PERSON><PERSON><PERSON>/io/Writer;I)V", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 9, "dsc": "([<PERSON><PERSON><PERSON><PERSON>/io/Writer;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 9, "dsc": "([<PERSON><PERSON><PERSON><PERSON>/io/Writer;<PERSON><PERSON><PERSON>/lang/String;I)V", "exs": ["java/io/IOException"]}, {"nme": "toString", "acc": 9, "dsc": "([B)<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "toString", "acc": 9, "dsc": "([BI)Ljava/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "toString", "acc": 9, "dsc": "([<PERSON><PERSON><PERSON><PERSON>/lang/String;)<PERSON>ja<PERSON>/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "toString", "acc": 9, "dsc": "([<PERSON><PERSON><PERSON><PERSON>/lang/String;I)<PERSON>ja<PERSON>/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 9, "dsc": "([BLjava/io/OutputStream;)V", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 9, "dsc": "([BLjava/io/OutputStream;I)V", "exs": ["java/io/IOException"]}, {"nme": "contentEquals", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;L<PERSON><PERSON>/io/InputStream;)Z", "exs": ["java/io/IOException"]}, {"nme": "close", "acc": 131081, "dsc": "(<PERSON><PERSON>va/io/InputStream;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "close", "acc": 131081, "dsc": "(Ljava/nio/channels/Channel;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "close", "acc": 131081, "dsc": "(Ljava/io/OutputStream;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "close", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "close", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/io/Writer;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}], "flds": [{"acc": 26, "nme": "DEFAULT_BUFFER_SIZE", "dsc": "I", "val": 16384}]}, "org/codehaus/plexus/util/DirectoryWalkListener.class": {"ver": 52, "acc": 1537, "nme": "org/codehaus/plexus/util/DirectoryWalkListener", "super": "java/lang/Object", "mthds": [{"nme": "directoryWalkStarting", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V"}, {"nme": "directoryWalkStep", "acc": 1025, "dsc": "(ILjava/io/File;)V"}, {"nme": "directoryWalkFinished", "acc": 1025, "dsc": "()V"}, {"nme": "debug", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "org/codehaus/plexus/util/dag/CycleDetectedException.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/dag/CycleDetectedException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Lja<PERSON>/lang/String;Ljava/util/List<Ljava/lang/String;>;)V"}, {"nme": "getCycle", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "cycleToString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getMessage", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "cycle", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}]}, "org/codehaus/plexus/util/cli/shell/BourneShell.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/cli/shell/BourneShell", "super": "org/codehaus/plexus/util/cli/shell/Shell", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Z)V"}, {"nme": "getExecutable", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getShellArgsList", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "getShellArgs", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getExecutionPreamble", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "quoteOneItem", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)Ljava/lang/String;"}], "flds": []}, "META-INF/versions/10/org/codehaus/plexus/util/BaseIOUtil.class": {"ver": 54, "acc": 1056, "nme": "org/codehaus/plexus/util/BaseIOUtil", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "copy", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;Ljava/io/OutputStream;)V", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;<PERSON><PERSON><PERSON>/io/Writer;)V", "exs": ["java/io/IOException"]}], "flds": []}, "org/codehaus/plexus/util/cli/StreamConsumer.class": {"ver": 52, "acc": 1537, "nme": "org/codehaus/plexus/util/cli/StreamConsumer", "super": "java/lang/Object", "mthds": [{"nme": "consumeLine", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}], "flds": []}, "org/codehaus/plexus/util/ReaderFactory.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/ReaderFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "newXmlReader", "acc": 9, "dsc": "(L<PERSON>va/io/InputStream;)Lorg/codehaus/plexus/util/xml/XmlStreamReader;", "exs": ["java/io/IOException"]}, {"nme": "newXmlReader", "acc": 9, "dsc": "(Ljava/io/File;)Lorg/codehaus/plexus/util/xml/XmlStreamReader;", "exs": ["java/io/IOException"]}, {"nme": "newXmlReader", "acc": 9, "dsc": "(Ljava/net/URL;)Lorg/codehaus/plexus/util/xml/XmlStreamReader;", "exs": ["java/io/IOException"]}, {"nme": "newPlatformReader", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;)<PERSON><PERSON><PERSON>/io/Reader;"}, {"nme": "newPlatformReader", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)<PERSON><PERSON><PERSON>/io/Reader;", "exs": ["java/io/IOException"]}, {"nme": "newPlatformReader", "acc": 9, "dsc": "(Ljava/net/URL;)Ljava/io/Reader;", "exs": ["java/io/IOException"]}, {"nme": "newReader", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/io/Reader;", "exs": ["java/io/UnsupportedEncodingException"]}, {"nme": "newReader", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/File;L<PERSON><PERSON>/lang/String;)Lja<PERSON>/io/Reader;", "exs": ["java/io/IOException"]}, {"nme": "newReader", "acc": 9, "dsc": "(Ljava/net/URL;Ljava/lang/String;)Ljava/io/Reader;", "exs": ["java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "ISO_8859_1", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "ISO-8859-1"}, {"acc": 25, "nme": "US_ASCII", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "US-ASCII"}, {"acc": 25, "nme": "UTF_16", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "UTF-16"}, {"acc": 25, "nme": "UTF_16BE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "UTF-16BE"}, {"acc": 25, "nme": "UTF_16LE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "UTF-16LE"}, {"acc": 25, "nme": "UTF_8", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "UTF-8"}, {"acc": 25, "nme": "FILE_ENCODING", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/codehaus/plexus/util/xml/pull/XmlPullParser.class": {"ver": 52, "acc": 1537, "nme": "org/codehaus/plexus/util/xml/pull/XmlPullParser", "super": "java/lang/Object", "mthds": [{"nme": "setFeature", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)V", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getFeature", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "setProperty", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getProperty", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "setInput", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;)V", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "setInput", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getInputEncoding", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "defineEntityReplacementText", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getNamespaceCount", "acc": 1025, "dsc": "(I)I", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getNamespacePrefix", "acc": 1025, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getNamespaceUri", "acc": 1025, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getNamespace", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON>h", "acc": 1025, "dsc": "()I"}, {"nme": "getPositionDescription", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLineNumber", "acc": 1025, "dsc": "()I"}, {"nme": "getColumnNumber", "acc": 1025, "dsc": "()I"}, {"nme": "isWhitespace", "acc": 1025, "dsc": "()Z", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getText", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getTextCharacters", "acc": 1025, "dsc": "([I)[C"}, {"nme": "getNamespace", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getPrefix", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isEmptyElementTag", "acc": 1025, "dsc": "()Z", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getAttributeCount", "acc": 1025, "dsc": "()I"}, {"nme": "getAttributeNamespace", "acc": 1025, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getAttributeName", "acc": 1025, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getAttributePrefix", "acc": 1025, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getAttributeType", "acc": 1025, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isAttributeDefault", "acc": 1025, "dsc": "(I)Z"}, {"nme": "getAttributeValue", "acc": 1025, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getAttributeValue", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getEventType", "acc": 1025, "dsc": "()I", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "next", "acc": 1025, "dsc": "()I", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "nextToken", "acc": 1025, "dsc": "()I", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "require", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "nextText", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "nextTag", "acc": 1025, "dsc": "()I", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "NO_NAMESPACE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": ""}, {"acc": 25, "nme": "START_DOCUMENT", "dsc": "I", "val": 0}, {"acc": 25, "nme": "END_DOCUMENT", "dsc": "I", "val": 1}, {"acc": 25, "nme": "START_TAG", "dsc": "I", "val": 2}, {"acc": 25, "nme": "END_TAG", "dsc": "I", "val": 3}, {"acc": 25, "nme": "TEXT", "dsc": "I", "val": 4}, {"acc": 25, "nme": "CDSECT", "dsc": "I", "val": 5}, {"acc": 25, "nme": "ENTITY_REF", "dsc": "I", "val": 6}, {"acc": 25, "nme": "IGNORABLE_WHITESPACE", "dsc": "I", "val": 7}, {"acc": 25, "nme": "PROCESSING_INSTRUCTION", "dsc": "I", "val": 8}, {"acc": 25, "nme": "COMMENT", "dsc": "I", "val": 9}, {"acc": 25, "nme": "DOCDECL", "dsc": "I", "val": 10}, {"acc": 25, "nme": "TYPES", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 25, "nme": "FEATURE_PROCESS_NAMESPACES", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "http://xmlpull.org/v1/doc/features.html#process-namespaces"}, {"acc": 25, "nme": "FEATURE_REPORT_NAMESPACE_ATTRIBUTES", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "http://xmlpull.org/v1/doc/features.html#report-namespace-prefixes"}, {"acc": 25, "nme": "FEATURE_PROCESS_DOCDECL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "http://xmlpull.org/v1/doc/features.html#process-docdecl"}, {"acc": 25, "nme": "FEATURE_VALIDATION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "http://xmlpull.org/v1/doc/features.html#validation"}]}, "org/codehaus/plexus/util/StringInputStream.class": {"ver": 52, "acc": 131105, "nme": "org/codehaus/plexus/util/StringInputStream", "super": "java/io/InputStream", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "read", "acc": 1, "dsc": "()I", "exs": ["java/io/IOException"]}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "mark", "acc": 33, "dsc": "(I)V"}, {"nme": "reset", "acc": 33, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "markSupported", "acc": 1, "dsc": "()Z"}], "flds": [{"acc": 2, "nme": "in", "dsc": "<PERSON><PERSON><PERSON>/io/StringReader;"}], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "org/codehaus/plexus/util/FastMap$EntrySet$1.class": {"ver": 52, "acc": 32, "nme": "org/codehaus/plexus/util/FastMap$EntrySet$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/codehaus/plexus/util/FastMap$EntrySet;)V"}, {"nme": "remove", "acc": 1, "dsc": "()V"}, {"nme": "hasNext", "acc": 1, "dsc": "()Z"}, {"nme": "next", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 0, "nme": "after", "dsc": "Lorg/codehaus/plexus/util/FastMap$EntryImpl;"}, {"acc": 0, "nme": "before", "dsc": "Lorg/codehaus/plexus/util/FastMap$EntryImpl;"}, {"acc": 4112, "nme": "this$1", "dsc": "Lorg/codehaus/plexus/util/FastMap$EntrySet;"}]}, "org/codehaus/plexus/util/cli/Commandline$Marker.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/cli/Commandline$Marker", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/codehaus/plexus/util/cli/Commandline;I)V"}, {"nme": "getPosition", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 2, "nme": "position", "dsc": "I"}, {"acc": 2, "nme": "realPos", "dsc": "I"}, {"acc": 4112, "nme": "this$0", "dsc": "Lorg/codehaus/plexus/util/cli/Commandline;"}]}, "org/codehaus/plexus/util/CollectionUtils.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/CollectionUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "mergeMaps", "acc": 9, "dsc": "(Ljava/util/Map;Ljava/util/Map;)Ljava/util/Map;", "sig": "<K:Ljava/lang/Object;V:Ljava/lang/Object;>(Ljava/util/Map<TK;TV;>;Ljava/util/Map<TK;TV;>;)Ljava/util/Map<TK;TV;>;"}, {"nme": "mergeMaps", "acc": 9, "dsc": "([Ljava/util/Map;)Ljava/util/Map;", "sig": "<K:Ljava/lang/Object;V:Ljava/lang/Object;>([Ljava/util/Map<TK;TV;>;)Ljava/util/Map<TK;TV;>;"}, {"nme": "intersection", "acc": 9, "dsc": "(Lja<PERSON>/util/Collection;Ljava/util/Collection;)Ljava/util/Collection;", "sig": "<E:Ljava/lang/Object;>(Ljava/util/Collection<TE;>;Ljava/util/Collection<TE;>;)Ljava/util/Collection<TE;>;"}, {"nme": "subtract", "acc": 9, "dsc": "(Lja<PERSON>/util/Collection;Ljava/util/Collection;)Ljava/util/Collection;", "sig": "<T:Ljava/lang/Object;>(Ljava/util/Collection<TT;>;Ljava/util/Collection<TT;>;)Ljava/util/Collection<TT;>;"}, {"nme": "getCardinalityMap", "acc": 9, "dsc": "(Lja<PERSON>/util/Collection;)Ljava/util/Map;", "sig": "<E:Ljava/lang/Object;>(Ljava/util/Collection<TE;>;)Ljava/util/Map<TE;Ljava/lang/Integer;>;"}, {"nme": "iteratorToList", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Iterator;)<PERSON>java/util/List;", "sig": "<E:Lja<PERSON>/lang/Object;>(Ljava/util/Iterator<TE;>;)Ljava/util/List<TE;>;"}, {"nme": "getFreq", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Ljava/util/Map;)I", "sig": "<E:Lja<PERSON>/lang/Object;>(TE;Ljava/util/Map<TE;Ljava/lang/Integer;>;)I"}], "flds": []}, "org/codehaus/plexus/util/TypeFormat.class": {"ver": 52, "acc": 49, "nme": "org/codehaus/plexus/util/TypeFormat", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "indexOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;<PERSON><PERSON><PERSON>/lang/CharSequence;I)I"}, {"nme": "parseBoolean", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;)Z"}, {"nme": "parseShort", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;)S"}, {"nme": "parseShort", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;I)S"}, {"nme": "parseInt", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;)I"}, {"nme": "parseInt", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;I)I"}, {"nme": "parseLong", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;)J"}, {"nme": "parseLong", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;I)J"}, {"nme": "parseFloat", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;)F"}, {"nme": "parseDouble", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;)D", "exs": ["java/lang/NumberFormatException"]}, {"nme": "format", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/StringBuffer;)<PERSON><PERSON><PERSON>/lang/StringBuffer;"}, {"nme": "format", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;)<PERSON>java/lang/StringBuffer;"}, {"nme": "format", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/StringBuffer;)<PERSON><PERSON><PERSON>/lang/StringBuffer;"}, {"nme": "format", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;)<PERSON>java/lang/StringBuffer;"}, {"nme": "format", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/StringBuffer;)<PERSON><PERSON><PERSON>/lang/StringBuffer;"}, {"nme": "format2", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/StringBuffer;)V"}, {"nme": "format", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/StringBuffer;)<PERSON><PERSON><PERSON>/lang/StringBuffer;"}, {"nme": "format", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/StringBuffer;)<PERSON><PERSON><PERSON>/lang/StringBuffer;"}, {"nme": "format2", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/StringBuffer;)V"}, {"nme": "format", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;)<PERSON>java/lang/StringBuffer;"}, {"nme": "format", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/StringBuffer;)<PERSON>java/lang/StringBuffer;"}, {"nme": "format", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;)<PERSON><PERSON><PERSON>/lang/StringBuffer;"}, {"nme": "format", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/StringBuffer;)<PERSON><PERSON><PERSON>/lang/StringBuffer;"}, {"nme": "format", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/StringBuffer;)<PERSON>java/lang/StringBuffer;"}, {"nme": "format", "acc": 10, "dsc": "(DDZ<PERSON>java/lang/StringBuffer;)<PERSON>java/lang/StringBuffer;"}, {"nme": "multE", "acc": 26, "dsc": "(DI)D"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "DIGITS", "dsc": "[C"}, {"acc": 26, "nme": "INT_POW_10", "dsc": "[I"}, {"acc": 26, "nme": "LONG_POW_10", "dsc": "[J"}, {"acc": 26, "nme": "LOG_10", "dsc": "D"}, {"acc": 26, "nme": "FLOAT_RELATIVE_ERROR", "dsc": "F"}, {"acc": 26, "nme": "DOUBLE_RELATIVE_ERROR", "dsc": "D"}, {"acc": 10, "nme": "LEADING_ZEROS", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "DOUBLE_POW_10", "dsc": "[D"}]}, "org/codehaus/plexus/util/StringUtils.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/StringUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clean", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "trim", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "deleteWhitespace", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "isNotEmpty", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isEmpty", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isBlank", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isNotBlank", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "equals", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "equalsIgnoreCase", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "indexOfAny", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;)I"}, {"nme": "lastIndexOfAny", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;)I"}, {"nme": "substring", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Ljava/lang/String;"}, {"nme": "substring", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;II)Ljava/lang/String;"}, {"nme": "left", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Ljava/lang/String;"}, {"nme": "right", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Ljava/lang/String;"}, {"nme": "mid", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;II)Ljava/lang/String;"}, {"nme": "split", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[Lja<PERSON>/lang/String;"}, {"nme": "split", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)[Ljava/lang/String;"}, {"nme": "split", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;I)[Ljava/lang/String;"}, {"nme": "concatenate", "acc": 9, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;)<PERSON>ja<PERSON>/lang/String;"}, {"nme": "join", "acc": 9, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "join", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Iterator;<PERSON><PERSON><PERSON>/lang/String;)<PERSON>ja<PERSON>/lang/String;", "sig": "(<PERSON><PERSON><PERSON>/util/Iterator<*>;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "replaceOnce", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;CC)Ljava/lang/String;"}, {"nme": "replace", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;CC)Ljava/lang/String;"}, {"nme": "replace", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;CCI)Ljava/lang/String;"}, {"nme": "replaceOnce", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)<PERSON>java/lang/String;"}, {"nme": "replace", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)<PERSON>java/lang/String;"}, {"nme": "replace", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;I)Ljava/lang/String;"}, {"nme": "overlayString", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;II)Ljava/lang/String;"}, {"nme": "center", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Ljava/lang/String;"}, {"nme": "center", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "chomp", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "chomp", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "chompLast", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "chompLast", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getChomp", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "prechomp", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getPrechomp", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "chop", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "chopNewline", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "escape", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "repeat", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Ljava/lang/String;"}, {"nme": "rightPad", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Ljava/lang/String;"}, {"nme": "rightPad", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "leftPad", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Ljava/lang/String;"}, {"nme": "leftPad", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "strip", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "strip", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "stripAll", "acc": 9, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)[Ljava/lang/String;"}, {"nme": "stripAll", "acc": 9, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)[Ljava/lang/String;"}, {"nme": "stripEnd", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "stripStart", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "upperCase", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "lowerCase", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "uncapitalise", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "capitalise", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "swapCase", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "capitaliseAllWords", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "uncapitaliseAllWords", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getNestedString", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getNestedString", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)<PERSON>java/lang/String;"}, {"nme": "count<PERSON><PERSON><PERSON>", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "isAlpha", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isWhitespace", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isAlphaSpace", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isAlphanumeric", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isAlphanumericSpace", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isNumeric", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isNumericSpace", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "defaultString", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON>ja<PERSON>/lang/String;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "defaultString", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "reverse", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "reverseDelimitedString", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "reverseArray", "acc": 10, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "abbreviate", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Ljava/lang/String;"}, {"nme": "abbreviate", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;II)Ljava/lang/String;"}, {"nme": "difference", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "differenceAt", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "interpolate", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;", "sig": "(Lja<PERSON>/lang/String;Ljava/util/Map<**>;)Ljava/lang/String;"}, {"nme": "removeAndHump", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "capitalizeFirstLetter", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "lowercaseFirstLetter", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "addAndDeHump", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "quoteAndEscape", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;C)Ljava/lang/String;"}, {"nme": "quoteAndEscape", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;C[C)Ljava/lang/String;"}, {"nme": "quoteAndEscape", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;C[CCZ)Ljava/lang/String;"}, {"nme": "quoteAndEscape", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;C[C[CCZ)Ljava/lang/String;"}, {"nme": "quoteAndEscape", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;C[C[CLjava/lang/String;Z)Ljava/lang/String;"}, {"nme": "escape", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[CC)Ljava/lang/String;"}, {"nme": "escape", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[C<PERSON><PERSON>va/lang/String;)Ljava/lang/String;"}, {"nme": "removeDuplicateWhitespace", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "unifyLineSeparators", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "unifyLineSeparators", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "contains", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;C)Z"}, {"nme": "contains", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}], "flds": []}, "META-INF/versions/11/org/codehaus/plexus/util/BaseFileUtils.class": {"ver": 55, "acc": 1056, "nme": "org/codehaus/plexus/util/BaseFileUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "fileRead", "acc": 8, "dsc": "(Lja<PERSON>/nio/file/Path;Ljava/lang/String;)Ljava/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "fileWrite", "acc": 136, "dsc": "(Ljava/nio/file/Path;Ljava/lang/String;Ljava/lang/String;[Ljava/nio/file/OpenOption;)V", "exs": ["java/io/IOException"]}], "flds": []}, "org/codehaus/plexus/util/CachedMap.class": {"ver": 52, "acc": 49, "nme": "org/codehaus/plexus/util/CachedMap", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(I)V"}, {"nme": "<init>", "acc": 1, "dsc": "(ILjava/util/Map;)V"}, {"nme": "getCacheSize", "acc": 1, "dsc": "()I"}, {"nme": "getBackingMap", "acc": 1, "dsc": "()Ljava/util/Map;"}, {"nme": "flush", "acc": 1, "dsc": "()V"}, {"nme": "get", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getCacheMissed", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;I)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "put", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "remove", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "size", "acc": 1, "dsc": "()I"}, {"nme": "isEmpty", "acc": 1, "dsc": "()Z"}, {"nme": "containsValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "putAll", "acc": 1, "dsc": "(Ljava/util/Map;)V"}, {"nme": "clear", "acc": 1, "dsc": "()V"}, {"nme": "keySet", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;"}, {"nme": "values", "acc": 1, "dsc": "()Ljava/util/Collection;"}, {"nme": "entrySet", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "_backingFastMap", "dsc": "Lorg/codehaus/plexus/util/FastMap;"}, {"acc": 18, "nme": "_backingMap", "dsc": "Ljava/util/Map;"}, {"acc": 18, "nme": "_keysMap", "dsc": "Lorg/codehaus/plexus/util/FastMap;"}, {"acc": 18, "nme": "_mask", "dsc": "I"}, {"acc": 18, "nme": "_keys", "dsc": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 18, "nme": "_values", "dsc": "[<PERSON><PERSON><PERSON>/lang/Object;"}]}, "org/codehaus/plexus/util/dag/CycleDetector.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/dag/CycleDetector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "hasCycle", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/dag/DAG;)Ljava/util/List;", "sig": "(Lorg/codehaus/plexus/util/dag/DAG;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "introducesCycle", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/dag/Vertex;Ljava/util/Map;)Ljava/util/List;", "sig": "(Lorg/codehaus/plexus/util/dag/Vertex;Ljava/util/Map<Lorg/codehaus/plexus/util/dag/Vertex;Ljava/lang/Integer;>;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "introducesCycle", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/dag/Vertex;)Ljava/util/List;", "sig": "(Lorg/codehaus/plexus/util/dag/Vertex;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "isNotVisited", "acc": 10, "dsc": "(Lorg/codehaus/plexus/util/dag/Vertex;Ljava/util/Map;)Z", "sig": "(Lorg/codehaus/plexus/util/dag/Vertex;Ljava/util/Map<Lorg/codehaus/plexus/util/dag/Vertex;Ljava/lang/Integer;>;)Z"}, {"nme": "isVisiting", "acc": 10, "dsc": "(Lorg/codehaus/plexus/util/dag/Vertex;Ljava/util/Map;)Z", "sig": "(Lorg/codehaus/plexus/util/dag/Vertex;Ljava/util/Map<Lorg/codehaus/plexus/util/dag/Vertex;Ljava/lang/Integer;>;)Z"}, {"nme": "dfsVisit", "acc": 10, "dsc": "(Lorg/codehaus/plexus/util/dag/Vertex;<PERSON>ja<PERSON>/util/LinkedList;<PERSON>java/util/Map;)Z", "sig": "(Lorg/codehaus/plexus/util/dag/Vertex;Ljava/util/LinkedList<Ljava/lang/String;>;Ljava/util/Map<Lorg/codehaus/plexus/util/dag/Vertex;Ljava/lang/Integer;>;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "NOT_VISITED", "dsc": "<PERSON><PERSON><PERSON>/lang/Integer;"}, {"acc": 26, "nme": "VISITING", "dsc": "<PERSON><PERSON><PERSON>/lang/Integer;"}, {"acc": 26, "nme": "VISITED", "dsc": "<PERSON><PERSON><PERSON>/lang/Integer;"}]}, "org/codehaus/plexus/util/xml/XmlStreamReader.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/xml/XmlStreamReader", "super": "org/codehaus/plexus/util/xml/XmlReader", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON>va/io/InputStream;)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/io/InputStream;Z)V", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/XmlStreamReaderException"]}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/net/URL;)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/net/URLConnection;)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/XmlStreamReaderException"]}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;<PERSON><PERSON><PERSON>/lang/String;Z)V", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/XmlStreamReaderException"]}], "flds": []}, "org/codehaus/plexus/util/DirectoryWalker.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/DirectoryWalker", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "addDirectoryWalkListener", "acc": 1, "dsc": "(Lorg/codehaus/plexus/util/DirectoryWalkListener;)V"}, {"nme": "addExclude", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "addInclude", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "addSCMExcludes", "acc": 1, "dsc": "()V"}, {"nme": "fireStep", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V"}, {"nme": "fireWalkFinished", "acc": 2, "dsc": "()V"}, {"nme": "fireWalkStarting", "acc": 2, "dsc": "()V"}, {"nme": "fireDebugMessage", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "fixPattern", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "setDebugMode", "acc": 1, "dsc": "(Z)V"}, {"nme": "getBaseDir", "acc": 1, "dsc": "()Ljava/io/File;"}, {"nme": "getExcludes", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "getIncludes", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "isExcluded", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isIncluded", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isMatch", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/lang/String;)Z", "sig": "(<PERSON><PERSON><PERSON>/util/List<Ljava/lang/String;>;Ljava/lang/String;)Z"}, {"nme": "relativeToBaseDir", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)Ljava/lang/String;"}, {"nme": "removeDirectoryWalkListener", "acc": 1, "dsc": "(Lorg/codehaus/plexus/util/DirectoryWalkListener;)V"}, {"nme": "scan", "acc": 1, "dsc": "()V"}, {"nme": "scanDir", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V"}, {"nme": "setBaseDir", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V"}, {"nme": "setExcludes", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/String;>;)V"}, {"nme": "setIncludes", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/String;>;)V"}], "flds": [{"acc": 2, "nme": "baseDir", "dsc": "Ljava/io/File;"}, {"acc": 2, "nme": "baseDirOffset", "dsc": "I"}, {"acc": 2, "nme": "dir<PERSON><PERSON><PERSON>", "dsc": "<PERSON><PERSON><PERSON>/util/<PERSON>ack;", "sig": "Ljava/util/Stack<Lorg/codehaus/plexus/util/DirectoryWalker$DirStackEntry;>;"}, {"acc": 2, "nme": "excludes", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 2, "nme": "includes", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 2, "nme": "isCaseSensitive", "dsc": "Z"}, {"acc": 2, "nme": "listeners", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/codehaus/plexus/util/DirectoryWalkListener;>;"}, {"acc": 2, "nme": "debugEnabled", "dsc": "Z"}]}, "org/codehaus/plexus/util/StringOutputStream.class": {"ver": 52, "acc": 131105, "nme": "org/codehaus/plexus/util/StringOutputStream", "super": "java/io/OutputStream", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "write", "acc": 1, "dsc": "([B)V", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "([BII)V", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(I)V", "exs": ["java/io/IOException"]}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "buf", "dsc": "<PERSON><PERSON><PERSON>/lang/StringBuffer;"}], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "org/codehaus/plexus/util/reflection/Reflector.class": {"ver": 52, "acc": 49, "nme": "org/codehaus/plexus/util/reflection/Reflector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "newInstance", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;[<PERSON>ja<PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;[Ljava/lang/Object;)TT;", "exs": ["org/codehaus/plexus/util/reflection/ReflectorException"]}, {"nme": "getSingleton", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;[<PERSON>ja<PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;[Ljava/lang/Object;)TT;", "exs": ["org/codehaus/plexus/util/reflection/ReflectorException"]}, {"nme": "invoke", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;[Lja<PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["org/codehaus/plexus/util/reflection/ReflectorException"]}, {"nme": "getStaticField", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["org/codehaus/plexus/util/reflection/ReflectorException"]}, {"nme": "getField", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["org/codehaus/plexus/util/reflection/ReflectorException"]}, {"nme": "getField", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;Z)Ljava/lang/Object;", "exs": ["org/codehaus/plexus/util/reflection/ReflectorException"]}, {"nme": "invokeStatic", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;L<PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["org/codehaus/plexus/util/reflection/ReflectorException"]}, {"nme": "getConstructor", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;[Ljava/lang/Class;)Ljava/lang/reflect/Constructor;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;[Ljava/lang/Class;)Ljava/lang/reflect/Constructor<TT;>;", "exs": ["org/codehaus/plexus/util/reflection/ReflectorException"]}, {"nme": "getObjectProperty", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["org/codehaus/plexus/util/reflection/ReflectorException"]}, {"nme": "getMethod", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Lja<PERSON>/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;", "exs": ["org/codehaus/plexus/util/reflection/ReflectorException"]}, {"nme": "_getMethod", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Lja<PERSON>/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;", "exs": ["org/codehaus/plexus/util/reflection/ReflectorException"]}, {"nme": "getConstructorMap", "acc": 2, "dsc": "(L<PERSON><PERSON>/lang/Class;)Ljava/util/Map;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/reflect/Constructor<TT;>;>;", "exs": ["org/codehaus/plexus/util/reflection/ReflectorException"]}, {"nme": "getMethodMap", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/String;)Ljava/util/Map;", "sig": "(Lja<PERSON>/lang/Class;Ljava/lang/String;)Ljava/util/Map<Ljava/lang/String;*>;", "exs": ["org/codehaus/plexus/util/reflection/ReflectorException"]}], "flds": [{"acc": 26, "nme": "CONSTRUCTOR_METHOD_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "$$CONSTRUCTOR$$"}, {"acc": 26, "nme": "GET_INSTANCE_METHOD_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "getInstance"}, {"acc": 2, "nme": "classMaps", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljava/lang/reflect/Method;>;>;>;"}]}, "org/codehaus/plexus/util/cli/CommandLineUtils$1.class": {"ver": 52, "acc": 32, "nme": "org/codehaus/plexus/util/cli/CommandLineUtils$1", "super": "java/lang/Thread", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Process;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "val$p", "dsc": "<PERSON><PERSON><PERSON>/lang/Process;"}]}, "org/codehaus/plexus/util/BaseFileUtils.class": {"ver": 52, "acc": 1056, "nme": "org/codehaus/plexus/util/BaseFileUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "fileRead", "acc": 8, "dsc": "(Lja<PERSON>/nio/file/Path;Ljava/lang/String;)Ljava/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "fileWrite", "acc": 136, "dsc": "(Ljava/nio/file/Path;Ljava/lang/String;Ljava/lang/String;[Ljava/nio/file/OpenOption;)V", "exs": ["java/io/IOException"]}], "flds": []}, "org/codehaus/plexus/util/cli/Commandline.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/cli/Commandline", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/codehaus/plexus/util/cli/shell/Shell;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/codehaus/plexus/util/cli/shell/Shell;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getPid", "acc": 1, "dsc": "()J"}, {"nme": "setPid", "acc": 1, "dsc": "(J)V"}, {"nme": "setDefaultShell", "acc": 2, "dsc": "()V"}, {"nme": "createArgument", "acc": 131073, "dsc": "()Lorg/codehaus/plexus/util/cli/Commandline$Argument;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "createArgument", "acc": 131073, "dsc": "(Z)Lorg/codehaus/plexus/util/cli/Commandline$Argument;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "createArg", "acc": 1, "dsc": "()Lorg/codehaus/plexus/util/cli/Arg;"}, {"nme": "createArg", "acc": 1, "dsc": "(Z)Lorg/codehaus/plexus/util/cli/Arg;"}, {"nme": "addArg", "acc": 1, "dsc": "(Lorg/codehaus/plexus/util/cli/Arg;)V"}, {"nme": "addArg", "acc": 1, "dsc": "(Lorg/codehaus/plexus/util/cli/Arg;Z)V"}, {"nme": "setExecutable", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getLiteralExecutable", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getExecutable", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "addArguments", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "addEnvironment", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "addSystemEnvironment", "acc": 1, "dsc": "()V", "exs": ["java/lang/Exception"]}, {"nme": "getEnvironmentVariables", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;", "exs": ["org/codehaus/plexus/util/cli/CommandLineException"]}, {"nme": "getCommandline", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getRawCommandline", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getShellCommandline", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getArguments", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "size", "acc": 1, "dsc": "()I"}, {"nme": "clone", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "clear", "acc": 1, "dsc": "()V"}, {"nme": "clearArgs", "acc": 1, "dsc": "()V"}, {"nme": "createMarker", "acc": 1, "dsc": "()Lorg/codehaus/plexus/util/cli/Commandline$Marker;"}, {"nme": "setWorkingDirectory", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setWorkingDirectory", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V"}, {"nme": "getWorkingDirectory", "acc": 1, "dsc": "()Ljava/io/File;"}, {"nme": "execute", "acc": 1, "dsc": "()Ljava/lang/Process;", "exs": ["org/codehaus/plexus/util/cli/CommandLineException"]}, {"nme": "verifyShellState", "acc": 131074, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getSystemEnvVars", "acc": 1, "dsc": "()Ljava/util/Properties;", "exs": ["java/lang/Exception"]}, {"nme": "setShell", "acc": 1, "dsc": "(Lorg/codehaus/plexus/util/cli/shell/Shell;)V"}, {"nme": "getShell", "acc": 1, "dsc": "()Lorg/codehaus/plexus/util/cli/shell/Shell;"}, {"nme": "translateCommandline", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[Lja<PERSON>/lang/String;", "exs": ["java/lang/Exception"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "quoteArgument", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "exs": ["org/codehaus/plexus/util/cli/CommandLineException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "toString", "acc": 131081, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}], "flds": [{"acc": 131100, "nme": "OS_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "os.name"}, {"acc": 131100, "nme": "WINDOWS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "Windows"}, {"acc": 4, "nme": "arguments", "dsc": "<PERSON><PERSON><PERSON>/util/Vector;", "sig": "Ljava/util/Vector<Lorg/codehaus/plexus/util/cli/Arg;>;"}, {"acc": 4, "nme": "envVars", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"acc": 2, "nme": "pid", "dsc": "J"}, {"acc": 2, "nme": "shell", "dsc": "Lorg/codehaus/plexus/util/cli/shell/Shell;"}, {"acc": 131076, "nme": "executable", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 131074, "nme": "workingDir", "dsc": "Ljava/io/File;"}]}, "org/codehaus/plexus/util/PropertyUtils.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/PropertyUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "loadProperties", "acc": 9, "dsc": "(Ljava/net/URL;)Ljava/util/Properties;", "exs": ["java/io/IOException"]}, {"nme": "loadProperties", "acc": 9, "dsc": "(Ljava/io/File;)Ljava/util/Properties;", "exs": ["java/io/IOException"]}, {"nme": "loadProperties", "acc": 9, "dsc": "(Ljava/io/InputStream;)Ljava/util/Properties;", "exs": ["java/io/IOException"]}], "flds": []}, "org/codehaus/plexus/util/introspection/ClassMap$MethodInfo.class": {"ver": 52, "acc": 48, "nme": "org/codehaus/plexus/util/introspection/ClassMap$MethodInfo", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)V"}, {"nme": "tryUpcasting", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "exs": ["java/lang/NoSuchMethodException"]}], "flds": [{"acc": 0, "nme": "method", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 0, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "parameterTypes", "dsc": "[Ljava/lang/Class;"}, {"acc": 0, "nme": "upcast", "dsc": "Z"}]}, "org/codehaus/plexus/util/introspection/MethodMap$AmbiguousException.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/introspection/MethodMap$AmbiguousException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "org/codehaus/plexus/util/NioFiles.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/NioFiles", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "isSymbolicLink", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)Z"}, {"nme": "chmod", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/File;I)V", "exs": ["java/io/IOException"]}, {"nme": "getPermissions", "acc": 10, "dsc": "(I)<PERSON><PERSON><PERSON>/util/Set;", "sig": "(I)Ljava/util/Set<Ljava/nio/file/attribute/PosixFilePermission;>;"}, {"nme": "getLastModified", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)J", "exs": ["java/io/IOException"]}, {"nme": "readSymbolicLink", "acc": 9, "dsc": "(Lja<PERSON>/io/File;)Ljava/io/File;", "exs": ["java/io/IOException"]}, {"nme": "createSymbolicLink", "acc": 9, "dsc": "(Ljava/io/File;Ljava/io/File;)Ljava/io/File;", "exs": ["java/io/IOException"]}, {"nme": "deleteIfExists", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)Z", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 9, "dsc": "(Ljava/io/File;Ljava/io/File;)Ljava/io/File;", "exs": ["java/io/IOException"]}], "flds": []}, "org/codehaus/plexus/util/xml/XmlReader.class": {"ver": 52, "acc": 131105, "nme": "org/codehaus/plexus/util/xml/XmlReader", "super": "java/io/Reader", "mthds": [{"nme": "setDefaultEncoding", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getDefaultEncoding", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON>va/io/InputStream;)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/io/InputStream;Z)V", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/XmlStreamReaderException"]}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/net/URL;)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/net/URLConnection;)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/XmlStreamReaderException"]}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;<PERSON><PERSON><PERSON>/lang/String;Z)V", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/XmlStreamReaderException"]}, {"nme": "doLenientDetection", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/codehaus/plexus/util/xml/XmlStreamReaderException;)V", "exs": ["java/io/IOException"]}, {"nme": "getEncoding", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "read", "acc": 1, "dsc": "([CII)I", "exs": ["java/io/IOException"]}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "doRawStream", "acc": 2, "dsc": "(Ljava/io/InputStream;Z)V", "exs": ["java/io/IOException"]}, {"nme": "doHttpStream", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;<PERSON><PERSON><PERSON>/lang/String;Z)V", "exs": ["java/io/IOException"]}, {"nme": "prepare<PERSON>eader", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "calculateRawEncoding", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/String;L<PERSON><PERSON>/io/InputStream;)Ljava/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "calculateHttpEncoding", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/io/InputStream;Z)Ljava/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "getContentTypeMime", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getContentTypeEncoding", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getBOMEncoding", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/io/BufferedInputStream;)Lja<PERSON>/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "getXMLGuessEncoding", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/io/BufferedInputStream;)Lja<PERSON>/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "getXmlProlog", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/io/BufferedInputStream;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "isAppXml", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isTextXml", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "BUFFER_SIZE", "dsc": "I", "val": 4096}, {"acc": 26, "nme": "UTF_8", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "UTF-8"}, {"acc": 26, "nme": "US_ASCII", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "US-ASCII"}, {"acc": 26, "nme": "UTF_16BE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "UTF-16BE"}, {"acc": 26, "nme": "UTF_16LE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "UTF-16LE"}, {"acc": 26, "nme": "UTF_16", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "UTF-16"}, {"acc": 26, "nme": "EBCDIC", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "CP1047"}, {"acc": 10, "nme": "_staticDefaultEncoding", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "_reader", "dsc": "<PERSON><PERSON><PERSON>/io/Reader;"}, {"acc": 2, "nme": "_encoding", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "_defaultEncoding", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "CHARSET_PATTERN", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 24, "nme": "ENCODING_PATTERN", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 26, "nme": "RAW_EX_1", "dsc": "Ljava/text/MessageFormat;"}, {"acc": 26, "nme": "RAW_EX_2", "dsc": "Ljava/text/MessageFormat;"}, {"acc": 26, "nme": "HTTP_EX_1", "dsc": "Ljava/text/MessageFormat;"}, {"acc": 26, "nme": "HTTP_EX_2", "dsc": "Ljava/text/MessageFormat;"}, {"acc": 26, "nme": "HTTP_EX_3", "dsc": "Ljava/text/MessageFormat;"}], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "org/codehaus/plexus/util/introspection/ReflectionValueExtractor$Tokenizer.class": {"ver": 52, "acc": 32, "nme": "org/codehaus/plexus/util/introspection/ReflectionValueExtractor$Tokenizer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "peekChar", "acc": 1, "dsc": "()I"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()I"}, {"nme": "nextToken", "acc": 1, "dsc": "(C)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "nextPropertyName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getPosition", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 16, "nme": "expression", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "idx", "dsc": "I"}]}, "org/codehaus/plexus/util/cli/EnhancedStringTokenizer.class": {"ver": 52, "acc": 49, "nme": "org/codehaus/plexus/util/cli/EnhancedStringTokenizer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Z)V"}, {"nme": "hasMoreTokens", "acc": 1, "dsc": "()Z"}, {"nme": "internalNextToken", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "nextToken", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}], "flds": [{"acc": 2, "nme": "cst", "dsc": "<PERSON><PERSON><PERSON>/util/StringTokenizer;"}, {"acc": 0, "nme": "cdelim", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 16, "nme": "cdelimSingleChar", "dsc": "Z"}, {"acc": 16, "nme": "cdelimChar", "dsc": "C"}, {"acc": 0, "nme": "c<PERSON><PERSON><PERSON><PERSON><PERSON>", "dsc": "Z"}, {"acc": 0, "nme": "lastToken", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "delimLast", "dsc": "Z"}]}, "org/codehaus/plexus/util/reflection/ReflectorException.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/reflection/ReflectorException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}], "flds": []}, "org/codehaus/plexus/util/FileUtils$FilterWrapper.class": {"ver": 52, "acc": 1057, "nme": "org/codehaus/plexus/util/FileUtils$FilterWrapper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;)<PERSON><PERSON><PERSON>/io/Reader;"}], "flds": []}, "org/codehaus/plexus/util/introspection/ClassMap.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/introspection/ClassMap", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V"}, {"nme": "getCachedClass", "acc": 0, "dsc": "()<PERSON><PERSON>va/lang/Class;"}, {"nme": "find<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Lja<PERSON>/lang/Object;)Ljava/lang/reflect/Method;", "exs": ["org/codehaus/plexus/util/introspection/MethodMap$AmbiguousException"]}, {"nme": "populate<PERSON>ethod<PERSON>ache", "acc": 2, "dsc": "()V"}, {"nme": "makeMethodKey", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)Ljava/lang/String;"}, {"nme": "makeMethodKey", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/Object;)Ljava/lang/String;"}, {"nme": "getAccessibleMethods", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)[Ljava/lang/reflect/Method;"}, {"nme": "getAccessibleMethods", "acc": 10, "dsc": "(<PERSON>ja<PERSON>/lang/Class;[Lorg/codehaus/plexus/util/introspection/ClassMap$MethodInfo;I)I"}, {"nme": "getPublicMethod", "acc": 9, "dsc": "(Lja<PERSON>/lang/reflect/Method;)Ljava/lang/reflect/Method;"}, {"nme": "getPublicMethod", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Lja<PERSON>/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CACHE_MISS", "dsc": "Lorg/codehaus/plexus/util/introspection/ClassMap$CacheMiss;"}, {"acc": 26, "nme": "OBJECT", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 18, "nme": "clazz", "dsc": "L<PERSON>va/lang/Class;"}, {"acc": 2, "nme": "methodCache", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;"}, {"acc": 18, "nme": "methodMap", "dsc": "Lorg/codehaus/plexus/util/introspection/MethodMap;"}]}, "org/codehaus/plexus/util/BaseIOUtil.class": {"ver": 52, "acc": 1056, "nme": "org/codehaus/plexus/util/BaseIOUtil", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "copy", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;Ljava/io/OutputStream;)V", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;<PERSON><PERSON><PERSON>/io/Writer;)V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 26, "nme": "DEFAULT_BUFFER_SIZE", "dsc": "I", "val": 16384}]}, "org/codehaus/plexus/util/xml/pull/MXSerializer.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/xml/pull/MXSerializer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "checkInterning", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "reset", "acc": 4, "dsc": "()V"}, {"nme": "ensureElementsCapacity", "acc": 4, "dsc": "()V"}, {"nme": "ensureNamespacesCapacity", "acc": 4, "dsc": "()V"}, {"nme": "setFeature", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)V", "exs": ["java/lang/IllegalArgumentException", "java/lang/IllegalStateException"]}, {"nme": "getFeature", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "rebuildIndentationBuf", "acc": 4, "dsc": "()V"}, {"nme": "writeIndent", "acc": 4, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "setProperty", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V", "exs": ["java/lang/IllegalArgumentException", "java/lang/IllegalStateException"]}, {"nme": "getProperty", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "getLocation", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getWriter", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/io/Writer;"}, {"nme": "setOutput", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Writer;)V"}, {"nme": "setOutput", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/OutputStream;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "startDocument", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Boolean;)V", "exs": ["java/io/IOException"]}, {"nme": "endDocument", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "setPrefix", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "lookupOrDeclarePrefix", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getPrefix", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)Ljava/lang/String;"}, {"nme": "generatePrefix", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON>h", "acc": 1, "dsc": "()I"}, {"nme": "getNamespace", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "startTag", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;)Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;", "exs": ["java/io/IOException"]}, {"nme": "attribute", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>java/lang/String;Ljava/lang/String;)Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;", "exs": ["java/io/IOException"]}, {"nme": "closeStartTag", "acc": 4, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "writeNamespaceDeclarations", "acc": 2, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "endTag", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;)Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;", "exs": ["java/io/IOException"]}, {"nme": "text", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;", "exs": ["java/io/IOException"]}, {"nme": "text", "acc": 1, "dsc": "([CII)Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;", "exs": ["java/io/IOException"]}, {"nme": "cdsect", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "entityRef", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "processingInstruction", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "comment", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "docdecl", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "ignorableWhitespace", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "flush", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "writeAttributeValue", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/io/Writer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeElement<PERSON><PERSON>nt", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/io/Writer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeElement<PERSON><PERSON>nt", "acc": 4, "dsc": "([C<PERSON><PERSON><PERSON><PERSON>/io/Writer;)V", "exs": ["java/io/IOException"]}, {"nme": "printable", "acc": 28, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "printable", "acc": 28, "dsc": "(C)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "addPrintable", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;C)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 28, "nme": "XML_URI", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "http://www.w3.org/XML/1998/namespace"}, {"acc": 28, "nme": "XMLNS_URI", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "http://www.w3.org/2000/xmlns/"}, {"acc": 26, "nme": "TRACE_SIZING", "dsc": "Z", "val": 0}, {"acc": 20, "nme": "FEATURE_SERIALIZER_ATTVALUE_USE_APOSTROPHE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "http://xmlpull.org/v1/doc/features.html#serializer-attvalue-use-apostrophe"}, {"acc": 20, "nme": "FEATURE_NAMES_INTERNED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "http://xmlpull.org/v1/doc/features.html#names-interned"}, {"acc": 20, "nme": "PROPERTY_SERIALIZER_INDENTATION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "http://xmlpull.org/v1/doc/properties.html#serializer-indentation"}, {"acc": 20, "nme": "PROPERTY_SERIALIZER_LINE_SEPARATOR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "http://xmlpull.org/v1/doc/properties.html#serializer-line-separator"}, {"acc": 28, "nme": "PROPERTY_LOCATION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "http://xmlpull.org/v1/doc/properties.html#location"}, {"acc": 4, "nme": "namesInterned", "dsc": "Z"}, {"acc": 4, "nme": "attributeUseApostrophe", "dsc": "Z"}, {"acc": 4, "nme": "indentationString", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4, "nme": "lineSeparator", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4, "nme": "location", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4, "nme": "out", "dsc": "<PERSON><PERSON><PERSON>/io/Writer;"}, {"acc": 4, "nme": "autoDeclaredPrefixes", "dsc": "I"}, {"acc": 4, "nme": "depth", "dsc": "I"}, {"acc": 4, "nme": "elNamespace", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4, "nme": "el<PERSON>ame", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4, "nme": "elNamespaceCount", "dsc": "[I"}, {"acc": 4, "nme": "namespaceEnd", "dsc": "I"}, {"acc": 4, "nme": "namespacePrefix", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4, "nme": "namespaceUri", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4, "nme": "finished", "dsc": "Z"}, {"acc": 4, "nme": "pastRoot", "dsc": "Z"}, {"acc": 4, "nme": "setPrefixCalled", "dsc": "Z"}, {"acc": 4, "nme": "startTagIncomplete", "dsc": "Z"}, {"acc": 4, "nme": "doIndent", "dsc": "Z"}, {"acc": 4, "nme": "seenTag", "dsc": "Z"}, {"acc": 4, "nme": "seenBracket", "dsc": "Z"}, {"acc": 4, "nme": "seenBracketBracket", "dsc": "Z"}, {"acc": 26, "nme": "BUF_LEN", "dsc": "I"}, {"acc": 4, "nme": "buf", "dsc": "[C"}, {"acc": 28, "nme": "precomputedPrefixes", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "checkNamesInterned", "dsc": "Z"}, {"acc": 4, "nme": "offsetNewLine", "dsc": "I"}, {"acc": 4, "nme": "indentationJump", "dsc": "I"}, {"acc": 4, "nme": "indentationBuf", "dsc": "[C"}, {"acc": 4, "nme": "maxIndentLevel", "dsc": "I"}, {"acc": 4, "nme": "writeLineSeparator", "dsc": "Z"}, {"acc": 4, "nme": "writeIndentation", "dsc": "Z"}]}, "org/codehaus/plexus/util/xml/PrettyPrintXMLWriter.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/xml/PrettyPrintXMLWriter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/PrintWriter;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Writer;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/io/PrintWriter;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Writer;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/PrintWriter;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Writer;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/PrintWriter;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Writer;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/PrintWriter;<PERSON>ja<PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "startElement", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "writeText", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "writeMarkup", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "writeText", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)V"}, {"nme": "escapeXml", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "escapeXmlAttribute", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "addAttribute", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "endElement", "acc": 1, "dsc": "()V"}, {"nme": "write", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "finishTag", "acc": 2, "dsc": "()V"}, {"nme": "getLineIndenter", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLineIndenter", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getLineSeparator", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLineSeparator", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "endOfLine", "acc": 4, "dsc": "()V"}, {"nme": "writeDocumentHeaders", "acc": 2, "dsc": "()V"}, {"nme": "setWriter", "acc": 4, "dsc": "(Ljava/io/PrintWriter;)V"}, {"nme": "getWriter", "acc": 4, "dsc": "()Ljava/io/PrintWriter;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 4, "dsc": "(I)V"}, {"nme": "<PERSON><PERSON><PERSON>h", "acc": 4, "dsc": "()I"}, {"nme": "setEncoding", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getEncoding", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setDocType", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getDocType", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getElementStack", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/util/LinkedList;", "sig": "()Ljava/util/LinkedList<Ljava/lang/String;>;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 28, "nme": "LS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "writer", "dsc": "Ljava/io/PrintWriter;"}, {"acc": 2, "nme": "elementStack", "dsc": "<PERSON><PERSON><PERSON>/util/LinkedList;", "sig": "Ljava/util/LinkedList<Ljava/lang/String;>;"}, {"acc": 2, "nme": "tagInProgress", "dsc": "Z"}, {"acc": 2, "nme": "depth", "dsc": "I"}, {"acc": 2, "nme": "lineIndenter", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "lineSeparator", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "encoding", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "docType", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "readyForNewLine", "dsc": "Z"}, {"acc": 2, "nme": "tagIsEmpty", "dsc": "Z"}, {"acc": 26, "nme": "amp", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 26, "nme": "lt", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 26, "nme": "gt", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 26, "nme": "dqoute", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 26, "nme": "sqoute", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 26, "nme": "crlf_str", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "\r\n"}, {"acc": 26, "nme": "crlf", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 26, "nme": "lowers", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}]}, "org/codehaus/plexus/util/Expand.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/Expand", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "execute", "acc": 1, "dsc": "()V", "exs": ["java/lang/Exception"]}, {"nme": "expandFile", "acc": 4, "dsc": "(Ljava/io/File;Ljava/io/File;)V", "exs": ["java/lang/Exception"]}, {"nme": "extractFile", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/io/File;Ljava/io/File;Ljava/io/InputStream;L<PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Date;Z)V", "exs": ["java/lang/Exception"]}, {"nme": "setDest", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V"}, {"nme": "setSrc", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V"}, {"nme": "setOverwrite", "acc": 1, "dsc": "(Z)V"}], "flds": [{"acc": 2, "nme": "dest", "dsc": "Ljava/io/File;"}, {"acc": 2, "nme": "source", "dsc": "Ljava/io/File;"}, {"acc": 2, "nme": "overwrite", "dsc": "Z"}]}, "org/codehaus/plexus/util/SweeperPool.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/SweeperPool", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(IIIII)V"}, {"nme": "saneConvert", "acc": 2, "dsc": "(I)I"}, {"nme": "get", "acc": 33, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "put", "acc": 33, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "getSize", "acc": 33, "dsc": "()I"}, {"nme": "dispose", "acc": 1, "dsc": "()V"}, {"nme": "isDisposed", "acc": 0, "dsc": "()Z"}, {"nme": "trim", "acc": 33, "dsc": "()V"}, {"nme": "objectDisposed", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "objectAdded", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "objectRetrieved", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}], "flds": [{"acc": 26, "nme": "DEBUG", "dsc": "Z", "val": 0}, {"acc": 130, "nme": "sweeper", "dsc": "Lorg/codehaus/plexus/util/SweeperPool$Sweeper;"}, {"acc": 130, "nme": "maxSize", "dsc": "I"}, {"acc": 130, "nme": "minSize", "dsc": "I"}, {"acc": 2, "nme": "triggerSize", "dsc": "I"}, {"acc": 2, "nme": "pooledObjects", "dsc": "<PERSON><PERSON><PERSON>/util/ArrayList;", "sig": "Ljava/util/ArrayList<Ljava/lang/Object;>;"}, {"acc": 2, "nme": "shuttingDown", "dsc": "Z"}]}, "org/codehaus/plexus/util/cli/Commandline$Argument.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/cli/Commandline$Argument", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "setValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setLine", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setFile", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V"}, {"nme": "getParts", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "parts", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/codehaus/plexus/util/DirectoryWalker$DirStackEntry.class": {"ver": 52, "acc": 32, "nme": "org/codehaus/plexus/util/DirectoryWalker$DirStackEntry", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/codehaus/plexus/util/DirectoryWalker;Ljava/io/File;I)V"}, {"nme": "getNextPercentageOffset", "acc": 1, "dsc": "()D"}, {"nme": "getNextPercentageSize", "acc": 1, "dsc": "()D"}, {"nme": "getPercentage", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 1, "nme": "count", "dsc": "I"}, {"acc": 1, "nme": "dir", "dsc": "Ljava/io/File;"}, {"acc": 1, "nme": "index", "dsc": "I"}, {"acc": 1, "nme": "percentageOffset", "dsc": "D"}, {"acc": 1, "nme": "percentageSize", "dsc": "D"}, {"acc": 4112, "nme": "this$0", "dsc": "Lorg/codehaus/plexus/util/DirectoryWalker;"}]}, "org/codehaus/plexus/util/cli/CommandLineUtils$StringStreamConsumer.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/cli/CommandLineUtils$StringStreamConsumer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "consumeLine", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getOutput", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "string", "dsc": "<PERSON><PERSON><PERSON>/lang/StringBuffer;"}, {"acc": 2, "nme": "ls", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/codehaus/plexus/util/LineOrientedInterpolatingReader.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/LineOrientedInterpolatingReader", "super": "java/io/FilterReader", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;<PERSON><PERSON><PERSON>/util/Map;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "sig": "(<PERSON><PERSON><PERSON>/io/Reader;Ljava/util/Map<Ljava/lang/String;*>;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;<PERSON><PERSON><PERSON>/util/Map;Lja<PERSON>/lang/String;Lja<PERSON>/lang/String;)V", "sig": "(<PERSON><PERSON><PERSON>/io/Reader;Ljava/util/Map<Ljava/lang/String;*>;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;<PERSON>ja<PERSON>/util/Map;)V", "sig": "(<PERSON><PERSON><PERSON>/io/Reader;Ljava/util/Map<Ljava/lang/String;*>;)V"}, {"nme": "read", "acc": 1, "dsc": "()I", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 1, "dsc": "([CII)I", "exs": ["java/io/IOException"]}, {"nme": "skip", "acc": 1, "dsc": "(J)J", "exs": ["java/io/IOException"]}, {"nme": "readAndInterpolateLine", "acc": 2, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "readLine", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "replaceWithInterpolatedValues", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;", "sig": "(Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;)Ljava/lang/String;"}, {"nme": "evaluateExpressions", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)Ljava/util/Map;", "sig": "(L<PERSON><PERSON>/util/Set<Ljava/lang/String;>;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;"}, {"nme": "parseForExpressions", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/util/Set;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Set<Ljava/lang/String;>;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;I)I"}, {"nme": "findAndReplaceUnlessEscaped", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)<PERSON>java/lang/String;"}], "flds": [{"acc": 25, "nme": "DEFAULT_START_DELIM", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "${"}, {"acc": 25, "nme": "DEFAULT_END_DELIM", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "}"}, {"acc": 25, "nme": "DEFAULT_ESCAPE_SEQ", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "\\"}, {"acc": 26, "nme": "CARRIAGE_RETURN_CHAR", "dsc": "C", "val": 13}, {"acc": 26, "nme": "NEWLINE_CHAR", "dsc": "C", "val": 10}, {"acc": 18, "nme": "pushback<PERSON><PERSON><PERSON>", "dsc": "<PERSON><PERSON><PERSON>/io/<PERSON><PERSON><PERSON><PERSON><PERSON>;"}, {"acc": 18, "nme": "context", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;"}, {"acc": 18, "nme": "start<PERSON><PERSON><PERSON>", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "escapeSeq", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "minExpressionSize", "dsc": "I"}, {"acc": 18, "nme": "reflector", "dsc": "Lorg/codehaus/plexus/util/reflection/Reflector;"}, {"acc": 2, "nme": "lineIdx", "dsc": "I"}, {"acc": 2, "nme": "line", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/codehaus/plexus/util/PathTool.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/PathTool", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getRelativePath", "acc": 25, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getRelativePath", "acc": 25, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getDirectoryComponent", "acc": 25, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "calculateLink", "acc": 25, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getRelativeWebPath", "acc": 25, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getRelativeFilePath", "acc": 25, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "determineRelativePath", "acc": 26, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "determineSeparator", "acc": 26, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "uppercaseDrive", "acc": 24, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "buildRelativePath", "acc": 26, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;C)Ljava/lang/String;"}], "flds": []}, "org/codehaus/plexus/util/xml/XmlWriterUtil.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/xml/XmlWriterUtil", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "writeLineBreak", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/xml/XMLWriter;)V"}, {"nme": "writeLineBreak", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/xml/XMLWriter;I)V"}, {"nme": "writeLineBreak", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/xml/XMLWriter;II)V"}, {"nme": "writeLineBreak", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/xml/XMLWriter;III)V"}, {"nme": "writeCommentLineBreak", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/xml/XMLWriter;)V"}, {"nme": "writeCommentLineBreak", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/xml/XMLWriter;I)V"}, {"nme": "writeComment", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/xml/XMLWriter;Ljava/lang/String;)V"}, {"nme": "writeComment", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/xml/XMLWriter;Ljava/lang/String;I)V"}, {"nme": "writeComment", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/xml/XMLWriter;Ljava/lang/String;II)V"}, {"nme": "writeComment", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/xml/XMLWriter;Ljava/lang/String;III)V"}, {"nme": "writeCommentText", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/xml/XMLWriter;Ljava/lang/String;)V"}, {"nme": "writeCommentText", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/xml/XMLWriter;Ljava/lang/String;I)V"}, {"nme": "writeCommentText", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/xml/XMLWriter;Ljava/lang/String;II)V"}, {"nme": "writeCommentText", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/xml/XMLWriter;Ljava/lang/String;III)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "LS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 25, "nme": "DEFAULT_INDENTATION_SIZE", "dsc": "I", "val": 2}, {"acc": 25, "nme": "DEFAULT_COLUMN_LINE", "dsc": "I", "val": 80}]}, "org/codehaus/plexus/util/xml/Xpp3DomWriter.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/xml/Xpp3DomWriter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "write", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/Writer;Lorg/codehaus/plexus/util/xml/Xpp3Dom;)V"}, {"nme": "write", "acc": 9, "dsc": "(Ljava/io/PrintWriter;Lorg/codehaus/plexus/util/xml/Xpp3Dom;)V"}, {"nme": "write", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/xml/XMLWriter;Lorg/codehaus/plexus/util/xml/Xpp3Dom;)V"}, {"nme": "write", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/xml/XMLWriter;Lorg/codehaus/plexus/util/xml/Xpp3Dom;Z)V"}], "flds": []}, "org/codehaus/plexus/util/FastMap$1.class": {"ver": 52, "acc": 4128, "nme": "org/codehaus/plexus/util/FastMap$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/codehaus/plexus/util/xml/XmlStreamReaderException.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/xml/XmlStreamReaderException", "super": "org/codehaus/plexus/util/xml/XmlReaderException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;Ljava/io/InputStream;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/io/InputStream;)V"}], "flds": []}, "org/codehaus/plexus/util/MatchPatterns.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/MatchPatterns", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "([Lorg/codehaus/plexus/util/MatchPattern;)V"}, {"nme": "matches", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)Z"}, {"nme": "matches", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;Z)Z"}, {"nme": "matches", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[[CZ)Z"}, {"nme": "matchesPatternStart", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)Z"}, {"nme": "from", "acc": 137, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)Lorg/codehaus/plexus/util/MatchPatterns;"}, {"nme": "from", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)Lorg/codehaus/plexus/util/MatchPatterns;", "sig": "(Lja<PERSON>/lang/Iterable<Ljava/lang/String;>;)Lorg/codehaus/plexus/util/MatchPatterns;"}, {"nme": "getMatchPatterns", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)[Lorg/codehaus/plexus/util/MatchPattern;", "sig": "(Lja<PERSON>/lang/Iterable<Ljava/lang/String;>;)[Lorg/codehaus/plexus/util/MatchPattern;"}], "flds": [{"acc": 18, "nme": "patterns", "dsc": "[Lorg/codehaus/plexus/util/MatchPattern;"}]}, "org/codehaus/plexus/util/xml/pull/MXParser.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/xml/pull/MXParser", "super": "java/lang/Object", "mthds": [{"nme": "resetStringCache", "acc": 2, "dsc": "()V"}, {"nme": "newString", "acc": 2, "dsc": "([CII)Ljava/lang/String;"}, {"nme": "newStringIntern", "acc": 2, "dsc": "([CII)Ljava/lang/String;"}, {"nme": "ensureElementsCapacity", "acc": 2, "dsc": "()V"}, {"nme": "ensureAttributesCapacity", "acc": 2, "dsc": "(I)V"}, {"nme": "ensureNamespacesCapacity", "acc": 2, "dsc": "(I)V"}, {"nme": "fastHash", "acc": 26, "dsc": "([CII)I"}, {"nme": "ensureEntityCapacity", "acc": 2, "dsc": "()V"}, {"nme": "reset", "acc": 2, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/EntityReplacementMap;)V"}, {"nme": "setupFromTemplate", "acc": 1, "dsc": "()V"}, {"nme": "setFeature", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)V", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getFeature", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "setProperty", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getProperty", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "setInput", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;)V", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "setInput", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getInputEncoding", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "defineEntityReplacementText", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getNamespaceCount", "acc": 1, "dsc": "(I)I", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getNamespacePrefix", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getNamespaceUri", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getNamespace", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON>h", "acc": 1, "dsc": "()I"}, {"nme": "findFragment", "acc": 10, "dsc": "(I[CII)I"}, {"nme": "getPositionDescription", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLineNumber", "acc": 1, "dsc": "()I"}, {"nme": "getColumnNumber", "acc": 1, "dsc": "()I"}, {"nme": "isWhitespace", "acc": 1, "dsc": "()Z", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getText", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getTextCharacters", "acc": 1, "dsc": "([I)[C"}, {"nme": "getNamespace", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getPrefix", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isEmptyElementTag", "acc": 1, "dsc": "()Z", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getAttributeCount", "acc": 1, "dsc": "()I"}, {"nme": "getAttributeNamespace", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getAttributeName", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getAttributePrefix", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getAttributeType", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isAttributeDefault", "acc": 1, "dsc": "(I)Z"}, {"nme": "getAttributeValue", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getAttributeValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getEventType", "acc": 1, "dsc": "()I", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "require", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "skipSubTree", "acc": 1, "dsc": "()V", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "nextText", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "nextTag", "acc": 1, "dsc": "()I", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "next", "acc": 1, "dsc": "()I", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "nextToken", "acc": 1, "dsc": "()I", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "nextImpl", "acc": 2, "dsc": "()I", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "parseProlog", "acc": 2, "dsc": "()I", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "parseEpilog", "acc": 2, "dsc": "()I", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "parseEndTag", "acc": 1, "dsc": "()I", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "parseStartTag", "acc": 1, "dsc": "()I", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "parseAttribute", "acc": 2, "dsc": "()C", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "parseCharOrPredefinedEntityRef", "acc": 2, "dsc": "()I", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "parseEntityRefInDocDecl", "acc": 2, "dsc": "()V", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "parseEntityRef", "acc": 2, "dsc": "()V", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "isValidCodePoint", "acc": 10, "dsc": "(I)Z"}, {"nme": "lookuEntityReplacement", "acc": 2, "dsc": "(I)[C"}, {"nme": "parseComment", "acc": 2, "dsc": "()V", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "parsePI", "acc": 2, "dsc": "()Z", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "parseXmlDecl", "acc": 2, "dsc": "(C)V", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "parseXmlDeclWithVersion", "acc": 2, "dsc": "(II)V", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "parseDocdecl", "acc": 2, "dsc": "()V", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "extractEntityRefInDocDecl", "acc": 2, "dsc": "()V", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "extractEntityRef", "acc": 2, "dsc": "()V", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "parseCDSect", "acc": 2, "dsc": "(Z)V", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 2, "dsc": "()V", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "more", "acc": 2, "dsc": "()C", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "ensurePC", "acc": 2, "dsc": "(I)V"}, {"nme": "joinPC", "acc": 2, "dsc": "()V"}, {"nme": "requireInput", "acc": 2, "dsc": "(C[C)C", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "skipS", "acc": 2, "dsc": "(C)C", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "setName", "acc": 10, "dsc": "(C)V"}, {"nme": "setNameStart", "acc": 10, "dsc": "(C)V"}, {"nme": "isNameStartChar", "acc": 10, "dsc": "(C)Z"}, {"nme": "isNameChar", "acc": 10, "dsc": "(C)Z"}, {"nme": "isS", "acc": 10, "dsc": "(C)Z"}, {"nme": "printable", "acc": 10, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "printable", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "XML_URI", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "http://www.w3.org/XML/1998/namespace"}, {"acc": 26, "nme": "XMLNS_URI", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "http://www.w3.org/2000/xmlns/"}, {"acc": 26, "nme": "FEATURE_XML_ROUNDTRIP", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "http://xmlpull.org/v1/doc/features.html#xml-roundtrip"}, {"acc": 26, "nme": "FEATURE_NAMES_INTERNED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "http://xmlpull.org/v1/doc/features.html#names-interned"}, {"acc": 26, "nme": "PROPERTY_XMLDECL_VERSION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "http://xmlpull.org/v1/doc/properties.html#xmldecl-version"}, {"acc": 26, "nme": "PROPERTY_XMLDECL_STANDALONE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "http://xmlpull.org/v1/doc/properties.html#xmldecl-standalone"}, {"acc": 26, "nme": "PROPERTY_XMLDECL_CONTENT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "http://xmlpull.org/v1/doc/properties.html#xmldecl-content"}, {"acc": 26, "nme": "PROPERTY_LOCATION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "http://xmlpull.org/v1/doc/properties.html#location"}, {"acc": 2, "nme": "allStringsInterned", "dsc": "Z"}, {"acc": 26, "nme": "TRACE_SIZING", "dsc": "Z", "val": 0}, {"acc": 2, "nme": "processNamespaces", "dsc": "Z"}, {"acc": 2, "nme": "roundtripSupported", "dsc": "Z"}, {"acc": 2, "nme": "location", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "lineNumber", "dsc": "I"}, {"acc": 2, "nme": "columnNumber", "dsc": "I"}, {"acc": 2, "nme": "seenRoot", "dsc": "Z"}, {"acc": 2, "nme": "reachedEnd", "dsc": "Z"}, {"acc": 2, "nme": "eventType", "dsc": "I"}, {"acc": 2, "nme": "emptyElementTag", "dsc": "Z"}, {"acc": 2, "nme": "depth", "dsc": "I"}, {"acc": 2, "nme": "elRawName", "dsc": "[[C"}, {"acc": 2, "nme": "elRawNameEnd", "dsc": "[I"}, {"acc": 2, "nme": "elRawNameLine", "dsc": "[I"}, {"acc": 2, "nme": "el<PERSON>ame", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "elPrefix", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "<PERSON><PERSON><PERSON>", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "elNamespaceCount", "dsc": "[I"}, {"acc": 2, "nme": "fileEncoding", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "attributeCount", "dsc": "I"}, {"acc": 2, "nme": "attributeName", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "attributeNameHash", "dsc": "[I"}, {"acc": 2, "nme": "attributePrefix", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "<PERSON><PERSON><PERSON>", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "attributeValue", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "namespaceEnd", "dsc": "I"}, {"acc": 2, "nme": "namespacePrefix", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "namespacePrefixHash", "dsc": "[I"}, {"acc": 2, "nme": "namespaceUri", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "entityEnd", "dsc": "I"}, {"acc": 2, "nme": "entityName", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "entityNameBuf", "dsc": "[[C"}, {"acc": 2, "nme": "entityReplacement", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "entityReplacementBuf", "dsc": "[[C"}, {"acc": 2, "nme": "entityNameHash", "dsc": "[I"}, {"acc": 18, "nme": "replacementMapTemplate", "dsc": "Lorg/codehaus/plexus/util/xml/pull/EntityReplacementMap;"}, {"acc": 26, "nme": "READ_CHUNK_SIZE", "dsc": "I", "val": 8192}, {"acc": 2, "nme": "reader", "dsc": "<PERSON><PERSON><PERSON>/io/Reader;"}, {"acc": 2, "nme": "inputEncoding", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "bufLoadFactor", "dsc": "I"}, {"acc": 2, "nme": "bufferLoadFactor", "dsc": "F"}, {"acc": 2, "nme": "buf", "dsc": "[C"}, {"acc": 2, "nme": "bufSoftLimit", "dsc": "I"}, {"acc": 2, "nme": "preventBufferCompaction", "dsc": "Z"}, {"acc": 2, "nme": "bufAbsoluteStart", "dsc": "I"}, {"acc": 2, "nme": "bufStart", "dsc": "I"}, {"acc": 2, "nme": "bufEnd", "dsc": "I"}, {"acc": 2, "nme": "pos", "dsc": "I"}, {"acc": 2, "nme": "posStart", "dsc": "I"}, {"acc": 2, "nme": "posEnd", "dsc": "I"}, {"acc": 2, "nme": "pc", "dsc": "[C"}, {"acc": 2, "nme": "pcStart", "dsc": "I"}, {"acc": 2, "nme": "pcEnd", "dsc": "I"}, {"acc": 2, "nme": "usePC", "dsc": "Z"}, {"acc": 2, "nme": "seenStartTag", "dsc": "Z"}, {"acc": 2, "nme": "seenEndTag", "dsc": "Z"}, {"acc": 2, "nme": "pastEndTag", "dsc": "Z"}, {"acc": 2, "nme": "seenAmpersand", "dsc": "Z"}, {"acc": 2, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "Z"}, {"acc": 2, "nme": "seenDocdecl", "dsc": "Z"}, {"acc": 2, "nme": "tokenize", "dsc": "Z"}, {"acc": 2, "nme": "text", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "entityRefName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "xmlDeclVersion", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "xmlDeclStandalone", "dsc": "<PERSON><PERSON><PERSON>/lang/Boolean;"}, {"acc": 2, "nme": "xmlDeclContent", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "BUF_NOT_RESOLVED", "dsc": "[C"}, {"acc": 26, "nme": "BUF_LT", "dsc": "[C"}, {"acc": 26, "nme": "BUF_AMP", "dsc": "[C"}, {"acc": 26, "nme": "BUF_GT", "dsc": "[C"}, {"acc": 26, "nme": "BUF_APO", "dsc": "[C"}, {"acc": 26, "nme": "BUF_QUOT", "dsc": "[C"}, {"acc": 2, "nme": "resolvedEntityRefCharBuf", "dsc": "[C"}, {"acc": 26, "nme": "VERSION", "dsc": "[C"}, {"acc": 26, "nme": "NCODING", "dsc": "[C"}, {"acc": 26, "nme": "TANDALONE", "dsc": "[C"}, {"acc": 26, "nme": "YES", "dsc": "[C"}, {"acc": 26, "nme": "NO", "dsc": "[C"}, {"acc": 26, "nme": "LOOKUP_MAX", "dsc": "I", "val": 1024}, {"acc": 26, "nme": "LOOKUP_MAX_CHAR", "dsc": "C", "val": 1024}, {"acc": 26, "nme": "lookupNameStartChar", "dsc": "[Z"}, {"acc": 26, "nme": "lookupNameChar", "dsc": "[Z"}]}, "org/codehaus/plexus/util/cli/CommandLineException.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/cli/CommandLineException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}], "flds": []}, "org/codehaus/plexus/util/xml/Xpp3DomBuilder.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/xml/Xpp3DomBuilder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "build", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;)Lorg/codehaus/plexus/util/xml/Xpp3Dom;", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "build", "acc": 9, "dsc": "(Ljava/io/Reader;Lorg/codehaus/plexus/util/xml/Xpp3DomBuilder$InputLocationBuilder;)Lorg/codehaus/plexus/util/xml/Xpp3Dom;", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "build", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;Lja<PERSON>/lang/String;)Lorg/codehaus/plexus/util/xml/Xpp3Dom;", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "build", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;Lja<PERSON>/lang/String;Z)Lorg/codehaus/plexus/util/xml/Xpp3Dom;", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "build", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;Z)Lorg/codehaus/plexus/util/xml/Xpp3Dom;", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "build", "acc": 9, "dsc": "(Ljava/io/Reader;ZLorg/codehaus/plexus/util/xml/Xpp3DomBuilder$InputLocationBuilder;)Lorg/codehaus/plexus/util/xml/Xpp3Dom;", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "build", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;)Lorg/codehaus/plexus/util/xml/Xpp3Dom;", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "build", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/codehaus/plexus/util/xml/Xpp3Dom;", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "build", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/codehaus/plexus/util/xml/Xpp3DomBuilder$InputLocationBuilder;)Lorg/codehaus/plexus/util/xml/Xpp3Dom;", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}], "flds": [{"acc": 26, "nme": "DEFAULT_TRIM", "dsc": "Z", "val": 1}]}, "org/codehaus/plexus/util/MatchPattern.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/MatchPattern", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "matchPath", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)Z"}, {"nme": "matchPath", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[[CZ)Z"}, {"nme": "matchPatternStart", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)Z"}, {"nme": "getTokenizedPathString", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getTokenizedPathChars", "acc": 1, "dsc": "()[[C"}, {"nme": "startsWith", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "tokenizePathToString", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)[Ljava/lang/String;"}, {"nme": "tokenizePathToCharArray", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)[[C"}, {"nme": "fromString", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/codehaus/plexus/util/MatchPattern;"}], "flds": [{"acc": 18, "nme": "source", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "regexPattern", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "separator", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "tokenized", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "tokenizedChar", "dsc": "[[C"}]}, "org/codehaus/plexus/util/cli/CommandLineCallable.class": {"ver": 52, "acc": 1537, "nme": "org/codehaus/plexus/util/cli/CommandLineCallable", "super": "java/lang/Object", "mthds": [{"nme": "call", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/Integer;", "exs": ["org/codehaus/plexus/util/cli/CommandLineException"]}, {"nme": "call", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}], "flds": []}, "org/codehaus/plexus/util/cli/CommandLineUtils.class": {"ver": 52, "acc": 1057, "nme": "org/codehaus/plexus/util/cli/CommandLineUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "executeCommandLine", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/cli/Commandline;Lorg/codehaus/plexus/util/cli/StreamConsumer;Lorg/codehaus/plexus/util/cli/StreamConsumer;)I", "exs": ["org/codehaus/plexus/util/cli/CommandLineException"]}, {"nme": "executeCommandLine", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/cli/Commandline;Lorg/codehaus/plexus/util/cli/StreamConsumer;Lorg/codehaus/plexus/util/cli/StreamConsumer;I)I", "exs": ["org/codehaus/plexus/util/cli/CommandLineException"]}, {"nme": "executeCommandLine", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/cli/Commandline;<PERSON>java/io/InputStream;Lorg/codehaus/plexus/util/cli/StreamConsumer;Lorg/codehaus/plexus/util/cli/StreamConsumer;)I", "exs": ["org/codehaus/plexus/util/cli/CommandLineException"]}, {"nme": "executeCommandLine", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/cli/Commandline;<PERSON>java/io/InputStream;Lorg/codehaus/plexus/util/cli/StreamConsumer;Lorg/codehaus/plexus/util/cli/StreamConsumer;I)I", "exs": ["org/codehaus/plexus/util/cli/CommandLineException"]}, {"nme": "executeCommandLineAsCallable", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/cli/Commandline;<PERSON>ja<PERSON>/io/InputStream;Lorg/codehaus/plexus/util/cli/StreamConsumer;Lorg/codehaus/plexus/util/cli/StreamConsumer;I)Lorg/codehaus/plexus/util/cli/CommandLineCallable;", "exs": ["org/codehaus/plexus/util/cli/CommandLineException"]}, {"nme": "handleException", "acc": 10, "dsc": "(Lorg/codehaus/plexus/util/cli/StreamPumper;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/codehaus/plexus/util/cli/CommandLineException"]}, {"nme": "handleException", "acc": 10, "dsc": "(Lorg/codehaus/plexus/util/cli/StreamFeeder;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/codehaus/plexus/util/cli/CommandLineException"]}, {"nme": "getSystemEnvVars", "acc": 9, "dsc": "()Ljava/util/Properties;"}, {"nme": "getSystemEnvVars", "acc": 9, "dsc": "(Z)Ljava/util/Properties;"}, {"nme": "isAlive", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Process;)Z"}, {"nme": "translateCommandline", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[Lja<PERSON>/lang/String;", "exs": ["java/lang/Exception"]}, {"nme": "quote", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "exs": ["org/codehaus/plexus/util/cli/CommandLineException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "quote", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)Ljava/lang/String;", "exs": ["org/codehaus/plexus/util/cli/CommandLineException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "quote", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;ZZZ)Ljava/lang/String;", "exs": ["org/codehaus/plexus/util/cli/CommandLineException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "toString", "acc": 9, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "access$000", "acc": 4104, "dsc": "(Lorg/codehaus/plexus/util/cli/StreamFeeder;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/codehaus/plexus/util/cli/CommandLineException"]}, {"nme": "access$100", "acc": 4104, "dsc": "(Lorg/codehaus/plexus/util/cli/StreamPumper;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["org/codehaus/plexus/util/cli/CommandLineException"]}], "flds": [{"acc": 26, "nme": "MILLIS_PER_SECOND", "dsc": "J", "val": 1000}, {"acc": 26, "nme": "NANOS_PER_SECOND", "dsc": "J", "val": 1000000000}]}, "org/codehaus/plexus/util/cli/AbstractStreamHandler.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/cli/AbstractStreamHandler", "super": "java/lang/Thread", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "isDone", "acc": 1, "dsc": "()Z"}, {"nme": "waitUntilDone", "acc": 33, "dsc": "()V", "exs": ["java/lang/InterruptedException"]}, {"nme": "isDisabled", "acc": 4, "dsc": "()Z"}, {"nme": "disable", "acc": 1, "dsc": "()V"}, {"nme": "setDone", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "done", "dsc": "Z"}, {"acc": 66, "nme": "disabled", "dsc": "Z"}]}, "org/codehaus/plexus/util/dag/TopologicalSorter.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/dag/TopologicalSorter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "sort", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/dag/DAG;)Ljava/util/List;", "sig": "(Lorg/codehaus/plexus/util/dag/DAG;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "sort", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/dag/Vertex;)Ljava/util/List;", "sig": "(Lorg/codehaus/plexus/util/dag/Vertex;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "dfs", "acc": 10, "dsc": "(Lorg/codehaus/plexus/util/dag/DAG;)Ljava/util/List;", "sig": "(Lorg/codehaus/plexus/util/dag/DAG;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "isNotVisited", "acc": 10, "dsc": "(Lorg/codehaus/plexus/util/dag/Vertex;Ljava/util/Map;)Z", "sig": "(Lorg/codehaus/plexus/util/dag/Vertex;Ljava/util/Map<Lorg/codehaus/plexus/util/dag/Vertex;Ljava/lang/Integer;>;)Z"}, {"nme": "dfsVisit", "acc": 10, "dsc": "(Lorg/codehaus/plexus/util/dag/Vertex;Ljava/util/Map;Ljava/util/List;)V", "sig": "(Lorg/codehaus/plexus/util/dag/Vertex;Ljava/util/Map<Lorg/codehaus/plexus/util/dag/Vertex;Ljava/lang/Integer;>;Ljava/util/List<Ljava/lang/String;>;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "NOT_VISITED", "dsc": "<PERSON><PERSON><PERSON>/lang/Integer;"}, {"acc": 26, "nme": "VISITING", "dsc": "<PERSON><PERSON><PERSON>/lang/Integer;"}, {"acc": 26, "nme": "VISITED", "dsc": "<PERSON><PERSON><PERSON>/lang/Integer;"}]}, "org/codehaus/plexus/util/FileUtils.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/FileUtils", "super": "org/codehaus/plexus/util/BaseFileUtils", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getDefaultExcludes", "acc": 9, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDefaultExcludesAsList", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "getDefaultExcludesAsString", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "byteCountToDisplaySize", "acc": 9, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "dirname", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "filename", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "basename", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "basename", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "extension", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "fileExists", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "fileRead", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "fileRead", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "fileRead", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)Ljava/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "fileRead", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/File;Ljava/lang/String;)Ljava/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "fileAppend", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "fileAppend", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "fileAppend", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/nio/file/Path;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "fileWrite", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "fileWrite", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "fileWrite", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/File;Ljava/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "fileWrite", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/File;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "fileDelete", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "waitFor", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Z"}, {"nme": "waitFor", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/File;I)Z"}, {"nme": "getFile", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/io/File;"}, {"nme": "getFilesFromExtension", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[<PERSON>ja<PERSON>/lang/String;)[Ljava/lang/String;"}, {"nme": "blendFilesToVector", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/List;[Lja<PERSON>/lang/String;)Ljava/util/List;", "sig": "(Ljava/util/List<Ljava/lang/String;>;[Ljava/lang/String;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "isValidFile", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;)Z"}, {"nme": "mkdir", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "contentEquals", "acc": 9, "dsc": "(Lja<PERSON>/io/File;Ljava/io/File;)Z", "exs": ["java/io/IOException"]}, {"nme": "toFile", "acc": 9, "dsc": "(Ljava/net/URL;)Ljava/io/File;"}, {"nme": "toURLs", "acc": 9, "dsc": "([Ljava/io/File;)[Ljava/net/URL;", "exs": ["java/io/IOException"]}, {"nme": "removeExtension", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getExtension", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "remove<PERSON>ath", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "remove<PERSON>ath", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;C)Ljava/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;C)Ljava/lang/String;"}, {"nme": "copyFileToDirectory", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "copyFileToDirectoryIfModified", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "copyFileToDirectory", "acc": 9, "dsc": "(Ljava/io/File;Ljava/io/File;)V", "exs": ["java/io/IOException"]}, {"nme": "copyFileToDirectoryIfModified", "acc": 9, "dsc": "(Ljava/io/File;Ljava/io/File;)V", "exs": ["java/io/IOException"]}, {"nme": "mkDirs", "acc": 9, "dsc": "(Lja<PERSON>/io/File;[Ljava/lang/String;Ljava/io/File;)V", "exs": ["java/io/IOException"]}, {"nme": "copyFile", "acc": 9, "dsc": "(Ljava/io/File;Ljava/io/File;)V", "exs": ["java/io/IOException"]}, {"nme": "doCopyFile", "acc": 10, "dsc": "(Ljava/io/File;Ljava/io/File;)V", "exs": ["java/io/IOException"]}, {"nme": "doCopyFileUsingNewIO", "acc": 10, "dsc": "(Ljava/io/File;Ljava/io/File;)V", "exs": ["java/io/IOException"]}, {"nme": "linkFile", "acc": 9, "dsc": "(Ljava/io/File;Ljava/io/File;)V", "exs": ["java/io/IOException"]}, {"nme": "copyFileIfModified", "acc": 9, "dsc": "(Lja<PERSON>/io/File;Ljava/io/File;)Z", "exs": ["java/io/IOException"]}, {"nme": "copyURLToFile", "acc": 9, "dsc": "(Ljava/net/URL;Ljava/io/File;)V", "exs": ["java/io/IOException"]}, {"nme": "copyStreamToFile", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/io/InputStreamFacade;Ljava/io/File;)V", "exs": ["java/io/IOException"]}, {"nme": "checkCanWrite", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V", "exs": ["java/io/IOException"]}, {"nme": "mkdirsFor", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V"}, {"nme": "normalize", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "catPath", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "resolveFile", "acc": 9, "dsc": "(Lja<PERSON>/io/File;Lja<PERSON>/lang/String;)Ljava/io/File;"}, {"nme": "forceDelete", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "forceDelete", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V", "exs": ["java/io/IOException"]}, {"nme": "deleteFile", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)Z", "exs": ["java/io/IOException"]}, {"nme": "forceDeleteOnExit", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V", "exs": ["java/io/IOException"]}, {"nme": "deleteDirectoryOnExit", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V", "exs": ["java/io/IOException"]}, {"nme": "cleanDirectoryOnExit", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V", "exs": ["java/io/IOException"]}, {"nme": "forceMkdir", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V", "exs": ["java/io/IOException"]}, {"nme": "deleteDirectory", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "deleteDirectory", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V", "exs": ["java/io/IOException"]}, {"nme": "cleanDirectory", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "cleanDirectory", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V", "exs": ["java/io/IOException"]}, {"nme": "sizeOfDirectory", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)J"}, {"nme": "sizeOfDirectory", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)J"}, {"nme": "getFiles", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/File;L<PERSON><PERSON>/lang/String;Lja<PERSON>/lang/String;)Ljava/util/List;", "sig": "(Ljava/io/File;Ljava/lang/String;Ljava/lang/String;)Ljava/util/List<Ljava/io/File;>;", "exs": ["java/io/IOException"]}, {"nme": "getFiles", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/File;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Z)Ljava/util/List;", "sig": "(Ljava/io/File;Ljava/lang/String;Ljava/lang/String;Z)Ljava/util/List<Ljava/io/File;>;", "exs": ["java/io/IOException"]}, {"nme": "getFileNames", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/File;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Z)Ljava/util/List;", "sig": "(Lja<PERSON>/io/File;Ljava/lang/String;Ljava/lang/String;Z)Ljava/util/List<Ljava/lang/String;>;", "exs": ["java/io/IOException"]}, {"nme": "getFileNames", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/File;<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;ZZ)Ljava/util/List;", "sig": "(Ljava/io/File;Ljava/lang/String;Ljava/lang/String;ZZ)Ljava/util/List<Ljava/lang/String;>;", "exs": ["java/io/IOException"]}, {"nme": "getDirectoryNames", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/File;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Z)Ljava/util/List;", "sig": "(Lja<PERSON>/io/File;Ljava/lang/String;Ljava/lang/String;Z)Ljava/util/List<Ljava/lang/String;>;", "exs": ["java/io/IOException"]}, {"nme": "getDirectoryNames", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/File;<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;ZZ)Ljava/util/List;", "sig": "(Ljava/io/File;Ljava/lang/String;Ljava/lang/String;ZZ)Ljava/util/List<Ljava/lang/String;>;", "exs": ["java/io/IOException"]}, {"nme": "getFileAndDirectoryNames", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/File;<PERSON><PERSON><PERSON>/lang/String;<PERSON>java/lang/String;ZZZZ)Ljava/util/List;", "sig": "(Ljava/io/File;Ljava/lang/String;Ljava/lang/String;ZZZZ)Ljava/util/List<Ljava/lang/String;>;", "exs": ["java/io/IOException"]}, {"nme": "copyDirectory", "acc": 9, "dsc": "(Ljava/io/File;Ljava/io/File;)V", "exs": ["java/io/IOException"]}, {"nme": "copyDirectory", "acc": 9, "dsc": "(L<PERSON><PERSON>/io/File;Ljava/io/File;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "copyDirectoryLayout", "acc": 9, "dsc": "(Lja<PERSON>/io/File;Ljava/io/File;[Ljava/lang/String;[Ljava/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "copyDirectoryStructure", "acc": 9, "dsc": "(Ljava/io/File;Ljava/io/File;)V", "exs": ["java/io/IOException"]}, {"nme": "copyDirectoryStructureIfModified", "acc": 9, "dsc": "(Ljava/io/File;Ljava/io/File;)V", "exs": ["java/io/IOException"]}, {"nme": "copyDirectoryStructure", "acc": 10, "dsc": "(Lja<PERSON>/io/File;Ljava/io/File;Ljava/io/File;Z)V", "exs": ["java/io/IOException"]}, {"nme": "rename", "acc": 9, "dsc": "(Ljava/io/File;Ljava/io/File;)V", "exs": ["java/io/IOException"]}, {"nme": "createTempFile", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Ljava/io/File;)Ljava/io/File;"}, {"nme": "copyFile", "acc": 9, "dsc": "(Ljava/io/File;Ljava/io/File;Ljava/lang/String;[Lorg/codehaus/plexus/util/FileUtils$FilterWrapper;)V", "exs": ["java/io/IOException"]}, {"nme": "copyFile", "acc": 9, "dsc": "(Ljava/io/File;Ljava/io/File;Ljava/lang/String;[Lorg/codehaus/plexus/util/FileUtils$FilterWrapper;Z)V", "exs": ["java/io/IOException"]}, {"nme": "isSourceNewerThanDestination", "acc": 10, "dsc": "(Lja<PERSON>/io/File;Ljava/io/File;)Z"}, {"nme": "loadFile", "acc": 9, "dsc": "(L<PERSON><PERSON>/io/File;)Ljava/util/List;", "sig": "(Ljava/io/File;)Ljava/util/List<Ljava/lang/String;>;", "exs": ["java/io/IOException"]}, {"nme": "isValidWindowsFileName", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "ONE_KB", "dsc": "I", "val": 1024}, {"acc": 25, "nme": "ONE_MB", "dsc": "I", "val": 1048576}, {"acc": 25, "nme": "ONE_GB", "dsc": "I", "val": 1073741824}, {"acc": 9, "nme": "FS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "INVALID_CHARACTERS_FOR_WINDOWS_FILE_NAME", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/codehaus/plexus/util/xml/Xpp3Dom.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/xml/Xpp3Dom", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/codehaus/plexus/util/xml/Xpp3Dom;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/codehaus/plexus/util/xml/Xpp3Dom;Ljava/lang/String;)V"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getValue", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getAttributeNames", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getAttribute", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "removeAttribute", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "setAttribute", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(I)Lorg/codehaus/plexus/util/xml/Xpp3Dom;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/codehaus/plexus/util/xml/Xpp3Dom;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Lorg/codehaus/plexus/util/xml/Xpp3Dom;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()[Lorg/codehaus/plexus/util/xml/Xpp3Dom;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[Lorg/codehaus/plexus/util/xml/Xpp3Dom;"}, {"nme": "getChildrenAsList", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/List<Lorg/codehaus/plexus/util/xml/Xpp3Dom;>;"}, {"nme": "get<PERSON><PERSON>d<PERSON>ount", "acc": 1, "dsc": "()I"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(I)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Lorg/codehaus/plexus/util/xml/Xpp3Dom;)V"}, {"nme": "getParent", "acc": 1, "dsc": "()Lorg/codehaus/plexus/util/xml/Xpp3Dom;"}, {"nme": "setParent", "acc": 1, "dsc": "(Lorg/codehaus/plexus/util/xml/Xpp3Dom;)V"}, {"nme": "getInputLocation", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "setInputLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "writeToSerializer", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "mergeIntoXpp3Dom", "acc": 10, "dsc": "(Lorg/codehaus/plexus/util/xml/Xpp3Dom;Lorg/codehaus/plexus/util/xml/Xpp3Dom;<PERSON><PERSON><PERSON>/lang/<PERSON>;)V"}, {"nme": "mergeXpp3Dom", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/xml/Xpp3Dom;Lorg/codehaus/plexus/util/xml/Xpp3Dom;<PERSON><PERSON><PERSON>/lang/<PERSON>;)Lorg/codehaus/plexus/util/xml/Xpp3Dom;"}, {"nme": "mergeXpp3Dom", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/xml/Xpp3Dom;Lorg/codehaus/plexus/util/xml/Xpp3Dom;)Lorg/codehaus/plexus/util/xml/Xpp3Dom;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toUnescapedString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isNotEmpty", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isEmpty", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 2567894443061173996}, {"acc": 4, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4, "nme": "value", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4, "nme": "attributes", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"acc": 20, "nme": "childList", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/codehaus/plexus/util/xml/Xpp3Dom;>;"}, {"acc": 4, "nme": "parent", "dsc": "Lorg/codehaus/plexus/util/xml/Xpp3Dom;"}, {"acc": 4, "nme": "inputLocation", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 26, "nme": "EMPTY_STRING_ARRAY", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "EMPTY_DOM_ARRAY", "dsc": "[Lorg/codehaus/plexus/util/xml/Xpp3Dom;"}, {"acc": 25, "nme": "CHILDREN_COMBINATION_MODE_ATTRIBUTE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "combine.children"}, {"acc": 25, "nme": "CHILDREN_COMBINATION_MERGE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "merge"}, {"acc": 25, "nme": "CHILDREN_COMBINATION_APPEND", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "append"}, {"acc": 25, "nme": "DEFAULT_CHILDREN_COMBINATION_MODE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "merge"}, {"acc": 25, "nme": "SELF_COMBINATION_MODE_ATTRIBUTE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "combine.self"}, {"acc": 25, "nme": "SELF_COMBINATION_OVERRIDE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "override"}, {"acc": 25, "nme": "SELF_COMBINATION_MERGE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "merge"}, {"acc": 25, "nme": "SELF_COMBINATION_REMOVE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "remove"}, {"acc": 25, "nme": "DEFAULT_SELF_COMBINATION_MODE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "merge"}]}, "org/codehaus/plexus/util/SweeperPool$Sweeper.class": {"ver": 52, "acc": 32, "nme": "org/codehaus/plexus/util/SweeperPool$Sweeper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/codehaus/plexus/util/SweeperPool;I)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}, {"nme": "start", "acc": 1, "dsc": "()V"}, {"nme": "stop", "acc": 33, "dsc": "()V"}, {"nme": "join", "acc": 0, "dsc": "()V", "exs": ["java/lang/InterruptedException"]}, {"nme": "hasStopped", "acc": 0, "dsc": "()Z"}, {"nme": "debug", "acc": 18, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "runSweep", "acc": 2, "dsc": "()V"}], "flds": [{"acc": 146, "nme": "pool", "dsc": "Lorg/codehaus/plexus/util/SweeperPool;"}, {"acc": 130, "nme": "service", "dsc": "Z"}, {"acc": 146, "nme": "sweepInterval", "dsc": "I"}, {"acc": 130, "nme": "t", "dsc": "<PERSON><PERSON><PERSON>/lang/Thread;"}]}, "org/codehaus/plexus/util/FastMap$EntryImpl.class": {"ver": 52, "acc": 48, "nme": "org/codehaus/plexus/util/FastMap$EntryImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TK;"}, {"nme": "getValue", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TV;"}, {"nme": "setValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(TV;)TV;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "access$000", "acc": 4104, "dsc": "(Lorg/codehaus/plexus/util/FastMap$EntryImpl;)Ljava/lang/Object;"}, {"nme": "access$100", "acc": 4104, "dsc": "(Lorg/codehaus/plexus/util/FastMap$EntryImpl;)Lorg/codehaus/plexus/util/FastMap$EntryImpl;"}, {"nme": "access$200", "acc": 4104, "dsc": "(Lorg/codehaus/plexus/util/FastMap$EntryImpl;)Ljava/lang/Object;"}, {"nme": "access$300", "acc": 4104, "dsc": "(Lorg/codehaus/plexus/util/FastMap$EntryImpl;)Lorg/codehaus/plexus/util/FastMap$EntryImpl;"}, {"nme": "access$202", "acc": 4104, "dsc": "(Lorg/codehaus/plexus/util/FastMap$EntryImpl;Ljava/lang/Object;)Ljava/lang/Object;"}, {"nme": "access$002", "acc": 4104, "dsc": "(Lorg/codehaus/plexus/util/FastMap$EntryImpl;Ljava/lang/Object;)Ljava/lang/Object;"}, {"nme": "access$402", "acc": 4104, "dsc": "(Lorg/codehaus/plexus/util/FastMap$EntryImpl;Lorg/codehaus/plexus/util/FastMap$EntryImpl;)Lorg/codehaus/plexus/util/FastMap$EntryImpl;"}, {"nme": "access$102", "acc": 4104, "dsc": "(Lorg/codehaus/plexus/util/FastMap$EntryImpl;Lorg/codehaus/plexus/util/FastMap$EntryImpl;)Lorg/codehaus/plexus/util/FastMap$EntryImpl;"}, {"nme": "access$500", "acc": 4104, "dsc": "(Lorg/codehaus/plexus/util/FastMap$EntryImpl;)Lorg/codehaus/plexus/util/FastMap$EntryImpl;"}, {"nme": "access$600", "acc": 4104, "dsc": "(Lorg/codehaus/plexus/util/FastMap$EntryImpl;)I"}, {"nme": "access$502", "acc": 4104, "dsc": "(Lorg/codehaus/plexus/util/FastMap$EntryImpl;Lorg/codehaus/plexus/util/FastMap$EntryImpl;)Lorg/codehaus/plexus/util/FastMap$EntryImpl;"}, {"nme": "access$302", "acc": 4104, "dsc": "(Lorg/codehaus/plexus/util/FastMap$EntryImpl;Lorg/codehaus/plexus/util/FastMap$EntryImpl;)Lorg/codehaus/plexus/util/FastMap$EntryImpl;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/codehaus/plexus/util/FastMap$1;)V"}, {"nme": "access$602", "acc": 4104, "dsc": "(Lorg/codehaus/plexus/util/FastMap$EntryImpl;I)I"}, {"nme": "access$400", "acc": 4104, "dsc": "(Lorg/codehaus/plexus/util/FastMap$EntryImpl;)Lorg/codehaus/plexus/util/FastMap$EntryImpl;"}], "flds": [{"acc": 2, "nme": "_key", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;", "sig": "TK;"}, {"acc": 2, "nme": "_value", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;", "sig": "TV;"}, {"acc": 2, "nme": "_index", "dsc": "I"}, {"acc": 2, "nme": "_previous", "dsc": "Lorg/codehaus/plexus/util/FastMap$EntryImpl;"}, {"acc": 2, "nme": "_next", "dsc": "Lorg/codehaus/plexus/util/FastMap$EntryImpl;"}, {"acc": 2, "nme": "_before", "dsc": "Lorg/codehaus/plexus/util/FastMap$EntryImpl;"}, {"acc": 2, "nme": "_after", "dsc": "Lorg/codehaus/plexus/util/FastMap$EntryImpl;"}]}, "org/codehaus/plexus/util/xml/pull/XmlPullParserException.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/xml/pull/XmlPullParserException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;<PERSON>ja<PERSON>/lang/Throwable;)V"}, {"nme": "getDetail", "acc": 131073, "dsc": "()<PERSON><PERSON><PERSON>/lang/Throwable;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getLineNumber", "acc": 1, "dsc": "()I"}, {"nme": "getColumnNumber", "acc": 1, "dsc": "()I"}, {"nme": "printStackTrace", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 131076, "nme": "detail", "dsc": "<PERSON><PERSON><PERSON>/lang/Throwable;"}, {"acc": 4, "nme": "row", "dsc": "I"}, {"acc": 4, "nme": "column", "dsc": "I"}]}, "org/codehaus/plexus/util/Base64.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/Base64", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "isBase64", "acc": 10, "dsc": "(B)Z"}, {"nme": "isArrayByteBase64", "acc": 9, "dsc": "([B)Z"}, {"nme": "encodeBase64", "acc": 9, "dsc": "([B)[B"}, {"nme": "encodeBase64Chunked", "acc": 9, "dsc": "([B)[B"}, {"nme": "decode", "acc": 1, "dsc": "([B)[B"}, {"nme": "encodeBase64", "acc": 9, "dsc": "([BZ)[B"}, {"nme": "decodeBase64", "acc": 9, "dsc": "([B)[B"}, {"nme": "discardWhitespace", "acc": 8, "dsc": "([B)[B"}, {"nme": "discardNonBase64", "acc": 8, "dsc": "([B)[B"}, {"nme": "encode", "acc": 1, "dsc": "([B)[B"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "CHUNK_SIZE", "dsc": "I", "val": 76}, {"acc": 24, "nme": "CHUNK_SEPARATOR", "dsc": "[B"}, {"acc": 24, "nme": "BASELENGTH", "dsc": "I", "val": 255}, {"acc": 24, "nme": "LOOKUPLENGTH", "dsc": "I", "val": 64}, {"acc": 24, "nme": "EIGHTBIT", "dsc": "I", "val": 8}, {"acc": 24, "nme": "SIXTEENBIT", "dsc": "I", "val": 16}, {"acc": 24, "nme": "TWENTYFOURBITGROUP", "dsc": "I", "val": 24}, {"acc": 24, "nme": "FOURBYTE", "dsc": "I", "val": 4}, {"acc": 24, "nme": "SIGN", "dsc": "I", "val": -128}, {"acc": 24, "nme": "PAD", "dsc": "B", "val": 61}, {"acc": 10, "nme": "base64Alphabet", "dsc": "[B"}, {"acc": 10, "nme": "lookUpBase64Alphabet", "dsc": "[B"}]}, "org/codehaus/plexus/util/xml/XMLWriter.class": {"ver": 52, "acc": 1537, "nme": "org/codehaus/plexus/util/xml/XMLWriter", "super": "java/lang/Object", "mthds": [{"nme": "startElement", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "addAttribute", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "writeText", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "writeMarkup", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "endElement", "acc": 1025, "dsc": "()V"}], "flds": []}, "org/codehaus/plexus/util/xml/XmlStreamWriter.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/xml/XmlStreamWriter", "super": "java/io/Writer", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/io/OutputStream;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V", "exs": ["java/io/IOException"]}, {"nme": "getEncoding", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "flush", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "detectEncoding", "acc": 2, "dsc": "([CII)V", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "([CII)V", "exs": ["java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "BUFFER_SIZE", "dsc": "I", "val": 4096}, {"acc": 2, "nme": "xmlPrologWriter", "dsc": "L<PERSON><PERSON>/io/StringWriter;"}, {"acc": 2, "nme": "out", "dsc": "Ljava/io/OutputStream;"}, {"acc": 2, "nme": "writer", "dsc": "<PERSON><PERSON><PERSON>/io/Writer;"}, {"acc": 2, "nme": "encoding", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 24, "nme": "ENCODING_PATTERN", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}]}, "org/codehaus/plexus/util/xml/XmlUtil.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/xml/XmlUtil", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "isXml", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)Z"}, {"nme": "prettyFormat", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;<PERSON><PERSON><PERSON>/io/Writer;)V", "exs": ["java/io/IOException"]}, {"nme": "prettyFormat", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;<PERSON><PERSON><PERSON>/io/Writer;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "prettyFormat", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;Ljava/io/OutputStream;)V", "exs": ["java/io/IOException"]}, {"nme": "prettyFormat", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;Ljava/io/OutputStream;<PERSON>java/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "prettyFormatInternal", "acc": 10, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Lorg/codehaus/plexus/util/xml/PrettyPrintXMLWriter;)V", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "DEFAULT_INDENTATION_SIZE", "dsc": "I", "val": 2}, {"acc": 25, "nme": "DEFAULT_LINE_SEPARATOR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/codehaus/plexus/util/AbstractScanner.class": {"ver": 52, "acc": 1057, "nme": "org/codehaus/plexus/util/AbstractScanner", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "setCaseSensitive", "acc": 1, "dsc": "(Z)V"}, {"nme": "matchPatternStart", "acc": 12, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "matchPatternStart", "acc": 12, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Z)Z"}, {"nme": "matchPath", "acc": 12, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "matchPath", "acc": 12, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Z)Z"}, {"nme": "match", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "match", "acc": 12, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Z)Z"}, {"nme": "setIncludes", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setExcludes", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "normalizePattern", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "isIncluded", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isIncluded", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;)Z"}, {"nme": "isIncluded", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[[C)Z"}, {"nme": "couldHoldIncluded", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isExcluded", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isExcluded", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;)Z"}, {"nme": "isExcluded", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[[C)Z"}, {"nme": "addDefaultExcludes", "acc": 1, "dsc": "()V"}, {"nme": "setupDefaultFilters", "acc": 4, "dsc": "()V"}, {"nme": "setupMatchPatterns", "acc": 4, "dsc": "()V"}, {"nme": "setFilenameComparator", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Comparator;)V", "sig": "(<PERSON><PERSON><PERSON>/util/Comparator<Ljava/lang/String;>;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "DEFAULTEXCLUDES", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4, "nme": "includes", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "includesPatterns", "dsc": "Lorg/codehaus/plexus/util/MatchPatterns;"}, {"acc": 4, "nme": "excludes", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "excludesPatterns", "dsc": "Lorg/codehaus/plexus/util/MatchPatterns;"}, {"acc": 4, "nme": "isCaseSensitive", "dsc": "Z"}, {"acc": 4, "nme": "filenameComparator", "dsc": "<PERSON><PERSON><PERSON>/util/Comparator;", "sig": "<PERSON><PERSON><PERSON>/util/Comparator<Ljava/lang/String;>;"}]}, "org/codehaus/plexus/util/cli/shell/CommandShell.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/cli/shell/CommandShell", "super": "org/codehaus/plexus/util/cli/shell/Shell", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "org/codehaus/plexus/util/io/RawInputStreamFacade.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/io/RawInputStreamFacade", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON>va/io/InputStream;)V"}, {"nme": "getInputStream", "acc": 1, "dsc": "()Ljava/io/InputStream;", "exs": ["java/io/IOException"]}], "flds": [{"acc": 16, "nme": "stream", "dsc": "Ljava/io/InputStream;"}]}, "org/codehaus/plexus/util/FastMap$KeySet.class": {"ver": 52, "acc": 32, "nme": "org/codehaus/plexus/util/FastMap$KeySet", "super": "java/util/AbstractSet", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/FastMap;)V"}, {"nme": "iterator", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Iterator;"}, {"nme": "size", "acc": 1, "dsc": "()I"}, {"nme": "contains", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "remove", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "clear", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/codehaus/plexus/util/FastMap;Lorg/codehaus/plexus/util/FastMap$1;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lorg/codehaus/plexus/util/FastMap;"}]}, "org/codehaus/plexus/util/SelectorUtils.class": {"ver": 52, "acc": 49, "nme": "org/codehaus/plexus/util/SelectorUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getInstance", "acc": 9, "dsc": "()Lorg/codehaus/plexus/util/SelectorUtils;"}, {"nme": "matchPatternStart", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "matchPatternStart", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Z)Z"}, {"nme": "isAntPrefixedPattern", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "matchAntPathPatternStart", "acc": 8, "dsc": "(Lorg/codehaus/plexus/util/MatchPattern;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Z)Z"}, {"nme": "matchAntPathPatternStart", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Z)Z"}, {"nme": "separatorPatternStartSlashMismatch", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "separatorPatternStartSlashMismatch", "acc": 10, "dsc": "(Lorg/codehaus/plexus/util/MatchPattern;<PERSON>ja<PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "matchAntPathPatternStart", "acc": 8, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Z)Z"}, {"nme": "matchPath", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "matchPath", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Z)Z"}, {"nme": "matchPath", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Z)Z"}, {"nme": "toOSRelatedPath", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "isRegexPrefixedPattern", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "matchAntPathPattern", "acc": 8, "dsc": "(Lorg/codehaus/plexus/util/MatchPattern;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Z)Z"}, {"nme": "matchAntPathPattern", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Z)Z"}, {"nme": "matchAntPathPattern", "acc": 8, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;Z)Z"}, {"nme": "matchAntPathPattern", "acc": 8, "dsc": "([[C[[CZ)Z"}, {"nme": "isDoubleStar", "acc": 10, "dsc": "([C)Z"}, {"nme": "match", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "match", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Z)Z"}, {"nme": "match", "acc": 9, "dsc": "([C[CZ)Z"}, {"nme": "equals", "acc": 10, "dsc": "(CCZ)Z"}, {"nme": "tokenizePathToString", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)[Ljava/lang/String;"}, {"nme": "isOutOfDate", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/File;Ljava/io/File;I)Z"}, {"nme": "removeWhitespace", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "PATTERN_HANDLER_PREFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "["}, {"acc": 25, "nme": "PATTERN_HANDLER_SUFFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "]"}, {"acc": 25, "nme": "REGEX_HANDLER_PREFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "%regex["}, {"acc": 25, "nme": "ANT_HANDLER_PREFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "%ant["}, {"acc": 10, "nme": "instance", "dsc": "Lorg/codehaus/plexus/util/SelectorUtils;"}]}, "org/codehaus/plexus/util/FastMap$KeySet$1.class": {"ver": 52, "acc": 32, "nme": "org/codehaus/plexus/util/FastMap$KeySet$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/codehaus/plexus/util/FastMap$KeySet;)V"}, {"nme": "remove", "acc": 1, "dsc": "()V"}, {"nme": "hasNext", "acc": 1, "dsc": "()Z"}, {"nme": "next", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 0, "nme": "after", "dsc": "Lorg/codehaus/plexus/util/FastMap$EntryImpl;"}, {"acc": 0, "nme": "before", "dsc": "Lorg/codehaus/plexus/util/FastMap$EntryImpl;"}, {"acc": 4112, "nme": "this$1", "dsc": "Lorg/codehaus/plexus/util/FastMap$KeySet;"}]}, "org/codehaus/plexus/util/introspection/MethodMap.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/introspection/MethodMap", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "add", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)V"}, {"nme": "get", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/List<Ljava/lang/reflect/Method;>;"}, {"nme": "find", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Lja<PERSON>/lang/Object;)Ljava/lang/reflect/Method;", "exs": ["org/codehaus/plexus/util/introspection/MethodMap$AmbiguousException"]}, {"nme": "getMostSpecific", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/List;[Ljava/lang/Class;)Ljava/lang/reflect/Method;", "sig": "(Lja<PERSON>/util/List<Ljava/lang/reflect/Method;>;[Ljava/lang/Class;)Ljava/lang/reflect/Method;", "exs": ["org/codehaus/plexus/util/introspection/MethodMap$AmbiguousException"]}, {"nme": "moreSpecific", "acc": 10, "dsc": "([<PERSON>ja<PERSON>/lang/Class;[Ljava/lang/Class;)I"}, {"nme": "getApplicables", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/List;[Lja<PERSON>/lang/Class;)Ljava/util/LinkedList;", "sig": "(Lja<PERSON>/util/List<Ljava/lang/reflect/Method;>;[Ljava/lang/Class;)Ljava/util/LinkedList<Ljava/lang/reflect/Method;>;"}, {"nme": "isApplicable", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;[Ljava/lang/Class;)Z"}, {"nme": "isMethodInvocationConvertible", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/Class;)Z"}, {"nme": "isStrictMethodInvocationConvertible", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/Class;)Z"}], "flds": [{"acc": 26, "nme": "MORE_SPECIFIC", "dsc": "I", "val": 0}, {"acc": 26, "nme": "LESS_SPECIFIC", "dsc": "I", "val": 1}, {"acc": 26, "nme": "INCOMPARABLE", "dsc": "I", "val": 2}, {"acc": 0, "nme": "methodByNameMap", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljava/lang/reflect/Method;>;>;"}]}, "org/codehaus/plexus/util/cli/DefaultConsumer.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/cli/DefaultConsumer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "consumeLine", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}], "flds": []}, "org/codehaus/plexus/util/introspection/ClassMap$1.class": {"ver": 52, "acc": 4128, "nme": "org/codehaus/plexus/util/introspection/ClassMap$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/codehaus/plexus/util/Scanner.class": {"ver": 52, "acc": 1537, "nme": "org/codehaus/plexus/util/Scanner", "super": "java/lang/Object", "mthds": [{"nme": "setIncludes", "acc": 1025, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setExcludes", "acc": 1025, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "addDefaultExcludes", "acc": 1025, "dsc": "()V"}, {"nme": "scan", "acc": 1025, "dsc": "()V"}, {"nme": "getIncludedFiles", "acc": 1025, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getIncludedDirectories", "acc": 1025, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getBasedir", "acc": 1025, "dsc": "()Ljava/io/File;"}, {"nme": "setFilenameComparator", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/Comparator;)V", "sig": "(<PERSON><PERSON><PERSON>/util/Comparator<Ljava/lang/String;>;)V"}], "flds": []}, "org/codehaus/plexus/util/cli/CommandLineTimeOutException.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/cli/CommandLineTimeOutException", "super": "org/codehaus/plexus/util/cli/CommandLineException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}], "flds": []}, "org/codehaus/plexus/util/dag/Vertex.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/dag/Vertex", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "addEdgeTo", "acc": 1, "dsc": "(Lorg/codehaus/plexus/util/dag/Vertex;)V"}, {"nme": "removeEdgeTo", "acc": 1, "dsc": "(Lorg/codehaus/plexus/util/dag/Vertex;)V"}, {"nme": "addEdgeFrom", "acc": 1, "dsc": "(Lorg/codehaus/plexus/util/dag/Vertex;)V"}, {"nme": "removeEdgeFrom", "acc": 1, "dsc": "(Lorg/codehaus/plexus/util/dag/Vertex;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/codehaus/plexus/util/dag/Vertex;>;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "getParents", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/codehaus/plexus/util/dag/Vertex;>;"}, {"nme": "getParentLabels", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Z"}, {"nme": "isRoot", "acc": 1, "dsc": "()Z"}, {"nme": "isConnected", "acc": 1, "dsc": "()Z"}, {"nme": "clone", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "label", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "children", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/codehaus/plexus/util/dag/Vertex;>;"}, {"acc": 0, "nme": "parents", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/codehaus/plexus/util/dag/Vertex;>;"}]}, "org/codehaus/plexus/util/io/CachingOutputStream.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/io/CachingOutputStream", "super": "java/io/OutputStream", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/nio/file/Path;I)V", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(I)V", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "([B)V", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "([BII)V", "exs": ["java/io/IOException"]}, {"nme": "flush", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "flushBuffer", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/nio/<PERSON>;)V", "exs": ["java/io/IOException"]}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "isModified", "acc": 1, "dsc": "()Z"}], "flds": [{"acc": 18, "nme": "path", "dsc": "Ljava/nio/file/Path;"}, {"acc": 2, "nme": "channel", "dsc": "Ljava/nio/channels/FileChannel;"}, {"acc": 2, "nme": "readBuffer", "dsc": "<PERSON><PERSON><PERSON>/nio/<PERSON>te<PERSON>er;"}, {"acc": 2, "nme": "writeBuffer", "dsc": "<PERSON><PERSON><PERSON>/nio/<PERSON>te<PERSON>er;"}, {"acc": 2, "nme": "modified", "dsc": "Z"}]}, "org/codehaus/plexus/util/ExceptionUtils.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/ExceptionUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "addCauseMethodName", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getCause", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)<PERSON><PERSON><PERSON>/lang/Throwable;"}, {"nme": "getCause", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;[<PERSON><PERSON><PERSON>/lang/String;)<PERSON>ja<PERSON>/lang/Throwable;"}, {"nme": "getRootCause", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)<PERSON><PERSON><PERSON>/lang/Throwable;"}, {"nme": "getCauseUsingWellKnownTypes", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)<PERSON><PERSON><PERSON>/lang/Throwable;"}, {"nme": "getCauseUsingMethodName", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;<PERSON><PERSON><PERSON>/lang/String;)<PERSON>ja<PERSON>/lang/Throwable;"}, {"nme": "getCauseUsingFieldName", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;<PERSON><PERSON><PERSON>/lang/String;)<PERSON>ja<PERSON>/lang/Throwable;"}, {"nme": "getThrowableCount", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)I"}, {"nme": "getThrowables", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)[<PERSON>ja<PERSON>/lang/Throwable;"}, {"nme": "indexOfThrowable", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;<PERSON><PERSON><PERSON>/lang/Class;)I"}, {"nme": "indexOfThrowable", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;<PERSON><PERSON><PERSON>/lang/Class;I)I"}, {"nme": "printRootCauseStackTrace", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;Ljava/io/PrintStream;)V"}, {"nme": "printRootCauseStackTrace", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "printRootCauseStackTrace", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;Ljava/io/PrintWriter;)V"}, {"nme": "getRootCauseStackTrace", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "removeCommonFrames", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Ljava/lang/String;>;Ljava/util/List<Ljava/lang/String;>;)V"}, {"nme": "getStackTrace", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getFullStackTrace", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isNestedThrowable", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)Z"}, {"nme": "getStackFrames", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getStackFrames", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[Lja<PERSON>/lang/String;"}, {"nme": "getStackFrameList", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)<PERSON><PERSON><PERSON>/util/List;", "sig": "(<PERSON><PERSON><PERSON>/lang/Throwable;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "WRAPPED_MARKER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": " [wrapped] "}, {"acc": 12, "nme": "CAUSE_METHOD_NAMES", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/codehaus/plexus/util/FastMap$Values.class": {"ver": 52, "acc": 32, "nme": "org/codehaus/plexus/util/FastMap$Values", "super": "java/util/AbstractCollection", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/FastMap;)V"}, {"nme": "iterator", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Iterator;"}, {"nme": "size", "acc": 1, "dsc": "()I"}, {"nme": "contains", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "clear", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/codehaus/plexus/util/FastMap;Lorg/codehaus/plexus/util/FastMap$1;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lorg/codehaus/plexus/util/FastMap;"}]}, "org/codehaus/plexus/util/io/URLInputStreamFacade.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/io/URLInputStreamFacade", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/net/URL;)V"}, {"nme": "getInputStream", "acc": 1, "dsc": "()Ljava/io/InputStream;", "exs": ["java/io/IOException"]}], "flds": [{"acc": 18, "nme": "url", "dsc": "Ljava/net/URL;"}]}, "org/codehaus/plexus/util/xml/XmlReaderException.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/xml/XmlReaderException", "super": "java/io/IOException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;Ljava/io/InputStream;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/io/InputStream;)V"}, {"nme": "getBomEncoding", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getXmlGuessEncoding", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getXmlEncoding", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getContentTypeMime", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getContentTypeEncoding", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getInputStream", "acc": 1, "dsc": "()Ljava/io/InputStream;"}], "flds": [{"acc": 2, "nme": "_bomEncoding", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "_xmlGuessEncoding", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "_xmlEncoding", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "_contentTypeMime", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "_contentTypeEncoding", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "_is", "dsc": "Ljava/io/InputStream;"}]}, "org/codehaus/plexus/util/io/CachingWriter.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/io/CachingWriter", "super": "java/io/OutputStreamWriter", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;<PERSON><PERSON><PERSON>/nio/charset/Charset;)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 1, "dsc": "(Lja<PERSON>/nio/file/Path;Ljava/nio/charset/Charset;)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/nio/file/Path;Ljava/nio/charset/Charset;I)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/io/CachingOutputStream;<PERSON><PERSON><PERSON>/nio/charset/Charset;)V", "exs": ["java/io/IOException"]}, {"nme": "isModified", "acc": 1, "dsc": "()Z"}], "flds": [{"acc": 18, "nme": "cos", "dsc": "Lorg/codehaus/plexus/util/io/CachingOutputStream;"}]}, "org/codehaus/plexus/util/xml/CompactXMLWriter.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/xml/CompactXMLWriter", "super": "org/codehaus/plexus/util/xml/PrettyPrintXMLWriter", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/io/PrintWriter;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Writer;)V"}, {"nme": "endOfLine", "acc": 4, "dsc": "()V"}], "flds": []}, "org/codehaus/plexus/util/xml/Xpp3DomUtils.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/xml/Xpp3DomUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "writeToSerializer", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;Lorg/codehaus/plexus/util/xml/Xpp3Dom;)V", "exs": ["java/io/IOException"]}, {"nme": "mergeIntoXpp3Dom", "acc": 10, "dsc": "(Lorg/codehaus/plexus/util/xml/Xpp3Dom;Lorg/codehaus/plexus/util/xml/Xpp3Dom;<PERSON><PERSON><PERSON>/lang/<PERSON>;)V"}, {"nme": "mergeXpp3Dom", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/xml/Xpp3Dom;Lorg/codehaus/plexus/util/xml/Xpp3Dom;<PERSON><PERSON><PERSON>/lang/<PERSON>;)Lorg/codehaus/plexus/util/xml/Xpp3Dom;"}, {"nme": "mergeXpp3Dom", "acc": 9, "dsc": "(Lorg/codehaus/plexus/util/xml/Xpp3Dom;Lorg/codehaus/plexus/util/xml/Xpp3Dom;)Lorg/codehaus/plexus/util/xml/Xpp3Dom;"}, {"nme": "isNotEmpty", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "isEmpty", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}], "flds": [{"acc": 25, "nme": "CHILDREN_COMBINATION_MODE_ATTRIBUTE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "combine.children"}, {"acc": 25, "nme": "CHILDREN_COMBINATION_MERGE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "merge"}, {"acc": 25, "nme": "CHILDREN_COMBINATION_APPEND", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "append"}, {"acc": 25, "nme": "DEFAULT_CHILDREN_COMBINATION_MODE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "merge"}, {"acc": 25, "nme": "SELF_COMBINATION_MODE_ATTRIBUTE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "combine.self"}, {"acc": 25, "nme": "SELF_COMBINATION_OVERRIDE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "override"}, {"acc": 25, "nme": "SELF_COMBINATION_MERGE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "merge"}, {"acc": 25, "nme": "ID_COMBINATION_MODE_ATTRIBUTE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "combine.id"}, {"acc": 25, "nme": "KEYS_COMBINATION_MODE_ATTRIBUTE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "combine.keys"}, {"acc": 25, "nme": "DEFAULT_SELF_COMBINATION_MODE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "merge"}]}, "org/codehaus/plexus/util/WriterFactory.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/WriterFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "newXmlWriter", "acc": 9, "dsc": "(Ljava/io/OutputStream;)Lorg/codehaus/plexus/util/xml/XmlStreamWriter;", "exs": ["java/io/IOException"]}, {"nme": "newXmlWriter", "acc": 9, "dsc": "(Ljava/io/File;)Lorg/codehaus/plexus/util/xml/XmlStreamWriter;", "exs": ["java/io/IOException"]}, {"nme": "newPlatformWriter", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/OutputStream;)<PERSON><PERSON><PERSON>/io/Writer;"}, {"nme": "newPlatformWriter", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)<PERSON><PERSON><PERSON>/io/Writer;", "exs": ["java/io/IOException"]}, {"nme": "newWriter", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/OutputStream;<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/io/Writer;", "exs": ["java/io/UnsupportedEncodingException"]}, {"nme": "newWriter", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/io/File;L<PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/io/Writer;", "exs": ["java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "ISO_8859_1", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "ISO-8859-1"}, {"acc": 25, "nme": "US_ASCII", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "US-ASCII"}, {"acc": 25, "nme": "UTF_16", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "UTF-16"}, {"acc": 25, "nme": "UTF_16BE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "UTF-16BE"}, {"acc": 25, "nme": "UTF_16LE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "UTF-16LE"}, {"acc": 25, "nme": "UTF_8", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "UTF-8"}, {"acc": 25, "nme": "FILE_ENCODING", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/codehaus/plexus/util/io/InputStreamFacade.class": {"ver": 52, "acc": 1537, "nme": "org/codehaus/plexus/util/io/InputStreamFacade", "super": "java/lang/Object", "mthds": [{"nme": "getInputStream", "acc": 1025, "dsc": "()Ljava/io/InputStream;", "exs": ["java/io/IOException"]}], "flds": []}, "org/codehaus/plexus/util/FastMap$EntrySet.class": {"ver": 52, "acc": 32, "nme": "org/codehaus/plexus/util/FastMap$EntrySet", "super": "java/util/AbstractSet", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/FastMap;)V"}, {"nme": "iterator", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Iterator;"}, {"nme": "size", "acc": 1, "dsc": "()I"}, {"nme": "contains", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "remove", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/codehaus/plexus/util/FastMap;Lorg/codehaus/plexus/util/FastMap$1;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lorg/codehaus/plexus/util/FastMap;"}]}, "org/codehaus/plexus/util/cli/WriterStreamConsumer.class": {"ver": 52, "acc": 33, "nme": "org/codehaus/plexus/util/cli/WriterStreamConsumer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Writer;)V"}, {"nme": "consumeLine", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 2, "nme": "writer", "dsc": "Ljava/io/PrintWriter;"}]}, "org/codehaus/plexus/util/FastMap$Values$1.class": {"ver": 52, "acc": 32, "nme": "org/codehaus/plexus/util/FastMap$Values$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/codehaus/plexus/util/FastMap$Values;)V"}, {"nme": "remove", "acc": 1, "dsc": "()V"}, {"nme": "hasNext", "acc": 1, "dsc": "()Z"}, {"nme": "next", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 0, "nme": "after", "dsc": "Lorg/codehaus/plexus/util/FastMap$EntryImpl;"}, {"acc": 0, "nme": "before", "dsc": "Lorg/codehaus/plexus/util/FastMap$EntryImpl;"}, {"acc": 4112, "nme": "this$1", "dsc": "Lorg/codehaus/plexus/util/FastMap$Values;"}]}}}}