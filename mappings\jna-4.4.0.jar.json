{"md5": "34d3537524a6c8c134e840e7be601569", "sha2": "cb208278274bf12ebdb56c61bd7407e6f774d65a", "sha256": "c4dadeeecaa90c8847902082aee5eb107fcf59c5d0e63a17fcaf273c0e2d2bd1", "contents": {"classes": {"com/sun/jna/Callback$UncaughtExceptionHandler.class": {"ver": 50, "acc": 1537, "nme": "com/sun/jna/Callback$UncaughtExceptionHandler", "super": "java/lang/Object", "mthds": [{"nme": "uncaughtException", "acc": 1025, "dsc": "(Lcom/sun/jna/Callback;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}], "flds": []}, "com/sun/jna/Native$4.class": {"ver": 50, "acc": 48, "nme": "com/sun/jna/Native$4", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "run", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "com/sun/jna/ptr/ShortByReference.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/ptr/ShortByReference", "super": "com/sun/jna/ptr/ByReference", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(S)V"}, {"nme": "setValue", "acc": 1, "dsc": "(S)V"}, {"nme": "getValue", "acc": 1, "dsc": "()S"}], "flds": []}, "com/sun/jna/Pointer.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/Pointer", "super": "java/lang/Object", "mthds": [{"nme": "createConstant", "acc": 25, "dsc": "(J)Lcom/sun/jna/Pointer;"}, {"nme": "createConstant", "acc": 25, "dsc": "(I)Lcom/sun/jna/Pointer;"}, {"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(J)V"}, {"nme": "share", "acc": 1, "dsc": "(J)Lcom/sun/jna/Pointer;"}, {"nme": "share", "acc": 1, "dsc": "(JJ)Lcom/sun/jna/Pointer;"}, {"nme": "clear", "acc": 1, "dsc": "(J)V"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "indexOf", "acc": 1, "dsc": "(JB)J"}, {"nme": "read", "acc": 1, "dsc": "(J[BII)V"}, {"nme": "read", "acc": 1, "dsc": "(J[SII)V"}, {"nme": "read", "acc": 1, "dsc": "(J[CII)V"}, {"nme": "read", "acc": 1, "dsc": "(J[III)V"}, {"nme": "read", "acc": 1, "dsc": "(J[JII)V"}, {"nme": "read", "acc": 1, "dsc": "(J[FII)V"}, {"nme": "read", "acc": 1, "dsc": "(J[DII)V"}, {"nme": "read", "acc": 1, "dsc": "(J[Lcom/sun/jna/Pointer;II)V"}, {"nme": "write", "acc": 1, "dsc": "(J[BII)V"}, {"nme": "write", "acc": 1, "dsc": "(J[SII)V"}, {"nme": "write", "acc": 1, "dsc": "(J[CII)V"}, {"nme": "write", "acc": 1, "dsc": "(J[III)V"}, {"nme": "write", "acc": 1, "dsc": "(J[JII)V"}, {"nme": "write", "acc": 1, "dsc": "(J[FII)V"}, {"nme": "write", "acc": 1, "dsc": "(J[DII)V"}, {"nme": "write", "acc": 1, "dsc": "(J[Lcom/sun/jna/Pointer;II)V"}, {"nme": "getValue", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(J<PERSON><PERSON><PERSON>/lang/Class<*>;Ljava/lang/Object;)Ljava/lang/Object;"}, {"nme": "readArray", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(J<PERSON><PERSON><PERSON>/lang/Object;Ljava/lang/Class<*>;)V"}, {"nme": "getByte", "acc": 1, "dsc": "(J)B"}, {"nme": "getChar", "acc": 1, "dsc": "(J)C"}, {"nme": "getShort", "acc": 1, "dsc": "(J)S"}, {"nme": "getInt", "acc": 1, "dsc": "(J)I"}, {"nme": "getLong", "acc": 1, "dsc": "(J)J"}, {"nme": "getNativeLong", "acc": 1, "dsc": "(J)Lcom/sun/jna/NativeLong;"}, {"nme": "getFloat", "acc": 1, "dsc": "(J)F"}, {"nme": "getDouble", "acc": 1, "dsc": "(J)D"}, {"nme": "getPointer", "acc": 1, "dsc": "(J)Lcom/sun/jna/Pointer;"}, {"nme": "getByteBuffer", "acc": 1, "dsc": "(JJ)Ljava/nio/ByteBuffer;"}, {"nme": "getString", "acc": 131073, "dsc": "(JZ)Ljava/lang/String;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getWideString", "acc": 1, "dsc": "(J)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getString", "acc": 1, "dsc": "(J)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getByteArray", "acc": 1, "dsc": "(JI)[B"}, {"nme": "getCharArray", "acc": 1, "dsc": "(JI)[C"}, {"nme": "getShortArray", "acc": 1, "dsc": "(JI)[S"}, {"nme": "getIntArray", "acc": 1, "dsc": "(JI)[I"}, {"nme": "getLongArray", "acc": 1, "dsc": "(JI)[J"}, {"nme": "getFloatArray", "acc": 1, "dsc": "(JI)[F"}, {"nme": "getDoubleArray", "acc": 1, "dsc": "(JI)[D"}, {"nme": "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(J)[Lcom/sun/jna/Pointer;"}, {"nme": "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(JI)[Lcom/sun/jna/Pointer;"}, {"nme": "getStringArray", "acc": 1, "dsc": "(J)[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getStringArray", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getStringArray", "acc": 1, "dsc": "(JI)[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getStringArray", "acc": 131073, "dsc": "(JZ)[<PERSON><PERSON><PERSON>/lang/String;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getWideStringArray", "acc": 1, "dsc": "(J)[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getWideStringArray", "acc": 1, "dsc": "(JI)[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getStringArray", "acc": 131073, "dsc": "(JIZ)[<PERSON><PERSON><PERSON>/lang/String;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getStringArray", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)[<PERSON>ja<PERSON>/lang/String;"}, {"nme": "setValue", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(J<PERSON><PERSON><PERSON>/lang/Object;Ljava/lang/Class<*>;)V"}, {"nme": "writeArray", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(J<PERSON><PERSON><PERSON>/lang/Object;Ljava/lang/Class<*>;)V"}, {"nme": "set<PERSON><PERSON>ory", "acc": 1, "dsc": "(JJB)V"}, {"nme": "setByte", "acc": 1, "dsc": "(JB)V"}, {"nme": "setShort", "acc": 1, "dsc": "(JS)V"}, {"nme": "setChar", "acc": 1, "dsc": "(JC)V"}, {"nme": "setInt", "acc": 1, "dsc": "(JI)V"}, {"nme": "setLong", "acc": 1, "dsc": "(JJ)V"}, {"nme": "setNativeLong", "acc": 1, "dsc": "(JLcom/sun/jna/NativeLong;)V"}, {"nme": "setFloat", "acc": 1, "dsc": "(JF)V"}, {"nme": "setDouble", "acc": 1, "dsc": "(JD)V"}, {"nme": "setPointer", "acc": 1, "dsc": "(JLcom/sun/jna/Pointer;)V"}, {"nme": "setString", "acc": 131073, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;Z)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "setWideString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setString", "acc": 1, "dsc": "(JLcom/sun/jna/WString;)V"}, {"nme": "setString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "dump", "acc": 1, "dsc": "(JI)Ljava/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "nativeValue", "acc": 9, "dsc": "(Lcom/sun/jna/Pointer;)J"}, {"nme": "nativeValue", "acc": 9, "dsc": "(Lcom/sun/jna/Pointer;J)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "SIZE", "dsc": "I"}, {"acc": 25, "nme": "NULL", "dsc": "Lcom/sun/jna/Pointer;"}, {"acc": 4, "nme": "peer", "dsc": "J"}]}, "com/sun/jna/VarArgsChecker$RealVarArgsChecker.class": {"ver": 50, "acc": 48, "nme": "com/sun/jna/VarArgsChecker$RealVarArgsChecker", "super": "com/sun/jna/<PERSON>ar<PERSON>rgs<PERSON>hecker", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "isVarArgs", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)Z"}, {"nme": "fixedArgs", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)I"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lcom/sun/jna/VarArgsChecker$1;)V"}], "flds": []}, "com/sun/jna/Structure$FFIType$size_t.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/Structure$FFIType$size_t", "super": "com/sun/jna/IntegerType", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(J)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}]}, "com/sun/jna/Memory$SharedMemory.class": {"ver": 50, "acc": 32, "nme": "com/sun/jna/Memory$SharedMemory", "super": "com/sun/jna/Memory", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/sun/jna/Memory;JJ)V"}, {"nme": "dispose", "acc": 4, "dsc": "()V"}, {"nme": "boundsCheck", "acc": 4, "dsc": "(JJ)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lcom/sun/jna/Memory;"}]}, "com/sun/jna/Native$7.class": {"ver": 50, "acc": 48, "nme": "com/sun/jna/Native$7", "super": "java/lang/ThreadLocal", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "initialValue", "acc": 4, "dsc": "()Lcom/sun/jna/Memory;"}, {"nme": "initialValue", "acc": 4164, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "com/sun/jna/Native$ffi_callback.class": {"ver": 50, "acc": 1537, "nme": "com/sun/jna/Native$ffi_callback", "super": "java/lang/Object", "mthds": [{"nme": "invoke", "acc": 1025, "dsc": "(JJJ)V"}], "flds": []}, "com/sun/jna/Native$Buffers.class": {"ver": 50, "acc": 32, "nme": "com/sun/jna/Native$Buffers", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}], "flds": []}, "com/sun/jna/TypeMapper.class": {"ver": 50, "acc": 1537, "nme": "com/sun/jna/TypeMapper", "super": "java/lang/Object", "mthds": [{"nme": "getFromNativeConverter", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Lcom/sun/jna/FromNativeConverter;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Lcom/sun/jna/FromNativeConverter;"}, {"nme": "getToNativeConverter", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Lcom/sun/jna/ToNativeConverter;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Lcom/sun/jna/ToNativeConverter;"}], "flds": []}, "com/sun/jna/CallbackParameterContext.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/CallbackParameterContext", "super": "com/sun/jna/FromNativeContext", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/reflect/Method;[Ljava/lang/Object;I)V", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/reflect/Method;[Ljava/lang/Object;I)V"}, {"nme": "getMethod", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"nme": "getArguments", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getIndex", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 2, "nme": "method", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 2, "nme": "args", "dsc": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 2, "nme": "index", "dsc": "I"}]}, "com/sun/jna/Union.class": {"ver": 50, "acc": 1057, "nme": "com/sun/jna/Union", "super": "com/sun/jna/Structure", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "<init>", "acc": 4, "dsc": "(Lcom/sun/jna/Pointer;)V"}, {"nme": "<init>", "acc": 4, "dsc": "(Lcom/sun/jna/Pointer;I)V"}, {"nme": "<init>", "acc": 4, "dsc": "(Lcom/sun/jna/TypeMapper;)V"}, {"nme": "<init>", "acc": 4, "dsc": "(Lcom/sun/jna/Pointer;ILcom/sun/jna/TypeMapper;)V"}, {"nme": "getFieldOrder", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "setType", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)V"}, {"nme": "setType", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "readField", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "writeField", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "writeField", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "getTypedValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>ja<PERSON>/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Ljava/lang/Object;"}, {"nme": "setTypedValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "findField", "acc": 2, "dsc": "(Ljava/lang/Class;)Lcom/sun/jna/Structure$StructField;", "sig": "(Ljava/lang/Class<*>;)Lcom/sun/jna/Structure$StructField;"}, {"nme": "writeField", "acc": 4, "dsc": "(Lcom/sun/jna/Structure$StructField;)V"}, {"nme": "readField", "acc": 4, "dsc": "(Lcom/sun/jna/Structure$StructField;)Ljava/lang/Object;"}, {"nme": "getNativeAlignment", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/Object;Z)I", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;Ljava/lang/Object;Z)I"}], "flds": [{"acc": 2, "nme": "activeField", "dsc": "Lcom/sun/jna/Structure$StructField;"}]}, "com/sun/jna/CallbackThreadInitializer.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/CallbackThreadInitializer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Z)V"}, {"nme": "<init>", "acc": 1, "dsc": "(ZZ)V"}, {"nme": "<init>", "acc": 1, "dsc": "(ZZ<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Z<PERSON><PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/ThreadGroup;)V"}, {"nme": "getName", "acc": 1, "dsc": "(Lcom/sun/jna/Callback;)Ljava/lang/String;"}, {"nme": "getThreadGroup", "acc": 1, "dsc": "(Lcom/sun/jna/Callback;)Ljava/lang/ThreadGroup;"}, {"nme": "isDaemon", "acc": 1, "dsc": "(Lcom/sun/jna/Callback;)Z"}, {"nme": "detach", "acc": 1, "dsc": "(Lcom/sun/jna/Callback;)Z"}], "flds": [{"acc": 2, "nme": "daemon", "dsc": "Z"}, {"acc": 2, "nme": "detach", "dsc": "Z"}, {"acc": 2, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "group", "dsc": "<PERSON><PERSON><PERSON>/lang/ThreadGroup;"}]}, "com/sun/jna/ToNativeConverter.class": {"ver": 50, "acc": 1537, "nme": "com/sun/jna/ToNativeConverter", "super": "java/lang/Object", "mthds": [{"nme": "toNative", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lcom/sun/jna/ToNativeContext;)Ljava/lang/Object;"}, {"nme": "nativeType", "acc": 1025, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<*>;"}], "flds": []}, "com/sun/jna/win32/StdCallFunctionMapper.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/win32/StdCallFunctionMapper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getArgumentNativeStackSize", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)I", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)I"}, {"nme": "getFunctionName", "acc": 1, "dsc": "(Lcom/sun/jna/NativeLibrary;Ljava/lang/reflect/Method;)Ljava/lang/String;"}], "flds": []}, "com/sun/jna/FunctionParameterContext.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/FunctionParameterContext", "super": "com/sun/jna/ToNativeContext", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/sun/jna/Function;[<PERSON><PERSON><PERSON>/lang/Object;I)V"}, {"nme": "getFunction", "acc": 1, "dsc": "()Lcom/sun/jna/Function;"}, {"nme": "getParameters", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getParameterIndex", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 2, "nme": "function", "dsc": "Lcom/sun/jna/Function;"}, {"acc": 2, "nme": "args", "dsc": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 2, "nme": "index", "dsc": "I"}]}, "com/sun/jna/ptr/LongByReference.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/ptr/LongByReference", "super": "com/sun/jna/ptr/ByReference", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(J)V"}, {"nme": "setValue", "acc": 1, "dsc": "(J)V"}, {"nme": "getValue", "acc": 1, "dsc": "()J"}], "flds": []}, "com/sun/jna/Structure$2.class": {"ver": 50, "acc": 48, "nme": "com/sun/jna/Structure$2", "super": "java/lang/ThreadLocal", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "initialValue", "acc": 36, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Lcom/sun/jna/Structure;>;"}, {"nme": "initialValue", "acc": 4164, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "com/sun/jna/ToNativeContext.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/ToNativeContext", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}], "flds": []}, "com/sun/jna/ptr/FloatByReference.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/ptr/FloatByReference", "super": "com/sun/jna/ptr/ByReference", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(F)V"}, {"nme": "setValue", "acc": 1, "dsc": "(F)V"}, {"nme": "getValue", "acc": 1, "dsc": "()F"}], "flds": []}, "com/sun/jna/CallbackReference$NativeFunctionHandler.class": {"ver": 50, "acc": 32, "nme": "com/sun/jna/CallbackReference$NativeFunctionHandler", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/sun/jna/Pointer;ILjava/util/Map;)V", "sig": "(Lcom/sun/jna/Pointer;ILjava/util/Map<Ljava/lang/String;*>;)V"}, {"nme": "invoke", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/reflect/Method;[Ljava/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Throwable"]}, {"nme": "getPointer", "acc": 1, "dsc": "()Lcom/sun/jna/Pointer;"}], "flds": [{"acc": 18, "nme": "function", "dsc": "Lcom/sun/jna/Function;"}, {"acc": 18, "nme": "options", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;*>;"}]}, "com/sun/jna/MethodResultContext.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/MethodResultContext", "super": "com/sun/jna/FunctionResultContext", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lja<PERSON>/lang/Class;Lcom/sun/jna/Function;[Ljava/lang/Object;Ljava/lang/reflect/Method;)V", "sig": "(Lja<PERSON>/lang/Class<*>;Lcom/sun/jna/Function;[Ljava/lang/Object;Ljava/lang/reflect/Method;)V"}, {"nme": "getMethod", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/reflect/Method;"}], "flds": [{"acc": 18, "nme": "method", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}]}, "com/sun/jna/NativeMappedConverter.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/NativeMappedConverter", "super": "java/lang/Object", "mthds": [{"nme": "getInstance", "acc": 9, "dsc": "(Lja<PERSON>/lang/Class;)Lcom/sun/jna/NativeMappedConverter;", "sig": "(Ljava/lang/Class<*>;)Lcom/sun/jna/NativeMappedConverter;"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)V"}, {"nme": "defaultValue", "acc": 1, "dsc": "()Lcom/sun/jna/NativeMapped;"}, {"nme": "fromNative", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lcom/sun/jna/FromNativeContext;)Ljava/lang/Object;"}, {"nme": "nativeType", "acc": 1, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<*>;"}, {"nme": "toNative", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lcom/sun/jna/ToNativeContext;)Ljava/lang/Object;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "converters", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Class<*>;Ljava/lang/ref/Reference<Lcom/sun/jna/NativeMappedConverter;>;>;"}, {"acc": 18, "nme": "type", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 18, "nme": "nativeType", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 18, "nme": "instance", "dsc": "Lcom/sun/jna/NativeMapped;"}]}, "com/sun/jna/VarArgsChecker$1.class": {"ver": 50, "acc": 4128, "nme": "com/sun/jna/VarArgsChecker$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "com/sun/jna/win32/StdCallLibrary$StdCallCallback.class": {"ver": 50, "acc": 1537, "nme": "com/sun/jna/win32/StdCallLibrary$StdCallCallback", "super": "java/lang/Object", "mthds": [], "flds": []}, "com/sun/jna/win32/W32APIOptions$2.class": {"ver": 50, "acc": 48, "nme": "com/sun/jna/win32/W32APIOptions$2", "super": "java/util/HashMap", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}]}, "com/sun/jna/AltCallingConvention.class": {"ver": 50, "acc": 1537, "nme": "com/sun/jna/AltCallingConvention", "super": "java/lang/Object", "mthds": [], "flds": []}, "com/sun/jna/Function$PostCallRead.class": {"ver": 50, "acc": 1537, "nme": "com/sun/jna/Function$PostCallRead", "super": "java/lang/Object", "mthds": [{"nme": "read", "acc": 1025, "dsc": "()V"}], "flds": []}, "com/sun/jna/NativeLibrary$1.class": {"ver": 50, "acc": 32, "nme": "com/sun/jna/NativeLibrary$1", "super": "com/sun/jna/Function", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/sun/jna/NativeLibrary;Lcom/sun/jna/NativeLibrary;Ljava/lang/String;ILjava/lang/String;)V"}, {"nme": "invoke", "acc": 0, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Class;ZI)Ljava/lang/Object;", "sig": "([<PERSON><PERSON><PERSON>/lang/Object;Lja<PERSON>/lang/Class<*>;ZI)Ljava/lang/Object;"}, {"nme": "invoke", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;[Ljava/lang/Class;Ljava/lang/Class;[Ljava/lang/Object;Ljava/util/Map;)Ljava/lang/Object;", "sig": "(Lja<PERSON>/lang/reflect/Method;[Ljava/lang/Class<*>;Ljava/lang/Class<*>;[Ljava/lang/Object;Ljava/util/Map<Ljava/lang/String;*>;)Ljava/lang/Object;"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lcom/sun/jna/NativeLibrary;"}]}, "com/sun/jna/NativeLibrary.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/NativeLibrary", "super": "java/lang/Object", "mthds": [{"nme": "functionKey", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>java/lang/String;J<PERSON>java/util/Map;)V", "sig": "(Lja<PERSON>/lang/String;Ljava/lang/String;JLjava/util/Map<Ljava/lang/String;*>;)V"}, {"nme": "openFlags", "acc": 10, "dsc": "(Ljava/util/Map;)I", "sig": "(Ljava/util/Map<Ljava/lang/String;*>;)I"}, {"nme": "loadLibrary", "acc": 10, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/Map;)Lcom/sun/jna/NativeLibrary;", "sig": "(Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;*>;)Lcom/sun/jna/NativeLibrary;"}, {"nme": "matchFramework", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getLibraryName", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getInstance", "acc": 25, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/sun/jna/NativeLibrary;"}, {"nme": "getInstance", "acc": 25, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/ClassLoader;)Lcom/sun/jna/NativeLibrary;"}, {"nme": "getInstance", "acc": 25, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/Map;)Lcom/sun/jna/NativeLibrary;", "sig": "(Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;*>;)Lcom/sun/jna/NativeLibrary;"}, {"nme": "getProcess", "acc": 57, "dsc": "()Lcom/sun/jna/NativeLibrary;"}, {"nme": "getProcess", "acc": 57, "dsc": "(Ljava/util/Map;)Lcom/sun/jna/NativeLibrary;", "sig": "(Ljava/util/Map<Ljava/lang/String;*>;)Lcom/sun/jna/NativeLibrary;"}, {"nme": "addSearchPath", "acc": 25, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getFunction", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/sun/jna/Function;"}, {"nme": "getFunction", "acc": 0, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/reflect/Method;)Lcom/sun/jna/Function;"}, {"nme": "getFunction", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Lcom/sun/jna/Function;"}, {"nme": "getFunction", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>java/lang/String;)Lcom/sun/jna/Function;"}, {"nme": "getOptions", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;*>;"}, {"nme": "getGlobalVariableAddress", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/sun/jna/Pointer;"}, {"nme": "getSymbolAddress", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)J"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getFile", "acc": 1, "dsc": "()Ljava/io/File;"}, {"nme": "finalize", "acc": 4, "dsc": "()V"}, {"nme": "disposeAll", "acc": 8, "dsc": "()V"}, {"nme": "dispose", "acc": 1, "dsc": "()V"}, {"nme": "initPaths", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "findLibraryPath", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/List;)Ljava/lang/String;", "sig": "(Lja<PERSON>/lang/String;Ljava/util/List<Ljava/lang/String;>;)Ljava/lang/String;"}, {"nme": "mapSharedLibraryName", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "isVersionedName", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "matchLibrary", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/List;)Ljava/lang/String;", "sig": "(Lja<PERSON>/lang/String;Ljava/util/List<Ljava/lang/String;>;)Ljava/lang/String;"}, {"nme": "parseVersion", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)D"}, {"nme": "getMultiArchPath", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLinuxLdPaths", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/util/ArrayList;", "sig": "()<PERSON>java/util/ArrayList<Ljava/lang/String;>;"}, {"nme": "access$000", "acc": 4104, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "handle", "dsc": "J"}, {"acc": 18, "nme": "libraryName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "libraryPath", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "functions", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lcom/sun/jna/Function;>;"}, {"acc": 16, "nme": "callFlags", "dsc": "I"}, {"acc": 2, "nme": "encoding", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 16, "nme": "options", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;*>;"}, {"acc": 26, "nme": "libraries", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/ref/Reference<Lcom/sun/jna/NativeLibrary;>;>;"}, {"acc": 26, "nme": "searchPaths", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;>;"}, {"acc": 26, "nme": "librarySearchPath", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 26, "nme": "DEFAULT_OPEN_OPTIONS", "dsc": "I", "val": -1}]}, "com/sun/jna/Structure$StructureSet.class": {"ver": 50, "acc": 32, "nme": "com/sun/jna/Structure$StructureSet", "super": "java/util/AbstractCollection", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "ensureCapacity", "acc": 2, "dsc": "(I)V"}, {"nme": "getElements", "acc": 1, "dsc": "()[Lcom/sun/jna/Structure;"}, {"nme": "size", "acc": 1, "dsc": "()I"}, {"nme": "contains", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "add", "acc": 1, "dsc": "(Lcom/sun/jna/Structure;)Z"}, {"nme": "indexOf", "acc": 2, "dsc": "(Lcom/sun/jna/Structure;)I"}, {"nme": "remove", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "iterator", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Iterator;", "sig": "()Ljava/util/Iterator<Lcom/sun/jna/Structure;>;"}, {"nme": "add", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "flds": [{"acc": 0, "nme": "elements", "dsc": "[Lcom/sun/jna/Structure;"}, {"acc": 2, "nme": "count", "dsc": "I"}]}, "com/sun/jna/ptr/PointerByReference.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/ptr/PointerByReference", "super": "com/sun/jna/ptr/ByReference", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lcom/sun/jna/Pointer;)V"}, {"nme": "setValue", "acc": 1, "dsc": "(Lcom/sun/jna/Pointer;)V"}, {"nme": "getValue", "acc": 1, "dsc": "()Lcom/sun/jna/Pointer;"}], "flds": []}, "com/sun/jna/Callback.class": {"ver": 50, "acc": 1537, "nme": "com/sun/jna/Callback", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "METHOD_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "callback"}, {"acc": 25, "nme": "FORBIDDEN_NAMES", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}]}, "com/sun/jna/Library$Handler$FunctionInfo.class": {"ver": 50, "acc": 48, "nme": "com/sun/jna/Library$Handler$FunctionInfo", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/InvocationHandler;Lcom/sun/jna/Function;[Ljava/lang/Class;ZLjava/util/Map;)V", "sig": "(Lja<PERSON>/lang/reflect/InvocationHandler;Lcom/sun/jna/Function;[Ljava/lang/Class<*>;ZLjava/util/Map<Ljava/lang/String;*>;)V"}], "flds": [{"acc": 16, "nme": "handler", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/InvocationHandler;"}, {"acc": 16, "nme": "function", "dsc": "Lcom/sun/jna/Function;"}, {"acc": 16, "nme": "isVarArgs", "dsc": "Z"}, {"acc": 16, "nme": "options", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;*>;"}, {"acc": 16, "nme": "parameterTypes", "dsc": "[Ljava/lang/Class;", "sig": "[Ljava/lang/Class<*>;"}]}, "com/sun/jna/Native$2.class": {"ver": 50, "acc": 48, "nme": "com/sun/jna/Native$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "finalize", "acc": 4, "dsc": "()V"}], "flds": []}, "com/sun/jna/Structure$1.class": {"ver": 50, "acc": 48, "nme": "com/sun/jna/Structure$1", "super": "java/lang/ThreadLocal", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "initialValue", "acc": 36, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Lcom/sun/jna/Pointer;Lcom/sun/jna/Structure;>;"}, {"nme": "initialValue", "acc": 4164, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "com/sun/jna/Native.class": {"ver": 50, "acc": 49, "nme": "com/sun/jna/Native", "super": "java/lang/Object", "mthds": [{"nme": "parseVersion", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)F", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "isCompatibleVersion", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "dispose", "acc": 10, "dsc": "()V"}, {"nme": "deleteLibrary", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)Z"}, {"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "initIDs", "acc": 266, "dsc": "()V"}, {"nme": "setProtected", "acc": 297, "dsc": "(Z)V"}, {"nme": "isProtected", "acc": 297, "dsc": "()Z"}, {"nme": "setPreserveLastError", "acc": 131081, "dsc": "(Z)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getPreserveLastError", "acc": 131081, "dsc": "()Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getWindowID", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/awt/Window;)J", "exs": ["java/awt/HeadlessException"]}, {"nme": "getComponentID", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/awt/Component;)J", "exs": ["java/awt/HeadlessException"]}, {"nme": "getWindowPointer", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/awt/Window;)Lcom/sun/jna/Pointer;", "exs": ["java/awt/HeadlessException"]}, {"nme": "getComponentPointer", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/awt/Component;)Lcom/sun/jna/Pointer;", "exs": ["java/awt/HeadlessException"]}, {"nme": "getWindowHandle0", "acc": 264, "dsc": "(<PERSON><PERSON><PERSON>/awt/Component;)J"}, {"nme": "getDirectBufferPointer", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/nio/Buffer;)Lcom/sun/jna/Pointer;"}, {"nme": "_getDirectBufferPointer", "acc": 266, "dsc": "(<PERSON><PERSON><PERSON>/nio/<PERSON>er;)J"}, {"nme": "toString", "acc": 9, "dsc": "([B)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toString", "acc": 9, "dsc": "([<PERSON><PERSON><PERSON><PERSON>/lang/String;)<PERSON>ja<PERSON>/lang/String;"}, {"nme": "toString", "acc": 9, "dsc": "([C)Ljava/lang/String;"}, {"nme": "toStringList", "acc": 9, "dsc": "([C)Ljava/util/List;", "sig": "([C)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "toStringList", "acc": 9, "dsc": "([CII)Ljava/util/List;", "sig": "([CII)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "loadLibrary", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>ja<PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;)TT;"}, {"nme": "loadLibrary", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/util/Map;)Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;Ljava/util/Map<Ljava/lang/String;*>;)TT;"}, {"nme": "loadLibrary", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Class;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/String;Ljava/lang/Class<TT;>;)TT;"}, {"nme": "loadLibrary", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Class;Ljava/util/Map;)Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/String;Ljava/lang/Class<TT;>;Ljava/util/Map<Ljava/lang/String;*>;)TT;"}, {"nme": "loadLibraryInstance", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)V"}, {"nme": "findEnclosingLibraryClass", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/Class;", "sig": "(Lja<PERSON>/lang/Class<*>;)Ljava/lang/Class<*>;"}, {"nme": "getLibraryOptions", "acc": 9, "dsc": "(L<PERSON><PERSON>/lang/Class;)Ljava/util/Map;", "sig": "(Ljava/lang/Class<*>;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;"}, {"nme": "lookupField", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Class;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(L<PERSON><PERSON>/lang/Class<*>;Ljava/lang/String;Ljava/lang/Class<*>;)Ljava/lang/Object;"}, {"nme": "getTypeMapper", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Lcom/sun/jna/TypeMapper;", "sig": "(L<PERSON><PERSON>/lang/Class<*>;)Lcom/sun/jna/TypeMapper;"}, {"nme": "getStringEncoding", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/String;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Ljava/lang/String;"}, {"nme": "getDefaultStringEncoding", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getStructureAlignment", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)I", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)I"}, {"nme": "getBytes", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[B"}, {"nme": "getBytes", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)[B"}, {"nme": "toByteArray", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[B"}, {"nme": "toByteArray", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)[B"}, {"nme": "toCharArray", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[C"}, {"nme": "loadNativeDispatchLibrary", "acc": 10, "dsc": "()V"}, {"nme": "loadNativeDispatchLibraryFromClasspath", "acc": 10, "dsc": "()V"}, {"nme": "isUnpacked", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)Z"}, {"nme": "extractFromResourcePath", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/io/File;", "exs": ["java/io/IOException"]}, {"nme": "extractFromResourcePath", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/ClassLoader;)Ljava/io/File;", "exs": ["java/io/IOException"]}, {"nme": "sizeof", "acc": 266, "dsc": "(I)I"}, {"nme": "getNativeVersion", "acc": 266, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getAPIChecksum", "acc": 266, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLastError", "acc": 265, "dsc": "()I"}, {"nme": "setLastError", "acc": 265, "dsc": "(I)V"}, {"nme": "synchronizedLibrary", "acc": 9, "dsc": "(Lcom/sun/jna/Library;)Lcom/sun/jna/Library;"}, {"nme": "getWebStartLibraryPath", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "markTemporaryFile", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V"}, {"nme": "getTempDir", "acc": 8, "dsc": "()Ljava/io/File;", "exs": ["java/io/IOException"]}, {"nme": "removeTemporaryFiles", "acc": 8, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "getNativeSize", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/Object;)I", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;Ljava/lang/Object;)I"}, {"nme": "getNativeSize", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)I", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)I"}, {"nme": "isSupportedNativeType", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "setCallbackExceptionHandler", "acc": 9, "dsc": "(Lcom/sun/jna/Callback$UncaughtExceptionHandler;)V"}, {"nme": "getCallbackExceptionHandler", "acc": 9, "dsc": "()Lcom/sun/jna/Callback$UncaughtExceptionHandler;"}, {"nme": "register", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "register", "acc": 9, "dsc": "(Lcom/sun/jna/NativeLibrary;)V"}, {"nme": "findDirectMappedClass", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/Class;", "sig": "(Lja<PERSON>/lang/Class<*>;)Ljava/lang/Class<*>;"}, {"nme": "getCallingClass", "acc": 8, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<*>;"}, {"nme": "setCallbackThreadInitializer", "acc": 9, "dsc": "(Lcom/sun/jna/Callback;Lcom/sun/jna/CallbackThreadInitializer;)V"}, {"nme": "unregisterAll", "acc": 10, "dsc": "()V"}, {"nme": "unregister", "acc": 9, "dsc": "()V"}, {"nme": "unregister", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)V"}, {"nme": "registered", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "unregister", "acc": 266, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;[J)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;[J)V"}, {"nme": "getSignature", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/String;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Ljava/lang/String;"}, {"nme": "replace", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)<PERSON>java/lang/String;"}, {"nme": "getConversion", "acc": 10, "dsc": "(L<PERSON><PERSON>/lang/Class;Lcom/sun/jna/TypeMapper;)I", "sig": "(L<PERSON><PERSON>/lang/Class<*>;Lcom/sun/jna/TypeMapper;)I"}, {"nme": "register", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/String;)V", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/String;)V"}, {"nme": "register", "acc": 9, "dsc": "(Ljava/lang/Class;Lcom/sun/jna/NativeLibrary;)V", "sig": "(Ljava/lang/Class<*>;Lcom/sun/jna/NativeLibrary;)V"}, {"nme": "cacheOptions", "acc": 10, "dsc": "(L<PERSON><PERSON>/lang/Class;Ljava/util/Map;Ljava/lang/Object;)Ljava/util/Map;", "sig": "(Ljava/lang/Class<*>;Ljava/util/Map<Ljava/lang/String;*>;Ljava/lang/Object;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;"}, {"nme": "registerMethod", "acc": 266, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;[I[J[JIJJLjava/lang/reflect/Method;JIZ[Lcom/sun/jna/ToNativeConverter;Lcom/sun/jna/FromNativeConverter;Ljava/lang/String;)J", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/String;Ljava/lang/String;[I[J[JIJJLjava/lang/reflect/Method;JIZ[Lcom/sun/jna/ToNativeConverter;Lcom/sun/jna/FromNativeConverter;Ljava/lang/String;)J"}, {"nme": "fromNative", "acc": 10, "dsc": "(Ljava/lang/Class;Ljava/lang/Object;)Lcom/sun/jna/NativeMapped;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Object;)Lcom/sun/jna/NativeMapped;"}, {"nme": "fromNative", "acc": 10, "dsc": "(Lja<PERSON>/lang/reflect/Method;Ljava/lang/Object;)Lcom/sun/jna/NativeMapped;"}, {"nme": "nativeType", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/Class;", "sig": "(Lja<PERSON>/lang/Class<*>;)Ljava/lang/Class<*>;"}, {"nme": "toNative", "acc": 10, "dsc": "(Lcom/sun/jna/ToNativeConverter;Lja<PERSON>/lang/Object;)Ljava/lang/Object;"}, {"nme": "fromNative", "acc": 10, "dsc": "(Lcom/sun/jna/FromNativeConverter;Lja<PERSON>/lang/Object;Ljava/lang/reflect/Method;)Ljava/lang/Object;"}, {"nme": "ffi_prep_cif", "acc": 265, "dsc": "(IIJJ)J"}, {"nme": "ffi_call", "acc": 265, "dsc": "(JJJJ)V"}, {"nme": "ffi_prep_closure", "acc": 265, "dsc": "(JLcom/sun/jna/Native$ffi_callback;)J"}, {"nme": "ffi_free_closure", "acc": 265, "dsc": "(J)V"}, {"nme": "initialize_ffi_type", "acc": 264, "dsc": "(J)I"}, {"nme": "main", "acc": 9, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "freeNativeCallback", "acc": 296, "dsc": "(J)V"}, {"nme": "createNativeCallback", "acc": 296, "dsc": "(Lcom/sun/jna/Callback;<PERSON><PERSON><PERSON>/lang/reflect/Method;[Ljava/lang/Class;Ljava/lang/Class;IILjava/lang/String;)J", "sig": "(Lcom/sun/jna/Callback;Lja<PERSON>/lang/reflect/Method;[Ljava/lang/Class<*>;Ljava/lang/Class<*>;IILjava/lang/String;)J"}, {"nme": "invokeInt", "acc": 264, "dsc": "(Lcom/sun/jna/Function;JI[Ljava/lang/Object;)I"}, {"nme": "invokeLong", "acc": 264, "dsc": "(Lcom/sun/jna/Function;JI[Ljava/lang/Object;)J"}, {"nme": "invokeVoid", "acc": 264, "dsc": "(Lcom/sun/jna/Function;JI[Ljava/lang/Object;)V"}, {"nme": "invokeFloat", "acc": 264, "dsc": "(Lcom/sun/jna/Function;JI[Ljava/lang/Object;)F"}, {"nme": "invokeDouble", "acc": 264, "dsc": "(Lcom/sun/jna/Function;JI[Ljava/lang/Object;)D"}, {"nme": "invokePointer", "acc": 264, "dsc": "(Lcom/sun/jna/Function;JI[Ljava/lang/Object;)J"}, {"nme": "invokeStructure", "acc": 266, "dsc": "(Lcom/sun/jna/Function;JI[Ljava/lang/Object;JJ)V"}, {"nme": "invokeStructure", "acc": 8, "dsc": "(Lcom/sun/jna/Function;JI[Ljava/lang/Object;Lcom/sun/jna/Structure;)Lcom/sun/jna/Structure;"}, {"nme": "invokeObject", "acc": 264, "dsc": "(Lcom/sun/jna/Function;JI[Ljava/lang/Object;)Ljava/lang/Object;"}, {"nme": "open", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)J"}, {"nme": "open", "acc": 264, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)J"}, {"nme": "close", "acc": 264, "dsc": "(J)V"}, {"nme": "findSymbol", "acc": 264, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)J"}, {"nme": "indexOf", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJB)J"}, {"nme": "read", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJ[BII)V"}, {"nme": "read", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJ[SII)V"}, {"nme": "read", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJ[CII)V"}, {"nme": "read", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJ[III)V"}, {"nme": "read", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJ[JII)V"}, {"nme": "read", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJ[FII)V"}, {"nme": "read", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJ[DII)V"}, {"nme": "write", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJ[BII)V"}, {"nme": "write", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJ[SII)V"}, {"nme": "write", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJ[CII)V"}, {"nme": "write", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJ[III)V"}, {"nme": "write", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJ[JII)V"}, {"nme": "write", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJ[FII)V"}, {"nme": "write", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJ[DII)V"}, {"nme": "getByte", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJ)B"}, {"nme": "getChar", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJ)C"}, {"nme": "getShort", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJ)S"}, {"nme": "getInt", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJ)I"}, {"nme": "getLong", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJ)J"}, {"nme": "getFloat", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJ)F"}, {"nme": "getDouble", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJ)D"}, {"nme": "getPointer", "acc": 8, "dsc": "(J)Lcom/sun/jna/Pointer;"}, {"nme": "_getPointer", "acc": 266, "dsc": "(J)J"}, {"nme": "getWideString", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJ)Ljava/lang/String;"}, {"nme": "getString", "acc": 8, "dsc": "(Lcom/sun/jna/Pointer;J)Ljava/lang/String;"}, {"nme": "getString", "acc": 8, "dsc": "(Lcom/sun/jna/Pointer;J<PERSON>java/lang/String;)Ljava/lang/String;"}, {"nme": "getStringBytes", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJ)[B"}, {"nme": "set<PERSON><PERSON>ory", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJJB)V"}, {"nme": "setByte", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJB)V"}, {"nme": "setShort", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJS)V"}, {"nme": "setChar", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJC)V"}, {"nme": "setInt", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJI)V"}, {"nme": "setLong", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJJ)V"}, {"nme": "setFloat", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJF)V"}, {"nme": "setDouble", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJD)V"}, {"nme": "setPointer", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJJ)V"}, {"nme": "setWideString", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJLjava/lang/String;)V"}, {"nme": "getDirectByteBuffer", "acc": 264, "dsc": "(Lcom/sun/jna/Pointer;JJJ)Ljava/nio/ByteBuffer;"}, {"nme": "malloc", "acc": 265, "dsc": "(J)J"}, {"nme": "free", "acc": 265, "dsc": "(J)V"}, {"nme": "getDirectByteBuffer", "acc": 131337, "dsc": "(JJ)Ljava/nio/ByteBuffer;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "detach", "acc": 9, "dsc": "(Z)V"}, {"nme": "getTerminationFlag", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Thread;)Lcom/sun/jna/Pointer;"}, {"nme": "setDetachState", "acc": 266, "dsc": "(ZJ)V"}, {"nme": "access$000", "acc": 4104, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "DEFAULT_ENCODING", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "DEBUG_LOAD", "dsc": "Z"}, {"acc": 9, "nme": "DEBUG_JNA_LOAD", "dsc": "Z"}, {"acc": 8, "nme": "jnidispatch<PERSON>ath", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "typeOptions", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Class<*>;Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;>;"}, {"acc": 26, "nme": "libraries", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Class<*>;Ljava/lang/ref/Reference<*>;>;"}, {"acc": 26, "nme": "_OPTION_ENCLOSING_LIBRARY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "enclosing-library"}, {"acc": 26, "nme": "DEFAULT_HANDLER", "dsc": "Lcom/sun/jna/Callback$UncaughtExceptionHandler;"}, {"acc": 10, "nme": "callback<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dsc": "Lcom/sun/jna/Callback$UncaughtExceptionHandler;"}, {"acc": 25, "nme": "POINTER_SIZE", "dsc": "I"}, {"acc": 25, "nme": "LONG_SIZE", "dsc": "I"}, {"acc": 25, "nme": "WCHAR_SIZE", "dsc": "I"}, {"acc": 25, "nme": "SIZE_T_SIZE", "dsc": "I"}, {"acc": 25, "nme": "BOOL_SIZE", "dsc": "I"}, {"acc": 26, "nme": "TYPE_VOIDP", "dsc": "I", "val": 0}, {"acc": 26, "nme": "TYPE_LONG", "dsc": "I", "val": 1}, {"acc": 26, "nme": "TYPE_WCHAR_T", "dsc": "I", "val": 2}, {"acc": 26, "nme": "TYPE_SIZE_T", "dsc": "I", "val": 3}, {"acc": 26, "nme": "TYPE_BOOL", "dsc": "I", "val": 4}, {"acc": 24, "nme": "MAX_ALIGNMENT", "dsc": "I"}, {"acc": 24, "nme": "MAX_PADDING", "dsc": "I"}, {"acc": 26, "nme": "finalizer", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 24, "nme": "JNA_TMPLIB_PREFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jna"}, {"acc": 10, "nme": "registeredClasses", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Class<*>;[J>;"}, {"acc": 10, "nme": "registeredLibraries", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Class<*>;Lcom/sun/jna/NativeLibrary;>;"}, {"acc": 24, "nme": "CB_HAS_INITIALIZER", "dsc": "I", "val": 1}, {"acc": 26, "nme": "CVT_UNSUPPORTED", "dsc": "I", "val": -1}, {"acc": 26, "nme": "CVT_DEFAULT", "dsc": "I", "val": 0}, {"acc": 26, "nme": "CVT_POINTER", "dsc": "I", "val": 1}, {"acc": 26, "nme": "CVT_STRING", "dsc": "I", "val": 2}, {"acc": 26, "nme": "CVT_STRUCTURE", "dsc": "I", "val": 3}, {"acc": 26, "nme": "CVT_STRUCTURE_BYVAL", "dsc": "I", "val": 4}, {"acc": 26, "nme": "CVT_BUFFER", "dsc": "I", "val": 5}, {"acc": 26, "nme": "CVT_ARRAY_BYTE", "dsc": "I", "val": 6}, {"acc": 26, "nme": "CVT_ARRAY_SHORT", "dsc": "I", "val": 7}, {"acc": 26, "nme": "CVT_ARRAY_CHAR", "dsc": "I", "val": 8}, {"acc": 26, "nme": "CVT_ARRAY_INT", "dsc": "I", "val": 9}, {"acc": 26, "nme": "CVT_ARRAY_LONG", "dsc": "I", "val": 10}, {"acc": 26, "nme": "CVT_ARRAY_FLOAT", "dsc": "I", "val": 11}, {"acc": 26, "nme": "CVT_ARRAY_DOUBLE", "dsc": "I", "val": 12}, {"acc": 26, "nme": "CVT_ARRAY_BOOLEAN", "dsc": "I", "val": 13}, {"acc": 26, "nme": "CVT_BOOLEAN", "dsc": "I", "val": 14}, {"acc": 26, "nme": "CVT_CALLBACK", "dsc": "I", "val": 15}, {"acc": 26, "nme": "CVT_FLOAT", "dsc": "I", "val": 16}, {"acc": 26, "nme": "CVT_NATIVE_MAPPED", "dsc": "I", "val": 17}, {"acc": 26, "nme": "CVT_NATIVE_MAPPED_STRING", "dsc": "I", "val": 18}, {"acc": 26, "nme": "CVT_NATIVE_MAPPED_WSTRING", "dsc": "I", "val": 19}, {"acc": 26, "nme": "CVT_WSTRING", "dsc": "I", "val": 20}, {"acc": 26, "nme": "CVT_INTEGER_TYPE", "dsc": "I", "val": 21}, {"acc": 26, "nme": "CVT_POINTER_TYPE", "dsc": "I", "val": 22}, {"acc": 26, "nme": "CVT_TYPE_MAPPER", "dsc": "I", "val": 23}, {"acc": 26, "nme": "CVT_TYPE_MAPPER_STRING", "dsc": "I", "val": 24}, {"acc": 26, "nme": "CVT_TYPE_MAPPER_WSTRING", "dsc": "I", "val": 25}, {"acc": 24, "nme": "CB_OPTION_DIRECT", "dsc": "I", "val": 1}, {"acc": 24, "nme": "CB_OPTION_IN_DLL", "dsc": "I", "val": 2}, {"acc": 26, "nme": "nativeThreadTerminationFlag", "dsc": "<PERSON><PERSON><PERSON>/lang/ThreadLocal;", "sig": "Ljava/lang/ThreadLocal<Lcom/sun/jna/Memory;>;"}, {"acc": 26, "nme": "nativeThreads", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Thread;Lcom/sun/jna/Pointer;>;"}]}, "com/sun/jna/InvocationMapper.class": {"ver": 50, "acc": 1537, "nme": "com/sun/jna/InvocationMapper", "super": "java/lang/Object", "mthds": [{"nme": "getInvocationHandler", "acc": 1025, "dsc": "(Lcom/sun/jna/NativeLibrary;Ljava/lang/reflect/Method;)Ljava/lang/reflect/InvocationHandler;"}], "flds": []}, "com/sun/jna/ptr/NativeLongByReference.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/ptr/NativeLongByReference", "super": "com/sun/jna/ptr/ByReference", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lcom/sun/jna/NativeLong;)V"}, {"nme": "setValue", "acc": 1, "dsc": "(Lcom/sun/jna/NativeLong;)V"}, {"nme": "getValue", "acc": 1, "dsc": "()Lcom/sun/jna/NativeLong;"}], "flds": []}, "com/sun/jna/FromNativeConverter.class": {"ver": 50, "acc": 1537, "nme": "com/sun/jna/FromNativeConverter", "super": "java/lang/Object", "mthds": [{"nme": "fromNative", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lcom/sun/jna/FromNativeContext;)Ljava/lang/Object;"}, {"nme": "nativeType", "acc": 1025, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<*>;"}], "flds": []}, "com/sun/jna/WeakMemoryHolder.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/WeakMemoryHolder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "put", "acc": 33, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lcom/sun/jna/Memory;)V"}, {"nme": "clean", "acc": 33, "dsc": "()V"}], "flds": [{"acc": 0, "nme": "referenceQueue", "dsc": "<PERSON><PERSON><PERSON>/lang/ref/ReferenceQueue;", "sig": "Ljava/lang/ref/ReferenceQueue<Ljava/lang/Object;>;"}, {"acc": 0, "nme": "backingMap", "dsc": "Ljava/util/IdentityHashMap;", "sig": "Ljava/util/IdentityHashMap<Ljava/lang/ref/Reference<Ljava/lang/Object;>;Lcom/sun/jna/Memory;>;"}]}, "com/sun/jna/NativeLibrary$2.class": {"ver": 50, "acc": 48, "nme": "com/sun/jna/NativeLibrary$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "accept", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;Lja<PERSON>/lang/String;)Z"}], "flds": [{"acc": 4112, "nme": "val$libName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "com/sun/jna/win32/W32APITypeMapper.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/win32/W32APITypeMapper", "super": "com/sun/jna/DefaultTypeMapper", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Z)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "UNICODE", "dsc": "Lcom/sun/jna/TypeMapper;"}, {"acc": 25, "nme": "ASCII", "dsc": "Lcom/sun/jna/TypeMapper;"}, {"acc": 25, "nme": "DEFAULT", "dsc": "Lcom/sun/jna/TypeMapper;"}]}, "com/sun/jna/Platform.class": {"ver": 50, "acc": 49, "nme": "com/sun/jna/Platform", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getOSType", "acc": 25, "dsc": "()I"}, {"nme": "isMac", "acc": 25, "dsc": "()Z"}, {"nme": "isAndroid", "acc": 25, "dsc": "()Z"}, {"nme": "isLinux", "acc": 25, "dsc": "()Z"}, {"nme": "isAIX", "acc": 25, "dsc": "()Z"}, {"nme": "isAix", "acc": 131097, "dsc": "()Z"}, {"nme": "isWindowsCE", "acc": 25, "dsc": "()Z"}, {"nme": "isWindows", "acc": 25, "dsc": "()Z"}, {"nme": "isSolaris", "acc": 25, "dsc": "()Z"}, {"nme": "isFreeBSD", "acc": 25, "dsc": "()Z"}, {"nme": "isOpenBSD", "acc": 25, "dsc": "()Z"}, {"nme": "isNetBSD", "acc": 25, "dsc": "()Z"}, {"nme": "isGNU", "acc": 25, "dsc": "()Z"}, {"nme": "iskFreeBSD", "acc": 25, "dsc": "()Z"}, {"nme": "isX11", "acc": 25, "dsc": "()Z"}, {"nme": "hasRuntimeExec", "acc": 25, "dsc": "()Z"}, {"nme": "is64Bit", "acc": 25, "dsc": "()Z"}, {"nme": "isIntel", "acc": 25, "dsc": "()Z"}, {"nme": "isPPC", "acc": 25, "dsc": "()Z"}, {"nme": "isARM", "acc": 25, "dsc": "()Z"}, {"nme": "isSPARC", "acc": 25, "dsc": "()Z"}, {"nme": "getCanonicalArchitecture", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)Ljava/lang/String;"}, {"nme": "isSoftFloat", "acc": 10, "dsc": "()Z"}, {"nme": "getNativeLibraryResourcePrefix", "acc": 8, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getNativeLibraryResourcePrefix", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getNativeLibraryResourcePrefix", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Z)Ljava/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "UNSPECIFIED", "dsc": "I", "val": -1}, {"acc": 25, "nme": "MAC", "dsc": "I", "val": 0}, {"acc": 25, "nme": "LINUX", "dsc": "I", "val": 1}, {"acc": 25, "nme": "WINDOWS", "dsc": "I", "val": 2}, {"acc": 25, "nme": "SOLARIS", "dsc": "I", "val": 3}, {"acc": 25, "nme": "FREEBSD", "dsc": "I", "val": 4}, {"acc": 25, "nme": "OPENBSD", "dsc": "I", "val": 5}, {"acc": 25, "nme": "WINDOWSCE", "dsc": "I", "val": 6}, {"acc": 25, "nme": "AIX", "dsc": "I", "val": 7}, {"acc": 25, "nme": "ANDROID", "dsc": "I", "val": 8}, {"acc": 25, "nme": "GNU", "dsc": "I", "val": 9}, {"acc": 25, "nme": "KFREEBSD", "dsc": "I", "val": 10}, {"acc": 25, "nme": "NETBSD", "dsc": "I", "val": 11}, {"acc": 25, "nme": "RO_FIELDS", "dsc": "Z"}, {"acc": 25, "nme": "HAS_BUFFERS", "dsc": "Z"}, {"acc": 25, "nme": "HAS_AWT", "dsc": "Z"}, {"acc": 25, "nme": "HAS_JAWT", "dsc": "Z"}, {"acc": 25, "nme": "MATH_LIBRARY_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 25, "nme": "C_LIBRARY_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 25, "nme": "HAS_DLL_CALLBACKS", "dsc": "Z"}, {"acc": 25, "nme": "RESOURCE_PREFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "osType", "dsc": "I"}, {"acc": 25, "nme": "ARCH", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "com/sun/jna/LastErrorException.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/LastErrorException", "super": "java/lang/RuntimeException", "mthds": [{"nme": "formatMessage", "acc": 10, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "parseMessage", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getErrorCode", "acc": 1, "dsc": "()I"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(I)V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}, {"acc": 2, "nme": "errorCode", "dsc": "I"}]}, "com/sun/jna/Library$Handler.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/Library$Handler", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Class;Ljava/util/Map;)V", "sig": "(Lja<PERSON>/lang/String;Ljava/lang/Class<*>;Ljava/util/Map<Ljava/lang/String;*>;)V"}, {"nme": "getNativeLibrary", "acc": 1, "dsc": "()Lcom/sun/jna/NativeLibrary;"}, {"nme": "getLibraryName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getInterfaceClass", "acc": 1, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<*>;"}, {"nme": "invoke", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/reflect/Method;[Ljava/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Throwable"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "OBJECT_TOSTRING", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 24, "nme": "OBJECT_HASHCODE", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 24, "nme": "OBJECT_EQUALS", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 18, "nme": "nativeLibrary", "dsc": "Lcom/sun/jna/NativeLibrary;"}, {"acc": 18, "nme": "interfaceClass", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 18, "nme": "options", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;"}, {"acc": 18, "nme": "invocationMapper", "dsc": "Lcom/sun/jna/InvocationMapper;"}, {"acc": 18, "nme": "functions", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/reflect/Method;Lcom/sun/jna/Library$Handler$FunctionInfo;>;"}]}, "com/sun/jna/win32/W32APITypeMapper$2.class": {"ver": 50, "acc": 32, "nme": "com/sun/jna/win32/W32APITypeMapper$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/sun/jna/win32/W32APITypeMapper;)V"}, {"nme": "toNative", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lcom/sun/jna/ToNativeContext;)Ljava/lang/Object;"}, {"nme": "fromNative", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lcom/sun/jna/FromNativeContext;)Ljava/lang/Object;"}, {"nme": "nativeType", "acc": 1, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<*>;"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lcom/sun/jna/win32/W32APITypeMapper;"}]}, "com/sun/jna/FunctionMapper.class": {"ver": 50, "acc": 1537, "nme": "com/sun/jna/FunctionMapper", "super": "java/lang/Object", "mthds": [{"nme": "getFunctionName", "acc": 1025, "dsc": "(Lcom/sun/jna/NativeLibrary;Ljava/lang/reflect/Method;)Ljava/lang/String;"}], "flds": []}, "com/sun/jna/win32/W32APIOptions$1.class": {"ver": 50, "acc": 48, "nme": "com/sun/jna/win32/W32APIOptions$1", "super": "java/util/HashMap", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}]}, "com/sun/jna/VarArgsChecker$NoVarArgsChecker.class": {"ver": 50, "acc": 48, "nme": "com/sun/jna/VarArgsChecker$NoVarArgsChecker", "super": "com/sun/jna/<PERSON>ar<PERSON>rgs<PERSON>hecker", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "isVarArgs", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)Z"}, {"nme": "fixedArgs", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)I"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lcom/sun/jna/VarArgsChecker$1;)V"}], "flds": []}, "com/sun/jna/Native$1.class": {"ver": 50, "acc": 48, "nme": "com/sun/jna/Native$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "uncaughtException", "acc": 1, "dsc": "(Lcom/sun/jna/Callback;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}], "flds": []}, "com/sun/jna/PointerType.class": {"ver": 50, "acc": 1057, "nme": "com/sun/jna/PointerType", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "<init>", "acc": 4, "dsc": "(Lcom/sun/jna/Pointer;)V"}, {"nme": "nativeType", "acc": 1, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<*>;"}, {"nme": "toNative", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getPointer", "acc": 1, "dsc": "()Lcom/sun/jna/Pointer;"}, {"nme": "setPointer", "acc": 1, "dsc": "(Lcom/sun/jna/Pointer;)V"}, {"nme": "fromNative", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lcom/sun/jna/FromNativeContext;)Ljava/lang/Object;"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "pointer", "dsc": "Lcom/sun/jna/Pointer;"}]}, "com/sun/jna/DefaultTypeMapper.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/DefaultTypeMapper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getAltClass", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/Class;", "sig": "(Lja<PERSON>/lang/Class<*>;)Ljava/lang/Class<*>;"}, {"nme": "addToNativeConverter", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Lcom/sun/jna/ToNativeConverter;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;Lcom/sun/jna/ToNativeConverter;)V"}, {"nme": "addFromNativeConverter", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Lcom/sun/jna/FromNativeConverter;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;Lcom/sun/jna/FromNativeConverter;)V"}, {"nme": "addTypeConverter", "acc": 1, "dsc": "(Ljava/lang/Class;Lcom/sun/jna/TypeConverter;)V", "sig": "(Lja<PERSON>/lang/Class<*>;Lcom/sun/jna/TypeConverter;)V"}, {"nme": "lookupConverter", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/util/Collection;)Lja<PERSON>/lang/Object;", "sig": "(Ljava/lang/Class<*>;Ljava/util/Collection<+Lcom/sun/jna/DefaultTypeMapper$Entry;>;)Ljava/lang/Object;"}, {"nme": "getFromNativeConverter", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Lcom/sun/jna/FromNativeConverter;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Lcom/sun/jna/FromNativeConverter;"}, {"nme": "getToNativeConverter", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Lcom/sun/jna/ToNativeConverter;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Lcom/sun/jna/ToNativeConverter;"}], "flds": [{"acc": 2, "nme": "toNativeConverters", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lcom/sun/jna/DefaultTypeMapper$Entry;>;"}, {"acc": 2, "nme": "fromNativeConverters", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lcom/sun/jna/DefaultTypeMapper$Entry;>;"}]}, "com/sun/jna/StructureWriteContext.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/StructureWriteContext", "super": "com/sun/jna/ToNativeContext", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/sun/jna/Structure;Ljava/lang/reflect/Field;)V"}, {"nme": "getStructure", "acc": 1, "dsc": "()Lcom/sun/jna/Structure;"}, {"nme": "getField", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/reflect/Field;"}], "flds": [{"acc": 2, "nme": "struct", "dsc": "Lcom/sun/jna/Structure;"}, {"acc": 2, "nme": "field", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Field;"}]}, "com/sun/jna/ptr/ByteByReference.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/ptr/ByteByReference", "super": "com/sun/jna/ptr/ByReference", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(B)V"}, {"nme": "setValue", "acc": 1, "dsc": "(B)V"}, {"nme": "getValue", "acc": 1, "dsc": "()B"}], "flds": []}, "com/sun/jna/win32/DLLCallback.class": {"ver": 50, "acc": 1537, "nme": "com/sun/jna/win32/DLLCallback", "super": "java/lang/Object", "mthds": [], "flds": [{"acc": 25, "nme": "DLL_FPTRS", "dsc": "I", "val": 16}]}, "com/sun/jna/CallbackReference$DefaultCallbackProxy.class": {"ver": 50, "acc": 32, "nme": "com/sun/jna/CallbackReference$DefaultCallbackProxy", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/sun/jna/CallbackReference;Ljava/lang/reflect/Method;Lcom/sun/jna/TypeMapper;Ljava/lang/String;)V"}, {"nme": "get<PERSON>allback", "acc": 1, "dsc": "()Lcom/sun/jna/Callback;"}, {"nme": "invokeCallback", "acc": 2, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "callback", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "convertArgument", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Class;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/Object;L<PERSON><PERSON>/lang/Class<*>;)Ljava/lang/Object;"}, {"nme": "convertResult", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getParameterTypes", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/Class;", "sig": "()[<PERSON><PERSON><PERSON>/lang/Class<*>;"}, {"nme": "getReturnType", "acc": 1, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<*>;"}], "flds": [{"acc": 18, "nme": "callback<PERSON><PERSON><PERSON>", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 2, "nme": "toNative", "dsc": "Lcom/sun/jna/ToNativeConverter;"}, {"acc": 18, "nme": "fromNative", "dsc": "[Lcom/sun/jna/FromNativeConverter;"}, {"acc": 18, "nme": "encoding", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lcom/sun/jna/CallbackReference;"}]}, "com/sun/jna/ptr/IntByReference.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/ptr/IntByReference", "super": "com/sun/jna/ptr/ByReference", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(I)V"}, {"nme": "setValue", "acc": 1, "dsc": "(I)V"}, {"nme": "getValue", "acc": 1, "dsc": "()I"}], "flds": []}, "com/sun/jna/Function.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/Function", "super": "com/sun/jna/Pointer", "mthds": [{"nme": "getFunction", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;)Lcom/sun/jna/Function;"}, {"nme": "getFunction", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>java/lang/String;I)Lcom/sun/jna/Function;"}, {"nme": "getFunction", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;<PERSON>java/lang/String;)Lcom/sun/jna/Function;"}, {"nme": "getFunction", "acc": 9, "dsc": "(Lcom/sun/jna/Pointer;)Lcom/sun/jna/Function;"}, {"nme": "getFunction", "acc": 9, "dsc": "(Lcom/sun/jna/Pointer;I)Lcom/sun/jna/Function;"}, {"nme": "getFunction", "acc": 9, "dsc": "(Lcom/sun/jna/Pointer;ILjava/lang/String;)Lcom/sun/jna/Function;"}, {"nme": "<init>", "acc": 0, "dsc": "(Lcom/sun/jna/NativeLibrary;Ljava/lang/String;ILjava/lang/String;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(Lcom/sun/jna/Pointer;ILjava/lang/String;)V"}, {"nme": "checkCallingConvention", "acc": 2, "dsc": "(I)V", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getCallingConvention", "acc": 1, "dsc": "()I"}, {"nme": "invoke", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;[<PERSON>ja<PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;[Ljava/lang/Object;)Ljava/lang/Object;"}, {"nme": "invoke", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;[Lja<PERSON>/lang/Object;Ljava/util/Map;)Ljava/lang/Object;", "sig": "(Lja<PERSON>/lang/Class<*>;[Ljava/lang/Object;Ljava/util/Map<Ljava/lang/String;*>;)Ljava/lang/Object;"}, {"nme": "invoke", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;[Ljava/lang/Class;Ljava/lang/Class;[Ljava/lang/Object;Ljava/util/Map;)Ljava/lang/Object;", "sig": "(Lja<PERSON>/lang/reflect/Method;[Ljava/lang/Class<*>;Ljava/lang/Class<*>;[Ljava/lang/Object;Ljava/util/Map<Ljava/lang/String;*>;)Ljava/lang/Object;"}, {"nme": "invoke", "acc": 0, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Class;Z)Ljava/lang/Object;", "sig": "([<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Class<*>;Z)Ljava/lang/Object;"}, {"nme": "invoke", "acc": 0, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Class;ZI)Ljava/lang/Object;", "sig": "([<PERSON><PERSON><PERSON>/lang/Object;Lja<PERSON>/lang/Class<*>;ZI)Ljava/lang/Object;"}, {"nme": "invokePointer", "acc": 2, "dsc": "(I[<PERSON><PERSON><PERSON>/lang/Object;)Lcom/sun/jna/Pointer;"}, {"nme": "convertArgument", "acc": 2, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;ILjava/lang/reflect/Method;Lcom/sun/jna/TypeMapper;ZLjava/lang/Class;)Ljava/lang/Object;", "sig": "([<PERSON><PERSON><PERSON>/lang/Object;ILjava/lang/reflect/Method;Lcom/sun/jna/TypeMapper;ZLjava/lang/Class<*>;)Ljava/lang/Object;"}, {"nme": "isPrimitiveArray", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "invoke", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "invokeString", "acc": 2, "dsc": "(I[<PERSON><PERSON><PERSON>/lang/Object;Z)Ljava/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "invokeObject", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "invokePointer", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;)Lcom/sun/jna/Pointer;"}, {"nme": "invokeString", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;Z)Ljava/lang/String;"}, {"nme": "invokeInt", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;)I"}, {"nme": "invokeLong", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;)J"}, {"nme": "invokeFloat", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;)F"}, {"nme": "invokeDouble", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;)D"}, {"nme": "invokeVoid", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "concatenateVarArgs", "acc": 8, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;)[<PERSON>ja<PERSON>/lang/Object;"}, {"nme": "isVarArgs", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)Z"}, {"nme": "fixedArgs", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)I"}, {"nme": "valueOf", "acc": 8, "dsc": "(Z)<PERSON><PERSON><PERSON>/lang/<PERSON>;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "MAX_NARGS", "dsc": "I", "val": 256}, {"acc": 25, "nme": "C_CONVENTION", "dsc": "I", "val": 0}, {"acc": 25, "nme": "ALT_CONVENTION", "dsc": "I", "val": 63}, {"acc": 26, "nme": "MASK_CC", "dsc": "I", "val": 63}, {"acc": 25, "nme": "THROW_LAST_ERROR", "dsc": "I", "val": 64}, {"acc": 25, "nme": "USE_VARARGS", "dsc": "I", "val": 384}, {"acc": 24, "nme": "INTEGER_TRUE", "dsc": "<PERSON><PERSON><PERSON>/lang/Integer;"}, {"acc": 24, "nme": "INTEGER_FALSE", "dsc": "<PERSON><PERSON><PERSON>/lang/Integer;"}, {"acc": 2, "nme": "library", "dsc": "Lcom/sun/jna/NativeLibrary;"}, {"acc": 18, "nme": "functionName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 16, "nme": "encoding", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 16, "nme": "callFlags", "dsc": "I"}, {"acc": 16, "nme": "options", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;*>;"}, {"acc": 24, "nme": "OPTION_INVOKING_METHOD", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "invoking-method"}, {"acc": 26, "nme": "IS_VARARGS", "dsc": "Lcom/sun/jna/Var<PERSON>rgs<PERSON>hecker;"}]}, "com/sun/jna/Structure$FFIType$FFITypes.class": {"ver": 50, "acc": 32, "nme": "com/sun/jna/Structure$FFIType$FFITypes", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "access$900", "acc": 4104, "dsc": "()Lcom/sun/jna/Pointer;"}, {"nme": "access$1000", "acc": 4104, "dsc": "()Lcom/sun/jna/Pointer;"}, {"nme": "access$1100", "acc": 4104, "dsc": "()Lcom/sun/jna/Pointer;"}, {"nme": "access$1200", "acc": 4104, "dsc": "()Lcom/sun/jna/Pointer;"}, {"nme": "access$1300", "acc": 4104, "dsc": "()Lcom/sun/jna/Pointer;"}, {"nme": "access$1400", "acc": 4104, "dsc": "()Lcom/sun/jna/Pointer;"}, {"nme": "access$1500", "acc": 4104, "dsc": "()Lcom/sun/jna/Pointer;"}, {"nme": "access$1600", "acc": 4104, "dsc": "()Lcom/sun/jna/Pointer;"}, {"nme": "access$1700", "acc": 4104, "dsc": "()Lcom/sun/jna/Pointer;"}, {"nme": "access$1800", "acc": 4104, "dsc": "()Lcom/sun/jna/Pointer;"}], "flds": [{"acc": 10, "nme": "ffi_type_void", "dsc": "Lcom/sun/jna/Pointer;"}, {"acc": 10, "nme": "ffi_type_float", "dsc": "Lcom/sun/jna/Pointer;"}, {"acc": 10, "nme": "ffi_type_double", "dsc": "Lcom/sun/jna/Pointer;"}, {"acc": 10, "nme": "ffi_type_longdouble", "dsc": "Lcom/sun/jna/Pointer;"}, {"acc": 10, "nme": "ffi_type_uint8", "dsc": "Lcom/sun/jna/Pointer;"}, {"acc": 10, "nme": "ffi_type_sint8", "dsc": "Lcom/sun/jna/Pointer;"}, {"acc": 10, "nme": "ffi_type_uint16", "dsc": "Lcom/sun/jna/Pointer;"}, {"acc": 10, "nme": "ffi_type_sint16", "dsc": "Lcom/sun/jna/Pointer;"}, {"acc": 10, "nme": "ffi_type_uint32", "dsc": "Lcom/sun/jna/Pointer;"}, {"acc": 10, "nme": "ffi_type_sint32", "dsc": "Lcom/sun/jna/Pointer;"}, {"acc": 10, "nme": "ffi_type_uint64", "dsc": "Lcom/sun/jna/Pointer;"}, {"acc": 10, "nme": "ffi_type_sint64", "dsc": "Lcom/sun/jna/Pointer;"}, {"acc": 10, "nme": "ffi_type_pointer", "dsc": "Lcom/sun/jna/Pointer;"}]}, "com/sun/jna/NativeString.class": {"ver": 50, "acc": 32, "nme": "com/sun/jna/NativeString", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lcom/sun/jna/WString;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getPointer", "acc": 1, "dsc": "()Lcom/sun/jna/Pointer;"}, {"nme": "char<PERSON>t", "acc": 1, "dsc": "(I)C"}, {"nme": "length", "acc": 1, "dsc": "()I"}, {"nme": "subSequence", "acc": 1, "dsc": "(II)<PERSON><PERSON><PERSON>/lang/CharSequence;"}, {"nme": "compareTo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}], "flds": [{"acc": 24, "nme": "WIDE_STRING", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "--WIDE-STRING--"}, {"acc": 2, "nme": "pointer", "dsc": "Lcom/sun/jna/Pointer;"}, {"acc": 2, "nme": "encoding", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "com/sun/jna/FunctionResultContext.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/FunctionResultContext", "super": "com/sun/jna/FromNativeContext", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lja<PERSON>/lang/Class;Lcom/sun/jna/Function;[Ljava/lang/Object;)V", "sig": "(Ljava/lang/Class<*>;Lcom/sun/jna/Function;[Ljava/lang/Object;)V"}, {"nme": "getFunction", "acc": 1, "dsc": "()Lcom/sun/jna/Function;"}, {"nme": "getArguments", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 2, "nme": "function", "dsc": "Lcom/sun/jna/Function;"}, {"acc": 2, "nme": "args", "dsc": "[<PERSON><PERSON><PERSON>/lang/Object;"}]}, "com/sun/jna/TypeConverter.class": {"ver": 50, "acc": 1537, "nme": "com/sun/jna/TypeConverter", "super": "java/lang/Object", "mthds": [], "flds": []}, "com/sun/jna/Pointer$Opaque.class": {"ver": 50, "acc": 32, "nme": "com/sun/jna/Pointer$Opaque", "super": "com/sun/jna/Pointer", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(J)V"}, {"nme": "share", "acc": 1, "dsc": "(JJ)Lcom/sun/jna/Pointer;"}, {"nme": "clear", "acc": 1, "dsc": "(J)V"}, {"nme": "indexOf", "acc": 1, "dsc": "(JB)J"}, {"nme": "read", "acc": 1, "dsc": "(J[BII)V"}, {"nme": "read", "acc": 1, "dsc": "(J[CII)V"}, {"nme": "read", "acc": 1, "dsc": "(J[SII)V"}, {"nme": "read", "acc": 1, "dsc": "(J[III)V"}, {"nme": "read", "acc": 1, "dsc": "(J[JII)V"}, {"nme": "read", "acc": 1, "dsc": "(J[FII)V"}, {"nme": "read", "acc": 1, "dsc": "(J[DII)V"}, {"nme": "read", "acc": 1, "dsc": "(J[Lcom/sun/jna/Pointer;II)V"}, {"nme": "write", "acc": 1, "dsc": "(J[BII)V"}, {"nme": "write", "acc": 1, "dsc": "(J[CII)V"}, {"nme": "write", "acc": 1, "dsc": "(J[SII)V"}, {"nme": "write", "acc": 1, "dsc": "(J[III)V"}, {"nme": "write", "acc": 1, "dsc": "(J[JII)V"}, {"nme": "write", "acc": 1, "dsc": "(J[FII)V"}, {"nme": "write", "acc": 1, "dsc": "(J[DII)V"}, {"nme": "write", "acc": 1, "dsc": "(J[Lcom/sun/jna/Pointer;II)V"}, {"nme": "getByteBuffer", "acc": 1, "dsc": "(JJ)Ljava/nio/ByteBuffer;"}, {"nme": "getByte", "acc": 1, "dsc": "(J)B"}, {"nme": "getChar", "acc": 1, "dsc": "(J)C"}, {"nme": "getShort", "acc": 1, "dsc": "(J)S"}, {"nme": "getInt", "acc": 1, "dsc": "(J)I"}, {"nme": "getLong", "acc": 1, "dsc": "(J)J"}, {"nme": "getFloat", "acc": 1, "dsc": "(J)F"}, {"nme": "getDouble", "acc": 1, "dsc": "(J)D"}, {"nme": "getPointer", "acc": 1, "dsc": "(J)Lcom/sun/jna/Pointer;"}, {"nme": "getString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getWideString", "acc": 1, "dsc": "(J)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setByte", "acc": 1, "dsc": "(JB)V"}, {"nme": "setChar", "acc": 1, "dsc": "(JC)V"}, {"nme": "setShort", "acc": 1, "dsc": "(JS)V"}, {"nme": "setInt", "acc": 1, "dsc": "(JI)V"}, {"nme": "setLong", "acc": 1, "dsc": "(JJ)V"}, {"nme": "setFloat", "acc": 1, "dsc": "(JF)V"}, {"nme": "setDouble", "acc": 1, "dsc": "(JD)V"}, {"nme": "setPointer", "acc": 1, "dsc": "(JLcom/sun/jna/Pointer;)V"}, {"nme": "setString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setWideString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "set<PERSON><PERSON>ory", "acc": 1, "dsc": "(JJB)V"}, {"nme": "dump", "acc": 1, "dsc": "(JI)Ljava/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<init>", "acc": 4096, "dsc": "(JLcom/sun/jna/Pointer$1;)V"}], "flds": [{"acc": 18, "nme": "MSG", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "com/sun/jna/MethodParameterContext.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/MethodParameterContext", "super": "com/sun/jna/FunctionParameterContext", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/sun/jna/Function;[<PERSON><PERSON><PERSON>/lang/Object;ILjava/lang/reflect/Method;)V"}, {"nme": "getMethod", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/reflect/Method;"}], "flds": [{"acc": 2, "nme": "method", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}]}, "com/sun/jna/NativeString$StringMemory.class": {"ver": 50, "acc": 32, "nme": "com/sun/jna/NativeString$StringMemory", "super": "com/sun/jna/Memory", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/sun/jna/NativeString;J)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lcom/sun/jna/NativeString;"}]}, "com/sun/jna/Structure$AutoAllocated.class": {"ver": 50, "acc": 32, "nme": "com/sun/jna/Structure$AutoAllocated", "super": "com/sun/jna/Memory", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(I)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "com/sun/jna/CallbackProxy.class": {"ver": 50, "acc": 1537, "nme": "com/sun/jna/CallbackProxy", "super": "java/lang/Object", "mthds": [{"nme": "callback", "acc": 1025, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getParameterTypes", "acc": 1025, "dsc": "()[<PERSON><PERSON><PERSON>/lang/Class;", "sig": "()[<PERSON><PERSON><PERSON>/lang/Class<*>;"}, {"nme": "getReturnType", "acc": 1025, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<*>;"}], "flds": []}, "com/sun/jna/win32/StdCallLibrary.class": {"ver": 50, "acc": 1537, "nme": "com/sun/jna/win32/StdCallLibrary", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "STDCALL_CONVENTION", "dsc": "I", "val": 63}, {"acc": 25, "nme": "FUNCTION_MAPPER", "dsc": "Lcom/sun/jna/FunctionMapper;"}]}, "com/sun/jna/NativeMapped.class": {"ver": 50, "acc": 1537, "nme": "com/sun/jna/NativeMapped", "super": "java/lang/Object", "mthds": [{"nme": "fromNative", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lcom/sun/jna/FromNativeContext;)Ljava/lang/Object;"}, {"nme": "toNative", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "nativeType", "acc": 1025, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<*>;"}], "flds": []}, "com/sun/jna/Structure$3.class": {"ver": 50, "acc": 48, "nme": "com/sun/jna/Structure$3", "super": "com/sun/jna/Pointer", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(J)V"}, {"nme": "share", "acc": 1, "dsc": "(JJ)Lcom/sun/jna/Pointer;"}], "flds": []}, "com/sun/jna/DefaultTypeMapper$Entry.class": {"ver": 50, "acc": 32, "nme": "com/sun/jna/DefaultTypeMapper$Entry", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/Object;)V", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/Object;)V"}], "flds": [{"acc": 1, "nme": "type", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 1, "nme": "converter", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "com/sun/jna/win32/StdCall.class": {"ver": 50, "acc": 1537, "nme": "com/sun/jna/win32/StdCall", "super": "java/lang/Object", "mthds": [], "flds": []}, "com/sun/jna/CallbackReference.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/CallbackReference", "super": "java/lang/ref/WeakReference", "mthds": [{"nme": "setCallbackThreadInitializer", "acc": 8, "dsc": "(Lcom/sun/jna/Callback;Lcom/sun/jna/CallbackThreadInitializer;)Lcom/sun/jna/CallbackThreadInitializer;"}, {"nme": "initializeThread", "acc": 10, "dsc": "(Lcom/sun/jna/Callback;Lcom/sun/jna/CallbackReference$AttachOptions;)Ljava/lang/ThreadGroup;"}, {"nme": "get<PERSON>allback", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Lcom/sun/jna/Pointer;)Lcom/sun/jna/Callback;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;Lcom/sun/jna/Pointer;)Lcom/sun/jna/Callback;"}, {"nme": "get<PERSON>allback", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Lcom/sun/jna/Pointer;Z)Lcom/sun/jna/Callback;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;Lcom/sun/jna/Pointer;Z)Lcom/sun/jna/Callback;"}, {"nme": "<init>", "acc": 2, "dsc": "(Lcom/sun/jna/Callback;IZ)V"}, {"nme": "getNativeType", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/Class;", "sig": "(Lja<PERSON>/lang/Class<*>;)Ljava/lang/Class<*>;"}, {"nme": "checkMethod", "acc": 10, "dsc": "(Lja<PERSON>/lang/reflect/Method;)Ljava/lang/reflect/Method;"}, {"nme": "findCallbackClass", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/Class;", "sig": "(Lja<PERSON>/lang/Class<*>;)Ljava/lang/Class<*>;"}, {"nme": "getCallbackMethod", "acc": 10, "dsc": "(Lcom/sun/jna/Callback;)Ljava/lang/reflect/Method;"}, {"nme": "getCallbackMethod", "acc": 10, "dsc": "(L<PERSON><PERSON>/lang/Class;)Ljava/lang/reflect/Method;", "sig": "(Lja<PERSON>/lang/Class<*>;)Ljava/lang/reflect/Method;"}, {"nme": "setCallbackOptions", "acc": 2, "dsc": "(I)V"}, {"nme": "getTrampoline", "acc": 1, "dsc": "()Lcom/sun/jna/Pointer;"}, {"nme": "finalize", "acc": 4, "dsc": "()V"}, {"nme": "dispose", "acc": 36, "dsc": "()V"}, {"nme": "disposeAll", "acc": 8, "dsc": "()V"}, {"nme": "get<PERSON>allback", "acc": 2, "dsc": "()Lcom/sun/jna/Callback;"}, {"nme": "getNativeFunctionPointer", "acc": 10, "dsc": "(Lcom/sun/jna/Callback;)Lcom/sun/jna/Pointer;"}, {"nme": "getFunctionPointer", "acc": 9, "dsc": "(Lcom/sun/jna/Callback;)Lcom/sun/jna/Pointer;"}, {"nme": "getFunctionPointer", "acc": 10, "dsc": "(Lcom/sun/jna/Callback;Z)Lcom/sun/jna/Pointer;"}, {"nme": "isAllowableNativeType", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "getNativeString", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Z)Lcom/sun/jna/Pointer;"}, {"nme": "access$000", "acc": 4104, "dsc": "(Lcom/sun/jna/CallbackReference;)Lcom/sun/jna/Callback;"}, {"nme": "access$100", "acc": 4104, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Z)Lcom/sun/jna/Pointer;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "callbackMap", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Lcom/sun/jna/Callback;Lcom/sun/jna/CallbackReference;>;"}, {"acc": 24, "nme": "directCallbackMap", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Lcom/sun/jna/Callback;Lcom/sun/jna/CallbackReference;>;"}, {"acc": 24, "nme": "pointerCallbackMap", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Lcom/sun/jna/Pointer;Ljava/lang/ref/Reference<Lcom/sun/jna/Callback;>;>;"}, {"acc": 24, "nme": "allocations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;"}, {"acc": 26, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Lcom/sun/jna/CallbackReference;Ljava/lang/ref/Reference<Lcom/sun/jna/CallbackReference;>;>;"}, {"acc": 26, "nme": "PROXY_CALLBACK_METHOD", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 26, "nme": "initializers", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Lcom/sun/jna/Callback;Lcom/sun/jna/CallbackThreadInitializer;>;"}, {"acc": 0, "nme": "cbstruct", "dsc": "Lcom/sun/jna/Pointer;"}, {"acc": 0, "nme": "trampoline", "dsc": "Lcom/sun/jna/Pointer;"}, {"acc": 0, "nme": "proxy", "dsc": "Lcom/sun/jna/CallbackProxy;"}, {"acc": 0, "nme": "method", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 0, "nme": "callingConvention", "dsc": "I"}]}, "com/sun/jna/win32/W32APITypeMapper$1.class": {"ver": 50, "acc": 32, "nme": "com/sun/jna/win32/W32APITypeMapper$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/sun/jna/win32/W32APITypeMapper;)V"}, {"nme": "toNative", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lcom/sun/jna/ToNativeContext;)Ljava/lang/Object;"}, {"nme": "fromNative", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lcom/sun/jna/FromNativeContext;)Ljava/lang/Object;"}, {"nme": "nativeType", "acc": 1, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<*>;"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lcom/sun/jna/win32/W32APITypeMapper;"}]}, "com/sun/jna/win32/W32APIFunctionMapper.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/win32/W32APIFunctionMapper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Z)V"}, {"nme": "getFunctionName", "acc": 1, "dsc": "(Lcom/sun/jna/NativeLibrary;Ljava/lang/reflect/Method;)Ljava/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "UNICODE", "dsc": "Lcom/sun/jna/FunctionMapper;"}, {"acc": 25, "nme": "ASCII", "dsc": "Lcom/sun/jna/FunctionMapper;"}, {"acc": 18, "nme": "suffix", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "com/sun/jna/Function$PointerArray.class": {"ver": 50, "acc": 32, "nme": "com/sun/jna/Function$PointerArray", "super": "com/sun/jna/Memory", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "([Lcom/sun/jna/Pointer;)V"}, {"nme": "read", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "original", "dsc": "[Lcom/sun/jna/Pointer;"}]}, "com/sun/jna/Structure$ByReference.class": {"ver": 50, "acc": 1537, "nme": "com/sun/jna/Structure$ByReference", "super": "java/lang/Object", "mthds": [], "flds": []}, "com/sun/jna/StringArray.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/StringArray", "super": "com/sun/jna/Memory", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;Z)V"}, {"nme": "<init>", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "([Lcom/sun/jna/WString;)V"}, {"nme": "<init>", "acc": 2, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;<PERSON>ja<PERSON>/lang/String;)V"}, {"nme": "read", "acc": 1, "dsc": "()V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "encoding", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "natives", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lcom/sun/jna/NativeString;>;"}, {"acc": 2, "nme": "original", "dsc": "[<PERSON><PERSON><PERSON>/lang/Object;"}]}, "com/sun/jna/Structure$ByValue.class": {"ver": 50, "acc": 1537, "nme": "com/sun/jna/Structure$ByValue", "super": "java/lang/Object", "mthds": [], "flds": []}, "com/sun/jna/Native$3.class": {"ver": 50, "acc": 48, "nme": "com/sun/jna/Native$3", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/sun/jna/Library$Handler;Lcom/sun/jna/Library;)V"}, {"nme": "invoke", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/reflect/Method;[Ljava/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Throwable"]}], "flds": [{"acc": 4112, "nme": "val$handler", "dsc": "Lcom/sun/jna/Library$Handler;"}, {"acc": 4112, "nme": "val$library", "dsc": "Lcom/sun/jna/Library;"}]}, "com/sun/jna/win32/W32APIOptions.class": {"ver": 50, "acc": 1537, "nme": "com/sun/jna/win32/W32APIOptions", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "UNICODE_OPTIONS", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;"}, {"acc": 25, "nme": "ASCII_OPTIONS", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;"}, {"acc": 25, "nme": "DEFAULT_OPTIONS", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;"}]}, "com/sun/jna/Native$6.class": {"ver": 50, "acc": 48, "nme": "com/sun/jna/Native$6", "super": "java/lang/SecurityManager", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "getClassContext", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/Class;", "sig": "()[<PERSON><PERSON><PERSON>/lang/Class<*>;"}], "flds": []}, "com/sun/jna/Native$AWT.class": {"ver": 50, "acc": 32, "nme": "com/sun/jna/Native$AWT", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getWindowID", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/awt/Window;)J", "exs": ["java/awt/HeadlessException"]}, {"nme": "getComponentID", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)J", "exs": ["java/awt/HeadlessException"]}], "flds": []}, "com/sun/jna/Structure.class": {"ver": 50, "acc": 1057, "nme": "com/sun/jna/Structure", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "<init>", "acc": 4, "dsc": "(Lcom/sun/jna/TypeMapper;)V"}, {"nme": "<init>", "acc": 4, "dsc": "(I)V"}, {"nme": "<init>", "acc": 4, "dsc": "(ILcom/sun/jna/TypeMapper;)V"}, {"nme": "<init>", "acc": 4, "dsc": "(Lcom/sun/jna/Pointer;)V"}, {"nme": "<init>", "acc": 4, "dsc": "(Lcom/sun/jna/Pointer;I)V"}, {"nme": "<init>", "acc": 4, "dsc": "(Lcom/sun/jna/Pointer;ILcom/sun/jna/TypeMapper;)V"}, {"nme": "fields", "acc": 0, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Lcom/sun/jna/Structure$StructField;>;"}, {"nme": "getTypeMapper", "acc": 0, "dsc": "()Lcom/sun/jna/TypeMapper;"}, {"nme": "initializeTypeMapper", "acc": 2, "dsc": "(Lcom/sun/jna/TypeMapper;)V"}, {"nme": "layoutChanged", "acc": 2, "dsc": "()V"}, {"nme": "setStringEncoding", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getStringEncoding", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setAlignType", "acc": 4, "dsc": "(I)V"}, {"nme": "autoAllocate", "acc": 4, "dsc": "(I)Lcom/sun/jna/Memory;"}, {"nme": "useMemory", "acc": 4, "dsc": "(Lcom/sun/jna/Pointer;)V"}, {"nme": "useMemory", "acc": 4, "dsc": "(Lcom/sun/jna/Pointer;I)V"}, {"nme": "useMemory", "acc": 0, "dsc": "(Lcom/sun/jna/Pointer;IZ)V"}, {"nme": "ensureAllocated", "acc": 4, "dsc": "()V"}, {"nme": "ensureAllocated", "acc": 2, "dsc": "(Z)V"}, {"nme": "allocateMemory", "acc": 4, "dsc": "()V"}, {"nme": "allocateMemory", "acc": 2, "dsc": "(Z)V"}, {"nme": "allocateMemory", "acc": 4, "dsc": "(I)V"}, {"nme": "size", "acc": 1, "dsc": "()I"}, {"nme": "clear", "acc": 1, "dsc": "()V"}, {"nme": "getPointer", "acc": 1, "dsc": "()Lcom/sun/jna/Pointer;"}, {"nme": "busy", "acc": 8, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Lcom/sun/jna/Structure;>;"}, {"nme": "reading", "acc": 8, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Lcom/sun/jna/Pointer;Lcom/sun/jna/Structure;>;"}, {"nme": "conditionalAutoRead", "acc": 0, "dsc": "()V"}, {"nme": "read", "acc": 1, "dsc": "()V"}, {"nme": "fieldOffset", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "readField", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getFieldValue", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)<PERSON>ja<PERSON>/lang/Object;"}, {"nme": "setFieldValue", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;Ljava/lang/Object;)V"}, {"nme": "setFieldValue", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;<PERSON><PERSON><PERSON>/lang/Object;Z)V"}, {"nme": "updateStructureByReference", "acc": 8, "dsc": "(L<PERSON><PERSON>/lang/Class;Lcom/sun/jna/Structure;Lcom/sun/jna/Pointer;)Lcom/sun/jna/Structure;", "sig": "(Ljava/lang/Class<*>;Lcom/sun/jna/Structure;Lcom/sun/jna/Pointer;)Lcom/sun/jna/Structure;"}, {"nme": "readField", "acc": 4, "dsc": "(Lcom/sun/jna/Structure$StructField;)Ljava/lang/Object;"}, {"nme": "write", "acc": 1, "dsc": "()V"}, {"nme": "writeField", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "writeField", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "writeField", "acc": 4, "dsc": "(Lcom/sun/jna/Structure$StructField;)V"}, {"nme": "getFieldOrder", "acc": 1028, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "setFieldOrder", "acc": 131092, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "sortFields", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Ljava/lang/reflect/Field;>;Ljava/util/List<Ljava/lang/String;>;)V"}, {"nme": "getFieldList", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/reflect/Field;>;"}, {"nme": "fieldOrder", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "createFieldsOrder", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/util/List;[Lja<PERSON>/lang/String;)Ljava/util/List;", "sig": "(Ljava/util/List<Ljava/lang/String;>;[Ljava/lang/String;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "createFieldsOrder", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/util/List;)<PERSON>java/util/List;", "sig": "(Ljava/util/List<Ljava/lang/String;>;Ljava/util/List<Ljava/lang/String;>;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "createFieldsOrder", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "createFieldsOrder", "acc": 137, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;", "sig": "([<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "sort", "acc": 10, "dsc": "(L<PERSON><PERSON>/util/Collection;)Ljava/util/List;", "sig": "<T::Ljava/lang/Comparable<TT;>;>(Ljava/util/Collection<+TT;>;)Ljava/util/List<TT;>;"}, {"nme": "getFields", "acc": 4, "dsc": "(Z)Ljava/util/List;", "sig": "(Z)Ljava/util/List<Ljava/lang/reflect/Field;>;"}, {"nme": "calculateSize", "acc": 4, "dsc": "(Z)I"}, {"nme": "size", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)I", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)I"}, {"nme": "size", "acc": 8, "dsc": "(Lja<PERSON>/lang/Class;Lcom/sun/jna/Structure;)I", "sig": "(L<PERSON><PERSON>/lang/Class<*>;Lcom/sun/jna/Structure;)I"}, {"nme": "calculateSize", "acc": 0, "dsc": "(ZZ)I"}, {"nme": "validateField", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/Class;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/Class<*>;)V"}, {"nme": "validateFields", "acc": 2, "dsc": "()V"}, {"nme": "deriveLayout", "acc": 2, "dsc": "(ZZ)Lcom/sun/jna/Structure$LayoutInfo;"}, {"nme": "initializeFields", "acc": 2, "dsc": "()V"}, {"nme": "initializeField", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;Ljava/lang/Class;)<PERSON>ja<PERSON>/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;Ljava/lang/Class<*>;)Ljava/lang/Object;"}, {"nme": "addPadding", "acc": 2, "dsc": "(I)I"}, {"nme": "addPadding", "acc": 2, "dsc": "(II)I"}, {"nme": "getStructAlignment", "acc": 4, "dsc": "()I"}, {"nme": "getNativeAlignment", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/Object;Z)I", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;Ljava/lang/Object;Z)I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "(Z)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "format", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/String;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Ljava/lang/String;"}, {"nme": "toString", "acc": 2, "dsc": "(IZZ)Ljava/lang/String;"}, {"nme": "toArray", "acc": 1, "dsc": "([Lcom/sun/jna/Structure;)[Lcom/sun/jna/Structure;"}, {"nme": "toArray", "acc": 1, "dsc": "(I)[Lcom/sun/jna/Structure;"}, {"nme": "baseClass", "acc": 2, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<*>;"}, {"nme": "dataEquals", "acc": 1, "dsc": "(Lcom/sun/jna/Structure;)Z"}, {"nme": "dataEquals", "acc": 1, "dsc": "(Lcom/sun/jna/Structure;Z)Z"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "cacheTypeInfo", "acc": 4, "dsc": "(Lcom/sun/jna/Pointer;)V"}, {"nme": "getFieldTypeInfo", "acc": 0, "dsc": "(Lcom/sun/jna/Structure$StructField;)Lcom/sun/jna/Pointer;"}, {"nme": "getTypeInfo", "acc": 0, "dsc": "()Lcom/sun/jna/Pointer;"}, {"nme": "setAutoSynch", "acc": 1, "dsc": "(Z)V"}, {"nme": "setAutoRead", "acc": 1, "dsc": "(Z)V"}, {"nme": "getAutoRead", "acc": 1, "dsc": "()Z"}, {"nme": "setAutoWrite", "acc": 1, "dsc": "(Z)V"}, {"nme": "getAutoWrite", "acc": 1, "dsc": "()Z"}, {"nme": "getTypeInfo", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lcom/sun/jna/Pointer;"}, {"nme": "newInstance", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;J)Lcom/sun/jna/Structure;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;J)Lcom/sun/jna/Structure;"}, {"nme": "newInstance", "acc": 9, "dsc": "(L<PERSON><PERSON>/lang/Class;Lcom/sun/jna/Pointer;)Lcom/sun/jna/Structure;", "sig": "(L<PERSON><PERSON>/lang/Class<*>;Lcom/sun/jna/Pointer;)Lcom/sun/jna/Structure;", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "newInstance", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Lcom/sun/jna/Structure;", "sig": "(L<PERSON><PERSON>/lang/Class<*>;)Lcom/sun/jna/Structure;", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "typeInfoField", "acc": 0, "dsc": "()Lcom/sun/jna/Structure$StructField;"}, {"nme": "structureArrayCheck", "acc": 10, "dsc": "([Lcom/sun/jna/Structure;)V"}, {"nme": "autoRead", "acc": 9, "dsc": "([Lcom/sun/jna/Structure;)V"}, {"nme": "autoRead", "acc": 1, "dsc": "()V"}, {"nme": "autoWrite", "acc": 9, "dsc": "([Lcom/sun/jna/Structure;)V"}, {"nme": "autoWrite", "acc": 1, "dsc": "()V"}, {"nme": "getNativeSize", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)I", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)I"}, {"nme": "getNativeSize", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/Object;)I", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;Ljava/lang/Object;)I"}, {"nme": "validate", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)V"}, {"nme": "access$1900", "acc": 4104, "dsc": "(Lcom/sun/jna/Structure;Z)V"}, {"nme": "access$2000", "acc": 4104, "dsc": "()Lcom/sun/jna/Pointer;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "ALIGN_DEFAULT", "dsc": "I", "val": 0}, {"acc": 25, "nme": "ALIGN_NONE", "dsc": "I", "val": 1}, {"acc": 25, "nme": "ALIGN_GNUC", "dsc": "I", "val": 2}, {"acc": 25, "nme": "ALIGN_MSVC", "dsc": "I", "val": 3}, {"acc": 28, "nme": "CALCULATE_SIZE", "dsc": "I", "val": -1}, {"acc": 24, "nme": "layoutInfo", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Class<*>;Lcom/sun/jna/Structure$LayoutInfo;>;"}, {"acc": 24, "nme": "fieldOrder", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Class<*>;Ljava/util/List<Ljava/lang/String;>;>;"}, {"acc": 2, "nme": "memory", "dsc": "Lcom/sun/jna/Pointer;"}, {"acc": 2, "nme": "size", "dsc": "I"}, {"acc": 2, "nme": "alignType", "dsc": "I"}, {"acc": 2, "nme": "encoding", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "actualAlignType", "dsc": "I"}, {"acc": 2, "nme": "structAlignment", "dsc": "I"}, {"acc": 2, "nme": "structFields", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lcom/sun/jna/Structure$StructField;>;"}, {"acc": 18, "nme": "nativeStrings", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;"}, {"acc": 2, "nme": "typeMapper", "dsc": "Lcom/sun/jna/TypeMapper;"}, {"acc": 2, "nme": "typeInfo", "dsc": "J"}, {"acc": 2, "nme": "autoRead", "dsc": "Z"}, {"acc": 2, "nme": "autoWrite", "dsc": "Z"}, {"acc": 2, "nme": "array", "dsc": "[Lcom/sun/jna/Structure;"}, {"acc": 2, "nme": "readCalled", "dsc": "Z"}, {"acc": 26, "nme": "reads", "dsc": "<PERSON><PERSON><PERSON>/lang/ThreadLocal;", "sig": "Ljava/lang/ThreadLocal<Ljava/util/Map<Lcom/sun/jna/Pointer;Lcom/sun/jna/Structure;>;>;"}, {"acc": 26, "nme": "busy", "dsc": "<PERSON><PERSON><PERSON>/lang/ThreadLocal;", "sig": "Ljava/lang/ThreadLocal<Ljava/util/Set<Lcom/sun/jna/Structure;>;>;"}, {"acc": 26, "nme": "PLACEHOLDER_MEMORY", "dsc": "Lcom/sun/jna/Pointer;"}]}, "com/sun/jna/CallbackReference$AttachOptions.class": {"ver": 50, "acc": 32, "nme": "com/sun/jna/CallbackReference$AttachOptions", "super": "com/sun/jna/Structure", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "getFieldOrder", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "FIELDS", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 1, "nme": "daemon", "dsc": "Z"}, {"acc": 1, "nme": "detach", "dsc": "Z"}, {"acc": 1, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "com/sun/jna/WString.class": {"ver": 50, "acc": 49, "nme": "com/sun/jna/WString", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "compareTo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}, {"nme": "length", "acc": 1, "dsc": "()I"}, {"nme": "char<PERSON>t", "acc": 1, "dsc": "(I)C"}, {"nme": "subSequence", "acc": 1, "dsc": "(II)<PERSON><PERSON><PERSON>/lang/CharSequence;"}], "flds": [{"acc": 2, "nme": "string", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "com/sun/jna/FromNativeContext.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/FromNativeContext", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)V"}, {"nme": "getTargetType", "acc": 1, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<*>;"}], "flds": [{"acc": 2, "nme": "type", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}]}, "com/sun/jna/Pointer$1.class": {"ver": 50, "acc": 4128, "nme": "com/sun/jna/Pointer$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "com/sun/jna/Structure$LayoutInfo.class": {"ver": 50, "acc": 32, "nme": "com/sun/jna/Structure$LayoutInfo", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "access$000", "acc": 4104, "dsc": "(Lcom/sun/jna/Structure$LayoutInfo;)Z"}, {"nme": "access$100", "acc": 4104, "dsc": "(Lcom/sun/jna/Structure$LayoutInfo;)I"}, {"nme": "access$200", "acc": 4104, "dsc": "(Lcom/sun/jna/Structure$LayoutInfo;)I"}, {"nme": "access$300", "acc": 4104, "dsc": "(Lcom/sun/jna/Structure$LayoutInfo;)Lcom/sun/jna/TypeMapper;"}, {"nme": "access$400", "acc": 4104, "dsc": "(Lcom/sun/jna/Structure$LayoutInfo;)I"}, {"nme": "access$500", "acc": 4104, "dsc": "(Lcom/sun/jna/Structure$LayoutInfo;)Ljava/util/Map;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lcom/sun/jna/Structure$1;)V"}, {"nme": "access$202", "acc": 4104, "dsc": "(Lcom/sun/jna/Structure$LayoutInfo;I)I"}, {"nme": "access$302", "acc": 4104, "dsc": "(Lcom/sun/jna/Structure$LayoutInfo;Lcom/sun/jna/TypeMapper;)Lcom/sun/jna/TypeMapper;"}, {"nme": "access$002", "acc": 4104, "dsc": "(Lcom/sun/jna/Structure$LayoutInfo;Z)Z"}, {"nme": "access$402", "acc": 4104, "dsc": "(Lcom/sun/jna/Structure$LayoutInfo;I)I"}, {"nme": "access$700", "acc": 4104, "dsc": "(Lcom/sun/jna/Structure$LayoutInfo;)Lcom/sun/jna/Structure$StructField;"}, {"nme": "access$702", "acc": 4104, "dsc": "(Lcom/sun/jna/Structure$LayoutInfo;Lcom/sun/jna/Structure$StructField;)Lcom/sun/jna/Structure$StructField;"}, {"nme": "access$102", "acc": 4104, "dsc": "(Lcom/sun/jna/Structure$LayoutInfo;I)I"}], "flds": [{"acc": 2, "nme": "size", "dsc": "I"}, {"acc": 2, "nme": "alignment", "dsc": "I"}, {"acc": 18, "nme": "fields", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lcom/sun/jna/Structure$StructField;>;"}, {"acc": 2, "nme": "alignType", "dsc": "I"}, {"acc": 2, "nme": "typeMapper", "dsc": "Lcom/sun/jna/TypeMapper;"}, {"acc": 2, "nme": "variable", "dsc": "Z"}, {"acc": 2, "nme": "typeInfoField", "dsc": "Lcom/sun/jna/Structure$StructField;"}]}, "com/sun/jna/Library.class": {"ver": 50, "acc": 1537, "nme": "com/sun/jna/Library", "super": "java/lang/Object", "mthds": [], "flds": [{"acc": 25, "nme": "OPTION_TYPE_MAPPER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "type-mapper"}, {"acc": 25, "nme": "OPTION_FUNCTION_MAPPER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "function-mapper"}, {"acc": 25, "nme": "OPTION_INVOCATION_MAPPER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "invocation-mapper"}, {"acc": 25, "nme": "OPTION_STRUCTURE_ALIGNMENT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "structure-alignment"}, {"acc": 25, "nme": "OPTION_STRING_ENCODING", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "string-encoding"}, {"acc": 25, "nme": "OPTION_ALLOW_OBJECTS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "allow-objects"}, {"acc": 25, "nme": "OPTION_CALLING_CONVENTION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "calling-convention"}, {"acc": 25, "nme": "OPTION_OPEN_FLAGS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "open-flags"}, {"acc": 25, "nme": "OPTION_CLASSLOADER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "classloader"}]}, "com/sun/jna/ptr/DoubleByReference.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/ptr/DoubleByReference", "super": "com/sun/jna/ptr/ByReference", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(D)V"}, {"nme": "setValue", "acc": 1, "dsc": "(D)V"}, {"nme": "getValue", "acc": 1, "dsc": "()D"}], "flds": []}, "com/sun/jna/Memory.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/Memory", "super": "com/sun/jna/Pointer", "mthds": [{"nme": "purge", "acc": 9, "dsc": "()V"}, {"nme": "disposeAll", "acc": 9, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(J)V"}, {"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "share", "acc": 1, "dsc": "(J)Lcom/sun/jna/Pointer;"}, {"nme": "share", "acc": 1, "dsc": "(JJ)Lcom/sun/jna/Pointer;"}, {"nme": "align", "acc": 1, "dsc": "(I)Lcom/sun/jna/Memory;"}, {"nme": "finalize", "acc": 4, "dsc": "()V"}, {"nme": "dispose", "acc": 36, "dsc": "()V"}, {"nme": "clear", "acc": 1, "dsc": "()V"}, {"nme": "valid", "acc": 1, "dsc": "()Z"}, {"nme": "size", "acc": 1, "dsc": "()J"}, {"nme": "boundsCheck", "acc": 4, "dsc": "(JJ)V"}, {"nme": "read", "acc": 1, "dsc": "(J[BII)V"}, {"nme": "read", "acc": 1, "dsc": "(J[SII)V"}, {"nme": "read", "acc": 1, "dsc": "(J[CII)V"}, {"nme": "read", "acc": 1, "dsc": "(J[III)V"}, {"nme": "read", "acc": 1, "dsc": "(J[JII)V"}, {"nme": "read", "acc": 1, "dsc": "(J[FII)V"}, {"nme": "read", "acc": 1, "dsc": "(J[DII)V"}, {"nme": "write", "acc": 1, "dsc": "(J[BII)V"}, {"nme": "write", "acc": 1, "dsc": "(J[SII)V"}, {"nme": "write", "acc": 1, "dsc": "(J[CII)V"}, {"nme": "write", "acc": 1, "dsc": "(J[III)V"}, {"nme": "write", "acc": 1, "dsc": "(J[JII)V"}, {"nme": "write", "acc": 1, "dsc": "(J[FII)V"}, {"nme": "write", "acc": 1, "dsc": "(J[DII)V"}, {"nme": "getByte", "acc": 1, "dsc": "(J)B"}, {"nme": "getChar", "acc": 1, "dsc": "(J)C"}, {"nme": "getShort", "acc": 1, "dsc": "(J)S"}, {"nme": "getInt", "acc": 1, "dsc": "(J)I"}, {"nme": "getLong", "acc": 1, "dsc": "(J)J"}, {"nme": "getFloat", "acc": 1, "dsc": "(J)F"}, {"nme": "getDouble", "acc": 1, "dsc": "(J)D"}, {"nme": "getPointer", "acc": 1, "dsc": "(J)Lcom/sun/jna/Pointer;"}, {"nme": "getByteBuffer", "acc": 1, "dsc": "(JJ)Ljava/nio/ByteBuffer;"}, {"nme": "getString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getWideString", "acc": 1, "dsc": "(J)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setByte", "acc": 1, "dsc": "(JB)V"}, {"nme": "setChar", "acc": 1, "dsc": "(JC)V"}, {"nme": "setShort", "acc": 1, "dsc": "(JS)V"}, {"nme": "setInt", "acc": 1, "dsc": "(JI)V"}, {"nme": "setLong", "acc": 1, "dsc": "(JJ)V"}, {"nme": "setFloat", "acc": 1, "dsc": "(JF)V"}, {"nme": "setDouble", "acc": 1, "dsc": "(JD)V"}, {"nme": "setPointer", "acc": 1, "dsc": "(JLcom/sun/jna/Pointer;)V"}, {"nme": "setString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setWideString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "free", "acc": 12, "dsc": "(J)V"}, {"nme": "malloc", "acc": 12, "dsc": "(J)J"}, {"nme": "dump", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Lcom/sun/jna/Memory;Ljava/lang/ref/Reference<Lcom/sun/jna/Memory;>;>;"}, {"acc": 26, "nme": "buffers", "dsc": "Lcom/sun/jna/WeakMemoryHolder;"}, {"acc": 4, "nme": "size", "dsc": "J"}]}, "com/sun/jna/ELFAnalyser.class": {"ver": 50, "acc": 32, "nme": "com/sun/jna/ELFAnalyser", "super": "java/lang/Object", "mthds": [{"nme": "analyse", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/sun/jna/ELFAnalyser;", "exs": ["java/io/IOException"]}, {"nme": "isELF", "acc": 1, "dsc": "()Z"}, {"nme": "is64Bit", "acc": 1, "dsc": "()Z"}, {"nme": "isBigEndian", "acc": 1, "dsc": "()Z"}, {"nme": "getFilename", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isArmHardFloat", "acc": 1, "dsc": "()Z"}, {"nme": "isArmSoftFloat", "acc": 1, "dsc": "()Z"}, {"nme": "isArm", "acc": 1, "dsc": "()Z"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "runDetection", "acc": 2, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "ELF_MAGIC", "dsc": "[B"}, {"acc": 26, "nme": "EF_ARM_ABI_FLOAT_HARD", "dsc": "I", "val": 1024}, {"acc": 26, "nme": "EF_ARM_ABI_FLOAT_SOFT", "dsc": "I", "val": 512}, {"acc": 26, "nme": "EI_DATA_BIG_ENDIAN", "dsc": "I", "val": 2}, {"acc": 26, "nme": "E_MACHINE_ARM", "dsc": "I", "val": 40}, {"acc": 26, "nme": "EI_CLASS_64BIT", "dsc": "I", "val": 2}, {"acc": 18, "nme": "filename", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "ELF", "dsc": "Z"}, {"acc": 2, "nme": "_64Bit", "dsc": "Z"}, {"acc": 2, "nme": "bigEndian", "dsc": "Z"}, {"acc": 2, "nme": "armHardFloat", "dsc": "Z"}, {"acc": 2, "nme": "armSoftFloat", "dsc": "Z"}, {"acc": 2, "nme": "arm", "dsc": "Z"}]}, "com/sun/jna/NativeLong.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/NativeLong", "super": "com/sun/jna/IntegerType", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(J)V"}, {"nme": "<init>", "acc": 1, "dsc": "(JZ)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}, {"acc": 25, "nme": "SIZE", "dsc": "I"}]}, "com/sun/jna/Function$NativeMappedArray.class": {"ver": 50, "acc": 32, "nme": "com/sun/jna/Function$NativeMappedArray", "super": "com/sun/jna/Memory", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "([Lcom/sun/jna/NativeMapped;)V"}, {"nme": "read", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "original", "dsc": "[Lcom/sun/jna/NativeMapped;"}]}, "com/sun/jna/Native$5.class": {"ver": 50, "acc": 48, "nme": "com/sun/jna/Native$5", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "accept", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;Lja<PERSON>/lang/String;)Z"}], "flds": []}, "com/sun/jna/ptr/ByReference.class": {"ver": 50, "acc": 1057, "nme": "com/sun/jna/ptr/ByReference", "super": "com/sun/jna/PointerType", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(I)V"}], "flds": []}, "com/sun/jna/IntegerType.class": {"ver": 50, "acc": 1057, "nme": "com/sun/jna/IntegerType", "super": "java/lang/Number", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(I)V"}, {"nme": "<init>", "acc": 1, "dsc": "(IZ)V"}, {"nme": "<init>", "acc": 1, "dsc": "(IJ)V"}, {"nme": "<init>", "acc": 1, "dsc": "(IJZ)V"}, {"nme": "setValue", "acc": 1, "dsc": "(J)V"}, {"nme": "toNative", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "fromNative", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lcom/sun/jna/FromNativeContext;)Ljava/lang/Object;"}, {"nme": "nativeType", "acc": 1, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<*>;"}, {"nme": "intValue", "acc": 1, "dsc": "()I"}, {"nme": "longValue", "acc": 1, "dsc": "()J"}, {"nme": "floatValue", "acc": 1, "dsc": "()F"}, {"nme": "doubleValue", "acc": 1, "dsc": "()D"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "compare", "acc": 9, "dsc": "(Lcom/sun/jna/IntegerType;Lcom/sun/jna/IntegerType;)I", "sig": "<T:Lcom/sun/jna/IntegerType;>(TT;TT;)I"}, {"nme": "compare", "acc": 9, "dsc": "(Lcom/sun/jna/IntegerType;J)I"}, {"nme": "compare", "acc": 25, "dsc": "(JJ)I"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}, {"acc": 2, "nme": "size", "dsc": "I"}, {"acc": 2, "nme": "number", "dsc": "<PERSON><PERSON><PERSON>/lang/Number;"}, {"acc": 2, "nme": "unsigned", "dsc": "Z"}, {"acc": 2, "nme": "value", "dsc": "J"}]}, "com/sun/jna/CallbackResultContext.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/CallbackResultContext", "super": "com/sun/jna/ToNativeContext", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)V"}, {"nme": "getMethod", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/reflect/Method;"}], "flds": [{"acc": 2, "nme": "method", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}]}, "com/sun/jna/Structure$StructField.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/Structure$StructField", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 1, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 1, "nme": "type", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 1, "nme": "field", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Field;"}, {"acc": 1, "nme": "size", "dsc": "I"}, {"acc": 1, "nme": "offset", "dsc": "I"}, {"acc": 1, "nme": "isVolatile", "dsc": "Z"}, {"acc": 1, "nme": "isReadOnly", "dsc": "Z"}, {"acc": 1, "nme": "readConverter", "dsc": "Lcom/sun/jna/FromNativeConverter;"}, {"acc": 1, "nme": "writeConverter", "dsc": "Lcom/sun/jna/ToNativeConverter;"}, {"acc": 1, "nme": "context", "dsc": "Lcom/sun/jna/FromNativeContext;"}]}, "com/sun/jna/Structure$FFIType.class": {"ver": 50, "acc": 32, "nme": "com/sun/jna/Structure$FFIType", "super": "com/sun/jna/Structure", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lcom/sun/jna/Structure;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Ljava/lang/Class;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Object;Ljava/lang/Class<*>;)V"}, {"nme": "getFieldOrder", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "init", "acc": 2, "dsc": "([Lcom/sun/jna/Pointer;)V"}, {"nme": "get", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lcom/sun/jna/Pointer;"}, {"nme": "get", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Ljava/lang/Class;)Lcom/sun/jna/Pointer;", "sig": "(L<PERSON><PERSON>/lang/Object;Ljava/lang/Class<*>;)Lcom/sun/jna/Pointer;"}, {"nme": "access$800", "acc": 4104, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Ljava/lang/Class;)Lcom/sun/jna/Pointer;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "typeInfoMap", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;"}, {"acc": 26, "nme": "FFI_TYPE_STRUCT", "dsc": "I", "val": 13}, {"acc": 1, "nme": "size", "dsc": "Lcom/sun/jna/Structure$FFIType$size_t;"}, {"acc": 1, "nme": "alignment", "dsc": "S"}, {"acc": 1, "nme": "type", "dsc": "S"}, {"acc": 1, "nme": "elements", "dsc": "Lcom/sun/jna/Pointer;"}]}, "com/sun/jna/VarArgsChecker.class": {"ver": 50, "acc": 1056, "nme": "com/sun/jna/<PERSON>ar<PERSON>rgs<PERSON>hecker", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "create", "acc": 8, "dsc": "()Lcom/sun/jna/Var<PERSON>rgs<PERSON>hecker;"}, {"nme": "isVarArgs", "acc": 1024, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)Z"}, {"nme": "fixedArgs", "acc": 1024, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)I"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lcom/sun/jna/VarArgsChecker$1;)V"}], "flds": []}, "com/sun/jna/StructureReadContext.class": {"ver": 50, "acc": 33, "nme": "com/sun/jna/StructureReadContext", "super": "com/sun/jna/FromNativeContext", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/sun/jna/Structure;Ljava/lang/reflect/Field;)V"}, {"nme": "getStructure", "acc": 1, "dsc": "()Lcom/sun/jna/Structure;"}, {"nme": "getField", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/reflect/Field;"}], "flds": [{"acc": 2, "nme": "structure", "dsc": "Lcom/sun/jna/Structure;"}, {"acc": 2, "nme": "field", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Field;"}]}, "com/sun/jna/Version.class": {"ver": 50, "acc": 1536, "nme": "com/sun/jna/Version", "super": "java/lang/Object", "mthds": [], "flds": [{"acc": 25, "nme": "VERSION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "4.4.0"}, {"acc": 25, "nme": "VERSION_NATIVE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "5.1.0"}]}}}}