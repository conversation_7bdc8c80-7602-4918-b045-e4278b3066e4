{"md5": "166b299fcca559916b8118091fe985b8", "sha2": "9fcc1293ea6c9b1e0b4c50f5bd18fc6610fb83b2", "sha256": "ec518aae8c9ceab7f11a77e5a0920c32ebf366b9a01007eee24a0b69bfa203b0", "contents": {"classes": {"classes/java/util/prefs/Preferences.class": {"ver": 68, "acc": 1057, "nme": "java/util/prefs/Preferences", "super": "java/lang/Object", "mthds": [{"nme": "factory", "acc": 10, "dsc": "()Ljava/util/prefs/PreferencesFactory;"}, {"nme": "userNodeForPackage", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/util/prefs/Preferences;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Ljava/util/prefs/Preferences;"}, {"nme": "systemNodeForPackage", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/util/prefs/Preferences;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Ljava/util/prefs/Preferences;"}, {"nme": "nodeName", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/String;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Ljava/lang/String;"}, {"nme": "userRoot", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/util/prefs/Preferences;"}, {"nme": "systemRoot", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/util/prefs/Preferences;"}, {"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "put", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "get", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "remove", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "clear", "acc": 1025, "dsc": "()V", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "putInt", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"nme": "getInt", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)I"}, {"nme": "putLong", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;J)V"}, {"nme": "getLong", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;J)J"}, {"nme": "putBoolean", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)V"}, {"nme": "getBoolean", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)Z"}, {"nme": "putFloat", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;F)V"}, {"nme": "getFloat", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;F)F"}, {"nme": "putDouble", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;D)V"}, {"nme": "getDouble", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;D)D"}, {"nme": "putByteArray", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[B)V"}, {"nme": "getByteArray", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[B)[B"}, {"nme": "keys", "acc": 1025, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "childrenNames", "acc": 1025, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "parent", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/prefs/Preferences;"}, {"nme": "node", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/prefs/Preferences;"}, {"nme": "nodeExists", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "removeNode", "acc": 1025, "dsc": "()V", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "name", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "absolutePath", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isUserNode", "acc": 1025, "dsc": "()Z"}, {"nme": "toString", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "flush", "acc": 1025, "dsc": "()V", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "sync", "acc": 1025, "dsc": "()V", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "addPreferenceChangeListener", "acc": 1025, "dsc": "(L<PERSON><PERSON>/util/prefs/PreferenceChangeListener;)V"}, {"nme": "removePreferenceChangeListener", "acc": 1025, "dsc": "(L<PERSON><PERSON>/util/prefs/PreferenceChangeListener;)V"}, {"nme": "addNodeChangeListener", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/prefs/NodeChangeListener;)V"}, {"nme": "removeNodeChangeListener", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/prefs/NodeChangeListener;)V"}, {"nme": "exportNode", "acc": 1025, "dsc": "(Ljava/io/OutputStream;)V", "exs": ["java/io/IOException", "java/util/prefs/BackingStoreException"]}, {"nme": "exportSubtree", "acc": 1025, "dsc": "(Ljava/io/OutputStream;)V", "exs": ["java/io/IOException", "java/util/prefs/BackingStoreException"]}, {"nme": "importPreferences", "acc": 9, "dsc": "(<PERSON><PERSON>va/io/InputStream;)V", "exs": ["java/io/IOException", "java/util/prefs/InvalidPreferencesFormatException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "factory", "dsc": "<PERSON><PERSON>va/util/prefs/PreferencesFactory;"}, {"acc": 25, "nme": "MAX_KEY_LENGTH", "dsc": "I", "val": 80}, {"acc": 25, "nme": "MAX_VALUE_LENGTH", "dsc": "I", "val": 8192}, {"acc": 25, "nme": "MAX_NAME_LENGTH", "dsc": "I", "val": 80}]}, "classes/java/util/prefs/AbstractPreferences$EventDispatchThread.class": {"ver": 68, "acc": 32, "nme": "java/util/prefs/AbstractPreferences$EventDispatchThread", "super": "java/lang/Thread", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": []}, "classes/java/util/prefs/AbstractPreferences$NodeRemovedEvent.class": {"ver": 68, "acc": 32, "nme": "java/util/prefs/AbstractPreferences$NodeRemovedEvent", "super": "java/util/prefs/NodeChangeEvent", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/prefs/Preferences;Ljava/util/prefs/Preferences;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 8735497392918824837}]}, "classes/java/util/prefs/XmlSupport$EH.class": {"ver": 68, "acc": 32, "nme": "java/util/prefs/XmlSupport$EH", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "error", "acc": 1, "dsc": "(Lorg/xml/sax/SAXParseException;)V", "exs": ["org/xml/sax/SAXException"]}, {"nme": "fatalError", "acc": 1, "dsc": "(Lorg/xml/sax/SAXParseException;)V", "exs": ["org/xml/sax/SAXException"]}, {"nme": "warning", "acc": 1, "dsc": "(Lorg/xml/sax/SAXParseException;)V", "exs": ["org/xml/sax/SAXException"]}], "flds": []}, "classes/java/util/prefs/Base64.class": {"ver": 68, "acc": 32, "nme": "java/util/prefs/Base64", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "byteArrayToBase64", "acc": 8, "dsc": "([B)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "byteArrayToAltBase64", "acc": 8, "dsc": "([B)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "byteArrayToBase64", "acc": 10, "dsc": "([BZ)Ljava/lang/String;"}, {"nme": "base64ToByteArray", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[B"}, {"nme": "altBase64ToByteArray", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[B"}, {"nme": "base64ToByteArray", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)[B"}, {"nme": "base64toInt", "acc": 10, "dsc": "(C[B)I"}, {"nme": "main", "acc": 9, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "intToBase64", "dsc": "[C"}, {"acc": 26, "nme": "intToAltBase64", "dsc": "[C"}, {"acc": 26, "nme": "base64ToInt", "dsc": "[B"}, {"acc": 26, "nme": "altBase64ToInt", "dsc": "[B"}]}, "classes/java/util/prefs/XmlSupport$Resolver.class": {"ver": 68, "acc": 32, "nme": "java/util/prefs/XmlSupport$Resolver", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "resolveEntity", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;)Lorg/xml/sax/InputSource;", "exs": ["org/xml/sax/SAXException"]}], "flds": []}, "classes/java/util/prefs/WindowsPreferences.class": {"ver": 68, "acc": 32, "nme": "java/util/prefs/WindowsPreferences", "super": "java/util/prefs/AbstractPreferences", "mthds": [{"nme": "loadPrefsLib", "acc": 10, "dsc": "()V"}, {"nme": "getUserRoot", "acc": 8, "dsc": "()<PERSON><PERSON><PERSON>/util/prefs/Preferences;"}, {"nme": "getSystemRoot", "acc": 8, "dsc": "()<PERSON><PERSON><PERSON>/util/prefs/Preferences;"}, {"nme": "WindowsRegOpenKey", "acc": 266, "dsc": "(J[BI)[J"}, {"nme": "WindowsRegOpenKey1", "acc": 10, "dsc": "(J[BI)[J"}, {"nme": "WindowsRegCloseKey", "acc": 266, "dsc": "(J)I"}, {"nme": "WindowsRegCreateKeyEx", "acc": 266, "dsc": "(J[B)[J"}, {"nme": "WindowsRegCreateKeyEx1", "acc": 10, "dsc": "(J[B)[J"}, {"nme": "WindowsRegDeleteKey", "acc": 266, "dsc": "(J[B)I"}, {"nme": "WindowsRegFlushKey", "acc": 266, "dsc": "(J)I"}, {"nme": "WindowsRegFlushKey1", "acc": 10, "dsc": "(J)I"}, {"nme": "WindowsRegQueryValueEx", "acc": 266, "dsc": "(J[B)[B"}, {"nme": "WindowsRegSetValueEx", "acc": 266, "dsc": "(J[B[B)I"}, {"nme": "WindowsRegSetValueEx1", "acc": 10, "dsc": "(J[B[B)I"}, {"nme": "WindowsRegDeleteValue", "acc": 266, "dsc": "(J[B)I"}, {"nme": "WindowsRegQueryInfoKey", "acc": 266, "dsc": "(J)[J"}, {"nme": "WindowsRegQueryInfoKey1", "acc": 10, "dsc": "(J)[J"}, {"nme": "WindowsRegEnumKeyEx", "acc": 266, "dsc": "(JII)[B"}, {"nme": "WindowsRegEnumKeyEx1", "acc": 10, "dsc": "(JII)[B"}, {"nme": "WindowsRegEnumValue", "acc": 266, "dsc": "(JII)[B"}, {"nme": "WindowsRegEnumValue1", "acc": 10, "dsc": "(JII)[B"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/prefs/WindowsPreferences;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(J[B)V"}, {"nme": "windowsAbsolutePath", "acc": 2, "dsc": "()[B"}, {"nme": "openKey", "acc": 2, "dsc": "(I)J"}, {"nme": "openKey", "acc": 2, "dsc": "(II)J"}, {"nme": "openKey", "acc": 2, "dsc": "([BII)J"}, {"nme": "openKey", "acc": 2, "dsc": "(J[BII)J"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(J)V"}, {"nme": "putSpi", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getSpi", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "removeSpi", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "keysSpi", "acc": 4, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "childrenNamesSpi", "acc": 4, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "flush", "acc": 1, "dsc": "()V", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "sync", "acc": 1, "dsc": "()V", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "child<PERSON><PERSON>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/prefs/AbstractPreferences;"}, {"nme": "removeNodeSpi", "acc": 1, "dsc": "()V", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "toJavaName", "acc": 10, "dsc": "([B)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toJavaAlt64Name", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "toWindowsName", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[B"}, {"nme": "toWindowsAlt64Name", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[B"}, {"nme": "toJavaValueString", "acc": 10, "dsc": "([B)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toWindowsValueString", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[B"}, {"nme": "rootNativeHandle", "acc": 2, "dsc": "()J"}, {"nme": "stringToByteArray", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[B"}, {"nme": "byteArrayToString", "acc": 10, "dsc": "([B)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "flushSpi", "acc": 4, "dsc": "()V", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "syncSpi", "acc": 4, "dsc": "()V", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "logger", "acc": 42, "dsc": "()Lsun/util/logging/PlatformLogger;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 10, "nme": "logger", "dsc": "Lsun/util/logging/PlatformLogger;"}, {"acc": 26, "nme": "WINDOWS_ROOT_PATH", "dsc": "[B"}, {"acc": 26, "nme": "HKEY_CURRENT_USER", "dsc": "I", "val": -2147483647}, {"acc": 26, "nme": "HKEY_LOCAL_MACHINE", "dsc": "I", "val": -2147483646}, {"acc": 26, "nme": "USER_ROOT_NATIVE_HANDLE", "dsc": "I", "val": -2147483647}, {"acc": 26, "nme": "SYSTEM_ROOT_NATIVE_HANDLE", "dsc": "I", "val": -2147483646}, {"acc": 26, "nme": "MAX_WINDOWS_PATH_LENGTH", "dsc": "I", "val": 256}, {"acc": 74, "nme": "userRoot", "dsc": "<PERSON><PERSON><PERSON>/util/prefs/Preferences;"}, {"acc": 74, "nme": "systemRoot", "dsc": "<PERSON><PERSON><PERSON>/util/prefs/Preferences;"}, {"acc": 26, "nme": "ERROR_SUCCESS", "dsc": "I", "val": 0}, {"acc": 26, "nme": "ERROR_FILE_NOT_FOUND", "dsc": "I", "val": 2}, {"acc": 26, "nme": "ERROR_ACCESS_DENIED", "dsc": "I", "val": 5}, {"acc": 26, "nme": "NATIVE_HANDLE", "dsc": "I", "val": 0}, {"acc": 26, "nme": "ERROR_CODE", "dsc": "I", "val": 1}, {"acc": 26, "nme": "SUBKEYS_NUMBER", "dsc": "I", "val": 0}, {"acc": 26, "nme": "VALUES_NUMBER", "dsc": "I", "val": 2}, {"acc": 26, "nme": "MAX_KEY_LENGTH", "dsc": "I", "val": 3}, {"acc": 26, "nme": "MAX_VALUE_NAME_LENGTH", "dsc": "I", "val": 4}, {"acc": 26, "nme": "DISPOSITION", "dsc": "I", "val": 2}, {"acc": 26, "nme": "REG_CREATED_NEW_KEY", "dsc": "I", "val": 1}, {"acc": 26, "nme": "REG_OPENED_EXISTING_KEY", "dsc": "I", "val": 2}, {"acc": 26, "nme": "NULL_NATIVE_HANDLE", "dsc": "I", "val": 0}, {"acc": 26, "nme": "DELETE", "dsc": "I", "val": 65536}, {"acc": 26, "nme": "KEY_QUERY_VALUE", "dsc": "I", "val": 1}, {"acc": 26, "nme": "KEY_SET_VALUE", "dsc": "I", "val": 2}, {"acc": 26, "nme": "KEY_CREATE_SUB_KEY", "dsc": "I", "val": 4}, {"acc": 26, "nme": "KEY_ENUMERATE_SUB_KEYS", "dsc": "I", "val": 8}, {"acc": 26, "nme": "KEY_READ", "dsc": "I", "val": 131097}, {"acc": 26, "nme": "KEY_WRITE", "dsc": "I", "val": 131078}, {"acc": 26, "nme": "KEY_ALL_ACCESS", "dsc": "I", "val": 983103}, {"acc": 10, "nme": "INIT_SLEEP_TIME", "dsc": "I"}, {"acc": 10, "nme": "MAX_ATTEMPTS", "dsc": "I"}, {"acc": 2, "nme": "isBackingStoreAvailable", "dsc": "Z"}]}, "classes/java/util/prefs/AbstractPreferences.class": {"ver": 68, "acc": 1057, "nme": "java/util/prefs/AbstractPreferences", "super": "java/util/prefs/Preferences", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/util/prefs/AbstractPreferences;Ljava/lang/String;)V"}, {"nme": "put", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "get", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "remove", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "clear", "acc": 1, "dsc": "()V", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "putInt", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"nme": "getInt", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)I"}, {"nme": "putLong", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;J)V"}, {"nme": "getLong", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;J)J"}, {"nme": "putBoolean", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)V"}, {"nme": "getBoolean", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)Z"}, {"nme": "putFloat", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;F)V"}, {"nme": "getFloat", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;F)F"}, {"nme": "putDouble", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;D)V"}, {"nme": "getDouble", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;D)D"}, {"nme": "putByteArray", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[B)V"}, {"nme": "getByteArray", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[B)[B"}, {"nme": "keys", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "childrenNames", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "cachedChildren", "acc": 20, "dsc": "()[<PERSON><PERSON><PERSON>/util/prefs/AbstractPreferences;"}, {"nme": "parent", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/prefs/Preferences;"}, {"nme": "node", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/prefs/Preferences;"}, {"nme": "node", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/StringTokenizer;)Ljava/util/prefs/Preferences;"}, {"nme": "nodeExists", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "nodeExists", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/StringTokenizer;)Z", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "removeNode", "acc": 1, "dsc": "()V", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "removeNode2", "acc": 2, "dsc": "()V", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "name", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "absolutePath", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isUserNode", "acc": 1, "dsc": "()Z"}, {"nme": "addPreferenceChangeListener", "acc": 1, "dsc": "(L<PERSON><PERSON>/util/prefs/PreferenceChangeListener;)V"}, {"nme": "removePreferenceChangeListener", "acc": 1, "dsc": "(L<PERSON><PERSON>/util/prefs/PreferenceChangeListener;)V"}, {"nme": "addNodeChangeListener", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/prefs/NodeChangeListener;)V"}, {"nme": "removeNodeChangeListener", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/prefs/NodeChangeListener;)V"}, {"nme": "putSpi", "acc": 1028, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getSpi", "acc": 1028, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "removeSpi", "acc": 1028, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "removeNodeSpi", "acc": 1028, "dsc": "()V", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "keysSpi", "acc": 1028, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "childrenNamesSpi", "acc": 1028, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/prefs/AbstractPreferences;", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "child<PERSON><PERSON>", "acc": 1028, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/prefs/AbstractPreferences;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "sync", "acc": 1, "dsc": "()V", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "sync2", "acc": 2, "dsc": "()V", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "syncSpi", "acc": 1028, "dsc": "()V", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "flush", "acc": 1, "dsc": "()V", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "flush2", "acc": 2, "dsc": "()V", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "flushSpi", "acc": 1028, "dsc": "()V", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "isRemoved", "acc": 4, "dsc": "()Z"}, {"nme": "startEventDispatchThreadIfNecessary", "acc": 42, "dsc": "()V"}, {"nme": "prefListeners", "acc": 0, "dsc": "()[<PERSON><PERSON><PERSON>/util/prefs/PreferenceChangeListener;"}, {"nme": "nodeListeners", "acc": 0, "dsc": "()[<PERSON><PERSON><PERSON>/util/prefs/NodeChangeListener;"}, {"nme": "enqueuePreferenceChangeEvent", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "enqueueNodeAddedEvent", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/prefs/Preferences;)V"}, {"nme": "enqueueNodeRemovedEvent", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/prefs/Preferences;)V"}, {"nme": "exportNode", "acc": 1, "dsc": "(Ljava/io/OutputStream;)V", "exs": ["java/io/IOException", "java/util/prefs/BackingStoreException"]}, {"nme": "exportSubtree", "acc": 1, "dsc": "(Ljava/io/OutputStream;)V", "exs": ["java/io/IOException", "java/util/prefs/BackingStoreException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "CODE_POINT_U0000", "dsc": "I", "val": 0}, {"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "absolutePath", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 16, "nme": "parent", "dsc": "<PERSON><PERSON><PERSON>/util/prefs/AbstractPreferences;"}, {"acc": 18, "nme": "root", "dsc": "<PERSON><PERSON><PERSON>/util/prefs/AbstractPreferences;"}, {"acc": 4, "nme": "newNode", "dsc": "Z"}, {"acc": 2, "nme": "<PERSON><PERSON><PERSON>", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/util/prefs/AbstractPreferences;>;"}, {"acc": 2, "nme": "removed", "dsc": "Z"}, {"acc": 2, "nme": "prefListeners", "dsc": "[Ljava/util/prefs/PreferenceChangeListener;"}, {"acc": 2, "nme": "nodeListeners", "dsc": "[<PERSON><PERSON><PERSON>/util/prefs/NodeChangeListener;"}, {"acc": 20, "nme": "lock", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 26, "nme": "EMPTY_STRING_ARRAY", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "EMPTY_ABSTRACT_PREFS_ARRAY", "dsc": "[Lja<PERSON>/util/prefs/AbstractPreferences;"}, {"acc": 26, "nme": "eventQueue", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/util/EventObject;>;"}, {"acc": 10, "nme": "eventDispatchThread", "dsc": "<PERSON><PERSON><PERSON>/lang/Thread;"}]}, "classes/java/util/prefs/BackingStoreException.class": {"ver": 68, "acc": 33, "nme": "java/util/prefs/BackingStoreException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 859796500401108469}]}, "classes/java/util/prefs/NodeChangeEvent.class": {"ver": 68, "acc": 33, "nme": "java/util/prefs/NodeChangeEvent", "super": "java/util/EventObject", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/prefs/Preferences;Ljava/util/prefs/Preferences;)V"}, {"nme": "getParent", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/prefs/Preferences;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/prefs/Preferences;"}, {"nme": "writeObject", "acc": 2, "dsc": "(Ljava/io/ObjectOutputStream;)V", "exs": ["java/io/NotSerializableException"]}, {"nme": "readObject", "acc": 2, "dsc": "(Ljava/io/ObjectInputStream;)V", "exs": ["java/io/NotSerializableException"]}], "flds": [{"acc": 130, "nme": "child", "dsc": "<PERSON><PERSON><PERSON>/util/prefs/Preferences;"}, {"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 8068949086596572957}]}, "classes/java/util/prefs/InvalidPreferencesFormatException.class": {"ver": 68, "acc": 33, "nme": "java/util/prefs/InvalidPreferencesFormatException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -791715184232119669}]}, "classes/java/util/prefs/PreferenceChangeListener.class": {"ver": 68, "acc": 1537, "nme": "java/util/prefs/PreferenceChangeListener", "super": "java/lang/Object", "mthds": [{"nme": "preferenceChange", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/prefs/PreferenceChangeEvent;)V"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/FunctionalInterface;"}]}, "classes/java/util/prefs/PreferencesFactory.class": {"ver": 68, "acc": 1537, "nme": "java/util/prefs/PreferencesFactory", "super": "java/lang/Object", "mthds": [{"nme": "systemRoot", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/prefs/Preferences;"}, {"nme": "userRoot", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/prefs/Preferences;"}], "flds": []}, "classes/module-info.class": {"ver": 68, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "classes/java/util/prefs/Preferences$1.class": {"ver": 68, "acc": 4128, "nme": "java/util/prefs/Preferences$1", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$jdk$internal$util$OperatingSystem", "dsc": "[I"}]}, "classes/java/util/prefs/AbstractPreferences$NodeAddedEvent.class": {"ver": 68, "acc": 32, "nme": "java/util/prefs/AbstractPreferences$NodeAddedEvent", "super": "java/util/prefs/NodeChangeEvent", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/prefs/Preferences;Ljava/util/prefs/Preferences;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -6743557530157328528}]}, "classes/java/util/prefs/NodeChangeListener.class": {"ver": 68, "acc": 1537, "nme": "java/util/prefs/NodeChangeListener", "super": "java/lang/Object", "mthds": [{"nme": "childAdded", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/prefs/NodeChangeEvent;)V"}, {"nme": "child<PERSON><PERSON>oved", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/prefs/NodeChangeEvent;)V"}], "flds": []}, "classes/java/util/prefs/WindowsPreferencesFactory.class": {"ver": 68, "acc": 32, "nme": "java/util/prefs/WindowsPreferencesFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "userRoot", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/prefs/Preferences;"}, {"nme": "systemRoot", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/prefs/Preferences;"}], "flds": []}, "classes/java/util/prefs/PreferenceChangeEvent.class": {"ver": 68, "acc": 33, "nme": "java/util/prefs/PreferenceChangeEvent", "super": "java/util/EventObject", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/prefs/Preferences;Lja<PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;)V"}, {"nme": "getNode", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/prefs/Preferences;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getNewValue", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "writeObject", "acc": 2, "dsc": "(Ljava/io/ObjectOutputStream;)V", "exs": ["java/io/NotSerializableException"]}, {"nme": "readObject", "acc": 2, "dsc": "(Ljava/io/ObjectInputStream;)V", "exs": ["java/io/NotSerializableException"]}], "flds": [{"acc": 2, "nme": "key", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "newValue", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 793724513368024975}]}, "classes/java/util/prefs/XmlSupport.class": {"ver": 68, "acc": 32, "nme": "java/util/prefs/XmlSupport", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "export", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/io/OutputStream;<PERSON><PERSON><PERSON>/util/prefs/Preferences;Z)V", "exs": ["java/io/IOException", "java/util/prefs/BackingStoreException"]}, {"nme": "putPreferencesInXml", "acc": 10, "dsc": "(Lorg/w3c/dom/Element;Lorg/w3c/dom/Document;<PERSON><PERSON><PERSON>/util/prefs/Preferences;Z)V", "exs": ["java/util/prefs/BackingStoreException"]}, {"nme": "importPreferences", "acc": 8, "dsc": "(<PERSON><PERSON>va/io/InputStream;)V", "exs": ["java/io/IOException", "java/util/prefs/InvalidPreferencesFormatException"]}, {"nme": "createPrefsDoc", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/w3c/dom/Document;"}, {"nme": "loadPrefsDoc", "acc": 10, "dsc": "(<PERSON><PERSON>va/io/InputStream;)Lorg/w3c/dom/Document;", "exs": ["org/xml/sax/SAXException", "java/io/IOException"]}, {"nme": "writeDoc", "acc": 26, "dsc": "(Lorg/w3c/dom/Document;Ljava/io/OutputStream;)V", "exs": ["java/io/IOException"]}, {"nme": "ImportSubtree", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/prefs/Preferences;Lorg/w3c/dom/Element;)V"}, {"nme": "ImportPrefs", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/prefs/Preferences;Lorg/w3c/dom/Element;)V"}, {"nme": "exportMap", "acc": 8, "dsc": "(Ljava/io/OutputStream;Ljava/util/Map;)V", "sig": "(Ljava/io/OutputStream;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V", "exs": ["java/io/IOException"]}, {"nme": "importMap", "acc": 8, "dsc": "(Ljava/io/InputStream;Ljava/util/Map;)V", "sig": "(Ljava/io/InputStream;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V", "exs": ["java/io/IOException", "java/util/prefs/InvalidPreferencesFormatException"]}], "flds": [{"acc": 26, "nme": "PREFS_DTD_URI", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "http://java.sun.com/dtd/preferences.dtd"}, {"acc": 26, "nme": "PREFS_DTD", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!-- DTD for preferences --><!ELEMENT preferences (root) ><!ATTLIST preferences EXTERNAL_XML_VERSION CDATA \"0.0\"  ><!ELEMENT root (map, node*) ><!ATTLIST root          type (system|user) #REQUIRED ><!ELEMENT node (map, node*) ><!ATTLIST node          name CDATA #REQUIRED ><!ELEMENT map (entry*) ><!ATTLIST map  MAP_XML_VERSION CDATA \"0.0\"  ><!ELEMENT entry EMPTY ><!ATTLIST entry          key CDATA #REQUIRED          value CDATA #REQUIRED >"}, {"acc": 26, "nme": "EXTERNAL_XML_VERSION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "1.0"}, {"acc": 26, "nme": "MAP_XML_VERSION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "1.0"}]}}}}