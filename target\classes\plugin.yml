name: SbMagicHook
version: 1.0.0-BETA
author: Shyrcs
main: shyrcs.extrastoragehook.SbMagicHook
description: A lightweight hook between ExtraStorage and Discord with advanced selling features.
api-version: 1.21
depend: [ExtraStorage, Vault]
soft-depend: [PlaceholderAP<PERSON>, ShopGUIPlus, EconomyShopGUI]

commands:
  sbmagichook:
    description: The main command of the plugin
    usage: /sbmagichook
    aliases: [sbmh, magichook]
  
permissions:
  sbmagichook.use.hook:
    description: Permission to hook your account to Discord
    default: true
  sbmagichook.use.sell:
    description: Permission to use sell commands via Discord
    default: true
  sbmagichook.admin:
    description: Admin permissions for the plugin
    default: op
  sbmagichook.reload:
    description: Permission to reload the plugin
    default: op
