{"md5": "289075e48b909e9e74e6c915b3631d2e", "sha2": "6975da39a7040257bd51d21a231b76c915872d38", "sha256": "91c77044a50c481636c32d916fd89c9118a72195390452c81065080f957de7ff", "contents": {"classes": {"javax/inject/Inject.class": {"ver": 49, "acc": 9729, "nme": "javax/inject/Inject", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "CONSTRUCTOR"], ["L<PERSON>va/lang/annotation/ElementType;", "FIELD"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "RUNTIME"]]}, {"dsc": "Ljava/lang/annotation/Documented;"}]}, "javax/inject/Named.class": {"ver": 49, "acc": 9729, "nme": "javax/inject/Named", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [], "vanns": [{"dsc": "Ljavax/inject/Qualifier;"}, {"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "RUNTIME"]]}]}, "javax/inject/Scope.class": {"ver": 49, "acc": 9729, "nme": "javax/inject/Scope", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "ANNOTATION_TYPE"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "RUNTIME"]]}, {"dsc": "Ljava/lang/annotation/Documented;"}]}, "javax/inject/Provider.class": {"ver": 49, "acc": 1537, "nme": "javax/inject/Provider", "super": "java/lang/Object", "mthds": [{"nme": "get", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}], "flds": []}, "javax/inject/Singleton.class": {"ver": 49, "acc": 9729, "nme": "javax/inject/Singleton", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljavax/inject/Scope;"}, {"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "RUNTIME"]]}]}, "javax/inject/Qualifier.class": {"ver": 49, "acc": 9729, "nme": "javax/inject/Qualifier", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "ANNOTATION_TYPE"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "RUNTIME"]]}, {"dsc": "Ljava/lang/annotation/Documented;"}]}}}}