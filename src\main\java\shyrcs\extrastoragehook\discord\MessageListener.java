package shyrcs.extrastoragehook.discord;

import net.dv8tion.jda.api.entities.channel.ChannelType;
import net.dv8tion.jda.api.events.message.MessageReceivedEvent;
import net.dv8tion.jda.api.hooks.ListenerAdapter;
import org.bukkit.scheduler.BukkitRunnable;
import shyrcs.extrastoragehook.application.Library;
import shyrcs.extrastoragehook.application.PluginBoot;
import shyrcs.extrastoragehook.SbMagicHook;

/**
 * Listener cho chat messages
 */
public class MessageListener extends ListenerAdapter {
    
    @Override
    public void onMessageReceived(MessageReceivedEvent event) {
        // Bỏ qua bot messages
        if (event.getAuthor().isBot()) {
            return;
        }
        
        // Chỉ xử lý trong guild text channels
        if (!event.getChannel().getType().equals(ChannelType.TEXT)) {
            return;
        }
        
        // Kiểm tra guild
        if (event.getGuild() == null) {
            return;
        }
        
        // Kiểm tra guild ID
        String guildId = Library.config.getGuildID();
        if (!guildId.isEmpty() && !event.getGuild().getId().equals(guildId)) {
            return;
        }
        
        // Kiểm tra member
        if (event.getMember() == null) {
            return;
        }
        
        String message = event.getMessage().getContentRaw();
        
        // Kiểm tra prefix (có thể là ! hoặc /)
        if (!message.startsWith("!") && !message.startsWith("/")) {
            return;
        }
        
        // Parse command
        String[] parts = message.substring(1).split("\\s+");
        if (parts.length == 0) {
            return;
        }
        
        String commandName = parts[0].toLowerCase();
        
        // Bỏ channel restrictions cho ! commands
        
        try {
            // Xử lý command nếu tồn tại
            if (Library.manager.hasCommands(commandName)) {
                new BukkitRunnable() {
                    public void run() {
                        try {
                            Library.manager.getCommand(commandName).onChatCommand(event);
                        } catch (Exception e) {
                            event.getChannel().sendMessage(Library.config.getMessage("error"))
                                .setMessageReference(event.getMessage()).queue();
                        }
                    }
                }.runTaskAsynchronously(PluginBoot.main);
            }
        } catch (Exception err) {
            // Bỏ debug messages
        }
    }
}
