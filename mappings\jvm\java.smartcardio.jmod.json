{"md5": "40b960c7994bb72039273107864773f3", "sha2": "3dd0cdaeb30c4c6e037fe8ca1f9defdc7cc42467", "sha256": "be7b5cda45ff609980aa42ba2c6e4d80530971ceb5a7f3bd9d56c7e7481ca9d8", "contents": {"classes": {"classes/javax/smartcardio/CardException.class": {"ver": 68, "acc": 33, "nme": "javax/smartcardio/CardException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 7787607144922050628}]}, "classes/javax/smartcardio/CardTerminal.class": {"ver": 68, "acc": 1057, "nme": "javax/smartcardio/CardTerminal", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "getName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "connect", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljavax/smartcardio/Card;", "exs": ["javax/smartcardio/CardException"]}, {"nme": "isCardPresent", "acc": 1025, "dsc": "()Z", "exs": ["javax/smartcardio/CardException"]}, {"nme": "waitForCardPresent", "acc": 1025, "dsc": "(J)Z", "exs": ["javax/smartcardio/CardException"]}, {"nme": "waitForCardAbsent", "acc": 1025, "dsc": "(J)Z", "exs": ["javax/smartcardio/CardException"]}], "flds": []}, "classes/sun/security/smartcardio/PCSCTerminals$ReaderState.class": {"ver": 68, "acc": 32, "nme": "sun/security/smartcardio/PCSCTerminals$ReaderState", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "get", "acc": 0, "dsc": "()I"}, {"nme": "update", "acc": 0, "dsc": "(I)V"}, {"nme": "isInsertion", "acc": 0, "dsc": "()Z"}, {"nme": "isRemoval", "acc": 0, "dsc": "()Z"}, {"nme": "present", "acc": 8, "dsc": "(I)Z"}], "flds": [{"acc": 2, "nme": "current", "dsc": "I"}, {"acc": 2, "nme": "previous", "dsc": "I"}]}, "classes/sun/security/smartcardio/SunPCSC$Factory.class": {"ver": 68, "acc": 49, "nme": "sun/security/smartcardio/SunPCSC$Factory", "super": "javax/smartcardio/TerminalFactorySpi", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "exs": ["sun/security/smartcardio/PCSCException"]}, {"nme": "engineTerminals", "acc": 4, "dsc": "()Ljavax/smartcardio/CardTerminals;"}], "flds": []}, "classes/javax/smartcardio/TerminalFactory$NoneFactorySpi.class": {"ver": 68, "acc": 48, "nme": "javax/smartcardio/TerminalFactory$NoneFactorySpi", "super": "javax/smartcardio/TerminalFactorySpi", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "engineTerminals", "acc": 4, "dsc": "()Ljavax/smartcardio/CardTerminals;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "INSTANCE", "dsc": "Ljavax/smartcardio/TerminalFactorySpi;"}]}, "classes/javax/smartcardio/CardChannel.class": {"ver": 68, "acc": 1057, "nme": "javax/smartcardio/CardChannel", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "getCard", "acc": 1025, "dsc": "()Ljavax/smartcardio/Card;"}, {"nme": "getChannelNumber", "acc": 1025, "dsc": "()I"}, {"nme": "transmit", "acc": 1025, "dsc": "(Ljavax/smartcardio/CommandAPDU;)Ljavax/smartcardio/ResponseAPDU;", "exs": ["javax/smartcardio/CardException"]}, {"nme": "transmit", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/nio/<PERSON>te<PERSON>uffer;<PERSON><PERSON><PERSON>/nio/ByteBuffer;)I", "exs": ["javax/smartcardio/CardException"]}, {"nme": "close", "acc": 1025, "dsc": "()V", "exs": ["javax/smartcardio/CardException"]}], "flds": []}, "classes/sun/security/smartcardio/CardImpl$State.class": {"ver": 68, "acc": 16432, "nme": "sun/security/smartcardio/CardImpl$State", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lsun/security/smartcardio/CardImpl$State;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Lsun/security/smartcardio/CardImpl$State;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Lsun/security/smartcardio/CardImpl$State;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "OK", "dsc": "Lsun/security/smartcardio/CardImpl$State;"}, {"acc": 16409, "nme": "REMOVED", "dsc": "Lsun/security/smartcardio/CardImpl$State;"}, {"acc": 16409, "nme": "DISCONNECTED", "dsc": "Lsun/security/smartcardio/CardImpl$State;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lsun/security/smartcardio/CardImpl$State;"}]}, "classes/javax/smartcardio/CardTerminals.class": {"ver": 68, "acc": 1057, "nme": "javax/smartcardio/CardTerminals", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "list", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljavax/smartcardio/CardTerminal;>;", "exs": ["javax/smartcardio/CardException"]}, {"nme": "list", "acc": 1025, "dsc": "(Ljavax/smartcardio/CardTerminals$State;)Ljava/util/List;", "sig": "(Ljavax/smartcardio/CardTerminals$State;)Ljava/util/List<Ljavax/smartcardio/CardTerminal;>;", "exs": ["javax/smartcardio/CardException"]}, {"nme": "getTerminal", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljavax/smartcardio/CardTerminal;"}, {"nme": "waitForChange", "acc": 1, "dsc": "()V", "exs": ["javax/smartcardio/CardException"]}, {"nme": "waitForChange", "acc": 1025, "dsc": "(J)Z", "exs": ["javax/smartcardio/CardException"]}], "flds": []}, "classes/javax/smartcardio/CommandAPDU.class": {"ver": 68, "acc": 49, "nme": "javax/smartcardio/CommandAPDU", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "([B)V"}, {"nme": "<init>", "acc": 1, "dsc": "([BII)V"}, {"nme": "checkArrayBounds", "acc": 2, "dsc": "([BII)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/nio/<PERSON>;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(IIII)V"}, {"nme": "<init>", "acc": 1, "dsc": "(IIIII)V"}, {"nme": "<init>", "acc": 1, "dsc": "(IIII[B)V"}, {"nme": "<init>", "acc": 1, "dsc": "(IIII[BII)V"}, {"nme": "<init>", "acc": 1, "dsc": "(IIII[BI)V"}, {"nme": "array<PERSON>ength", "acc": 10, "dsc": "([B)I"}, {"nme": "parse", "acc": 2, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(IIII[BIII)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(IIII)V"}, {"nme": "getCLA", "acc": 1, "dsc": "()I"}, {"nme": "getINS", "acc": 1, "dsc": "()I"}, {"nme": "getP1", "acc": 1, "dsc": "()I"}, {"nme": "getP2", "acc": 1, "dsc": "()I"}, {"nme": "getNc", "acc": 1, "dsc": "()I"}, {"nme": "getData", "acc": 1, "dsc": "()[B"}, {"nme": "getNe", "acc": 1, "dsc": "()I"}, {"nme": "getBytes", "acc": 1, "dsc": "()[B"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "readObject", "acc": 2, "dsc": "(Ljava/io/ObjectInputStream;)V", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 398698301286670877}, {"acc": 26, "nme": "MAX_APDU_SIZE", "dsc": "I", "val": 65544}, {"acc": 2, "nme": "apdu", "dsc": "[B"}, {"acc": 130, "nme": "nc", "dsc": "I"}, {"acc": 130, "nme": "ne", "dsc": "I"}, {"acc": 130, "nme": "dataOffset", "dsc": "I"}]}, "classes/module-info.class": {"ver": 68, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "classes/sun/security/smartcardio/TerminalImpl.class": {"ver": 68, "acc": 48, "nme": "sun/security/smartcardio/TerminalImpl", "super": "javax/smartcardio/CardTerminal", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "connect", "acc": 33, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljavax/smartcardio/Card;", "exs": ["javax/smartcardio/CardException"]}, {"nme": "isCardPresent", "acc": 1, "dsc": "()Z", "exs": ["javax/smartcardio/CardException"]}, {"nme": "waitForCard", "acc": 2, "dsc": "(ZJ)Z", "exs": ["javax/smartcardio/CardException"]}, {"nme": "waitForCardPresent", "acc": 1, "dsc": "(J)Z", "exs": ["javax/smartcardio/CardException"]}, {"nme": "waitForCardAbsent", "acc": 1, "dsc": "(J)Z", "exs": ["javax/smartcardio/CardException"]}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 16, "nme": "contextId", "dsc": "J"}, {"acc": 16, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "card", "dsc": "Lsun/security/smartcardio/CardImpl;"}]}, "classes/javax/smartcardio/Card.class": {"ver": 68, "acc": 1057, "nme": "javax/smartcardio/Card", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "getATR", "acc": 1025, "dsc": "()Ljavax/smartcardio/ATR;"}, {"nme": "getProtocol", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getBasicChannel", "acc": 1025, "dsc": "()Ljavax/smartcardio/CardChannel;"}, {"nme": "openLogicalChannel", "acc": 1025, "dsc": "()Ljavax/smartcardio/CardChannel;", "exs": ["javax/smartcardio/CardException"]}, {"nme": "beginExclusive", "acc": 1025, "dsc": "()V", "exs": ["javax/smartcardio/CardException"]}, {"nme": "endExclusive", "acc": 1025, "dsc": "()V", "exs": ["javax/smartcardio/CardException"]}, {"nme": "transmitControlCommand", "acc": 1025, "dsc": "(I[B)[B", "exs": ["javax/smartcardio/CardException"]}, {"nme": "disconnect", "acc": 1025, "dsc": "(Z)V", "exs": ["javax/smartcardio/CardException"]}], "flds": []}, "classes/javax/smartcardio/TerminalFactorySpi.class": {"ver": 68, "acc": 1057, "nme": "javax/smartcardio/TerminalFactorySpi", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "engineTerminals", "acc": 1028, "dsc": "()Ljavax/smartcardio/CardTerminals;"}], "flds": []}, "classes/javax/smartcardio/CardTerminals$State.class": {"ver": 68, "acc": 16433, "nme": "javax/smartcardio/CardTerminals$State", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljavax/smartcardio/CardTerminals$State;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Ljavax/smartcardio/CardTerminals$State;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljavax/smartcardio/CardTerminals$State;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "ALL", "dsc": "Ljavax/smartcardio/CardTerminals$State;"}, {"acc": 16409, "nme": "CARD_PRESENT", "dsc": "Ljavax/smartcardio/CardTerminals$State;"}, {"acc": 16409, "nme": "CARD_ABSENT", "dsc": "Ljavax/smartcardio/CardTerminals$State;"}, {"acc": 16409, "nme": "CARD_INSERTION", "dsc": "Ljavax/smartcardio/CardTerminals$State;"}, {"acc": 16409, "nme": "CARD_REMOVAL", "dsc": "Ljavax/smartcardio/CardTerminals$State;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljavax/smartcardio/CardTerminals$State;"}]}, "classes/javax/smartcardio/TerminalFactory.class": {"ver": 68, "acc": 49, "nme": "javax/smartcardio/TerminalFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljavax/smartcardio/TerminalFactorySpi;Ljava/security/Provider;Ljava/lang/String;)V"}, {"nme": "getDefaultType", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDefault", "acc": 9, "dsc": "()Ljavax/smartcardio/TerminalFactory;"}, {"nme": "getInstance", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/Object;)Ljavax/smartcardio/TerminalFactory;", "exs": ["java/security/NoSuchAlgorithmException"]}, {"nme": "getInstance", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;Ljava/lang/String;)Ljavax/smartcardio/TerminalFactory;", "exs": ["java/security/NoSuchAlgorithmException", "java/security/NoSuchProviderException"]}, {"nme": "getInstance", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/Object;Ljava/security/Provider;)Ljavax/smartcardio/TerminalFactory;", "exs": ["java/security/NoSuchAlgorithmException"]}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Ljava/security/Provider;"}, {"nme": "getType", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "terminals", "acc": 1, "dsc": "()Ljavax/smartcardio/CardTerminals;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "PROP_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "javax.smartcardio.TerminalFactory.DefaultType"}, {"acc": 26, "nme": "defaultType", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "defaultFactory", "dsc": "Ljavax/smartcardio/TerminalFactory;"}, {"acc": 18, "nme": "spi", "dsc": "Ljavax/smartcardio/TerminalFactorySpi;"}, {"acc": 18, "nme": "provider", "dsc": "Ljava/security/Provider;"}, {"acc": 18, "nme": "type", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/sun/security/smartcardio/PlatformPCSC$1.class": {"ver": 68, "acc": 32, "nme": "sun/security/smartcardio/PlatformPCSC$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "run", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Void;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/sun/security/smartcardio/PCSC.class": {"ver": 68, "acc": 48, "nme": "sun/security/smartcardio/PCSC", "super": "sun/security/smartcardio/PlatformPCSC", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "checkAvailable", "acc": 8, "dsc": "()V", "exs": ["java/lang/RuntimeException"]}, {"nme": "SCardEstablishContext", "acc": 264, "dsc": "(I)J", "exs": ["sun/security/smartcardio/PCSCException"]}, {"nme": "SCardListReaders", "acc": 264, "dsc": "(J)[<PERSON><PERSON><PERSON>/lang/String;", "exs": ["sun/security/smartcardio/PCSCException"]}, {"nme": "SCardConnect", "acc": 264, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;II)J", "exs": ["sun/security/smartcardio/PCSCException"]}, {"nme": "SCardTransmit", "acc": 264, "dsc": "(JI[BII)[B", "exs": ["sun/security/smartcardio/PCSCException"]}, {"nme": "SCardStatus", "acc": 264, "dsc": "(J[B)[B", "exs": ["sun/security/smartcardio/PCSCException"]}, {"nme": "SCardDisconnect", "acc": 264, "dsc": "(JI)V", "exs": ["sun/security/smartcardio/PCSCException"]}, {"nme": "SCardGetStatusChange", "acc": 264, "dsc": "(JJ[<PERSON>[<PERSON><PERSON><PERSON>/lang/String;)[I", "exs": ["sun/security/smartcardio/PCSCException"]}, {"nme": "SCardBeginTransaction", "acc": 264, "dsc": "(J)V", "exs": ["sun/security/smartcardio/PCSCException"]}, {"nme": "SCardEndTransaction", "acc": 264, "dsc": "(JI)V", "exs": ["sun/security/smartcardio/PCSCException"]}, {"nme": "SCardControl", "acc": 264, "dsc": "(JI[B)[B", "exs": ["sun/security/smartcardio/PCSCException"]}, {"nme": "toString", "acc": 9, "dsc": "([B)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "SCARD_S_SUCCESS", "dsc": "I", "val": 0}, {"acc": 24, "nme": "SCARD_E_CANCELLED", "dsc": "I", "val": -2146435070}, {"acc": 24, "nme": "SCARD_E_CANT_DISPOSE", "dsc": "I", "val": -2146435058}, {"acc": 24, "nme": "SCARD_E_INSUFFICIENT_BUFFER", "dsc": "I", "val": -2146435064}, {"acc": 24, "nme": "SCARD_E_INVALID_ATR", "dsc": "I", "val": -2146435051}, {"acc": 24, "nme": "SCARD_E_INVALID_HANDLE", "dsc": "I", "val": -2146435069}, {"acc": 24, "nme": "SCARD_E_INVALID_PARAMETER", "dsc": "I", "val": -2146435068}, {"acc": 24, "nme": "SCARD_E_INVALID_TARGET", "dsc": "I", "val": -2146435067}, {"acc": 24, "nme": "SCARD_E_INVALID_VALUE", "dsc": "I", "val": -2146435055}, {"acc": 24, "nme": "SCARD_E_NO_MEMORY", "dsc": "I", "val": -2146435066}, {"acc": 24, "nme": "SCARD_F_COMM_ERROR", "dsc": "I", "val": -2146435053}, {"acc": 24, "nme": "SCARD_F_INTERNAL_ERROR", "dsc": "I", "val": -2146435071}, {"acc": 24, "nme": "SCARD_F_UNKNOWN_ERROR", "dsc": "I", "val": -2146435052}, {"acc": 24, "nme": "SCARD_F_WAITED_TOO_LONG", "dsc": "I", "val": -2146435065}, {"acc": 24, "nme": "SCARD_E_UNKNOWN_READER", "dsc": "I", "val": -2146435063}, {"acc": 24, "nme": "SCARD_E_TIMEOUT", "dsc": "I", "val": -2146435062}, {"acc": 24, "nme": "SCARD_E_SHARING_VIOLATION", "dsc": "I", "val": -2146435061}, {"acc": 24, "nme": "SCARD_E_NO_SMARTCARD", "dsc": "I", "val": -2146435060}, {"acc": 24, "nme": "SCARD_E_UNKNOWN_CARD", "dsc": "I", "val": -2146435059}, {"acc": 24, "nme": "SCARD_E_PROTO_MISMATCH", "dsc": "I", "val": -2146435057}, {"acc": 24, "nme": "SCARD_E_NOT_READY", "dsc": "I", "val": -2146435056}, {"acc": 24, "nme": "SCARD_E_SYSTEM_CANCELLED", "dsc": "I", "val": -2146435054}, {"acc": 24, "nme": "SCARD_E_NOT_TRANSACTED", "dsc": "I", "val": -2146435050}, {"acc": 24, "nme": "SCARD_E_READER_UNAVAILABLE", "dsc": "I", "val": -2146435049}, {"acc": 24, "nme": "SCARD_W_UNSUPPORTED_CARD", "dsc": "I", "val": -2146434971}, {"acc": 24, "nme": "SCARD_W_UNRESPONSIVE_CARD", "dsc": "I", "val": -2146434970}, {"acc": 24, "nme": "SCARD_W_UNPOWERED_CARD", "dsc": "I", "val": -2146434969}, {"acc": 24, "nme": "SCARD_W_RESET_CARD", "dsc": "I", "val": -2146434968}, {"acc": 24, "nme": "SCARD_W_REMOVED_CARD", "dsc": "I", "val": -2146434967}, {"acc": 24, "nme": "SCARD_W_INSERTED_CARD", "dsc": "I", "val": -2146434966}, {"acc": 24, "nme": "SCARD_E_UNSUPPORTED_FEATURE", "dsc": "I", "val": -2146435041}, {"acc": 24, "nme": "SCARD_E_PCI_TOO_SMALL", "dsc": "I", "val": -2146435047}, {"acc": 24, "nme": "SCARD_E_READER_UNSUPPORTED", "dsc": "I", "val": -2146435046}, {"acc": 24, "nme": "SCARD_E_DUPLICATE_READER", "dsc": "I", "val": -2146435045}, {"acc": 24, "nme": "SCARD_E_CARD_UNSUPPORTED", "dsc": "I", "val": -2146435044}, {"acc": 24, "nme": "SCARD_E_NO_SERVICE", "dsc": "I", "val": -2146435043}, {"acc": 24, "nme": "SCARD_E_SERVICE_STOPPED", "dsc": "I", "val": -2146435042}, {"acc": 24, "nme": "SCARD_E_NO_READERS_AVAILABLE", "dsc": "I", "val": -2146435026}, {"acc": 24, "nme": "WINDOWS_ERROR_INVALID_HANDLE", "dsc": "I", "val": 6}, {"acc": 24, "nme": "WINDOWS_ERROR_INVALID_PARAMETER", "dsc": "I", "val": 87}, {"acc": 24, "nme": "SCARD_SCOPE_USER", "dsc": "I", "val": 0}, {"acc": 24, "nme": "SCARD_SCOPE_TERMINAL", "dsc": "I", "val": 1}, {"acc": 24, "nme": "SCARD_SCOPE_SYSTEM", "dsc": "I", "val": 2}, {"acc": 24, "nme": "SCARD_SCOPE_GLOBAL", "dsc": "I", "val": 3}, {"acc": 24, "nme": "SCARD_SHARE_EXCLUSIVE", "dsc": "I", "val": 1}, {"acc": 24, "nme": "SCARD_SHARE_SHARED", "dsc": "I", "val": 2}, {"acc": 24, "nme": "SCARD_SHARE_DIRECT", "dsc": "I", "val": 3}, {"acc": 24, "nme": "SCARD_LEAVE_CARD", "dsc": "I", "val": 0}, {"acc": 24, "nme": "SCARD_RESET_CARD", "dsc": "I", "val": 1}, {"acc": 24, "nme": "SCARD_UNPOWER_CARD", "dsc": "I", "val": 2}, {"acc": 24, "nme": "SCARD_EJECT_CARD", "dsc": "I", "val": 3}, {"acc": 24, "nme": "SCARD_STATE_UNAWARE", "dsc": "I", "val": 0}, {"acc": 24, "nme": "SCARD_STATE_IGNORE", "dsc": "I", "val": 1}, {"acc": 24, "nme": "SCARD_STATE_CHANGED", "dsc": "I", "val": 2}, {"acc": 24, "nme": "SCARD_STATE_UNKNOWN", "dsc": "I", "val": 4}, {"acc": 24, "nme": "SCARD_STATE_UNAVAILABLE", "dsc": "I", "val": 8}, {"acc": 24, "nme": "SCARD_STATE_EMPTY", "dsc": "I", "val": 16}, {"acc": 24, "nme": "SCARD_STATE_PRESENT", "dsc": "I", "val": 32}, {"acc": 24, "nme": "SCARD_STATE_ATRMATCH", "dsc": "I", "val": 64}, {"acc": 24, "nme": "SCARD_STATE_EXCLUSIVE", "dsc": "I", "val": 128}, {"acc": 24, "nme": "SCARD_STATE_INUSE", "dsc": "I", "val": 256}, {"acc": 24, "nme": "SCARD_STATE_MUTE", "dsc": "I", "val": 512}, {"acc": 24, "nme": "SCARD_STATE_UNPOWERED", "dsc": "I", "val": 1024}, {"acc": 24, "nme": "TIMEOUT_INFINITE", "dsc": "I", "val": -1}, {"acc": 26, "nme": "hexDigits", "dsc": "[C"}]}, "classes/sun/security/smartcardio/PlatformPCSC.class": {"ver": 68, "acc": 32, "nme": "sun/security/smartcardio/PlatformPCSC", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "loadLibrary", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/lang/Throwable;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "initException", "dsc": "<PERSON><PERSON><PERSON>/lang/Throwable;"}, {"acc": 24, "nme": "SCARD_PROTOCOL_T0", "dsc": "I", "val": 1}, {"acc": 24, "nme": "SCARD_PROTOCOL_T1", "dsc": "I", "val": 2}, {"acc": 24, "nme": "SCARD_PROTOCOL_RAW", "dsc": "I", "val": 65536}, {"acc": 24, "nme": "SCARD_UNKNOWN", "dsc": "I", "val": 0}, {"acc": 24, "nme": "SCARD_ABSENT", "dsc": "I", "val": 1}, {"acc": 24, "nme": "SCARD_PRESENT", "dsc": "I", "val": 2}, {"acc": 24, "nme": "SCARD_SWALLOWED", "dsc": "I", "val": 3}, {"acc": 24, "nme": "SCARD_POWERED", "dsc": "I", "val": 4}, {"acc": 24, "nme": "SCARD_NEGOTIABLE", "dsc": "I", "val": 5}, {"acc": 24, "nme": "SCARD_SPECIFIC", "dsc": "I", "val": 6}]}, "classes/javax/smartcardio/ResponseAPDU.class": {"ver": 68, "acc": 49, "nme": "javax/smartcardio/ResponseAPDU", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "([B)V"}, {"nme": "check", "acc": 10, "dsc": "([B)V"}, {"nme": "getNr", "acc": 1, "dsc": "()I"}, {"nme": "getData", "acc": 1, "dsc": "()[B"}, {"nme": "getSW1", "acc": 1, "dsc": "()I"}, {"nme": "getSW2", "acc": 1, "dsc": "()I"}, {"nme": "getSW", "acc": 1, "dsc": "()I"}, {"nme": "getBytes", "acc": 1, "dsc": "()[B"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "readObject", "acc": 2, "dsc": "(Ljava/io/ObjectInputStream;)V", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 6962744978375594225}, {"acc": 2, "nme": "apdu", "dsc": "[B"}]}, "classes/sun/security/smartcardio/SunPCSC$1.class": {"ver": 68, "acc": 32, "nme": "sun/security/smartcardio/SunPCSC$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/security/smartcardio/SunPCSC;Ljava/security/Provider;)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Void;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 4112, "nme": "val$p", "dsc": "Ljava/security/Provider;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/security/smartcardio/SunPCSC;"}]}, "classes/sun/security/smartcardio/PCSCTerminals.class": {"ver": 68, "acc": 48, "nme": "sun/security/smartcardio/PCSCTerminals", "super": "javax/smartcardio/CardTerminals", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "initContext", "acc": 40, "dsc": "()V", "exs": ["sun/security/smartcardio/PCSCException"]}, {"nme": "implGetTerminal", "acc": 42, "dsc": "(Lja<PERSON>/lang/String;)Lsun/security/smartcardio/TerminalImpl;"}, {"nme": "list", "acc": 33, "dsc": "(Ljavax/smartcardio/CardTerminals$State;)Ljava/util/List;", "sig": "(Ljavax/smartcardio/CardTerminals$State;)Ljava/util/List<Ljavax/smartcardio/CardTerminal;>;", "exs": ["javax/smartcardio/CardException"]}, {"nme": "waitForChange", "acc": 33, "dsc": "(J)Z", "exs": ["javax/smartcardio/CardException"]}, {"nme": "waitForCards", "acc": 8, "dsc": "(Lja<PERSON>/util/List;JZ)Ljava/util/List;", "sig": "(Ljava/util/List<+Ljavax/smartcardio/CardTerminal;>;JZ)Ljava/util/List<Ljavax/smartcardio/CardTerminal;>;", "exs": ["javax/smartcardio/CardException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 10, "nme": "contextId", "dsc": "J"}, {"acc": 2, "nme": "stateMap", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lsun/security/smartcardio/PCSCTerminals$ReaderState;>;"}, {"acc": 26, "nme": "terminals", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/ref/Reference<Lsun/security/smartcardio/TerminalImpl;>;>;"}]}, "classes/sun/security/smartcardio/ChannelImpl.class": {"ver": 68, "acc": 48, "nme": "sun/security/smartcardio/ChannelImpl", "super": "javax/smartcardio/CardChannel", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/security/smartcardio/CardImpl;I)V"}, {"nme": "checkClosed", "acc": 0, "dsc": "()V"}, {"nme": "getCard", "acc": 1, "dsc": "()Ljavax/smartcardio/Card;"}, {"nme": "getChannelNumber", "acc": 1, "dsc": "()I"}, {"nme": "checkManageChannel", "acc": 10, "dsc": "([B)V"}, {"nme": "transmit", "acc": 1, "dsc": "(Ljavax/smartcardio/CommandAPDU;)Ljavax/smartcardio/ResponseAPDU;", "exs": ["javax/smartcardio/CardException"]}, {"nme": "transmit", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/nio/<PERSON>te<PERSON>uffer;<PERSON><PERSON><PERSON>/nio/ByteBuffer;)I", "exs": ["javax/smartcardio/CardException"]}, {"nme": "getBooleanProperty", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)Z"}, {"nme": "concat", "acc": 2, "dsc": "([B[BI)[B"}, {"nme": "doTransmit", "acc": 2, "dsc": "([B)[B", "exs": ["javax/smartcardio/CardException"]}, {"nme": "getSW", "acc": 10, "dsc": "([B)I", "exs": ["javax/smartcardio/CardException"]}, {"nme": "isOK", "acc": 10, "dsc": "([B)Z", "exs": ["javax/smartcardio/CardException"]}, {"nme": "setChannel", "acc": 2, "dsc": "([B)V"}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["javax/smartcardio/CardException"]}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$getBooleanProperty$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "card", "dsc": "Lsun/security/smartcardio/CardImpl;"}, {"acc": 18, "nme": "channel", "dsc": "I"}, {"acc": 66, "nme": "isClosed", "dsc": "Z"}, {"acc": 26, "nme": "t0GetResponse", "dsc": "Z"}, {"acc": 26, "nme": "t1GetResponse", "dsc": "Z"}, {"acc": 26, "nme": "t1StripLe", "dsc": "Z"}, {"acc": 26, "nme": "RESPONSE_ITERATIONS", "dsc": "I", "val": 256}, {"acc": 26, "nme": "B0", "dsc": "[B"}]}, "classes/javax/smartcardio/TerminalFactory$NoneCardTerminals.class": {"ver": 68, "acc": 48, "nme": "javax/smartcardio/TerminalFactory$NoneCardTerminals", "super": "javax/smartcardio/CardTerminals", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "list", "acc": 1, "dsc": "(Ljavax/smartcardio/CardTerminals$State;)Ljava/util/List;", "sig": "(Ljavax/smartcardio/CardTerminals$State;)Ljava/util/List<Ljavax/smartcardio/CardTerminal;>;", "exs": ["javax/smartcardio/CardException"]}, {"nme": "waitForChange", "acc": 1, "dsc": "(J)Z", "exs": ["javax/smartcardio/CardException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "INSTANCE", "dsc": "Ljavax/smartcardio/CardTerminals;"}]}, "classes/javax/smartcardio/TerminalFactory$NoneProvider.class": {"ver": 68, "acc": 48, "nme": "javax/smartcardio/TerminalFactory$NoneProvider", "super": "java/security/Provider", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 2745808869881593918}, {"acc": 24, "nme": "INSTANCE", "dsc": "Ljava/security/Provider;"}]}, "classes/sun/security/smartcardio/PCSCException.class": {"ver": 68, "acc": 48, "nme": "sun/security/smartcardio/PCSCException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(I)V"}, {"nme": "toErrorString", "acc": 10, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": *******************}, {"acc": 16, "nme": "code", "dsc": "I"}]}, "classes/javax/smartcardio/ATR.class": {"ver": 68, "acc": 49, "nme": "javax/smartcardio/ATR", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "([B)V"}, {"nme": "parse", "acc": 2, "dsc": "()V"}, {"nme": "getBytes", "acc": 1, "dsc": "()[B"}, {"nme": "getHistoricalBytes", "acc": 1, "dsc": "()[B"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "readObject", "acc": 2, "dsc": "(Ljava/io/ObjectInputStream;)V", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 6695383790847736493}, {"acc": 2, "nme": "atr", "dsc": "[B"}, {"acc": 130, "nme": "startHistorical", "dsc": "I"}, {"acc": 130, "nme": "nHistorical", "dsc": "I"}]}, "classes/sun/security/smartcardio/CardImpl.class": {"ver": 68, "acc": 48, "nme": "sun/security/smartcardio/CardImpl", "super": "javax/smartcardio/Card", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/security/smartcardio/TerminalImpl;Ljava/lang/String;)V", "exs": ["sun/security/smartcardio/PCSCException"]}, {"nme": "checkState", "acc": 0, "dsc": "()V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 0, "dsc": "()Z"}, {"nme": "checkSecurity", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "handleError", "acc": 0, "dsc": "(Lsun/security/smartcardio/PCSCException;)V"}, {"nme": "getATR", "acc": 1, "dsc": "()Ljavax/smartcardio/ATR;"}, {"nme": "getProtocol", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getBasicChannel", "acc": 1, "dsc": "()Ljavax/smartcardio/CardChannel;"}, {"nme": "getSW", "acc": 10, "dsc": "([B)I"}, {"nme": "openLogicalChannel", "acc": 1, "dsc": "()Ljavax/smartcardio/CardChannel;", "exs": ["javax/smartcardio/CardException"]}, {"nme": "checkExclusive", "acc": 0, "dsc": "()V", "exs": ["javax/smartcardio/CardException"]}, {"nme": "beginExclusive", "acc": 33, "dsc": "()V", "exs": ["javax/smartcardio/CardException"]}, {"nme": "endExclusive", "acc": 33, "dsc": "()V", "exs": ["javax/smartcardio/CardException"]}, {"nme": "transmitControlCommand", "acc": 1, "dsc": "(I[B)[B", "exs": ["javax/smartcardio/CardException"]}, {"nme": "disconnect", "acc": 1, "dsc": "(Z)V", "exs": ["javax/smartcardio/CardException"]}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "finalize", "acc": 4, "dsc": "()V", "exs": ["java/lang/Throwable"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "terminal", "dsc": "Lsun/security/smartcardio/TerminalImpl;"}, {"acc": 16, "nme": "cardId", "dsc": "J"}, {"acc": 18, "nme": "atr", "dsc": "Ljavax/smartcardio/ATR;"}, {"acc": 16, "nme": "protocol", "dsc": "I"}, {"acc": 18, "nme": "basicChannel", "dsc": "Lsun/security/smartcardio/ChannelImpl;"}, {"acc": 66, "nme": "state", "dsc": "Lsun/security/smartcardio/CardImpl$State;"}, {"acc": 66, "nme": "exclusiveThread", "dsc": "<PERSON><PERSON><PERSON>/lang/Thread;"}, {"acc": 10, "nme": "commandOpenChannel", "dsc": "[B"}]}, "classes/sun/security/smartcardio/PCSCTerminals$1.class": {"ver": 68, "acc": 4128, "nme": "sun/security/smartcardio/PCSCTerminals$1", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$javax$smartcardio$CardTerminals$State", "dsc": "[I"}]}, "classes/sun/security/smartcardio/SunPCSC$ProviderService.class": {"ver": 68, "acc": 48, "nme": "sun/security/smartcardio/SunPCSC$ProviderService", "super": "java/security/Provider$Service", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/security/Provider;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "newInstance", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/security/NoSuchAlgorithmException"]}], "flds": []}, "classes/javax/smartcardio/CardPermission.class": {"ver": 68, "acc": 33, "nme": "javax/smartcardio/CardPermission", "super": "java/security/Permission", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getMask", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "getActions", "acc": 10, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getActions", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "implies", "acc": 1, "dsc": "(Ljava/security/Permission;)Z"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "writeObject", "acc": 2, "dsc": "(Ljava/io/ObjectOutputStream;)V", "exs": ["java/io/IOException"]}, {"nme": "readObject", "acc": 2, "dsc": "(Ljava/io/ObjectInputStream;)V", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 7146787880530705613}, {"acc": 26, "nme": "A_CONNECT", "dsc": "I", "val": 1}, {"acc": 26, "nme": "A_EXCLUSIVE", "dsc": "I", "val": 2}, {"acc": 26, "nme": "A_GET_BASIC_CHANNEL", "dsc": "I", "val": 4}, {"acc": 26, "nme": "A_OPEN_LOGICAL_CHANNEL", "dsc": "I", "val": 8}, {"acc": 26, "nme": "A_RESET", "dsc": "I", "val": 16}, {"acc": 26, "nme": "A_TRANSMIT_CONTROL", "dsc": "I", "val": 32}, {"acc": 26, "nme": "A_ALL", "dsc": "I", "val": 63}, {"acc": 26, "nme": "ARRAY_MASKS", "dsc": "[I"}, {"acc": 26, "nme": "S_CONNECT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "connect"}, {"acc": 26, "nme": "S_EXCLUSIVE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "exclusive"}, {"acc": 26, "nme": "S_GET_BASIC_CHANNEL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "getBasicChannel"}, {"acc": 26, "nme": "S_OPEN_LOGICAL_CHANNEL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "openLogicalChannel"}, {"acc": 26, "nme": "S_RESET", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "reset"}, {"acc": 26, "nme": "S_TRANSMIT_CONTROL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "transmitControl"}, {"acc": 26, "nme": "S_ALL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "*"}, {"acc": 26, "nme": "ARRAY_STRINGS", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 130, "nme": "mask", "dsc": "I"}, {"acc": 18, "nme": "actions", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/sun/security/smartcardio/SunPCSC.class": {"ver": 68, "acc": 49, "nme": "sun/security/smartcardio/SunPCSC", "super": "java/security/Provider", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "access$000", "acc": 4104, "dsc": "(Lsun/security/smartcardio/SunPCSC;Ljava/security/Provider$Service;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 6168388284028876579}]}, "classes/javax/smartcardio/CardNotPresentException.class": {"ver": 68, "acc": 33, "nme": "javax/smartcardio/CardNotPresentException", "super": "javax/smartcardio/CardException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1346879911706545215}]}}}}