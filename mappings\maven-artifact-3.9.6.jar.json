{"md5": "96a7982d9a46c5c19696b4558be1bbd2", "sha2": "fb0979832c10c1a25d038a33ca862bef055fcdc8", "sha256": "ad7a0fb408f8e47585ccc0d0011e0b501d93bfc9888d369bbd4a043d19475073", "contents": {"classes": {"org/apache/maven/repository/Proxy.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/repository/Proxy", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getHost", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setHost", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getPassword", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setPassword", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getPort", "acc": 1, "dsc": "()I"}, {"nme": "setPort", "acc": 1, "dsc": "(I)V"}, {"nme": "getUserName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setUserName", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getProtocol", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setProtocol", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getNonProxyHosts", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setNonProxyHosts", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getNtlmHost", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setNtlmHost", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setNtlmDomain", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getNtlmDomain", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 25, "nme": "PROXY_SOCKS5", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "SOCKS_5"}, {"acc": 25, "nme": "PROXY_SOCKS4", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "SOCKS4"}, {"acc": 25, "nme": "PROXY_HTTP", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "HTTP"}, {"acc": 2, "nme": "host", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "userName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "password", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "port", "dsc": "I"}, {"acc": 2, "nme": "protocol", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "nonProxyHosts", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "ntlmHost", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "ntlmDomain", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/apache/maven/artifact/resolver/filter/ArtifactFilter.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/artifact/resolver/filter/ArtifactFilter", "super": "java/lang/Object", "mthds": [{"nme": "include", "acc": 1025, "dsc": "(Lorg/apache/maven/artifact/Artifact;)Z"}], "flds": []}, "org/apache/maven/artifact/DefaultArtifact.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/artifact/DefaultArtifact", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/artifact/handler/ArtifactHandler;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;<PERSON>java/lang/String;Lorg/apache/maven/artifact/versioning/VersionRange;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/artifact/handler/ArtifactHandler;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON>ja<PERSON>/lang/String;<PERSON>java/lang/String;Lorg/apache/maven/artifact/versioning/VersionRange;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/artifact/handler/ArtifactHandler;Z)V"}, {"nme": "validateIdentity", "acc": 2, "dsc": "()V"}, {"nme": "empty", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "getClassifier", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "hasClassifier", "acc": 1, "dsc": "()Z"}, {"nme": "getScope", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getGroupId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getArtifactId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setVersion", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getType", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setFile", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V"}, {"nme": "getFile", "acc": 1, "dsc": "()Ljava/io/File;"}, {"nme": "getRepository", "acc": 1, "dsc": "()Lorg/apache/maven/artifact/repository/ArtifactRepository;"}, {"nme": "setRepository", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/repository/ArtifactRepository;)V"}, {"nme": "getId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDependencyConflictId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "appendArtifactTypeClassifierString", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;)V"}, {"nme": "addMetadata", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/metadata/ArtifactMetadata;)V"}, {"nme": "getMetadataList", "acc": 1, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Lorg/apache/maven/artifact/metadata/ArtifactMetadata;>;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "getBaseVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getBaseVersionInternal", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBaseVersion", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setBaseVersionInternal", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "compareTo", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/Artifact;)I"}, {"nme": "updateVersion", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/apache/maven/artifact/repository/ArtifactRepository;)V"}, {"nme": "getDownloadUrl", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setDownloadUrl", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getDependencyFilter", "acc": 1, "dsc": "()Lorg/apache/maven/artifact/resolver/filter/ArtifactFilter;"}, {"nme": "setDependencyFilter", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/resolver/filter/ArtifactFilter;)V"}, {"nme": "getArtifactHandler", "acc": 1, "dsc": "()Lorg/apache/maven/artifact/handler/ArtifactHandler;"}, {"nme": "getDependencyTrail", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "setDependencyTrail", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/String;>;)V"}, {"nme": "setScope", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getVersionRange", "acc": 1, "dsc": "()Lorg/apache/maven/artifact/versioning/VersionRange;"}, {"nme": "setVersionRange", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/versioning/VersionRange;)V"}, {"nme": "selectVersionFromNewRangeIfAvailable", "acc": 2, "dsc": "()V"}, {"nme": "selectVersion", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setGroupId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setArtifactId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "isSnapshot", "acc": 1, "dsc": "()Z"}, {"nme": "setResolved", "acc": 1, "dsc": "(Z)V"}, {"nme": "isResolved", "acc": 1, "dsc": "()Z"}, {"nme": "setResolvedVersion", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setArtifactHandler", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/handler/ArtifactHandler;)V"}, {"nme": "setRelease", "acc": 1, "dsc": "(Z)V"}, {"nme": "isRelease", "acc": 1, "dsc": "()Z"}, {"nme": "getAvailableVersions", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/artifact/versioning/ArtifactVersion;>;"}, {"nme": "setAvailableVersions", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Lorg/apache/maven/artifact/versioning/ArtifactVersion;>;)V"}, {"nme": "isOptional", "acc": 1, "dsc": "()Z"}, {"nme": "getSelectedVersion", "acc": 1, "dsc": "()Lorg/apache/maven/artifact/versioning/ArtifactVersion;", "exs": ["org/apache/maven/artifact/versioning/OverConstrainedVersionException"]}, {"nme": "isSelectedVersionKnown", "acc": 1, "dsc": "()Z", "exs": ["org/apache/maven/artifact/versioning/OverConstrainedVersionException"]}, {"nme": "setOptional", "acc": 1, "dsc": "(Z)V"}, {"nme": "compareTo", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}], "flds": [{"acc": 2, "nme": "groupId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "artifactId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "baseVersion", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "type", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "classifier", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 66, "nme": "scope", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 66, "nme": "file", "dsc": "Ljava/io/File;"}, {"acc": 2, "nme": "repository", "dsc": "Lorg/apache/maven/artifact/repository/ArtifactRepository;"}, {"acc": 2, "nme": "downloadUrl", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "dependencyFilter", "dsc": "Lorg/apache/maven/artifact/resolver/filter/ArtifactFilter;"}, {"acc": 2, "nme": "artifact<PERSON><PERSON><PERSON>", "dsc": "Lorg/apache/maven/artifact/handler/ArtifactHandler;"}, {"acc": 2, "nme": "dependencyTrail", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 66, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "versionRange", "dsc": "Lorg/apache/maven/artifact/versioning/VersionRange;"}, {"acc": 66, "nme": "resolved", "dsc": "Z"}, {"acc": 2, "nme": "release", "dsc": "Z"}, {"acc": 2, "nme": "availableVersions", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/artifact/versioning/ArtifactVersion;>;"}, {"acc": 2, "nme": "metadataMap", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/artifact/metadata/ArtifactMetadata;>;"}, {"acc": 2, "nme": "optional", "dsc": "Z"}]}, "org/apache/maven/artifact/versioning/VersionRange.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/artifact/versioning/VersionRange", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lorg/apache/maven/artifact/versioning/ArtifactVersion;Ljava/util/List;)V", "sig": "(Lorg/apache/maven/artifact/versioning/ArtifactVersion;Ljava/util/List<Lorg/apache/maven/artifact/versioning/Restriction;>;)V"}, {"nme": "getRecommendedVersion", "acc": 1, "dsc": "()Lorg/apache/maven/artifact/versioning/ArtifactVersion;"}, {"nme": "getRestrictions", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/artifact/versioning/Restriction;>;"}, {"nme": "cloneOf", "acc": 131073, "dsc": "()Lorg/apache/maven/artifact/versioning/VersionRange;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "createFromVersionSpec", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/apache/maven/artifact/versioning/VersionRange;", "exs": ["org/apache/maven/artifact/versioning/InvalidVersionSpecificationException"]}, {"nme": "parseRestriction", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/apache/maven/artifact/versioning/Restriction;", "exs": ["org/apache/maven/artifact/versioning/InvalidVersionSpecificationException"]}, {"nme": "createFromVersion", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/apache/maven/artifact/versioning/VersionRange;"}, {"nme": "restrict", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/versioning/VersionRange;)Lorg/apache/maven/artifact/versioning/VersionRange;"}, {"nme": "intersection", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/util/List;)<PERSON>java/util/List;", "sig": "(Ljava/util/List<Lorg/apache/maven/artifact/versioning/Restriction;>;Ljava/util/List<Lorg/apache/maven/artifact/versioning/Restriction;>;)Ljava/util/List<Lorg/apache/maven/artifact/versioning/Restriction;>;"}, {"nme": "getSelectedVersion", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/Artifact;)Lorg/apache/maven/artifact/versioning/ArtifactVersion;", "exs": ["org/apache/maven/artifact/versioning/OverConstrainedVersionException"]}, {"nme": "isSelectedVersionKnown", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/Artifact;)Z", "exs": ["org/apache/maven/artifact/versioning/OverConstrainedVersionException"]}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "matchVersion", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lorg/apache/maven/artifact/versioning/ArtifactVersion;", "sig": "(Lja<PERSON>/util/List<Lorg/apache/maven/artifact/versioning/ArtifactVersion;>;)Lorg/apache/maven/artifact/versioning/ArtifactVersion;"}, {"nme": "containsVersion", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/versioning/ArtifactVersion;)Z"}, {"nme": "hasRestrictions", "acc": 1, "dsc": "()Z"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CACHE_SPEC", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lorg/apache/maven/artifact/versioning/VersionRange;>;"}, {"acc": 26, "nme": "CACHE_VERSION", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lorg/apache/maven/artifact/versioning/VersionRange;>;"}, {"acc": 18, "nme": "recommendedVersion", "dsc": "Lorg/apache/maven/artifact/versioning/ArtifactVersion;"}, {"acc": 18, "nme": "restrictions", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/artifact/versioning/Restriction;>;"}]}, "org/apache/maven/artifact/metadata/ArtifactMetadata.class": {"ver": 52, "acc": 132609, "nme": "org/apache/maven/artifact/metadata/ArtifactMetadata", "super": "java/lang/Object", "mthds": [{"nme": "merge", "acc": 1025, "dsc": "(Lorg/apache/maven/artifact/metadata/ArtifactMetadata;)V"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "org/apache/maven/artifact/versioning/ComparableVersion$Item.class": {"ver": 52, "acc": 1536, "nme": "org/apache/maven/artifact/versioning/ComparableVersion$Item", "super": "java/lang/Object", "mthds": [{"nme": "compareTo", "acc": 1025, "dsc": "(Lorg/apache/maven/artifact/versioning/ComparableVersion$Item;)I"}, {"nme": "getType", "acc": 1025, "dsc": "()I"}, {"nme": "isNull", "acc": 1025, "dsc": "()Z"}], "flds": [{"acc": 25, "nme": "INT_ITEM", "dsc": "I", "val": 3}, {"acc": 25, "nme": "LONG_ITEM", "dsc": "I", "val": 4}, {"acc": 25, "nme": "BIGINTEGER_ITEM", "dsc": "I", "val": 0}, {"acc": 25, "nme": "STRING_ITEM", "dsc": "I", "val": 1}, {"acc": 25, "nme": "LIST_ITEM", "dsc": "I", "val": 2}]}, "org/apache/maven/artifact/versioning/ComparableVersion$ListItem.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/artifact/versioning/ComparableVersion$ListItem", "super": "java/util/ArrayList", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getType", "acc": 1, "dsc": "()I"}, {"nme": "isNull", "acc": 1, "dsc": "()Z"}, {"nme": "normalize", "acc": 0, "dsc": "()V"}, {"nme": "compareTo", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/versioning/ComparableVersion$Item;)I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toListString", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/apache/maven/artifact/versioning/ComparableVersion$1;)V"}, {"nme": "access$100", "acc": 4104, "dsc": "(Lorg/apache/maven/artifact/versioning/ComparableVersion$ListItem;)Ljava/lang/String;"}], "flds": []}, "org/apache/maven/repository/legacy/metadata/ArtifactMetadata.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/repository/legacy/metadata/ArtifactMetadata", "super": "java/lang/Object", "mthds": [{"nme": "storedInArtifactVersionDirectory", "acc": 1025, "dsc": "()Z"}, {"nme": "storedInGroupDirectory", "acc": 1025, "dsc": "()Z"}, {"nme": "getGroupId", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getArtifactId", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getBaseVersion", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getLocalFilename", "acc": 1025, "dsc": "(Lorg/apache/maven/artifact/repository/ArtifactRepository;)Ljava/lang/String;"}, {"nme": "getRemoteFilename", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "merge", "acc": 1025, "dsc": "(Lorg/apache/maven/repository/legacy/metadata/ArtifactMetadata;)V"}, {"nme": "storeInLocalRepository", "acc": 1025, "dsc": "(Lorg/apache/maven/artifact/repository/ArtifactRepository;Lorg/apache/maven/artifact/repository/ArtifactRepository;)V", "exs": ["org/apache/maven/artifact/repository/metadata/RepositoryMetadataStoreException"]}, {"nme": "extendedToString", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "org/apache/maven/artifact/repository/ArtifactRepositoryPolicy.class": {"ver": 52, "acc": 131105, "nme": "org/apache/maven/artifact/repository/ArtifactRepositoryPolicy", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/repository/ArtifactRepositoryPolicy;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setEnabled", "acc": 1, "dsc": "(Z)V"}, {"nme": "setUpdatePolicy", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setChecksumPolicy", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "isEnabled", "acc": 1, "dsc": "()Z"}, {"nme": "getUpdatePolicy", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getChecksumPolicy", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "checkOutOfDate", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Date;)Z"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "merge", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/repository/ArtifactRepositoryPolicy;)V"}, {"nme": "ordinalOfCksumPolicy", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "ordinalOfUpdatePolicy", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}], "flds": [{"acc": 25, "nme": "UPDATE_POLICY_NEVER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "never"}, {"acc": 25, "nme": "UPDATE_POLICY_ALWAYS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "always"}, {"acc": 25, "nme": "UPDATE_POLICY_DAILY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "daily"}, {"acc": 25, "nme": "UPDATE_POLICY_INTERVAL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "interval"}, {"acc": 25, "nme": "CHECKSUM_POLICY_FAIL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "fail"}, {"acc": 25, "nme": "CHECKSUM_POLICY_WARN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "warn"}, {"acc": 25, "nme": "CHECKSUM_POLICY_IGNORE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "ignore"}, {"acc": 2, "nme": "enabled", "dsc": "Z"}, {"acc": 2, "nme": "updatePolicy", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "checksumPolicy", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "org/apache/maven/artifact/resolver/AbstractArtifactResolutionException.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/artifact/resolver/AbstractArtifactResolutionException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/List;)V", "sig": "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List<Lorg/apache/maven/artifact/repository/ArtifactRepository;>;Ljava/util/List<Ljava/lang/String;>;)V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;L<PERSON>va/util/List;Ljava/util/List;Ljava/lang/Throwable;)V", "sig": "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List<Lorg/apache/maven/artifact/repository/ArtifactRepository;>;Ljava/util/List<Ljava/lang/String;>;Ljava/lang/Throwable;)V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/apache/maven/artifact/Artifact;)V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/apache/maven/artifact/Artifact;Ljava/util/List;)V", "sig": "(Lja<PERSON>/lang/String;Lorg/apache/maven/artifact/Artifact;Ljava/util/List<Lorg/apache/maven/artifact/repository/ArtifactRepository;>;)V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/apache/maven/artifact/Artifact;Lja<PERSON>/util/List;Lja<PERSON>/lang/Throwable;)V", "sig": "(Ljava/lang/String;Lorg/apache/maven/artifact/Artifact;Ljava/util/List<Lorg/apache/maven/artifact/repository/ArtifactRepository;>;Ljava/lang/Throwable;)V"}, {"nme": "getArtifact", "acc": 1, "dsc": "()Lorg/apache/maven/artifact/Artifact;"}, {"nme": "getGroupId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getArtifactId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getType", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getClassifier", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getRemoteRepositories", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/artifact/repository/ArtifactRepository;>;"}, {"nme": "getOriginalMessage", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "constructArtifactPath", "acc": 12, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/String;>;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "constructMessageBase", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;L<PERSON>va/util/List;)Ljava/lang/String;", "sig": "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List<Lorg/apache/maven/artifact/repository/ArtifactRepository;>;Ljava/util/List<Ljava/lang/String;>;)Ljava/lang/String;"}, {"nme": "constructMissingArtifactMessage", "acc": 12, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;)Ljava/lang/String;", "sig": "(Lja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;)Ljava/lang/String;"}, {"nme": "getArtifactPath", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "groupId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "artifactId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "type", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "classifier", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "artifact", "dsc": "Lorg/apache/maven/artifact/Artifact;"}, {"acc": 2, "nme": "remoteRepositories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/artifact/repository/ArtifactRepository;>;"}, {"acc": 18, "nme": "originalMessage", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "path", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 24, "nme": "LS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/apache/maven/artifact/versioning/ArtifactVersion.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/artifact/versioning/ArtifactVersion", "super": "java/lang/Object", "mthds": [{"nme": "getMajorVersion", "acc": 1025, "dsc": "()I"}, {"nme": "getMinorVersion", "acc": 1025, "dsc": "()I"}, {"nme": "getIncrementalVersion", "acc": 1025, "dsc": "()I"}, {"nme": "getBuildNumber", "acc": 1025, "dsc": "()I"}, {"nme": "getQualifier", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "parseVersion", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "org/apache/maven/artifact/versioning/ComparableVersion$StringItem.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/artifact/versioning/ComparableVersion$StringItem", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)V"}, {"nme": "getType", "acc": 1, "dsc": "()I"}, {"nme": "isNull", "acc": 1, "dsc": "()Z"}, {"nme": "comparableQualifier", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "compareTo", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/versioning/ComparableVersion$Item;)I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "QUALIFIERS", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 26, "nme": "ALIASES", "dsc": "Ljava/util/Properties;"}, {"acc": 26, "nme": "RELEASE_VERSION_INDEX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "value", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/apache/maven/artifact/versioning/ComparableVersion.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/artifact/versioning/ComparableVersion", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "parseVersion", "acc": 17, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "parseItem", "acc": 10, "dsc": "(Z<PERSON>java/lang/String;)Lorg/apache/maven/artifact/versioning/ComparableVersion$Item;"}, {"nme": "stripLeadingZeroes", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "compareTo", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/versioning/ComparableVersion;)I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getCanonical", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "main", "acc": 137, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "compareTo", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}], "flds": [{"acc": 26, "nme": "MAX_INTITEM_LENGTH", "dsc": "I", "val": 9}, {"acc": 26, "nme": "MAX_LONGITEM_LENGTH", "dsc": "I", "val": 18}, {"acc": 2, "nme": "value", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "canonical", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "items", "dsc": "Lorg/apache/maven/artifact/versioning/ComparableVersion$ListItem;"}]}, "org/apache/maven/artifact/repository/ArtifactRepository.class": {"ver": 52, "acc": 132609, "nme": "org/apache/maven/artifact/repository/ArtifactRepository", "super": "java/lang/Object", "mthds": [{"nme": "pathOf", "acc": 1025, "dsc": "(Lorg/apache/maven/artifact/Artifact;)Ljava/lang/String;"}, {"nme": "pathOfRemoteRepositoryMetadata", "acc": 1025, "dsc": "(Lorg/apache/maven/artifact/metadata/ArtifactMetadata;)Ljava/lang/String;"}, {"nme": "pathOfLocalRepositoryMetadata", "acc": 1025, "dsc": "(Lorg/apache/maven/artifact/metadata/ArtifactMetadata;Lorg/apache/maven/artifact/repository/ArtifactRepository;)Ljava/lang/String;"}, {"nme": "getUrl", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setUrl", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getBasedir", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getProtocol", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getId", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setId", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getSnapshots", "acc": 1025, "dsc": "()Lorg/apache/maven/artifact/repository/ArtifactRepositoryPolicy;"}, {"nme": "setSnapshotUpdatePolicy", "acc": 1025, "dsc": "(Lorg/apache/maven/artifact/repository/ArtifactRepositoryPolicy;)V"}, {"nme": "getReleases", "acc": 1025, "dsc": "()Lorg/apache/maven/artifact/repository/ArtifactRepositoryPolicy;"}, {"nme": "setReleaseUpdatePolicy", "acc": 1025, "dsc": "(Lorg/apache/maven/artifact/repository/ArtifactRepositoryPolicy;)V"}, {"nme": "getLayout", "acc": 1025, "dsc": "()Lorg/apache/maven/artifact/repository/layout/ArtifactRepositoryLayout;"}, {"nme": "setLayout", "acc": 1025, "dsc": "(Lorg/apache/maven/artifact/repository/layout/ArtifactRepositoryLayout;)V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isUniqueVersion", "acc": 132097, "dsc": "()Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "isBlacklisted", "acc": 132097, "dsc": "()Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "setBlacklisted", "acc": 132097, "dsc": "(Z)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "isBlocked", "acc": 1025, "dsc": "()Z"}, {"nme": "setBlocked", "acc": 1025, "dsc": "(Z)V"}, {"nme": "find", "acc": 1025, "dsc": "(Lorg/apache/maven/artifact/Artifact;)Lorg/apache/maven/artifact/Artifact;"}, {"nme": "findVersions", "acc": 1025, "dsc": "(Lorg/apache/maven/artifact/Artifact;)Ljava/util/List;", "sig": "(Lorg/apache/maven/artifact/Artifact;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "isProjectAware", "acc": 1025, "dsc": "()Z"}, {"nme": "setAuthentication", "acc": 1025, "dsc": "(Lorg/apache/maven/artifact/repository/Authentication;)V"}, {"nme": "getAuthentication", "acc": 1025, "dsc": "()Lorg/apache/maven/artifact/repository/Authentication;"}, {"nme": "setProxy", "acc": 1025, "dsc": "(Lorg/apache/maven/repository/Proxy;)V"}, {"nme": "getProxy", "acc": 1025, "dsc": "()Lorg/apache/maven/repository/Proxy;"}, {"nme": "getMirroredRepositories", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/artifact/repository/ArtifactRepository;>;"}, {"nme": "setMirroredRepositories", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Lorg/apache/maven/artifact/repository/ArtifactRepository;>;)V"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "org/apache/maven/artifact/versioning/ComparableVersion$BigIntegerItem.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/artifact/versioning/ComparableVersion$BigIntegerItem", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getType", "acc": 1, "dsc": "()I"}, {"nme": "isNull", "acc": 1, "dsc": "()Z"}, {"nme": "compareTo", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/versioning/ComparableVersion$Item;)I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "value", "dsc": "Ljava/math/BigInteger;"}]}, "org/apache/maven/artifact/versioning/OverConstrainedVersionException.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/artifact/versioning/OverConstrainedVersionException", "super": "org/apache/maven/artifact/resolver/ArtifactResolutionException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/apache/maven/artifact/Artifact;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/apache/maven/artifact/Artifact;Ljava/util/List;)V", "sig": "(Lja<PERSON>/lang/String;Lorg/apache/maven/artifact/Artifact;Ljava/util/List<Lorg/apache/maven/artifact/repository/ArtifactRepository;>;)V"}], "flds": []}, "org/apache/maven/artifact/repository/Authentication.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/artifact/repository/Authentication", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getPassword", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setPassword", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getUsername", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setUsername", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getPassphrase", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setPassphrase", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getPrivateKey", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setPrivateKey", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 2, "nme": "privateKey", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "passphrase", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "username", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "password", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/apache/maven/artifact/versioning/DefaultArtifactVersion.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/artifact/versioning/DefaultArtifactVersion", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "compareTo", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/versioning/ArtifactVersion;)I"}, {"nme": "getMajorVersion", "acc": 1, "dsc": "()I"}, {"nme": "getMinorVersion", "acc": 1, "dsc": "()I"}, {"nme": "getIncrementalVersion", "acc": 1, "dsc": "()I"}, {"nme": "getBuildNumber", "acc": 1, "dsc": "()I"}, {"nme": "getQualifier", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "parseVersion", "acc": 17, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getNextIntegerToken", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/StringTokenizer;)<PERSON><PERSON><PERSON>/lang/Integer;"}, {"nme": "tryParseInt", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Integer;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "compareTo", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}], "flds": [{"acc": 2, "nme": "majorVersion", "dsc": "<PERSON><PERSON><PERSON>/lang/Integer;"}, {"acc": 2, "nme": "minorVersion", "dsc": "<PERSON><PERSON><PERSON>/lang/Integer;"}, {"acc": 2, "nme": "incrementalVersion", "dsc": "<PERSON><PERSON><PERSON>/lang/Integer;"}, {"acc": 2, "nme": "buildNumber", "dsc": "<PERSON><PERSON><PERSON>/lang/Integer;"}, {"acc": 2, "nme": "qualifier", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "comparable", "dsc": "Lorg/apache/maven/artifact/versioning/ComparableVersion;"}]}, "org/apache/maven/artifact/handler/ArtifactHandler.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/artifact/handler/ArtifactHandler", "super": "java/lang/Object", "mthds": [{"nme": "getExtension", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDirectory", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getClassifier", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getPackaging", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isIncludesDependencies", "acc": 1025, "dsc": "()Z"}, {"nme": "getLanguage", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isAddedToClasspath", "acc": 1025, "dsc": "()Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "ROLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/apache/maven/artifact/resolver/CyclicDependencyException.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/artifact/resolver/CyclicDependencyException", "super": "org/apache/maven/artifact/resolver/ArtifactResolutionException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/apache/maven/artifact/Artifact;)V"}, {"nme": "getArtifact", "acc": 1, "dsc": "()Lorg/apache/maven/artifact/Artifact;"}], "flds": [{"acc": 2, "nme": "artifact", "dsc": "Lorg/apache/maven/artifact/Artifact;"}]}, "org/apache/maven/artifact/versioning/ComparableVersion$LongItem.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/artifact/versioning/ComparableVersion$LongItem", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getType", "acc": 1, "dsc": "()I"}, {"nme": "isNull", "acc": 1, "dsc": "()Z"}, {"nme": "compareTo", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/versioning/ComparableVersion$Item;)I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "value", "dsc": "J"}]}, "org/apache/maven/artifact/repository/metadata/RepositoryMetadataStoreException.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/artifact/repository/metadata/RepositoryMetadataStoreException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Exception;)V"}], "flds": []}, "org/apache/maven/artifact/versioning/Restriction.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/artifact/versioning/Restriction", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/versioning/ArtifactVersion;ZLorg/apache/maven/artifact/versioning/ArtifactVersion;Z)V"}, {"nme": "getLowerBound", "acc": 1, "dsc": "()Lorg/apache/maven/artifact/versioning/ArtifactVersion;"}, {"nme": "isLowerBoundInclusive", "acc": 1, "dsc": "()Z"}, {"nme": "getUpperBound", "acc": 1, "dsc": "()Lorg/apache/maven/artifact/versioning/ArtifactVersion;"}, {"nme": "isUpperBoundInclusive", "acc": 1, "dsc": "()Z"}, {"nme": "containsVersion", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/versioning/ArtifactVersion;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "lowerBound", "dsc": "Lorg/apache/maven/artifact/versioning/ArtifactVersion;"}, {"acc": 18, "nme": "lowerBoundInclusive", "dsc": "Z"}, {"acc": 18, "nme": "upperBound", "dsc": "Lorg/apache/maven/artifact/versioning/ArtifactVersion;"}, {"acc": 18, "nme": "upperBoundInclusive", "dsc": "Z"}, {"acc": 25, "nme": "EVERYTHING", "dsc": "Lorg/apache/maven/artifact/versioning/Restriction;"}]}, "org/apache/maven/artifact/resolver/MultipleArtifactsNotFoundException.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/artifact/resolver/MultipleArtifactsNotFoundException", "super": "org/apache/maven/artifact/resolver/ArtifactResolutionException", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "(Lorg/apache/maven/artifact/Artifact;Ljava/util/List;Ljava/util/List;)V", "sig": "(Lorg/apache/maven/artifact/Artifact;Ljava/util/List<Lorg/apache/maven/artifact/Artifact;>;Ljava/util/List<Lorg/apache/maven/artifact/repository/ArtifactRepository;>;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/Artifact;Ljava/util/List;Ljava/util/List;Lja<PERSON>/util/List;)V", "sig": "(Lorg/apache/maven/artifact/Artifact;Ljava/util/List<Lorg/apache/maven/artifact/Artifact;>;Ljava/util/List<Lorg/apache/maven/artifact/Artifact;>;Ljava/util/List<Lorg/apache/maven/artifact/repository/ArtifactRepository;>;)V"}, {"nme": "getResolvedArtifacts", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/artifact/Artifact;>;"}, {"nme": "getMissingArtifacts", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/artifact/Artifact;>;"}, {"nme": "constructMessage", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Ljava/lang/String;", "sig": "(Lja<PERSON>/util/List<Lorg/apache/maven/artifact/Artifact;>;)Ljava/lang/String;"}], "flds": [{"acc": 18, "nme": "resolvedArtifacts", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/artifact/Artifact;>;"}, {"acc": 18, "nme": "missingArtifacts", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/artifact/Artifact;>;"}]}, "org/apache/maven/artifact/versioning/InvalidVersionSpecificationException.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/artifact/versioning/InvalidVersionSpecificationException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "org/apache/maven/artifact/Artifact.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/artifact/Artifact", "super": "java/lang/Object", "mthds": [{"nme": "getGroupId", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getArtifactId", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getVersion", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setVersion", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getScope", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getType", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getClassifier", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "hasClassifier", "acc": 1025, "dsc": "()Z"}, {"nme": "getFile", "acc": 1025, "dsc": "()Ljava/io/File;"}, {"nme": "setFile", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V"}, {"nme": "getBaseVersion", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBaseVersion", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getId", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDependencyConflictId", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "addMetadata", "acc": 1025, "dsc": "(Lorg/apache/maven/artifact/metadata/ArtifactMetadata;)V"}, {"nme": "getMetadataList", "acc": 1025, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Lorg/apache/maven/artifact/metadata/ArtifactMetadata;>;"}, {"nme": "setRepository", "acc": 1025, "dsc": "(Lorg/apache/maven/artifact/repository/ArtifactRepository;)V"}, {"nme": "getRepository", "acc": 1025, "dsc": "()Lorg/apache/maven/artifact/repository/ArtifactRepository;"}, {"nme": "updateVersion", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/apache/maven/artifact/repository/ArtifactRepository;)V"}, {"nme": "getDownloadUrl", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setDownloadUrl", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getDependencyFilter", "acc": 1025, "dsc": "()Lorg/apache/maven/artifact/resolver/filter/ArtifactFilter;"}, {"nme": "setDependencyFilter", "acc": 1025, "dsc": "(Lorg/apache/maven/artifact/resolver/filter/ArtifactFilter;)V"}, {"nme": "getArtifactHandler", "acc": 1025, "dsc": "()Lorg/apache/maven/artifact/handler/ArtifactHandler;"}, {"nme": "getDependencyTrail", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "setDependencyTrail", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/String;>;)V"}, {"nme": "setScope", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getVersionRange", "acc": 1025, "dsc": "()Lorg/apache/maven/artifact/versioning/VersionRange;"}, {"nme": "setVersionRange", "acc": 1025, "dsc": "(Lorg/apache/maven/artifact/versioning/VersionRange;)V"}, {"nme": "selectVersion", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setGroupId", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setArtifactId", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "isSnapshot", "acc": 1025, "dsc": "()Z"}, {"nme": "setResolved", "acc": 1025, "dsc": "(Z)V"}, {"nme": "isResolved", "acc": 1025, "dsc": "()Z"}, {"nme": "setResolvedVersion", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setArtifactHandler", "acc": 1025, "dsc": "(Lorg/apache/maven/artifact/handler/ArtifactHandler;)V"}, {"nme": "isRelease", "acc": 1025, "dsc": "()Z"}, {"nme": "setRelease", "acc": 1025, "dsc": "(Z)V"}, {"nme": "getAvailableVersions", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/artifact/versioning/ArtifactVersion;>;"}, {"nme": "setAvailableVersions", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Lorg/apache/maven/artifact/versioning/ArtifactVersion;>;)V"}, {"nme": "isOptional", "acc": 1025, "dsc": "()Z"}, {"nme": "setOptional", "acc": 1025, "dsc": "(Z)V"}, {"nme": "getSelectedVersion", "acc": 1025, "dsc": "()Lorg/apache/maven/artifact/versioning/ArtifactVersion;", "exs": ["org/apache/maven/artifact/versioning/OverConstrainedVersionException"]}, {"nme": "isSelectedVersionKnown", "acc": 1025, "dsc": "()Z", "exs": ["org/apache/maven/artifact/versioning/OverConstrainedVersionException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "RELEASE_VERSION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "RELEASE"}, {"acc": 25, "nme": "LATEST_VERSION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "LATEST"}, {"acc": 25, "nme": "SNAPSHOT_VERSION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "SNAPSHOT"}, {"acc": 25, "nme": "VERSION_FILE_PATTERN", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 25, "nme": "SCOPE_COMPILE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "compile"}, {"acc": 25, "nme": "SCOPE_COMPILE_PLUS_RUNTIME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "compile+runtime"}, {"acc": 25, "nme": "SCOPE_TEST", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "test"}, {"acc": 25, "nme": "SCOPE_RUNTIME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "runtime"}, {"acc": 25, "nme": "SCOPE_RUNTIME_PLUS_SYSTEM", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "runtime+system"}, {"acc": 25, "nme": "SCOPE_PROVIDED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "provided"}, {"acc": 25, "nme": "SCOPE_SYSTEM", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "system"}, {"acc": 25, "nme": "SCOPE_IMPORT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "import"}]}, "org/apache/maven/artifact/versioning/ComparableVersion$IntItem.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/artifact/versioning/ComparableVersion$IntItem", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getType", "acc": 1, "dsc": "()I"}, {"nme": "isNull", "acc": 1, "dsc": "()Z"}, {"nme": "compareTo", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/versioning/ComparableVersion$Item;)I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "value", "dsc": "I"}, {"acc": 25, "nme": "ZERO", "dsc": "Lorg/apache/maven/artifact/versioning/ComparableVersion$IntItem;"}]}, "org/apache/maven/artifact/resolver/ArtifactNotFoundException.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/artifact/resolver/ArtifactNotFoundException", "super": "org/apache/maven/artifact/resolver/AbstractArtifactResolutionException", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/apache/maven/artifact/Artifact;Ljava/util/List;)V", "sig": "(Lja<PERSON>/lang/String;Lorg/apache/maven/artifact/Artifact;Ljava/util/List<Lorg/apache/maven/artifact/repository/ArtifactRepository;>;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/apache/maven/artifact/Artifact;)V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/apache/maven/artifact/Artifact;Lja<PERSON>/util/List;Lja<PERSON>/lang/Throwable;)V", "sig": "(Ljava/lang/String;Lorg/apache/maven/artifact/Artifact;Ljava/util/List<Lorg/apache/maven/artifact/repository/ArtifactRepository;>;Ljava/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/lang/String;<PERSON><PERSON>va/util/List;Ljava/lang/Throwable;)V", "sig": "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List<Lorg/apache/maven/artifact/repository/ArtifactRepository;>;Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;Ljava/lang/Throwable;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/lang/String;Ljava/util/List;)V", "sig": "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List<Lorg/apache/maven/artifact/repository/ArtifactRepository;>;Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;)V"}, {"nme": "getDownloadUrl", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "downloadUrl", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/apache/maven/artifact/repository/layout/ArtifactRepositoryLayout.class": {"ver": 52, "acc": 132609, "nme": "org/apache/maven/artifact/repository/layout/ArtifactRepositoryLayout", "super": "java/lang/Object", "mthds": [{"nme": "getId", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "pathOf", "acc": 1025, "dsc": "(Lorg/apache/maven/artifact/Artifact;)Ljava/lang/String;"}, {"nme": "pathOfLocalRepositoryMetadata", "acc": 1025, "dsc": "(Lorg/apache/maven/artifact/metadata/ArtifactMetadata;Lorg/apache/maven/artifact/repository/ArtifactRepository;)Ljava/lang/String;"}, {"nme": "pathOfRemoteRepositoryMetadata", "acc": 1025, "dsc": "(Lorg/apache/maven/artifact/metadata/ArtifactMetadata;)Ljava/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "ROLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "org/apache/maven/artifact/repository/layout/ArtifactRepositoryLayout2.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/artifact/repository/layout/ArtifactRepositoryLayout2", "super": "java/lang/Object", "mthds": [{"nme": "newMavenArtifactRepository", "acc": 1025, "dsc": "(Ljava/lang/String;Ljava/lang/String;Lorg/apache/maven/artifact/repository/ArtifactRepositoryPolicy;Lorg/apache/maven/artifact/repository/ArtifactRepositoryPolicy;)Lorg/apache/maven/artifact/repository/ArtifactRepository;"}], "flds": []}, "org/apache/maven/artifact/resolver/ArtifactResolutionException.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/artifact/resolver/ArtifactResolutionException", "super": "org/apache/maven/artifact/resolver/AbstractArtifactResolutionException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;L<PERSON>va/util/List;Ljava/util/List;Ljava/lang/Throwable;)V", "sig": "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List<Lorg/apache/maven/artifact/repository/ArtifactRepository;>;Ljava/util/List<Ljava/lang/String;>;Ljava/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/String;L<PERSON><PERSON>/lang/String;Ljava/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/apache/maven/artifact/Artifact;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/apache/maven/artifact/Artifact;Ljava/util/List;)V", "sig": "(Lja<PERSON>/lang/String;Lorg/apache/maven/artifact/Artifact;Ljava/util/List<Lorg/apache/maven/artifact/repository/ArtifactRepository;>;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/apache/maven/artifact/Artifact;Ljava/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lorg/apache/maven/artifact/Artifact;Lja<PERSON>/util/List;Lja<PERSON>/lang/Throwable;)V", "sig": "(Ljava/lang/String;Lorg/apache/maven/artifact/Artifact;Ljava/util/List<Lorg/apache/maven/artifact/repository/ArtifactRepository;>;Ljava/lang/Throwable;)V"}], "flds": []}, "org/apache/maven/artifact/ArtifactUtils.class": {"ver": 52, "acc": 49, "nme": "org/apache/maven/artifact/ArtifactUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "isSnapshot", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "toSnapshotVersion", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "version<PERSON><PERSON><PERSON>", "acc": 9, "dsc": "(Lorg/apache/maven/artifact/Artifact;)Ljava/lang/String;"}, {"nme": "version<PERSON><PERSON><PERSON>", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "key", "acc": 9, "dsc": "(Lorg/apache/maven/artifact/Artifact;)Ljava/lang/String;"}, {"nme": "key", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)<PERSON>java/lang/String;"}, {"nme": "notBlank", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "artifactMapByVersionlessId", "acc": 9, "dsc": "(Lja<PERSON>/util/Collection;)Ljava/util/Map;", "sig": "(Ljava/util/Collection<Lorg/apache/maven/artifact/Artifact;>;)Ljava/util/Map<Ljava/lang/String;Lorg/apache/maven/artifact/Artifact;>;"}, {"nme": "copyArtifactSafe", "acc": 9, "dsc": "(Lorg/apache/maven/artifact/Artifact;)Lorg/apache/maven/artifact/Artifact;"}, {"nme": "copyArtifact", "acc": 9, "dsc": "(Lorg/apache/maven/artifact/Artifact;)Lorg/apache/maven/artifact/Artifact;"}, {"nme": "copyArtifacts", "acc": 9, "dsc": "(Lja<PERSON>/util/Collection;Ljava/util/Collection;)Ljava/util/Collection;", "sig": "<T::Ljava/util/Collection<Lorg/apache/maven/artifact/Artifact;>;>(Ljava/util/Collection<Lorg/apache/maven/artifact/Artifact;>;TT;)TT;"}, {"nme": "copyArtifacts", "acc": 9, "dsc": "(Ljava/util/Map;Ljava/util/Map;)Ljava/util/Map;", "sig": "<K:Ljava/lang/Object;T::Ljava/util/Map<TK;Lorg/apache/maven/artifact/Artifact;>;>(Ljava/util/Map<TK;+Lorg/apache/maven/artifact/Artifact;>;TT;)TT;"}, {"nme": "copyList", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Ljava/util/List;", "sig": "<T:Ljava/lang/Object;>(Ljava/util/List<TT;>;)Ljava/util/List<TT;>;"}], "flds": []}, "org/apache/maven/artifact/InvalidArtifactRTException.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/artifact/InvalidArtifactRTException", "super": "java/lang/RuntimeException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "getMessage", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getBaseMessage", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getArtifactId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getGroupId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getType", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getArtifactKey", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "groupId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "artifactId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "type", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "baseMessage", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/apache/maven/artifact/versioning/ComparableVersion$1.class": {"ver": 52, "acc": 4128, "nme": "org/apache/maven/artifact/versioning/ComparableVersion$1", "super": "java/lang/Object", "mthds": [], "flds": []}}}}