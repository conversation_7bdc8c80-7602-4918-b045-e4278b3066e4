@echo off
echo ========================================
echo    SbMagicHook Obfuscated Build Script
echo ========================================

echo.
echo [1/4] Building project with Maven...
call mvn clean package -DskipTests
if %errorlevel% neq 0 (
    echo ERROR: Maven build failed!
    pause
    exit /b 1
)

echo.
echo [2/4] Checking if Skidfuscator exists...
if not exist "skidfuscator.jar" (
    echo ERROR: skidfuscator.jar not found!
    echo Please download it from: https://github.com/skidfuscatordev/skidfuscator-java-obfuscator/releases
    pause
    exit /b 1
)

echo.
echo [3/4] Running Skidfuscator obfuscation...
java -jar skidfuscator.jar obfuscate target/SbMagicHook.jar -li=target/libs -o=target/SbMagicHook-protected.jar

if %errorlevel% neq 0 (
    echo ERROR: Obfuscation failed!
    pause
    exit /b 1
)

echo.
echo [4/4] Copying obfuscated JAR to plugins folder...
copy "target\SbMagicHook-protected.jar" "C:\Users\<USER>\Desktop\Survival Project - MC\plugins\SbMagicHook.jar"

echo.
echo ========================================
echo    Build completed successfully!
echo    Protected JAR: target/SbMagicHook-protected.jar
echo ========================================
pause
