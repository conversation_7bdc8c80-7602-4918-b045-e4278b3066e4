{"md5": "58a684bcc774fb44e4f73fa7466dc44d", "sha2": "497cda3149f3c661113f9a663e0270ce2566cc95", "sha256": "e047a67b204c434994253e2ab5bdff5fe8cb7ada9316ac3e754c39f900ea847b", "contents": {"classes": {"org/apache/maven/artifact/repository/metadata/Versioning.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/artifact/repository/metadata/Versioning", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "addSnapshotVersion", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/repository/metadata/SnapshotVersion;)V"}, {"nme": "addVersion", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/artifact/repository/metadata/Versioning;"}, {"nme": "getLastUpdated", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLatest", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getRelease", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getSnapshot", "acc": 1, "dsc": "()Lorg/apache/maven/artifact/repository/metadata/Snapshot;"}, {"nme": "getSnapshotVersions", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/artifact/repository/metadata/SnapshotVersion;>;"}, {"nme": "getVersions", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "removeSnapshotVersion", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/repository/metadata/SnapshotVersion;)V"}, {"nme": "removeVersion", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setLastUpdated", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setLatest", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setRelease", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setSnapshot", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/repository/metadata/Snapshot;)V"}, {"nme": "setSnapshotVersions", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Lorg/apache/maven/artifact/repository/metadata/SnapshotVersion;>;)V"}, {"nme": "setVersions", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/String;>;)V"}, {"nme": "updateTimestamp", "acc": 1, "dsc": "()V"}, {"nme": "setLastUpdatedTimestamp", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Date;)V"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "latest", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "release", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "versions", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 2, "nme": "lastUpdated", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "snapshot", "dsc": "Lorg/apache/maven/artifact/repository/metadata/Snapshot;"}, {"acc": 2, "nme": "snapshotVersions", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/artifact/repository/metadata/SnapshotVersion;>;"}]}, "org/apache/maven/artifact/repository/metadata/Metadata.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/artifact/repository/metadata/Metadata", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "addPlugin", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/repository/metadata/Plugin;)V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/artifact/repository/metadata/Metadata;"}, {"nme": "getArtifactId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getGroupId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getModelEncoding", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getModelVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getPlugins", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/artifact/repository/metadata/Plugin;>;"}, {"nme": "getVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getVersioning", "acc": 1, "dsc": "()Lorg/apache/maven/artifact/repository/metadata/Versioning;"}, {"nme": "removePlugin", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/repository/metadata/Plugin;)V"}, {"nme": "setArtifactId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setGroupId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setModelEncoding", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setModelVersion", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setPlugins", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Lorg/apache/maven/artifact/repository/metadata/Plugin;>;)V"}, {"nme": "setVersion", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setVersioning", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/repository/metadata/Versioning;)V"}, {"nme": "getSnapshotVersionKey", "acc": 2, "dsc": "(Lorg/apache/maven/artifact/repository/metadata/SnapshotVersion;)Ljava/lang/String;"}, {"nme": "merge", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/repository/metadata/Metadata;)Z"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "modelVersion", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "groupId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "artifactId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "versioning", "dsc": "Lorg/apache/maven/artifact/repository/metadata/Versioning;"}, {"acc": 2, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "plugins", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/artifact/repository/metadata/Plugin;>;"}, {"acc": 2, "nme": "modelEncoding", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/apache/maven/artifact/repository/metadata/io/xpp3/MetadataXpp3Reader$1.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/artifact/repository/metadata/io/xpp3/MetadataXpp3Reader$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "transform", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": []}, "org/apache/maven/artifact/repository/metadata/io/xpp3/MetadataXpp3Reader$ContentTransformer.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/artifact/repository/metadata/io/xpp3/MetadataXpp3Reader$ContentTransformer", "super": "java/lang/Object", "mthds": [{"nme": "transform", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": []}, "org/apache/maven/artifact/repository/metadata/io/xpp3/MetadataXpp3Writer.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/artifact/repository/metadata/io/xpp3/MetadataXpp3Writer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "setFileComment", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "write", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Writer;Lorg/apache/maven/artifact/repository/metadata/Metadata;)V", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Ljava/io/OutputStream;Lorg/apache/maven/artifact/repository/metadata/Metadata;)V", "exs": ["java/io/IOException"]}, {"nme": "writeMetadata", "acc": 2, "dsc": "(Lorg/apache/maven/artifact/repository/metadata/Metadata;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writePlugin", "acc": 2, "dsc": "(Lorg/apache/maven/artifact/repository/metadata/Plugin;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeSnapshot", "acc": 2, "dsc": "(Lorg/apache/maven/artifact/repository/metadata/Snapshot;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeSnapshotVersion", "acc": 2, "dsc": "(Lorg/apache/maven/artifact/repository/metadata/SnapshotVersion;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeVersioning", "acc": 2, "dsc": "(Lorg/apache/maven/artifact/repository/metadata/Versioning;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "NAMESPACE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "fileComment", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/apache/maven/artifact/repository/metadata/Snapshot.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/artifact/repository/metadata/Snapshot", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/artifact/repository/metadata/Snapshot;"}, {"nme": "getBuildNumber", "acc": 1, "dsc": "()I"}, {"nme": "getTimestamp", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isLocalCopy", "acc": 1, "dsc": "()Z"}, {"nme": "setBuildNumber", "acc": 1, "dsc": "(I)V"}, {"nme": "setLocalCopy", "acc": 1, "dsc": "(Z)V"}, {"nme": "setTimestamp", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "timestamp", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "buildNumber", "dsc": "I"}, {"acc": 2, "nme": "localCopy", "dsc": "Z"}]}, "org/apache/maven/artifact/repository/metadata/Plugin.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/artifact/repository/metadata/Plugin", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/artifact/repository/metadata/Plugin;"}, {"nme": "getArtifactId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getPrefix", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setArtifactId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setName", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setPrefix", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "prefix", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "artifactId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/apache/maven/artifact/repository/metadata/SnapshotVersion.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/artifact/repository/metadata/SnapshotVersion", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/artifact/repository/metadata/SnapshotVersion;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "getClassifier", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getExtension", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getUpdated", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "setClassifier", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setExtension", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setUpdated", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setVersion", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "classifier", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "extension", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "updated", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/apache/maven/artifact/repository/metadata/io/xpp3/MetadataXpp3Reader.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/artifact/repository/metadata/io/xpp3/MetadataXpp3Reader", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/apache/maven/artifact/repository/metadata/io/xpp3/MetadataXpp3Reader$ContentTransformer;)V"}, {"nme": "checkFieldWithDuplicate", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Ljava/lang/String;Ljava/lang/String;Ljava/util/Set;)Z", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "checkUnknownAttribute", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Ljava/lang/String;Ljava/lang/String;Z)V", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "checkUnknownElement", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)V", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "getAddDefaultEntities", "acc": 1, "dsc": "()Z"}, {"nme": "getBooleanValue", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;)Z", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getBooleanValue", "acc": 2, "dsc": "(<PERSON>ja<PERSON>/lang/String;<PERSON>java/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Ljava/lang/String;)Z", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getByteValue", "acc": 2, "dsc": "(<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)B", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getCharacterValue", "acc": 2, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;)C", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getDateValue", "acc": 2, "dsc": "(Lja<PERSON>/lang/String;<PERSON>java/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;)Ljava/util/Date;", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getDateValue", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;)Ljava/util/Date;", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getDoubleValue", "acc": 2, "dsc": "(<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)D", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getFloatValue", "acc": 2, "dsc": "(<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)F", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getIntegerValue", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)I", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getLongValue", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)J", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getRequiredAttributeValue", "acc": 2, "dsc": "(Lja<PERSON>/lang/String;<PERSON>java/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Ljava/lang/String;", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getShortValue", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)S", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getTrimmedValue", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "interpolatedTrimmed", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "nextTag", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;)I", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "read", "acc": 1, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/artifact/repository/metadata/Metadata;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "read", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;Z)Lorg/apache/maven/artifact/repository/metadata/Metadata;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "read", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;)Lorg/apache/maven/artifact/repository/metadata/Metadata;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "read", "acc": 1, "dsc": "(Ljava/io/InputStream;Z)Lorg/apache/maven/artifact/repository/metadata/Metadata;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "read", "acc": 1, "dsc": "(Ljava/io/InputStream;)Lorg/apache/maven/artifact/repository/metadata/Metadata;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseMetadata", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/artifact/repository/metadata/Metadata;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parsePlugin", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/artifact/repository/metadata/Plugin;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseSnapshot", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/artifact/repository/metadata/Snapshot;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseSnapshotVersion", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/artifact/repository/metadata/SnapshotVersion;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseVersioning", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/artifact/repository/metadata/Versioning;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "setAddDefaultEntities", "acc": 1, "dsc": "(Z)V"}], "flds": [{"acc": 2, "nme": "addDefaultEntities", "dsc": "Z"}, {"acc": 17, "nme": "contentTransformer", "dsc": "Lorg/apache/maven/artifact/repository/metadata/io/xpp3/MetadataXpp3Reader$ContentTransformer;"}]}}}}