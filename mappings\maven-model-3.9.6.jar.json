{"md5": "ac8747986567850914e2c7f0e85599b3", "sha2": "ac9a1c8a8cfa36f3a5489837e653ec0cd530d576", "sha256": "4f8f07fdb6b8701fa89a23a2edf830808fd65892d90cce40c0e6df7c8f2fcb62", "contents": {"classes": {"org/apache/maven/model/DistributionManagement.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/DistributionManagement", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/DistributionManagement;"}, {"nme": "getDownloadUrl", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "setOtherLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "getOtherLocation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getRelocation", "acc": 1, "dsc": "()Lorg/apache/maven/model/Relocation;"}, {"nme": "getRepository", "acc": 1, "dsc": "()Lorg/apache/maven/model/DeploymentRepository;"}, {"nme": "getSite", "acc": 1, "dsc": "()Lorg/apache/maven/model/Site;"}, {"nme": "getSnapshotRepository", "acc": 1, "dsc": "()Lorg/apache/maven/model/DeploymentRepository;"}, {"nme": "getStatus", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setDownloadUrl", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setRelocation", "acc": 1, "dsc": "(Lorg/apache/maven/model/Relocation;)V"}, {"nme": "setRepository", "acc": 1, "dsc": "(Lorg/apache/maven/model/DeploymentRepository;)V"}, {"nme": "setSite", "acc": 1, "dsc": "(Lorg/apache/maven/model/Site;)V"}, {"nme": "setSnapshotRepository", "acc": 1, "dsc": "(Lorg/apache/maven/model/DeploymentRepository;)V"}, {"nme": "setStatus", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "repository", "dsc": "Lorg/apache/maven/model/DeploymentRepository;"}, {"acc": 2, "nme": "snapshotRepository", "dsc": "Lorg/apache/maven/model/DeploymentRepository;"}, {"acc": 2, "nme": "site", "dsc": "Lorg/apache/maven/model/Site;"}, {"acc": 2, "nme": "downloadUrl", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "relocation", "dsc": "Lorg/apache/maven/model/Relocation;"}, {"acc": 2, "nme": "status", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "locations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "repositoryLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "snapshotRepositoryLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "siteLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "downloadUrlLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "relocationLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "statusLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}]}, "org/apache/maven/model/ActivationProperty.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/ActivationProperty", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/ActivationProperty;"}, {"nme": "getLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "setOtherLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "getOtherLocation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getValue", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setName", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "value", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "locations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "nameLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "valueLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}]}, "org/apache/maven/model/io/xpp3/MavenXpp3Reader$1.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/io/xpp3/MavenXpp3Reader$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "transform", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": []}, "org/apache/maven/model/MailingList.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/MailingList", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "addOtherArchive", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/MailingList;"}, {"nme": "getArchive", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getOtherArchives", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "setOtherLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "getOtherLocation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getPost", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getSubscribe", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getUnsubscribe", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "removeOtherArchive", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setArchive", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setName", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setOtherArchives", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/String;>;)V"}, {"nme": "setPost", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setSubscribe", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setUnsubscribe", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "subscribe", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "unsubscribe", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "post", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "archive", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "otherArchives", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 2, "nme": "locations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "nameLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "subscribeLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "unsubscribeLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "postLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "archiveLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "otherArchivesLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}]}, "org/apache/maven/model/merge/ModelMerger$ContributorKeyComputer.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/merge/ModelMerger$ContributorKeyComputer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;)V"}, {"nme": "key", "acc": 1, "dsc": "(Lorg/apache/maven/model/Contributor;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "key", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;Lorg/apache/maven/model/merge/ModelMerger$1;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lorg/apache/maven/model/merge/ModelMerger;"}]}, "org/apache/maven/model/merge/ModelMerger$ReportPluginKeyComputer.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/merge/ModelMerger$ReportPluginKeyComputer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;)V"}, {"nme": "key", "acc": 1, "dsc": "(Lorg/apache/maven/model/ReportPlugin;)Ljava/lang/Object;"}, {"nme": "key", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;Lorg/apache/maven/model/merge/ModelMerger$1;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lorg/apache/maven/model/merge/ModelMerger;"}]}, "org/apache/maven/model/Parent.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/Parent", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/Parent;"}, {"nme": "getArtifactId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getGroupId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "setOtherLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "getOtherLocation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getRelativePath", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setArtifactId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setGroupId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setRelativePath", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setVersion", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "groupId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "artifactId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "relativePath", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "locations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "groupIdLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "artifactIdLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "versionLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "relativePathLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}]}, "org/apache/maven/model/ModelBase.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/ModelBase", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "addDependency", "acc": 1, "dsc": "(Lorg/apache/maven/model/Dependency;)V"}, {"nme": "addModule", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "addPluginRepository", "acc": 1, "dsc": "(Lorg/apache/maven/model/Repository;)V"}, {"nme": "addProperty", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "addRepository", "acc": 1, "dsc": "(Lorg/apache/maven/model/Repository;)V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/ModelBase;"}, {"nme": "getDependencies", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/Dependency;>;"}, {"nme": "getDependencyManagement", "acc": 1, "dsc": "()Lorg/apache/maven/model/DependencyManagement;"}, {"nme": "getDistributionManagement", "acc": 1, "dsc": "()Lorg/apache/maven/model/DistributionManagement;"}, {"nme": "getLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getModules", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "setOtherLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "getOtherLocation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getPluginRepositories", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/Repository;>;"}, {"nme": "getProperties", "acc": 1, "dsc": "()Ljava/util/Properties;"}, {"nme": "getReporting", "acc": 1, "dsc": "()Lorg/apache/maven/model/Reporting;"}, {"nme": "getReports", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getRepositories", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/Repository;>;"}, {"nme": "removeDependency", "acc": 1, "dsc": "(Lorg/apache/maven/model/Dependency;)V"}, {"nme": "removeModule", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "removePluginRepository", "acc": 1, "dsc": "(Lorg/apache/maven/model/Repository;)V"}, {"nme": "removeRepository", "acc": 1, "dsc": "(Lorg/apache/maven/model/Repository;)V"}, {"nme": "setDependencies", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(<PERSON><PERSON><PERSON>/util/List<Lorg/apache/maven/model/Dependency;>;)V"}, {"nme": "setDependencyManagement", "acc": 1, "dsc": "(Lorg/apache/maven/model/DependencyManagement;)V"}, {"nme": "setDistributionManagement", "acc": 1, "dsc": "(Lorg/apache/maven/model/DistributionManagement;)V"}, {"nme": "setModules", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/String;>;)V"}, {"nme": "setPluginRepositories", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(<PERSON>java/util/List<Lorg/apache/maven/model/Repository;>;)V"}, {"nme": "setProperties", "acc": 1, "dsc": "(Ljava/util/Properties;)V"}, {"nme": "setReporting", "acc": 1, "dsc": "(Lorg/apache/maven/model/Reporting;)V"}, {"nme": "setReports", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "setRepositories", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(<PERSON>java/util/List<Lorg/apache/maven/model/Repository;>;)V"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "modules", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 2, "nme": "distributionManagement", "dsc": "Lorg/apache/maven/model/DistributionManagement;"}, {"acc": 2, "nme": "properties", "dsc": "Ljava/util/Properties;"}, {"acc": 2, "nme": "dependencyManagement", "dsc": "Lorg/apache/maven/model/DependencyManagement;"}, {"acc": 2, "nme": "dependencies", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/model/Dependency;>;"}, {"acc": 2, "nme": "repositories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/model/Repository;>;"}, {"acc": 2, "nme": "pluginRepositories", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/model/Repository;>;"}, {"acc": 2, "nme": "reports", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 2, "nme": "reporting", "dsc": "Lorg/apache/maven/model/Reporting;"}, {"acc": 2, "nme": "locations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "modulesLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "distributionManagementLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "propertiesLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "dependencyManagementLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "dependenciesLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "repositoriesLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "pluginRepositoriesLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "reportsLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "reportingLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}]}, "org/apache/maven/model/io/xpp3/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/apache/maven/model/io/xpp3/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/apache/maven/model/InputLocation.class": {"ver": 52, "acc": 49, "nme": "org/apache/maven/model/InputLocation", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(II)V"}, {"nme": "<init>", "acc": 1, "dsc": "(IILorg/apache/maven/model/InputSource;)V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/InputLocation;"}, {"nme": "getColumnNumber", "acc": 1, "dsc": "()I"}, {"nme": "getLineNumber", "acc": 1, "dsc": "()I"}, {"nme": "getLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getLocations", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "setOtherLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "getOtherLocation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getSource", "acc": 1, "dsc": "()Lorg/apache/maven/model/InputSource;"}, {"nme": "merge", "acc": 9, "dsc": "(Lorg/apache/maven/model/InputLocation;Lorg/apache/maven/model/InputLocation;Z)Lorg/apache/maven/model/InputLocation;"}, {"nme": "merge", "acc": 9, "dsc": "(Lorg/apache/maven/model/InputLocation;Lorg/apache/maven/model/InputLocation;Ljava/util/Collection;)Lorg/apache/maven/model/InputLocation;", "sig": "(Lorg/apache/maven/model/InputLocation;Lorg/apache/maven/model/InputLocation;Ljava/util/Collection<Ljava/lang/Integer;>;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "setLocations", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "lineNumber", "dsc": "I"}, {"acc": 2, "nme": "columnNumber", "dsc": "I"}, {"acc": 2, "nme": "source", "dsc": "Lorg/apache/maven/model/InputSource;"}, {"acc": 2, "nme": "locations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}]}, "org/apache/maven/model/merge/ModelMerger$ReportSetKeyComputer.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/merge/ModelMerger$ReportSetKeyComputer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;)V"}, {"nme": "key", "acc": 1, "dsc": "(Lorg/apache/maven/model/ReportSet;)Ljava/lang/Object;"}, {"nme": "key", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;Lorg/apache/maven/model/merge/ModelMerger$1;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lorg/apache/maven/model/merge/ModelMerger;"}]}, "org/apache/maven/model/Notifier.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/Notifier", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "addConfiguration", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/Notifier;"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getConfiguration", "acc": 1, "dsc": "()Ljava/util/Properties;"}, {"nme": "getLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "setOtherLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "getOtherLocation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getType", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isSendOnError", "acc": 1, "dsc": "()Z"}, {"nme": "isSendOnFailure", "acc": 1, "dsc": "()Z"}, {"nme": "isSendOnSuccess", "acc": 1, "dsc": "()Z"}, {"nme": "isSendOnWarning", "acc": 1, "dsc": "()Z"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setConfiguration", "acc": 1, "dsc": "(Ljava/util/Properties;)V"}, {"nme": "setSendOnError", "acc": 1, "dsc": "(Z)V"}, {"nme": "setSendOnFailure", "acc": 1, "dsc": "(Z)V"}, {"nme": "setSendOnSuccess", "acc": 1, "dsc": "(Z)V"}, {"nme": "setSendOnWarning", "acc": 1, "dsc": "(Z)V"}, {"nme": "setType", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "type", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "sendOnError", "dsc": "Z"}, {"acc": 2, "nme": "sendOnFailure", "dsc": "Z"}, {"acc": 2, "nme": "sendOnSuccess", "dsc": "Z"}, {"acc": 2, "nme": "sendOnWarning", "dsc": "Z"}, {"acc": 2, "nme": "address", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "configuration", "dsc": "Ljava/util/Properties;"}, {"acc": 2, "nme": "locations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "typeLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "sendOnErrorLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "sendOnFailureLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "sendOnSuccessLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "sendOnWarningLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "addressLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "configurationLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}]}, "org/apache/maven/model/merge/ModelMerger$DependencyKeyComputer.class": {"ver": 52, "acc": 48, "nme": "org/apache/maven/model/merge/ModelMerger$DependencyKeyComputer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;)V"}, {"nme": "key", "acc": 1, "dsc": "(Lorg/apache/maven/model/Dependency;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "key", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;Lorg/apache/maven/model/merge/ModelMerger$1;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lorg/apache/maven/model/merge/ModelMerger;"}]}, "org/apache/maven/model/DependencyManagement.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/DependencyManagement", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "addDependency", "acc": 1, "dsc": "(Lorg/apache/maven/model/Dependency;)V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/DependencyManagement;"}, {"nme": "getDependencies", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/Dependency;>;"}, {"nme": "getLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "setOtherLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "getOtherLocation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "removeDependency", "acc": 1, "dsc": "(Lorg/apache/maven/model/Dependency;)V"}, {"nme": "setDependencies", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(<PERSON><PERSON><PERSON>/util/List<Lorg/apache/maven/model/Dependency;>;)V"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "dependencies", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/model/Dependency;>;"}, {"acc": 2, "nme": "locations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "dependenciesLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}]}, "org/apache/maven/model/PluginContainer.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/PluginContainer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "addPlugin", "acc": 1, "dsc": "(Lorg/apache/maven/model/Plugin;)V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/PluginContainer;"}, {"nme": "getLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "setOtherLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "getOtherLocation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getPlugins", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/Plugin;>;"}, {"nme": "removePlugin", "acc": 1, "dsc": "(Lorg/apache/maven/model/Plugin;)V"}, {"nme": "setPlugins", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(<PERSON>ja<PERSON>/util/List<Lorg/apache/maven/model/Plugin;>;)V"}, {"nme": "flushPluginMap", "acc": 33, "dsc": "()V"}, {"nme": "getPluginsAsMap", "acc": 33, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Lorg/apache/maven/model/Plugin;>;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "plugins", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/model/Plugin;>;"}, {"acc": 2, "nme": "locations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "pluginsLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 0, "nme": "pluginMap", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lorg/apache/maven/model/Plugin;>;"}]}, "org/apache/maven/model/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/apache/maven/model/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/apache/maven/model/ReportSet.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/ReportSet", "super": "org/apache/maven/model/ConfigurationContainer", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "addReport", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/ReportSet;"}, {"nme": "getId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getReports", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "removeReport", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setReports", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/String;>;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "clone", "acc": 4161, "dsc": "()Lorg/apache/maven/model/ConfigurationContainer;"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "id", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "reports", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}]}, "org/apache/maven/model/Exclusion.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/Exclusion", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/Exclusion;"}, {"nme": "getArtifactId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getGroupId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "setOtherLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "getOtherLocation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "setArtifactId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setGroupId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "groupId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "artifactId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "locations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "groupIdLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "artifactIdLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}]}, "org/apache/maven/model/Scm.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/Scm", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/Scm;"}, {"nme": "getChildScmConnectionInheritAppendPath", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getChildScmDeveloperConnectionInheritAppendPath", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getChildScmUrlInheritAppendPath", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getConnection", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDeveloperConnection", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "setOtherLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "getOtherLocation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getTag", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getUrl", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setChildScmConnectionInheritAppendPath", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setChildScmDeveloperConnectionInheritAppendPath", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setChildScmUrlInheritAppendPath", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setConnection", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setDeveloperConnection", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setTag", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setUrl", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "isChildScmConnectionInheritAppendPath", "acc": 1, "dsc": "()Z"}, {"nme": "setChildScmConnectionInheritAppendPath", "acc": 1, "dsc": "(Z)V"}, {"nme": "isChildScmDeveloperConnectionInheritAppendPath", "acc": 1, "dsc": "()Z"}, {"nme": "setChildScmDeveloperConnectionInheritAppendPath", "acc": 1, "dsc": "(Z)V"}, {"nme": "isChildScmUrlInheritAppendPath", "acc": 1, "dsc": "()Z"}, {"nme": "setChildScmUrlInheritAppendPath", "acc": 1, "dsc": "(Z)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "connection", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "developerConnection", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "tag", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "url", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "childScmConnectionInheritAppendPath", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "childScmDeveloperConnectionInheritAppendPath", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "childScmUrlInheritAppendPath", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "locations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "connectionLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "developerConnectionLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "tagLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "urlLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "childScmConnectionInheritAppendPathLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "childScmDeveloperConnectionInheritAppendPathLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "childScmUrlInheritAppendPathLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}]}, "org/apache/maven/model/BuildBase.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/BuildBase", "super": "org/apache/maven/model/PluginConfiguration", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "addFilter", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "addResource", "acc": 1, "dsc": "(Lorg/apache/maven/model/Resource;)V"}, {"nme": "addTestResource", "acc": 1, "dsc": "(Lorg/apache/maven/model/Resource;)V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/BuildBase;"}, {"nme": "getDefaultGoal", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDirectory", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getFilters", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "getFinalName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getResources", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/Resource;>;"}, {"nme": "getTestResources", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/Resource;>;"}, {"nme": "removeFilter", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "removeResource", "acc": 1, "dsc": "(Lorg/apache/maven/model/Resource;)V"}, {"nme": "removeTestResource", "acc": 1, "dsc": "(Lorg/apache/maven/model/Resource;)V"}, {"nme": "setDefaultGoal", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setDirectory", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setFilters", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/String;>;)V"}, {"nme": "setFinalName", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setResources", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Lorg/apache/maven/model/Resource;>;)V"}, {"nme": "setTestResources", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Lorg/apache/maven/model/Resource;>;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "clone", "acc": 4161, "dsc": "()Lorg/apache/maven/model/PluginConfiguration;"}, {"nme": "clone", "acc": 4161, "dsc": "()Lorg/apache/maven/model/PluginContainer;"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "defaultGoal", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "resources", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/model/Resource;>;"}, {"acc": 2, "nme": "testResources", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/model/Resource;>;"}, {"acc": 2, "nme": "directory", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "finalName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "filters", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}]}, "org/apache/maven/model/DeploymentRepository.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/DeploymentRepository", "super": "org/apache/maven/model/Repository", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/DeploymentRepository;"}, {"nme": "isUniqueVersion", "acc": 1, "dsc": "()Z"}, {"nme": "setUniqueVersion", "acc": 1, "dsc": "(Z)V"}, {"nme": "clone", "acc": 4161, "dsc": "()Lorg/apache/maven/model/Repository;"}, {"nme": "clone", "acc": 4161, "dsc": "()Lorg/apache/maven/model/RepositoryBase;"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "uniqueVersion", "dsc": "Z"}]}, "org/apache/maven/model/merge/ModelMerger.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/merge/ModelMerger", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "merge", "acc": 1, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<**>;)V"}, {"nme": "mergeModel", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_ModelVersion", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_Parent", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_GroupId", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_ArtifactId", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_ChildProjectUrlInheritAppendPath", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_Version", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_Packaging", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_Name", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_Description", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_Url", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_InceptionYear", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_Organization", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_Licenses", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_MailingLists", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_Developers", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_Contributors", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_IssueManagement", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_Scm", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_CiManagement", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_Prerequisites", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_Build", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModel_Profiles", "acc": 4, "dsc": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Model;Lorg/apache/maven/model/Model;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModelBase", "acc": 4, "dsc": "(Lorg/apache/maven/model/ModelBase;Lorg/apache/maven/model/ModelBase;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/ModelBase;Lorg/apache/maven/model/ModelBase;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModelBase_Modules", "acc": 4, "dsc": "(Lorg/apache/maven/model/ModelBase;Lorg/apache/maven/model/ModelBase;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/ModelBase;Lorg/apache/maven/model/ModelBase;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModelBase_Dependencies", "acc": 4, "dsc": "(Lorg/apache/maven/model/ModelBase;Lorg/apache/maven/model/ModelBase;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/ModelBase;Lorg/apache/maven/model/ModelBase;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModelBase_Repositories", "acc": 4, "dsc": "(Lorg/apache/maven/model/ModelBase;Lorg/apache/maven/model/ModelBase;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/ModelBase;Lorg/apache/maven/model/ModelBase;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModelBase_PluginRepositories", "acc": 4, "dsc": "(Lorg/apache/maven/model/ModelBase;Lorg/apache/maven/model/ModelBase;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/ModelBase;Lorg/apache/maven/model/ModelBase;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModelBase_DistributionManagement", "acc": 4, "dsc": "(Lorg/apache/maven/model/ModelBase;Lorg/apache/maven/model/ModelBase;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/ModelBase;Lorg/apache/maven/model/ModelBase;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModelBase_Reporting", "acc": 4, "dsc": "(Lorg/apache/maven/model/ModelBase;Lorg/apache/maven/model/ModelBase;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/ModelBase;Lorg/apache/maven/model/ModelBase;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModelBase_DependencyManagement", "acc": 4, "dsc": "(Lorg/apache/maven/model/ModelBase;Lorg/apache/maven/model/ModelBase;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/ModelBase;Lorg/apache/maven/model/ModelBase;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeModelBase_Properties", "acc": 4, "dsc": "(Lorg/apache/maven/model/ModelBase;Lorg/apache/maven/model/ModelBase;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/ModelBase;Lorg/apache/maven/model/ModelBase;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeDistributionManagement", "acc": 4, "dsc": "(Lorg/apache/maven/model/DistributionManagement;Lorg/apache/maven/model/DistributionManagement;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/DistributionManagement;Lorg/apache/maven/model/DistributionManagement;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeDistributionManagement_Repository", "acc": 4, "dsc": "(Lorg/apache/maven/model/DistributionManagement;Lorg/apache/maven/model/DistributionManagement;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/DistributionManagement;Lorg/apache/maven/model/DistributionManagement;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeDistributionManagement_SnapshotRepository", "acc": 4, "dsc": "(Lorg/apache/maven/model/DistributionManagement;Lorg/apache/maven/model/DistributionManagement;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/DistributionManagement;Lorg/apache/maven/model/DistributionManagement;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeDistributionManagement_Site", "acc": 4, "dsc": "(Lorg/apache/maven/model/DistributionManagement;Lorg/apache/maven/model/DistributionManagement;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/DistributionManagement;Lorg/apache/maven/model/DistributionManagement;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeDistributionManagement_Status", "acc": 4, "dsc": "(Lorg/apache/maven/model/DistributionManagement;Lorg/apache/maven/model/DistributionManagement;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/DistributionManagement;Lorg/apache/maven/model/DistributionManagement;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeDistributionManagement_DownloadUrl", "acc": 4, "dsc": "(Lorg/apache/maven/model/DistributionManagement;Lorg/apache/maven/model/DistributionManagement;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/DistributionManagement;Lorg/apache/maven/model/DistributionManagement;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeRelocation", "acc": 4, "dsc": "(Lorg/apache/maven/model/Relocation;Lorg/apache/maven/model/Relocation;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Relocation;Lorg/apache/maven/model/Relocation;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeRelocation_GroupId", "acc": 4, "dsc": "(Lorg/apache/maven/model/Relocation;Lorg/apache/maven/model/Relocation;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Relocation;Lorg/apache/maven/model/Relocation;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeRelocation_ArtifactId", "acc": 4, "dsc": "(Lorg/apache/maven/model/Relocation;Lorg/apache/maven/model/Relocation;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Relocation;Lorg/apache/maven/model/Relocation;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeRelocation_Version", "acc": 4, "dsc": "(Lorg/apache/maven/model/Relocation;Lorg/apache/maven/model/Relocation;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Relocation;Lorg/apache/maven/model/Relocation;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeRelocation_Message", "acc": 4, "dsc": "(Lorg/apache/maven/model/Relocation;Lorg/apache/maven/model/Relocation;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Relocation;Lorg/apache/maven/model/Relocation;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeDeploymentRepository", "acc": 4, "dsc": "(Lorg/apache/maven/model/DeploymentRepository;Lorg/apache/maven/model/DeploymentRepository;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/DeploymentRepository;Lorg/apache/maven/model/DeploymentRepository;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeDeploymentRepository_UniqueVersion", "acc": 4, "dsc": "(Lorg/apache/maven/model/DeploymentRepository;Lorg/apache/maven/model/DeploymentRepository;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/DeploymentRepository;Lorg/apache/maven/model/DeploymentRepository;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeSite", "acc": 4, "dsc": "(Lorg/apache/maven/model/Site;Lorg/apache/maven/model/Site;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Site;Lorg/apache/maven/model/Site;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeSite_ChildSiteUrlInheritAppendPath", "acc": 4, "dsc": "(Lorg/apache/maven/model/Site;Lorg/apache/maven/model/Site;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Site;Lorg/apache/maven/model/Site;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeSite_Id", "acc": 4, "dsc": "(Lorg/apache/maven/model/Site;Lorg/apache/maven/model/Site;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Site;Lorg/apache/maven/model/Site;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeSite_Name", "acc": 4, "dsc": "(Lorg/apache/maven/model/Site;Lorg/apache/maven/model/Site;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Site;Lorg/apache/maven/model/Site;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeSite_Url", "acc": 4, "dsc": "(Lorg/apache/maven/model/Site;Lorg/apache/maven/model/Site;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Site;Lorg/apache/maven/model/Site;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeRepository", "acc": 4, "dsc": "(Lorg/apache/maven/model/Repository;Lorg/apache/maven/model/Repository;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Repository;Lorg/apache/maven/model/Repository;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeRepository_Releases", "acc": 4, "dsc": "(Lorg/apache/maven/model/Repository;Lorg/apache/maven/model/Repository;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Repository;Lorg/apache/maven/model/Repository;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeRepository_Snapshots", "acc": 4, "dsc": "(Lorg/apache/maven/model/Repository;Lorg/apache/maven/model/Repository;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Repository;Lorg/apache/maven/model/Repository;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeRepositoryBase", "acc": 4, "dsc": "(Lorg/apache/maven/model/RepositoryBase;Lorg/apache/maven/model/RepositoryBase;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/RepositoryBase;Lorg/apache/maven/model/RepositoryBase;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeRepositoryBase_Id", "acc": 4, "dsc": "(Lorg/apache/maven/model/RepositoryBase;Lorg/apache/maven/model/RepositoryBase;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/RepositoryBase;Lorg/apache/maven/model/RepositoryBase;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeRepositoryBase_Url", "acc": 4, "dsc": "(Lorg/apache/maven/model/RepositoryBase;Lorg/apache/maven/model/RepositoryBase;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/RepositoryBase;Lorg/apache/maven/model/RepositoryBase;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeRepositoryBase_Name", "acc": 4, "dsc": "(Lorg/apache/maven/model/RepositoryBase;Lorg/apache/maven/model/RepositoryBase;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/RepositoryBase;Lorg/apache/maven/model/RepositoryBase;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeRepositoryBase_Layout", "acc": 4, "dsc": "(Lorg/apache/maven/model/RepositoryBase;Lorg/apache/maven/model/RepositoryBase;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/RepositoryBase;Lorg/apache/maven/model/RepositoryBase;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeRepositoryPolicy", "acc": 4, "dsc": "(Lorg/apache/maven/model/RepositoryPolicy;Lorg/apache/maven/model/RepositoryPolicy;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/RepositoryPolicy;Lorg/apache/maven/model/RepositoryPolicy;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeRepositoryPolicy_Enabled", "acc": 4, "dsc": "(Lorg/apache/maven/model/RepositoryPolicy;Lorg/apache/maven/model/RepositoryPolicy;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/RepositoryPolicy;Lorg/apache/maven/model/RepositoryPolicy;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeRepositoryPolicy_UpdatePolicy", "acc": 4, "dsc": "(Lorg/apache/maven/model/RepositoryPolicy;Lorg/apache/maven/model/RepositoryPolicy;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/RepositoryPolicy;Lorg/apache/maven/model/RepositoryPolicy;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeRepositoryPolicy_ChecksumPolicy", "acc": 4, "dsc": "(Lorg/apache/maven/model/RepositoryPolicy;Lorg/apache/maven/model/RepositoryPolicy;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/RepositoryPolicy;Lorg/apache/maven/model/RepositoryPolicy;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeDependency", "acc": 4, "dsc": "(Lorg/apache/maven/model/Dependency;Lorg/apache/maven/model/Dependency;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Dependency;Lorg/apache/maven/model/Dependency;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeDependency_GroupId", "acc": 4, "dsc": "(Lorg/apache/maven/model/Dependency;Lorg/apache/maven/model/Dependency;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Dependency;Lorg/apache/maven/model/Dependency;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeDependency_ArtifactId", "acc": 4, "dsc": "(Lorg/apache/maven/model/Dependency;Lorg/apache/maven/model/Dependency;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Dependency;Lorg/apache/maven/model/Dependency;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeDependency_Version", "acc": 4, "dsc": "(Lorg/apache/maven/model/Dependency;Lorg/apache/maven/model/Dependency;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Dependency;Lorg/apache/maven/model/Dependency;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeDependency_Type", "acc": 4, "dsc": "(Lorg/apache/maven/model/Dependency;Lorg/apache/maven/model/Dependency;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Dependency;Lorg/apache/maven/model/Dependency;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeDependency_Classifier", "acc": 4, "dsc": "(Lorg/apache/maven/model/Dependency;Lorg/apache/maven/model/Dependency;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Dependency;Lorg/apache/maven/model/Dependency;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeDependency_Scope", "acc": 4, "dsc": "(Lorg/apache/maven/model/Dependency;Lorg/apache/maven/model/Dependency;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Dependency;Lorg/apache/maven/model/Dependency;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeDependency_SystemPath", "acc": 4, "dsc": "(Lorg/apache/maven/model/Dependency;Lorg/apache/maven/model/Dependency;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Dependency;Lorg/apache/maven/model/Dependency;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeDependency_Optional", "acc": 4, "dsc": "(Lorg/apache/maven/model/Dependency;Lorg/apache/maven/model/Dependency;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Dependency;Lorg/apache/maven/model/Dependency;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeDependency_Exclusions", "acc": 4, "dsc": "(Lorg/apache/maven/model/Dependency;Lorg/apache/maven/model/Dependency;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Dependency;Lorg/apache/maven/model/Dependency;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeExclusion", "acc": 4, "dsc": "(Lorg/apache/maven/model/Exclusion;Lorg/apache/maven/model/Exclusion;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Exclusion;Lorg/apache/maven/model/Exclusion;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeExclusion_GroupId", "acc": 4, "dsc": "(Lorg/apache/maven/model/Exclusion;Lorg/apache/maven/model/Exclusion;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Exclusion;Lorg/apache/maven/model/Exclusion;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeExclusion_ArtifactId", "acc": 4, "dsc": "(Lorg/apache/maven/model/Exclusion;Lorg/apache/maven/model/Exclusion;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Exclusion;Lorg/apache/maven/model/Exclusion;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeReporting", "acc": 4, "dsc": "(Lorg/apache/maven/model/Reporting;Lorg/apache/maven/model/Reporting;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Reporting;Lorg/apache/maven/model/Reporting;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeReporting_OutputDirectory", "acc": 4, "dsc": "(Lorg/apache/maven/model/Reporting;Lorg/apache/maven/model/Reporting;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Reporting;Lorg/apache/maven/model/Reporting;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeReporting_ExcludeDefaults", "acc": 4, "dsc": "(Lorg/apache/maven/model/Reporting;Lorg/apache/maven/model/Reporting;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Reporting;Lorg/apache/maven/model/Reporting;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeReporting_Plugins", "acc": 4, "dsc": "(Lorg/apache/maven/model/Reporting;Lorg/apache/maven/model/Reporting;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Reporting;Lorg/apache/maven/model/Reporting;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeReportPlugin", "acc": 4, "dsc": "(Lorg/apache/maven/model/ReportPlugin;Lorg/apache/maven/model/ReportPlugin;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/ReportPlugin;Lorg/apache/maven/model/ReportPlugin;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeReportPlugin_GroupId", "acc": 4, "dsc": "(Lorg/apache/maven/model/ReportPlugin;Lorg/apache/maven/model/ReportPlugin;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/ReportPlugin;Lorg/apache/maven/model/ReportPlugin;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeReportPlugin_ArtifactId", "acc": 4, "dsc": "(Lorg/apache/maven/model/ReportPlugin;Lorg/apache/maven/model/ReportPlugin;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/ReportPlugin;Lorg/apache/maven/model/ReportPlugin;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeReportPlugin_Version", "acc": 4, "dsc": "(Lorg/apache/maven/model/ReportPlugin;Lorg/apache/maven/model/ReportPlugin;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/ReportPlugin;Lorg/apache/maven/model/ReportPlugin;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeReportPlugin_ReportSets", "acc": 4, "dsc": "(Lorg/apache/maven/model/ReportPlugin;Lorg/apache/maven/model/ReportPlugin;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/ReportPlugin;Lorg/apache/maven/model/ReportPlugin;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeReportSet", "acc": 4, "dsc": "(Lorg/apache/maven/model/ReportSet;Lorg/apache/maven/model/ReportSet;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/ReportSet;Lorg/apache/maven/model/ReportSet;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeReportSet_Id", "acc": 4, "dsc": "(Lorg/apache/maven/model/ReportSet;Lorg/apache/maven/model/ReportSet;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/ReportSet;Lorg/apache/maven/model/ReportSet;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeReportSet_Reports", "acc": 4, "dsc": "(Lorg/apache/maven/model/ReportSet;Lorg/apache/maven/model/ReportSet;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/ReportSet;Lorg/apache/maven/model/ReportSet;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeDependencyManagement", "acc": 4, "dsc": "(Lorg/apache/maven/model/DependencyManagement;Lorg/apache/maven/model/DependencyManagement;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/DependencyManagement;Lorg/apache/maven/model/DependencyManagement;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeDependencyManagement_Dependencies", "acc": 4, "dsc": "(Lorg/apache/maven/model/DependencyManagement;Lorg/apache/maven/model/DependencyManagement;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/DependencyManagement;Lorg/apache/maven/model/DependencyManagement;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeParent", "acc": 4, "dsc": "(Lorg/apache/maven/model/Parent;Lorg/apache/maven/model/Parent;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Parent;Lorg/apache/maven/model/Parent;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeParent_GroupId", "acc": 4, "dsc": "(Lorg/apache/maven/model/Parent;Lorg/apache/maven/model/Parent;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Parent;Lorg/apache/maven/model/Parent;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeParent_ArtifactId", "acc": 4, "dsc": "(Lorg/apache/maven/model/Parent;Lorg/apache/maven/model/Parent;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Parent;Lorg/apache/maven/model/Parent;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeParent_Version", "acc": 4, "dsc": "(Lorg/apache/maven/model/Parent;Lorg/apache/maven/model/Parent;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Parent;Lorg/apache/maven/model/Parent;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeParent_RelativePath", "acc": 4, "dsc": "(Lorg/apache/maven/model/Parent;Lorg/apache/maven/model/Parent;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Parent;Lorg/apache/maven/model/Parent;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeOrganization", "acc": 4, "dsc": "(Lorg/apache/maven/model/Organization;Lorg/apache/maven/model/Organization;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Organization;Lorg/apache/maven/model/Organization;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeOrganization_Name", "acc": 4, "dsc": "(Lorg/apache/maven/model/Organization;Lorg/apache/maven/model/Organization;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Organization;Lorg/apache/maven/model/Organization;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeOrganization_Url", "acc": 4, "dsc": "(Lorg/apache/maven/model/Organization;Lorg/apache/maven/model/Organization;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Organization;Lorg/apache/maven/model/Organization;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeLicense", "acc": 4, "dsc": "(Lorg/apache/maven/model/License;Lorg/apache/maven/model/License;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/License;Lorg/apache/maven/model/License;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeLicense_Name", "acc": 4, "dsc": "(Lorg/apache/maven/model/License;Lorg/apache/maven/model/License;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/License;Lorg/apache/maven/model/License;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeLicense_Url", "acc": 4, "dsc": "(Lorg/apache/maven/model/License;Lorg/apache/maven/model/License;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/License;Lorg/apache/maven/model/License;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeLicense_Distribution", "acc": 4, "dsc": "(Lorg/apache/maven/model/License;Lorg/apache/maven/model/License;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/License;Lorg/apache/maven/model/License;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeLicense_Comments", "acc": 4, "dsc": "(Lorg/apache/maven/model/License;Lorg/apache/maven/model/License;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/License;Lorg/apache/maven/model/License;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeMailingList", "acc": 4, "dsc": "(Lorg/apache/maven/model/MailingList;Lorg/apache/maven/model/MailingList;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/MailingList;Lorg/apache/maven/model/MailingList;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeMailingList_Name", "acc": 4, "dsc": "(Lorg/apache/maven/model/MailingList;Lorg/apache/maven/model/MailingList;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/MailingList;Lorg/apache/maven/model/MailingList;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeMailingList_Subscribe", "acc": 4, "dsc": "(Lorg/apache/maven/model/MailingList;Lorg/apache/maven/model/MailingList;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/MailingList;Lorg/apache/maven/model/MailingList;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeMailingList_Unsubscribe", "acc": 4, "dsc": "(Lorg/apache/maven/model/MailingList;Lorg/apache/maven/model/MailingList;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/MailingList;Lorg/apache/maven/model/MailingList;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeMailingList_Post", "acc": 4, "dsc": "(Lorg/apache/maven/model/MailingList;Lorg/apache/maven/model/MailingList;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/MailingList;Lorg/apache/maven/model/MailingList;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeMailingList_Archive", "acc": 4, "dsc": "(Lorg/apache/maven/model/MailingList;Lorg/apache/maven/model/MailingList;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/MailingList;Lorg/apache/maven/model/MailingList;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeMailingList_OtherArchives", "acc": 4, "dsc": "(Lorg/apache/maven/model/MailingList;Lorg/apache/maven/model/MailingList;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/MailingList;Lorg/apache/maven/model/MailingList;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeDeveloper", "acc": 4, "dsc": "(Lorg/apache/maven/model/Developer;Lorg/apache/maven/model/Developer;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Developer;Lorg/apache/maven/model/Developer;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeDeveloper_Id", "acc": 4, "dsc": "(Lorg/apache/maven/model/Developer;Lorg/apache/maven/model/Developer;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Developer;Lorg/apache/maven/model/Developer;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeContributor", "acc": 4, "dsc": "(Lorg/apache/maven/model/Contributor;Lorg/apache/maven/model/Contributor;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Contributor;Lorg/apache/maven/model/Contributor;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeContributor_Name", "acc": 4, "dsc": "(Lorg/apache/maven/model/Contributor;Lorg/apache/maven/model/Contributor;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Contributor;Lorg/apache/maven/model/Contributor;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeContributor_Email", "acc": 4, "dsc": "(Lorg/apache/maven/model/Contributor;Lorg/apache/maven/model/Contributor;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Contributor;Lorg/apache/maven/model/Contributor;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeContributor_Url", "acc": 4, "dsc": "(Lorg/apache/maven/model/Contributor;Lorg/apache/maven/model/Contributor;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Contributor;Lorg/apache/maven/model/Contributor;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeContributor_Organization", "acc": 4, "dsc": "(Lorg/apache/maven/model/Contributor;Lorg/apache/maven/model/Contributor;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Contributor;Lorg/apache/maven/model/Contributor;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeContributor_OrganizationUrl", "acc": 4, "dsc": "(Lorg/apache/maven/model/Contributor;Lorg/apache/maven/model/Contributor;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Contributor;Lorg/apache/maven/model/Contributor;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeContributor_Timezone", "acc": 4, "dsc": "(Lorg/apache/maven/model/Contributor;Lorg/apache/maven/model/Contributor;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Contributor;Lorg/apache/maven/model/Contributor;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeContributor_Roles", "acc": 4, "dsc": "(Lorg/apache/maven/model/Contributor;Lorg/apache/maven/model/Contributor;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Contributor;Lorg/apache/maven/model/Contributor;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeContributor_Properties", "acc": 4, "dsc": "(Lorg/apache/maven/model/Contributor;Lorg/apache/maven/model/Contributor;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Contributor;Lorg/apache/maven/model/Contributor;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeIssueManagement", "acc": 4, "dsc": "(Lorg/apache/maven/model/IssueManagement;Lorg/apache/maven/model/IssueManagement;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/IssueManagement;Lorg/apache/maven/model/IssueManagement;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeIssueManagement_System", "acc": 4, "dsc": "(Lorg/apache/maven/model/IssueManagement;Lorg/apache/maven/model/IssueManagement;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/IssueManagement;Lorg/apache/maven/model/IssueManagement;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeIssueManagement_Url", "acc": 4, "dsc": "(Lorg/apache/maven/model/IssueManagement;Lorg/apache/maven/model/IssueManagement;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/IssueManagement;Lorg/apache/maven/model/IssueManagement;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeScm", "acc": 4, "dsc": "(Lorg/apache/maven/model/Scm;Lorg/apache/maven/model/Scm;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Scm;Lorg/apache/maven/model/Scm;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeScm_ChildScmConnectionInheritAppendPath", "acc": 4, "dsc": "(Lorg/apache/maven/model/Scm;Lorg/apache/maven/model/Scm;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Scm;Lorg/apache/maven/model/Scm;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeScm_ChildScmDeveloperConnectionInheritAppendPath", "acc": 4, "dsc": "(Lorg/apache/maven/model/Scm;Lorg/apache/maven/model/Scm;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Scm;Lorg/apache/maven/model/Scm;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeScm_ChildScmUrlInheritAppendPath", "acc": 4, "dsc": "(Lorg/apache/maven/model/Scm;Lorg/apache/maven/model/Scm;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Scm;Lorg/apache/maven/model/Scm;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeScm_Url", "acc": 4, "dsc": "(Lorg/apache/maven/model/Scm;Lorg/apache/maven/model/Scm;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Scm;Lorg/apache/maven/model/Scm;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeScm_Connection", "acc": 4, "dsc": "(Lorg/apache/maven/model/Scm;Lorg/apache/maven/model/Scm;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Scm;Lorg/apache/maven/model/Scm;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeScm_DeveloperConnection", "acc": 4, "dsc": "(Lorg/apache/maven/model/Scm;Lorg/apache/maven/model/Scm;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Scm;Lorg/apache/maven/model/Scm;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeScm_Tag", "acc": 4, "dsc": "(Lorg/apache/maven/model/Scm;Lorg/apache/maven/model/Scm;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Scm;Lorg/apache/maven/model/Scm;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeCiManagement", "acc": 4, "dsc": "(Lorg/apache/maven/model/CiManagement;Lorg/apache/maven/model/CiManagement;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/CiManagement;Lorg/apache/maven/model/CiManagement;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeCiManagement_System", "acc": 4, "dsc": "(Lorg/apache/maven/model/CiManagement;Lorg/apache/maven/model/CiManagement;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/CiManagement;Lorg/apache/maven/model/CiManagement;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeCiManagement_Url", "acc": 4, "dsc": "(Lorg/apache/maven/model/CiManagement;Lorg/apache/maven/model/CiManagement;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/CiManagement;Lorg/apache/maven/model/CiManagement;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeCiManagement_Notifiers", "acc": 4, "dsc": "(Lorg/apache/maven/model/CiManagement;Lorg/apache/maven/model/CiManagement;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/CiManagement;Lorg/apache/maven/model/CiManagement;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeNotifier", "acc": 4, "dsc": "(Lorg/apache/maven/model/Notifier;Lorg/apache/maven/model/Notifier;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Notifier;Lorg/apache/maven/model/Notifier;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeNotifier_Type", "acc": 4, "dsc": "(Lorg/apache/maven/model/Notifier;Lorg/apache/maven/model/Notifier;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Notifier;Lorg/apache/maven/model/Notifier;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeNotifier_Address", "acc": 4, "dsc": "(Lorg/apache/maven/model/Notifier;Lorg/apache/maven/model/Notifier;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Notifier;Lorg/apache/maven/model/Notifier;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeNotifier_Configuration", "acc": 4, "dsc": "(Lorg/apache/maven/model/Notifier;Lorg/apache/maven/model/Notifier;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Notifier;Lorg/apache/maven/model/Notifier;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeNotifier_SendOnError", "acc": 4, "dsc": "(Lorg/apache/maven/model/Notifier;Lorg/apache/maven/model/Notifier;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Notifier;Lorg/apache/maven/model/Notifier;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeNotifier_SendOnFailure", "acc": 4, "dsc": "(Lorg/apache/maven/model/Notifier;Lorg/apache/maven/model/Notifier;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Notifier;Lorg/apache/maven/model/Notifier;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeNotifier_SendOnSuccess", "acc": 4, "dsc": "(Lorg/apache/maven/model/Notifier;Lorg/apache/maven/model/Notifier;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Notifier;Lorg/apache/maven/model/Notifier;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeNotifier_SendOnWarning", "acc": 4, "dsc": "(Lorg/apache/maven/model/Notifier;Lorg/apache/maven/model/Notifier;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Notifier;Lorg/apache/maven/model/Notifier;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergePrerequisites", "acc": 4, "dsc": "(Lorg/apache/maven/model/Prerequisites;Lorg/apache/maven/model/Prerequisites;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Prerequisites;Lorg/apache/maven/model/Prerequisites;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergePrerequisites_Maven", "acc": 4, "dsc": "(Lorg/apache/maven/model/Prerequisites;Lorg/apache/maven/model/Prerequisites;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Prerequisites;Lorg/apache/maven/model/Prerequisites;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeBuild", "acc": 4, "dsc": "(Lorg/apache/maven/model/Build;Lorg/apache/maven/model/Build;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Build;Lorg/apache/maven/model/Build;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeBuild_SourceDirectory", "acc": 4, "dsc": "(Lorg/apache/maven/model/Build;Lorg/apache/maven/model/Build;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Build;Lorg/apache/maven/model/Build;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeBuild_ScriptSourceDirectory", "acc": 4, "dsc": "(Lorg/apache/maven/model/Build;Lorg/apache/maven/model/Build;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Build;Lorg/apache/maven/model/Build;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeBuild_TestSourceDirectory", "acc": 4, "dsc": "(Lorg/apache/maven/model/Build;Lorg/apache/maven/model/Build;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Build;Lorg/apache/maven/model/Build;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeBuild_OutputDirectory", "acc": 4, "dsc": "(Lorg/apache/maven/model/Build;Lorg/apache/maven/model/Build;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Build;Lorg/apache/maven/model/Build;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeBuild_TestOutputDirectory", "acc": 4, "dsc": "(Lorg/apache/maven/model/Build;Lorg/apache/maven/model/Build;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Build;Lorg/apache/maven/model/Build;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeBuild_Extensions", "acc": 4, "dsc": "(Lorg/apache/maven/model/Build;Lorg/apache/maven/model/Build;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Build;Lorg/apache/maven/model/Build;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeExtension", "acc": 4, "dsc": "(Lorg/apache/maven/model/Extension;Lorg/apache/maven/model/Extension;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Extension;Lorg/apache/maven/model/Extension;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeExtension_GroupId", "acc": 4, "dsc": "(Lorg/apache/maven/model/Extension;Lorg/apache/maven/model/Extension;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Extension;Lorg/apache/maven/model/Extension;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeExtension_ArtifactId", "acc": 4, "dsc": "(Lorg/apache/maven/model/Extension;Lorg/apache/maven/model/Extension;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Extension;Lorg/apache/maven/model/Extension;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeExtension_Version", "acc": 4, "dsc": "(Lorg/apache/maven/model/Extension;Lorg/apache/maven/model/Extension;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Extension;Lorg/apache/maven/model/Extension;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeBuildBase", "acc": 4, "dsc": "(Lorg/apache/maven/model/BuildBase;Lorg/apache/maven/model/BuildBase;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/BuildBase;Lorg/apache/maven/model/BuildBase;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeBuildBase_DefaultGoal", "acc": 4, "dsc": "(Lorg/apache/maven/model/BuildBase;Lorg/apache/maven/model/BuildBase;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/BuildBase;Lorg/apache/maven/model/BuildBase;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeBuildBase_Directory", "acc": 4, "dsc": "(Lorg/apache/maven/model/BuildBase;Lorg/apache/maven/model/BuildBase;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/BuildBase;Lorg/apache/maven/model/BuildBase;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeBuildBase_FinalName", "acc": 4, "dsc": "(Lorg/apache/maven/model/BuildBase;Lorg/apache/maven/model/BuildBase;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/BuildBase;Lorg/apache/maven/model/BuildBase;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeBuildBase_Filters", "acc": 4, "dsc": "(Lorg/apache/maven/model/BuildBase;Lorg/apache/maven/model/BuildBase;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/BuildBase;Lorg/apache/maven/model/BuildBase;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeBuildBase_Resources", "acc": 4, "dsc": "(Lorg/apache/maven/model/BuildBase;Lorg/apache/maven/model/BuildBase;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/BuildBase;Lorg/apache/maven/model/BuildBase;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeBuildBase_TestResources", "acc": 4, "dsc": "(Lorg/apache/maven/model/BuildBase;Lorg/apache/maven/model/BuildBase;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/BuildBase;Lorg/apache/maven/model/BuildBase;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergePluginConfiguration", "acc": 4, "dsc": "(Lorg/apache/maven/model/PluginConfiguration;Lorg/apache/maven/model/PluginConfiguration;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/PluginConfiguration;Lorg/apache/maven/model/PluginConfiguration;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergePluginConfiguration_PluginManagement", "acc": 4, "dsc": "(Lorg/apache/maven/model/PluginConfiguration;Lorg/apache/maven/model/PluginConfiguration;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/PluginConfiguration;Lorg/apache/maven/model/PluginConfiguration;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergePluginContainer", "acc": 4, "dsc": "(Lorg/apache/maven/model/PluginContainer;Lorg/apache/maven/model/PluginContainer;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/PluginContainer;Lorg/apache/maven/model/PluginContainer;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergePluginContainer_Plugins", "acc": 4, "dsc": "(Lorg/apache/maven/model/PluginContainer;Lorg/apache/maven/model/PluginContainer;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/PluginContainer;Lorg/apache/maven/model/PluginContainer;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergePluginManagement", "acc": 4, "dsc": "(Lorg/apache/maven/model/PluginManagement;Lorg/apache/maven/model/PluginManagement;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/PluginManagement;Lorg/apache/maven/model/PluginManagement;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergePlugin", "acc": 4, "dsc": "(Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergePlugin_GroupId", "acc": 4, "dsc": "(Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergePlugin_ArtifactId", "acc": 4, "dsc": "(Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergePlugin_Version", "acc": 4, "dsc": "(Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergePlugin_Extensions", "acc": 4, "dsc": "(Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergePlugin_Dependencies", "acc": 4, "dsc": "(Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergePlugin_Executions", "acc": 4, "dsc": "(Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeConfigurationContainer", "acc": 4, "dsc": "(Lorg/apache/maven/model/ConfigurationContainer;Lorg/apache/maven/model/ConfigurationContainer;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/ConfigurationContainer;Lorg/apache/maven/model/ConfigurationContainer;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeConfigurationContainer_Inherited", "acc": 4, "dsc": "(Lorg/apache/maven/model/ConfigurationContainer;Lorg/apache/maven/model/ConfigurationContainer;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/ConfigurationContainer;Lorg/apache/maven/model/ConfigurationContainer;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeConfigurationContainer_Configuration", "acc": 4, "dsc": "(Lorg/apache/maven/model/ConfigurationContainer;Lorg/apache/maven/model/ConfigurationContainer;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/ConfigurationContainer;Lorg/apache/maven/model/ConfigurationContainer;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergePluginExecution", "acc": 4, "dsc": "(Lorg/apache/maven/model/PluginExecution;Lorg/apache/maven/model/PluginExecution;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/PluginExecution;Lorg/apache/maven/model/PluginExecution;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergePluginExecution_Id", "acc": 4, "dsc": "(Lorg/apache/maven/model/PluginExecution;Lorg/apache/maven/model/PluginExecution;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/PluginExecution;Lorg/apache/maven/model/PluginExecution;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergePluginExecution_Phase", "acc": 4, "dsc": "(Lorg/apache/maven/model/PluginExecution;Lorg/apache/maven/model/PluginExecution;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/PluginExecution;Lorg/apache/maven/model/PluginExecution;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergePluginExecution_Goals", "acc": 4, "dsc": "(Lorg/apache/maven/model/PluginExecution;Lorg/apache/maven/model/PluginExecution;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/PluginExecution;Lorg/apache/maven/model/PluginExecution;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeResource", "acc": 4, "dsc": "(Lorg/apache/maven/model/Resource;Lorg/apache/maven/model/Resource;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Resource;Lorg/apache/maven/model/Resource;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeResource_TargetPath", "acc": 4, "dsc": "(Lorg/apache/maven/model/Resource;Lorg/apache/maven/model/Resource;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Resource;Lorg/apache/maven/model/Resource;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeResource_Filtering", "acc": 4, "dsc": "(Lorg/apache/maven/model/Resource;Lorg/apache/maven/model/Resource;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Resource;Lorg/apache/maven/model/Resource;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeResource_MergeId", "acc": 4, "dsc": "(Lorg/apache/maven/model/Resource;Lorg/apache/maven/model/Resource;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Resource;Lorg/apache/maven/model/Resource;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeFileSet", "acc": 4, "dsc": "(Lorg/apache/maven/model/FileSet;Lorg/apache/maven/model/FileSet;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/FileSet;Lorg/apache/maven/model/FileSet;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeFileSet_Directory", "acc": 4, "dsc": "(Lorg/apache/maven/model/FileSet;Lorg/apache/maven/model/FileSet;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/FileSet;Lorg/apache/maven/model/FileSet;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergePatternSet", "acc": 4, "dsc": "(Lorg/apache/maven/model/PatternSet;Lorg/apache/maven/model/PatternSet;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/PatternSet;Lorg/apache/maven/model/PatternSet;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergePatternSet_Includes", "acc": 4, "dsc": "(Lorg/apache/maven/model/PatternSet;Lorg/apache/maven/model/PatternSet;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/PatternSet;Lorg/apache/maven/model/PatternSet;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergePatternSet_Excludes", "acc": 4, "dsc": "(Lorg/apache/maven/model/PatternSet;Lorg/apache/maven/model/PatternSet;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/PatternSet;Lorg/apache/maven/model/PatternSet;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeProfile", "acc": 4, "dsc": "(Lorg/apache/maven/model/Profile;Lorg/apache/maven/model/Profile;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Profile;Lorg/apache/maven/model/Profile;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "mergeActivation", "acc": 4, "dsc": "(Lorg/apache/maven/model/Activation;Lorg/apache/maven/model/Activation;ZLjava/util/Map;)V", "sig": "(Lorg/apache/maven/model/Activation;Lorg/apache/maven/model/Activation;ZLjava/util/Map<Ljava/lang/Object;Ljava/lang/Object;>;)V"}, {"nme": "getDependency<PERSON>ey", "acc": 4, "dsc": "(Lorg/apache/maven/model/Dependency;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getPlugin<PERSON>ey", "acc": 4, "dsc": "(Lorg/apache/maven/model/Plugin;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getPluginExecutionKey", "acc": 4, "dsc": "(Lorg/apache/maven/model/PluginExecution;)Ljava/lang/Object;"}, {"nme": "getReportPluginKey", "acc": 4, "dsc": "(Lorg/apache/maven/model/ReportPlugin;)Ljava/lang/Object;"}, {"nme": "getReportSetKey", "acc": 4, "dsc": "(Lorg/apache/maven/model/ReportSet;)Ljava/lang/Object;"}, {"nme": "getLicenseKey", "acc": 4, "dsc": "(Lorg/apache/maven/model/License;)Ljava/lang/Object;"}, {"nme": "getMailingListKey", "acc": 4, "dsc": "(Lorg/apache/maven/model/MailingList;)L<PERSON><PERSON>/lang/Object;"}, {"nme": "getDeveloperKey", "acc": 4, "dsc": "(Lorg/apache/maven/model/Developer;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getContributorKey", "acc": 4, "dsc": "(Lorg/apache/maven/model/Contributor;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getProfileKey", "acc": 4, "dsc": "(Lorg/apache/maven/model/Profile;)Ljava/lang/Object;"}, {"nme": "getRepositoryKey", "acc": 4, "dsc": "(Lorg/apache/maven/model/Repository;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getRepositoryBaseKey", "acc": 4, "dsc": "(Lorg/apache/maven/model/RepositoryBase;)L<PERSON><PERSON>/lang/Object;"}, {"nme": "getNotifierKey", "acc": 4, "dsc": "(Lorg/apache/maven/model/Notifier;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getResourceKey", "acc": 4, "dsc": "(Lorg/apache/maven/model/Resource;)Ljava/lang/Object;"}, {"nme": "getExtensionKey", "acc": 4, "dsc": "(Lorg/apache/maven/model/Extension;)Ljava/lang/Object;"}, {"nme": "getExclusionKey", "acc": 4, "dsc": "(Lorg/apache/maven/model/Exclusion;)L<PERSON><PERSON>/lang/Object;"}, {"nme": "merge", "acc": 10, "dsc": "(Ljava/util/List;Ljava/util/List;ZLorg/apache/maven/model/merge/ModelMerger$KeyComputer;)Ljava/util/List;", "sig": "<T:Ljava/lang/Object;>(Ljava/util/List<TT;>;Ljava/util/List<TT;>;ZLorg/apache/maven/model/merge/ModelMerger$KeyComputer<TT;>;)Ljava/util/List<TT;>;"}, {"nme": "merge", "acc": 10, "dsc": "(Ljava/util/List;Ljava/util/List;Lorg/apache/maven/model/merge/ModelMerger$KeyComputer;Lorg/apache/maven/model/merge/ModelMerger$Remapping;)Ljava/util/List;", "sig": "<T:Ljava/lang/Object;>(Ljava/util/List<TT;>;Ljava/util/List<TT;>;Lorg/apache/maven/model/merge/ModelMerger$KeyComputer<TT;>;Lorg/apache/maven/model/merge/ModelMerger$Remapping<TT;>;)Ljava/util/List<TT;>;"}], "flds": []}, "org/apache/maven/model/Activation.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/Activation", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/Activation;"}, {"nme": "getFile", "acc": 1, "dsc": "()Lorg/apache/maven/model/ActivationFile;"}, {"nme": "getJdk", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getOs", "acc": 1, "dsc": "()Lorg/apache/maven/model/ActivationOS;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "setOtherLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "getOtherLocation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getProperty", "acc": 1, "dsc": "()Lorg/apache/maven/model/ActivationProperty;"}, {"nme": "isActiveByDefault", "acc": 1, "dsc": "()Z"}, {"nme": "setActiveByDefault", "acc": 1, "dsc": "(Z)V"}, {"nme": "setFile", "acc": 1, "dsc": "(Lorg/apache/maven/model/ActivationFile;)V"}, {"nme": "setJdk", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setOs", "acc": 1, "dsc": "(Lorg/apache/maven/model/ActivationOS;)V"}, {"nme": "setProperty", "acc": 1, "dsc": "(Lorg/apache/maven/model/ActivationProperty;)V"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "activeByDefault", "dsc": "Z"}, {"acc": 2, "nme": "jdk", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "os", "dsc": "Lorg/apache/maven/model/ActivationOS;"}, {"acc": 2, "nme": "property", "dsc": "Lorg/apache/maven/model/ActivationProperty;"}, {"acc": 2, "nme": "file", "dsc": "Lorg/apache/maven/model/ActivationFile;"}, {"acc": 2, "nme": "locations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "activeByDefaultLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "jdkLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "osLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "propertyLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "fileLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}]}, "org/apache/maven/model/Developer.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/Developer", "super": "org/apache/maven/model/Contributor", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/Developer;"}, {"nme": "getId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "clone", "acc": 4161, "dsc": "()Lorg/apache/maven/model/Contributor;"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "id", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/apache/maven/model/RepositoryBase.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/RepositoryBase", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/RepositoryBase;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "getId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLayout", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "setOtherLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "getOtherLocation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getUrl", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "setId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setLayout", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setName", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setUrl", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "id", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "url", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "layout", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "locations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "idLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "nameLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "urlLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "layoutLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}]}, "org/apache/maven/model/PatternSet.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/PatternSet", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "addExclude", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "addInclude", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/PatternSet;"}, {"nme": "getExcludes", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "getIncludes", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "getLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "setOtherLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "getOtherLocation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "removeExclude", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "removeInclude", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setExcludes", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/String;>;)V"}, {"nme": "setIncludes", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/String;>;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "includes", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 2, "nme": "excludes", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 2, "nme": "locations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "includesLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "excludesLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}]}, "org/apache/maven/model/Reporting.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/Reporting", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "addPlugin", "acc": 1, "dsc": "(Lorg/apache/maven/model/ReportPlugin;)V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/Reporting;"}, {"nme": "getExcludeDefaults", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "setOtherLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "getOtherLocation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getOutputDirectory", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getPlugins", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/ReportPlugin;>;"}, {"nme": "removePlugin", "acc": 1, "dsc": "(Lorg/apache/maven/model/ReportPlugin;)V"}, {"nme": "setExclude<PERSON>efaults", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setOutputDirectory", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setPlugins", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Lorg/apache/maven/model/ReportPlugin;>;)V"}, {"nme": "isExcludeDefaults", "acc": 1, "dsc": "()Z"}, {"nme": "setExclude<PERSON>efaults", "acc": 1, "dsc": "(Z)V"}, {"nme": "flushReportPluginMap", "acc": 33, "dsc": "()V"}, {"nme": "getReportPluginsAsMap", "acc": 33, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Lorg/apache/maven/model/ReportPlugin;>;"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "excludeDefaults", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "outputDirectory", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "plugins", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/model/ReportPlugin;>;"}, {"acc": 2, "nme": "locations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "excludeDefaultsLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "outputDirectoryLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "pluginsLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 0, "nme": "reportPluginMap", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lorg/apache/maven/model/ReportPlugin;>;"}]}, "org/apache/maven/model/merge/ModelMerger$Remapping.class": {"ver": 52, "acc": 1536, "nme": "org/apache/maven/model/merge/ModelMerger$Remapping", "super": "java/lang/Object", "mthds": [{"nme": "merge", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(TT;TT;)TT;"}], "flds": []}, "org/apache/maven/model/Repository.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/Repository", "super": "org/apache/maven/model/RepositoryBase", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/Repository;"}, {"nme": "getReleases", "acc": 1, "dsc": "()Lorg/apache/maven/model/RepositoryPolicy;"}, {"nme": "getSnapshots", "acc": 1, "dsc": "()Lorg/apache/maven/model/RepositoryPolicy;"}, {"nme": "setReleases", "acc": 1, "dsc": "(Lorg/apache/maven/model/RepositoryPolicy;)V"}, {"nme": "setSnapshots", "acc": 1, "dsc": "(Lorg/apache/maven/model/RepositoryPolicy;)V"}, {"nme": "clone", "acc": 4161, "dsc": "()Lorg/apache/maven/model/RepositoryBase;"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "releases", "dsc": "Lorg/apache/maven/model/RepositoryPolicy;"}, {"acc": 2, "nme": "snapshots", "dsc": "Lorg/apache/maven/model/RepositoryPolicy;"}]}, "org/apache/maven/model/io/xpp3/MavenXpp3ReaderEx$ContentTransformer.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/io/xpp3/MavenXpp3ReaderEx$ContentTransformer", "super": "java/lang/Object", "mthds": [{"nme": "transform", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": []}, "org/apache/maven/model/io/xpp3/MavenXpp3ReaderEx.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/io/xpp3/MavenXpp3ReaderEx", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/apache/maven/model/io/xpp3/MavenXpp3ReaderEx$ContentTransformer;)V"}, {"nme": "checkFieldWithDuplicate", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Ljava/lang/String;Ljava/lang/String;Ljava/util/Set;)Z", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "checkUnknownAttribute", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Ljava/lang/String;Ljava/lang/String;Z)V", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "checkUnknownElement", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)V", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "getAddDefaultEntities", "acc": 1, "dsc": "()Z"}, {"nme": "getBooleanValue", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;)Z", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getBooleanValue", "acc": 2, "dsc": "(<PERSON>ja<PERSON>/lang/String;<PERSON>java/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Ljava/lang/String;)Z", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getByteValue", "acc": 2, "dsc": "(<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)B", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getCharacterValue", "acc": 2, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;)C", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getDateValue", "acc": 2, "dsc": "(Lja<PERSON>/lang/String;<PERSON>java/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;)Ljava/util/Date;", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getDateValue", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;)Ljava/util/Date;", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getDoubleValue", "acc": 2, "dsc": "(<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)D", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getFloatValue", "acc": 2, "dsc": "(<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)F", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getIntegerValue", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)I", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getLongValue", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)J", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getRequiredAttributeValue", "acc": 2, "dsc": "(Lja<PERSON>/lang/String;<PERSON>java/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Ljava/lang/String;", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getShortValue", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)S", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getTrimmedValue", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "interpolatedTrimmed", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "nextTag", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;)I", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "read", "acc": 1, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/Model;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "read", "acc": 1, "dsc": "(Lja<PERSON>/io/Reader;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/Model;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "read", "acc": 1, "dsc": "(Ljava/io/InputStream;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/Model;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseActivation", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/Activation;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseActivationFile", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/ActivationFile;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseActivationOS", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/ActivationOS;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseActivationProperty", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/ActivationProperty;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseBuild", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/Build;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseBuildBase", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/BuildBase;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseCiManagement", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/CiManagement;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseConfigurationContainer", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/ConfigurationContainer;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseContributor", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/Contributor;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseDependency", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/Dependency;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseDependencyManagement", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/DependencyManagement;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseDeploymentRepository", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/DeploymentRepository;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseDeveloper", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/Developer;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseDistributionManagement", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/DistributionManagement;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseExclusion", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/Exclusion;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseExtension", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/Extension;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseFileSet", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/FileSet;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseIssueManagement", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/IssueManagement;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseLicense", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/License;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseMailingList", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/MailingList;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseModel", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/Model;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseModelBase", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/ModelBase;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseNotifier", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/Notifier;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseOrganization", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/Organization;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseParent", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/Parent;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parsePatternSet", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/PatternSet;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parsePlugin", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/Plugin;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parsePluginConfiguration", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/PluginConfiguration;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parsePluginContainer", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/PluginContainer;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parsePluginExecution", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/PluginExecution;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parsePluginManagement", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/PluginManagement;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parsePrerequisites", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/Prerequisites;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseProfile", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/Profile;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseRelocation", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/Relocation;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseReportPlugin", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/ReportPlugin;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseReportSet", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/ReportSet;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseReporting", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/Reporting;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseRepository", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/Repository;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseRepositoryBase", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/RepositoryBase;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseRepositoryPolicy", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/RepositoryPolicy;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseResource", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/Resource;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseScm", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/Scm;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseSite", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;ZLorg/apache/maven/model/InputSource;)Lorg/apache/maven/model/Site;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "setAddDefaultEntities", "acc": 1, "dsc": "(Z)V"}], "flds": [{"acc": 2, "nme": "addDefaultEntities", "dsc": "Z"}, {"acc": 17, "nme": "contentTransformer", "dsc": "Lorg/apache/maven/model/io/xpp3/MavenXpp3ReaderEx$ContentTransformer;"}]}, "org/apache/maven/model/PluginConfiguration.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/PluginConfiguration", "super": "org/apache/maven/model/PluginContainer", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/PluginConfiguration;"}, {"nme": "getPluginManagement", "acc": 1, "dsc": "()Lorg/apache/maven/model/PluginManagement;"}, {"nme": "setPluginManagement", "acc": 1, "dsc": "(Lorg/apache/maven/model/PluginManagement;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "clone", "acc": 4161, "dsc": "()Lorg/apache/maven/model/PluginContainer;"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "pluginManagement", "dsc": "Lorg/apache/maven/model/PluginManagement;"}]}, "org/apache/maven/model/Prerequisites.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/Prerequisites", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/Prerequisites;"}, {"nme": "getLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getMaven", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "setOtherLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "getOtherLocation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "setMaven", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "maven", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "locations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "mavenLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}]}, "org/apache/maven/model/Build.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/Build", "super": "org/apache/maven/model/BuildBase", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "addExtension", "acc": 1, "dsc": "(Lorg/apache/maven/model/Extension;)V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/Build;"}, {"nme": "getExtensions", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/Extension;>;"}, {"nme": "getOutputDirectory", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getScriptSourceDirectory", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getSourceDirectory", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getTestOutputDirectory", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getTestSourceDirectory", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "removeExtension", "acc": 1, "dsc": "(Lorg/apache/maven/model/Extension;)V"}, {"nme": "setExtensions", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Lorg/apache/maven/model/Extension;>;)V"}, {"nme": "setOutputDirectory", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setScriptSourceDirectory", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setSourceDirectory", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setTestOutputDirectory", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setTestSourceDirectory", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "clone", "acc": 4161, "dsc": "()Lorg/apache/maven/model/BuildBase;"}, {"nme": "clone", "acc": 4161, "dsc": "()Lorg/apache/maven/model/PluginConfiguration;"}, {"nme": "clone", "acc": 4161, "dsc": "()Lorg/apache/maven/model/PluginContainer;"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "sourceDirectory", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "scriptSourceDirectory", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "testSourceDirectory", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "outputDirectory", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "testOutputDirectory", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "extensions", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/model/Extension;>;"}]}, "org/apache/maven/model/License.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/License", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/License;"}, {"nme": "getComments", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDistribution", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "setOtherLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "getOtherLocation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getUrl", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setComments", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setDistribution", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setName", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setUrl", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "url", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "distribution", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "comments", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "locations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "nameLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "urlLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "distributionLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "commentsLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}]}, "org/apache/maven/model/merge/ModelMerger$MailingListKeyComputer.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/merge/ModelMerger$MailingListKeyComputer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;)V"}, {"nme": "key", "acc": 1, "dsc": "(Lorg/apache/maven/model/MailingList;)L<PERSON><PERSON>/lang/Object;"}, {"nme": "key", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;Lorg/apache/maven/model/merge/ModelMerger$1;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lorg/apache/maven/model/merge/ModelMerger;"}]}, "org/apache/maven/model/merge/ModelMerger$1.class": {"ver": 52, "acc": 4128, "nme": "org/apache/maven/model/merge/ModelMerger$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/apache/maven/model/Dependency.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/Dependency", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "addExclusion", "acc": 1, "dsc": "(Lorg/apache/maven/model/Exclusion;)V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/Dependency;"}, {"nme": "getArtifactId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getClassifier", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getExclusions", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/Exclusion;>;"}, {"nme": "getGroupId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getOptional", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "setOtherLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "getOtherLocation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getScope", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getSystemPath", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getType", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "removeExclusion", "acc": 1, "dsc": "(Lorg/apache/maven/model/Exclusion;)V"}, {"nme": "setArtifactId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setClassifier", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setExclusions", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Lorg/apache/maven/model/Exclusion;>;)V"}, {"nme": "setGroupId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setOptional", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setScope", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setSystemPath", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setType", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setVersion", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "isOptional", "acc": 1, "dsc": "()Z"}, {"nme": "setOptional", "acc": 1, "dsc": "(Z)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getManagementKey", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "clearManagementKey", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "groupId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "artifactId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "type", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "classifier", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "scope", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "systemPath", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "exclusions", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/model/Exclusion;>;"}, {"acc": 2, "nme": "optional", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "locations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "groupIdLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "artifactIdLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "versionLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "typeLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "classifierLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "scopeLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "systemPathLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "exclusionsLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "optionalLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "managementKey", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/apache/maven/model/merge/ModelMerger$DeveloperKeyComputer.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/merge/ModelMerger$DeveloperKeyComputer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;)V"}, {"nme": "key", "acc": 1, "dsc": "(Lorg/apache/maven/model/Developer;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "key", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;Lorg/apache/maven/model/merge/ModelMerger$1;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lorg/apache/maven/model/merge/ModelMerger;"}]}, "org/apache/maven/model/IssueManagement.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/IssueManagement", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/IssueManagement;"}, {"nme": "getLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "setOtherLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "getOtherLocation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getSystem", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getUrl", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setSystem", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setUrl", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "system", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "url", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "locations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "systemLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "urlLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}]}, "org/apache/maven/model/io/xpp3/MavenXpp3ReaderEx$Xpp3DomBuilderInputLocationBuilder.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/io/xpp3/MavenXpp3ReaderEx$Xpp3DomBuilderInputLocationBuilder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "toInputLocation", "acc": 1, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;)Ljava/lang/Object;"}], "flds": [{"acc": 18, "nme": "rootLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}]}, "org/apache/maven/model/merge/ModelMerger$KeyComputer.class": {"ver": 52, "acc": 1536, "nme": "org/apache/maven/model/merge/ModelMerger$KeyComputer", "super": "java/lang/Object", "mthds": [{"nme": "key", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(TT;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "org/apache/maven/model/io/xpp3/MavenXpp3Writer.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/io/xpp3/MavenXpp3Writer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "setFileComment", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "write", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Writer;Lorg/apache/maven/model/Model;)V", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Ljava/io/OutputStream;Lorg/apache/maven/model/Model;)V", "exs": ["java/io/IOException"]}, {"nme": "writeActivation", "acc": 2, "dsc": "(Lorg/apache/maven/model/Activation;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeActivationFile", "acc": 2, "dsc": "(Lorg/apache/maven/model/ActivationFile;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeActivationOS", "acc": 2, "dsc": "(Lorg/apache/maven/model/ActivationOS;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeActivationProperty", "acc": 2, "dsc": "(Lorg/apache/maven/model/ActivationProperty;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeBuild", "acc": 2, "dsc": "(Lorg/apache/maven/model/Build;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeBuildBase", "acc": 2, "dsc": "(Lorg/apache/maven/model/BuildBase;<PERSON>ja<PERSON>/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeCiManagement", "acc": 2, "dsc": "(Lorg/apache/maven/model/CiManagement;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeConfigurationContainer", "acc": 2, "dsc": "(Lorg/apache/maven/model/ConfigurationContainer;<PERSON>ja<PERSON>/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeContributor", "acc": 2, "dsc": "(Lorg/apache/maven/model/Contributor;Lja<PERSON>/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeDependency", "acc": 2, "dsc": "(Lorg/apache/maven/model/Dependency;<PERSON><PERSON><PERSON>/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeDependencyManagement", "acc": 2, "dsc": "(Lorg/apache/maven/model/DependencyManagement;Lja<PERSON>/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeDeploymentRepository", "acc": 2, "dsc": "(Lorg/apache/maven/model/DeploymentRepository;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeDeveloper", "acc": 2, "dsc": "(Lorg/apache/maven/model/Developer;<PERSON>ja<PERSON>/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeDistributionManagement", "acc": 2, "dsc": "(Lorg/apache/maven/model/DistributionManagement;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeExclusion", "acc": 2, "dsc": "(Lorg/apache/maven/model/Exclusion;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeExtension", "acc": 2, "dsc": "(Lorg/apache/maven/model/Extension;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeFileSet", "acc": 2, "dsc": "(Lorg/apache/maven/model/FileSet;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeIssueManagement", "acc": 2, "dsc": "(Lorg/apache/maven/model/IssueManagement;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeLicense", "acc": 2, "dsc": "(Lorg/apache/maven/model/License;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeMailingList", "acc": 2, "dsc": "(Lorg/apache/maven/model/MailingList;<PERSON>ja<PERSON>/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeModel", "acc": 2, "dsc": "(Lorg/apache/maven/model/Model;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeModelBase", "acc": 2, "dsc": "(Lorg/apache/maven/model/ModelBase;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeNotifier", "acc": 2, "dsc": "(Lorg/apache/maven/model/Notifier;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeOrganization", "acc": 2, "dsc": "(Lorg/apache/maven/model/Organization;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeParent", "acc": 2, "dsc": "(Lorg/apache/maven/model/Parent;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writePatternSet", "acc": 2, "dsc": "(Lorg/apache/maven/model/PatternSet;<PERSON>ja<PERSON>/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writePlugin", "acc": 2, "dsc": "(Lorg/apache/maven/model/Plugin;<PERSON>java/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writePluginConfiguration", "acc": 2, "dsc": "(Lorg/apache/maven/model/PluginConfiguration;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writePluginContainer", "acc": 2, "dsc": "(Lorg/apache/maven/model/PluginContainer;<PERSON><PERSON><PERSON>/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writePluginExecution", "acc": 2, "dsc": "(Lorg/apache/maven/model/PluginExecution;Lja<PERSON>/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writePluginManagement", "acc": 2, "dsc": "(Lorg/apache/maven/model/PluginManagement;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writePrerequisites", "acc": 2, "dsc": "(Lorg/apache/maven/model/Prerequisites;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeProfile", "acc": 2, "dsc": "(Lorg/apache/maven/model/Profile;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeRelocation", "acc": 2, "dsc": "(Lorg/apache/maven/model/Relocation;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeReportPlugin", "acc": 2, "dsc": "(Lorg/apache/maven/model/ReportPlugin;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeReportSet", "acc": 2, "dsc": "(Lorg/apache/maven/model/ReportSet;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeReporting", "acc": 2, "dsc": "(Lorg/apache/maven/model/Reporting;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeRepository", "acc": 2, "dsc": "(Lorg/apache/maven/model/Repository;<PERSON>ja<PERSON>/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeRepositoryBase", "acc": 2, "dsc": "(Lorg/apache/maven/model/RepositoryBase;<PERSON>java/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeRepositoryPolicy", "acc": 2, "dsc": "(Lorg/apache/maven/model/RepositoryPolicy;<PERSON>ja<PERSON>/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeResource", "acc": 2, "dsc": "(Lorg/apache/maven/model/Resource;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeScm", "acc": 2, "dsc": "(Lorg/apache/maven/model/Scm;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeSite", "acc": 2, "dsc": "(Lorg/apache/maven/model/Site;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "NAMESPACE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "fileComment", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/apache/maven/model/merge/ModelMerger$ExecutionKeyComputer.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/merge/ModelMerger$ExecutionKeyComputer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;)V"}, {"nme": "key", "acc": 1, "dsc": "(Lorg/apache/maven/model/PluginExecution;)Ljava/lang/Object;"}, {"nme": "key", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;Lorg/apache/maven/model/merge/ModelMerger$1;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lorg/apache/maven/model/merge/ModelMerger;"}]}, "org/apache/maven/model/Organization.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/Organization", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/Organization;"}, {"nme": "getLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "setOtherLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "getOtherLocation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getUrl", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setName", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setUrl", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "url", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "locations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "nameLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "urlLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}]}, "org/apache/maven/model/InputSource.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/InputSource", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/InputSource;"}, {"nme": "getLocation", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getModelId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setModelId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "modelId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "location", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/apache/maven/model/merge/ModelMerger$ProfileKeyComputer.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/merge/ModelMerger$ProfileKeyComputer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;)V"}, {"nme": "key", "acc": 1, "dsc": "(Lorg/apache/maven/model/Profile;)Ljava/lang/Object;"}, {"nme": "key", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;Lorg/apache/maven/model/merge/ModelMerger$1;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lorg/apache/maven/model/merge/ModelMerger;"}]}, "org/apache/maven/model/merge/ModelMerger$ExtensionKeyComputer.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/merge/ModelMerger$ExtensionKeyComputer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;)V"}, {"nme": "key", "acc": 1, "dsc": "(Lorg/apache/maven/model/Extension;)Ljava/lang/Object;"}, {"nme": "key", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;Lorg/apache/maven/model/merge/ModelMerger$1;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lorg/apache/maven/model/merge/ModelMerger;"}]}, "org/apache/maven/model/merge/ModelMerger$SourceDominant.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/merge/ModelMerger$SourceDominant", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Z)V"}, {"nme": "merge", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(TT;TT;)TT;"}], "flds": [{"acc": 18, "nme": "sourceDominant", "dsc": "Z"}]}, "org/apache/maven/model/CiManagement.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/CiManagement", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "addNotifier", "acc": 1, "dsc": "(Lorg/apache/maven/model/Notifier;)V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/CiManagement;"}, {"nme": "getLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getNotifiers", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/Notifier;>;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "setOtherLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "getOtherLocation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getSystem", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getUrl", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "removeNotifier", "acc": 1, "dsc": "(Lorg/apache/maven/model/Notifier;)V"}, {"nme": "setNotifiers", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(<PERSON>java/util/List<Lorg/apache/maven/model/Notifier;>;)V"}, {"nme": "setSystem", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setUrl", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "system", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "url", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "notifiers", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/model/Notifier;>;"}, {"acc": 2, "nme": "locations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "systemLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "urlLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "notifiersLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}]}, "org/apache/maven/model/InputLocationTracker.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/InputLocationTracker", "super": "java/lang/Object", "mthds": [{"nme": "getLocation", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "setLocation", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}], "flds": []}, "org/apache/maven/model/io/xpp3/MavenXpp3WriterEx.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/io/xpp3/MavenXpp3WriterEx", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "setFileComment", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Lorg/apache/maven/model/InputLocation$StringFormatter;)V"}, {"nme": "toString", "acc": 4, "dsc": "(Lorg/apache/maven/model/InputLocation;)Ljava/lang/String;"}, {"nme": "write", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Writer;Lorg/apache/maven/model/Model;)V", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(Ljava/io/OutputStream;Lorg/apache/maven/model/Model;)V", "exs": ["java/io/IOException"]}, {"nme": "writeXpp3DomToSerializer", "acc": 4, "dsc": "(Lorg/codehaus/plexus/util/xml/Xpp3Dom;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeActivation", "acc": 2, "dsc": "(Lorg/apache/maven/model/Activation;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeActivationFile", "acc": 2, "dsc": "(Lorg/apache/maven/model/ActivationFile;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeActivationOS", "acc": 2, "dsc": "(Lorg/apache/maven/model/ActivationOS;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeActivationProperty", "acc": 2, "dsc": "(Lorg/apache/maven/model/ActivationProperty;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeBuild", "acc": 2, "dsc": "(Lorg/apache/maven/model/Build;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeBuildBase", "acc": 2, "dsc": "(Lorg/apache/maven/model/BuildBase;<PERSON>ja<PERSON>/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeCiManagement", "acc": 2, "dsc": "(Lorg/apache/maven/model/CiManagement;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeConfigurationContainer", "acc": 2, "dsc": "(Lorg/apache/maven/model/ConfigurationContainer;<PERSON>ja<PERSON>/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeContributor", "acc": 2, "dsc": "(Lorg/apache/maven/model/Contributor;Lja<PERSON>/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeDependency", "acc": 2, "dsc": "(Lorg/apache/maven/model/Dependency;<PERSON><PERSON><PERSON>/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeDependencyManagement", "acc": 2, "dsc": "(Lorg/apache/maven/model/DependencyManagement;Lja<PERSON>/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeDeploymentRepository", "acc": 2, "dsc": "(Lorg/apache/maven/model/DeploymentRepository;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeDeveloper", "acc": 2, "dsc": "(Lorg/apache/maven/model/Developer;<PERSON>ja<PERSON>/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeDistributionManagement", "acc": 2, "dsc": "(Lorg/apache/maven/model/DistributionManagement;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeExclusion", "acc": 2, "dsc": "(Lorg/apache/maven/model/Exclusion;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeExtension", "acc": 2, "dsc": "(Lorg/apache/maven/model/Extension;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeFileSet", "acc": 2, "dsc": "(Lorg/apache/maven/model/FileSet;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeIssueManagement", "acc": 2, "dsc": "(Lorg/apache/maven/model/IssueManagement;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeLicense", "acc": 2, "dsc": "(Lorg/apache/maven/model/License;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeLocationTracking", "acc": 2, "dsc": "(Lorg/apache/maven/model/InputLocationTracker;Ljava/lang/Object;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeMailingList", "acc": 2, "dsc": "(Lorg/apache/maven/model/MailingList;<PERSON>ja<PERSON>/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeModel", "acc": 2, "dsc": "(Lorg/apache/maven/model/Model;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeModelBase", "acc": 2, "dsc": "(Lorg/apache/maven/model/ModelBase;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeNotifier", "acc": 2, "dsc": "(Lorg/apache/maven/model/Notifier;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeOrganization", "acc": 2, "dsc": "(Lorg/apache/maven/model/Organization;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeParent", "acc": 2, "dsc": "(Lorg/apache/maven/model/Parent;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writePatternSet", "acc": 2, "dsc": "(Lorg/apache/maven/model/PatternSet;<PERSON>ja<PERSON>/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writePlugin", "acc": 2, "dsc": "(Lorg/apache/maven/model/Plugin;<PERSON>java/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writePluginConfiguration", "acc": 2, "dsc": "(Lorg/apache/maven/model/PluginConfiguration;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writePluginContainer", "acc": 2, "dsc": "(Lorg/apache/maven/model/PluginContainer;<PERSON><PERSON><PERSON>/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writePluginExecution", "acc": 2, "dsc": "(Lorg/apache/maven/model/PluginExecution;Lja<PERSON>/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writePluginManagement", "acc": 2, "dsc": "(Lorg/apache/maven/model/PluginManagement;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writePrerequisites", "acc": 2, "dsc": "(Lorg/apache/maven/model/Prerequisites;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeProfile", "acc": 2, "dsc": "(Lorg/apache/maven/model/Profile;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeRelocation", "acc": 2, "dsc": "(Lorg/apache/maven/model/Relocation;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeReportPlugin", "acc": 2, "dsc": "(Lorg/apache/maven/model/ReportPlugin;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeReportSet", "acc": 2, "dsc": "(Lorg/apache/maven/model/ReportSet;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeReporting", "acc": 2, "dsc": "(Lorg/apache/maven/model/Reporting;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeRepository", "acc": 2, "dsc": "(Lorg/apache/maven/model/Repository;<PERSON>ja<PERSON>/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeRepositoryBase", "acc": 2, "dsc": "(Lorg/apache/maven/model/RepositoryBase;<PERSON>java/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeRepositoryPolicy", "acc": 2, "dsc": "(Lorg/apache/maven/model/RepositoryPolicy;<PERSON>ja<PERSON>/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeResource", "acc": 2, "dsc": "(Lorg/apache/maven/model/Resource;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeScm", "acc": 2, "dsc": "(Lorg/apache/maven/model/Scm;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "writeSite", "acc": 2, "dsc": "(Lorg/apache/maven/model/Site;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlSerializer;)V", "exs": ["java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "NAMESPACE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "fileComment", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4, "nme": "string<PERSON><PERSON><PERSON>er", "dsc": "Lorg/apache/maven/model/InputLocation$StringFormatter;"}]}, "org/apache/maven/model/ConfigurationContainer.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/ConfigurationContainer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/ConfigurationContainer;"}, {"nme": "getConfiguration", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getInherited", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "setOtherLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "getOtherLocation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "setConfiguration", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "setInherited", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "isInherited", "acc": 1, "dsc": "()Z"}, {"nme": "setInherited", "acc": 1, "dsc": "(Z)V"}, {"nme": "unsetInheritanceApplied", "acc": 1, "dsc": "()V"}, {"nme": "isInheritanceApplied", "acc": 1, "dsc": "()Z"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "inherited", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "configuration", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 2, "nme": "locations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "inheritedLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "configurationLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "inheritanceApplied", "dsc": "Z"}]}, "org/apache/maven/model/merge/ModelMerger$NotifierKeyComputer.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/merge/ModelMerger$NotifierKeyComputer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;)V"}, {"nme": "key", "acc": 1, "dsc": "(Lorg/apache/maven/model/Notifier;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "key", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;Lorg/apache/maven/model/merge/ModelMerger$1;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lorg/apache/maven/model/merge/ModelMerger;"}]}, "org/apache/maven/model/Extension.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/Extension", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/Extension;"}, {"nme": "getArtifactId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getGroupId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "setOtherLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "getOtherLocation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setArtifactId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setGroupId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setVersion", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "equal", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)Z", "sig": "<T:Ljava/lang/Object;>(TT;TT;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "groupId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "artifactId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "locations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "groupIdLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "artifactIdLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "versionLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}]}, "org/apache/maven/model/merge/ModelMerger$ExclusionKeyComputer.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/merge/ModelMerger$ExclusionKeyComputer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;)V"}, {"nme": "key", "acc": 1, "dsc": "(Lorg/apache/maven/model/Exclusion;)L<PERSON><PERSON>/lang/Object;"}, {"nme": "key", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;Lorg/apache/maven/model/merge/ModelMerger$1;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lorg/apache/maven/model/merge/ModelMerger;"}]}, "org/apache/maven/model/io/xpp3/MavenXpp3Reader.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/io/xpp3/MavenXpp3Reader", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lorg/apache/maven/model/io/xpp3/MavenXpp3Reader$ContentTransformer;)V"}, {"nme": "checkFieldWithDuplicate", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Ljava/lang/String;Ljava/lang/String;Ljava/util/Set;)Z", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "checkUnknownAttribute", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Ljava/lang/String;Ljava/lang/String;Z)V", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "checkUnknownElement", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)V", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException", "java/io/IOException"]}, {"nme": "getAddDefaultEntities", "acc": 1, "dsc": "()Z"}, {"nme": "getBooleanValue", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;)Z", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getBooleanValue", "acc": 2, "dsc": "(<PERSON>ja<PERSON>/lang/String;<PERSON>java/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Ljava/lang/String;)Z", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getByteValue", "acc": 2, "dsc": "(<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)B", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getCharacterValue", "acc": 2, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;)C", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getDateValue", "acc": 2, "dsc": "(Lja<PERSON>/lang/String;<PERSON>java/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;)Ljava/util/Date;", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getDateValue", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;)Ljava/util/Date;", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getDoubleValue", "acc": 2, "dsc": "(<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)D", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getFloatValue", "acc": 2, "dsc": "(<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)F", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getIntegerValue", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)I", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getLongValue", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)J", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getRequiredAttributeValue", "acc": 2, "dsc": "(Lja<PERSON>/lang/String;<PERSON>java/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Ljava/lang/String;", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getShortValue", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)S", "exs": ["org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "getTrimmedValue", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "interpolatedTrimmed", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "nextTag", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;)I", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "read", "acc": 1, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/Model;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "read", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;Z)Lorg/apache/maven/model/Model;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "read", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;)Lorg/apache/maven/model/Model;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "read", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;Z)Lorg/apache/maven/model/Model;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "read", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;)Lorg/apache/maven/model/Model;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseActivation", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/Activation;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseActivationFile", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/ActivationFile;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseActivationOS", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/ActivationOS;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseActivationProperty", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/ActivationProperty;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseBuild", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/Build;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseBuildBase", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/BuildBase;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseCiManagement", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/CiManagement;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseConfigurationContainer", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/ConfigurationContainer;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseContributor", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/Contributor;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseDependency", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/Dependency;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseDependencyManagement", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/DependencyManagement;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseDeploymentRepository", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/DeploymentRepository;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseDeveloper", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/Developer;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseDistributionManagement", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/DistributionManagement;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseExclusion", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/Exclusion;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseExtension", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/Extension;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseFileSet", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/FileSet;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseIssueManagement", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/IssueManagement;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseLicense", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/License;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseMailingList", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/MailingList;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseModel", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/Model;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseModelBase", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/ModelBase;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseNotifier", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/Notifier;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseOrganization", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/Organization;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseParent", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/Parent;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parsePatternSet", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/PatternSet;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parsePlugin", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/Plugin;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parsePluginConfiguration", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/PluginConfiguration;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parsePluginContainer", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/PluginContainer;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parsePluginExecution", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/PluginExecution;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parsePluginManagement", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/PluginManagement;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parsePrerequisites", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/Prerequisites;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseProfile", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/Profile;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseRelocation", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/Relocation;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseReportPlugin", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/ReportPlugin;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseReportSet", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/ReportSet;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseReporting", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/Reporting;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseRepository", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/Repository;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseRepositoryBase", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/RepositoryBase;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseRepositoryPolicy", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/RepositoryPolicy;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseResource", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/Resource;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseScm", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/Scm;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "parseSite", "acc": 2, "dsc": "(Lorg/codehaus/plexus/util/xml/pull/XmlPullParser;Z)Lorg/apache/maven/model/Site;", "exs": ["java/io/IOException", "org/codehaus/plexus/util/xml/pull/XmlPullParserException"]}, {"nme": "setAddDefaultEntities", "acc": 1, "dsc": "(Z)V"}], "flds": [{"acc": 2, "nme": "addDefaultEntities", "dsc": "Z"}, {"acc": 17, "nme": "contentTransformer", "dsc": "Lorg/apache/maven/model/io/xpp3/MavenXpp3Reader$ContentTransformer;"}]}, "org/apache/maven/model/Contributor.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/Contributor", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "addProperty", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "addRole", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/Contributor;"}, {"nme": "getEmail", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getOrganization", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getOrganizationUrl", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "setOtherLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "getOtherLocation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getProperties", "acc": 1, "dsc": "()Ljava/util/Properties;"}, {"nme": "getRoles", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "getTimezone", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getUrl", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "removeRole", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setEmail", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setName", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setOrganization", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setOrganizationUrl", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setProperties", "acc": 1, "dsc": "(Ljava/util/Properties;)V"}, {"nme": "setRoles", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/String;>;)V"}, {"nme": "setTimezone", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setUrl", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "email", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "url", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "organization", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "organizationUrl", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "roles", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 2, "nme": "timezone", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "properties", "dsc": "Ljava/util/Properties;"}, {"acc": 2, "nme": "locations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "nameLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "emailLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "urlLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "organizationLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "organizationUrlLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "rolesLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "timezoneLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "propertiesLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}]}, "org/apache/maven/model/merge/ModelMerger$MergingList.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/merge/ModelMerger$MergingList", "super": "java/util/AbstractList", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger$KeyComputer;I)V", "sig": "(Lorg/apache/maven/model/merge/ModelMerger$KeyComputer<TV;>;I)V"}, {"nme": "writeReplace", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/io/ObjectStreamException"]}, {"nme": "iterator", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Iterator;", "sig": "()Ljava/util/Iterator<TV;>;"}, {"nme": "mergeAll", "acc": 0, "dsc": "(Ljava/util/Collection;Lorg/apache/maven/model/merge/ModelMerger$Remapping;)V", "sig": "(Ljava/util/Collection<TV;>;Lorg/apache/maven/model/merge/ModelMerger$Remapping<TV;>;)V"}, {"nme": "contains", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "asList", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<TV;>;"}, {"nme": "add", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(ITV;)V"}, {"nme": "remove", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(I)TV;"}, {"nme": "get", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(I)TV;"}, {"nme": "size", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "keyComputer", "dsc": "Lorg/apache/maven/model/merge/ModelMerger$KeyComputer;", "sig": "Lorg/apache/maven/model/merge/ModelMerger$KeyComputer<TV;>;"}, {"acc": 2, "nme": "map", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;TV;>;"}, {"acc": 2, "nme": "list", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<TV;>;"}]}, "org/apache/maven/model/RepositoryPolicy.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/RepositoryPolicy", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/RepositoryPolicy;"}, {"nme": "getChecksumPolicy", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getEnabled", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "setOtherLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "getOtherLocation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getUpdatePolicy", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setChecksumPolicy", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setEnabled", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setUpdatePolicy", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "isEnabled", "acc": 1, "dsc": "()Z"}, {"nme": "setEnabled", "acc": 1, "dsc": "(Z)V"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "enabled", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "updatePolicy", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "checksumPolicy", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "locations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "enabledLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "updatePolicyLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "checksumPolicyLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}]}, "org/apache/maven/model/ActivationOS.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/ActivationOS", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/ActivationOS;"}, {"nme": "getArch", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getFamily", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "setOtherLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "getOtherLocation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setArch", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setFamily", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setName", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setVersion", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "family", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "arch", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "locations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "nameLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "familyLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "archLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "versionLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}]}, "org/apache/maven/model/Plugin.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/Plugin", "super": "org/apache/maven/model/ConfigurationContainer", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "addDependency", "acc": 1, "dsc": "(Lorg/apache/maven/model/Dependency;)V"}, {"nme": "addExecution", "acc": 1, "dsc": "(Lorg/apache/maven/model/PluginExecution;)V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/Plugin;"}, {"nme": "getArtifactId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDependencies", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/Dependency;>;"}, {"nme": "getExecutions", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/PluginExecution;>;"}, {"nme": "getExtensions", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getGoals", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getGroupId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "removeDependency", "acc": 1, "dsc": "(Lorg/apache/maven/model/Dependency;)V"}, {"nme": "removeExecution", "acc": 1, "dsc": "(Lorg/apache/maven/model/PluginExecution;)V"}, {"nme": "setArtifactId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setDependencies", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(<PERSON><PERSON><PERSON>/util/List<Lorg/apache/maven/model/Dependency;>;)V"}, {"nme": "setExecutions", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Lorg/apache/maven/model/PluginExecution;>;)V"}, {"nme": "setExtensions", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setGoals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "setGroupId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setVersion", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "isExtensions", "acc": 1, "dsc": "()Z"}, {"nme": "setExtensions", "acc": 1, "dsc": "(Z)V"}, {"nme": "flushExecutionMap", "acc": 1, "dsc": "()V"}, {"nme": "getExecutionsAsMap", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Lorg/apache/maven/model/PluginExecution;>;"}, {"nme": "getId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "construct<PERSON>ey", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "clone", "acc": 4161, "dsc": "()Lorg/apache/maven/model/ConfigurationContainer;"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "groupId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "artifactId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "extensions", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "executions", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/model/PluginExecution;>;"}, {"acc": 2, "nme": "dependencies", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/model/Dependency;>;"}, {"acc": 2, "nme": "goals", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 2, "nme": "executionMap", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lorg/apache/maven/model/PluginExecution;>;"}]}, "org/apache/maven/model/merge/ModelMerger$RepositoryKeyComputer.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/merge/ModelMerger$RepositoryKeyComputer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;)V"}, {"nme": "key", "acc": 1, "dsc": "(Lorg/apache/maven/model/Repository;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "key", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;Lorg/apache/maven/model/merge/ModelMerger$1;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lorg/apache/maven/model/merge/ModelMerger;"}]}, "org/apache/maven/model/ActivationFile.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/ActivationFile", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/ActivationFile;"}, {"nme": "getExists", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getMissing", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "setOtherLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "getOtherLocation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "setExists", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setMissing", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "missing", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "exists", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "locations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "missingLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "existsLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}]}, "org/apache/maven/model/Site.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/Site", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/Site;"}, {"nme": "getChildSiteUrlInheritAppendPath", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "setOtherLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "getOtherLocation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getUrl", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setChildSiteUrlInheritAppendPath", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setName", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setUrl", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "isChildSiteUrlInheritAppendPath", "acc": 1, "dsc": "()Z"}, {"nme": "setChildSiteUrlInheritAppendPath", "acc": 1, "dsc": "(Z)V"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "id", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "url", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "childSiteUrlInheritAppendPath", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "locations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "idLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "nameLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "urlLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "childSiteUrlInheritAppendPathLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}]}, "org/apache/maven/model/PluginExecution.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/PluginExecution", "super": "org/apache/maven/model/ConfigurationContainer", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "addGoal", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/PluginExecution;"}, {"nme": "getGoals", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "getId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getPhase", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getPriority", "acc": 1, "dsc": "()I"}, {"nme": "removeGoal", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setGoals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/String;>;)V"}, {"nme": "setId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setPhase", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setPriority", "acc": 1, "dsc": "(I)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "clone", "acc": 4161, "dsc": "()Lorg/apache/maven/model/ConfigurationContainer;"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "id", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "phase", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "priority", "dsc": "I"}, {"acc": 2, "nme": "goals", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 25, "nme": "DEFAULT_EXECUTION_ID", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "default"}]}, "org/apache/maven/model/Model.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/Model", "super": "org/apache/maven/model/ModelBase", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "addContributor", "acc": 1, "dsc": "(Lorg/apache/maven/model/Contributor;)V"}, {"nme": "addDeveloper", "acc": 1, "dsc": "(Lorg/apache/maven/model/Developer;)V"}, {"nme": "addLicense", "acc": 1, "dsc": "(Lorg/apache/maven/model/License;)V"}, {"nme": "addMailingList", "acc": 1, "dsc": "(Lorg/apache/maven/model/MailingList;)V"}, {"nme": "addProfile", "acc": 1, "dsc": "(Lorg/apache/maven/model/Profile;)V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/Model;"}, {"nme": "getArtifactId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getBuild", "acc": 1, "dsc": "()Lorg/apache/maven/model/Build;"}, {"nme": "getChildProjectUrlInheritAppendPath", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getCiManagement", "acc": 1, "dsc": "()Lorg/apache/maven/model/CiManagement;"}, {"nme": "getContributors", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/Contributor;>;"}, {"nme": "getDescription", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDevelopers", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/Developer;>;"}, {"nme": "getGroupId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getInceptionYear", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getIssueManagement", "acc": 1, "dsc": "()Lorg/apache/maven/model/IssueManagement;"}, {"nme": "getLicenses", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/License;>;"}, {"nme": "getMailingLists", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/MailingList;>;"}, {"nme": "getModelEncoding", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getModelVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getOrganization", "acc": 1, "dsc": "()Lorg/apache/maven/model/Organization;"}, {"nme": "getPackaging", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getParent", "acc": 1, "dsc": "()Lorg/apache/maven/model/Parent;"}, {"nme": "getPrerequisites", "acc": 1, "dsc": "()Lorg/apache/maven/model/Prerequisites;"}, {"nme": "getProfiles", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/Profile;>;"}, {"nme": "getScm", "acc": 1, "dsc": "()Lorg/apache/maven/model/Scm;"}, {"nme": "getUrl", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "removeContributor", "acc": 1, "dsc": "(Lorg/apache/maven/model/Contributor;)V"}, {"nme": "removeDeveloper", "acc": 1, "dsc": "(Lorg/apache/maven/model/Developer;)V"}, {"nme": "removeLicense", "acc": 1, "dsc": "(Lorg/apache/maven/model/License;)V"}, {"nme": "removeMailingList", "acc": 1, "dsc": "(Lorg/apache/maven/model/MailingList;)V"}, {"nme": "removeProfile", "acc": 1, "dsc": "(Lorg/apache/maven/model/Profile;)V"}, {"nme": "setArtifactId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setBuild", "acc": 1, "dsc": "(Lorg/apache/maven/model/Build;)V"}, {"nme": "setChildProjectUrlInheritAppendPath", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setCiManagement", "acc": 1, "dsc": "(Lorg/apache/maven/model/CiManagement;)V"}, {"nme": "setContributors", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(<PERSON><PERSON><PERSON>/util/List<Lorg/apache/maven/model/Contributor;>;)V"}, {"nme": "setDescription", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setDevelopers", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(<PERSON><PERSON><PERSON>/util/List<Lorg/apache/maven/model/Developer;>;)V"}, {"nme": "setGroupId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setInceptionYear", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setIssueManagement", "acc": 1, "dsc": "(Lorg/apache/maven/model/IssueManagement;)V"}, {"nme": "setLicenses", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Lorg/apache/maven/model/License;>;)V"}, {"nme": "setMailingLists", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Lorg/apache/maven/model/MailingList;>;)V"}, {"nme": "setModelEncoding", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setModelVersion", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setName", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setOrganization", "acc": 1, "dsc": "(Lorg/apache/maven/model/Organization;)V"}, {"nme": "setPackaging", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setParent", "acc": 1, "dsc": "(Lorg/apache/maven/model/Parent;)V"}, {"nme": "setPrerequisites", "acc": 1, "dsc": "(Lorg/apache/maven/model/Prerequisites;)V"}, {"nme": "setProfiles", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Lorg/apache/maven/model/Profile;>;)V"}, {"nme": "setScm", "acc": 1, "dsc": "(Lorg/apache/maven/model/Scm;)V"}, {"nme": "setUrl", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setVersion", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(Lorg/apache/maven/model/Model;)V"}, {"nme": "getPomFile", "acc": 1, "dsc": "()Ljava/io/File;"}, {"nme": "setPomFile", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V"}, {"nme": "getProjectDirectory", "acc": 1, "dsc": "()Ljava/io/File;"}, {"nme": "getId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isChildProjectUrlInheritAppendPath", "acc": 1, "dsc": "()Z"}, {"nme": "setChildProjectUrlInheritAppendPath", "acc": 1, "dsc": "(Z)V"}, {"nme": "clone", "acc": 4161, "dsc": "()Lorg/apache/maven/model/ModelBase;"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "modelVersion", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "parent", "dsc": "Lorg/apache/maven/model/Parent;"}, {"acc": 2, "nme": "groupId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "artifactId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "packaging", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "description", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "url", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "childProjectUrlInheritAppendPath", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "inceptionYear", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "organization", "dsc": "Lorg/apache/maven/model/Organization;"}, {"acc": 2, "nme": "licenses", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/model/License;>;"}, {"acc": 2, "nme": "developers", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/model/Developer;>;"}, {"acc": 2, "nme": "contributors", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/model/Contributor;>;"}, {"acc": 2, "nme": "mailingLists", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/model/MailingList;>;"}, {"acc": 2, "nme": "prerequisites", "dsc": "Lorg/apache/maven/model/Prerequisites;"}, {"acc": 2, "nme": "scm", "dsc": "Lorg/apache/maven/model/Scm;"}, {"acc": 2, "nme": "issueManagement", "dsc": "Lorg/apache/maven/model/IssueManagement;"}, {"acc": 2, "nme": "ciManagement", "dsc": "Lorg/apache/maven/model/CiManagement;"}, {"acc": 2, "nme": "build", "dsc": "Lorg/apache/maven/model/Build;"}, {"acc": 2, "nme": "profiles", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/model/Profile;>;"}, {"acc": 2, "nme": "modelEncoding", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "pomFile", "dsc": "Ljava/io/File;"}]}, "org/apache/maven/model/io/xpp3/MavenXpp3ReaderEx$1.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/io/xpp3/MavenXpp3ReaderEx$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "transform", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": []}, "org/apache/maven/model/ReportPlugin.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/ReportPlugin", "super": "org/apache/maven/model/ConfigurationContainer", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "addReportSet", "acc": 1, "dsc": "(Lorg/apache/maven/model/ReportSet;)V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/ReportPlugin;"}, {"nme": "getArtifactId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getGroupId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getReportSets", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lorg/apache/maven/model/ReportSet;>;"}, {"nme": "getVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "removeReportSet", "acc": 1, "dsc": "(Lorg/apache/maven/model/ReportSet;)V"}, {"nme": "setArtifactId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setGroupId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setReportSets", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Lorg/apache/maven/model/ReportSet;>;)V"}, {"nme": "setVersion", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "flushReportSetMap", "acc": 1, "dsc": "()V"}, {"nme": "getReportSetsAsMap", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Lorg/apache/maven/model/ReportSet;>;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "construct<PERSON>ey", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "clone", "acc": 4161, "dsc": "()Lorg/apache/maven/model/ConfigurationContainer;"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "groupId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "artifactId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "reportSets", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lorg/apache/maven/model/ReportSet;>;"}, {"acc": 2, "nme": "reportSetMap", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lorg/apache/maven/model/ReportSet;>;"}]}, "org/apache/maven/model/PluginManagement.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/PluginManagement", "super": "org/apache/maven/model/PluginContainer", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/PluginManagement;"}, {"nme": "clone", "acc": 4161, "dsc": "()Lorg/apache/maven/model/PluginContainer;"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": []}, "org/apache/maven/model/Relocation.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/Relocation", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/Relocation;"}, {"nme": "getArtifactId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getGroupId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getMessage", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "setOtherLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lorg/apache/maven/model/InputLocation;)V"}, {"nme": "getOtherLocation", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lorg/apache/maven/model/InputLocation;"}, {"nme": "getVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setArtifactId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setGroupId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setMessage", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setVersion", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "groupId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "artifactId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "message", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "locations", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;>;"}, {"acc": 2, "nme": "location", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "groupIdLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "artifactIdLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "versionLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}, {"acc": 2, "nme": "messageLocation", "dsc": "Lorg/apache/maven/model/InputLocation;"}]}, "org/apache/maven/model/io/xpp3/MavenXpp3Reader$ContentTransformer.class": {"ver": 52, "acc": 1537, "nme": "org/apache/maven/model/io/xpp3/MavenXpp3Reader$ContentTransformer", "super": "java/lang/Object", "mthds": [{"nme": "transform", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": []}, "org/apache/maven/model/merge/ModelMerger$PluginKeyComputer.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/merge/ModelMerger$PluginKeyComputer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;)V"}, {"nme": "key", "acc": 1, "dsc": "(Lorg/apache/maven/model/Plugin;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "key", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;Lorg/apache/maven/model/merge/ModelMerger$1;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lorg/apache/maven/model/merge/ModelMerger;"}]}, "org/apache/maven/model/InputLocation$StringFormatter.class": {"ver": 52, "acc": 1057, "nme": "org/apache/maven/model/InputLocation$StringFormatter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "toString", "acc": 1025, "dsc": "(Lorg/apache/maven/model/InputLocation;)Ljava/lang/String;"}], "flds": []}, "org/apache/maven/model/merge/ModelMerger$LicenseKeyComputer.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/merge/ModelMerger$LicenseKeyComputer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;)V"}, {"nme": "key", "acc": 1, "dsc": "(Lorg/apache/maven/model/License;)Ljava/lang/Object;"}, {"nme": "key", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;Lorg/apache/maven/model/merge/ModelMerger$1;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lorg/apache/maven/model/merge/ModelMerger;"}]}, "org/apache/maven/model/Resource.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/Resource", "super": "org/apache/maven/model/FileSet", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/Resource;"}, {"nme": "getFiltering", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getMergeId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setFiltering", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setMergeId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "initMergeId", "acc": 1, "dsc": "()V"}, {"nme": "isFiltering", "acc": 1, "dsc": "()Z"}, {"nme": "setFiltering", "acc": 1, "dsc": "(Z)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "clone", "acc": 4161, "dsc": "()Lorg/apache/maven/model/FileSet;"}, {"nme": "clone", "acc": 4161, "dsc": "()Lorg/apache/maven/model/PatternSet;"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "targetPath", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "filtering", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "mergeId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 10, "nme": "mergeIdCounter", "dsc": "I"}]}, "org/apache/maven/model/FileSet.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/FileSet", "super": "org/apache/maven/model/PatternSet", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/FileSet;"}, {"nme": "getDirectory", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setDirectory", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "clone", "acc": 4161, "dsc": "()Lorg/apache/maven/model/PatternSet;"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "directory", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/apache/maven/model/merge/package-info.class": {"ver": 52, "acc": 5632, "nme": "org/apache/maven/model/merge/package-info", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/apache/maven/model/Profile.class": {"ver": 52, "acc": 33, "nme": "org/apache/maven/model/Profile", "super": "org/apache/maven/model/ModelBase", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "clone", "acc": 1, "dsc": "()Lorg/apache/maven/model/Profile;"}, {"nme": "getActivation", "acc": 1, "dsc": "()Lorg/apache/maven/model/Activation;"}, {"nme": "getBuild", "acc": 1, "dsc": "()Lorg/apache/maven/model/BuildBase;"}, {"nme": "getId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setActivation", "acc": 1, "dsc": "(Lorg/apache/maven/model/Activation;)V"}, {"nme": "setBuild", "acc": 1, "dsc": "(Lorg/apache/maven/model/BuildBase;)V"}, {"nme": "setId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setSource", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getSource", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "clone", "acc": 4161, "dsc": "()Lorg/apache/maven/model/ModelBase;"}, {"nme": "clone", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}], "flds": [{"acc": 2, "nme": "id", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "activation", "dsc": "Lorg/apache/maven/model/Activation;"}, {"acc": 2, "nme": "build", "dsc": "Lorg/apache/maven/model/BuildBase;"}, {"acc": 25, "nme": "SOURCE_POM", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "pom"}, {"acc": 25, "nme": "SOURCE_SETTINGS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "settings.xml"}, {"acc": 2, "nme": "source", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "org/apache/maven/model/merge/ModelMerger$ResourceKeyComputer.class": {"ver": 52, "acc": 32, "nme": "org/apache/maven/model/merge/ModelMerger$ResourceKeyComputer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;)V"}, {"nme": "key", "acc": 1, "dsc": "(Lorg/apache/maven/model/Resource;)Ljava/lang/Object;"}, {"nme": "key", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<init>", "acc": 4096, "dsc": "(Lorg/apache/maven/model/merge/ModelMerger;Lorg/apache/maven/model/merge/ModelMerger$1;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lorg/apache/maven/model/merge/ModelMerger;"}]}}}}