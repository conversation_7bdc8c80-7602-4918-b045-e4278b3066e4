{"md5": "d7baf5ebe9e732906eb72fef7adaf987", "sha2": "ea52892bb6fbf6124661dc2bd6bd771b6761d07d", "sha256": "33eba8208848045ab9727df0475773fc8c0580c23d9b392b49aeef29b281ba8e", "contents": {"classes": {"net/kyori/adventure/text/minimessage/internal/parser/node/RootNode.class": {"ver": 52, "acc": 49, "nme": "net/kyori/adventure/text/minimessage/internal/parser/node/RootNode", "super": "net/kyori/adventure/text/minimessage/internal/parser/node/ElementNode", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "input", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": [{"acc": 18, "nme": "beforePreprocessing", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "net/kyori/adventure/text/minimessage/translation/Argument.class": {"ver": 52, "acc": 49, "nme": "net/kyori/adventure/text/minimessage/translation/Argument", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "bool", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)Lnet/kyori/adventure/text/ComponentLike;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "numeric", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/Number;)Lnet/kyori/adventure/text/ComponentLike;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "numeric", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;)Lnet/kyori/adventure/text/ComponentLike;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "component", "acc": 9, "dsc": "(L<PERSON><PERSON>/lang/String;Lnet/kyori/adventure/text/ComponentLike;)Lnet/kyori/adventure/text/ComponentLike;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "argument", "acc": 9, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/text/TranslationArgumentLike;)Lnet/kyori/adventure/text/ComponentLike;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "argument", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;Lnet/kyori/adventure/text/TranslationArgument;)Lnet/kyori/adventure/text/ComponentLike;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "tag", "acc": 9, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/Tag;)Lnet/kyori/adventure/text/ComponentLike;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "tagResolver", "acc": 137, "dsc": "([Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Lnet/kyori/adventure/text/ComponentLike;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "tagResolver", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)Lnet/kyori/adventure/text/ComponentLike;", "sig": "(Ljava/lang/Iterable<Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;>;)Lnet/kyori/adventure/text/ComponentLike;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "tagResolver", "acc": 9, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Lnet/kyori/adventure/text/ComponentLike;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "target", "acc": 9, "dsc": "(Lnet/kyori/adventure/pointer/Pointered;)Lnet/kyori/adventure/text/ComponentLike;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": []}, "net/kyori/adventure/text/minimessage/tag/standard/PrideTag.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/standard/PrideTag", "super": "net/kyori/adventure/text/minimessage/tag/standard/GradientTag", "mthds": [{"nme": "create", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;"}, {"nme": "<init>", "acc": 0, "dsc": "(DLjava/util/List;Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/Context;)V", "sig": "(DLjava/util/List<Lnet/kyori/adventure/text/format/TextColor;>;Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/Context;)V"}, {"nme": "examinableProperties", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<+Lnet/kyori/examination/ExaminableProperty;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "colors", "acc": 138, "dsc": "([I)<PERSON>ja<PERSON>/util/List;", "sig": "([I)Ljava/util/List<Lnet/kyori/adventure/text/format/TextColor;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "PRIDE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "pride"}, {"acc": 24, "nme": "RESOLVER", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}, {"acc": 26, "nme": "FLAGS", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Lnet/kyori/adventure/text/format/TextColor;>;>;"}, {"acc": 18, "nme": "flag", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "net/kyori/adventure/text/minimessage/translation/MiniMessageTranslator.class": {"ver": 52, "acc": 1057, "nme": "net/kyori/adventure/text/minimessage/translation/MiniMessageTranslator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/minimessage/MiniMessage;)V"}, {"nme": "getMiniMessageString", "acc": 1028, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Locale;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "translate", "acc": 17, "dsc": "(L<PERSON><PERSON>/lang/String;Ljava/util/Locale;)Ljava/text/MessageFormat;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "translate", "acc": 17, "dsc": "(Lnet/kyori/adventure/text/TranslatableComponent;Ljava/util/Locale;)Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}], "flds": [{"acc": 18, "nme": "miniMessage", "dsc": "Lnet/kyori/adventure/text/minimessage/MiniMessage;"}]}, "net/kyori/adventure/text/minimessage/tag/standard/HoverTag$ActionHandler.class": {"ver": 52, "acc": 1536, "nme": "net/kyori/adventure/text/minimessage/tag/standard/HoverTag$ActionHandler", "super": "java/lang/Object", "mthds": [{"nme": "parse", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Ljava/lang/Object;", "sig": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)TV;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "emit", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V", "sig": "(TV;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V"}], "flds": []}, "net/kyori/adventure/text/minimessage/internal/parser/node/ValueNode.class": {"ver": 52, "acc": 1057, "nme": "net/kyori/adventure/text/minimessage/internal/parser/node/ValueNode", "super": "net/kyori/adventure/text/minimessage/internal/parser/node/ElementNode", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lnet/kyori/adventure/text/minimessage/internal/parser/node/ElementNode;Lnet/kyori/adventure/text/minimessage/internal/parser/Token;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "valueName", "acc": 1024, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "value", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "token", "acc": 1, "dsc": "()Lnet/kyori/adventure/text/minimessage/internal/parser/Token;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "buildToString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;I)Ljava/lang/StringBuilder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": [{"acc": 18, "nme": "value", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "net/kyori/adventure/text/minimessage/MiniMessageImpl$BuilderImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/MiniMessageImpl$BuilderImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "<init>", "acc": 0, "dsc": "(Lnet/kyori/adventure/text/minimessage/MiniMessageImpl;)V"}, {"nme": "tags", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Lnet/kyori/adventure/text/minimessage/MiniMessage$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "editTags", "acc": 1, "dsc": "(Ljava/util/function/Consumer;)Lnet/kyori/adventure/text/minimessage/MiniMessage$Builder;", "sig": "(Ljava/util/function/Consumer<Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Builder;>;)Lnet/kyori/adventure/text/minimessage/MiniMessage$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "strict", "acc": 1, "dsc": "(Z)Lnet/kyori/adventure/text/minimessage/MiniMessage$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "emitVirtuals", "acc": 1, "dsc": "(Z)Lnet/kyori/adventure/text/minimessage/MiniMessage$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "debug", "acc": 1, "dsc": "(Ljava/util/function/Consumer;)Lnet/kyori/adventure/text/minimessage/MiniMessage$Builder;", "sig": "(Ljava/util/function/Consumer<Ljava/lang/String;>;)Lnet/kyori/adventure/text/minimessage/MiniMessage$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "postProcessor", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/function/UnaryOperator;)Lnet/kyori/adventure/text/minimessage/MiniMessage$Builder;", "sig": "(Ljava/util/function/UnaryOperator<Lnet/kyori/adventure/text/Component;>;)Lnet/kyori/adventure/text/minimessage/MiniMessage$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "preProcessor", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/function/UnaryOperator;)Lnet/kyori/adventure/text/minimessage/MiniMessage$Builder;", "sig": "(Ljava/util/function/UnaryOperator<Ljava/lang/String;>;)Lnet/kyori/adventure/text/minimessage/MiniMessage$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "build", "acc": 1, "dsc": "()Lnet/kyori/adventure/text/minimessage/MiniMessage;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "build", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": [{"acc": 2, "nme": "tagResolver", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}, {"acc": 2, "nme": "strict", "dsc": "Z"}, {"acc": 2, "nme": "emitVirtuals", "dsc": "Z"}, {"acc": 2, "nme": "debug", "dsc": "Ljava/util/function/Consumer;", "sig": "Ljava/util/function/Consumer<Ljava/lang/String;>;"}, {"acc": 2, "nme": "postProcessor", "dsc": "<PERSON><PERSON><PERSON>/util/function/UnaryOperator;", "sig": "Ljava/util/function/UnaryOperator<Lnet/kyori/adventure/text/Component;>;"}, {"acc": 2, "nme": "preProcessor", "dsc": "<PERSON><PERSON><PERSON>/util/function/UnaryOperator;", "sig": "Ljava/util/function/UnaryOperator<Ljava/lang/String;>;"}]}, "net/kyori/adventure/text/minimessage/internal/parser/TokenParser$SecondPassState.class": {"ver": 52, "acc": 16432, "nme": "net/kyori/adventure/text/minimessage/internal/parser/TokenParser$SecondPassState", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lnet/kyori/adventure/text/minimessage/internal/parser/TokenParser$SecondPassState;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/internal/parser/TokenParser$SecondPassState;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Lnet/kyori/adventure/text/minimessage/internal/parser/TokenParser$SecondPassState;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "NORMAL", "dsc": "Lnet/kyori/adventure/text/minimessage/internal/parser/TokenParser$SecondPassState;"}, {"acc": 16409, "nme": "STRING", "dsc": "Lnet/kyori/adventure/text/minimessage/internal/parser/TokenParser$SecondPassState;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lnet/kyori/adventure/text/minimessage/internal/parser/TokenParser$SecondPassState;"}]}, "net/kyori/adventure/text/minimessage/internal/serializer/ComponentClaimingResolverImpl.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/text/minimessage/internal/serializer/ComponentClaimingResolverImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(L<PERSON><PERSON>/util/Set;Ljava/util/function/BiFunction;Ljava/util/function/Function;)V", "sig": "(Ljava/util/Set<Ljava/lang/String;>;Ljava/util/function/BiFunction<Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;Lnet/kyori/adventure/text/minimessage/tag/Tag;>;Ljava/util/function/Function<Lnet/kyori/adventure/text/Component;Lnet/kyori/adventure/text/minimessage/internal/serializer/Emitable;>;)V"}, {"nme": "resolve", "acc": 1, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "has", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "claimComponent", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;)Lnet/kyori/adventure/text/minimessage/internal/serializer/Emitable;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}], "flds": [{"acc": 18, "nme": "names", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 18, "nme": "handler", "dsc": "Lja<PERSON>/util/function/BiFunction;", "sig": "Ljava/util/function/BiFunction<Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;Lnet/kyori/adventure/text/minimessage/tag/Tag;>;"}, {"acc": 18, "nme": "componentClaim", "dsc": "Ljava/util/function/Function;", "sig": "Ljava/util/function/Function<Lnet/kyori/adventure/text/Component;Lnet/kyori/adventure/text/minimessage/internal/serializer/Emitable;>;"}]}, "net/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Builder.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Builder", "super": "java/lang/Object", "mthds": [{"nme": "tag", "acc": 1025, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/Tag;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "tag", "acc": 1, "dsc": "(Ljava/lang/String;Ljava/util/function/BiFunction;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Builder;", "sig": "(Ljava/lang/String;Ljava/util/function/BiFunction<Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;Lnet/kyori/adventure/text/minimessage/tag/Tag;>;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "tag", "acc": 1, "dsc": "(Ljava/util/Set;Ljava/util/function/BiFunction;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Builder;", "sig": "(Ljava/util/Set<Ljava/lang/String;>;Ljava/util/function/BiFunction<Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;Lnet/kyori/adventure/text/minimessage/tag/Tag;>;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "resolver", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "resolvers", "acc": 1153, "dsc": "([Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "resolvers", "acc": 1025, "dsc": "(Lja<PERSON>/lang/Iterable;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Builder;", "sig": "(Ljava/lang/Iterable<+Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;>;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "caching", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$WithoutArguments;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "build", "acc": 1025, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": []}, "net/kyori/adventure/text/minimessage/tag/AbstractTag.class": {"ver": 52, "acc": 1056, "nme": "net/kyori/adventure/text/minimessage/tag/AbstractTag", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "toString", "acc": 17, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "net/kyori/adventure/text/minimessage/tag/resolver/MapTagResolver.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/resolver/MapTagResolver", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/util/Map;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;+Lnet/kyori/adventure/text/minimessage/tag/Tag;>;)V"}, {"nme": "resolve", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "contributeToMap", "acc": 1, "dsc": "(Ljava/util/Map;)Z", "sig": "(Ljava/util/Map<Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/Tag;>;)Z"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "tagMap", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;+Lnet/kyori/adventure/text/minimessage/tag/Tag;>;"}]}, "net/kyori/adventure/text/minimessage/internal/parser/TokenParser.class": {"ver": 52, "acc": 49, "nme": "net/kyori/adventure/text/minimessage/internal/parser/TokenParser", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "parse", "acc": 9, "dsc": "(Lnet/kyori/adventure/text/minimessage/internal/parser/TokenParser$TagProvider;Ljava/util/function/Predicate;Ljava/lang/String;Ljava/lang/String;Z)Lnet/kyori/adventure/text/minimessage/internal/parser/node/RootNode;", "sig": "(Lnet/kyori/adventure/text/minimessage/internal/parser/TokenParser$TagProvider;Ljava/util/function/Predicate<Ljava/lang/String;>;Ljava/lang/String;Ljava/lang/String;Z)Lnet/kyori/adventure/text/minimessage/internal/parser/node/RootNode;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"]}, {"nme": "resolvePreProcessTags", "acc": 9, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/internal/parser/TokenParser$TagProvider;)Ljava/lang/String;"}, {"nme": "tokenize", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)Ljava/util/List;", "sig": "(Lja<PERSON>/lang/String;Z)Ljava/util/List<Lnet/kyori/adventure/text/minimessage/internal/parser/Token;>;"}, {"nme": "parseString", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;ZLnet/kyori/adventure/text/minimessage/internal/parser/match/MatchedTokenConsumer;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/String;ZLnet/kyori/adventure/text/minimessage/internal/parser/match/MatchedTokenConsumer<*>;)V"}, {"nme": "parseSecondPass", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Lja<PERSON>/lang/String;Ljava/util/List<Lnet/kyori/adventure/text/minimessage/internal/parser/Token;>;)V"}, {"nme": "buildTree", "acc": 10, "dsc": "(Lnet/kyori/adventure/text/minimessage/internal/parser/TokenParser$TagProvider;Ljava/util/function/Predicate;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;Z)Lnet/kyori/adventure/text/minimessage/internal/parser/node/RootNode;", "sig": "(Lnet/kyori/adventure/text/minimessage/internal/parser/TokenParser$TagProvider;Ljava/util/function/Predicate<Ljava/lang/String;>;Ljava/util/List<Lnet/kyori/adventure/text/minimessage/internal/parser/Token;>;Ljava/lang/String;Ljava/lang/String;Z)Lnet/kyori/adventure/text/minimessage/internal/parser/node/RootNode;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"]}, {"nme": "tagCloses", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/util/List;)Z", "sig": "(Ljava/util/List<Ljava/lang/String;>;Ljava/util/List<Lnet/kyori/adventure/text/minimessage/internal/parser/node/TagPart;>;)Z"}, {"nme": "boundsCheck", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;II)Z"}, {"nme": "insert", "acc": 10, "dsc": "(Lnet/kyori/adventure/text/minimessage/internal/parser/Token;Lnet/kyori/adventure/text/minimessage/internal/parser/Token;)V"}, {"nme": "unescape", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;IILjava/util/function/IntPredicate;)Ljava/lang/String;"}], "flds": [{"acc": 26, "nme": "MAX_DEPTH", "dsc": "I", "val": 16}, {"acc": 25, "nme": "TAG_START", "dsc": "C", "val": 60}, {"acc": 25, "nme": "TAG_END", "dsc": "C", "val": 62}, {"acc": 25, "nme": "CLOSE_TAG", "dsc": "C", "val": 47}, {"acc": 25, "nme": "SEPARATOR", "dsc": "C", "val": 58}, {"acc": 25, "nme": "ESCAPE", "dsc": "C", "val": 92}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$Internal;"}]}, "net/kyori/adventure/text/minimessage/internal/parser/node/TagPart.class": {"ver": 52, "acc": 49, "nme": "net/kyori/adventure/text/minimessage/internal/parser/node/TagPart", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lnet/kyori/adventure/text/minimessage/internal/parser/Token;Lnet/kyori/adventure/text/minimessage/internal/parser/TokenParser$TagProvider;)V"}, {"nme": "value", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "token", "acc": 1, "dsc": "()Lnet/kyori/adventure/text/minimessage/internal/parser/Token;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "unquoteAndEscape", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;II)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$unquoteAndEscape$0", "acc": 4106, "dsc": "(CI)Z"}], "flds": [{"acc": 18, "nme": "value", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "token", "dsc": "Lnet/kyori/adventure/text/minimessage/internal/parser/Token;"}]}, "net/kyori/adventure/text/minimessage/internal/serializer/QuotingOverride.class": {"ver": 52, "acc": 16433, "nme": "net/kyori/adventure/text/minimessage/internal/serializer/QuotingOverride", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lnet/kyori/adventure/text/minimessage/internal/serializer/QuotingOverride;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/internal/serializer/QuotingOverride;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Lnet/kyori/adventure/text/minimessage/internal/serializer/QuotingOverride;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "UNQUOTED", "dsc": "Lnet/kyori/adventure/text/minimessage/internal/serializer/QuotingOverride;"}, {"acc": 16409, "nme": "QUOTED", "dsc": "Lnet/kyori/adventure/text/minimessage/internal/serializer/QuotingOverride;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lnet/kyori/adventure/text/minimessage/internal/serializer/QuotingOverride;"}]}, "net/kyori/adventure/text/minimessage/tag/standard/FontTag.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/standard/FontTag", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "create", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"]}, {"nme": "emit", "acc": 8, "dsc": "(Lnet/kyori/adventure/key/Key;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V"}, {"nme": "lambda$create$0", "acc": 4106, "dsc": "(Lnet/kyori/adventure/key/Key;Lnet/kyori/adventure/text/format/Style$Builder;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "FONT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "font"}, {"acc": 24, "nme": "RESOLVER", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}]}, "net/kyori/adventure/text/minimessage/tree/Node.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/text/minimessage/tree/Node", "super": "java/lang/Object", "mthds": [{"nme": "toString", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "children", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Lnet/kyori/adventure/text/minimessage/tree/Node;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "parent", "acc": 1025, "dsc": "()Lnet/kyori/adventure/text/minimessage/tree/Node;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}], "flds": [], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$NonExtendable;"}]}, "net/kyori/adventure/text/minimessage/tag/resolver/TagResolver$WithoutArguments.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/text/minimessage/tag/resolver/TagResolver$WithoutArguments", "super": "java/lang/Object", "mthds": [{"nme": "resolve", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "has", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "resolve", "acc": 1, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/FunctionalInterface;"}]}, "net/kyori/adventure/text/minimessage/tag/standard/StandardTags.class": {"ver": 52, "acc": 49, "nme": "net/kyori/adventure/text/minimessage/tag/standard/StandardTags", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "decorations", "acc": 9, "dsc": "(Lnet/kyori/adventure/text/format/TextDecoration;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "decorations", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "color", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "hoverEvent", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "clickEvent", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "keybind", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "translatable", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "translatableFallback", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "insertion", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "font", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "gradient", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "rainbow", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "transition", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}, {"nme": "reset", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "newline", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "selector", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "score", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "nbt", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "pride", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "shadowColor", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "defaults", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "names", "acc": 136, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/util/Set;", "sig": "([<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Set<Ljava/lang/String;>;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "ALL", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}]}, "net/kyori/adventure/text/minimessage/tag/standard/NewlineTag.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/standard/NewlineTag", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "create", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"]}, {"nme": "claimComponent", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/Component;)Lnet/kyori/adventure/text/minimessage/internal/serializer/Emitable;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "lambda$claimComponent$0", "acc": 4106, "dsc": "(Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "BR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "br"}, {"acc": 26, "nme": "NEWLINE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "newline"}, {"acc": 24, "nme": "RESOLVER", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}]}, "net/kyori/adventure/text/minimessage/internal/serializer/StyleClaim.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/text/minimessage/internal/serializer/StyleClaim", "super": "java/lang/Object", "mthds": [{"nme": "claim", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/function/Function;Ljava/util/function/BiConsumer;)Lnet/kyori/adventure/text/minimessage/internal/serializer/StyleClaim;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/String;Ljava/util/function/Function<Lnet/kyori/adventure/text/format/Style;TT;>;Ljava/util/function/BiConsumer<TT;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;>;)Lnet/kyori/adventure/text/minimessage/internal/serializer/StyleClaim<TT;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "claim", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/function/Function;Ljava/util/function/Predicate;Ljava/util/function/BiConsumer;)Lnet/kyori/adventure/text/minimessage/internal/serializer/StyleClaim;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/String;Ljava/util/function/Function<Lnet/kyori/adventure/text/format/Style;TT;>;Ljava/util/function/Predicate<TT;>;Ljava/util/function/BiConsumer<TT;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;>;)Lnet/kyori/adventure/text/minimessage/internal/serializer/StyleClaim<TT;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "claim<PERSON>ey", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "apply", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/format/Style;)Lnet/kyori/adventure/text/minimessage/internal/serializer/Emitable;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "lambda$claim$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "flds": []}, "net/kyori/adventure/text/minimessage/translation/ArgumentTag.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/translation/ArgumentTag", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/util/List;Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)V", "sig": "(Ljava/util/List<Lnet/kyori/adventure/text/minimessage/tag/Tag;>;Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)V"}, {"nme": "resolve", "acc": 1, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "has", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "lambda$resolve$0", "acc": 4106, "dsc": "(Lnet/kyori/adventure/text/minimessage/Context;Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;)Lnet/kyori/adventure/text/minimessage/ParsingException;"}], "flds": [{"acc": 26, "nme": "NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "argument"}, {"acc": 26, "nme": "NAME_1", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "arg"}, {"acc": 18, "nme": "arguments", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lnet/kyori/adventure/text/minimessage/tag/Tag;>;"}, {"acc": 18, "nme": "tagResolver", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}]}, "net/kyori/adventure/text/minimessage/internal/serializer/SerializableResolver.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/text/minimessage/internal/serializer/SerializableResolver", "super": "java/lang/Object", "mthds": [{"nme": "claimingComponent", "acc": 9, "dsc": "(Ljava/lang/String;Ljava/util/function/BiFunction;Ljava/util/function/Function;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "sig": "(Ljava/lang/String;Ljava/util/function/BiFunction<Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;Lnet/kyori/adventure/text/minimessage/tag/Tag;>;Ljava/util/function/Function<Lnet/kyori/adventure/text/Component;Lnet/kyori/adventure/text/minimessage/internal/serializer/Emitable;>;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "claimingComponent", "acc": 9, "dsc": "(Lja<PERSON>/util/Set;Ljava/util/function/BiFunction;Ljava/util/function/Function;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "sig": "(Ljava/util/Set<Ljava/lang/String;>;Ljava/util/function/BiFunction<Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;Lnet/kyori/adventure/text/minimessage/tag/Tag;>;Ljava/util/function/Function<Lnet/kyori/adventure/text/Component;Lnet/kyori/adventure/text/minimessage/internal/serializer/Emitable;>;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "claimingStyle", "acc": 9, "dsc": "(Ljava/lang/String;Ljava/util/function/BiFunction;Lnet/kyori/adventure/text/minimessage/internal/serializer/StyleClaim;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "sig": "(Ljava/lang/String;Ljava/util/function/BiFunction<Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;Lnet/kyori/adventure/text/minimessage/tag/Tag;>;Lnet/kyori/adventure/text/minimessage/internal/serializer/StyleClaim<*>;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "claimingStyle", "acc": 9, "dsc": "(Ljava/util/Set;Ljava/util/function/BiFunction;Lnet/kyori/adventure/text/minimessage/internal/serializer/StyleClaim;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "sig": "(Ljava/util/Set<Ljava/lang/String;>;Ljava/util/function/BiFunction<Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;Lnet/kyori/adventure/text/minimessage/tag/Tag;>;Lnet/kyori/adventure/text/minimessage/internal/serializer/StyleClaim<*>;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "handle", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/Component;Lnet/kyori/adventure/text/minimessage/internal/serializer/ClaimConsumer;)V"}], "flds": []}, "net/kyori/adventure/text/minimessage/translation/MiniMessageTranslationStoreImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/translation/MiniMessageTranslationStoreImpl", "super": "net/kyori/adventure/translation/AbstractTranslationStore$StringBased", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lnet/kyori/adventure/key/Key;Lnet/kyori/adventure/text/minimessage/MiniMessage;)V"}, {"nme": "parse", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Locale;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "translate", "acc": 1, "dsc": "(L<PERSON><PERSON>/lang/String;Ljava/util/Locale;)Ljava/text/MessageFormat;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "translate", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/TranslatableComponent;Ljava/util/Locale;)Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "parse", "acc": 4164, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Locale;)<PERSON>ja<PERSON>/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "access$100", "acc": 4104, "dsc": "(Lnet/kyori/adventure/text/minimessage/translation/MiniMessageTranslationStoreImpl;Ljava/lang/String;Ljava/util/Locale;)Ljava/lang/Object;"}], "flds": [{"acc": 18, "nme": "translator", "dsc": "Lnet/kyori/adventure/text/minimessage/translation/MiniMessageTranslationStoreImpl$Translator;"}]}, "net/kyori/adventure/text/minimessage/tag/resolver/SingleResolver.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/resolver/SingleResolver", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lja<PERSON>/lang/String;Lnet/kyori/adventure/text/minimessage/tag/Tag;)V"}, {"nme": "key", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "tag", "acc": 1, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/Tag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "contributeToMap", "acc": 1, "dsc": "(Ljava/util/Map;)Z", "sig": "(Ljava/util/Map<Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/Tag;>;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "flds": [{"acc": 18, "nme": "key", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "tag", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/Tag;"}]}, "net/kyori/adventure/text/minimessage/translation/MiniMessageTranslationStoreImpl$1.class": {"ver": 52, "acc": 4128, "nme": "net/kyori/adventure/text/minimessage/translation/MiniMessageTranslationStoreImpl$1", "super": "java/lang/Object", "mthds": [], "flds": []}, "net/kyori/adventure/text/minimessage/ParsingException.class": {"ver": 52, "acc": 1057, "nme": "net/kyori/adventure/text/minimessage/ParsingException", "super": "java/lang/RuntimeException", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;ZZ)V"}, {"nme": "originalText", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "detailMessage", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "startIndex", "acc": 1025, "dsc": "()I"}, {"nme": "endIndex", "acc": 1025, "dsc": "()I"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 4502774670340827070}, {"acc": 25, "nme": "LOCATION_UNKNOWN", "dsc": "I", "val": -1}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$NonExtendable;"}]}, "net/kyori/adventure/text/minimessage/tag/standard/DecorationTag.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/standard/DecorationTag", "super": "java/lang/Object", "mthds": [{"nme": "resolvers", "acc": 136, "dsc": "(Lnet/kyori/adventure/text/format/TextDecoration;Ljava/lang/String;[Ljava/lang/String;)Ljava/util/Map$Entry;", "sig": "(Lnet/kyori/adventure/text/format/TextDecoration;Ljava/lang/String;[Ljava/lang/String;)Ljava/util/Map$Entry<Lnet/kyori/adventure/text/format/TextDecoration;Ljava/util/stream/Stream<Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;>;>;"}, {"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "create", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/format/TextDecoration;Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;"}, {"nme": "createNegated", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/format/TextDecoration;)Lnet/kyori/adventure/text/minimessage/tag/Tag;"}, {"nme": "claim", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/format/TextDecoration;Ljava/util/function/BiConsumer;)Lnet/kyori/adventure/text/minimessage/internal/serializer/StyleClaim;", "sig": "(Lnet/kyori/adventure/text/format/TextDecoration;Ljava/util/function/BiConsumer<Lnet/kyori/adventure/text/format/TextDecoration$State;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;>;)Lnet/kyori/adventure/text/minimessage/internal/serializer/StyleClaim<Lnet/kyori/adventure/text/format/TextDecoration$State;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "emit", "acc": 8, "dsc": "(Ljava/lang/String;Ljava/lang/String;Lnet/kyori/adventure/text/format/TextDecoration$State;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V"}, {"nme": "lambda$claim$6", "acc": 4106, "dsc": "(Lnet/kyori/adventure/text/format/TextDecoration$State;)Z"}, {"nme": "lambda$claim$5", "acc": 4106, "dsc": "(Lnet/kyori/adventure/text/format/TextDecoration;Lnet/kyori/adventure/text/format/Style;)Lnet/kyori/adventure/text/format/TextDecoration$State;"}, {"nme": "lambda$static$4", "acc": 4106, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}, {"nme": "lambda$static$3", "acc": 4106, "dsc": "(Ljava/util/Map$Entry;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}, {"nme": "lambda$resolvers$2", "acc": 4106, "dsc": "(Lnet/kyori/adventure/text/format/TextDecoration;Ljava/lang/String;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Single;"}, {"nme": "lambda$resolvers$1", "acc": 4106, "dsc": "(Ljava/lang/String;Ljava/lang/String;Lnet/kyori/adventure/text/format/TextDecoration$State;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V"}, {"nme": "lambda$resolvers$0", "acc": 4106, "dsc": "(Lnet/kyori/adventure/text/format/TextDecoration;Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "B", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "b"}, {"acc": 26, "nme": "I", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "i"}, {"acc": 26, "nme": "EM", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "em"}, {"acc": 26, "nme": "OBF", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "obf"}, {"acc": 26, "nme": "ST", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "st"}, {"acc": 26, "nme": "U", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "u"}, {"acc": 25, "nme": "REVERT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "!"}, {"acc": 24, "nme": "RESOLVERS", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Lnet/kyori/adventure/text/format/TextDecoration;Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;>;"}, {"acc": 24, "nme": "RESOLVER", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}]}, "net/kyori/adventure/text/minimessage/MiniMessageParser$1.class": {"ver": 52, "acc": 4128, "nme": "net/kyori/adventure/text/minimessage/MiniMessageParser$1", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$net$kyori$adventure$text$minimessage$internal$parser$TokenType", "dsc": "[I"}]}, "net/kyori/adventure/text/minimessage/tag/standard/AbstractColorChangingTag$TagInfoHolder.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/standard/AbstractColorChangingTag$TagInfoHolder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/util/function/Consumer;Lnet/kyori/adventure/text/Component;)V", "sig": "(Ljava/util/function/Consumer<Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;>;Lnet/kyori/adventure/text/Component;)V"}, {"nme": "apply", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Void;)Lnet/kyori/adventure/text/ComponentLike;"}, {"nme": "fallbackString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "emit", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V"}, {"nme": "substitute", "acc": 1, "dsc": "()Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "apply", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lnet/kyori/adventure/text/ComponentLike;"}], "flds": [{"acc": 18, "nme": "output", "dsc": "Ljava/util/function/Consumer;", "sig": "Ljava/util/function/Consumer<Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;>;"}, {"acc": 18, "nme": "originalComp", "dsc": "Lnet/kyori/adventure/text/Component;"}]}, "net/kyori/adventure/text/minimessage/tag/standard/KeybindTag.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/standard/KeybindTag", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "create", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"]}, {"nme": "emit", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/Component;)Lnet/kyori/adventure/text/minimessage/internal/serializer/Emitable;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "lambda$emit$0", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "KEYBIND", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "key"}, {"acc": 24, "nme": "RESOLVER", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}]}, "net/kyori/adventure/text/minimessage/internal/parser/TokenType.class": {"ver": 52, "acc": 16433, "nme": "net/kyori/adventure/text/minimessage/internal/parser/TokenType", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lnet/kyori/adventure/text/minimessage/internal/parser/TokenType;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/internal/parser/TokenType;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Lnet/kyori/adventure/text/minimessage/internal/parser/TokenType;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "TEXT", "dsc": "Lnet/kyori/adventure/text/minimessage/internal/parser/TokenType;"}, {"acc": 16409, "nme": "OPEN_TAG", "dsc": "Lnet/kyori/adventure/text/minimessage/internal/parser/TokenType;"}, {"acc": 16409, "nme": "OPEN_CLOSE_TAG", "dsc": "Lnet/kyori/adventure/text/minimessage/internal/parser/TokenType;"}, {"acc": 16409, "nme": "CLOSE_TAG", "dsc": "Lnet/kyori/adventure/text/minimessage/internal/parser/TokenType;"}, {"acc": 16409, "nme": "TAG_VALUE", "dsc": "Lnet/kyori/adventure/text/minimessage/internal/parser/TokenType;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lnet/kyori/adventure/text/minimessage/internal/parser/TokenType;"}]}, "net/kyori/adventure/text/minimessage/tag/standard/TransitionTag.class": {"ver": 52, "acc": 49, "nme": "net/kyori/adventure/text/minimessage/tag/standard/TransitionTag", "super": "java/lang/Object", "mthds": [{"nme": "create", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;"}, {"nme": "<init>", "acc": 2, "dsc": "(FLjava/util/List;)V", "sig": "(FLjava/util/List<Lnet/kyori/adventure/text/format/TextColor;>;)V"}, {"nme": "value", "acc": 1, "dsc": "()Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "color", "acc": 2, "dsc": "()Lnet/kyori/adventure/text/format/TextColor;"}, {"nme": "examinableProperties", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<+Lnet/kyori/examination/ExaminableProperty;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "TRANSITION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "transition"}, {"acc": 18, "nme": "colors", "dsc": "[Lnet/kyori/adventure/text/format/TextColor;"}, {"acc": 18, "nme": "phase", "dsc": "F"}, {"acc": 18, "nme": "negativePhase", "dsc": "Z"}, {"acc": 24, "nme": "RESOLVER", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}]}, "net/kyori/adventure/text/minimessage/MiniMessageParser.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/MiniMessageParser", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "<init>", "acc": 0, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)V"}, {"nme": "escapeTokens", "acc": 0, "dsc": "(Lnet/kyori/adventure/text/minimessage/ContextImpl;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "escapeTokens", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;Lnet/kyori/adventure/text/minimessage/ContextImpl;)V"}, {"nme": "escapeTokens", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/ContextImpl;)V"}, {"nme": "stripTokens", "acc": 0, "dsc": "(Lnet/kyori/adventure/text/minimessage/ContextImpl;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "processTokens", "acc": 2, "dsc": "(<PERSON>java/lang/StringBuilder;Lnet/kyori/adventure/text/minimessage/ContextImpl;Ljava/util/function/BiConsumer;)V", "sig": "(Ljava/lang/StringBuilder;Lnet/kyori/adventure/text/minimessage/ContextImpl;Ljava/util/function/BiConsumer<Lnet/kyori/adventure/text/minimessage/internal/parser/Token;Ljava/lang/StringBuilder;>;)V"}, {"nme": "processTokens", "acc": 2, "dsc": "(<PERSON>ja<PERSON>/lang/StringBuilder;Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/ContextImpl;Ljava/util/function/BiConsumer;)V", "sig": "(Ljava/lang/StringBuilder;Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/ContextImpl;Ljava/util/function/BiConsumer<Lnet/kyori/adventure/text/minimessage/internal/parser/Token;Ljava/lang/StringBuilder;>;)V"}, {"nme": "parseToTree", "acc": 0, "dsc": "(Lnet/kyori/adventure/text/minimessage/ContextImpl;)Lnet/kyori/adventure/text/minimessage/internal/parser/node/RootNode;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "parseFormat", "acc": 0, "dsc": "(Lnet/kyori/adventure/text/minimessage/ContextImpl;)Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "treeToComponent", "acc": 0, "dsc": "(Lnet/kyori/adventure/text/minimessage/internal/parser/node/ElementNode;Lnet/kyori/adventure/text/minimessage/ContextImpl;)Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "visitModifying", "acc": 2, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/Modifying;Lnet/kyori/adventure/text/minimessage/internal/parser/node/ElementNode;I)V"}, {"nme": "handleModifying", "acc": 2, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/Modifying;Lnet/kyori/adventure/text/Component;I)Lnet/kyori/adventure/text/Component;"}, {"nme": "lambda$parseToTree$4", "acc": 4106, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;Ljava/lang/String;)Z"}, {"nme": "lambda$parseToTree$3", "acc": 4106, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;Lnet/kyori/adventure/text/minimessage/ContextImpl;Ljava/lang/String;Ljava/util/List;Lnet/kyori/adventure/text/minimessage/internal/parser/Token;)Lnet/kyori/adventure/text/minimessage/tag/Tag;"}, {"nme": "lambda$parseToTree$2", "acc": 4106, "dsc": "(Ljava/util/function/Consumer;Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;Lnet/kyori/adventure/text/minimessage/ContextImpl;Ljava/lang/String;Ljava/util/List;Lnet/kyori/adventure/text/minimessage/internal/parser/Token;)Lnet/kyori/adventure/text/minimessage/tag/Tag;"}, {"nme": "lambda$stripTokens$1", "acc": 4106, "dsc": "(Lnet/kyori/adventure/text/minimessage/internal/parser/Token;<PERSON><PERSON><PERSON>/lang/StringBuilder;)V"}, {"nme": "lambda$escapeTokens$0", "acc": 4098, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/ContextImpl;Lnet/kyori/adventure/text/minimessage/internal/parser/Token;Ljava/lang/StringBuilder;)V"}], "flds": [{"acc": 16, "nme": "tagResolver", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}]}, "net/kyori/adventure/text/minimessage/tag/standard/InsertionTag.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/standard/InsertionTag", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "create", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"]}, {"nme": "emit", "acc": 8, "dsc": "(Lja<PERSON>/lang/String;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V"}, {"nme": "lambda$create$0", "acc": 4106, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/text/format/Style$Builder;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "INSERTION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "insert"}, {"acc": 24, "nme": "RESOLVER", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}]}, "net/kyori/adventure/text/minimessage/tag/resolver/SequentialTagResolver.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/resolver/SequentialTagResolver", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "([Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)V"}, {"nme": "resolve", "acc": 1, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "has", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "handle", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;Lnet/kyori/adventure/text/minimessage/internal/serializer/ClaimConsumer;)V"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 16, "nme": "resolvers", "dsc": "[Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}]}, "net/kyori/adventure/text/minimessage/tag/PreProcessTagImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/PreProcessTagImpl", "super": "net/kyori/adventure/text/minimessage/tag/AbstractTag", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "value", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "examinableProperties", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<+Lnet/kyori/examination/ExaminableProperty;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": [{"acc": 18, "nme": "value", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "net/kyori/adventure/text/minimessage/tag/resolver/MappableResolver.class": {"ver": 52, "acc": 1536, "nme": "net/kyori/adventure/text/minimessage/tag/resolver/MappableResolver", "super": "java/lang/Object", "mthds": [{"nme": "contributeToMap", "acc": 1025, "dsc": "(Ljava/util/Map;)Z", "sig": "(Ljava/util/Map<Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/Tag;>;)Z"}], "flds": []}, "net/kyori/adventure/text/minimessage/ContextImpl.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/text/minimessage/ContextImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(ZZLjava/util/function/Consumer;Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/MiniMessage;Lnet/kyori/adventure/pointer/Pointered;Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;Ljava/util/function/UnaryOperator;Ljava/util/function/UnaryOperator;)V", "sig": "(ZZLjava/util/function/Consumer<Ljava/lang/String;>;Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/MiniMessage;Lnet/kyori/adventure/pointer/Pointered;Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;Ljava/util/function/UnaryOperator<Ljava/lang/String;>;Ljava/util/function/UnaryOperator<Lnet/kyori/adventure/text/Component;>;)V"}, {"nme": "strict", "acc": 1, "dsc": "()Z"}, {"nme": "emitVirtuals", "acc": 1, "dsc": "()Z"}, {"nme": "debugOutput", "acc": 1, "dsc": "()Ljava/util/function/Consumer;", "sig": "()Ljava/util/function/Consumer<Ljava/lang/String;>;"}, {"nme": "message", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "message", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "extraTags", "acc": 1, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "postProcessor", "acc": 1, "dsc": "()Ljava/util/function/UnaryOperator;", "sig": "()Ljava/util/function/UnaryOperator<Lnet/kyori/adventure/text/Component;>;"}, {"nme": "preProcessor", "acc": 1, "dsc": "()Ljava/util/function/UnaryOperator;", "sig": "()Ljava/util/function/UnaryOperator<Ljava/lang/String;>;"}, {"nme": "target", "acc": 1, "dsc": "()Lnet/kyori/adventure/pointer/Pointered;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "targetOrThrow", "acc": 1, "dsc": "()Lnet/kyori/adventure/pointer/Pointered;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "targetAsType", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Lnet/kyori/adventure/pointer/Pointered;", "sig": "<T::Lnet/kyori/adventure/pointer/Pointered;>(Ljava/lang/Class<TT;>;)TT;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "deserialize", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "deserialize", "acc": 1, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "deserialize", "acc": 129, "dsc": "(Lja<PERSON>/lang/String;[Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "newException", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/ParsingException;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "newException", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;)Lnet/kyori/adventure/text/minimessage/ParsingException;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "newException", "acc": 1, "dsc": "(Ljava/lang/String;Ljava/lang/Throwable;Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;)Lnet/kyori/adventure/text/minimessage/ParsingException;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "deserializeWithOptionalTarget", "acc": 2, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "tagsToTokens", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)[Lnet/kyori/adventure/text/minimessage/internal/parser/Token;", "sig": "(Ljava/util/List<+Lnet/kyori/adventure/text/minimessage/tag/Tag$Argument;>;)[Lnet/kyori/adventure/text/minimessage/internal/parser/Token;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "EMPTY_TOKEN_ARRAY", "dsc": "[Lnet/kyori/adventure/text/minimessage/internal/parser/Token;"}, {"acc": 18, "nme": "strict", "dsc": "Z"}, {"acc": 18, "nme": "emitVirtuals", "dsc": "Z"}, {"acc": 18, "nme": "debugOutput", "dsc": "Ljava/util/function/Consumer;", "sig": "Ljava/util/function/Consumer<Ljava/lang/String;>;"}, {"acc": 2, "nme": "message", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "miniMessage", "dsc": "Lnet/kyori/adventure/text/minimessage/MiniMessage;"}, {"acc": 18, "nme": "target", "dsc": "Lnet/kyori/adventure/pointer/Pointered;"}, {"acc": 18, "nme": "tagResolver", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}, {"acc": 18, "nme": "preProcessor", "dsc": "<PERSON><PERSON><PERSON>/util/function/UnaryOperator;", "sig": "Ljava/util/function/UnaryOperator<Ljava/lang/String;>;"}, {"acc": 18, "nme": "postProcessor", "dsc": "<PERSON><PERSON><PERSON>/util/function/UnaryOperator;", "sig": "Ljava/util/function/UnaryOperator<Lnet/kyori/adventure/text/Component;>;"}]}, "net/kyori/adventure/text/minimessage/tag/resolver/TagResolver.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/text/minimessage/tag/resolver/TagResolver", "super": "java/lang/Object", "mthds": [{"nme": "builder", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "standard", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "empty", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "resolver", "acc": 9, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/Tag;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Single;"}, {"nme": "resolver", "acc": 9, "dsc": "(Ljava/lang/String;Ljava/util/function/BiFunction;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "sig": "(Ljava/lang/String;Ljava/util/function/BiFunction<Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;Lnet/kyori/adventure/text/minimessage/tag/Tag;>;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "resolver", "acc": 9, "dsc": "(Lja<PERSON>/util/Set;Ljava/util/function/BiFunction;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "sig": "(Ljava/util/Set<Ljava/lang/String;>;Ljava/util/function/BiFunction<Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;Lnet/kyori/adventure/text/minimessage/tag/Tag;>;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "resolver", "acc": 137, "dsc": "([Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "resolver", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "sig": "(Ljava/lang/Iterable<+Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;>;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "caching", "acc": 9, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$WithoutArguments;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "toTagResolver", "acc": 9, "dsc": "()Ljava/util/stream/Collector;", "sig": "()Ljava/util/stream/Collector<Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;*Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "resolve", "acc": 1025, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "has", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}], "flds": []}, "net/kyori/adventure/text/minimessage/tag/standard/ResetTag.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/standard/ResetTag", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "RESET", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "reset"}, {"acc": 24, "nme": "RESOLVER", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}]}, "net/kyori/adventure/text/minimessage/tag/standard/HoverTag$ShowItem.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/standard/HoverTag$ShowItem", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "parse", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/event/HoverEvent$ShowItem;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"]}, {"nme": "legacyShowItem", "acc": 10, "dsc": "(Lnet/kyori/adventure/key/Key;ILjava/lang/String;)Lnet/kyori/adventure/text/event/HoverEvent$ShowItem;"}, {"nme": "emit", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/event/HoverEvent$ShowItem;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V"}, {"nme": "hasLegacy", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/event/HoverEvent$ShowItem;)Z"}, {"nme": "emitLegacyHover", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/event/HoverEvent$ShowItem;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V"}, {"nme": "emit", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V"}, {"nme": "parse", "acc": 4161, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Ljava/lang/Object;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"]}, {"nme": "lambda$parse$0", "acc": 4106, "dsc": "(Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/ParsingException;"}, {"nme": "access$100", "acc": 4104, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/standard/HoverTag$ShowItem;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "INSTANCE", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/standard/HoverTag$ShowItem;"}]}, "net/kyori/adventure/text/minimessage/tag/resolver/TagResolverBuilderImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/resolver/TagResolverBuilderImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "tag", "acc": 1, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/Tag;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Builder;"}, {"nme": "resolver", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Builder;"}, {"nme": "resolvers", "acc": 129, "dsc": "([Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Builder;"}, {"nme": "resolvers", "acc": 2, "dsc": "([Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;Z)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Builder;"}, {"nme": "resolvers", "acc": 1, "dsc": "(Lja<PERSON>/lang/Iterable;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Builder;", "sig": "(Ljava/lang/Iterable<+Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;>;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Builder;"}, {"nme": "single", "acc": 2, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;Z)Z"}, {"nme": "popMap", "acc": 2, "dsc": "()V"}, {"nme": "consumePotentialMappable", "acc": 2, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Z"}, {"nme": "build", "acc": 1, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Builder;Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Builder;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Builder;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "COLLECTOR", "dsc": "Ljava/util/stream/Collector;", "sig": "Ljava/util/stream/Collector<Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Builder;Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;>;"}, {"acc": 18, "nme": "replacements", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/Tag;>;"}, {"acc": 18, "nme": "resolvers", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;>;"}]}, "net/kyori/adventure/text/minimessage/tag/ParserDirective$1.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/text/minimessage/tag/ParserDirective$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "net/kyori/adventure/text/minimessage/translation/MiniMessageTranslatorTarget.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/translation/MiniMessageTranslatorTarget", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lnet/kyori/adventure/pointer/Pointered;)V"}, {"nme": "pointered", "acc": 0, "dsc": "()Lnet/kyori/adventure/pointer/Pointered;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "apply", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Void;)Lnet/kyori/adventure/text/ComponentLike;"}, {"nme": "apply", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lnet/kyori/adventure/text/ComponentLike;"}], "flds": [{"acc": 18, "nme": "pointered", "dsc": "Lnet/kyori/adventure/pointer/Pointered;"}]}, "net/kyori/adventure/text/minimessage/internal/parser/match/TokenListProducingMatchedTokenConsumer.class": {"ver": 52, "acc": 49, "nme": "net/kyori/adventure/text/minimessage/internal/parser/match/TokenListProducingMatchedTokenConsumer", "super": "net/kyori/adventure/text/minimessage/internal/parser/match/MatchedTokenConsumer", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "accept", "acc": 1, "dsc": "(IILnet/kyori/adventure/text/minimessage/internal/parser/TokenType;)V"}, {"nme": "result", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lnet/kyori/adventure/text/minimessage/internal/parser/Token;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "result", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": [{"acc": 2, "nme": "result", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lnet/kyori/adventure/text/minimessage/internal/parser/Token;>;"}]}, "net/kyori/adventure/text/minimessage/tag/TagPattern.class": {"ver": 52, "acc": 9729, "nme": "net/kyori/adventure/text/minimessage/tag/TagPattern", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "FIELD"], ["L<PERSON>va/lang/annotation/ElementType;", "PARAMETER"], ["L<PERSON>va/lang/annotation/ElementType;", "LOCAL_VARIABLE"]]]}], "invanns": [{"dsc": "Lorg/intellij/lang/annotations/Pattern;", "vals": ["value", "[!?#]?[a-z0-9_-]*"]}]}, "net/kyori/adventure/text/minimessage/tag/CallbackStylingTagImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/CallbackStylingTagImpl", "super": "net/kyori/adventure/text/minimessage/tag/AbstractTag", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/util/function/Consumer;)V", "sig": "(Ljava/util/function/Consumer<Lnet/kyori/adventure/text/format/Style$Builder;>;)V"}, {"nme": "value", "acc": 1, "dsc": "()Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "examinableProperties", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<+Lnet/kyori/examination/ExaminableProperty;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": [{"acc": 18, "nme": "styles", "dsc": "Ljava/util/function/Consumer;", "sig": "Ljava/util/function/Consumer<Lnet/kyori/adventure/text/format/Style$Builder;>;"}]}, "net/kyori/adventure/text/minimessage/translation/MiniMessageTranslatorArgument.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/translation/MiniMessageTranslatorArgument", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/String;TT;)V"}, {"nme": "name", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "data", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "apply", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Void;)Lnet/kyori/adventure/text/ComponentLike;"}, {"nme": "apply", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lnet/kyori/adventure/text/ComponentLike;"}], "flds": [{"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "data", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;", "sig": "TT;"}]}, "net/kyori/adventure/text/minimessage/tag/standard/GradientTag.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/text/minimessage/tag/standard/GradientTag", "super": "net/kyori/adventure/text/minimessage/tag/standard/AbstractColorChangingTag", "mthds": [{"nme": "create", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;"}, {"nme": "<init>", "acc": 0, "dsc": "(DLjava/util/List;Lnet/kyori/adventure/text/minimessage/Context;)V", "sig": "(DLjava/util/List<Lnet/kyori/adventure/text/format/TextColor;>;Lnet/kyori/adventure/text/minimessage/Context;)V"}, {"nme": "init", "acc": 4, "dsc": "()V"}, {"nme": "advanceColor", "acc": 4, "dsc": "()V"}, {"nme": "color", "acc": 4, "dsc": "()Lnet/kyori/adventure/text/format/TextColor;"}, {"nme": "preserveData", "acc": 4, "dsc": "()Ljava/util/function/Consumer;", "sig": "()Ljava/util/function/Consumer<Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "examinableProperties", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<+Lnet/kyori/examination/ExaminableProperty;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "lambda$preserveData$0", "acc": 4106, "dsc": "([Lnet/kyori/adventure/text/format/TextColor;DLnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "GRADIENT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "gradient"}, {"acc": 26, "nme": "DEFAULT_WHITE", "dsc": "Lnet/kyori/adventure/text/format/TextColor;"}, {"acc": 26, "nme": "DEFAULT_BLACK", "dsc": "Lnet/kyori/adventure/text/format/TextColor;"}, {"acc": 24, "nme": "RESOLVER", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}, {"acc": 2, "nme": "index", "dsc": "I"}, {"acc": 2, "nme": "multiplier", "dsc": "D"}, {"acc": 18, "nme": "colors", "dsc": "[Lnet/kyori/adventure/text/format/TextColor;"}, {"acc": 0, "nme": "phase", "dsc": "D"}, {"acc": 18, "nme": "negativePhase", "dsc": "Z"}]}, "net/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue", "super": "java/lang/Object", "mthds": [{"nme": "pop", "acc": 1025, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/Tag$Argument;"}, {"nme": "popOr", "acc": 1025, "dsc": "(Lja<PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/tag/Tag$Argument;"}, {"nme": "popOr", "acc": 1025, "dsc": "(Ljava/util/function/Supplier;)Lnet/kyori/adventure/text/minimessage/tag/Tag$Argument;", "sig": "(Ljava/util/function/Supplier<Ljava/lang/String;>;)Lnet/kyori/adventure/text/minimessage/tag/Tag$Argument;"}, {"nme": "peek", "acc": 1025, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/Tag$Argument;"}, {"nme": "hasNext", "acc": 1025, "dsc": "()Z"}, {"nme": "reset", "acc": 1025, "dsc": "()V"}], "flds": [], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$NonExtendable;"}]}, "net/kyori/adventure/text/minimessage/tag/resolver/Placeholder.class": {"ver": 52, "acc": 49, "nme": "net/kyori/adventure/text/minimessage/tag/resolver/Placeholder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "parsed", "acc": 9, "dsc": "(Ljava/lang/String;Ljava/lang/String;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Single;"}, {"nme": "unparsed", "acc": 9, "dsc": "(Ljava/lang/String;Ljava/lang/String;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Single;"}, {"nme": "component", "acc": 9, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/text/ComponentLike;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Single;"}, {"nme": "styling", "acc": 137, "dsc": "(Ljava/lang/String;[Lnet/kyori/adventure/text/format/StyleBuilderApplicable;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Single;"}], "flds": []}, "net/kyori/adventure/text/minimessage/tag/resolver/CachingTagResolver.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/resolver/CachingTagResolver", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$WithoutArguments;)V"}, {"nme": "query", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/tag/Tag;"}, {"nme": "resolve", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "has", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "contributeToMap", "acc": 1, "dsc": "(Ljava/util/Map;)Z", "sig": "(Ljava/util/Map<Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/Tag;>;)Z"}, {"nme": "handle", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;Lnet/kyori/adventure/text/minimessage/internal/serializer/ClaimConsumer;)V"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "lambda$query$1", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/tag/Tag;"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "()Lnet/kyori/adventure/text/Component;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "NULL_REPLACEMENT", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/Tag;"}, {"acc": 18, "nme": "cache", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/Tag;>;"}, {"acc": 18, "nme": "resolver", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$WithoutArguments;"}]}, "net/kyori/adventure/text/minimessage/MiniMessage$Provider.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/text/minimessage/MiniMessage$Provider", "super": "java/lang/Object", "mthds": [{"nme": "miniMessage", "acc": 1025, "dsc": "()Lnet/kyori/adventure/text/minimessage/MiniMessage;", "vanns": [{"dsc": "Lnet/kyori/adventure/util/PlatformAPI;"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$Internal;"}, {"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "builder", "acc": 1025, "dsc": "()Ljava/util/function/Consumer;", "sig": "()Ljava/util/function/Consumer<Lnet/kyori/adventure/text/minimessage/MiniMessage$Builder;>;", "vanns": [{"dsc": "Lnet/kyori/adventure/util/PlatformAPI;"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$Internal;"}, {"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": [], "vanns": [{"dsc": "Lnet/kyori/adventure/util/PlatformAPI;"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$Internal;"}]}, "net/kyori/adventure/text/minimessage/translation/MiniMessageTranslationStore.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/text/minimessage/translation/MiniMessageTranslationStore", "super": "java/lang/Object", "mthds": [{"nme": "create", "acc": 9, "dsc": "(Lnet/kyori/adventure/key/Key;)Lnet/kyori/adventure/text/minimessage/translation/MiniMessageTranslationStore;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "create", "acc": 9, "dsc": "(Lnet/kyori/adventure/key/Key;Lnet/kyori/adventure/text/minimessage/MiniMessage;)Lnet/kyori/adventure/text/minimessage/translation/MiniMessageTranslationStore;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": [], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$NonExtendable;"}]}, "net/kyori/adventure/text/minimessage/internal/parser/package-info.class": {"ver": 52, "acc": 5632, "nme": "net/kyori/adventure/text/minimessage/internal/parser/package-info", "super": "java/lang/Object", "mthds": [], "flds": [], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$Internal;"}]}, "net/kyori/adventure/text/minimessage/tag/standard/HoverTag.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/standard/HoverTag", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "create", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"]}, {"nme": "emit", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/event/HoverEvent;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V", "sig": "(Lnet/kyori/adventure/text/event/HoverEvent<*>;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V"}, {"nme": "actionHandler", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/event/HoverEvent$Action;)Lnet/kyori/adventure/text/minimessage/tag/standard/HoverTag$ActionHandler;", "sig": "<V:Ljava/lang/Object;>(Lnet/kyori/adventure/text/event/HoverEvent$Action<TV;>;)Lnet/kyori/adventure/text/minimessage/tag/standard/HoverTag$ActionHandler<TV;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "compactAsString", "acc": 8, "dsc": "(Lnet/kyori/adventure/key/Key;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "HOVER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "hover"}, {"acc": 24, "nme": "RESOLVER", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}]}, "net/kyori/adventure/text/minimessage/internal/parser/node/package-info.class": {"ver": 52, "acc": 5632, "nme": "net/kyori/adventure/text/minimessage/internal/parser/node/package-info", "super": "java/lang/Object", "mthds": [], "flds": [], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$Internal;"}]}, "net/kyori/adventure/text/minimessage/MiniMessageImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/MiniMessageImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;ZZLjava/util/function/Consumer;Ljava/util/function/UnaryOperator;Ljava/util/function/UnaryOperator;)V", "sig": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;ZZLjava/util/function/Consumer<Ljava/lang/String;>;Ljava/util/function/UnaryOperator<Ljava/lang/String;>;Ljava/util/function/UnaryOperator<Lnet/kyori/adventure/text/Component;>;)V"}, {"nme": "deserialize", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "deserialize", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lnet/kyori/adventure/pointer/Pointered;)Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "deserialize", "acc": 1, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "deserialize", "acc": 1, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/pointer/Pointered;Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "deserializeToTree", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/tree/Node$Root;"}, {"nme": "deserializeToTree", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Lnet/kyori/adventure/pointer/Pointered;)Lnet/kyori/adventure/text/minimessage/tree/Node$Root;"}, {"nme": "deserializeToTree", "acc": 1, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Lnet/kyori/adventure/text/minimessage/tree/Node$Root;"}, {"nme": "deserializeToTree", "acc": 1, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/pointer/Pointered;Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Lnet/kyori/adventure/text/minimessage/tree/Node$Root;"}, {"nme": "serialize", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "serialResolver", "acc": 2, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Lnet/kyori/adventure/text/minimessage/internal/serializer/SerializableResolver;"}, {"nme": "escapeTags", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "escapeTags", "acc": 1, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "stripTags", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "stripTags", "acc": 1, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "strict", "acc": 1, "dsc": "()Z"}, {"nme": "tags", "acc": 1, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "newContext", "acc": 2, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/pointer/Pointered;Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Lnet/kyori/adventure/text/minimessage/ContextImpl;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "serialize", "acc": 4161, "dsc": "(Lnet/kyori/adventure/text/Component;)Ljava/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "deserialize", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "lambda$static$1", "acc": 4106, "dsc": "()Ljava/util/function/Consumer;"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "(Lnet/kyori/adventure/text/minimessage/MiniMessage$Builder;)V"}, {"nme": "access$000", "acc": 4104, "dsc": "()Ljava/util/Optional;"}, {"nme": "access$100", "acc": 4104, "dsc": "(Lnet/kyori/adventure/text/minimessage/MiniMessageImpl;)Z"}, {"nme": "access$200", "acc": 4104, "dsc": "(Lnet/kyori/adventure/text/minimessage/MiniMessageImpl;)Ljava/util/function/Consumer;"}, {"nme": "access$300", "acc": 4104, "dsc": "(Lnet/kyori/adventure/text/minimessage/MiniMessageImpl;)Ljava/util/function/UnaryOperator;"}, {"nme": "access$400", "acc": 4104, "dsc": "(Lnet/kyori/adventure/text/minimessage/MiniMessageImpl;)Ljava/util/function/UnaryOperator;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "SERVICE", "dsc": "Ljava/util/Optional;", "sig": "Ljava/util/Optional<Lnet/kyori/adventure/text/minimessage/MiniMessage$Provider;>;"}, {"acc": 24, "nme": "BUILDER", "dsc": "Ljava/util/function/Consumer;", "sig": "Ljava/util/function/Consumer<Lnet/kyori/adventure/text/minimessage/MiniMessage$Builder;>;"}, {"acc": 24, "nme": "DEFAULT_NO_OP", "dsc": "<PERSON><PERSON><PERSON>/util/function/UnaryOperator;", "sig": "Ljava/util/function/UnaryOperator<Ljava/lang/String;>;"}, {"acc": 24, "nme": "DEFAULT_COMPACTING_METHOD", "dsc": "<PERSON><PERSON><PERSON>/util/function/UnaryOperator;", "sig": "Ljava/util/function/UnaryOperator<Lnet/kyori/adventure/text/Component;>;"}, {"acc": 18, "nme": "strict", "dsc": "Z"}, {"acc": 18, "nme": "emitVirtuals", "dsc": "Z"}, {"acc": 18, "nme": "debugOutput", "dsc": "Ljava/util/function/Consumer;", "sig": "Ljava/util/function/Consumer<Ljava/lang/String;>;"}, {"acc": 18, "nme": "postProcessor", "dsc": "<PERSON><PERSON><PERSON>/util/function/UnaryOperator;", "sig": "Ljava/util/function/UnaryOperator<Lnet/kyori/adventure/text/Component;>;"}, {"acc": 18, "nme": "preProcessor", "dsc": "<PERSON><PERSON><PERSON>/util/function/UnaryOperator;", "sig": "Ljava/util/function/UnaryOperator<Ljava/lang/String;>;"}, {"acc": 16, "nme": "parser", "dsc": "Lnet/kyori/adventure/text/minimessage/MiniMessageParser;"}]}, "net/kyori/adventure/text/minimessage/tag/PreProcess.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/text/minimessage/tag/PreProcess", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": []}, "net/kyori/adventure/text/minimessage/internal/serializer/StyleClaimImpl.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/text/minimessage/internal/serializer/StyleClaimImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(L<PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/function/Function;Ljava/util/function/Predicate;Ljava/util/function/BiConsumer;)V", "sig": "(Ljava/lang/String;Ljava/util/function/Function<Lnet/kyori/adventure/text/format/Style;TV;>;Ljava/util/function/Predicate<TV;>;Ljava/util/function/BiConsumer<TV;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;>;)V"}, {"nme": "claim<PERSON>ey", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "apply", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/format/Style;)Lnet/kyori/adventure/text/minimessage/internal/serializer/Emitable;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "lambda$apply$0", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V"}], "flds": [{"acc": 18, "nme": "claim<PERSON>ey", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "lens", "dsc": "Ljava/util/function/Function;", "sig": "Ljava/util/function/Function<Lnet/kyori/adventure/text/format/Style;TV;>;"}, {"acc": 18, "nme": "filter", "dsc": "<PERSON><PERSON><PERSON>/util/function/Predicate;", "sig": "Ljava/util/function/Predicate<TV;>;"}, {"acc": 18, "nme": "emitable", "dsc": "Ljava/util/function/BiConsumer;", "sig": "Ljava/util/function/BiConsumer<TV;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;>;"}]}, "net/kyori/adventure/text/minimessage/internal/serializer/package-info.class": {"ver": 52, "acc": 5632, "nme": "net/kyori/adventure/text/minimessage/internal/serializer/package-info", "super": "java/lang/Object", "mthds": [], "flds": [], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$Internal;"}]}, "net/kyori/adventure/text/minimessage/tag/standard/ColorTagResolver.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/standard/ColorTagResolver", "super": "java/lang/Object", "mthds": [{"nme": "isColorOrAbbreviation", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "resolve", "acc": 1, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "resolveColorOrNull", "acc": 8, "dsc": "(L<PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/format/TextColor;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "resolveColor", "acc": 8, "dsc": "(Lja<PERSON>/lang/String;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/format/TextColor;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "has", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "claimStyle", "acc": 1, "dsc": "()Lnet/kyori/adventure/text/minimessage/internal/serializer/StyleClaim;", "sig": "()Lnet/kyori/adventure/text/minimessage/internal/serializer/StyleClaim<*>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "(Lnet/kyori/adventure/text/format/TextColor;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "COLOR_3", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "c"}, {"acc": 26, "nme": "COLOR_2", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "colour"}, {"acc": 26, "nme": "COLOR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "color"}, {"acc": 24, "nme": "INSTANCE", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}, {"acc": 26, "nme": "STYLE", "dsc": "Lnet/kyori/adventure/text/minimessage/internal/serializer/StyleClaim;", "sig": "Lnet/kyori/adventure/text/minimessage/internal/serializer/StyleClaim<Lnet/kyori/adventure/text/format/TextColor;>;"}, {"acc": 26, "nme": "COLOR_ALIASES", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lnet/kyori/adventure/text/format/TextColor;>;"}]}, "net/kyori/adventure/text/minimessage/tag/standard/ScoreTag.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/standard/ScoreTag", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "create", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"]}, {"nme": "emit", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/Component;)Lnet/kyori/adventure/text/minimessage/internal/serializer/Emitable;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "lambda$emit$0", "acc": 4106, "dsc": "(Lnet/kyori/adventure/text/ScoreComponent;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "SCORE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "score"}, {"acc": 24, "nme": "RESOLVER", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}]}, "net/kyori/adventure/text/minimessage/internal/parser/Token.class": {"ver": 52, "acc": 49, "nme": "net/kyori/adventure/text/minimessage/internal/parser/Token", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(IILnet/kyori/adventure/text/minimessage/internal/parser/TokenType;)V"}, {"nme": "startIndex", "acc": 1, "dsc": "()I"}, {"nme": "endIndex", "acc": 1, "dsc": "()I"}, {"nme": "type", "acc": 1, "dsc": "()Lnet/kyori/adventure/text/minimessage/internal/parser/TokenType;"}, {"nme": "childTokens", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lnet/kyori/adventure/text/minimessage/internal/parser/Token;>;"}, {"nme": "childTokens", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Lnet/kyori/adventure/text/minimessage/internal/parser/Token;>;)V"}, {"nme": "get", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;)<PERSON>java/lang/CharSequence;"}, {"nme": "examinableProperties", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<+Lnet/kyori/examination/ExaminableProperty;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "startIndex", "dsc": "I"}, {"acc": 18, "nme": "endIndex", "dsc": "I"}, {"acc": 18, "nme": "type", "dsc": "Lnet/kyori/adventure/text/minimessage/internal/parser/TokenType;"}, {"acc": 2, "nme": "childTokens", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lnet/kyori/adventure/text/minimessage/internal/parser/Token;>;"}]}, "net/kyori/adventure/text/minimessage/internal/parser/node/ElementNode.class": {"ver": 52, "acc": 33, "nme": "net/kyori/adventure/text/minimessage/internal/parser/node/ElementNode", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lnet/kyori/adventure/text/minimessage/internal/parser/node/ElementNode;Lnet/kyori/adventure/text/minimessage/internal/parser/Token;Lja<PERSON>/lang/String;)V"}, {"nme": "parent", "acc": 1, "dsc": "()Lnet/kyori/adventure/text/minimessage/internal/parser/node/ElementNode;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "token", "acc": 1, "dsc": "()Lnet/kyori/adventure/text/minimessage/internal/parser/Token;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "sourceMessage", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "children", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lnet/kyori/adventure/text/minimessage/internal/parser/node/ElementNode;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "unsafeChildren", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lnet/kyori/adventure/text/minimessage/internal/parser/node/ElementNode;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/minimessage/internal/parser/node/ElementNode;)V"}, {"nme": "buildToString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;I)Ljava/lang/StringBuilder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "ident", "acc": 0, "dsc": "(I)[C"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "parent", "acc": 4161, "dsc": "()Lnet/kyori/adventure/text/minimessage/tree/Node;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}], "flds": [{"acc": 18, "nme": "parent", "dsc": "Lnet/kyori/adventure/text/minimessage/internal/parser/node/ElementNode;"}, {"acc": 18, "nme": "token", "dsc": "Lnet/kyori/adventure/text/minimessage/internal/parser/Token;"}, {"acc": 18, "nme": "sourceMessage", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "children", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lnet/kyori/adventure/text/minimessage/internal/parser/node/ElementNode;>;"}]}, "net/kyori/adventure/text/minimessage/tag/StylingTagImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/StylingTagImpl", "super": "net/kyori/adventure/text/minimessage/tag/AbstractTag", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "([Lnet/kyori/adventure/text/format/StyleBuilderApplicable;)V"}, {"nme": "value", "acc": 1, "dsc": "()Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "examinableProperties", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<+Lnet/kyori/examination/ExaminableProperty;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": [{"acc": 18, "nme": "styles", "dsc": "[Lnet/kyori/adventure/text/format/StyleBuilderApplicable;"}]}, "net/kyori/adventure/text/minimessage/tag/standard/TranslatableFallbackTag.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/standard/TranslatableFallbackTag", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "create", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"]}, {"nme": "claim", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/Component;)Lnet/kyori/adventure/text/minimessage/internal/serializer/Emitable;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "lambda$claim$0", "acc": 4106, "dsc": "(Lnet/kyori/adventure/text/TranslatableComponent;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "TR_OR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "tr_or"}, {"acc": 26, "nme": "TRANSLATE_OR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "translate_or"}, {"acc": 26, "nme": "LANG_OR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "lang_or"}, {"acc": 24, "nme": "RESOLVER", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}]}, "net/kyori/adventure/text/minimessage/MiniMessageSerializer.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/MiniMessageSerializer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "serialize", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/Component;Lnet/kyori/adventure/text/minimessage/internal/serializer/SerializableResolver;Z)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "visit", "acc": 10, "dsc": "(Lnet/kyori/adventure/text/Component;Lnet/kyori/adventure/text/minimessage/MiniMessageSerializer$Collector;Lnet/kyori/adventure/text/minimessage/internal/serializer/SerializableResolver;Z)V"}], "flds": []}, "net/kyori/adventure/text/minimessage/MiniMessageImpl$Instances.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/MiniMessageImpl$Instances", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "()Lnet/kyori/adventure/text/minimessage/MiniMessage;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "INSTANCE", "dsc": "Lnet/kyori/adventure/text/minimessage/MiniMessage;"}]}, "net/kyori/adventure/text/minimessage/tag/Tag$Argument.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/text/minimessage/tag/Tag$Argument", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "lowerValue", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "isTrue", "acc": 1, "dsc": "()Z"}, {"nme": "isFalse", "acc": 1, "dsc": "()Z"}, {"nme": "asInt", "acc": 1, "dsc": "()Ljava/util/OptionalInt;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "asDouble", "acc": 1, "dsc": "()Ljava/util/OptionalDouble;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": [], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$NonExtendable;"}]}, "net/kyori/adventure/text/minimessage/internal/serializer/Emitable.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/text/minimessage/internal/serializer/Emitable", "super": "java/lang/Object", "mthds": [{"nme": "emit", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V"}, {"nme": "substitute", "acc": 1, "dsc": "()Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/FunctionalInterface;"}]}, "net/kyori/adventure/text/minimessage/tag/resolver/TagResolver$1.class": {"ver": 52, "acc": 32, "nme": "net/kyori/adventure/text/minimessage/tag/resolver/TagResolver$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;Ljava/util/function/BiFunction;)V", "sig": "()V"}, {"nme": "resolve", "acc": 1, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "has", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}], "flds": [{"acc": 4112, "nme": "val$names", "dsc": "<PERSON><PERSON><PERSON>/util/Set;"}, {"acc": 4112, "nme": "val$handler", "dsc": "Lja<PERSON>/util/function/BiFunction;"}]}, "net/kyori/adventure/text/minimessage/tag/standard/HoverTag$ShowEntity.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/standard/HoverTag$ShowEntity", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "parse", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/event/HoverEvent$ShowEntity;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"]}, {"nme": "emit", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/event/HoverEvent$ShowEntity;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V"}, {"nme": "emit", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V"}, {"nme": "parse", "acc": 4161, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Ljava/lang/Object;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "INSTANCE", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/standard/HoverTag$ShowEntity;"}]}, "net/kyori/adventure/text/minimessage/MiniMessage$Builder.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/text/minimessage/MiniMessage$Builder", "super": "java/lang/Object", "mthds": [{"nme": "tags", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Lnet/kyori/adventure/text/minimessage/MiniMessage$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "editTags", "acc": 1025, "dsc": "(Ljava/util/function/Consumer;)Lnet/kyori/adventure/text/minimessage/MiniMessage$Builder;", "sig": "(Ljava/util/function/Consumer<Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Builder;>;)Lnet/kyori/adventure/text/minimessage/MiniMessage$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "strict", "acc": 1025, "dsc": "(Z)Lnet/kyori/adventure/text/minimessage/MiniMessage$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "emitVirtuals", "acc": 1025, "dsc": "(Z)Lnet/kyori/adventure/text/minimessage/MiniMessage$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "debug", "acc": 1025, "dsc": "(Ljava/util/function/Consumer;)Lnet/kyori/adventure/text/minimessage/MiniMessage$Builder;", "sig": "(Ljava/util/function/Consumer<Ljava/lang/String;>;)Lnet/kyori/adventure/text/minimessage/MiniMessage$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "postProcessor", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/function/UnaryOperator;)Lnet/kyori/adventure/text/minimessage/MiniMessage$Builder;", "sig": "(Ljava/util/function/UnaryOperator<Lnet/kyori/adventure/text/Component;>;)Lnet/kyori/adventure/text/minimessage/MiniMessage$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "preProcessor", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/function/UnaryOperator;)Lnet/kyori/adventure/text/minimessage/MiniMessage$Builder;", "sig": "(Ljava/util/function/UnaryOperator<Ljava/lang/String;>;)Lnet/kyori/adventure/text/minimessage/MiniMessage$Builder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "build", "acc": 1025, "dsc": "()Lnet/kyori/adventure/text/minimessage/MiniMessage;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "build", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": []}, "net/kyori/adventure/text/minimessage/internal/package-info.class": {"ver": 52, "acc": 5632, "nme": "net/kyori/adventure/text/minimessage/internal/package-info", "super": "java/lang/Object", "mthds": [], "flds": [], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$Internal;"}]}, "net/kyori/adventure/text/minimessage/internal/serializer/ClaimConsumer.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/text/minimessage/internal/serializer/ClaimConsumer", "super": "java/lang/Object", "mthds": [{"nme": "style", "acc": 1025, "dsc": "(Lja<PERSON>/lang/String;Lnet/kyori/adventure/text/minimessage/internal/serializer/Emitable;)V"}, {"nme": "component", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/minimessage/internal/serializer/Emitable;)Z"}, {"nme": "styleClaimed", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "componentClaimed", "acc": 1025, "dsc": "()Z"}], "flds": []}, "net/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter", "super": "java/lang/Object", "mthds": [{"nme": "tag", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "selfClosingTag", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "arguments", "acc": 129, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "argument", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "argument", "acc": 1025, "dsc": "(Lja<PERSON>/lang/String;Lnet/kyori/adventure/text/minimessage/internal/serializer/QuotingOverride;)Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "argument", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/Component;)Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "text", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "pop", "acc": 1025, "dsc": "()Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": []}, "net/kyori/adventure/text/minimessage/MiniMessageSerializer$Collector.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/MiniMessageSerializer$Collector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lnet/kyori/adventure/text/minimessage/internal/serializer/SerializableResolver;ZLjava/lang/StringBuilder;)V"}, {"nme": "pushActiveTag", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "popTag", "acc": 2, "dsc": "(Z)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "mark", "acc": 0, "dsc": "()V"}, {"nme": "popToMark", "acc": 0, "dsc": "()V"}, {"nme": "popAll", "acc": 0, "dsc": "()V"}, {"nme": "completeTag", "acc": 0, "dsc": "()V"}, {"nme": "tag", "acc": 1, "dsc": "(L<PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/MiniMessageSerializer$Collector;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "selfClosingTag", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "argument", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "argument", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Lnet/kyori/adventure/text/minimessage/internal/serializer/QuotingOverride;)Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "argument", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;)Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "text", "acc": 1, "dsc": "(L<PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/MiniMessageSerializer$Collector;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(L<PERSON><PERSON>/lang/String;Lnet/kyori/adventure/text/minimessage/internal/serializer/QuotingOverride;)V"}, {"nme": "appendEscaping", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;<PERSON>ja<PERSON>/lang/String;[CZ)V"}, {"nme": "pop", "acc": 1, "dsc": "()Lnet/kyori/adventure/text/minimessage/MiniMessageSerializer$Collector;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "emitClose", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "style", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Lnet/kyori/adventure/text/minimessage/internal/serializer/Emitable;)V"}, {"nme": "component", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/minimessage/internal/serializer/Emitable;)Z"}, {"nme": "componentClaimed", "acc": 1, "dsc": "()Z"}, {"nme": "styleClaimed", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "flushClaims", "acc": 0, "dsc": "(Lnet/kyori/adventure/text/Component;)Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "pop", "acc": 4161, "dsc": "()Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "text", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "tag", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "MARK", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "__<'\"\\MARK__"}, {"acc": 26, "nme": "TEXT_ESCAPES", "dsc": "[C"}, {"acc": 26, "nme": "TAG_TOKENS", "dsc": "[C"}, {"acc": 26, "nme": "SINGLE_QUOTED_ESCAPES", "dsc": "[C"}, {"acc": 26, "nme": "DOUBLE_QUOTED_ESCAPES", "dsc": "[C"}, {"acc": 18, "nme": "resolver", "dsc": "Lnet/kyori/adventure/text/minimessage/internal/serializer/SerializableResolver;"}, {"acc": 18, "nme": "strict", "dsc": "Z"}, {"acc": 18, "nme": "consumer", "dsc": "<PERSON><PERSON><PERSON>/lang/StringBuilder;"}, {"acc": 2, "nme": "activeTags", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "tagLevel", "dsc": "I"}, {"acc": 2, "nme": "tagState", "dsc": "Lnet/kyori/adventure/text/minimessage/MiniMessageSerializer$Collector$TagState;"}, {"acc": 0, "nme": "componentClaim", "dsc": "Lnet/kyori/adventure/text/minimessage/internal/serializer/Emitable;"}, {"acc": 16, "nme": "claimedStyleElements", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}]}, "net/kyori/adventure/text/minimessage/tag/resolver/Formatter.class": {"ver": 52, "acc": 49, "nme": "net/kyori/adventure/text/minimessage/tag/resolver/Formatter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "number", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/Number;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "date", "acc": 9, "dsc": "(Ljava/lang/String;Ljava/time/temporal/TemporalAccessor;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "choice", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/Number;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "booleanChoice", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}, {"nme": "joining", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/Iterable;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "sig": "(Ljava/lang/String;Ljava/lang/Iterable<+Lnet/kyori/adventure/text/ComponentLike;>;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}, {"nme": "joining", "acc": 137, "dsc": "(Lja<PERSON>/lang/String;[Lnet/kyori/adventure/text/ComponentLike;)Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}, {"nme": "lambda$joining$4", "acc": 4106, "dsc": "(Ljava/lang/Iterable;Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;"}, {"nme": "lambda$booleanChoice$3", "acc": 4106, "dsc": "(ZLnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;"}, {"nme": "lambda$choice$2", "acc": 4106, "dsc": "(Ljava/lang/Number;Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;"}, {"nme": "lambda$date$1", "acc": 4106, "dsc": "(Ljava/time/temporal/TemporalAccessor;Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;"}, {"nme": "lambda$number$0", "acc": 4106, "dsc": "(Ljava/lang/Number;Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;"}], "flds": []}, "net/kyori/adventure/text/minimessage/internal/parser/node/TextNode.class": {"ver": 52, "acc": 49, "nme": "net/kyori/adventure/text/minimessage/internal/parser/node/TextNode", "super": "net/kyori/adventure/text/minimessage/internal/parser/node/ValueNode", "mthds": [{"nme": "isEscape", "acc": 10, "dsc": "(I)Z"}, {"nme": "<init>", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/minimessage/internal/parser/node/ElementNode;Lnet/kyori/adventure/text/minimessage/internal/parser/Token;Lja<PERSON>/lang/String;)V"}, {"nme": "valueName", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "net/kyori/adventure/text/minimessage/internal/TagInternals.class": {"ver": 52, "acc": 49, "nme": "net/kyori/adventure/text/minimessage/internal/TagInternals", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "assertValidTagName", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "sanitizeAndCheckValidTagName", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "sanitizeAndAssertValidTagName", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "TAG_NAME_REGEX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "[!?#]?[a-z0-9_-]*"}, {"acc": 26, "nme": "TAG_NAME_PATTERN", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$Internal;"}]}, "net/kyori/adventure/text/minimessage/tag/standard/HoverTag$ShowText.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/standard/HoverTag$ShowText", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "parse", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/Component;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "emit", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V"}, {"nme": "emit", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V"}, {"nme": "parse", "acc": 4161, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Ljava/lang/Object;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "access$000", "acc": 4104, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/standard/HoverTag$ShowText;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "INSTANCE", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/standard/HoverTag$ShowText;"}]}, "net/kyori/adventure/text/minimessage/tag/Inserting.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/text/minimessage/tag/Inserting", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Z"}], "flds": []}, "net/kyori/adventure/text/minimessage/Context.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/text/minimessage/Context", "super": "java/lang/Object", "mthds": [{"nme": "target", "acc": 1025, "dsc": "()Lnet/kyori/adventure/pointer/Pointered;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "targetOrThrow", "acc": 1025, "dsc": "()Lnet/kyori/adventure/pointer/Pointered;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "targetAsType", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Lnet/kyori/adventure/pointer/Pointered;", "sig": "<T::Lnet/kyori/adventure/pointer/Pointered;>(Ljava/lang/Class<TT;>;)TT;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "deserialize", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "deserialize", "acc": 1025, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "deserialize", "acc": 1153, "dsc": "(Lja<PERSON>/lang/String;[Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "newException", "acc": 1025, "dsc": "(Lja<PERSON>/lang/String;Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;)Lnet/kyori/adventure/text/minimessage/ParsingException;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "newException", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/ParsingException;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "newException", "acc": 1025, "dsc": "(Ljava/lang/String;Ljava/lang/Throwable;Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;)Lnet/kyori/adventure/text/minimessage/ParsingException;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "emitVirtuals", "acc": 1025, "dsc": "()Z"}], "flds": [], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$NonExtendable;"}]}, "net/kyori/adventure/text/minimessage/MiniMessage.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/text/minimessage/MiniMessage", "super": "java/lang/Object", "mthds": [{"nme": "miniMessage", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/minimessage/MiniMessage;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "escapeTags", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "escapeTags", "acc": 1025, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "escapeTags", "acc": 129, "dsc": "(Ljava/lang/String;[Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "stripTags", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "stripTags", "acc": 1025, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "stripTags", "acc": 129, "dsc": "(Ljava/lang/String;[Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "deserialize", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lnet/kyori/adventure/pointer/Pointered;)Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "deserialize", "acc": 1025, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "deserialize", "acc": 1025, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/pointer/Pointered;Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "deserialize", "acc": 129, "dsc": "(Lja<PERSON>/lang/String;[Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "deserialize", "acc": 129, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/pointer/Pointered;[Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "deserializeToTree", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/tree/Node$Root;"}, {"nme": "deserializeToTree", "acc": 1025, "dsc": "(Lja<PERSON>/lang/String;Lnet/kyori/adventure/pointer/Pointered;)Lnet/kyori/adventure/text/minimessage/tree/Node$Root;"}, {"nme": "deserializeToTree", "acc": 1025, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Lnet/kyori/adventure/text/minimessage/tree/Node$Root;"}, {"nme": "deserializeToTree", "acc": 1025, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/pointer/Pointered;Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Lnet/kyori/adventure/text/minimessage/tree/Node$Root;"}, {"nme": "deserializeToTree", "acc": 129, "dsc": "(Ljava/lang/String;[Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Lnet/kyori/adventure/text/minimessage/tree/Node$Root;"}, {"nme": "deserializeToTree", "acc": 129, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/pointer/Pointered;[Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;)Lnet/kyori/adventure/text/minimessage/tree/Node$Root;"}, {"nme": "strict", "acc": 1025, "dsc": "()Z"}, {"nme": "tags", "acc": 1025, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "builder", "acc": 9, "dsc": "()Lnet/kyori/adventure/text/minimessage/MiniMessage$Builder;"}], "flds": []}, "net/kyori/adventure/text/minimessage/translation/MiniMessageTranslationStoreImpl$Translator.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/translation/MiniMessageTranslationStoreImpl$Translator", "super": "net/kyori/adventure/text/minimessage/translation/MiniMessageTranslator", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lnet/kyori/adventure/text/minimessage/translation/MiniMessageTranslationStoreImpl;Lnet/kyori/adventure/text/minimessage/MiniMessage;)V"}, {"nme": "getMiniMessageString", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Locale;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "name", "acc": 1, "dsc": "()Lnet/kyori/adventure/key/Key;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "hasAnyTranslations", "acc": 1, "dsc": "()Lnet/kyori/adventure/util/TriState;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "<init>", "acc": 4096, "dsc": "(Lnet/kyori/adventure/text/minimessage/translation/MiniMessageTranslationStoreImpl;Lnet/kyori/adventure/text/minimessage/MiniMessage;Lnet/kyori/adventure/text/minimessage/translation/MiniMessageTranslationStoreImpl$1;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lnet/kyori/adventure/text/minimessage/translation/MiniMessageTranslationStoreImpl;"}]}, "net/kyori/adventure/text/minimessage/internal/parser/match/StringResolvingMatchedTokenConsumer.class": {"ver": 52, "acc": 49, "nme": "net/kyori/adventure/text/minimessage/internal/parser/match/StringResolvingMatchedTokenConsumer", "super": "net/kyori/adventure/text/minimessage/internal/parser/match/MatchedTokenConsumer", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(L<PERSON><PERSON>/lang/String;Lnet/kyori/adventure/text/minimessage/internal/parser/TokenParser$TagProvider;)V"}, {"nme": "accept", "acc": 1, "dsc": "(IILnet/kyori/adventure/text/minimessage/internal/parser/TokenType;)V"}, {"nme": "result", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "result", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": [{"acc": 18, "nme": "builder", "dsc": "<PERSON><PERSON><PERSON>/lang/StringBuilder;"}, {"acc": 18, "nme": "tagProvider", "dsc": "Lnet/kyori/adventure/text/minimessage/internal/parser/TokenParser$TagProvider;"}]}, "net/kyori/adventure/text/minimessage/tag/standard/SelectorTag.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/standard/SelectorTag", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "create", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"]}, {"nme": "claim", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/Component;)Lnet/kyori/adventure/text/minimessage/internal/serializer/Emitable;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "lambda$claim$0", "acc": 4106, "dsc": "(Lnet/kyori/adventure/text/SelectorComponent;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "SEL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "sel"}, {"acc": 26, "nme": "SELECTOR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "selector"}, {"acc": 24, "nme": "RESOLVER", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}]}, "net/kyori/adventure/text/minimessage/tag/standard/TranslatableTag.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/standard/TranslatableTag", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "create", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"]}, {"nme": "claim", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/Component;)Lnet/kyori/adventure/text/minimessage/internal/serializer/Emitable;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "lambda$claim$0", "acc": 4106, "dsc": "(Lnet/kyori/adventure/text/TranslatableComponent;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "TR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "tr"}, {"acc": 26, "nme": "TRANSLATE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "translate"}, {"acc": 26, "nme": "LANG", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "lang"}, {"acc": 24, "nme": "RESOLVER", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}]}, "net/kyori/adventure/text/minimessage/tag/InsertingImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/InsertingImpl", "super": "net/kyori/adventure/text/minimessage/tag/AbstractTag", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(ZLnet/kyori/adventure/text/Component;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Z"}, {"nme": "value", "acc": 1, "dsc": "()Lnet/kyori/adventure/text/Component;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "examinableProperties", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<+Lnet/kyori/examination/ExaminableProperty;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": [{"acc": 18, "nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dsc": "Z"}, {"acc": 18, "nme": "value", "dsc": "Lnet/kyori/adventure/text/Component;"}]}, "net/kyori/adventure/text/minimessage/internal/serializer/SerializableResolver$Single.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/text/minimessage/internal/serializer/SerializableResolver$Single", "super": "java/lang/Object", "mthds": [{"nme": "handle", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;Lnet/kyori/adventure/text/minimessage/internal/serializer/ClaimConsumer;)V"}, {"nme": "claimStyle", "acc": 1, "dsc": "()Lnet/kyori/adventure/text/minimessage/internal/serializer/StyleClaim;", "sig": "()Lnet/kyori/adventure/text/minimessage/internal/serializer/StyleClaim<*>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "claimComponent", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;)Lnet/kyori/adventure/text/minimessage/internal/serializer/Emitable;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}], "flds": []}, "net/kyori/adventure/text/minimessage/tag/standard/RainbowTag.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/standard/RainbowTag", "super": "net/kyori/adventure/text/minimessage/tag/standard/AbstractColorChangingTag", "mthds": [{"nme": "create", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;"}, {"nme": "<init>", "acc": 2, "dsc": "(ZILnet/kyori/adventure/text/minimessage/Context;)V"}, {"nme": "init", "acc": 4, "dsc": "()V"}, {"nme": "advanceColor", "acc": 4, "dsc": "()V"}, {"nme": "color", "acc": 4, "dsc": "()Lnet/kyori/adventure/text/format/TextColor;"}, {"nme": "preserveData", "acc": 4, "dsc": "()Ljava/util/function/Consumer;", "sig": "()Ljava/util/function/Consumer<Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "examinableProperties", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<+Lnet/kyori/examination/ExaminableProperty;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "lambda$preserveData$0", "acc": 4106, "dsc": "(ZILnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "REVERSE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "!"}, {"acc": 26, "nme": "RAINBOW", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "rainbow"}, {"acc": 24, "nme": "RESOLVER", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}, {"acc": 18, "nme": "reversed", "dsc": "Z"}, {"acc": 18, "nme": "dividedPhase", "dsc": "D"}, {"acc": 2, "nme": "colorIndex", "dsc": "I"}]}, "net/kyori/adventure/text/minimessage/internal/parser/ParsingExceptionImpl.class": {"ver": 52, "acc": 33, "nme": "net/kyori/adventure/text/minimessage/internal/parser/ParsingExceptionImpl", "super": "net/kyori/adventure/text/minimessage/ParsingException", "mthds": [{"nme": "<init>", "acc": 129, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;[Lnet/kyori/adventure/text/minimessage/internal/parser/Token;)V"}, {"nme": "<init>", "acc": 129, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/Throwable;Z[Lnet/kyori/adventure/text/minimessage/internal/parser/Token;)V"}, {"nme": "getMessage", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "detailMessage", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "originalText", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "tokens", "acc": 1, "dsc": "()[Lnet/kyori/adventure/text/minimessage/internal/parser/Token;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "tokens", "acc": 1, "dsc": "([Lnet/kyori/adventure/text/minimessage/internal/parser/Token;)V"}, {"nme": "arrow", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "startIndex", "acc": 1, "dsc": "()I"}, {"nme": "endIndex", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 2507190809441787202}, {"acc": 18, "nme": "originalText", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "tokens", "dsc": "[Lnet/kyori/adventure/text/minimessage/internal/parser/Token;"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$Internal;"}]}, "net/kyori/adventure/text/minimessage/internal/parser/match/MatchedTokenConsumer.class": {"ver": 52, "acc": 1057, "nme": "net/kyori/adventure/text/minimessage/internal/parser/match/MatchedTokenConsumer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "accept", "acc": 1, "dsc": "(IILnet/kyori/adventure/text/minimessage/internal/parser/TokenType;)V", "invanns": [{"dsc": "Lorg/jetbrains/annotations/MustBeInvokedByOverriders;"}]}, {"nme": "result", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}, {"nme": "lastEndIndex", "acc": 17, "dsc": "()I"}], "flds": [{"acc": 20, "nme": "input", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "lastIndex", "dsc": "I"}]}, "net/kyori/adventure/text/minimessage/internal/serializer/StyleClaimingResolverImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/internal/serializer/StyleClaimingResolverImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/util/Set;Ljava/util/function/BiFunction;Lnet/kyori/adventure/text/minimessage/internal/serializer/StyleClaim;)V", "sig": "(Ljava/util/Set<Ljava/lang/String;>;Ljava/util/function/BiFunction<Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;Lnet/kyori/adventure/text/minimessage/tag/Tag;>;Lnet/kyori/adventure/text/minimessage/internal/serializer/StyleClaim<*>;)V"}, {"nme": "resolve", "acc": 1, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"], "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "has", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "claimStyle", "acc": 1, "dsc": "()Lnet/kyori/adventure/text/minimessage/internal/serializer/StyleClaim;", "sig": "()Lnet/kyori/adventure/text/minimessage/internal/serializer/StyleClaim<*>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}], "flds": [{"acc": 18, "nme": "names", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 18, "nme": "handler", "dsc": "Lja<PERSON>/util/function/BiFunction;", "sig": "Ljava/util/function/BiFunction<Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;Lnet/kyori/adventure/text/minimessage/tag/Tag;>;"}, {"acc": 18, "nme": "styleClaim", "dsc": "Lnet/kyori/adventure/text/minimessage/internal/serializer/StyleClaim;", "sig": "Lnet/kyori/adventure/text/minimessage/internal/serializer/StyleClaim<*>;"}]}, "net/kyori/adventure/text/minimessage/MiniMessageSerializer$Collector$TagState.class": {"ver": 52, "acc": 16432, "nme": "net/kyori/adventure/text/minimessage/MiniMessageSerializer$Collector$TagState", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lnet/kyori/adventure/text/minimessage/MiniMessageSerializer$Collector$TagState;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/MiniMessageSerializer$Collector$TagState;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;IZ)V", "sig": "(Z)V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Lnet/kyori/adventure/text/minimessage/MiniMessageSerializer$Collector$TagState;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "TEXT", "dsc": "Lnet/kyori/adventure/text/minimessage/MiniMessageSerializer$Collector$TagState;"}, {"acc": 16409, "nme": "MID", "dsc": "Lnet/kyori/adventure/text/minimessage/MiniMessageSerializer$Collector$TagState;"}, {"acc": 16409, "nme": "MID_SELF_CLOSING", "dsc": "Lnet/kyori/adventure/text/minimessage/MiniMessageSerializer$Collector$TagState;"}, {"acc": 16, "nme": "isTag", "dsc": "Z"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lnet/kyori/adventure/text/minimessage/MiniMessageSerializer$Collector$TagState;"}]}, "net/kyori/adventure/text/minimessage/tag/standard/NbtTag.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/standard/NbtTag", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "resolve", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"]}, {"nme": "emit", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/Component;)Lnet/kyori/adventure/text/minimessage/internal/serializer/Emitable;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "lambda$emit$0", "acc": 4106, "dsc": "(Lnet/kyori/adventure/text/Component;Ljava/lang/String;Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "NBT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "nbt"}, {"acc": 26, "nme": "DATA", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "data"}, {"acc": 26, "nme": "BLOCK", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "block"}, {"acc": 26, "nme": "ENTITY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "entity"}, {"acc": 26, "nme": "STORAGE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "storage"}, {"acc": 26, "nme": "INTERPRET", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "interpret"}, {"acc": 24, "nme": "RESOLVER", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}]}, "net/kyori/adventure/text/minimessage/tag/standard/ClickTag.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/standard/ClickTag", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "create", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"]}, {"nme": "lambda$create$1", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "(Lnet/kyori/adventure/text/event/ClickEvent;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CLICK", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "click"}, {"acc": 24, "nme": "RESOLVER", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}]}, "net/kyori/adventure/text/minimessage/internal/parser/match/package-info.class": {"ver": 52, "acc": 5632, "nme": "net/kyori/adventure/text/minimessage/internal/parser/match/package-info", "super": "java/lang/Object", "mthds": [], "flds": [], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$Internal;"}]}, "net/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Single.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/text/minimessage/tag/resolver/TagResolver$Single", "super": "java/lang/Object", "mthds": [{"nme": "key", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "tag", "acc": 1025, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/Tag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "resolve", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "has", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}], "flds": [], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$NonExtendable;"}]}, "net/kyori/adventure/text/minimessage/tree/Node$Root.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/text/minimessage/tree/Node$Root", "super": "java/lang/Object", "mthds": [{"nme": "input", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": [], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$NonExtendable;"}]}, "net/kyori/adventure/text/minimessage/internal/parser/TokenParser$1.class": {"ver": 52, "acc": 4128, "nme": "net/kyori/adventure/text/minimessage/internal/parser/TokenParser$1", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$net$kyori$adventure$text$minimessage$internal$parser$TokenParser$FirstPassState", "dsc": "[I"}, {"acc": 4120, "nme": "$SwitchMap$net$kyori$adventure$text$minimessage$internal$parser$TokenParser$SecondPassState", "dsc": "[I"}, {"acc": 4120, "nme": "$SwitchMap$net$kyori$adventure$text$minimessage$internal$parser$TokenType", "dsc": "[I"}]}, "net/kyori/adventure/text/minimessage/tag/resolver/EmptyTagResolver.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/resolver/EmptyTagResolver", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "resolve", "acc": 1, "dsc": "(Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "has", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "contributeToMap", "acc": 1, "dsc": "(Ljava/util/Map;)Z", "sig": "(Ljava/util/Map<Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/tag/Tag;>;)Z"}, {"nme": "handle", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/Component;Lnet/kyori/adventure/text/minimessage/internal/serializer/ClaimConsumer;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "INSTANCE", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/resolver/EmptyTagResolver;"}]}, "net/kyori/adventure/text/minimessage/tag/ParserDirective.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/text/minimessage/tag/ParserDirective", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "RESET", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/Tag;"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$NonExtendable;"}]}, "net/kyori/adventure/text/minimessage/tag/standard/ShadowColorTag.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/tag/standard/ShadowColorTag", "super": "java/lang/Object", "mthds": [{"nme": "create", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/resolver/ArgumentQueue;Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "exs": ["net/kyori/adventure/text/minimessage/ParsingException"]}, {"nme": "emit", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/format/ShadowColor;Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;)V"}, {"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "lambda$create$0", "acc": 4106, "dsc": "(Lnet/kyori/adventure/text/minimessage/Context;)Lnet/kyori/adventure/text/minimessage/ParsingException;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "SHADOW_COLOR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "shadow"}, {"acc": 26, "nme": "SHADOW_NONE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "!shadow"}, {"acc": 26, "nme": "DEFAULT_ALPHA", "dsc": "F", "val": 0.25}, {"acc": 24, "nme": "RESOLVER", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/resolver/TagResolver;"}]}, "net/kyori/adventure/text/minimessage/ArgumentQueueImpl.class": {"ver": 52, "acc": 48, "nme": "net/kyori/adventure/text/minimessage/ArgumentQueueImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lnet/kyori/adventure/text/minimessage/Context;Ljava/util/List;)V", "sig": "(Lnet/kyori/adventure/text/minimessage/Context;Ljava/util/List<TT;>;)V"}, {"nme": "pop", "acc": 1, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/Tag$Argument;", "sig": "()TT;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "popOr", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/tag/Tag$Argument;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;)TT;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "popOr", "acc": 1, "dsc": "(Ljava/util/function/Supplier;)Lnet/kyori/adventure/text/minimessage/tag/Tag$Argument;", "sig": "(Ljava/util/function/Supplier<Ljava/lang/String;>;)TT;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "peek", "acc": 1, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/Tag$Argument;", "sig": "()TT;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "hasNext", "acc": 1, "dsc": "()Z"}, {"nme": "reset", "acc": 1, "dsc": "()V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "context", "dsc": "Lnet/kyori/adventure/text/minimessage/Context;"}, {"acc": 16, "nme": "args", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<TT;>;"}, {"acc": 2, "nme": "ptr", "dsc": "I"}]}, "net/kyori/adventure/text/minimessage/internal/parser/TokenParser$FirstPassState.class": {"ver": 52, "acc": 16432, "nme": "net/kyori/adventure/text/minimessage/internal/parser/TokenParser$FirstPassState", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lnet/kyori/adventure/text/minimessage/internal/parser/TokenParser$FirstPassState;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/internal/parser/TokenParser$FirstPassState;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Lnet/kyori/adventure/text/minimessage/internal/parser/TokenParser$FirstPassState;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "NORMAL", "dsc": "Lnet/kyori/adventure/text/minimessage/internal/parser/TokenParser$FirstPassState;"}, {"acc": 16409, "nme": "TAG", "dsc": "Lnet/kyori/adventure/text/minimessage/internal/parser/TokenParser$FirstPassState;"}, {"acc": 16409, "nme": "STRING", "dsc": "Lnet/kyori/adventure/text/minimessage/internal/parser/TokenParser$FirstPassState;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lnet/kyori/adventure/text/minimessage/internal/parser/TokenParser$FirstPassState;"}]}, "net/kyori/adventure/text/minimessage/tag/Modifying.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/text/minimessage/tag/Modifying", "super": "java/lang/Object", "mthds": [{"nme": "visit", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/minimessage/tree/Node;I)V"}, {"nme": "postVisit", "acc": 1, "dsc": "()V"}, {"nme": "apply", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/Component;I)Lnet/kyori/adventure/text/Component;"}], "flds": [], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$OverrideOnly;"}]}, "net/kyori/adventure/text/minimessage/tag/Tag.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/text/minimessage/tag/Tag", "super": "java/lang/Object", "mthds": [{"nme": "preProcessParsed", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/tag/PreProcess;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "inserting", "acc": 9, "dsc": "(Lnet/kyori/adventure/text/Component;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "inserting", "acc": 9, "dsc": "(Lnet/kyori/adventure/text/ComponentLike;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "selfClosingInserting", "acc": 9, "dsc": "(Lnet/kyori/adventure/text/Component;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "selfClosingInserting", "acc": 9, "dsc": "(Lnet/kyori/adventure/text/ComponentLike;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "styling", "acc": 9, "dsc": "(Ljava/util/function/Consumer;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "sig": "(Ljava/util/function/Consumer<Lnet/kyori/adventure/text/format/Style$Builder;>;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "styling", "acc": 137, "dsc": "([Lnet/kyori/adventure/text/format/StyleBuilderApplicable;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": []}, "net/kyori/adventure/text/minimessage/internal/parser/node/TagNode.class": {"ver": 52, "acc": 49, "nme": "net/kyori/adventure/text/minimessage/internal/parser/node/TagNode", "super": "net/kyori/adventure/text/minimessage/internal/parser/node/ElementNode", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/minimessage/internal/parser/node/ElementNode;Lnet/kyori/adventure/text/minimessage/internal/parser/Token;Lja<PERSON>/lang/String;Lnet/kyori/adventure/text/minimessage/internal/parser/TokenParser$TagProvider;)V"}, {"nme": "gen<PERSON>arts", "acc": 10, "dsc": "(Lnet/kyori/adventure/text/minimessage/internal/parser/Token;Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/internal/parser/TokenParser$TagProvider;)Ljava/util/List;", "sig": "(Lnet/kyori/adventure/text/minimessage/internal/parser/Token;Ljava/lang/String;Lnet/kyori/adventure/text/minimessage/internal/parser/TokenParser$TagProvider;)Ljava/util/List<Lnet/kyori/adventure/text/minimessage/internal/parser/node/TagPart;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "parts", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lnet/kyori/adventure/text/minimessage/internal/parser/node/TagPart;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "name", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "token", "acc": 1, "dsc": "()Lnet/kyori/adventure/text/minimessage/internal/parser/Token;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "tag", "acc": 1, "dsc": "()Lnet/kyori/adventure/text/minimessage/tag/Tag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "tag", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/minimessage/tag/Tag;)V"}, {"nme": "buildToString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;I)Ljava/lang/StringBuilder;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": [{"acc": 18, "nme": "parts", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lnet/kyori/adventure/text/minimessage/internal/parser/node/TagPart;>;"}, {"acc": 2, "nme": "tag", "dsc": "Lnet/kyori/adventure/text/minimessage/tag/Tag;"}]}, "net/kyori/adventure/text/minimessage/internal/parser/TokenParser$TagProvider.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/text/minimessage/internal/parser/TokenParser$TagProvider", "super": "java/lang/Object", "mthds": [{"nme": "resolve", "acc": 1025, "dsc": "(Ljava/lang/String;Ljava/util/List;Lnet/kyori/adventure/text/minimessage/internal/parser/Token;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "sig": "(Ljava/lang/String;Ljava/util/List<+Lnet/kyori/adventure/text/minimessage/tag/Tag$Argument;>;Lnet/kyori/adventure/text/minimessage/internal/parser/Token;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "resolve", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "resolve", "acc": 1, "dsc": "(Lnet/kyori/adventure/text/minimessage/internal/parser/node/TagNode;)Lnet/kyori/adventure/text/minimessage/tag/Tag;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "sanitizePlaceholderName", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": [], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$Internal;"}]}, "net/kyori/adventure/text/minimessage/tag/standard/AbstractColorChangingTag.class": {"ver": 52, "acc": 1056, "nme": "net/kyori/adventure/text/minimessage/tag/standard/AbstractColorChangingTag", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lnet/kyori/adventure/text/minimessage/Context;)V"}, {"nme": "size", "acc": 20, "dsc": "()I"}, {"nme": "visit", "acc": 17, "dsc": "(Lnet/kyori/adventure/text/minimessage/tree/Node;I)V"}, {"nme": "postVisit", "acc": 17, "dsc": "()V"}, {"nme": "apply", "acc": 17, "dsc": "(Lnet/kyori/adventure/text/Component;I)Lnet/kyori/adventure/text/Component;"}, {"nme": "skipColorForLengthOf", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "init", "acc": 1028, "dsc": "()V"}, {"nme": "advanceColor", "acc": 1028, "dsc": "()V"}, {"nme": "color", "acc": 1028, "dsc": "()Lnet/kyori/adventure/text/format/TextColor;"}, {"nme": "preserveData", "acc": 1028, "dsc": "()Ljava/util/function/Consumer;", "sig": "()Ljava/util/function/Consumer<Lnet/kyori/adventure/text/minimessage/internal/serializer/TokenEmitter;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "examinableProperties", "acc": 1025, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<+Lnet/kyori/examination/ExaminableProperty;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "toString", "acc": 17, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "equals", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1025, "dsc": "()I"}, {"nme": "claimComponent", "acc": 8, "dsc": "(Lnet/kyori/adventure/text/Component;)Lnet/kyori/adventure/text/minimessage/internal/serializer/Emitable;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "lambda$visit$1", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "(Lnet/kyori/adventure/text/Component;)Ljava/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "LENGTH_CALCULATOR", "dsc": "Lnet/kyori/adventure/text/flattener/ComponentFlattener;"}, {"acc": 2, "nme": "visited", "dsc": "Z"}, {"acc": 2, "nme": "size", "dsc": "I"}, {"acc": 2, "nme": "disableApplyingColorDepth", "dsc": "I"}, {"acc": 18, "nme": "emitVirtuals", "dsc": "Z"}]}}}}