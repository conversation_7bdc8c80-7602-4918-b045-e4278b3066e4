{"md5": "98775b48cc132dadfb5a4222883872fa", "sha2": "2f5101d2751ba0e9b067ccd01a4de61359e0a602", "sha256": "e4775374b593caa9342155909f51332be2553d97a58ff1978af027c838435f49", "contents": {"classes": {"classes/javax/management/remote/rmi/RMIConnection.class": {"ver": 68, "acc": 1537, "nme": "javax/management/remote/rmi/RMIConnection", "super": "java/lang/Object", "mthds": [{"nme": "getConnectionId", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "close", "acc": 1025, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "createMBean", "acc": 1025, "dsc": "(Ljava/lang/String;Ljavax/management/ObjectName;Ljavax/security/auth/Subject;)Ljavax/management/ObjectInstance;", "exs": ["javax/management/ReflectionException", "javax/management/InstanceAlreadyExistsException", "javax/management/MBeanRegistrationException", "javax/management/MBeanException", "javax/management/NotCompliantMBeanException", "java/io/IOException"]}, {"nme": "createMBean", "acc": 1025, "dsc": "(Ljava/lang/String;Ljavax/management/ObjectName;Ljavax/management/ObjectName;Ljavax/security/auth/Subject;)Ljavax/management/ObjectInstance;", "exs": ["javax/management/ReflectionException", "javax/management/InstanceAlreadyExistsException", "javax/management/MBeanRegistrationException", "javax/management/MBeanException", "javax/management/NotCompliantMBeanException", "javax/management/InstanceNotFoundException", "java/io/IOException"]}, {"nme": "createMBean", "acc": 1025, "dsc": "(Ljava/lang/String;Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject;[Ljava/lang/String;Ljavax/security/auth/Subject;)Ljavax/management/ObjectInstance;", "exs": ["javax/management/ReflectionException", "javax/management/InstanceAlreadyExistsException", "javax/management/MBeanRegistrationException", "javax/management/MBeanException", "javax/management/NotCompliantMBeanException", "java/io/IOException"]}, {"nme": "createMBean", "acc": 1025, "dsc": "(Ljava/lang/String;Ljavax/management/ObjectName;Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject;[Ljava/lang/String;Ljavax/security/auth/Subject;)Ljavax/management/ObjectInstance;", "exs": ["javax/management/ReflectionException", "javax/management/InstanceAlreadyExistsException", "javax/management/MBeanRegistrationException", "javax/management/MBeanException", "javax/management/NotCompliantMBeanException", "javax/management/InstanceNotFoundException", "java/io/IOException"]}, {"nme": "unregisterMBean", "acc": 1025, "dsc": "(Ljavax/management/ObjectName;Ljavax/security/auth/Subject;)V", "exs": ["javax/management/InstanceNotFoundException", "javax/management/MBeanRegistrationException", "java/io/IOException"]}, {"nme": "getObjectInstance", "acc": 1025, "dsc": "(Ljavax/management/ObjectName;Ljavax/security/auth/Subject;)Ljavax/management/ObjectInstance;", "exs": ["javax/management/InstanceNotFoundException", "java/io/IOException"]}, {"nme": "queryMBeans", "acc": 1025, "dsc": "(Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject;Ljavax/security/auth/Subject;)Ljava/util/Set;", "sig": "(Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject;Ljavax/security/auth/Subject;)Ljava/util/Set<Ljavax/management/ObjectInstance;>;", "exs": ["java/io/IOException"]}, {"nme": "queryNames", "acc": 1025, "dsc": "(Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject;Ljavax/security/auth/Subject;)Ljava/util/Set;", "sig": "(Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject;Ljavax/security/auth/Subject;)Ljava/util/Set<Ljavax/management/ObjectName;>;", "exs": ["java/io/IOException"]}, {"nme": "isRegistered", "acc": 1025, "dsc": "(Ljavax/management/ObjectName;Ljavax/security/auth/Subject;)Z", "exs": ["java/io/IOException"]}, {"nme": "getMBeanCount", "acc": 1025, "dsc": "(Ljavax/security/auth/Subject;)Ljava/lang/Integer;", "exs": ["java/io/IOException"]}, {"nme": "getAttribute", "acc": 1025, "dsc": "(Ljavax/management/ObjectName;Ljava/lang/String;Ljavax/security/auth/Subject;)Ljava/lang/Object;", "exs": ["javax/management/MBeanException", "javax/management/AttributeNotFoundException", "javax/management/InstanceNotFoundException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "getAttributes", "acc": 1025, "dsc": "(Ljavax/management/ObjectName;[Ljava/lang/String;Ljavax/security/auth/Subject;)Ljavax/management/AttributeList;", "exs": ["javax/management/InstanceNotFoundException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "setAttribute", "acc": 1025, "dsc": "(Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject;Ljavax/security/auth/Subject;)V", "exs": ["javax/management/InstanceNotFoundException", "javax/management/AttributeNotFoundException", "javax/management/InvalidAttributeValueException", "javax/management/MBeanException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "setAttributes", "acc": 1025, "dsc": "(Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject;Ljavax/security/auth/Subject;)Ljavax/management/AttributeList;", "exs": ["javax/management/InstanceNotFoundException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "invoke", "acc": 1025, "dsc": "(Ljavax/management/ObjectName;Ljava/lang/String;Ljava/rmi/MarshalledObject;[Ljava/lang/String;Ljavax/security/auth/Subject;)Ljava/lang/Object;", "exs": ["javax/management/InstanceNotFoundException", "javax/management/MBeanException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "getDefaultDomain", "acc": 1025, "dsc": "(Ljavax/security/auth/Subject;)Ljava/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "getDomains", "acc": 1025, "dsc": "(Ljavax/security/auth/Subject;)[Ljava/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "getMBeanInfo", "acc": 1025, "dsc": "(Ljavax/management/ObjectName;Ljavax/security/auth/Subject;)Ljavax/management/MBeanInfo;", "exs": ["javax/management/InstanceNotFoundException", "javax/management/IntrospectionException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "isInstanceOf", "acc": 1025, "dsc": "(Ljavax/management/ObjectName;Ljava/lang/String;Ljavax/security/auth/Subject;)Z", "exs": ["javax/management/InstanceNotFoundException", "java/io/IOException"]}, {"nme": "addNotificationListener", "acc": 1025, "dsc": "(Ljavax/management/ObjectName;Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject;Ljava/rmi/MarshalledObject;Ljavax/security/auth/Subject;)V", "exs": ["javax/management/InstanceNotFoundException", "java/io/IOException"]}, {"nme": "removeNotificationListener", "acc": 1025, "dsc": "(Ljavax/management/ObjectName;Ljavax/management/ObjectName;Ljavax/security/auth/Subject;)V", "exs": ["javax/management/InstanceNotFoundException", "javax/management/ListenerNotFoundException", "java/io/IOException"]}, {"nme": "removeNotificationListener", "acc": 1025, "dsc": "(Ljavax/management/ObjectName;Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject;Ljava/rmi/MarshalledObject;Ljavax/security/auth/Subject;)V", "exs": ["javax/management/InstanceNotFoundException", "javax/management/ListenerNotFoundException", "java/io/IOException"]}, {"nme": "addNotificationListeners", "acc": 1025, "dsc": "([Ljavax/management/ObjectName;[Ljava/rmi/MarshalledObject;[Ljavax/security/auth/Subject;)[Ljava/lang/Integer;", "exs": ["javax/management/InstanceNotFoundException", "java/io/IOException"]}, {"nme": "removeNotificationListeners", "acc": 1025, "dsc": "(Ljavax/management/ObjectName;[Ljava/lang/Integer;Ljavax/security/auth/Subject;)V", "exs": ["javax/management/InstanceNotFoundException", "javax/management/ListenerNotFoundException", "java/io/IOException"]}, {"nme": "fetchNotifications", "acc": 1025, "dsc": "(JIJ)Ljavax/management/remote/NotificationResult;", "exs": ["java/io/IOException"]}], "flds": []}, "classes/com/sun/jmx/remote/protocol/rmi/ServerProvider.class": {"ver": 68, "acc": 33, "nme": "com/sun/jmx/remote/protocol/rmi/ServerProvider", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "newJMXConnectorServer", "acc": 1, "dsc": "(Ljavax/management/remote/JMXServiceURL;Ljava/util/Map;Ljavax/management/MBeanServer;)Ljavax/management/remote/JMXConnectorServer;", "sig": "(Ljavax/management/remote/JMXServiceURL;Ljava/util/Map<Ljava/lang/String;*>;Ljavax/management/MBeanServer;)Ljavax/management/remote/JMXConnectorServer;", "exs": ["java/io/IOException"]}], "flds": []}, "classes/javax/management/remote/rmi/RMIServerImpl_Stub.class": {"ver": 68, "acc": 49, "nme": "javax/management/remote/rmi/RMIServerImpl_Stub", "super": "java/rmi/server/RemoteStub", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/rmi/server/RemoteRef;)V"}, {"nme": "getVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/rmi/RemoteException"]}, {"nme": "newClient", "acc": 1, "dsc": "(Ljava/lang/Object;)Ljavax/management/remote/rmi/RMIConnection;", "exs": ["java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 2}, {"acc": 10, "nme": "$method_getVersion_0", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 10, "nme": "$method_newClient_1", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}]}, "classes/javax/management/remote/rmi/RMIConnectionImpl.class": {"ver": 68, "acc": 33, "nme": "javax/management/remote/rmi/RMIConnectionImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/management/remote/rmi/RMIServerImpl;Ljava/lang/String;Ljava/lang/ClassLoader;Ljavax/security/auth/Subject;Ljava/util/Map;)V", "sig": "(Ljavax/management/remote/rmi/RMIServerImpl;Ljava/lang/String;Ljava/lang/ClassLoader;Ljavax/security/auth/Subject;Ljava/util/Map<Ljava/lang/String;*>;)V"}, {"nme": "getServerNotifFwd", "acc": 34, "dsc": "()Lcom/sun/jmx/remote/internal/ServerNotifForwarder;"}, {"nme": "getConnectionId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "unreferenced", "acc": 1, "dsc": "()V"}, {"nme": "createMBean", "acc": 1, "dsc": "(Ljava/lang/String;Ljavax/management/ObjectName;Ljavax/security/auth/Subject;)Ljavax/management/ObjectInstance;", "exs": ["javax/management/ReflectionException", "javax/management/InstanceAlreadyExistsException", "javax/management/MBeanRegistrationException", "javax/management/MBeanException", "javax/management/NotCompliantMBeanException", "java/io/IOException"]}, {"nme": "createMBean", "acc": 1, "dsc": "(Ljava/lang/String;Ljavax/management/ObjectName;Ljavax/management/ObjectName;Ljavax/security/auth/Subject;)Ljavax/management/ObjectInstance;", "exs": ["javax/management/ReflectionException", "javax/management/InstanceAlreadyExistsException", "javax/management/MBeanRegistrationException", "javax/management/MBeanException", "javax/management/NotCompliantMBeanException", "javax/management/InstanceNotFoundException", "java/io/IOException"]}, {"nme": "createMBean", "acc": 1, "dsc": "(Ljava/lang/String;Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject;[Ljava/lang/String;Ljavax/security/auth/Subject;)Ljavax/management/ObjectInstance;", "exs": ["javax/management/ReflectionException", "javax/management/InstanceAlreadyExistsException", "javax/management/MBeanRegistrationException", "javax/management/MBeanException", "javax/management/NotCompliantMBeanException", "java/io/IOException"]}, {"nme": "createMBean", "acc": 1, "dsc": "(Ljava/lang/String;Ljavax/management/ObjectName;Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject;[Ljava/lang/String;Ljavax/security/auth/Subject;)Ljavax/management/ObjectInstance;", "exs": ["javax/management/ReflectionException", "javax/management/InstanceAlreadyExistsException", "javax/management/MBeanRegistrationException", "javax/management/MBeanException", "javax/management/NotCompliantMBeanException", "javax/management/InstanceNotFoundException", "java/io/IOException"]}, {"nme": "unregisterMBean", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljavax/security/auth/Subject;)V", "exs": ["javax/management/InstanceNotFoundException", "javax/management/MBeanRegistrationException", "java/io/IOException"]}, {"nme": "getObjectInstance", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljavax/security/auth/Subject;)Ljavax/management/ObjectInstance;", "exs": ["javax/management/InstanceNotFoundException", "java/io/IOException"]}, {"nme": "queryMBeans", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject;Ljavax/security/auth/Subject;)Ljava/util/Set;", "sig": "(Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject;Ljavax/security/auth/Subject;)Ljava/util/Set<Ljavax/management/ObjectInstance;>;", "exs": ["java/io/IOException"]}, {"nme": "queryNames", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject;Ljavax/security/auth/Subject;)Ljava/util/Set;", "sig": "(Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject;Ljavax/security/auth/Subject;)Ljava/util/Set<Ljavax/management/ObjectName;>;", "exs": ["java/io/IOException"]}, {"nme": "isRegistered", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljavax/security/auth/Subject;)Z", "exs": ["java/io/IOException"]}, {"nme": "getMBeanCount", "acc": 1, "dsc": "(Ljavax/security/auth/Subject;)Ljava/lang/Integer;", "exs": ["java/io/IOException"]}, {"nme": "getAttribute", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljava/lang/String;Ljavax/security/auth/Subject;)Ljava/lang/Object;", "exs": ["javax/management/MBeanException", "javax/management/AttributeNotFoundException", "javax/management/InstanceNotFoundException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "getAttributes", "acc": 1, "dsc": "(Ljavax/management/ObjectName;[Ljava/lang/String;Ljavax/security/auth/Subject;)Ljavax/management/AttributeList;", "exs": ["javax/management/InstanceNotFoundException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "setAttribute", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject;Ljavax/security/auth/Subject;)V", "exs": ["javax/management/InstanceNotFoundException", "javax/management/AttributeNotFoundException", "javax/management/InvalidAttributeValueException", "javax/management/MBeanException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "setAttributes", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject;Ljavax/security/auth/Subject;)Ljavax/management/AttributeList;", "exs": ["javax/management/InstanceNotFoundException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "invoke", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljava/lang/String;Ljava/rmi/MarshalledObject;[Ljava/lang/String;Ljavax/security/auth/Subject;)Ljava/lang/Object;", "exs": ["javax/management/InstanceNotFoundException", "javax/management/MBeanException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "getDefaultDomain", "acc": 1, "dsc": "(Ljavax/security/auth/Subject;)Ljava/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "getDomains", "acc": 1, "dsc": "(Ljavax/security/auth/Subject;)[Ljava/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "getMBeanInfo", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljavax/security/auth/Subject;)Ljavax/management/MBeanInfo;", "exs": ["javax/management/InstanceNotFoundException", "javax/management/IntrospectionException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "isInstanceOf", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljava/lang/String;Ljavax/security/auth/Subject;)Z", "exs": ["javax/management/InstanceNotFoundException", "java/io/IOException"]}, {"nme": "addNotificationListeners", "acc": 1, "dsc": "([Ljavax/management/ObjectName;[Ljava/rmi/MarshalledObject;[Ljavax/security/auth/Subject;)[Ljava/lang/Integer;", "exs": ["javax/management/InstanceNotFoundException", "java/io/IOException"]}, {"nme": "addNotificationListener", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject;Ljava/rmi/MarshalledObject;Ljavax/security/auth/Subject;)V", "exs": ["javax/management/InstanceNotFoundException", "java/io/IOException"]}, {"nme": "removeNotificationListeners", "acc": 1, "dsc": "(Ljavax/management/ObjectName;[Ljava/lang/Integer;Ljavax/security/auth/Subject;)V", "exs": ["javax/management/InstanceNotFoundException", "javax/management/ListenerNotFoundException", "java/io/IOException"]}, {"nme": "removeNotificationListener", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljavax/management/ObjectName;Ljavax/security/auth/Subject;)V", "exs": ["javax/management/InstanceNotFoundException", "javax/management/ListenerNotFoundException", "java/io/IOException"]}, {"nme": "removeNotificationListener", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject;Ljava/rmi/MarshalledObject;Ljavax/security/auth/Subject;)V", "exs": ["javax/management/InstanceNotFoundException", "javax/management/ListenerNotFoundException", "java/io/IOException"]}, {"nme": "fetchNotifications", "acc": 1, "dsc": "(JIJ)Ljavax/management/remote/NotificationResult;", "exs": ["java/io/IOException"]}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getClassLoaderFor", "acc": 2, "dsc": "(Ljavax/management/ObjectName;)Ljava/lang/ClassLoader;", "exs": ["javax/management/InstanceNotFoundException"]}, {"nme": "doPrivilegedOperation", "acc": 2, "dsc": "(I[<PERSON><PERSON><PERSON>/lang/Object;Ljavax/security/auth/Subject;)Ljava/lang/Object;", "exs": ["java/security/PrivilegedActionException", "java/io/IOException"]}, {"nme": "doOperation", "acc": 2, "dsc": "(I[<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}, {"nme": "setCcl", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;)<PERSON>ja<PERSON>/lang/ClassLoader;"}, {"nme": "unwrap", "acc": 2, "dsc": "(Ljava/rmi/MarshalledObject;Ljava/lang/ClassLoader;Ljava/lang/Class;Ljavax/security/auth/Subject;)Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/rmi/MarshalledObject<*>;Ljava/lang/ClassLoader;Ljava/lang/Class<TT;>;Ljavax/security/auth/Subject;)TT;", "exs": ["java/io/IOException"]}, {"nme": "unwrap", "acc": 2, "dsc": "(<PERSON>java/rmi/MarshalledObject;Ljava/lang/ClassLoader;Ljava/lang/ClassLoader;Ljava/lang/Class;Ljavax/security/auth/Subject;)Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/rmi/MarshalledObject<*>;Ljava/lang/ClassLoader;Ljava/lang/ClassLoader;Ljava/lang/Class<TT;>;Ljavax/security/auth/Subject;)TT;", "exs": ["java/io/IOException"]}, {"nme": "newIOException", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)Ljava/io/IOException;"}, {"nme": "extractException", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Exception;)<PERSON><PERSON><PERSON>/lang/Exception;"}, {"nme": "nullIsEmpty", "acc": 10, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;)[<PERSON>ja<PERSON>/lang/Object;"}, {"nme": "nullIsEmpty", "acc": 10, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)[Ljava/lang/String;"}, {"nme": "checkNonNull", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "objects", "acc": 10, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;)<PERSON>ja<PERSON>/lang/String;"}, {"nme": "strings", "acc": 10, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "lambda$unwrap$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;L<PERSON><PERSON>/rmi/MarshalledObject;)Ljava/lang/Object;", "exs": ["java/lang/Exception"]}, {"nme": "lambda$doPrivilegedOperation$0", "acc": 4098, "dsc": "(I[<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}, {"nme": "lambda$fetchNotifications$0", "acc": 4098, "dsc": "(JJI)Ljavax/management/remote/NotificationResult;", "exs": ["java/lang/Exception"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "NO_OBJECTS", "dsc": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 26, "nme": "NO_STRINGS", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "subject", "dsc": "Ljavax/security/auth/Subject;"}, {"acc": 18, "nme": "rmiServer", "dsc": "Ljavax/management/remote/rmi/RMIServerImpl;"}, {"acc": 18, "nme": "mbeanServer", "dsc": "Ljavax/management/MBeanServer;"}, {"acc": 18, "nme": "defaultClassLoader", "dsc": "<PERSON><PERSON><PERSON>/lang/ClassLoader;"}, {"acc": 18, "nme": "defaultContextClassLoader", "dsc": "<PERSON><PERSON><PERSON>/lang/ClassLoader;"}, {"acc": 18, "nme": "classLoaderWithRepository", "dsc": "Lcom/sun/jmx/remote/util/ClassLoaderWithRepository;"}, {"acc": 2, "nme": "terminated", "dsc": "Z"}, {"acc": 18, "nme": "connectionId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "serverCommunicatorAdmin", "dsc": "Lcom/sun/jmx/remote/internal/ServerCommunicatorAdmin;"}, {"acc": 26, "nme": "ADD_NOTIFICATION_LISTENERS", "dsc": "I", "val": 1}, {"acc": 26, "nme": "ADD_NOTIFICATION_LISTENER_OBJECTNAME", "dsc": "I", "val": 2}, {"acc": 26, "nme": "CREATE_MBEAN", "dsc": "I", "val": 3}, {"acc": 26, "nme": "CREATE_MBEAN_PARAMS", "dsc": "I", "val": 4}, {"acc": 26, "nme": "CREATE_MBEAN_LOADER", "dsc": "I", "val": 5}, {"acc": 26, "nme": "CREATE_MBEAN_LOADER_PARAMS", "dsc": "I", "val": 6}, {"acc": 26, "nme": "GET_ATTRIBUTE", "dsc": "I", "val": 7}, {"acc": 26, "nme": "GET_ATTRIBUTES", "dsc": "I", "val": 8}, {"acc": 26, "nme": "GET_DEFAULT_DOMAIN", "dsc": "I", "val": 9}, {"acc": 26, "nme": "GET_DOMAINS", "dsc": "I", "val": 10}, {"acc": 26, "nme": "GET_MBEAN_COUNT", "dsc": "I", "val": 11}, {"acc": 26, "nme": "GET_MBEAN_INFO", "dsc": "I", "val": 12}, {"acc": 26, "nme": "GET_OBJECT_INSTANCE", "dsc": "I", "val": 13}, {"acc": 26, "nme": "INVOKE", "dsc": "I", "val": 14}, {"acc": 26, "nme": "IS_INSTANCE_OF", "dsc": "I", "val": 15}, {"acc": 26, "nme": "IS_REGISTERED", "dsc": "I", "val": 16}, {"acc": 26, "nme": "QUERY_MBEANS", "dsc": "I", "val": 17}, {"acc": 26, "nme": "QUERY_NAMES", "dsc": "I", "val": 18}, {"acc": 26, "nme": "REMOVE_NOTIFICATION_LISTENER", "dsc": "I", "val": 19}, {"acc": 26, "nme": "REMOVE_NOTIFICATION_LISTENER_OBJECTNAME", "dsc": "I", "val": 20}, {"acc": 26, "nme": "REMOVE_NOTIFICATION_LISTENER_OBJECTNAME_FILTER_HANDBACK", "dsc": "I", "val": 21}, {"acc": 26, "nme": "SET_ATTRIBUTE", "dsc": "I", "val": 22}, {"acc": 26, "nme": "SET_ATTRIBUTES", "dsc": "I", "val": 23}, {"acc": 26, "nme": "UNREGISTER_MBEAN", "dsc": "I", "val": 24}, {"acc": 2, "nme": "serverNotifForwarder", "dsc": "Lcom/sun/jmx/remote/internal/ServerNotifForwarder;"}, {"acc": 2, "nme": "env", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;*>;"}, {"acc": 26, "nme": "logger", "dsc": "Lcom/sun/jmx/remote/util/ClassLogger;"}]}, "classes/javax/management/remote/rmi/RMIConnector$RMINotifClient.class": {"ver": 68, "acc": 32, "nme": "javax/management/remote/rmi/RMIConnector$RMINotifClient", "super": "com/sun/jmx/remote/internal/ClientNotifForwarder", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/management/remote/rmi/RMIConnector;Ljava/lang/ClassLoader;Ljava/util/Map;)V", "sig": "(Lja<PERSON>/lang/ClassLoader;Ljava/util/Map<Ljava/lang/String;*>;)V"}, {"nme": "fetchNotifs", "acc": 4, "dsc": "(JIJ)Ljavax/management/remote/NotificationResult;", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}, {"nme": "rethrowDeserializationException", "acc": 2, "dsc": "(<PERSON>java/io/IOException;)V", "exs": ["java/lang/ClassNotFoundException", "java/io/IOException"]}, {"nme": "addListenerForMBeanRemovedNotif", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/lang/Integer;", "exs": ["java/io/IOException", "javax/management/InstanceNotFoundException"]}, {"nme": "removeListenerForMBeanRemovedNotif", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Integer;)V", "exs": ["java/io/IOException", "javax/management/InstanceNotFoundException", "javax/management/ListenerNotFoundException"]}, {"nme": "lostNotifs", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;J)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Ljavax/management/remote/rmi/RMIConnector;"}]}, "classes/com/sun/jmx/remote/internal/rmi/ProxyRef.class": {"ver": 68, "acc": 33, "nme": "com/sun/jmx/remote/internal/rmi/ProxyRef", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/rmi/server/RemoteRef;)V"}, {"nme": "readExternal", "acc": 1, "dsc": "(Ljava/io/ObjectInput;)V", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}, {"nme": "writeExternal", "acc": 1, "dsc": "(Ljava/io/ObjectOutput;)V", "exs": ["java/io/IOException"]}, {"nme": "invoke", "acc": 131073, "dsc": "(Ljava/rmi/server/RemoteCall;)V", "exs": ["java/lang/Exception"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "invoke", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/rmi/Remote;<PERSON><PERSON><PERSON>/lang/reflect/Method;[Ljava/lang/Object;J)Ljava/lang/Object;", "exs": ["java/lang/Exception"]}, {"nme": "done", "acc": 131073, "dsc": "(Ljava/rmi/server/RemoteCall;)V", "exs": ["java/rmi/RemoteException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getRefClass", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/ObjectOutput;)Ljava/lang/String;"}, {"nme": "newCall", "acc": 131073, "dsc": "(Ljava/rmi/server/RemoteObject;[Ljava/rmi/server/Operation;IJ)Ljava/rmi/server/RemoteCall;", "exs": ["java/rmi/RemoteException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "remoteEquals", "acc": 1, "dsc": "(Ljava/rmi/server/RemoteRef;)Z"}, {"nme": "remoteHashCode", "acc": 1, "dsc": "()I"}, {"nme": "remoteToString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -6503061366316814723}, {"acc": 4, "nme": "ref", "dsc": "Ljava/rmi/server/RemoteRef;"}]}, "classes/javax/management/remote/rmi/RMIConnectionImpl$CombinedClassLoader$ClassLoaderWrapper.class": {"ver": 68, "acc": 48, "nme": "javax/management/remote/rmi/RMIConnectionImpl$CombinedClassLoader$ClassLoaderWrapper", "super": "java/lang/ClassLoader", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;)V"}, {"nme": "loadClass", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)Ljava/lang/Class;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;Z)Ljava/lang/Class<*>;", "exs": ["java/lang/ClassNotFoundException"]}], "flds": []}, "classes/javax/management/remote/rmi/RMIConnector$RemoteMBeanServerConnection.class": {"ver": 68, "acc": 32, "nme": "javax/management/remote/rmi/RMIConnector$RemoteMBeanServerConnection", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/management/remote/rmi/RMIConnector;)V"}, {"nme": "createMBean", "acc": 1, "dsc": "(Ljava/lang/String;Ljavax/management/ObjectName;)Ljavax/management/ObjectInstance;", "exs": ["javax/management/ReflectionException", "javax/management/InstanceAlreadyExistsException", "javax/management/MBeanRegistrationException", "javax/management/MBeanException", "javax/management/NotCompliantMBeanException", "java/io/IOException"]}, {"nme": "createMBean", "acc": 1, "dsc": "(Ljava/lang/String;Ljavax/management/ObjectName;Ljavax/management/ObjectName;)Ljavax/management/ObjectInstance;", "exs": ["javax/management/ReflectionException", "javax/management/InstanceAlreadyExistsException", "javax/management/MBeanRegistrationException", "javax/management/MBeanException", "javax/management/NotCompliantMBeanException", "javax/management/InstanceNotFoundException", "java/io/IOException"]}, {"nme": "createMBean", "acc": 1, "dsc": "(Ljava/lang/String;Ljavax/management/ObjectName;[Ljava/lang/Object;[Ljava/lang/String;)Ljavax/management/ObjectInstance;", "exs": ["javax/management/ReflectionException", "javax/management/InstanceAlreadyExistsException", "javax/management/MBeanRegistrationException", "javax/management/MBeanException", "javax/management/NotCompliantMBeanException", "java/io/IOException"]}, {"nme": "createMBean", "acc": 1, "dsc": "(Ljava/lang/String;Ljavax/management/ObjectName;Ljavax/management/ObjectName;[Ljava/lang/Object;[Ljava/lang/String;)Ljavax/management/ObjectInstance;", "exs": ["javax/management/ReflectionException", "javax/management/InstanceAlreadyExistsException", "javax/management/MBeanRegistrationException", "javax/management/MBeanException", "javax/management/NotCompliantMBeanException", "javax/management/InstanceNotFoundException", "java/io/IOException"]}, {"nme": "unregisterMBean", "acc": 1, "dsc": "(Ljavax/management/ObjectName;)V", "exs": ["javax/management/InstanceNotFoundException", "javax/management/MBeanRegistrationException", "java/io/IOException"]}, {"nme": "getObjectInstance", "acc": 1, "dsc": "(Ljavax/management/ObjectName;)Ljavax/management/ObjectInstance;", "exs": ["javax/management/InstanceNotFoundException", "java/io/IOException"]}, {"nme": "queryMBeans", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljavax/management/QueryExp;)Ljava/util/Set;", "sig": "(Ljavax/management/ObjectName;Ljavax/management/QueryExp;)Ljava/util/Set<Ljavax/management/ObjectInstance;>;", "exs": ["java/io/IOException"]}, {"nme": "queryNames", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljavax/management/QueryExp;)Ljava/util/Set;", "sig": "(Ljavax/management/ObjectName;Ljavax/management/QueryExp;)Ljava/util/Set<Ljavax/management/ObjectName;>;", "exs": ["java/io/IOException"]}, {"nme": "isRegistered", "acc": 1, "dsc": "(Ljavax/management/ObjectName;)Z", "exs": ["java/io/IOException"]}, {"nme": "getMBeanCount", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Integer;", "exs": ["java/io/IOException"]}, {"nme": "getAttribute", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljava/lang/String;)Ljava/lang/Object;", "exs": ["javax/management/MBeanException", "javax/management/AttributeNotFoundException", "javax/management/InstanceNotFoundException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "getAttributes", "acc": 1, "dsc": "(Ljavax/management/ObjectName;[Ljava/lang/String;)Ljavax/management/AttributeList;", "exs": ["javax/management/InstanceNotFoundException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "setAttribute", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljavax/management/Attribute;)V", "exs": ["javax/management/InstanceNotFoundException", "javax/management/AttributeNotFoundException", "javax/management/InvalidAttributeValueException", "javax/management/MBeanException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "setAttributes", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljavax/management/AttributeList;)Ljavax/management/AttributeList;", "exs": ["javax/management/InstanceNotFoundException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "invoke", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljava/lang/String;[Ljava/lang/Object;[Ljava/lang/String;)Ljava/lang/Object;", "exs": ["javax/management/InstanceNotFoundException", "javax/management/MBeanException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "getDefaultDomain", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "getDomains", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "getMBeanInfo", "acc": 1, "dsc": "(Ljavax/management/ObjectName;)Ljavax/management/MBeanInfo;", "exs": ["javax/management/InstanceNotFoundException", "javax/management/IntrospectionException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "isInstanceOf", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljava/lang/String;)Z", "exs": ["javax/management/InstanceNotFoundException", "java/io/IOException"]}, {"nme": "addNotificationListener", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljavax/management/ObjectName;Ljavax/management/NotificationFilter;Ljava/lang/Object;)V", "exs": ["javax/management/InstanceNotFoundException", "java/io/IOException"]}, {"nme": "removeNotificationListener", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljavax/management/ObjectName;)V", "exs": ["javax/management/InstanceNotFoundException", "javax/management/ListenerNotFoundException", "java/io/IOException"]}, {"nme": "removeNotificationListener", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljavax/management/ObjectName;Ljavax/management/NotificationFilter;Ljava/lang/Object;)V", "exs": ["javax/management/InstanceNotFoundException", "javax/management/ListenerNotFoundException", "java/io/IOException"]}, {"nme": "addNotificationListener", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljavax/management/NotificationListener;Ljavax/management/NotificationFilter;Ljava/lang/Object;)V", "exs": ["javax/management/InstanceNotFoundException", "java/io/IOException"]}, {"nme": "removeNotificationListener", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljavax/management/NotificationListener;)V", "exs": ["javax/management/InstanceNotFoundException", "javax/management/ListenerNotFoundException", "java/io/IOException"]}, {"nme": "removeNotificationListener", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljavax/management/NotificationListener;Ljavax/management/NotificationFilter;Ljava/lang/Object;)V", "exs": ["javax/management/InstanceNotFoundException", "javax/management/ListenerNotFoundException", "java/io/IOException"]}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Ljavax/management/remote/rmi/RMIConnector;"}]}, "classes/javax/management/remote/rmi/NoCallStackClassLoader.class": {"ver": 68, "acc": 32, "nme": "javax/management/remote/rmi/NoCallStackClassLoader", "super": "java/lang/ClassLoader", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[B[Ljava/lang/String;Ljava/lang/ClassLoader;Ljava/security/ProtectionDomain;)V"}, {"nme": "<init>", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;[[B[Ljava/lang/String;Ljava/lang/ClassLoader;Ljava/security/ProtectionDomain;)V"}, {"nme": "findClass", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class<*>;", "exs": ["java/lang/ClassNotFoundException"]}, {"nme": "stringToBytes", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[B"}], "flds": [{"acc": 18, "nme": "classNames", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "byteCodes", "dsc": "[[B"}, {"acc": 18, "nme": "referencedClassNames", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "referencedClassLoader", "dsc": "<PERSON><PERSON><PERSON>/lang/ClassLoader;"}, {"acc": 18, "nme": "protectionDomain", "dsc": "Ljava/security/ProtectionDomain;"}]}, "classes/javax/management/remote/rmi/RMIConnectorServer.class": {"ver": 68, "acc": 33, "nme": "javax/management/remote/rmi/RMIConnectorServer", "super": "javax/management/remote/JMXConnectorServer", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/management/remote/JMXServiceURL;Ljava/util/Map;)V", "sig": "(Ljavax/management/remote/JMXServiceURL;Ljava/util/Map<Ljava/lang/String;*>;)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 1, "dsc": "(Ljavax/management/remote/JMXServiceURL;Ljava/util/Map;Ljavax/management/MBeanServer;)V", "sig": "(Ljavax/management/remote/JMXServiceURL;Ljava/util/Map<Ljava/lang/String;*>;Ljavax/management/MBeanServer;)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 1, "dsc": "(Ljavax/management/remote/JMXServiceURL;Ljava/util/Map;Ljavax/management/remote/rmi/RMIServerImpl;Ljavax/management/MBeanServer;)V", "sig": "(Ljavax/management/remote/JMXServiceURL;Ljava/util/Map<Ljava/lang/String;*>;Ljavax/management/remote/rmi/RMIServerImpl;Ljavax/management/MBeanServer;)V", "exs": ["java/io/IOException"]}, {"nme": "toJMXConnector", "acc": 1, "dsc": "(Ljava/util/Map;)Ljavax/management/remote/JMXConnector;", "sig": "(Ljava/util/Map<Ljava/lang/String;*>;)Ljavax/management/remote/JMXConnector;", "exs": ["java/io/IOException"]}, {"nme": "start", "acc": 33, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "stop", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "isActive", "acc": 33, "dsc": "()Z"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Ljavax/management/remote/JMXServiceURL;"}, {"nme": "getAttributes", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;*>;"}, {"nme": "setMBeanServerForwarder", "acc": 33, "dsc": "(Ljavax/management/remote/MBeanServerForwarder;)V"}, {"nme": "connectionOpened", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "connectionClosed", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "connectionFailed", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "bind", "acc": 0, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/Hashtable;Ljavax/management/remote/rmi/RMIServer;Z)V", "sig": "(Lja<PERSON>/lang/String;Ljava/util/Hashtable<**>;Ljavax/management/remote/rmi/RMIServer;Z)V", "exs": ["javax/naming/NamingException", "java/net/MalformedURLException"]}, {"nme": "newServer", "acc": 0, "dsc": "()Ljavax/management/remote/rmi/RMIServerImpl;", "exs": ["java/io/IOException"]}, {"nme": "encodeStubInAddress", "acc": 2, "dsc": "(Ljavax/management/remote/rmi/RMIServer;Ljava/util/Map;)V", "sig": "(Ljavax/management/remote/rmi/RMIServer;Ljava/util/Map<Ljava/lang/String;*>;)V", "exs": ["java/io/IOException"]}, {"nme": "encodeStub", "acc": 8, "dsc": "(Ljavax/management/remote/rmi/RMIServer;Ljava/util/Map;)Ljava/lang/String;", "sig": "(Ljavax/management/remote/rmi/RMIServer;Ljava/util/Map<Ljava/lang/String;*>;)Ljava/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "encodeJRMPStub", "acc": 8, "dsc": "(Ljavax/management/remote/rmi/RMIServer;Ljava/util/Map;)Ljava/lang/String;", "sig": "(Ljavax/management/remote/rmi/RMIServer;Ljava/util/Map<Ljava/lang/String;*>;)Ljava/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "objectToBind", "acc": 10, "dsc": "(Ljavax/management/remote/rmi/RMIServerImpl;Ljava/util/Map;)Ljavax/management/remote/rmi/RMIServer;", "sig": "(Ljavax/management/remote/rmi/RMIServerImpl;Ljava/util/Map<Ljava/lang/String;*>;)Ljavax/management/remote/rmi/RMIServer;", "exs": ["java/io/IOException"]}, {"nme": "newJRMPServer", "acc": 10, "dsc": "(Ljava/util/Map;I)Ljavax/management/remote/rmi/RMIServerImpl;", "sig": "(Ljava/util/Map<Ljava/lang/String;*>;I)Ljavax/management/remote/rmi/RMIServerImpl;", "exs": ["java/io/IOException"]}, {"nme": "byteArrayToBase64", "acc": 10, "dsc": "([B)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "newIOException", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)Ljava/io/IOException;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "JNDI_REBIND_ATTRIBUTE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jmx.remote.jndi.rebind"}, {"acc": 25, "nme": "RMI_CLIENT_SOCKET_FACTORY_ATTRIBUTE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jmx.remote.rmi.client.socket.factory"}, {"acc": 25, "nme": "RMI_SERVER_SOCKET_FACTORY_ATTRIBUTE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jmx.remote.rmi.server.socket.factory"}, {"acc": 25, "nme": "CREDENTIALS_FILTER_PATTERN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jmx.remote.rmi.server.credentials.filter.pattern"}, {"acc": 25, "nme": "SERIAL_FILTER_PATTERN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jmx.remote.rmi.server.serial.filter.pattern"}, {"acc": 26, "nme": "intToAlpha", "dsc": "[C"}, {"acc": 10, "nme": "logger", "dsc": "Lcom/sun/jmx/remote/util/ClassLogger;"}, {"acc": 2, "nme": "address", "dsc": "Ljavax/management/remote/JMXServiceURL;"}, {"acc": 2, "nme": "rmiServerImpl", "dsc": "Ljavax/management/remote/rmi/RMIServerImpl;"}, {"acc": 18, "nme": "attributes", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;*>;"}, {"acc": 2, "nme": "defaultClassLoader", "dsc": "<PERSON><PERSON><PERSON>/lang/ClassLoader;"}, {"acc": 2, "nme": "boundJndiUrl", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "CREATED", "dsc": "I", "val": 0}, {"acc": 26, "nme": "STARTED", "dsc": "I", "val": 1}, {"acc": 26, "nme": "STOPPED", "dsc": "I", "val": 2}, {"acc": 2, "nme": "state", "dsc": "I"}, {"acc": 26, "nme": "openedServers", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljavax/management/remote/rmi/RMIConnectorServer;>;"}]}, "classes/javax/management/remote/rmi/RMIConnector$ObjectInputStreamWithLoader.class": {"ver": 68, "acc": 48, "nme": "javax/management/remote/rmi/RMIConnector$ObjectInputStreamWithLoader", "super": "java/io/ObjectInputStream", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;<PERSON><PERSON><PERSON>/lang/ClassLoader;)V", "exs": ["java/io/IOException", "java/lang/IllegalArgumentException"]}, {"nme": "resolveClass", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/io/ObjectStreamClass;)Ljava/lang/Class;", "sig": "(Ljava/io/ObjectStreamClass;)Ljava/lang/Class<*>;", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}], "flds": [{"acc": 18, "nme": "loader", "dsc": "<PERSON><PERSON><PERSON>/lang/ClassLoader;"}]}, "classes/javax/management/remote/rmi/RMIConnector$RMIClientCommunicatorAdmin.class": {"ver": 68, "acc": 32, "nme": "javax/management/remote/rmi/RMIConnector$RMIClientCommunicatorAdmin", "super": "com/sun/jmx/remote/internal/ClientCommunicatorAdmin", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/management/remote/rmi/RMIConnector;J)V"}, {"nme": "gotIOException", "acc": 1, "dsc": "(<PERSON>java/io/IOException;)V", "exs": ["java/io/IOException"]}, {"nme": "reconnectNotificationListeners", "acc": 1, "dsc": "([Lcom/sun/jmx/remote/internal/ClientListenerInfo;)V", "exs": ["java/io/IOException"]}, {"nme": "checkConnection", "acc": 4, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "doStart", "acc": 4, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "doStop", "acc": 4, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Ljavax/management/remote/rmi/RMIConnector;"}]}, "classes/com/sun/jmx/remote/internal/rmi/RMIExporter.class": {"ver": 68, "acc": 1537, "nme": "com/sun/jmx/remote/internal/rmi/RMIExporter", "super": "java/lang/Object", "mthds": [{"nme": "exportObject", "acc": 1025, "dsc": "(Ljava/rmi/Remote;ILjava/rmi/server/RMIClientSocketFactory;Ljava/rmi/server/RMIServerSocketFactory;Ljava/io/ObjectInputFilter;)Ljava/rmi/Remote;", "exs": ["java/rmi/RemoteException"]}, {"nme": "unexportObject", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/rmi/Remote;Z)Z", "exs": ["java/rmi/NoSuchObjectException"]}], "flds": [{"acc": 25, "nme": "EXPORTER_ATTRIBUTE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.jmx.remote.rmi.exporter"}]}, "classes/javax/management/remote/rmi/RMIConnectionImpl$RMIServerCommunicatorAdmin.class": {"ver": 68, "acc": 32, "nme": "javax/management/remote/rmi/RMIConnectionImpl$RMIServerCommunicatorAdmin", "super": "com/sun/jmx/remote/internal/ServerCommunicatorAdmin", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/management/remote/rmi/RMIConnectionImpl;J)V"}, {"nme": "doStop", "acc": 4, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Ljavax/management/remote/rmi/RMIConnectionImpl;"}]}, "classes/javax/management/remote/rmi/RMIServer.class": {"ver": 68, "acc": 1537, "nme": "javax/management/remote/rmi/RMIServer", "super": "java/lang/Object", "mthds": [{"nme": "getVersion", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/rmi/RemoteException"]}, {"nme": "newClient", "acc": 1025, "dsc": "(Ljava/lang/Object;)Ljavax/management/remote/rmi/RMIConnection;", "exs": ["java/io/IOException"]}], "flds": []}, "classes/module-info.class": {"ver": 68, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "classes/com/sun/jmx/remote/protocol/rmi/ClientProvider.class": {"ver": 68, "acc": 33, "nme": "com/sun/jmx/remote/protocol/rmi/ClientProvider", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "newJMXConnector", "acc": 1, "dsc": "(Ljavax/management/remote/JMXServiceURL;Ljava/util/Map;)Ljavax/management/remote/JMXConnector;", "sig": "(Ljavax/management/remote/JMXServiceURL;Ljava/util/Map<Ljava/lang/String;*>;)Ljavax/management/remote/JMXConnector;", "exs": ["java/io/IOException"]}], "flds": []}, "classes/javax/management/remote/rmi/RMIConnectionImpl$CombinedClassLoader.class": {"ver": 68, "acc": 48, "nme": "javax/management/remote/rmi/RMIConnectionImpl$CombinedClassLoader", "super": "java/lang/ClassLoader", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;<PERSON><PERSON><PERSON>/lang/ClassLoader;)V"}, {"nme": "loadClass", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)Ljava/lang/Class;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;Z)Ljava/lang/Class<*>;", "exs": ["java/lang/ClassNotFoundException"]}], "flds": [{"acc": 16, "nme": "defaultCL", "dsc": "Ljavax/management/remote/rmi/RMIConnectionImpl$CombinedClassLoader$ClassLoaderWrapper;"}]}, "classes/javax/management/remote/rmi/RMIServerImpl.class": {"ver": 68, "acc": 1057, "nme": "javax/management/remote/rmi/RMIServerImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;*>;)V"}, {"nme": "setRMIConnectorServer", "acc": 0, "dsc": "(Ljavax/management/remote/rmi/RMIConnectorServer;)V", "exs": ["java/io/IOException"]}, {"nme": "export", "acc": 1028, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "toStub", "acc": 1025, "dsc": "()<PERSON><PERSON>va/rmi/Remote;", "exs": ["java/io/IOException"]}, {"nme": "setDefaultClassLoader", "acc": 33, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;)V"}, {"nme": "getDefaultClassLoader", "acc": 33, "dsc": "()<PERSON><PERSON><PERSON>/lang/ClassLoader;"}, {"nme": "setMBeanServer", "acc": 33, "dsc": "(Ljavax/management/MBeanServer;)V"}, {"nme": "getMBeanServer", "acc": 33, "dsc": "()Ljavax/management/MBeanServer;"}, {"nme": "getVersion", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "newClient", "acc": 1, "dsc": "(Ljava/lang/Object;)Ljavax/management/remote/rmi/RMIConnection;", "exs": ["java/io/IOException"]}, {"nme": "doNewClient", "acc": 0, "dsc": "(Ljava/lang/Object;)Ljavax/management/remote/rmi/RMIConnection;", "exs": ["java/io/IOException"]}, {"nme": "makeClient", "acc": 1028, "dsc": "(Ljava/lang/String;Ljavax/security/auth/Subject;)Ljavax/management/remote/rmi/RMIConnection;", "exs": ["java/io/IOException"]}, {"nme": "closeClient", "acc": 1028, "dsc": "(Ljavax/management/remote/rmi/RMIConnection;)V", "exs": ["java/io/IOException"]}, {"nme": "getProtocol", "acc": 1028, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "clientClosed", "acc": 4, "dsc": "(Ljavax/management/remote/rmi/RMIConnection;)V", "exs": ["java/io/IOException"]}, {"nme": "close", "acc": 33, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "closeServer", "acc": 1028, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "makeConnectionId", "acc": 42, "dsc": "(Ljava/lang/String;Ljavax/security/auth/Subject;)Ljava/lang/String;"}, {"nme": "dropDeadReferences", "acc": 2, "dsc": "()V"}, {"nme": "getNotifBuffer", "acc": 32, "dsc": "()Lcom/sun/jmx/remote/internal/NotificationBuffer;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "logger", "dsc": "Lcom/sun/jmx/remote/util/ClassLogger;"}, {"acc": 18, "nme": "clientList", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/ref/WeakReference<Ljavax/management/remote/rmi/RMIConnection;>;>;"}, {"acc": 2, "nme": "cl", "dsc": "<PERSON><PERSON><PERSON>/lang/ClassLoader;"}, {"acc": 2, "nme": "mbeanServer", "dsc": "Ljavax/management/MBeanServer;"}, {"acc": 18, "nme": "env", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;*>;"}, {"acc": 2, "nme": "connServer", "dsc": "Ljavax/management/remote/rmi/RMIConnectorServer;"}, {"acc": 10, "nme": "connectionIdNumber", "dsc": "I"}, {"acc": 2, "nme": "not<PERSON><PERSON><PERSON><PERSON>", "dsc": "Lcom/sun/jmx/remote/internal/NotificationBuffer;"}]}, "classes/javax/management/remote/rmi/RMIConnector$Util.class": {"ver": 68, "acc": 48, "nme": "javax/management/remote/rmi/RMIConnector$Util", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "cast", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Object;)TT;"}], "flds": []}, "classes/javax/management/remote/rmi/RMIJRMPServerImpl.class": {"ver": 68, "acc": 33, "nme": "javax/management/remote/rmi/RMIJRMPServerImpl", "super": "javax/management/remote/rmi/RMIServerImpl", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(ILjava/rmi/server/RMIClientSocketFactory;Ljava/rmi/server/RMIServerSocketFactory;Ljava/util/Map;)V", "sig": "(ILjava/rmi/server/RMIClientSocketFactory;Ljava/rmi/server/RMIServerSocketFactory;Ljava/util/Map<Ljava/lang/String;*>;)V", "exs": ["java/io/IOException"]}, {"nme": "export", "acc": 4, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "export", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/rmi/Remote;Ljava/io/ObjectInputFilter;)V", "exs": ["java/rmi/RemoteException"]}, {"nme": "unexport", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/rmi/Remote;Z)V", "exs": ["java/rmi/NoSuchObjectException"]}, {"nme": "getProtocol", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toStub", "acc": 1, "dsc": "()<PERSON><PERSON>va/rmi/Remote;", "exs": ["java/io/IOException"]}, {"nme": "makeClient", "acc": 4, "dsc": "(Ljava/lang/String;Ljavax/security/auth/Subject;)Ljavax/management/remote/rmi/RMIConnection;", "exs": ["java/io/IOException"]}, {"nme": "closeClient", "acc": 4, "dsc": "(Ljavax/management/remote/rmi/RMIConnection;)V", "exs": ["java/io/IOException"]}, {"nme": "closeServer", "acc": 4, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "newClientCheckInput", "acc": 0, "dsc": "(Ljava/io/ObjectInputFilter$FilterInfo;)Ljava/io/ObjectInputFilter$Status;"}, {"nme": "lambda$new$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}], "flds": [{"acc": 18, "nme": "port", "dsc": "I"}, {"acc": 18, "nme": "csf", "dsc": "Ljava/rmi/server/RMIClientSocketFactory;"}, {"acc": 18, "nme": "ssf", "dsc": "Ljava/rmi/server/RMIServerSocketFactory;"}, {"acc": 18, "nme": "env", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;*>;"}, {"acc": 18, "nme": "allowedTypes", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 18, "nme": "jmxRmiFilter", "dsc": "Ljava/io/ObjectInputFilter;"}, {"acc": 18, "nme": "cFilter", "dsc": "Ljava/io/ObjectInputFilter;"}]}, "classes/javax/management/remote/rmi/RMIConnector.class": {"ver": 68, "acc": 33, "nme": "javax/management/remote/rmi/RMIConnector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljavax/management/remote/rmi/RMIServer;Ljavax/management/remote/JMXServiceURL;Ljava/util/Map;)V", "sig": "(Ljavax/management/remote/rmi/RMIServer;Ljavax/management/remote/JMXServiceURL;Ljava/util/Map<Ljava/lang/String;*>;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljavax/management/remote/JMXServiceURL;Ljava/util/Map;)V", "sig": "(Ljavax/management/remote/JMXServiceURL;Ljava/util/Map<Ljava/lang/String;*>;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljavax/management/remote/rmi/RMIServer;Ljava/util/Map;)V", "sig": "(Ljavax/management/remote/rmi/RMIServer;Ljava/util/Map<Ljava/lang/String;*>;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Ljavax/management/remote/JMXServiceURL;"}, {"nme": "connect", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "connect", "acc": 33, "dsc": "(Ljava/util/Map;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;*>;)V", "exs": ["java/io/IOException"]}, {"nme": "getConnectionId", "acc": 33, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "getMBeanServerConnection", "acc": 33, "dsc": "()Ljavax/management/MBeanServerConnection;", "exs": ["java/io/IOException"]}, {"nme": "addConnectionNotificationListener", "acc": 1, "dsc": "(Ljavax/management/NotificationListener;Ljavax/management/NotificationFilter;Ljava/lang/Object;)V"}, {"nme": "removeConnectionNotificationListener", "acc": 1, "dsc": "(Ljavax/management/NotificationListener;)V", "exs": ["javax/management/ListenerNotFoundException"]}, {"nme": "removeConnectionNotificationListener", "acc": 1, "dsc": "(Ljavax/management/NotificationListener;Ljavax/management/NotificationFilter;Ljava/lang/Object;)V", "exs": ["javax/management/ListenerNotFoundException"]}, {"nme": "sendNotification", "acc": 2, "dsc": "(Ljavax/management/Notification;)V"}, {"nme": "close", "acc": 33, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "close", "acc": 34, "dsc": "(Z)V", "exs": ["java/io/IOException"]}, {"nme": "addListenerWithSubject", "acc": 2, "dsc": "(Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject;Ljavax/security/auth/Subject;Z)Ljava/lang/Integer;", "sig": "(Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject<Ljavax/management/NotificationFilter;>;Ljavax/security/auth/Subject;Z)Ljava/lang/Integer;", "exs": ["javax/management/InstanceNotFoundException", "java/io/IOException"]}, {"nme": "addListenersWithSubjects", "acc": 2, "dsc": "([Ljavax/management/ObjectName;[Ljava/rmi/MarshalledObject;[Ljavax/security/auth/Subject;Z)[Ljava/lang/Integer;", "sig": "([Ljavax/management/ObjectName;[Ljava/rmi/MarshalledObject<Ljavax/management/NotificationFilter;>;[Ljavax/security/auth/Subject;Z)[Ljava/lang/Integer;", "exs": ["javax/management/InstanceNotFoundException", "java/io/IOException"]}, {"nme": "readObject", "acc": 2, "dsc": "(Ljava/io/ObjectInputStream;)V", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}, {"nme": "writeObject", "acc": 2, "dsc": "(Ljava/io/ObjectOutputStream;)V", "exs": ["java/io/IOException"]}, {"nme": "initTransients", "acc": 2, "dsc": "()V"}, {"nme": "checkStub", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/rmi/Remote;Lja<PERSON>/lang/Class;)V", "sig": "(<PERSON><PERSON><PERSON>/rmi/Remote;Ljava/lang/Class<*>;)V"}, {"nme": "findRMIServer", "acc": 2, "dsc": "(Ljavax/management/remote/JMXServiceURL;Ljava/util/Map;)Ljavax/management/remote/rmi/RMIServer;", "sig": "(Ljavax/management/remote/JMXServiceURL;Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;)Ljavax/management/remote/rmi/RMIServer;", "exs": ["javax/naming/NamingException", "java/io/IOException"]}, {"nme": "findRMIServerJNDI", "acc": 2, "dsc": "(Ljava/lang/String;Ljava/util/Map;)Ljavax/management/remote/rmi/RMIServer;", "sig": "(Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;*>;)Ljavax/management/remote/rmi/RMIServer;", "exs": ["javax/naming/NamingException"]}, {"nme": "narrowJRMPServer", "acc": 10, "dsc": "(Ljava/lang/Object;)Ljavax/management/remote/rmi/RMIServer;"}, {"nme": "findRMIServerJRMP", "acc": 2, "dsc": "(Ljava/lang/String;Ljava/util/Map;)Ljavax/management/remote/rmi/RMIServer;", "sig": "(Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;*>;)Ljavax/management/remote/rmi/RMIServer;", "exs": ["java/io/IOException"]}, {"nme": "getConnection", "acc": 2, "dsc": "()Ljavax/management/MBeanServerConnection;"}, {"nme": "packageOf", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "shadowJrmpStub", "acc": 10, "dsc": "(Ljava/rmi/server/RemoteObject;)Ljavax/management/remote/rmi/RMIConnection;", "exs": ["java/lang/InstantiationException", "java/lang/IllegalAccessException", "java/lang/reflect/InvocationTargetException", "java/lang/ClassNotFoundException", "java/lang/NoSuchMethodException"]}, {"nme": "getConnection", "acc": 10, "dsc": "(Ljavax/management/remote/rmi/RMIServer;Ljava/lang/Object;Z)Ljavax/management/remote/rmi/RMIConnection;", "exs": ["java/io/IOException"]}, {"nme": "base64ToByteArray", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[B"}, {"nme": "base64toInt", "acc": 10, "dsc": "(C)I"}, {"nme": "pushDefaultClassLoader", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/ClassLoader;"}, {"nme": "popDefaultClassLoader", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;)V"}, {"nme": "objects", "acc": 10, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;)<PERSON>ja<PERSON>/lang/String;"}, {"nme": "strings", "acc": 10, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getAttributesNames", "acc": 8, "dsc": "(Ljavax/management/AttributeList;)Ljava/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "logger", "dsc": "Lcom/sun/jmx/remote/util/ClassLogger;"}, {"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 817323035842634473}, {"acc": 26, "nme": "rmiServerImplStubClassName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "rmiServerImplStubClass", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "rmiConnectionImplStubClassName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "rmiConnectionImplStubClass", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 26, "nme": "pRefClassName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jdk.jmx.remote.internal.rmi.PRef"}, {"acc": 26, "nme": "proxyRefConstructor", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Constructor;", "sig": "<PERSON><PERSON><PERSON>/lang/reflect/Constructor<*>;"}, {"acc": 26, "nme": "base64ToInt", "dsc": "[B"}, {"acc": 18, "nme": "rmiServer", "dsc": "Ljavax/management/remote/rmi/RMIServer;"}, {"acc": 18, "nme": "jmxServiceURL", "dsc": "Ljavax/management/remote/JMXServiceURL;"}, {"acc": 130, "nme": "env", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;"}, {"acc": 130, "nme": "defaultClassLoader", "dsc": "<PERSON><PERSON><PERSON>/lang/ClassLoader;"}, {"acc": 130, "nme": "connection", "dsc": "Ljavax/management/remote/rmi/RMIConnection;"}, {"acc": 130, "nme": "connectionId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 130, "nme": "clientNotifSeqNo", "dsc": "J"}, {"acc": 130, "nme": "nullSubjectConnRef", "dsc": "<PERSON><PERSON><PERSON>/lang/ref/WeakReference;", "sig": "Ljava/lang/ref/WeakReference<Ljavax/management/MBeanServerConnection;>;"}, {"acc": 130, "nme": "rmiNotifClient", "dsc": "Ljavax/management/remote/rmi/RMIConnector$RMINotifClient;"}, {"acc": 130, "nme": "clientNotifCounter", "dsc": "J"}, {"acc": 130, "nme": "connected", "dsc": "Z"}, {"acc": 130, "nme": "terminated", "dsc": "Z"}, {"acc": 130, "nme": "closeException", "dsc": "<PERSON><PERSON><PERSON>/lang/Exception;"}, {"acc": 130, "nme": "connectionBroadcaster", "dsc": "Ljavax/management/NotificationBroadcasterSupport;"}, {"acc": 130, "nme": "communicatorAdmin", "dsc": "Lcom/sun/jmx/remote/internal/ClientCommunicatorAdmin;"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "classes/javax/management/remote/rmi/RMIConnectionImpl_Stub.class": {"ver": 68, "acc": 49, "nme": "javax/management/remote/rmi/RMIConnectionImpl_Stub", "super": "java/rmi/server/RemoteStub", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/rmi/server/RemoteRef;)V"}, {"nme": "addNotificationListener", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject;Ljava/rmi/MarshalledObject;Ljavax/security/auth/Subject;)V", "exs": ["java/io/IOException", "javax/management/InstanceNotFoundException"]}, {"nme": "addNotificationListeners", "acc": 1, "dsc": "([Ljavax/management/ObjectName;[Ljava/rmi/MarshalledObject;[Ljavax/security/auth/Subject;)[Ljava/lang/Integer;", "exs": ["java/io/IOException", "javax/management/InstanceNotFoundException"]}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "createMBean", "acc": 1, "dsc": "(Ljava/lang/String;Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject;[Ljava/lang/String;Ljavax/security/auth/Subject;)Ljavax/management/ObjectInstance;", "exs": ["java/io/IOException", "javax/management/InstanceAlreadyExistsException", "javax/management/MBeanException", "javax/management/MBeanRegistrationException", "javax/management/NotCompliantMBeanException", "javax/management/ReflectionException"]}, {"nme": "createMBean", "acc": 1, "dsc": "(Ljava/lang/String;Ljavax/management/ObjectName;Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject;[Ljava/lang/String;Ljavax/security/auth/Subject;)Ljavax/management/ObjectInstance;", "exs": ["java/io/IOException", "javax/management/InstanceAlreadyExistsException", "javax/management/InstanceNotFoundException", "javax/management/MBeanException", "javax/management/MBeanRegistrationException", "javax/management/NotCompliantMBeanException", "javax/management/ReflectionException"]}, {"nme": "createMBean", "acc": 1, "dsc": "(Ljava/lang/String;Ljavax/management/ObjectName;Ljavax/management/ObjectName;Ljavax/security/auth/Subject;)Ljavax/management/ObjectInstance;", "exs": ["java/io/IOException", "javax/management/InstanceAlreadyExistsException", "javax/management/InstanceNotFoundException", "javax/management/MBeanException", "javax/management/MBeanRegistrationException", "javax/management/NotCompliantMBeanException", "javax/management/ReflectionException"]}, {"nme": "createMBean", "acc": 1, "dsc": "(Ljava/lang/String;Ljavax/management/ObjectName;Ljavax/security/auth/Subject;)Ljavax/management/ObjectInstance;", "exs": ["java/io/IOException", "javax/management/InstanceAlreadyExistsException", "javax/management/MBeanException", "javax/management/MBeanRegistrationException", "javax/management/NotCompliantMBeanException", "javax/management/ReflectionException"]}, {"nme": "fetchNotifications", "acc": 1, "dsc": "(JIJ)Ljavax/management/remote/NotificationResult;", "exs": ["java/io/IOException"]}, {"nme": "getAttribute", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljava/lang/String;Ljavax/security/auth/Subject;)Ljava/lang/Object;", "exs": ["java/io/IOException", "javax/management/AttributeNotFoundException", "javax/management/InstanceNotFoundException", "javax/management/MBeanException", "javax/management/ReflectionException"]}, {"nme": "getAttributes", "acc": 1, "dsc": "(Ljavax/management/ObjectName;[Ljava/lang/String;Ljavax/security/auth/Subject;)Ljavax/management/AttributeList;", "exs": ["java/io/IOException", "javax/management/InstanceNotFoundException", "javax/management/ReflectionException"]}, {"nme": "getConnectionId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "getDefaultDomain", "acc": 1, "dsc": "(Ljavax/security/auth/Subject;)Ljava/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "getDomains", "acc": 1, "dsc": "(Ljavax/security/auth/Subject;)[Ljava/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "getMBeanCount", "acc": 1, "dsc": "(Ljavax/security/auth/Subject;)Ljava/lang/Integer;", "exs": ["java/io/IOException"]}, {"nme": "getMBeanInfo", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljavax/security/auth/Subject;)Ljavax/management/MBeanInfo;", "exs": ["java/io/IOException", "javax/management/InstanceNotFoundException", "javax/management/IntrospectionException", "javax/management/ReflectionException"]}, {"nme": "getObjectInstance", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljavax/security/auth/Subject;)Ljavax/management/ObjectInstance;", "exs": ["java/io/IOException", "javax/management/InstanceNotFoundException"]}, {"nme": "invoke", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljava/lang/String;Ljava/rmi/MarshalledObject;[Ljava/lang/String;Ljavax/security/auth/Subject;)Ljava/lang/Object;", "exs": ["java/io/IOException", "javax/management/InstanceNotFoundException", "javax/management/MBeanException", "javax/management/ReflectionException"]}, {"nme": "isInstanceOf", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljava/lang/String;Ljavax/security/auth/Subject;)Z", "exs": ["java/io/IOException", "javax/management/InstanceNotFoundException"]}, {"nme": "isRegistered", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljavax/security/auth/Subject;)Z", "exs": ["java/io/IOException"]}, {"nme": "queryMBeans", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject;Ljavax/security/auth/Subject;)Ljava/util/Set;", "exs": ["java/io/IOException"]}, {"nme": "queryNames", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject;Ljavax/security/auth/Subject;)Ljava/util/Set;", "exs": ["java/io/IOException"]}, {"nme": "removeNotificationListener", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject;Ljava/rmi/MarshalledObject;Ljavax/security/auth/Subject;)V", "exs": ["java/io/IOException", "javax/management/InstanceNotFoundException", "javax/management/ListenerNotFoundException"]}, {"nme": "removeNotificationListener", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljavax/management/ObjectName;Ljavax/security/auth/Subject;)V", "exs": ["java/io/IOException", "javax/management/InstanceNotFoundException", "javax/management/ListenerNotFoundException"]}, {"nme": "removeNotificationListeners", "acc": 1, "dsc": "(Ljavax/management/ObjectName;[Ljava/lang/Integer;Ljavax/security/auth/Subject;)V", "exs": ["java/io/IOException", "javax/management/InstanceNotFoundException", "javax/management/ListenerNotFoundException"]}, {"nme": "setAttribute", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject;Ljavax/security/auth/Subject;)V", "exs": ["java/io/IOException", "javax/management/AttributeNotFoundException", "javax/management/InstanceNotFoundException", "javax/management/InvalidAttributeValueException", "javax/management/MBeanException", "javax/management/ReflectionException"]}, {"nme": "setAttributes", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljava/rmi/MarshalledObject;Ljavax/security/auth/Subject;)Ljavax/management/AttributeList;", "exs": ["java/io/IOException", "javax/management/InstanceNotFoundException", "javax/management/ReflectionException"]}, {"nme": "unregisterMBean", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljavax/security/auth/Subject;)V", "exs": ["java/io/IOException", "javax/management/InstanceNotFoundException", "javax/management/MBeanRegistrationException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 2}, {"acc": 10, "nme": "$method_addNotificationListener_0", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 10, "nme": "$method_addNotificationListeners_1", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 10, "nme": "$method_close_2", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 10, "nme": "$method_createMBean_3", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 10, "nme": "$method_createMBean_4", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 10, "nme": "$method_createMBean_5", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 10, "nme": "$method_createMBean_6", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 10, "nme": "$method_fetchNotifications_7", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 10, "nme": "$method_getAttribute_8", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 10, "nme": "$method_getAttributes_9", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 10, "nme": "$method_getConnectionId_10", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 10, "nme": "$method_getDefaultDomain_11", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 10, "nme": "$method_getDomains_12", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 10, "nme": "$method_getMBeanCount_13", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 10, "nme": "$method_getMBeanInfo_14", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 10, "nme": "$method_getObjectInstance_15", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 10, "nme": "$method_invoke_16", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 10, "nme": "$method_isInstanceOf_17", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 10, "nme": "$method_isRegistered_18", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 10, "nme": "$method_queryMBeans_19", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 10, "nme": "$method_queryNames_20", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 10, "nme": "$method_removeNotificationListener_21", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 10, "nme": "$method_removeNotificationListener_22", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 10, "nme": "$method_removeNotificationListeners_23", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 10, "nme": "$method_setAttribute_24", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 10, "nme": "$method_setAttributes_25", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 10, "nme": "$method_unregisterMBean_26", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}]}}}}