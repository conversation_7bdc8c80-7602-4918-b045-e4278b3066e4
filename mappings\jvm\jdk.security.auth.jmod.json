{"md5": "3490424e538a8f8b09acdea966e466a1", "sha2": "8ac32ef98aebb99e259581d76636faf31c2ea465", "sha256": "4273b5835bb05455b50ce0418f0840bac3d1d29c9706648e0d3fe52585ef3034", "contents": {"classes": {"classes/com/sun/security/auth/module/NTLoginModule.class": {"ver": 68, "acc": 33, "nme": "com/sun/security/auth/module/NTLoginModule", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "initialize", "acc": 1, "dsc": "(Ljavax/security/auth/Subject;Ljavax/security/auth/callback/CallbackHandler;Ljava/util/Map;Ljava/util/Map;)V", "sig": "(Ljavax/security/auth/Subject;Ljavax/security/auth/callback/CallbackHandler;Ljava/util/Map<Ljava/lang/String;*>;Ljava/util/Map<Ljava/lang/String;*>;)V"}, {"nme": "login", "acc": 1, "dsc": "()Z", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "commit", "acc": 1, "dsc": "()Z", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "abort", "acc": 1, "dsc": "()Z", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "logout", "acc": 1, "dsc": "()Z", "exs": ["javax/security/auth/login/LoginException"]}], "flds": [{"acc": 2, "nme": "ntSystem", "dsc": "Lcom/sun/security/auth/module/NTSystem;"}, {"acc": 2, "nme": "subject", "dsc": "Ljavax/security/auth/Subject;"}, {"acc": 2, "nme": "call<PERSON><PERSON><PERSON><PERSON>", "dsc": "Ljavax/security/auth/callback/CallbackHandler;"}, {"acc": 2, "nme": "sharedState", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;*>;"}, {"acc": 2, "nme": "options", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;*>;"}, {"acc": 2, "nme": "debug", "dsc": "Z"}, {"acc": 2, "nme": "debugNative", "dsc": "Z"}, {"acc": 2, "nme": "succeeded", "dsc": "Z"}, {"acc": 2, "nme": "commitSucceeded", "dsc": "Z"}, {"acc": 2, "nme": "userPrincipal", "dsc": "Lcom/sun/security/auth/NTUserPrincipal;"}, {"acc": 2, "nme": "userSID", "dsc": "Lcom/sun/security/auth/NTSidUserPrincipal;"}, {"acc": 2, "nme": "userDomain", "dsc": "Lcom/sun/security/auth/NTDomainPrincipal;"}, {"acc": 2, "nme": "domainSID", "dsc": "Lcom/sun/security/auth/NTSidDomainPrincipal;"}, {"acc": 2, "nme": "primaryGroup", "dsc": "Lcom/sun/security/auth/NTSidPrimaryGroupPrincipal;"}, {"acc": 2, "nme": "groups", "dsc": "[Lcom/sun/security/auth/NTSidGroupPrincipal;"}, {"acc": 2, "nme": "iToken", "dsc": "Lcom/sun/security/auth/NTNumericCredential;"}]}, "classes/com/sun/security/auth/module/UnixSystem.class": {"ver": 68, "acc": 33, "nme": "com/sun/security/auth/module/UnixSystem", "super": "java/lang/Object", "mthds": [{"nme": "getUnixInfo", "acc": 258, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getUsername", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getUid", "acc": 1, "dsc": "()J"}, {"nme": "getGid", "acc": 1, "dsc": "()J"}, {"nme": "getGroups", "acc": 1, "dsc": "()[J"}], "flds": [{"acc": 4, "nme": "username", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4, "nme": "uid", "dsc": "J"}, {"acc": 4, "nme": "gid", "dsc": "J"}, {"acc": 4, "nme": "groups", "dsc": "[J"}]}, "classes/com/sun/security/auth/module/UnixLoginModule.class": {"ver": 68, "acc": 33, "nme": "com/sun/security/auth/module/UnixLoginModule", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "initialize", "acc": 1, "dsc": "(Ljavax/security/auth/Subject;Ljavax/security/auth/callback/CallbackHandler;Ljava/util/Map;Ljava/util/Map;)V", "sig": "(Ljavax/security/auth/Subject;Ljavax/security/auth/callback/CallbackHandler;Ljava/util/Map<Ljava/lang/String;*>;Ljava/util/Map<Ljava/lang/String;*>;)V"}, {"nme": "login", "acc": 1, "dsc": "()Z", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "commit", "acc": 1, "dsc": "()Z", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "abort", "acc": 1, "dsc": "()Z", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "logout", "acc": 1, "dsc": "()Z", "exs": ["javax/security/auth/login/LoginException"]}], "flds": [{"acc": 2, "nme": "subject", "dsc": "Ljavax/security/auth/Subject;"}, {"acc": 2, "nme": "call<PERSON><PERSON><PERSON><PERSON>", "dsc": "Ljavax/security/auth/callback/CallbackHandler;"}, {"acc": 2, "nme": "sharedState", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;*>;"}, {"acc": 2, "nme": "options", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;*>;"}, {"acc": 2, "nme": "debug", "dsc": "Z"}, {"acc": 2, "nme": "ss", "dsc": "Lcom/sun/security/auth/module/UnixSystem;"}, {"acc": 2, "nme": "succeeded", "dsc": "Z"}, {"acc": 2, "nme": "commitSucceeded", "dsc": "Z"}, {"acc": 2, "nme": "userPrincipal", "dsc": "Lcom/sun/security/auth/UnixPrincipal;"}, {"acc": 2, "nme": "UIDPrincipal", "dsc": "Lcom/sun/security/auth/UnixNumericUserPrincipal;"}, {"acc": 2, "nme": "GIDPrincipal", "dsc": "Lcom/sun/security/auth/UnixNumericGroupPrincipal;"}, {"acc": 2, "nme": "supplementaryGroups", "dsc": "<PERSON><PERSON><PERSON>/util/LinkedList;", "sig": "Ljava/util/LinkedList<Lcom/sun/security/auth/UnixNumericGroupPrincipal;>;"}]}, "classes/com/sun/security/auth/NTNumericCredential.class": {"ver": 68, "acc": 33, "nme": "com/sun/security/auth/NTNumericCredential", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(J)V"}, {"nme": "getToken", "acc": 1, "dsc": "()J"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 2, "nme": "impersonationToken", "dsc": "J"}]}, "classes/com/sun/security/auth/NTSidUserPrincipal.class": {"ver": 68, "acc": 33, "nme": "com/sun/security/auth/NTSidUserPrincipal", "super": "com/sun/security/auth/NTSid", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -5573239889517749525}]}, "classes/com/sun/security/auth/NTUserPrincipal.class": {"ver": 68, "acc": 33, "nme": "com/sun/security/auth/NTUserPrincipal", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "readObject", "acc": 2, "dsc": "(Ljava/io/ObjectInputStream;)V", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -8737649811939033735}, {"acc": 2, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/com/sun/security/auth/NTSidGroupPrincipal.class": {"ver": 68, "acc": 33, "nme": "com/sun/security/auth/NTSidGroupPrincipal", "super": "com/sun/security/auth/NTSid", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -1373347438636198229}]}, "classes/module-info.class": {"ver": 68, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "classes/com/sun/security/auth/UnixPrincipal.class": {"ver": 68, "acc": 33, "nme": "com/sun/security/auth/UnixPrincipal", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "readObject", "acc": 2, "dsc": "(Ljava/io/ObjectInputStream;)V", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -2951667807323493631}, {"acc": 2, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/com/sun/security/auth/login/ConfigFile.class": {"ver": 68, "acc": 33, "nme": "com/sun/security/auth/login/ConfigFile", "super": "javax/security/auth/login/Configuration", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/net/URI;)V"}, {"nme": "getAppConfigurationEntry", "acc": 1, "dsc": "(Ljava/lang/String;)[Ljavax/security/auth/login/AppConfigurationEntry;"}, {"nme": "refresh", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "spi", "dsc": "Lsun/security/provider/ConfigFile$Spi;"}]}, "classes/com/sun/security/auth/LdapPrincipal.class": {"ver": 68, "acc": 49, "nme": "com/sun/security/auth/LdapPrincipal", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["javax/naming/InvalidNameException"]}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLdapName", "acc": 2, "dsc": "(Lja<PERSON>/lang/String;)Ljavax/naming/ldap/LdapName;", "exs": ["javax/naming/InvalidNameException"]}, {"nme": "readObject", "acc": 2, "dsc": "(Ljava/io/ObjectInputStream;)V", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 6820120005580754861}, {"acc": 18, "nme": "nameString", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "name", "dsc": "Ljavax/naming/ldap/LdapName;"}]}, "classes/com/sun/security/auth/UnixNumericGroupPrincipal.class": {"ver": 68, "acc": 33, "nme": "com/sun/security/auth/UnixNumericGroupPrincipal", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)V"}, {"nme": "<init>", "acc": 1, "dsc": "(JZ)V"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "longValue", "acc": 1, "dsc": "()J"}, {"nme": "isPrimaryGroup", "acc": 1, "dsc": "()Z"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "readObject", "acc": 2, "dsc": "(Ljava/io/ObjectInputStream;)V", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 3941535899328403223}, {"acc": 2, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "primaryGroup", "dsc": "Z"}]}, "classes/com/sun/security/auth/NTSid.class": {"ver": 68, "acc": 33, "nme": "com/sun/security/auth/NTSid", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "readObject", "acc": 2, "dsc": "(Ljava/io/ObjectInputStream;)V", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 4412290580770249885}, {"acc": 2, "nme": "sid", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/com/sun/security/auth/module/Crypt.class": {"ver": 68, "acc": 32, "nme": "com/sun/security/auth/module/Crypt", "super": "java/lang/Object", "mthds": [{"nme": "setkey", "acc": 2, "dsc": "([B)V"}, {"nme": "encrypt", "acc": 2, "dsc": "([BI)V"}, {"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "crypt", "acc": 33, "dsc": "([B[B)[B"}, {"nme": "main", "acc": 9, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "IP", "dsc": "[B"}, {"acc": 26, "nme": "FP", "dsc": "[B"}, {"acc": 26, "nme": "PC1_C", "dsc": "[B"}, {"acc": 26, "nme": "PC1_D", "dsc": "[B"}, {"acc": 26, "nme": "shifts", "dsc": "[B"}, {"acc": 26, "nme": "PC2_C", "dsc": "[B"}, {"acc": 26, "nme": "PC2_D", "dsc": "[B"}, {"acc": 2, "nme": "C", "dsc": "[B"}, {"acc": 2, "nme": "D", "dsc": "[B"}, {"acc": 2, "nme": "KS", "dsc": "[B"}, {"acc": 2, "nme": "E", "dsc": "[B"}, {"acc": 26, "nme": "e2", "dsc": "[B"}, {"acc": 26, "nme": "S", "dsc": "[[B"}, {"acc": 26, "nme": "P", "dsc": "[B"}, {"acc": 2, "nme": "L", "dsc": "[B"}, {"acc": 2, "nme": "tempL", "dsc": "[B"}, {"acc": 2, "nme": "f", "dsc": "[B"}, {"acc": 2, "nme": "preS", "dsc": "[B"}]}, "classes/com/sun/security/auth/module/NTSystem.class": {"ver": 68, "acc": 33, "nme": "com/sun/security/auth/module/NTSystem", "super": "java/lang/Object", "mthds": [{"nme": "get<PERSON>urrent", "acc": 258, "dsc": "(Z)V"}, {"nme": "getImpersonationToken0", "acc": 258, "dsc": "()J"}, {"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 0, "dsc": "(Z)V"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDomain", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDomainSID", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getUserSID", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getPrimaryGroupID", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getGroupIDs", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getImpersonationToken", "acc": 33, "dsc": "()J"}, {"nme": "loadNative", "acc": 2, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "userName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "domain", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "domainSID", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "userSID", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "groupIDs", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "primaryGroupID", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "impersonationToken", "dsc": "J"}]}, "classes/com/sun/security/auth/PrincipalComparator.class": {"ver": 68, "acc": 1537, "nme": "com/sun/security/auth/PrincipalComparator", "super": "java/lang/Object", "mthds": [{"nme": "implies", "acc": 1025, "dsc": "(Ljavax/security/auth/Subject;)Z"}], "flds": []}, "classes/com/sun/security/auth/module/LdapLoginModule.class": {"ver": 68, "acc": 33, "nme": "com/sun/security/auth/module/LdapLoginModule", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "initialize", "acc": 1, "dsc": "(Ljavax/security/auth/Subject;Ljavax/security/auth/callback/CallbackHandler;Ljava/util/Map;Ljava/util/Map;)V", "sig": "(Ljavax/security/auth/Subject;Ljavax/security/auth/callback/CallbackHandler;Ljava/util/Map<Ljava/lang/String;*>;Ljava/util/Map<Ljava/lang/String;*>;)V"}, {"nme": "login", "acc": 1, "dsc": "()Z", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "commit", "acc": 1, "dsc": "()Z", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "abort", "acc": 1, "dsc": "()Z", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "logout", "acc": 1, "dsc": "()Z", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "attemptAuthentication", "acc": 2, "dsc": "(Z)V", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "findUserDN", "acc": 2, "dsc": "(Ljavax/naming/ldap/LdapContext;)Ljava/lang/String;", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "escapeUsernameChars", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "replaceUsernameToken", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/regex/Matcher;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getUsernamePassword", "acc": 2, "dsc": "(Z)V", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "cleanState", "acc": 2, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "USERNAME_KEY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "javax.security.auth.login.name"}, {"acc": 26, "nme": "PASSWORD_KEY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "javax.security.auth.login.password"}, {"acc": 26, "nme": "USER_PROVIDER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "userProvider"}, {"acc": 26, "nme": "USER_FILTER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "userFilter"}, {"acc": 26, "nme": "AUTHC_IDENTITY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "authIdentity"}, {"acc": 26, "nme": "AUTHZ_IDENTITY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "authzIdentity"}, {"acc": 26, "nme": "USERNAME_TOKEN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "{USERNAME}"}, {"acc": 26, "nme": "USERNAME_PATTERN", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 2, "nme": "userProvider", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "userFilter", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "authcIdentity", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "authzIdentity", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "authzIdentityAttr", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "useSSL", "dsc": "Z"}, {"acc": 2, "nme": "authF<PERSON><PERSON>", "dsc": "Z"}, {"acc": 2, "nme": "auth<PERSON>n<PERSON>", "dsc": "Z"}, {"acc": 2, "nme": "useFirstPass", "dsc": "Z"}, {"acc": 2, "nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dsc": "Z"}, {"acc": 2, "nme": "storePass", "dsc": "Z"}, {"acc": 2, "nme": "clearPass", "dsc": "Z"}, {"acc": 2, "nme": "debug", "dsc": "Z"}, {"acc": 2, "nme": "succeeded", "dsc": "Z"}, {"acc": 2, "nme": "commitSucceeded", "dsc": "Z"}, {"acc": 2, "nme": "username", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "password", "dsc": "[C"}, {"acc": 2, "nme": "ldapPrincipal", "dsc": "Lcom/sun/security/auth/LdapPrincipal;"}, {"acc": 2, "nme": "userPrincipal", "dsc": "Lcom/sun/security/auth/UserPrincipal;"}, {"acc": 2, "nme": "authzPrincipal", "dsc": "Lcom/sun/security/auth/UserPrincipal;"}, {"acc": 2, "nme": "subject", "dsc": "Ljavax/security/auth/Subject;"}, {"acc": 2, "nme": "call<PERSON><PERSON><PERSON><PERSON>", "dsc": "Ljavax/security/auth/callback/CallbackHandler;"}, {"acc": 2, "nme": "sharedState", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;"}, {"acc": 2, "nme": "options", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;*>;"}, {"acc": 2, "nme": "ctx", "dsc": "Ljavax/naming/ldap/LdapContext;"}, {"acc": 2, "nme": "identityMatcher", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Matcher;"}, {"acc": 2, "nme": "filterMatcher", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Matcher;"}, {"acc": 2, "nme": "ldapEnvironment", "dsc": "<PERSON><PERSON><PERSON>/util/Hashtable;", "sig": "Lja<PERSON>/util/Hashtable<Ljava/lang/String;Ljava/lang/Object;>;"}, {"acc": 2, "nme": "constraints", "dsc": "Ljavax/naming/directory/SearchControls;"}]}, "classes/com/sun/security/auth/NTSidPrimaryGroupPrincipal.class": {"ver": 68, "acc": 33, "nme": "com/sun/security/auth/NTSidPrimaryGroupPrincipal", "super": "com/sun/security/auth/NTSid", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 8011978367305190527}]}, "classes/com/sun/security/auth/callback/TextCallbackHandler.class": {"ver": 68, "acc": 33, "nme": "com/sun/security/auth/callback/TextCallbackHandler", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "handle", "acc": 1, "dsc": "([Ljavax/security/auth/callback/Callback;)V", "exs": ["java/io/IOException", "javax/security/auth/callback/UnsupportedCallbackException"]}], "flds": [{"acc": 18, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "Ljavax/security/auth/callback/CallbackHandler;"}]}, "classes/com/sun/security/auth/module/Krb5LoginModule.class": {"ver": 68, "acc": 33, "nme": "com/sun/security/auth/module/Krb5LoginModule", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "initialize", "acc": 1, "dsc": "(Ljavax/security/auth/Subject;Ljavax/security/auth/callback/CallbackHandler;Ljava/util/Map;Ljava/util/Map;)V", "sig": "(Ljavax/security/auth/Subject;Ljavax/security/auth/callback/CallbackHandler;Ljava/util/Map<Ljava/lang/String;*>;Ljava/util/Map<Ljava/lang/String;*>;)V"}, {"nme": "login", "acc": 1, "dsc": "()Z", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "attemptAuthentication", "acc": 2, "dsc": "(Z)V", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "promptFor<PERSON>ame", "acc": 2, "dsc": "(Z)V", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "promptF<PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(Z)V", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "validateConfiguration", "acc": 2, "dsc": "()V", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "isCurrent", "acc": 10, "dsc": "(Lsun/security/krb5/Credentials;)Z"}, {"nme": "isOld", "acc": 10, "dsc": "(Lsun/security/krb5/Credentials;)Z"}, {"nme": "renewCredentials", "acc": 2, "dsc": "(Lsun/security/krb5/Credentials;)Lsun/security/krb5/Credentials;"}, {"nme": "commit", "acc": 1, "dsc": "()Z", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "abort", "acc": 1, "dsc": "()Z", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "logout", "acc": 1, "dsc": "()Z", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "cleanKerberosCred", "acc": 2, "dsc": "()V", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "cleanState", "acc": 2, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "subject", "dsc": "Ljavax/security/auth/Subject;"}, {"acc": 2, "nme": "call<PERSON><PERSON><PERSON><PERSON>", "dsc": "Ljavax/security/auth/callback/CallbackHandler;"}, {"acc": 2, "nme": "sharedState", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;"}, {"acc": 2, "nme": "options", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;*>;"}, {"acc": 2, "nme": "debug", "dsc": "Lsun/security/util/Debug;"}, {"acc": 2, "nme": "storeKey", "dsc": "Z"}, {"acc": 2, "nme": "doNotPrompt", "dsc": "Z"}, {"acc": 2, "nme": "useTicketCache", "dsc": "Z"}, {"acc": 2, "nme": "useKeyTab", "dsc": "Z"}, {"acc": 2, "nme": "ticketCacheName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "keyTabName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "princ<PERSON>ame", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "useFirstPass", "dsc": "Z"}, {"acc": 2, "nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dsc": "Z"}, {"acc": 2, "nme": "storePass", "dsc": "Z"}, {"acc": 2, "nme": "clearPass", "dsc": "Z"}, {"acc": 2, "nme": "refreshKrb5Config", "dsc": "Z"}, {"acc": 2, "nme": "renewTGT", "dsc": "Z"}, {"acc": 2, "nme": "isInitiator", "dsc": "Z"}, {"acc": 2, "nme": "succeeded", "dsc": "Z"}, {"acc": 2, "nme": "commitSucceeded", "dsc": "Z"}, {"acc": 2, "nme": "username", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "enc<PERSON><PERSON><PERSON>", "dsc": "[Lsun/security/krb5/EncryptionKey;"}, {"acc": 0, "nme": "ktab", "dsc": "Ljavax/security/auth/kerberos/KeyTab;"}, {"acc": 2, "nme": "cred", "dsc": "Lsun/security/krb5/Credentials;"}, {"acc": 2, "nme": "principal", "dsc": "Lsun/security/krb5/PrincipalName;"}, {"acc": 2, "nme": "kerbClientPrinc", "dsc": "Ljavax/security/auth/kerberos/KerberosPrincipal;"}, {"acc": 2, "nme": "kerbTicket", "dsc": "Ljavax/security/auth/kerberos/KerberosTicket;"}, {"acc": 2, "nme": "kerb<PERSON><PERSON><PERSON>", "dsc": "[Ljavax/security/auth/kerberos/<PERSON>rber<PERSON>Key;"}, {"acc": 2, "nme": "krb5PrincName", "dsc": "<PERSON><PERSON><PERSON>/lang/StringBuffer;"}, {"acc": 2, "nme": "unboundServer", "dsc": "Z"}, {"acc": 2, "nme": "password", "dsc": "[C"}, {"acc": 26, "nme": "NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "javax.security.auth.login.name"}, {"acc": 26, "nme": "PWD", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "javax.security.auth.login.password"}]}, "classes/com/sun/security/auth/UserPrincipal.class": {"ver": 68, "acc": 49, "nme": "com/sun/security/auth/UserPrincipal", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "readObject", "acc": 2, "dsc": "(Ljava/io/ObjectInputStream;)V", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 892106070870210969}, {"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/com/sun/security/auth/NTDomainPrincipal.class": {"ver": 68, "acc": 33, "nme": "com/sun/security/auth/NTDomainPrincipal", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "readObject", "acc": 2, "dsc": "(Ljava/io/ObjectInputStream;)V", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -4408637351440771220}, {"acc": 2, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/com/sun/security/auth/UnixNumericUserPrincipal.class": {"ver": 68, "acc": 33, "nme": "com/sun/security/auth/UnixNumericUserPrincipal", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(J)V"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "longValue", "acc": 1, "dsc": "()J"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "readObject", "acc": 2, "dsc": "(Ljava/io/ObjectInputStream;)V", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -4329764253802397821}, {"acc": 2, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/com/sun/security/auth/module/KeyStoreLoginModule.class": {"ver": 68, "acc": 33, "nme": "com/sun/security/auth/module/KeyStoreLoginModule", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "initialize", "acc": 1, "dsc": "(Ljavax/security/auth/Subject;Ljavax/security/auth/callback/CallbackHandler;Ljava/util/Map;Ljava/util/Map;)V", "sig": "(Ljavax/security/auth/Subject;Ljavax/security/auth/callback/CallbackHandler;Ljava/util/Map<Ljava/lang/String;*>;Ljava/util/Map<Ljava/lang/String;*>;)V"}, {"nme": "processOptions", "acc": 2, "dsc": "()V"}, {"nme": "login", "acc": 1, "dsc": "()Z", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "getAliasAndPasswords", "acc": 2, "dsc": "(I)V", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "check<PERSON><PERSON><PERSON>", "acc": 2, "dsc": "()V", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "checkStorePass", "acc": 2, "dsc": "()V", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "checkKeyPass", "acc": 2, "dsc": "()V", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "prompt", "acc": 2, "dsc": "(Ljavax/security/auth/callback/NameCallback;Ljavax/security/auth/callback/PasswordCallback;Ljavax/security/auth/callback/PasswordCallback;)V", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "save<PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(Ljavax/security/auth/callback/NameCallback;)V"}, {"nme": "saveStorePass", "acc": 2, "dsc": "(Ljavax/security/auth/callback/PasswordCallback;)V"}, {"nme": "save<PERSON>eyPass", "acc": 2, "dsc": "(Ljavax/security/auth/callback/PasswordCallback;)V"}, {"nme": "getKeyStoreInfo", "acc": 2, "dsc": "()V", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "commit", "acc": 1, "dsc": "()Z", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "commitInternal", "acc": 2, "dsc": "()Z", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "abort", "acc": 1, "dsc": "()Z", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "logout", "acc": 1, "dsc": "()Z", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "logoutInternal", "acc": 2, "dsc": "()V", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "debugPrint", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "newURL", "acc": 10, "dsc": "(Ljava/lang/String;)Ljava/net/URL;", "exs": ["java/net/MalformedURLException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "UNINITIALIZED", "dsc": "I", "val": 0}, {"acc": 26, "nme": "INITIALIZED", "dsc": "I", "val": 1}, {"acc": 26, "nme": "AUTHENTICATED", "dsc": "I", "val": 2}, {"acc": 26, "nme": "LOGGED_IN", "dsc": "I", "val": 3}, {"acc": 26, "nme": "PROTECTED_PATH", "dsc": "I", "val": 0}, {"acc": 26, "nme": "TOKEN", "dsc": "I", "val": 1}, {"acc": 26, "nme": "NORMAL", "dsc": "I", "val": 2}, {"acc": 26, "nme": "NONE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "NONE"}, {"acc": 26, "nme": "P11KEYSTORE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "PKCS11"}, {"acc": 26, "nme": "bannerCallback", "dsc": "Ljavax/security/auth/callback/TextOutputCallback;"}, {"acc": 18, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "Ljavax/security/auth/callback/ConfirmationCallback;"}, {"acc": 2, "nme": "subject", "dsc": "Ljavax/security/auth/Subject;"}, {"acc": 2, "nme": "call<PERSON><PERSON><PERSON><PERSON>", "dsc": "Ljavax/security/auth/callback/CallbackHandler;"}, {"acc": 2, "nme": "sharedState", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;"}, {"acc": 2, "nme": "options", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;*>;"}, {"acc": 2, "nme": "keyStorePassword", "dsc": "[C"}, {"acc": 2, "nme": "privateKeyPassword", "dsc": "[C"}, {"acc": 2, "nme": "keyStore", "dsc": "Ljava/security/KeyStore;"}, {"acc": 2, "nme": "keyStoreURL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "keyStoreType", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "keyStoreProvider", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "keyStoreAlias", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "keyStorePasswordURL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "privateKeyPasswordURL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "debug", "dsc": "Z"}, {"acc": 2, "nme": "principal", "dsc": "Ljavax/security/auth/x500/X500Principal;"}, {"acc": 2, "nme": "fromKeyStore", "dsc": "[Ljava/security/cert/Certificate;"}, {"acc": 2, "nme": "certP", "dsc": "Ljava/security/cert/CertPath;"}, {"acc": 2, "nme": "privateCredential", "dsc": "Ljavax/security/auth/x500/X500PrivateCredential;"}, {"acc": 2, "nme": "status", "dsc": "I"}, {"acc": 2, "nme": "nullStream", "dsc": "Z"}, {"acc": 2, "nme": "token", "dsc": "Z"}, {"acc": 2, "nme": "protectedPath", "dsc": "Z"}]}, "classes/com/sun/security/auth/module/JndiLoginModule.class": {"ver": 68, "acc": 33, "nme": "com/sun/security/auth/module/JndiLoginModule", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "initialize", "acc": 1, "dsc": "(Ljavax/security/auth/Subject;Ljavax/security/auth/callback/CallbackHandler;Ljava/util/Map;Ljava/util/Map;)V", "sig": "(Ljavax/security/auth/Subject;Ljavax/security/auth/callback/CallbackHandler;Ljava/util/Map<Ljava/lang/String;*>;Ljava/util/Map<Ljava/lang/String;*>;)V"}, {"nme": "login", "acc": 1, "dsc": "()Z", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "commit", "acc": 1, "dsc": "()Z", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "abort", "acc": 1, "dsc": "()Z", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "logout", "acc": 1, "dsc": "()Z", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "attemptAuthentication", "acc": 2, "dsc": "(Z)V", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "getUsernamePassword", "acc": 2, "dsc": "(Z)V", "exs": ["javax/security/auth/login/LoginException"]}, {"nme": "verifyPassword", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "cleanState", "acc": 2, "dsc": "()V"}], "flds": [{"acc": 17, "nme": "USER_PROVIDER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "user.provider.url"}, {"acc": 17, "nme": "GROUP_PROVIDER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "group.provider.url"}, {"acc": 2, "nme": "debug", "dsc": "Z"}, {"acc": 2, "nme": "strong<PERSON>ebug", "dsc": "Z"}, {"acc": 2, "nme": "userProvider", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "groupProvider", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "useFirstPass", "dsc": "Z"}, {"acc": 2, "nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dsc": "Z"}, {"acc": 2, "nme": "storePass", "dsc": "Z"}, {"acc": 2, "nme": "clearPass", "dsc": "Z"}, {"acc": 2, "nme": "succeeded", "dsc": "Z"}, {"acc": 2, "nme": "commitSucceeded", "dsc": "Z"}, {"acc": 2, "nme": "username", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "password", "dsc": "[C"}, {"acc": 0, "nme": "ctx", "dsc": "Ljavax/naming/directory/DirContext;"}, {"acc": 2, "nme": "userPrincipal", "dsc": "Lcom/sun/security/auth/UnixPrincipal;"}, {"acc": 2, "nme": "UIDPrincipal", "dsc": "Lcom/sun/security/auth/UnixNumericUserPrincipal;"}, {"acc": 2, "nme": "GIDPrincipal", "dsc": "Lcom/sun/security/auth/UnixNumericGroupPrincipal;"}, {"acc": 2, "nme": "supplementaryGroups", "dsc": "<PERSON><PERSON><PERSON>/util/LinkedList;", "sig": "Ljava/util/LinkedList<Lcom/sun/security/auth/UnixNumericGroupPrincipal;>;"}, {"acc": 2, "nme": "subject", "dsc": "Ljavax/security/auth/Subject;"}, {"acc": 2, "nme": "call<PERSON><PERSON><PERSON><PERSON>", "dsc": "Ljavax/security/auth/callback/CallbackHandler;"}, {"acc": 2, "nme": "sharedState", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;"}, {"acc": 2, "nme": "options", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;*>;"}, {"acc": 26, "nme": "CRYPT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "{crypt}"}, {"acc": 26, "nme": "USER_PWD", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "userPassword"}, {"acc": 26, "nme": "USER_UID", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "uidNumber"}, {"acc": 26, "nme": "USER_GID", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "gidNumber"}, {"acc": 26, "nme": "GROUP_ID", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "gidNumber"}, {"acc": 26, "nme": "NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "javax.security.auth.login.name"}, {"acc": 26, "nme": "PWD", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "javax.security.auth.login.password"}]}, "classes/com/sun/security/auth/NTSidDomainPrincipal.class": {"ver": 68, "acc": 33, "nme": "com/sun/security/auth/NTSidDomainPrincipal", "super": "com/sun/security/auth/NTSid", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 5247810785821650912}]}}}}