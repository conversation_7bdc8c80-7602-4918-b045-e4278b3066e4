{"md5": "8c7de3f82037fa4a2e8be2a2f13092af", "sha2": "a231e0d844d2721b0fa1b238006d15c6ded6842a", "sha256": "b509448ac506d607319f182537f0b35d71007582ec741832a1f111e5b5b70b38", "contents": {"classes": {"org/apiguardian/api/API$Status.class": {"ver": 50, "acc": 16433, "nme": "org/apiguardian/api/API$Status", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lorg/apiguardian/api/API$Status;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/apiguardian/api/API$Status;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "INTERNAL", "dsc": "Lorg/apiguardian/api/API$Status;"}, {"acc": 16409, "nme": "DEPRECATED", "dsc": "Lorg/apiguardian/api/API$Status;"}, {"acc": 16409, "nme": "EXPERIMENTAL", "dsc": "Lorg/apiguardian/api/API$Status;"}, {"acc": 16409, "nme": "MAINTAINED", "dsc": "Lorg/apiguardian/api/API$Status;"}, {"acc": 16409, "nme": "STABLE", "dsc": "Lorg/apiguardian/api/API$Status;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lorg/apiguardian/api/API$Status;"}]}, "module-info.class": {"ver": 53, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "org/apiguardian/api/API.class": {"ver": 50, "acc": 9729, "nme": "org/apiguardian/api/API", "super": "java/lang/Object", "mthds": [{"nme": "status", "acc": 1025, "dsc": "()Lorg/apiguardian/api/API$Status;"}, {"nme": "since", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "consumers", "acc": 1025, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "CONSTRUCTOR"], ["L<PERSON>va/lang/annotation/ElementType;", "FIELD"], ["L<PERSON>va/lang/annotation/ElementType;", "PACKAGE"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "RUNTIME"]]}, {"dsc": "Ljava/lang/annotation/Documented;"}]}}}}