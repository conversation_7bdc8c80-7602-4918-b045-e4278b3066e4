{"md5": "eafd0331ac93b635939b0733ab9af2de", "sha2": "70939bcfacfecf84bd29a70fe57170a6ff325ae6", "sha256": "a2b77b719b09a14646aa8bd085a2718dceb81b4488168c7c640034e36af0a5f5", "contents": {"classes": {"org/bstats/bukkit/Metrics.class": {"ver": 52, "acc": 33, "nme": "org/bstats/bukkit/Metrics", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lorg/bukkit/plugin/java/JavaPlugin;I)V"}, {"nme": "shutdown", "acc": 1, "dsc": "()V"}, {"nme": "addCustomChart", "acc": 1, "dsc": "(Lorg/bstats/charts/CustomChart;)V"}, {"nme": "appendPlatformData", "acc": 2, "dsc": "(Lorg/bstats/json/JsonObjectBuilder;)V"}, {"nme": "appendServiceData", "acc": 2, "dsc": "(Lorg/bstats/json/JsonObjectBuilder;)V"}, {"nme": "getPlayerAmount", "acc": 2, "dsc": "()I"}, {"nme": "lambda$new$2", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "lambda$new$1", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "lambda$new$0", "acc": 4106, "dsc": "(Lorg/bukkit/plugin/java/JavaPlugin;<PERSON>java/lang/Runnable;)V"}], "flds": [{"acc": 18, "nme": "plugin", "dsc": "Lorg/bukkit/plugin/Plugin;"}, {"acc": 18, "nme": "metricsBase", "dsc": "Lorg/bstats/MetricsBase;"}]}}}}