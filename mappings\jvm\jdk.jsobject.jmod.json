{"md5": "46c5ff109698c63083cabd91d15b4482", "sha2": "020e053f612dd7a6172d9961e214c7d18e674aae", "sha256": "e452f9371055a2bfcdc0e9bee1054cdc47261a1280a0a41c0847b78314e60aba", "contents": {"classes": {"classes/netscape/javascript/JSException.class": {"ver": 68, "acc": 131105, "nme": "netscape/javascript/JSException", "super": "java/lang/RuntimeException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 2778103758223661489}], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "24", "forRemoval", true]}]}, "classes/module-info.class": {"ver": 68, "acc": 32768, "nme": "module-info", "mthds": [], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "24", "forRemoval", true]}]}, "classes/netscape/javascript/JSObject.class": {"ver": 68, "acc": 132129, "nme": "netscape/javascript/JSObject", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "call", "acc": 1153, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["netscape/javascript/JSException"]}, {"nme": "eval", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["netscape/javascript/JSException"]}, {"nme": "getMember", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["netscape/javascript/JSException"]}, {"nme": "setMember", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V", "exs": ["netscape/javascript/JSException"]}, {"nme": "removeMember", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["netscape/javascript/JSException"]}, {"nme": "getSlot", "acc": 1025, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["netscape/javascript/JSException"]}, {"nme": "setSlot", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "exs": ["netscape/javascript/JSException"]}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "24", "forRemoval", true]}]}}}}