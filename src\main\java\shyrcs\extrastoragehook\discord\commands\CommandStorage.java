package shyrcs.extrastoragehook.discord.commands;
import net.dv8tion.jda.api.EmbedBuilder;
import net.dv8tion.jda.api.entities.Message;
import net.dv8tion.jda.api.entities.MessageEmbed;
import net.dv8tion.jda.api.entities.channel.middleman.MessageChannel;
import net.dv8tion.jda.api.events.interaction.command.SlashCommandInteractionEvent;
import net.dv8tion.jda.api.events.interaction.component.ButtonInteractionEvent;
import net.dv8tion.jda.api.events.message.MessageReceivedEvent;
import net.dv8tion.jda.api.interactions.components.ActionRow;
import net.dv8tion.jda.api.interactions.components.buttons.Button;
import org.bukkit.Bukkit;
import org.bukkit.OfflinePlayer;
import org.bukkit.scheduler.BukkitRunnable;
import shyrcs.extrastoragehook.application.Library;
import shyrcs.extrastoragehook.application.PluginBoot;
import shyrcs.extrastoragehook.executor.DiscordExecutor;
import shyrcs.extrastoragehook.SbMagicHook;

import java.awt.*;
import java.text.DecimalFormat;
import java.util.*;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Discord command để xem thông tin kho ExtraStorage với pagination
 */
public class CommandStorage extends DiscordExecutor {

    private static final DecimalFormat formatter = new DecimalFormat("#,###");
    private static final Map<String, PaginationData> paginationCache = new ConcurrentHashMap<>();

    public CommandStorage() {
        super("storage", Library.config.getCommand("storage"));
    }

    /**
     * Class để lưu trữ dữ liệu pagination
     */
    private static class PaginationData {
        final UUID playerUuid;
        final List<ItemData> items;
        final int totalPages;
        int currentPage;

        PaginationData(UUID playerUuid, List<ItemData> items, int currentPage) {
            this.playerUuid = playerUuid;
            this.items = items;
            this.currentPage = currentPage;
            this.totalPages = (int) Math.ceil((double) items.size() / getItemsPerPage());
        }
    }

    /**
     * Class để lưu trữ thông tin item
     */
    private static class ItemData {
        final String materialKey;
        final long quantity;

        ItemData(String materialKey, long quantity) {
            this.materialKey = materialKey;
            this.quantity = quantity;
        }
    }

    private static int getItemsPerPage() {
        return Library.config.getConfig().getInt("extrastorage.display.items-per-page", 10);
    }
    
    @Override
    public void onSlashCommand(SlashCommandInteractionEvent event) {
        final String authorId = Objects.requireNonNull(event.getMember()).getId();

        if (!Library.storage.userConnected(authorId)) {
            event.reply(Library.config.getMessage("account-not-linked"))
                .setEphemeral(true).queue();
            return;
        }

        event.deferReply().queue();

        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    UUID playerUuid = Library.storage.getMinecraftUUID(authorId);
                    if (playerUuid == null) {
                        event.getHook().editOriginal(Library.config.getMessage("account-not-linked")).queue();
                        return;
                    }

                    OfflinePlayer player = Bukkit.getOfflinePlayer(playerUuid);
                    Object storage = Library.extraStorageHook.getStorage(playerUuid);

                    if (storage == null) {
                        event.getHook().editOriginal("❌ Không thể truy cập kho của bạn!").queue();
                        return;
                    }

                    // Tạo pagination data và hiển thị trang đầu tiên
                    List<ItemData> items = getFilteredItems(playerUuid);
                    if (items.isEmpty()) {
                        MessageEmbed embed = buildEmptyStorageEmbed(player);
                        event.getHook().editOriginalEmbeds(embed).queue();
                        return;
                    }

                    PaginationData paginationData = new PaginationData(playerUuid, items, 1);
                    String cacheKey = authorId + "_" + System.currentTimeMillis();
                    paginationCache.put(cacheKey, paginationData);

                    MessageEmbed embed = buildStorageEmbed(player, paginationData, 1);
                    List<ActionRow> components = buildPaginationButtons(cacheKey, 1, paginationData.totalPages);

                    event.getHook().editOriginalEmbeds(embed).setComponents(components).queue();

                } catch (Exception e) {
                    event.getHook().editOriginal(Library.config.getMessage("error")).queue();
                }
            }
        }.runTaskAsynchronously(PluginBoot.main);
    }
    
    @Override
    public void onChatCommand(MessageReceivedEvent event) {
        final MessageChannel channel = event.getChannel();
        final Message message = event.getMessage();
        final String authorId = Objects.requireNonNull(event.getMember()).getId();

        if (!Library.storage.userConnected(authorId)) {
            channel.sendMessage(Library.config.getMessage("account-not-linked"))
                .setMessageReference(message).queue();
            return;
        }

        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    UUID playerUuid = Library.storage.getMinecraftUUID(authorId);
                    if (playerUuid == null) {
                        channel.sendMessage(Library.config.getMessage("account-not-linked"))
                            .setMessageReference(message).queue();
                        return;
                    }

                    OfflinePlayer player = Bukkit.getOfflinePlayer(playerUuid);
                    Object storage = Library.extraStorageHook.getStorage(playerUuid);

                    if (storage == null) {
                        channel.sendMessage("❌ Không thể truy cập kho của bạn!")
                            .setMessageReference(message).queue();
                        return;
                    }

                    // Tạo pagination data và hiển thị trang đầu tiên với buttons
                    List<ItemData> items = getFilteredItems(playerUuid);
                    if (items.isEmpty()) {
                        MessageEmbed embed = buildEmptyStorageEmbed(player);
                        channel.sendMessageEmbeds(embed).setMessageReference(message).queue();
                        return;
                    }

                    PaginationData paginationData = new PaginationData(playerUuid, items, 1);
                    String cacheKey = authorId + "_" + System.currentTimeMillis();
                    paginationCache.put(cacheKey, paginationData);

                    MessageEmbed embed = buildStorageEmbed(player, paginationData, 1);
                    List<ActionRow> components = buildPaginationButtons(cacheKey, 1, paginationData.totalPages);

                    channel.sendMessageEmbeds(embed).setComponents(components).setMessageReference(message).queue();

                } catch (Exception e) {
                    channel.sendMessage(Library.config.getMessage("error"))
                        .setMessageReference(message).queue();
                }
            }
        }.runTaskAsynchronously(PluginBoot.main);
    }
    
    /**
     * Lấy danh sách items đã được filter theo config
     */
    private List<ItemData> getFilteredItems(UUID playerUuid) {
        List<ItemData> items = new ArrayList<>();
        Map<String, Object> allItems = Library.extraStorageHook.getAllItems(playerUuid);

        if (allItems == null || allItems.isEmpty()) {
            return items;
        }

        // Lấy filter từ config
        List<String> itemFilter = Library.config.getConfig().getStringList("extrastorage.display.item-filter");

        for (Map.Entry<String, Object> entry : allItems.entrySet()) {
            String materialKey = entry.getKey();

            try {
                long quantity = Library.extraStorageHook.getItemAmount(playerUuid, materialKey);

                if (quantity > 0) {
                    // Nếu có filter và item không trong filter thì bỏ qua
                    if (!itemFilter.isEmpty() && !itemFilter.contains(materialKey)) {
                        continue;
                    }

                    items.add(new ItemData(materialKey, quantity));
                }
            } catch (Exception e) {
                // Bỏ qua item có lỗi
                continue;
            }
        }

        // Sắp xếp theo tên material
        items.sort(Comparator.comparing(item -> item.materialKey));

        return items;
    }

    /**
     * Tạo embed khi kho trống
     */
    private MessageEmbed buildEmptyStorageEmbed(OfflinePlayer player) {
        EmbedBuilder embed = new EmbedBuilder();
        embed.setTitle("📦 Thông tin kho ExtraStorage");
        embed.setColor(Color.BLUE);

        embed.addField("👤 Người chơi", player.getName(), true);
        embed.addField("📋 Items", Library.config.getMessage("no-items"), false);

        embed.setFooter("SbMagicHook • ExtraStorage Integration");
        embed.setTimestamp(java.time.Instant.now());

        return embed.build();
    }
    
    /**
     * Tạo embed hiển thị thông tin kho với pagination
     */
    private MessageEmbed buildStorageEmbed(OfflinePlayer player, PaginationData paginationData, int page) {
        EmbedBuilder embed = new EmbedBuilder();
        embed.setTitle("📦 Thông tin kho ExtraStorage");
        embed.setColor(Color.BLUE);

        // Thông tin người chơi
        embed.addField("👤 Người chơi", player.getName(), true);
        boolean isActive = Library.extraStorageHook.getStorageStatus(paginationData.playerUuid);
        embed.addField("🔄 Trạng thái", isActive ? "✅ Hoạt động" : "❌ Tắt", true);

        // Thông tin không gian
        long totalSpace = Library.extraStorageHook.getStorageSpace(paginationData.playerUuid);
        long usedSpace = Library.extraStorageHook.getUsedSpace(paginationData.playerUuid);
        long freeSpace = Library.extraStorageHook.getFreeSpace(paginationData.playerUuid);

        if (totalSpace == -1) {
            embed.addField("💾 Không gian", "♾️ Không giới hạn", true);
        } else {
            String spaceInfo = String.format("**%s** / **%s** (%s còn trống)",
                formatter.format(usedSpace),
                formatter.format(totalSpace),
                formatter.format(freeSpace)
            );
            embed.addField("💾 Không gian", spaceInfo, true);
        }

        // Danh sách items cho trang hiện tại
        StringBuilder itemList = new StringBuilder();
        int itemsPerPage = getItemsPerPage();
        int startIndex = (page - 1) * itemsPerPage;
        int endIndex = Math.min(startIndex + itemsPerPage, paginationData.items.size());

        for (int i = startIndex; i < endIndex; i++) {
            ItemData item = paginationData.items.get(i);
            String emote = Library.config.getEmote(item.materialKey);
            String displayName = formatMaterialName(item.materialKey);
            itemList.append(emote).append(" **").append(displayName).append("**: ")
                .append(formatter.format(item.quantity)).append("\n");
        }

        if (itemList.length() == 0) {
            embed.addField("📋 Items", Library.config.getMessage("no-items"), false);
        } else {
            String title = String.format("📋 Items (%d)", paginationData.items.size());
            embed.addField(title, itemList.toString(), false);
        }

        // Footer với thông tin trang
        String footerText = String.format("SbMagicHook • ExtraStorage Integration • Trang %d/%d",
            page, paginationData.totalPages);
        embed.setFooter(footerText);
        embed.setTimestamp(java.time.Instant.now());

        return embed.build();
    }

    /**
     * Tạo buttons cho pagination
     */
    private List<ActionRow> buildPaginationButtons(String cacheKey, int currentPage, int totalPages) {
        List<Button> buttons = new ArrayList<>();

        // Previous button
        Button prevButton = Button.secondary("storage_prev_" + cacheKey, "◀️ Trang trước")
            .withDisabled(currentPage <= 1);
        buttons.add(prevButton);

        // Refresh button
        Button refreshButton = Button.primary("storage_refresh_" + cacheKey, "🔄 Làm mới");
        buttons.add(refreshButton);

        // Next button
        Button nextButton = Button.secondary("storage_next_" + cacheKey, "Trang sau ▶️")
            .withDisabled(currentPage >= totalPages);
        buttons.add(nextButton);

        return Collections.singletonList(ActionRow.of(buttons));
    }

    /**
     * Xử lý button interactions
     */
    public static void handleButtonInteraction(ButtonInteractionEvent event) {
        String buttonId = event.getComponentId();

        if (!buttonId.startsWith("storage_")) {
            return;
        }

        String[] parts = buttonId.split("_", 3);
        if (parts.length != 3) {
            return;
        }

        String action = parts[1];
        String cacheKey = parts[2];

        PaginationData paginationData = paginationCache.get(cacheKey);
        if (paginationData == null) {
            event.reply("❌ Dữ liệu đã hết hạn! Vui lòng sử dụng lại lệnh.").setEphemeral(true).queue();
            return;
        }

        // Kiểm tra quyền sử dụng button
        String authorId = event.getUser().getId();
        if (!cacheKey.startsWith(authorId + "_")) {
            event.reply("❌ Bạn không có quyền sử dụng button này!").setEphemeral(true).queue();
            return;
        }

        int newPage = paginationData.currentPage;

        switch (action) {
            case "prev":
                if (paginationData.currentPage > 1) {
                    newPage = paginationData.currentPage - 1;
                }
                break;
            case "next":
                if (paginationData.currentPage < paginationData.totalPages) {
                    newPage = paginationData.currentPage + 1;
                }
                break;
            case "refresh":
                // Làm mới dữ liệu
                newPage = paginationData.currentPage;
                break;
        }

        paginationData.currentPage = newPage;

        // Cập nhật embed và buttons
        OfflinePlayer player = Bukkit.getOfflinePlayer(paginationData.playerUuid);
        CommandStorage storage = new CommandStorage();
        MessageEmbed embed = storage.buildStorageEmbed(player, paginationData, newPage);
        List<ActionRow> components = storage.buildPaginationButtons(cacheKey, newPage, paginationData.totalPages);

        event.editMessageEmbeds(embed).setComponents(components).queue();
    }

    /**
     * Format tên material để hiển thị đẹp hơn
     */
    private String formatMaterialName(String materialKey) {
        String[] words = materialKey.toLowerCase().replace("_", " ").split(" ");
        StringBuilder result = new StringBuilder();
        for (String word : words) {
            if (word.length() > 0) {
                result.append(Character.toUpperCase(word.charAt(0)))
                      .append(word.substring(1))
                      .append(" ");
            }
        }
        return result.toString().trim();
    }
}
