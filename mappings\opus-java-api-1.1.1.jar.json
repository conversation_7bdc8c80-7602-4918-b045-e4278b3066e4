{"md5": "ea6c7917563db24e91b4358afb20ee0c", "sha2": "63c6910fb1f7eebbfd922a01d37a4b94fa0ee5e3", "sha256": "7cd818482722cd2809b14aaed1856f084838f90493b69d1b819868c778466cfb", "contents": {"classes": {"tomp2p/opuswrapper/Opus$OpusCustomMode.class": {"ver": 52, "acc": 33, "nme": "tomp2p/opuswrapper/Opus$OpusCustomMode", "super": "com/sun/jna/PointerType", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/sun/jna/Pointer;)V"}, {"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "tomp2p/opuswrapper/Opus$OpusMSEncoder.class": {"ver": 52, "acc": 33, "nme": "tomp2p/opuswrapper/Opus$OpusMSEncoder", "super": "com/sun/jna/PointerType", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/sun/jna/Pointer;)V"}, {"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "tomp2p/opuswrapper/Opus$OpusCustomDecoder.class": {"ver": 52, "acc": 33, "nme": "tomp2p/opuswrapper/Opus$OpusCustomDecoder", "super": "com/sun/jna/PointerType", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/sun/jna/Pointer;)V"}, {"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "tomp2p/opuswrapper/Opus$OpusMSDecoder.class": {"ver": 52, "acc": 33, "nme": "tomp2p/opuswrapper/Opus$OpusMSDecoder", "super": "com/sun/jna/PointerType", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/sun/jna/Pointer;)V"}, {"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "tomp2p/opuswrapper/Opus$OpusEncoder.class": {"ver": 52, "acc": 33, "nme": "tomp2p/opuswrapper/Opus$OpusEncoder", "super": "com/sun/jna/PointerType", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/sun/jna/Pointer;)V"}, {"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "tomp2p/opuswrapper/Opus.class": {"ver": 52, "acc": 1537, "nme": "tomp2p/opuswrapper/Opus", "super": "java/lang/Object", "mthds": [{"nme": "opus_encoder_get_size", "acc": 1025, "dsc": "(I)I"}, {"nme": "opus_encoder_create", "acc": 1025, "dsc": "(IIILjava/nio/IntBuffer;)Lcom/sun/jna/ptr/PointerByReference;"}, {"nme": "opus_encoder_init", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;III)I"}, {"nme": "opus_encode", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;Ljava/nio/ShortBuffer;ILjava/nio/ByteBuffer;I)I"}, {"nme": "opus_encode", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;Lcom/sun/jna/ptr/ShortByReference;ILcom/sun/jna/Pointer;I)I"}, {"nme": "opus_encode_float", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;[FILjava/nio/ByteBuffer;I)I"}, {"nme": "opus_encode_float", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;Lcom/sun/jna/ptr/FloatByReference;ILcom/sun/jna/Pointer;I)I"}, {"nme": "opus_encoder_destroy", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;)V"}, {"nme": "opus_encoder_ctl", "acc": 1153, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;I[<PERSON>ja<PERSON>/lang/Object;)I"}, {"nme": "opus_decoder_get_size", "acc": 1025, "dsc": "(I)I"}, {"nme": "opus_decoder_create", "acc": 1025, "dsc": "(IILjava/nio/IntBuffer;)Lcom/sun/jna/ptr/PointerByReference;"}, {"nme": "opus_decoder_init", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;II)I"}, {"nme": "opus_decode", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;[B<PERSON>java/nio/ShortBuffer;II)I"}, {"nme": "opus_decode", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;Lcom/sun/jna/Pointer;ILcom/sun/jna/ptr/ShortByReference;II)I"}, {"nme": "opus_decode_float", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;[BILjava/nio/FloatBuffer;II)I"}, {"nme": "opus_decode_float", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;Lcom/sun/jna/Pointer;ILcom/sun/jna/ptr/FloatByReference;II)I"}, {"nme": "opus_decoder_ctl", "acc": 1153, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;I[<PERSON>ja<PERSON>/lang/Object;)I"}, {"nme": "opus_decoder_destroy", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;)V"}, {"nme": "opus_packet_parse", "acc": 1025, "dsc": "([<PERSON><PERSON><PERSON><PERSON>/nio/ByteBuffer;[B<PERSON><PERSON><PERSON>/nio/ShortBuffer;<PERSON><PERSON><PERSON>/nio/IntBuffer;)I"}, {"nme": "opus_packet_get_bandwidth", "acc": 1025, "dsc": "([B)I"}, {"nme": "opus_packet_get_samples_per_frame", "acc": 1025, "dsc": "([BI)I"}, {"nme": "opus_packet_get_nb_channels", "acc": 1025, "dsc": "([B)I"}, {"nme": "opus_packet_get_nb_frames", "acc": 1025, "dsc": "([BI)I"}, {"nme": "opus_packet_get_nb_samples", "acc": 1025, "dsc": "([BII)I"}, {"nme": "opus_decoder_get_nb_samples", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;[BI)I"}, {"nme": "opus_decoder_get_nb_samples", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;Lcom/sun/jna/Pointer;I)I"}, {"nme": "opus_pcm_soft_clip", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/nio/FloatBuffer;IILjava/nio/FloatBuffer;)V"}, {"nme": "opus_repacketizer_get_size", "acc": 1025, "dsc": "()I"}, {"nme": "opus_repacketizer_init", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;)Lcom/sun/jna/ptr/PointerByReference;"}, {"nme": "opus_repacketizer_create", "acc": 1025, "dsc": "()Lcom/sun/jna/ptr/PointerByReference;"}, {"nme": "opus_repacketizer_destroy", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;)V"}, {"nme": "opus_repacketizer_cat", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;[BI)I"}, {"nme": "opus_repacketizer_cat", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;Lcom/sun/jna/Pointer;I)I"}, {"nme": "opus_repacketizer_out_range", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;IILjava/nio/ByteBuffer;I)I"}, {"nme": "opus_repacketizer_out_range", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;IILcom/sun/jna/Pointer;I)I"}, {"nme": "opus_repacketizer_get_nb_frames", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;)I"}, {"nme": "opus_repacketizer_out", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;<PERSON><PERSON>va/nio/<PERSON><PERSON><PERSON>er;I)I"}, {"nme": "opus_repacketizer_out", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;Lcom/sun/jna/Pointer;I)I"}, {"nme": "opus_packet_pad", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/nio/<PERSON>;II)I"}, {"nme": "opus_packet_unpad", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/nio/<PERSON>;<PERSON>)I"}, {"nme": "opus_multistream_packet_pad", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/nio/<PERSON>;III)I"}, {"nme": "opus_multistream_packet_unpad", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/nio/<PERSON>;II)I"}, {"nme": "opus_strerror", "acc": 1025, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "opus_get_version_string", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "opus_multistream_encoder_get_size", "acc": 1025, "dsc": "(II)I"}, {"nme": "opus_multistream_surround_encoder_get_size", "acc": 1025, "dsc": "(II)I"}, {"nme": "opus_multistream_encoder_create", "acc": 1025, "dsc": "(IIII[B<PERSON>java/nio/IntBuffer;)Lcom/sun/jna/ptr/PointerByReference;"}, {"nme": "opus_multistream_surround_encoder_create", "acc": 1025, "dsc": "(IIILjava/nio/IntBuffer;<PERSON><PERSON><PERSON>/nio/IntBuffer;<PERSON><PERSON><PERSON>/nio/ByteBuffer;ILjava/nio/IntBuffer;)Lcom/sun/jna/ptr/PointerByReference;"}, {"nme": "opus_multistream_encoder_init", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;IIII[BI)I"}, {"nme": "opus_multistream_encoder_init", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;IIIILcom/sun/jna/Pointer;I)I"}, {"nme": "opus_multistream_surround_encoder_init", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;IIILjava/nio/IntBuffer;<PERSON><PERSON><PERSON>/nio/IntBuffer;<PERSON><PERSON><PERSON>/nio/ByteBuffer;I)I"}, {"nme": "opus_multistream_surround_encoder_init", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;IIILcom/sun/jna/ptr/IntByReference;Lcom/sun/jna/ptr/IntByReference;Lcom/sun/jna/Pointer;I)I"}, {"nme": "opus_multistream_encode", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;Ljava/nio/ShortBuffer;ILjava/nio/ByteBuffer;I)I"}, {"nme": "opus_multistream_encode", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;Lcom/sun/jna/ptr/ShortByReference;ILcom/sun/jna/Pointer;I)I"}, {"nme": "opus_multistream_encode_float", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;[FILjava/nio/ByteBuffer;I)I"}, {"nme": "opus_multistream_encode_float", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;Lcom/sun/jna/ptr/FloatByReference;ILcom/sun/jna/Pointer;I)I"}, {"nme": "opus_multistream_encoder_destroy", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;)V"}, {"nme": "opus_multistream_encoder_ctl", "acc": 1153, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;I[<PERSON>ja<PERSON>/lang/Object;)I"}, {"nme": "opus_multistream_decoder_get_size", "acc": 1025, "dsc": "(II)I"}, {"nme": "opus_multistream_decoder_create", "acc": 1025, "dsc": "(IIII[B<PERSON><PERSON><PERSON>/nio/IntBuffer;)Lcom/sun/jna/ptr/PointerByReference;"}, {"nme": "opus_multistream_decoder_init", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;IIII[B)I"}, {"nme": "opus_multistream_decoder_init", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;IIIILcom/sun/jna/Pointer;)I"}, {"nme": "opus_multistream_decode", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;[B<PERSON>java/nio/ShortBuffer;II)I"}, {"nme": "opus_multistream_decode", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;Lcom/sun/jna/Pointer;ILcom/sun/jna/ptr/ShortByReference;II)I"}, {"nme": "opus_multistream_decode_float", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;[BILjava/nio/FloatBuffer;II)I"}, {"nme": "opus_multistream_decode_float", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;Lcom/sun/jna/Pointer;ILcom/sun/jna/ptr/FloatByReference;II)I"}, {"nme": "opus_multistream_decoder_ctl", "acc": 1153, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;I[<PERSON>ja<PERSON>/lang/Object;)I"}, {"nme": "opus_multistream_decoder_destroy", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;)V"}, {"nme": "opus_custom_mode_create", "acc": 1025, "dsc": "(IILjava/nio/IntBuffer;)Lcom/sun/jna/ptr/PointerByReference;"}, {"nme": "opus_custom_mode_destroy", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;)V"}, {"nme": "opus_custom_encoder_get_size", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;I)I"}, {"nme": "opus_custom_encoder_create", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;ILjava/nio/IntBuffer;)Lcom/sun/jna/ptr/PointerByReference;"}, {"nme": "opus_custom_encoder_create", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;ILcom/sun/jna/ptr/IntByReference;)Lcom/sun/jna/ptr/PointerByReference;"}, {"nme": "opus_custom_encoder_destroy", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;)V"}, {"nme": "opus_custom_encode_float", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;[FILjava/nio/ByteBuffer;I)I"}, {"nme": "opus_custom_encode_float", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;Lcom/sun/jna/ptr/FloatByReference;ILcom/sun/jna/Pointer;I)I"}, {"nme": "opus_custom_encode", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;Ljava/nio/ShortBuffer;ILjava/nio/ByteBuffer;I)I"}, {"nme": "opus_custom_encode", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;Lcom/sun/jna/ptr/ShortByReference;ILcom/sun/jna/Pointer;I)I"}, {"nme": "opus_custom_encoder_ctl", "acc": 1153, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;I[<PERSON>ja<PERSON>/lang/Object;)I"}, {"nme": "opus_custom_decoder_get_size", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;I)I"}, {"nme": "opus_custom_decoder_init", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;Lcom/sun/jna/ptr/PointerByReference;I)I"}, {"nme": "opus_custom_decoder_create", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;ILjava/nio/IntBuffer;)Lcom/sun/jna/ptr/PointerByReference;"}, {"nme": "opus_custom_decoder_create", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;ILcom/sun/jna/ptr/IntByReference;)Lcom/sun/jna/ptr/PointerByReference;"}, {"nme": "opus_custom_decoder_destroy", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;)V"}, {"nme": "opus_custom_decode_float", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;[BILjava/nio/FloatBuffer;I)I"}, {"nme": "opus_custom_decode_float", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;Lcom/sun/jna/Pointer;ILcom/sun/jna/ptr/FloatByReference;I)I"}, {"nme": "opus_custom_decode", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;[B<PERSON>java/nio/ShortBuffer;I)I"}, {"nme": "opus_custom_decode", "acc": 1025, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;Lcom/sun/jna/Pointer;ILcom/sun/jna/ptr/ShortByReference;I)I"}, {"nme": "opus_custom_decoder_ctl", "acc": 1153, "dsc": "(Lcom/sun/jna/ptr/PointerByReference;I[<PERSON>ja<PERSON>/lang/Object;)I"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "INSTANCE", "dsc": "Ltomp2p/opuswrapper/Opus;"}, {"acc": 25, "nme": "OPUS_GET_LSB_DEPTH_REQUEST", "dsc": "I", "val": 4037}, {"acc": 25, "nme": "OPUS_GET_APPLICATION_REQUEST", "dsc": "I", "val": 4001}, {"acc": 25, "nme": "OPUS_GET_FORCE_CHANNELS_REQUEST", "dsc": "I", "val": 4023}, {"acc": 25, "nme": "OPUS_GET_VBR_REQUEST", "dsc": "I", "val": 4007}, {"acc": 25, "nme": "OPUS_GET_BANDWIDTH_REQUEST", "dsc": "I", "val": 4009}, {"acc": 25, "nme": "OPUS_SET_BITRATE_REQUEST", "dsc": "I", "val": 4002}, {"acc": 25, "nme": "OPUS_SET_BANDWIDTH_REQUEST", "dsc": "I", "val": 4008}, {"acc": 25, "nme": "OPUS_SIGNAL_MUSIC", "dsc": "I", "val": 3002}, {"acc": 25, "nme": "OPUS_RESET_STATE", "dsc": "I", "val": 4028}, {"acc": 25, "nme": "OPUS_FRAMESIZE_2_5_MS", "dsc": "I", "val": 5001}, {"acc": 25, "nme": "OPUS_GET_COMPLEXITY_REQUEST", "dsc": "I", "val": 4011}, {"acc": 25, "nme": "OPUS_FRAMESIZE_40_MS", "dsc": "I", "val": 5005}, {"acc": 25, "nme": "OPUS_SET_PACKET_LOSS_PERC_REQUEST", "dsc": "I", "val": 4014}, {"acc": 25, "nme": "OPUS_GET_VBR_CONSTRAINT_REQUEST", "dsc": "I", "val": 4021}, {"acc": 25, "nme": "OPUS_SET_INBAND_FEC_REQUEST", "dsc": "I", "val": 4012}, {"acc": 25, "nme": "OPUS_APPLICATION_RESTRICTED_LOWDELAY", "dsc": "I", "val": 2051}, {"acc": 25, "nme": "OPUS_BANDWIDTH_FULLBAND", "dsc": "I", "val": 1105}, {"acc": 25, "nme": "OPUS_SET_VBR_REQUEST", "dsc": "I", "val": 4006}, {"acc": 25, "nme": "OPUS_BANDWIDTH_SUPERWIDEBAND", "dsc": "I", "val": 1104}, {"acc": 25, "nme": "OPUS_SET_FORCE_CHANNELS_REQUEST", "dsc": "I", "val": 4022}, {"acc": 25, "nme": "OPUS_APPLICATION_VOIP", "dsc": "I", "val": 2048}, {"acc": 25, "nme": "OPUS_SIGNAL_VOICE", "dsc": "I", "val": 3001}, {"acc": 25, "nme": "OPUS_GET_FINAL_RANGE_REQUEST", "dsc": "I", "val": 4031}, {"acc": 25, "nme": "OPUS_BUFFER_TOO_SMALL", "dsc": "I", "val": -2}, {"acc": 25, "nme": "OPUS_SET_COMPLEXITY_REQUEST", "dsc": "I", "val": 4010}, {"acc": 25, "nme": "OPUS_FRAMESIZE_ARG", "dsc": "I", "val": 5000}, {"acc": 25, "nme": "OPUS_GET_LOOKAHEAD_REQUEST", "dsc": "I", "val": 4027}, {"acc": 25, "nme": "OPUS_GET_INBAND_FEC_REQUEST", "dsc": "I", "val": 4013}, {"acc": 25, "nme": "OPUS_BITRATE_MAX", "dsc": "I", "val": -1}, {"acc": 25, "nme": "OPUS_FRAMESIZE_5_MS", "dsc": "I", "val": 5002}, {"acc": 25, "nme": "OPUS_BAD_ARG", "dsc": "I", "val": -1}, {"acc": 25, "nme": "OPUS_GET_PITCH_REQUEST", "dsc": "I", "val": 4033}, {"acc": 25, "nme": "OPUS_SET_SIGNAL_REQUEST", "dsc": "I", "val": 4024}, {"acc": 25, "nme": "OPUS_FRAMESIZE_20_MS", "dsc": "I", "val": 5004}, {"acc": 25, "nme": "OPUS_APPLICATION_AUDIO", "dsc": "I", "val": 2049}, {"acc": 25, "nme": "OPUS_GET_DTX_REQUEST", "dsc": "I", "val": 4017}, {"acc": 25, "nme": "OPUS_FRAMESIZE_10_MS", "dsc": "I", "val": 5003}, {"acc": 25, "nme": "OPUS_SET_LSB_DEPTH_REQUEST", "dsc": "I", "val": 4036}, {"acc": 25, "nme": "OPUS_UNIMPLEMENTED", "dsc": "I", "val": -5}, {"acc": 25, "nme": "OPUS_GET_PACKET_LOSS_PERC_REQUEST", "dsc": "I", "val": 4015}, {"acc": 25, "nme": "OPUS_INVALID_STATE", "dsc": "I", "val": -6}, {"acc": 25, "nme": "OPUS_SET_EXPERT_FRAME_DURATION_REQUEST", "dsc": "I", "val": 4040}, {"acc": 25, "nme": "OPUS_FRAMESIZE_60_MS", "dsc": "I", "val": 5006}, {"acc": 25, "nme": "OPUS_GET_BITRATE_REQUEST", "dsc": "I", "val": 4003}, {"acc": 25, "nme": "OPUS_INTERNAL_ERROR", "dsc": "I", "val": -3}, {"acc": 25, "nme": "OPUS_SET_MAX_BANDWIDTH_REQUEST", "dsc": "I", "val": 4004}, {"acc": 25, "nme": "OPUS_SET_VBR_CONSTRAINT_REQUEST", "dsc": "I", "val": 4020}, {"acc": 25, "nme": "OPUS_GET_MAX_BANDWIDTH_REQUEST", "dsc": "I", "val": 4005}, {"acc": 25, "nme": "OPUS_BANDWIDTH_NARROWBAND", "dsc": "I", "val": 1101}, {"acc": 25, "nme": "OPUS_SET_GAIN_REQUEST", "dsc": "I", "val": 4034}, {"acc": 25, "nme": "OPUS_SET_PREDICTION_DISABLED_REQUEST", "dsc": "I", "val": 4042}, {"acc": 25, "nme": "OPUS_SET_APPLICATION_REQUEST", "dsc": "I", "val": 4000}, {"acc": 25, "nme": "OPUS_SET_DTX_REQUEST", "dsc": "I", "val": 4016}, {"acc": 25, "nme": "OPUS_BANDWIDTH_MEDIUMBAND", "dsc": "I", "val": 1102}, {"acc": 25, "nme": "OPUS_GET_SAMPLE_RATE_REQUEST", "dsc": "I", "val": 4029}, {"acc": 25, "nme": "OPUS_GET_EXPERT_FRAME_DURATION_REQUEST", "dsc": "I", "val": 4041}, {"acc": 25, "nme": "OPUS_AUTO", "dsc": "I", "val": -1000}, {"acc": 25, "nme": "OPUS_GET_SIGNAL_REQUEST", "dsc": "I", "val": 4025}, {"acc": 25, "nme": "OPUS_GET_LAST_PACKET_DURATION_REQUEST", "dsc": "I", "val": 4039}, {"acc": 25, "nme": "OPUS_GET_PREDICTION_DISABLED_REQUEST", "dsc": "I", "val": 4043}, {"acc": 25, "nme": "OPUS_GET_GAIN_REQUEST", "dsc": "I", "val": 4045}, {"acc": 25, "nme": "OPUS_BANDWIDTH_WIDEBAND", "dsc": "I", "val": 1103}, {"acc": 25, "nme": "OPUS_INVALID_PACKET", "dsc": "I", "val": -4}, {"acc": 25, "nme": "OPUS_ALLOC_FAIL", "dsc": "I", "val": -7}, {"acc": 25, "nme": "OPUS_OK", "dsc": "I", "val": 0}, {"acc": 25, "nme": "OPUS_MULTISTREAM_GET_DECODER_STATE_REQUEST", "dsc": "I", "val": 5122}, {"acc": 25, "nme": "OPUS_MULTISTREAM_GET_ENCODER_STATE_REQUEST", "dsc": "I", "val": 5120}]}, "club/minnced/opus/util/NativeUtil.class": {"ver": 52, "acc": 33, "nme": "club/minnced/opus/util/NativeUtil", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "loadLibraryFromJar", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}], "flds": []}, "tomp2p/opuswrapper/Opus$OpusRepacketizer.class": {"ver": 52, "acc": 33, "nme": "tomp2p/opuswrapper/Opus$OpusRepacketizer", "super": "com/sun/jna/PointerType", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/sun/jna/Pointer;)V"}, {"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "tomp2p/opuswrapper/Opus$OpusCustomEncoder.class": {"ver": 52, "acc": 33, "nme": "tomp2p/opuswrapper/Opus$OpusCustomEncoder", "super": "com/sun/jna/PointerType", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/sun/jna/Pointer;)V"}, {"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "club/minnced/opus/util/OpusLibrary.class": {"ver": 52, "acc": 49, "nme": "club/minnced/opus/util/OpusLibrary", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getSupportedPlatforms", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "isSupportedPlatform", "acc": 9, "dsc": "()Z"}, {"nme": "isInitialized", "acc": 41, "dsc": "()Z"}, {"nme": "loadFrom", "acc": 41, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "loadFromJar", "acc": 41, "dsc": "()Z", "exs": ["java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 10, "nme": "initialized", "dsc": "Z"}, {"acc": 26, "nme": "SUPPORTED_SYSTEMS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "platforms", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}]}, "tomp2p/opuswrapper/Opus$OpusDecoder.class": {"ver": 52, "acc": 33, "nme": "tomp2p/opuswrapper/Opus$OpusDecoder", "super": "com/sun/jna/PointerType", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/sun/jna/Pointer;)V"}, {"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}}}}