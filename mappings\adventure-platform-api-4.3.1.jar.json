{"md5": "44db3caceefbd9daa37625afb2ea8a5d", "sha2": "50847f49cb181fdafd0de37459bbe0fb8b1eb972", "sha256": "ec604628c2b7c165ea74c1fcb3a2d0f03359c2c77a14960e3a0bc2e379670ac2", "contents": {"classes": {"net/kyori/adventure/platform/AudienceProvider.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/platform/AudienceProvider", "super": "java/lang/Object", "mthds": [{"nme": "all", "acc": 1025, "dsc": "()Lnet/kyori/adventure/audience/Audience;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "console", "acc": 1025, "dsc": "()Lnet/kyori/adventure/audience/Audience;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "players", "acc": 1025, "dsc": "()Lnet/kyori/adventure/audience/Audience;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "player", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/UUID;)Lnet/kyori/adventure/audience/Audience;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "permission", "acc": 1, "dsc": "(Lnet/kyori/adventure/key/Key;)Lnet/kyori/adventure/audience/Audience;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "permission", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/audience/Audience;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "world", "acc": 1025, "dsc": "(Lnet/kyori/adventure/key/Key;)Lnet/kyori/adventure/audience/Audience;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "server", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lnet/kyori/adventure/audience/Audience;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "flattener", "acc": 1025, "dsc": "()Lnet/kyori/adventure/text/flattener/ComponentFlattener;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "close", "acc": 1025, "dsc": "()V"}], "flds": []}, "net/kyori/adventure/platform/AudienceProvider$Builder.class": {"ver": 52, "acc": 1537, "nme": "net/kyori/adventure/platform/AudienceProvider$Builder", "super": "java/lang/Object", "mthds": [{"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(Lnet/kyori/adventure/text/renderer/ComponentRenderer;)Lnet/kyori/adventure/platform/AudienceProvider$Builder;", "sig": "(Lnet/kyori/adventure/text/renderer/ComponentRenderer<Lnet/kyori/adventure/pointer/Pointered;>;)TB;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "partition", "acc": 1025, "dsc": "(Ljava/util/function/Function;)Lnet/kyori/adventure/platform/AudienceProvider$Builder;", "sig": "(Ljava/util/function/Function<Lnet/kyori/adventure/pointer/Pointered;*>;)TB;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Ljava/util/function/Function;Lnet/kyori/adventure/text/renderer/ComponentRenderer;)Lnet/kyori/adventure/platform/AudienceProvider$Builder;", "sig": "<T:Ljava/lang/Object;>(Ljava/util/function/Function<Lnet/kyori/adventure/pointer/Pointered;TT;>;Lnet/kyori/adventure/text/renderer/ComponentRenderer<TT;>;)TB;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "build", "acc": 1025, "dsc": "()Lnet/kyori/adventure/platform/AudienceProvider;", "sig": "()TP;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}], "flds": []}}}}