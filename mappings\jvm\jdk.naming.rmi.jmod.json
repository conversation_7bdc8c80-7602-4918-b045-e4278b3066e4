{"md5": "ecad2c85492a6d705775c2c0c3dfc2c2", "sha2": "7981d5736706befb5fcba737d99b689074e0b7ae", "sha256": "8586dfaaa48f659538e9f260e043308cc6d42d2578761956bded7e65a27f40a9", "contents": {"classes": {"classes/com/sun/jndi/url/rmi/rmiURLContext.class": {"ver": 68, "acc": 33, "nme": "com/sun/jndi/url/rmi/rmiURLContext", "super": "com/sun/jndi/toolkit/url/GenericURLContext", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Hashtable;)V", "sig": "(<PERSON><PERSON><PERSON>/util/Hashtable<**>;)V"}, {"nme": "getRootURLContext", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Hashtable;)Ljavax/naming/spi/ResolveResult;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/util/Hashtable<**>;)Ljavax/naming/spi/ResolveResult;", "exs": ["javax/naming/NamingException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "PARSE_MODE_PROP", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.jndi.rmiURLParsing"}, {"acc": 26, "nme": "DEFAULT_PARSE_MODE", "dsc": "Lcom/sun/jndi/toolkit/url/Uri$ParseMode;"}, {"acc": 25, "nme": "PARSE_MODE", "dsc": "Lcom/sun/jndi/toolkit/url/Uri$ParseMode;"}]}, "classes/com/sun/jndi/rmi/registry/RegistryContext.class": {"ver": 68, "acc": 33, "nme": "com/sun/jndi/rmi/registry/RegistryContext", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Hashtable;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>java/util/Hashtable<**>;)V", "exs": ["javax/naming/NamingException"]}, {"nme": "<init>", "acc": 0, "dsc": "(Lcom/sun/jndi/rmi/registry/RegistryContext;)V"}, {"nme": "finalize", "acc": 4, "dsc": "()V"}, {"nme": "lookup", "acc": 1, "dsc": "(Ljavax/naming/Name;)Ljava/lang/Object;", "exs": ["javax/naming/NamingException"]}, {"nme": "lookup", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["javax/naming/NamingException"]}, {"nme": "bind", "acc": 1, "dsc": "(Ljavax/naming/Name;Ljava/lang/Object;)V", "exs": ["javax/naming/NamingException"]}, {"nme": "bind", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V", "exs": ["javax/naming/NamingException"]}, {"nme": "rebind", "acc": 1, "dsc": "(Ljavax/naming/Name;Ljava/lang/Object;)V", "exs": ["javax/naming/NamingException"]}, {"nme": "rebind", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V", "exs": ["javax/naming/NamingException"]}, {"nme": "unbind", "acc": 1, "dsc": "(Ljavax/naming/Name;)V", "exs": ["javax/naming/NamingException"]}, {"nme": "unbind", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["javax/naming/NamingException"]}, {"nme": "rename", "acc": 1, "dsc": "(Ljavax/naming/Name;Ljavax/naming/Name;)V", "exs": ["javax/naming/NamingException"]}, {"nme": "rename", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["javax/naming/NamingException"]}, {"nme": "list", "acc": 1, "dsc": "(Ljavax/naming/Name;)Ljavax/naming/NamingEnumeration;", "sig": "(Ljavax/naming/Name;)Ljavax/naming/NamingEnumeration<Ljavax/naming/NameClassPair;>;", "exs": ["javax/naming/NamingException"]}, {"nme": "list", "acc": 1, "dsc": "(Ljava/lang/String;)Ljavax/naming/NamingEnumeration;", "sig": "(Ljava/lang/String;)Ljavax/naming/NamingEnumeration<Ljavax/naming/NameClassPair;>;", "exs": ["javax/naming/NamingException"]}, {"nme": "listBindings", "acc": 1, "dsc": "(Ljavax/naming/Name;)Ljavax/naming/NamingEnumeration;", "sig": "(Ljavax/naming/Name;)Ljavax/naming/NamingEnumeration<Ljavax/naming/Binding;>;", "exs": ["javax/naming/NamingException"]}, {"nme": "listBindings", "acc": 1, "dsc": "(Ljava/lang/String;)Ljavax/naming/NamingEnumeration;", "sig": "(Ljava/lang/String;)Ljavax/naming/NamingEnumeration<Ljavax/naming/Binding;>;", "exs": ["javax/naming/NamingException"]}, {"nme": "destroySubcontext", "acc": 1, "dsc": "(Ljavax/naming/Name;)V", "exs": ["javax/naming/NamingException"]}, {"nme": "destroySubcontext", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["javax/naming/NamingException"]}, {"nme": "createSubcontext", "acc": 1, "dsc": "(Ljavax/naming/Name;)Ljavax/naming/Context;", "exs": ["javax/naming/NamingException"]}, {"nme": "createSubcontext", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;)Ljavax/naming/Context;", "exs": ["javax/naming/NamingException"]}, {"nme": "lookupLink", "acc": 1, "dsc": "(Ljavax/naming/Name;)Ljava/lang/Object;", "exs": ["javax/naming/NamingException"]}, {"nme": "lookupLink", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["javax/naming/NamingException"]}, {"nme": "get<PERSON>ame<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Ljavax/naming/Name;)Ljavax/naming/NameParser;", "exs": ["javax/naming/NamingException"]}, {"nme": "get<PERSON>ame<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(L<PERSON><PERSON>/lang/String;)Ljavax/naming/NameParser;", "exs": ["javax/naming/NamingException"]}, {"nme": "composeName", "acc": 1, "dsc": "(Ljavax/naming/Name;Ljavax/naming/Name;)Ljavax/naming/Name;", "exs": ["javax/naming/NamingException"]}, {"nme": "composeName", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "exs": ["javax/naming/NamingException"]}, {"nme": "removeFromEnvironment", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["javax/naming/NamingException"]}, {"nme": "addToEnvironment", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["javax/naming/NamingException"]}, {"nme": "getEnvironment", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Hashtable;", "sig": "()<PERSON><PERSON><PERSON>/util/Hashtable<Ljava/lang/String;Ljava/lang/Object;>;", "exs": ["javax/naming/NamingException"]}, {"nme": "close", "acc": 1, "dsc": "()V"}, {"nme": "getNameInNamespace", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getReference", "acc": 1, "dsc": "()Ljavax/naming/Reference;", "exs": ["javax/naming/NamingException"]}, {"nme": "wrapRemoteException", "acc": 9, "dsc": "(Ljava/rmi/RemoteException;)Ljavax/naming/NamingException;"}, {"nme": "getRegistry", "acc": 10, "dsc": "(Ljava/lang/String;ILjava/rmi/server/RMIClientSocketFactory;)Ljava/rmi/registry/Registry;", "exs": ["javax/naming/NamingException"]}, {"nme": "encodeObject", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Ljavax/naming/Name;)Ljava/rmi/Remote;", "exs": ["javax/naming/NamingException", "java/rmi/RemoteException"]}, {"nme": "decodeObject", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/rmi/Remote;Ljavax/naming/Name;)Ljava/lang/Object;", "exs": ["javax/naming/NamingException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "environment", "dsc": "<PERSON><PERSON><PERSON>/util/Hashtable;", "sig": "Lja<PERSON>/util/Hashtable<Ljava/lang/String;Ljava/lang/Object;>;"}, {"acc": 2, "nme": "registry", "dsc": "Ljava/rmi/registry/Registry;"}, {"acc": 2, "nme": "host", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "port", "dsc": "I"}, {"acc": 26, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "Ljavax/naming/NameParser;"}, {"acc": 26, "nme": "SOCKET_FACTORY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.jndi.rmi.factory.socket"}, {"acc": 0, "nme": "reference", "dsc": "Ljavax/naming/Reference;"}]}, "classes/com/sun/jndi/rmi/registry/AtomicNameParser.class": {"ver": 68, "acc": 32, "nme": "com/sun/jndi/rmi/registry/AtomicNameParser", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "parse", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljavax/naming/Name;", "exs": ["javax/naming/NamingException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "syntax", "dsc": "Ljava/util/Properties;"}]}, "classes/com/sun/jndi/url/rmi/rmiURLContextFactory.class": {"ver": 68, "acc": 33, "nme": "com/sun/jndi/url/rmi/rmiURLContextFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getObjectInstance", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Ljavax/naming/Name;Ljavax/naming/Context;Lja<PERSON>/util/Hashtable;)Ljava/lang/Object;", "sig": "(L<PERSON><PERSON>/lang/Object;Ljavax/naming/Name;Ljavax/naming/Context;Ljava/util/Hashtable<**>;)Ljava/lang/Object;", "exs": ["javax/naming/NamingException"]}, {"nme": "getUsingURL", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Hashtable;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Hashtable<**>;)Ljava/lang/Object;", "exs": ["javax/naming/NamingException"]}, {"nme": "getUsingURLs", "acc": 10, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Hashtable;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "([<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Hashtable<**>;)Ljava/lang/Object;", "exs": ["javax/naming/NamingException"]}], "flds": []}, "classes/com/sun/jndi/rmi/registry/ReferenceWrapper.class": {"ver": 68, "acc": 33, "nme": "com/sun/jndi/rmi/registry/ReferenceWrapper", "super": "java/rmi/server/UnicastRemoteObject", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/naming/Reference;)V", "exs": ["javax/naming/NamingException", "java/rmi/RemoteException"]}, {"nme": "getReference", "acc": 1, "dsc": "()Ljavax/naming/Reference;", "exs": ["java/rmi/RemoteException"]}], "flds": [{"acc": 4, "nme": "wrappee", "dsc": "Ljavax/naming/Reference;"}, {"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 6078186197417641456}]}, "classes/com/sun/jndi/url/rmi/rmiURLContext$Parser.class": {"ver": 68, "acc": 33, "nme": "com/sun/jndi/url/rmi/rmiURLContext$Parser", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/lang/String;Lcom/sun/jndi/toolkit/url/Uri$ParseMode;)V"}, {"nme": "url", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "host", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "port", "acc": 1, "dsc": "()I"}, {"nme": "objName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "mode", "acc": 1, "dsc": "()Lcom/sun/jndi/toolkit/url/Uri$ParseMode;"}, {"nme": "parse", "acc": 1, "dsc": "()V", "exs": ["javax/naming/NamingException"]}, {"nme": "parseStrict", "acc": 2, "dsc": "()V", "exs": ["javax/naming/NamingException"]}, {"nme": "parseCompat", "acc": 2, "dsc": "()V", "exs": ["javax/naming/NamingException"]}, {"nme": "parseLegacy", "acc": 2, "dsc": "()V"}, {"nme": "newNamingException", "acc": 0, "dsc": "(L<PERSON><PERSON>/lang/Throwable;)Ljavax/naming/NamingException;"}, {"nme": "acceptsFragment", "acc": 4, "dsc": "()Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16, "nme": "url", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 16, "nme": "mode", "dsc": "Lcom/sun/jndi/toolkit/url/Uri$ParseMode;"}, {"acc": 0, "nme": "host", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "port", "dsc": "I"}, {"acc": 0, "nme": "objName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "classes/com/sun/jndi/rmi/registry/RemoteReference.class": {"ver": 68, "acc": 1537, "nme": "com/sun/jndi/rmi/registry/RemoteReference", "super": "java/lang/Object", "mthds": [{"nme": "getReference", "acc": 1025, "dsc": "()Ljavax/naming/Reference;", "exs": ["javax/naming/NamingException", "java/rmi/RemoteException"]}], "flds": []}, "classes/com/sun/jndi/rmi/registry/ReferenceWrapper_Stub.class": {"ver": 68, "acc": 49, "nme": "com/sun/jndi/rmi/registry/ReferenceWrapper_Stub", "super": "java/rmi/server/RemoteStub", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/rmi/server/RemoteRef;)V"}, {"nme": "getReference", "acc": 1, "dsc": "()Ljavax/naming/Reference;", "exs": ["java/rmi/RemoteException", "javax/naming/NamingException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 2}, {"acc": 10, "nme": "$method_getReference_0", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}]}, "classes/com/sun/jndi/rmi/registry/BindingEnumeration.class": {"ver": 68, "acc": 32, "nme": "com/sun/jndi/rmi/registry/BindingEnumeration", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/sun/jndi/rmi/registry/RegistryContext;[<PERSON>ja<PERSON>/lang/String;)V"}, {"nme": "finalize", "acc": 4, "dsc": "()V"}, {"nme": "hasMore", "acc": 1, "dsc": "()Z"}, {"nme": "next", "acc": 1, "dsc": "()Ljavax/naming/Binding;", "exs": ["javax/naming/NamingException"]}, {"nme": "hasMoreElements", "acc": 1, "dsc": "()Z"}, {"nme": "nextElement", "acc": 1, "dsc": "()Ljavax/naming/Binding;"}, {"nme": "close", "acc": 1, "dsc": "()V"}, {"nme": "next", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["javax/naming/NamingException"]}, {"nme": "nextElement", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 2, "nme": "ctx", "dsc": "Lcom/sun/jndi/rmi/registry/RegistryContext;"}, {"acc": 18, "nme": "names", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "nextName", "dsc": "I"}]}, "classes/module-info.class": {"ver": 68, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "classes/com/sun/jndi/url/rmi/rmiURLContext$1.class": {"ver": 68, "acc": 4128, "nme": "com/sun/jndi/url/rmi/rmiURLContext$1", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$com$sun$jndi$toolkit$url$Uri$ParseMode", "dsc": "[I"}]}, "classes/com/sun/jndi/rmi/registry/RegistryContextFactory.class": {"ver": 68, "acc": 33, "nme": "com/sun/jndi/rmi/registry/RegistryContextFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getInitialContext", "acc": 1, "dsc": "(L<PERSON><PERSON>/util/Hashtable;)Ljavax/naming/Context;", "sig": "(L<PERSON><PERSON>/util/Hashtable<**>;)Ljavax/naming/Context;", "exs": ["javax/naming/NamingException"]}, {"nme": "getObjectInstance", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Ljavax/naming/Name;Ljavax/naming/Context;Lja<PERSON>/util/Hashtable;)Ljava/lang/Object;", "sig": "(L<PERSON><PERSON>/lang/Object;Ljavax/naming/Name;Ljavax/naming/Context;Ljava/util/Hashtable<**>;)Ljava/lang/Object;", "exs": ["javax/naming/NamingException"]}, {"nme": "URLToContext", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Hashtable;)Ljavax/naming/Context;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/util/Hashtable<**>;)Ljavax/naming/Context;", "exs": ["javax/naming/NamingException"]}, {"nme": "URLsToObject", "acc": 10, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Hashtable;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "([<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Hashtable<**>;)Ljava/lang/Object;", "exs": ["javax/naming/NamingException"]}, {"nme": "getInitCtxURL", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/Hashtable;)<PERSON><PERSON><PERSON>/lang/String;", "sig": "(<PERSON><PERSON><PERSON>/util/Hashtable<**>;)Ljava/lang/String;"}, {"nme": "isRegistryRef", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "getURLs", "acc": 10, "dsc": "(Ljavax/naming/Reference;)[Ljava/lang/String;", "exs": ["javax/naming/NamingException"]}], "flds": [{"acc": 25, "nme": "ADDRESS_TYPE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "URL"}]}, "classes/com/sun/jndi/rmi/registry/NameClassPairEnumeration.class": {"ver": 68, "acc": 32, "nme": "com/sun/jndi/rmi/registry/NameClassPairEnumeration", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "hasMore", "acc": 1, "dsc": "()Z"}, {"nme": "next", "acc": 1, "dsc": "()Ljavax/naming/NameClassPair;", "exs": ["javax/naming/NamingException"]}, {"nme": "hasMoreElements", "acc": 1, "dsc": "()Z"}, {"nme": "nextElement", "acc": 1, "dsc": "()Ljavax/naming/NameClassPair;"}, {"nme": "close", "acc": 1, "dsc": "()V"}, {"nme": "next", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["javax/naming/NamingException"]}, {"nme": "nextElement", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 18, "nme": "names", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "nextName", "dsc": "I"}]}}}}