{"md5": "08d57d98d111f5b6652919b7647d856c", "sha2": "851ff3f0a4bc772cd5268ae48b08b33c05fb013c", "sha256": "1d1e59602eccfae9db1b8be0a8167bc117a11f9b9d4f84eed49ab12d39385019", "contents": {"classes": {"classes/jdk/jpackage/internal/WixToolset.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/WixToolset", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljava/util/Map;)V", "sig": "(Ljava/util/Map<Ljdk/jpackage/internal/WixTool;Ljdk/jpackage/internal/WixTool$ToolInfo;>;)V"}, {"nme": "getType", "acc": 0, "dsc": "()Ljdk/jpackage/internal/WixToolset$WixToolsetType;"}, {"nme": "getToolPath", "acc": 0, "dsc": "(Ljdk/jpackage/internal/WixTool;)Ljava/nio/file/Path;"}, {"nme": "getVersion", "acc": 0, "dsc": "()Ljdk/jpackage/internal/DottedVersion;"}, {"nme": "create", "acc": 8, "dsc": "(Lja<PERSON>/util/Set;Ljava/util/Map;)Ljdk/jpackage/internal/WixToolset;", "sig": "(Ljava/util/Set<Ljdk/jpackage/internal/WixTool;>;Ljava/util/Map<Ljdk/jpackage/internal/WixTool;Ljdk/jpackage/internal/WixTool$ToolInfo;>;)Ljdk/jpackage/internal/WixToolset;"}, {"nme": "lambda$create$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;Ljava/util/Map$Entry;)Z"}, {"nme": "lambda$getType$0", "acc": 4098, "dsc": "(Ljdk/jpackage/internal/WixToolset$WixToolsetType;)Z"}], "flds": [{"acc": 18, "nme": "tools", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljdk/jpackage/internal/WixTool;Ljdk/jpackage/internal/WixTool$ToolInfo;>;"}]}, "classes/jdk/jpackage/internal/JLinkBundlerHelper$LazyLoad.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/JLinkBundlerHelper$LazyLoad", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "JLINK_TOOL", "dsc": "<PERSON><PERSON><PERSON>/util/spi/Tool<PERSON>;"}]}, "classes/jdk/jpackage/internal/ShortPathUtils.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/ShortPathUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "adjustPath", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "adjustPath", "acc": 8, "dsc": "(Ljava/nio/file/Path;)Ljava/nio/file/Path;"}, {"nme": "to<PERSON><PERSON>t<PERSON><PERSON>", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Optional;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/Optional<Ljava/lang/String;>;"}, {"nme": "to<PERSON><PERSON>t<PERSON><PERSON>", "acc": 8, "dsc": "(Ljava/nio/file/Path;)Ljava/util/Optional;", "sig": "(Ljava/nio/file/Path;)Ljava/util/Optional<Ljava/nio/file/Path;>;"}, {"nme": "getShortPathWrapper", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getShortPath", "acc": 266, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "lambda$getShortPathWrapper$0", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;)Ljdk/jpackage/internal/ShortPathUtils$ShortPathException;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "MAX_PATH", "dsc": "I", "val": 240}, {"acc": 26, "nme": "LONG_PATH_PREFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "\\\\?\\"}]}, "classes/jdk/jpackage/internal/OverridableResource$SourceHandler.class": {"ver": 68, "acc": 1536, "nme": "jdk/jpackage/internal/OverridableResource$SourceHandler", "super": "java/lang/Object", "mthds": [{"nme": "apply", "acc": 1025, "dsc": "(Ljdk/jpackage/internal/OverridableResource$ResourceConsumer;)Z", "exs": ["java/io/IOException"]}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/FunctionalInterface;"}]}, "classes/jdk/jpackage/internal/util/PathUtils.class": {"ver": 68, "acc": 49, "nme": "jdk/jpackage/internal/util/PathUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getSuffix", "acc": 9, "dsc": "(Lja<PERSON>/nio/file/Path;)Ljava/lang/String;"}, {"nme": "addSuffix", "acc": 9, "dsc": "(Ljava/nio/file/Path;Ljava/lang/String;)Ljava/nio/file/Path;"}, {"nme": "replaceSuffix", "acc": 9, "dsc": "(Ljava/nio/file/Path;Ljava/lang/String;)Ljava/nio/file/Path;"}, {"nme": "resolveNullablePath", "acc": 9, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/Path;)Ljava/nio/file/Path;"}], "flds": []}, "classes/jdk/jpackage/internal/DottedVersion$DigitsSupplier.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/DottedVersion$DigitsSupplier", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getNextDigits", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getUnprocessedString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "cursor", "dsc": "I"}, {"acc": 2, "nme": "lastDotPos", "dsc": "I"}, {"acc": 2, "nme": "stoped", "dsc": "Z"}, {"acc": 18, "nme": "input", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/jpackage/internal/ScriptRunner.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/ScriptRunner", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "setResourceCategoryId", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljdk/jpackage/internal/ScriptRunner;"}, {"nme": "setDirectory", "acc": 0, "dsc": "(Ljava/nio/file/Path;)Ljdk/jpackage/internal/ScriptRunner;"}, {"nme": "setScriptNameSuffix", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljdk/jpackage/internal/ScriptRunner;"}, {"nme": "addEnvironment", "acc": 0, "dsc": "(Ljava/util/Map;)Ljdk/jpackage/internal/ScriptRunner;", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)Ljdk/jpackage/internal/ScriptRunner;"}, {"nme": "setEnvironmentVariable", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;)Ljdk/jpackage/internal/ScriptRunner;"}, {"nme": "run", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(L<PERSON><PERSON>/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)V", "exs": ["java/io/IOException"]}, {"nme": "shell", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "scriptSuffix", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$shell$0", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "scriptNameSuffix", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "resourceCategoryId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "directory", "dsc": "Ljava/nio/file/Path;"}, {"acc": 2, "nme": "environment", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}]}, "classes/jdk/jpackage/internal/WixUiFragmentBuilder$DialogPair.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/WixUiFragmentBuilder$DialogPair", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/jpackage/internal/WixUiFragmentBuilder$Dialog;Ljdk/jpackage/internal/WixUiFragmentBuilder$Dialog;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "flip", "acc": 0, "dsc": "()Ljdk/jpackage/internal/WixUiFragmentBuilder$DialogPair;"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "flds": [{"acc": 18, "nme": "firstId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "secondId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/jpackage/internal/WixUiFragmentBuilder$Publish.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/WixUiFragmentBuilder$Publish", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;I)V"}], "flds": [{"acc": 18, "nme": "control", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "condition", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "order", "dsc": "I"}]}, "classes/jdk/jpackage/internal/ConfigException.class": {"ver": 68, "acc": 33, "nme": "jdk/jpackage/internal/ConfigException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Exception;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Exception;)V"}, {"nme": "getAdvice", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}, {"acc": 16, "nme": "advice", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/module-info.class": {"ver": 68, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "classes/jdk/jpackage/internal/WixSourceConverter.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/WixSourceConverter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "appyTo", "acc": 0, "dsc": "(Ljdk/jpackage/internal/OverridableResource;Ljava/nio/file/Path;)Ljdk/jpackage/internal/WixSourceConverter$Status;", "exs": ["java/io/IOException"]}, {"nme": "saveResourceInMemory", "acc": 10, "dsc": "(Ljdk/jpackage/internal/OverridableResource;)[B", "exs": ["java/io/IOException"]}, {"nme": "lambda$appyTo$0", "acc": 4106, "dsc": "([B)Ljavax/xml/transform/Source;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "transformer", "dsc": "Ljavax/xml/transform/Transformer;"}, {"acc": 18, "nme": "outputFactory", "dsc": "Ljavax/xml/stream/XMLOutputFactory;"}, {"acc": 26, "nme": "KNOWN_NAMESPACES", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}]}, "classes/jdk/jpackage/internal/WixSourceConverter$ResourceGroup.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/WixSourceConverter$ResourceGroup", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/jpackage/internal/WixToolset$WixToolsetType;)V"}, {"nme": "addResource", "acc": 0, "dsc": "(Ljdk/jpackage/internal/OverridableResource;Ljava/nio/file/Path;)V"}, {"nme": "saveResources", "acc": 0, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "lambda$saveResources$0", "acc": 4106, "dsc": "(Ljdk/jpackage/internal/OverridableResource;)Z"}], "flds": [{"acc": 18, "nme": "resources", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/nio/file/Path;Ljdk/jpackage/internal/OverridableResource;>;"}, {"acc": 18, "nme": "wixToolsetType", "dsc": "Ljdk/jpackage/internal/WixToolset$WixToolsetType;"}]}, "classes/jdk/jpackage/internal/AbstractAppImageBuilder$IconType.class": {"ver": 68, "acc": 16432, "nme": "jdk/jpackage/internal/AbstractAppImageBuilder$IconType", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/jpackage/internal/AbstractAppImageBuilder$IconType;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Ljdk/jpackage/internal/AbstractAppImageBuilder$IconType;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/jpackage/internal/AbstractAppImageBuilder$IconType;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "DefaultOrResourceDirIcon", "dsc": "Ljdk/jpackage/internal/AbstractAppImageBuilder$IconType;"}, {"acc": 16409, "nme": "CustomIcon", "dsc": "Ljdk/jpackage/internal/AbstractAppImageBuilder$IconType;"}, {"acc": 16409, "nme": "NoIcon", "dsc": "Ljdk/jpackage/internal/AbstractAppImageBuilder$IconType;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/jpackage/internal/AbstractAppImageBuilder$IconType;"}]}, "classes/jdk/jpackage/internal/LauncherData$ModuleInfo.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/LauncherData$ModuleInfo", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "fromModuleDescriptor", "acc": 8, "dsc": "(Ljava/lang/module/ModuleDescriptor;)Ljdk/jpackage/internal/LauncherData$ModuleInfo;"}, {"nme": "fromCookedRuntime", "acc": 8, "dsc": "(Ljava/lang/String;Ljava/nio/file/Path;)Ljdk/jpackage/internal/LauncherData$ModuleInfo;"}], "flds": [{"acc": 0, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "mainClass", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/jpackage/internal/util/FileUtils.class": {"ver": 68, "acc": 49, "nme": "jdk/jpackage/internal/util/FileUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "deleteRecursive", "acc": 9, "dsc": "(Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "copyRecursive", "acc": 137, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/Path;[Ljava/nio/file/CopyOption;)V", "exs": ["java/io/IOException"]}, {"nme": "copyRecursive", "acc": 137, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/Path;Ljava/util/List;[Ljava/nio/file/CopyOption;)V", "sig": "(Ljava/nio/file/Path;Ljava/nio/file/Path;Ljava/util/List<Ljava/nio/file/Path;>;[Ljava/nio/file/CopyOption;)V", "exs": ["java/io/IOException"]}, {"nme": "isPathMatch", "acc": 10, "dsc": "(Ljava/nio/file/Path;Ljava/util/List;)Z", "sig": "(Ljava/nio/file/Path;Ljava/util/List<Ljava/nio/file/Path;>;)Z"}], "flds": []}, "classes/jdk/jpackage/internal/Executor.class": {"ver": 68, "acc": 49, "nme": "jdk/jpackage/internal/Executor", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "setOutputConsumer", "acc": 0, "dsc": "(Ljava/util/function/Consumer;)Ljdk/jpackage/internal/Executor;", "sig": "(Ljava/util/function/Consumer<Ljava/util/stream/Stream<Ljava/lang/String;>;>;)Ljdk/jpackage/internal/Executor;"}, {"nme": "saveOutput", "acc": 0, "dsc": "(Z)Ljdk/jpackage/internal/Executor;"}, {"nme": "setWriteOutputToFile", "acc": 0, "dsc": "(Z)Ljdk/jpackage/internal/Executor;"}, {"nme": "setTimeout", "acc": 0, "dsc": "(J)Ljdk/jpackage/internal/Executor;"}, {"nme": "setProcessBuilder", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/ProcessBuilder;)Ljdk/jpackage/internal/Executor;"}, {"nme": "setCommandLine", "acc": 128, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)Ljdk/jpackage/internal/Executor;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 0, "dsc": "(Z)Ljdk/jpackage/internal/Executor;"}, {"nme": "getOutput", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "executeExpectSuccess", "acc": 0, "dsc": "()Ljdk/jpackage/internal/Executor;", "exs": ["java/io/IOException"]}, {"nme": "execute", "acc": 0, "dsc": "()I", "exs": ["java/io/IOException"]}, {"nme": "waitForProcess", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Process;)I", "exs": ["java/lang/InterruptedException"]}, {"nme": "of", "acc": 136, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)Ljdk/jpackage/internal/Executor;"}, {"nme": "of", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/ProcessBuilder;)Ljdk/jpackage/internal/Executor;"}, {"nme": "createLogMessage", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/ProcessBuilder;Z)Ljava/lang/String;"}, {"nme": "lambda$execute$2", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "lambda$execute$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/List;L<PERSON><PERSON>/io/BufferedReader;)Ljava/util/stream/Stream;"}, {"nme": "lambda$execute$0", "acc": 4106, "dsc": "(Lja<PERSON>/util/List;)Ljava/util/stream/Stream;"}], "flds": [{"acc": 25, "nme": "INFINITE_TIMEOUT", "dsc": "I", "val": -1}, {"acc": 2, "nme": "pb", "dsc": "<PERSON><PERSON><PERSON>/lang/ProcessBuilder;"}, {"acc": 2, "nme": "saveOutput", "dsc": "Z"}, {"acc": 2, "nme": "writeOutputToFile", "dsc": "Z"}, {"acc": 2, "nme": "quietCommand", "dsc": "Z"}, {"acc": 2, "nme": "timeout", "dsc": "J"}, {"acc": 2, "nme": "output", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 2, "nme": "outputConsumer", "dsc": "Ljava/util/function/Consumer;", "sig": "Ljava/util/function/Consumer<Ljava/util/stream/Stream<Ljava/lang/String;>;>;"}]}, "classes/jdk/jpackage/internal/WixAppImageFragmentBuilder$4.class": {"ver": 68, "acc": 4128, "nme": "jdk/jpackage/internal/WixAppImageFragmentBuilder$4", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$jdk$jpackage$internal$WixToolset$WixToolsetType", "dsc": "[I"}]}, "classes/jdk/jpackage/internal/RetryExecutor.class": {"ver": 68, "acc": 49, "nme": "jdk/jpackage/internal/RetryExecutor", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "setMaxAttemptsCount", "acc": 1, "dsc": "(I)Ljdk/jpackage/internal/RetryExecutor;"}, {"nme": "setAttemptTimeoutMillis", "acc": 1, "dsc": "(I)Ljdk/jpackage/internal/RetryExecutor;"}, {"nme": "saveOutput", "acc": 1, "dsc": "(Z)Ljdk/jpackage/internal/RetryExecutor;"}, {"nme": "getOutput", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "setWriteOutputToFile", "acc": 1, "dsc": "(Z)Ljdk/jpackage/internal/RetryExecutor;"}, {"nme": "setExecutorInitializer", "acc": 1, "dsc": "(Ljava/util/function/Consumer;)Ljdk/jpackage/internal/RetryExecutor;", "sig": "(Ljava/util/function/Consumer<Ljdk/jpackage/internal/Executor;>;)Ljdk/jpackage/internal/RetryExecutor;"}, {"nme": "abort", "acc": 1, "dsc": "()V"}, {"nme": "isAborted", "acc": 1, "dsc": "()Z"}, {"nme": "retryOnKnownErrorMessage", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljdk/jpackage/internal/RetryExecutor;"}, {"nme": "execute", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "execute", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/ProcessBuilder;)V", "exs": ["java/io/IOException"]}, {"nme": "executeLoop", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Supplier;)V", "sig": "(Ljava/util/function/Supplier<Ljdk/jpackage/internal/Executor;>;)V", "exs": ["java/io/IOException"]}, {"nme": "lambda$execute$1", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/ProcessBuilder;)Ljdk/jpackage/internal/Executor;"}, {"nme": "lambda$execute$0", "acc": 4098, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)Ljdk/jpackage/internal/Executor;"}, {"nme": "lambda$retryOnKnownErrorMessage$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljdk/jpackage/internal/RetryExecutor;Ljdk/jpackage/internal/Executor;)V"}, {"nme": "lambda$retryOnKnownErrorMessage$1", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;Ljdk/jpackage/internal/RetryExecutor;Ljava/util/stream/Stream;)V"}], "flds": [{"acc": 2, "nme": "executorInitializer", "dsc": "Ljava/util/function/Consumer;", "sig": "Ljava/util/function/Consumer<Ljdk/jpackage/internal/Executor;>;"}, {"acc": 2, "nme": "aborted", "dsc": "Z"}, {"acc": 2, "nme": "attempts", "dsc": "I"}, {"acc": 2, "nme": "timeout<PERSON><PERSON><PERSON>", "dsc": "I"}, {"acc": 2, "nme": "saveOutput", "dsc": "Z"}, {"acc": 2, "nme": "output", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 2, "nme": "writeOutputToFile", "dsc": "Z"}]}, "classes/jdk/jpackage/internal/util/function/ThrowingSupplier.class": {"ver": 68, "acc": 1537, "nme": "jdk/jpackage/internal/util/function/ThrowingSupplier", "super": "java/lang/Object", "mthds": [{"nme": "get", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;", "exs": ["java/lang/Throwable"]}, {"nme": "toSupplier", "acc": 9, "dsc": "(Ljdk/jpackage/internal/util/function/ThrowingSupplier;)Ljava/util/function/Supplier;", "sig": "<T:Ljava/lang/Object;>(Ljdk/jpackage/internal/util/function/ThrowingSupplier<TT;>;)Ljava/util/function/Supplier<TT;>;"}, {"nme": "lambda$toSupplier$0", "acc": 4106, "dsc": "(Ljdk/jpackage/internal/util/function/ThrowingSupplier;)Ljava/lang/Object;"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/FunctionalInterface;"}]}, "classes/jdk/jpackage/internal/util/FileUtils$CopyAction.class": {"ver": 68, "acc": 65584, "nme": "jdk/jpackage/internal/util/FileUtils$CopyAction", "super": "java/lang/Record", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/Path;)V"}, {"nme": "apply", "acc": 128, "dsc": "([Ljava/nio/file/CopyOption;)V", "exs": ["java/io/IOException"]}, {"nme": "toString", "acc": 17, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "hashCode", "acc": 17, "dsc": "()I"}, {"nme": "equals", "acc": 17, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "src", "acc": 1, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "dest", "acc": 1, "dsc": "()Ljava/nio/file/Path;"}], "flds": [{"acc": 18, "nme": "src", "dsc": "Ljava/nio/file/Path;"}, {"acc": 18, "nme": "dest", "dsc": "Ljava/nio/file/Path;"}]}, "classes/jdk/jpackage/internal/AppImageFile.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/AppImageFile", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/nio/file/Path;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "sig": "(Ljava/nio/file/Path;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List<Ljdk/jpackage/internal/AppImageFile$LauncherInfo;>;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "getAddLaunchers", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljdk/jpackage/internal/AppImageFile$LauncherInfo;>;"}, {"nme": "getAppVersion", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLauncherName", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getMainClass", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isSigned", "acc": 1, "dsc": "()Z"}, {"nme": "isAppStore", "acc": 1, "dsc": "()Z"}, {"nme": "getPathInAppImage", "acc": 9, "dsc": "(Ljava/nio/file/Path;)Ljava/nio/file/Path;"}, {"nme": "save", "acc": 0, "dsc": "(Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "save", "acc": 8, "dsc": "(Ljava/nio/file/Path;Ljava/util/Map;)V", "sig": "(Ljava/nio/file/Path;Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;)V", "exs": ["java/io/IOException"]}, {"nme": "save", "acc": 10, "dsc": "(Ljava/nio/file/Path;Ljava/util/Map;Ljdk/jpackage/internal/AppImageFile;)V", "sig": "(Ljava/nio/file/Path;Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;Ljdk/jpackage/internal/AppImageFile;)V", "exs": ["java/io/IOException"]}, {"nme": "addLauncherInfo", "acc": 8, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;Ljdk/jpackage/internal/AppImageFile$LauncherInfo;)V", "exs": ["javax/xml/stream/XMLStreamException"]}, {"nme": "load", "acc": 9, "dsc": "(Ljava/nio/file/Path;)Ljdk/jpackage/internal/AppImageFile;"}, {"nme": "copyAsSigned", "acc": 1, "dsc": "()Ljdk/jpackage/internal/AppImageFile;"}, {"nme": "readXml", "acc": 9, "dsc": "(Ljava/nio/file/Path;)Lorg/w3c/dom/Document;", "exs": ["java/io/IOException"]}, {"nme": "getLaunchers", "acc": 8, "dsc": "(Ljava/nio/file/Path;Ljava/util/Map;)Ljava/util/List;", "sig": "(Ljava/nio/file/Path;Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;)Ljava/util/List<Ljdk/jpackage/internal/AppImageFile$LauncherInfo;>;"}, {"nme": "extractAppName", "acc": 9, "dsc": "(Lja<PERSON>/nio/file/Path;)Ljava/lang/String;"}, {"nme": "extractMainClass", "acc": 9, "dsc": "(Lja<PERSON>/nio/file/Path;)Ljava/lang/String;"}, {"nme": "xpathQueryNullable", "acc": 10, "dsc": "(Ljavax/xml/xpath/XPath;Ljava/lang/String;Lorg/w3c/dom/Document;)Ljava/lang/String;", "exs": ["javax/xml/xpath/XPathExpressionException"]}, {"nme": "getVersion", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getPlatform", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$getLaunchers$0", "acc": 4106, "dsc": "(Ljava/util/Map;)Ljdk/jpackage/internal/AppImageFile$LauncherInfo;"}, {"nme": "lambda$save$0", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;<PERSON>java/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/Map;Ljavax/xml/stream/XMLStreamWriter;)V", "exs": ["java/io/IOException", "javax/xml/stream/XMLStreamException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "appVersion", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "launcherName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "mainClass", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "addLauncherInfos", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/jpackage/internal/AppImageFile$LauncherInfo;>;"}, {"acc": 18, "nme": "creatorVersion", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "creatorPlatform", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "signed", "dsc": "Z"}, {"acc": 18, "nme": "appStore", "dsc": "Z"}, {"acc": 26, "nme": "FILENAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": ".jpackage.xml"}, {"acc": 26, "nme": "PLATFORM_LABELS", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljdk/internal/util/OperatingSystem;Ljava/lang/String;>;"}]}, "classes/jdk/jpackage/internal/DeployParams$Template.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/DeployParams$Template", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/Path;)V"}], "flds": [{"acc": 0, "nme": "in", "dsc": "Ljava/nio/file/Path;"}, {"acc": 0, "nme": "out", "dsc": "Ljava/nio/file/Path;"}]}, "classes/jdk/jpackage/internal/StandardBundlerParam.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/StandardBundlerParam", "super": "jdk/jpackage/internal/BundlerParamInfo", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(L<PERSON><PERSON>/lang/String;Ljava/lang/Class;Ljava/util/function/Function;Ljava/util/function/BiFunction;)V", "sig": "(Ljava/lang/String;Ljava/lang/Class<TT;>;Ljava/util/function/Function<Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;TT;>;Ljava/util/function/BiFunction<Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;TT;>;)V"}, {"nme": "findPathOfModule", "acc": 10, "dsc": "(L<PERSON><PERSON>/util/List;Ljava/lang/String;)Ljava/nio/file/Path;", "sig": "(Ljava/util/List<Ljava/nio/file/Path;>;Ljava/lang/String;)Ljava/nio/file/Path;"}, {"nme": "isRuntimeInstaller", "acc": 8, "dsc": "(Ljava/util/Map;)Z", "sig": "(<PERSON><PERSON><PERSON>/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)Z"}, {"nme": "hasPredefinedAppImage", "acc": 8, "dsc": "(Ljava/util/Map;)Z", "sig": "(<PERSON><PERSON><PERSON>/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)Z"}, {"nme": "getPredefinedAppImage", "acc": 8, "dsc": "(Ljava/util/Map;)Ljava/nio/file/Path;", "sig": "(Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)Ljava/nio/file/Path;"}, {"nme": "copyPredefinedRuntimeImage", "acc": 8, "dsc": "(Ljava/util/Map;Ljdk/jpackage/internal/ApplicationLayout;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;Ljdk/jpackage/internal/ApplicationLayout;)V", "exs": ["java/io/IOException", "jdk/jpackage/internal/ConfigException"]}, {"nme": "getDefaultModulePath", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/nio/file/Path;>;"}, {"nme": "getDefaultAppVersion", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;", "sig": "(L<PERSON><PERSON>/util/Map<Ljava/lang/String;-<PERSON><PERSON><PERSON>/lang/Object;>;)Ljava/lang/String;"}, {"nme": "lambda$static$82", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)<PERSON><PERSON><PERSON>/lang/Boolean;"}, {"nme": "lambda$static$81", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/Boolean;"}, {"nme": "lambda$static$80", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)<PERSON><PERSON><PERSON>/lang/Boolean;"}, {"nme": "lambda$static$79", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/Boolean;"}, {"nme": "lambda$static$78", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)Ljava/util/Set;"}, {"nme": "lambda$static$77", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/util/Set;"}, {"nme": "lambda$static$76", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/util/Map;)Ljava/util/List;"}, {"nme": "lambda$static$75", "acc": 4106, "dsc": "(Ljava/util/Map;)Ljava/util/List;"}, {"nme": "lambda$static$74", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)Ljava/util/Set;"}, {"nme": "lambda$static$73", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/util/Set;"}, {"nme": "lambda$static$72", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$71", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$69", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/util/Map;)Ljava/util/List;"}, {"nme": "lambda$static$70", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;)Ljava/nio/file/Path;"}, {"nme": "lambda$static$68", "acc": 4106, "dsc": "(Ljava/util/Map;)Ljava/util/List;"}, {"nme": "lambda$static$67", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/util/Map;)Ljava/util/List;"}, {"nme": "lambda$static$66", "acc": 4106, "dsc": "(Ljava/util/Map;)Ljava/util/List;"}, {"nme": "lambda$static$65", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/util/Map;)Ljava/util/List;"}, {"nme": "lambda$static$64", "acc": 4106, "dsc": "(Ljava/util/Map;)Ljava/util/List;"}, {"nme": "lambda$static$63", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/Map;)Ljava/nio/file/Path;"}, {"nme": "lambda$static$62", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$61", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$60", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/util/Map;)Ljava/util/List;"}, {"nme": "lambda$static$59", "acc": 4106, "dsc": "(Ljava/util/Map;)Ljava/util/List;"}, {"nme": "lambda$static$58", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/util/Map;)Ljava/util/List;"}, {"nme": "lambda$static$57", "acc": 4106, "dsc": "(Ljava/util/Map;)Ljava/util/List;"}, {"nme": "lambda$static$56", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/util/Map;)Ljava/util/List;"}, {"nme": "lambda$static$55", "acc": 4106, "dsc": "(Ljava/util/Map;)Ljava/util/List;"}, {"nme": "lambda$static$54", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/util/Map;)Ljava/util/List;"}, {"nme": "lambda$static$53", "acc": 4106, "dsc": "(Ljava/util/Map;)Ljava/util/List;"}, {"nme": "lambda$static$52", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)<PERSON><PERSON><PERSON>/lang/Boolean;"}, {"nme": "lambda$static$51", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/Boolean;"}, {"nme": "lambda$static$50", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$49", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$48", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/Map;)Ljava/nio/file/Path;"}, {"nme": "lambda$static$47", "acc": 4106, "dsc": "(Ljava/util/Map;)Ljava/nio/file/Path;"}, {"nme": "lambda$static$46", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)<PERSON><PERSON><PERSON>/lang/Boolean;"}, {"nme": "lambda$static$45", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/Boolean;"}, {"nme": "lambda$static$44", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)<PERSON><PERSON><PERSON>/lang/Boolean;"}, {"nme": "lambda$static$43", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/Boolean;"}, {"nme": "lambda$static$42", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)<PERSON><PERSON><PERSON>/lang/Boolean;"}, {"nme": "lambda$static$41", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/Boolean;"}, {"nme": "lambda$static$40", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/Map;)Ljava/nio/file/Path;"}, {"nme": "lambda$static$39", "acc": 4106, "dsc": "(Ljava/util/Map;)Ljava/nio/file/Path;"}, {"nme": "lambda$static$38", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/Map;)Ljava/nio/file/Path;"}, {"nme": "lambda$static$37", "acc": 4106, "dsc": "(Ljava/util/Map;)Ljava/nio/file/Path;"}, {"nme": "lambda$static$36", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$35", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$34", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$33", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$32", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$31", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/util/Map;)Ljava/util/List;"}, {"nme": "lambda$static$30", "acc": 4106, "dsc": "(Ljava/util/Map;)Ljava/util/List;"}, {"nme": "lambda$static$29", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/util/Map;)Ljava/util/List;"}, {"nme": "lambda$static$28", "acc": 4106, "dsc": "(Ljava/util/Map;)Ljava/util/List;"}, {"nme": "lambda$static$27", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$26", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$25", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$24", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$23", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$22", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$21", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$20", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$19", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/Map;)Ljava/nio/file/Path;"}, {"nme": "lambda$static$18", "acc": 4106, "dsc": "(Ljava/util/Map;)Ljava/nio/file/Path;"}, {"nme": "lambda$static$17", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$16", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$15", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$14", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$13", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$12", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$11", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/Map;)Ljava/nio/file/Path;"}, {"nme": "lambda$static$10", "acc": 4106, "dsc": "(Ljava/util/Map;)Ljava/nio/file/Path;"}, {"nme": "lambda$static$9", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/Map;)Ljava/nio/file/Path;"}, {"nme": "lambda$static$8", "acc": 4106, "dsc": "(Ljava/util/Map;)Ljava/nio/file/Path;"}, {"nme": "lambda$static$7", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$6", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$5", "acc": 4106, "dsc": "(Ljava/util/Map;)Ljava/nio/file/Path;"}, {"nme": "lambda$static$4", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/Map;)Ljava/nio/file/Path;"}, {"nme": "lambda$static$3", "acc": 4106, "dsc": "(Ljava/util/Map;)Ljava/nio/file/Path;"}, {"nme": "lambda$static$2", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/Map;)Ljava/nio/file/Path;"}, {"nme": "lambda$static$1", "acc": 4106, "dsc": "(Ljava/util/Map;)Ljava/nio/file/Path;"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "(Ljava/util/Map;)Ljdk/jpackage/internal/LauncherData;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "JAVABASEJMOD", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "java.base.jmod"}, {"acc": 26, "nme": "DEFAULT_VERSION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "1.0"}, {"acc": 26, "nme": "DEFAULT_RELEASE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "1"}, {"acc": 26, "nme": "DEFAULT_JLINK_OPTIONS", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 24, "nme": "LAUNCHER_DATA", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljdk/jpackage/internal/LauncherData;>;"}, {"acc": 24, "nme": "SOURCE_DIR", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/nio/file/Path;>;"}, {"acc": 24, "nme": "OUTPUT_DIR", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/nio/file/Path;>;"}, {"acc": 24, "nme": "MAIN_JAR", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/nio/file/Path;>;"}, {"acc": 24, "nme": "MAIN_CLASS", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/lang/String;>;"}, {"acc": 24, "nme": "PREDEFINED_RUNTIME_IMAGE", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/nio/file/Path;>;"}, {"acc": 24, "nme": "PREDEFINED_APP_IMAGE", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/nio/file/Path;>;"}, {"acc": 24, "nme": "NAME", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/lang/String;>;"}, {"acc": 24, "nme": "APP_NAME", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/lang/String;>;"}, {"acc": 24, "nme": "INSTALLER_NAME", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/lang/String;>;"}, {"acc": 24, "nme": "ICON", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/nio/file/Path;>;"}, {"acc": 24, "nme": "ABOUT_URL", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/lang/String;>;"}, {"acc": 24, "nme": "VENDOR", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/lang/String;>;"}, {"acc": 24, "nme": "DESCRIPTION", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/lang/String;>;"}, {"acc": 24, "nme": "COPYRIGHT", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/lang/String;>;"}, {"acc": 24, "nme": "ARGUMENTS", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/util/List<Ljava/lang/String;>;>;"}, {"acc": 24, "nme": "JAVA_OPTIONS", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/util/List<Ljava/lang/String;>;>;"}, {"acc": 24, "nme": "VERSION", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/lang/String;>;"}, {"acc": 24, "nme": "RELEASE", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/lang/String;>;"}, {"acc": 25, "nme": "LICENSE_FILE", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/lang/String;>;"}, {"acc": 24, "nme": "TEMP_ROOT", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/nio/file/Path;>;"}, {"acc": 25, "nme": "CONFIG_ROOT", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/nio/file/Path;>;"}, {"acc": 24, "nme": "VERBOSE", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/lang/Boolean;>;"}, {"acc": 24, "nme": "SHORTCUT_HINT", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/lang/Boolean;>;"}, {"acc": 24, "nme": "MENU_HINT", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/lang/Boolean;>;"}, {"acc": 24, "nme": "RESOURCE_DIR", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/nio/file/Path;>;"}, {"acc": 24, "nme": "INSTALL_DIR", "dsc": "Ljdk/jpackage/internal/BundlerParamInfo;", "sig": "Ljdk/jpackage/internal/BundlerParamInfo<Ljava/lang/String;>;"}, {"acc": 24, "nme": "LAUNCHER_AS_SERVICE", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/lang/Boolean;>;"}, {"acc": 24, "nme": "ADD_LAUNCHERS", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/util/List<Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;>;>;"}, {"acc": 24, "nme": "FILE_ASSOCIATIONS", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/util/List<Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;>;>;"}, {"acc": 24, "nme": "FA_EXTENSIONS", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/util/List<Ljava/lang/String;>;>;"}, {"acc": 24, "nme": "FA_CONTENT_TYPE", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/util/List<Ljava/lang/String;>;>;"}, {"acc": 24, "nme": "FA_DESCRIPTION", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/lang/String;>;"}, {"acc": 24, "nme": "FA_ICON", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/nio/file/Path;>;"}, {"acc": 24, "nme": "DMG_CONTENT", "dsc": "Ljdk/jpackage/internal/BundlerParamInfo;", "sig": "Ljdk/jpackage/internal/BundlerParamInfo<Ljava/util/List<Ljava/lang/String;>;>;"}, {"acc": 24, "nme": "APP_CONTENT", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/util/List<Ljava/lang/String;>;>;"}, {"acc": 24, "nme": "MODULE_PATH", "dsc": "Ljdk/jpackage/internal/BundlerParamInfo;", "sig": "Ljdk/jpackage/internal/BundlerParamInfo<Ljava/util/List<Ljava/nio/file/Path;>;>;"}, {"acc": 24, "nme": "MODULE", "dsc": "Ljdk/jpackage/internal/BundlerParamInfo;", "sig": "Ljdk/jpackage/internal/BundlerParamInfo<Ljava/lang/String;>;"}, {"acc": 24, "nme": "ADD_MODULES", "dsc": "Ljdk/jpackage/internal/BundlerParamInfo;", "sig": "Ljdk/jpackage/internal/BundlerParamInfo<Ljava/util/Set<Ljava/lang/String;>;>;"}, {"acc": 24, "nme": "JLINK_OPTIONS", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/util/List<Ljava/lang/String;>;>;"}, {"acc": 24, "nme": "LIMIT_MODULES", "dsc": "Ljdk/jpackage/internal/BundlerParamInfo;", "sig": "Ljdk/jpackage/internal/BundlerParamInfo<Ljava/util/Set<Ljava/lang/String;>;>;"}, {"acc": 24, "nme": "SIGN_BUNDLE", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/lang/Boolean;>;"}, {"acc": 24, "nme": "APP_STORE", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/lang/Boolean;>;"}]}, "classes/jdk/jpackage/internal/WixAppImageFragmentBuilder$1.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/WixAppImageFragmentBuilder$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/jpackage/internal/WixAppImageFragmentBuilder;Ljava/util/Set;Ljava/util/Set;)V", "sig": "()V"}, {"nme": "copyFile", "acc": 1, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "createDirectory", "acc": 1, "dsc": "(Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 4112, "nme": "val$emptyDirs", "dsc": "<PERSON><PERSON><PERSON>/util/Set;"}, {"acc": 4112, "nme": "val$allDirs", "dsc": "<PERSON><PERSON><PERSON>/util/Set;"}]}, "classes/jdk/jpackage/internal/ApplicationLayout$PathRole.class": {"ver": 68, "acc": 16432, "nme": "jdk/jpackage/internal/ApplicationLayout$PathRole", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/jpackage/internal/ApplicationLayout$PathRole;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Ljdk/jpackage/internal/ApplicationLayout$PathRole;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/jpackage/internal/ApplicationLayout$PathRole;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "RUNTIME", "dsc": "Ljdk/jpackage/internal/ApplicationLayout$PathRole;"}, {"acc": 16409, "nme": "RUNTIME_HOME", "dsc": "Ljdk/jpackage/internal/ApplicationLayout$PathRole;"}, {"acc": 16409, "nme": "APP", "dsc": "Ljdk/jpackage/internal/ApplicationLayout$PathRole;"}, {"acc": 16409, "nme": "LAUNCHERS", "dsc": "Ljdk/jpackage/internal/ApplicationLayout$PathRole;"}, {"acc": 16409, "nme": "DESKTOP", "dsc": "Ljdk/jpackage/internal/ApplicationLayout$PathRole;"}, {"acc": 16409, "nme": "MODULES", "dsc": "Ljdk/jpackage/internal/ApplicationLayout$PathRole;"}, {"acc": 16409, "nme": "LINUX_APPLAUNCHER_LIB", "dsc": "Ljdk/jpackage/internal/ApplicationLayout$PathRole;"}, {"acc": 16409, "nme": "CONTENT", "dsc": "Ljdk/jpackage/internal/ApplicationLayout$PathRole;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/jpackage/internal/ApplicationLayout$PathRole;"}]}, "classes/jdk/jpackage/internal/Arguments.class": {"ver": 68, "acc": 33, "nme": "jdk/jpackage/internal/Arguments", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "processArguments", "acc": 1, "dsc": "()Z"}, {"nme": "validateArguments", "acc": 2, "dsc": "()V", "exs": ["jdk/jpackage/internal/PackagerException"]}, {"nme": "getPlatformBundler", "acc": 2, "dsc": "()Ljdk/jpackage/internal/Bundler;"}, {"nme": "generateBundle", "acc": 2, "dsc": "(Ljava/util/Map;)V", "sig": "(L<PERSON><PERSON>/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)V", "exs": ["jdk/jpackage/internal/PackagerException"]}, {"nme": "toCLIOption", "acc": 8, "dsc": "(Ljava/lang/String;)Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"nme": "getPropertiesFromFile", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Map;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "getArgumentList", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "putUnlessNull", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;<PERSON>java/lang/String;Lja<PERSON>/lang/Object;)V", "sig": "(L<PERSON><PERSON>/util/Map<Ljava/lang/String;-<PERSON><PERSON><PERSON>/lang/Object;>;Ljava/lang/String;Ljava/lang/Object;)V"}, {"nme": "unquoteIfNeeded", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "lambda$generateBundle$0", "acc": 4098, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "(Ljdk/jpackage/internal/Arguments$CLIOptions;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "I18N", "dsc": "Ljava/util/ResourceBundle;"}, {"acc": 26, "nme": "FA_EXTENSIONS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "extension"}, {"acc": 26, "nme": "FA_CONTENT_TYPE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "mime-type"}, {"acc": 26, "nme": "FA_DESCRIPTION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "description"}, {"acc": 26, "nme": "FA_ICON", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "icon"}, {"acc": 25, "nme": "MAC_CFBUNDLETYPEROLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "mac.CFBundleTypeRole"}, {"acc": 25, "nme": "MAC_LSHANDLERRANK", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "mac.LSHandlerRank"}, {"acc": 25, "nme": "MAC_NSSTORETYPEKEY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "mac.NSPersistentStoreTypeKey"}, {"acc": 25, "nme": "MAC_NSDOCUMENTCLASS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "mac.NSDocumentClass"}, {"acc": 25, "nme": "MAC_LSTYPEISPACKAGE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "mac.LSTypeIsPackage"}, {"acc": 25, "nme": "MAC_LSDOCINPLACE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "mac.LSSupportsOpeningDocumentsInPlace"}, {"acc": 25, "nme": "MAC_UIDOCBROWSER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "mac.UISupportsDocumentBrowser"}, {"acc": 25, "nme": "MAC_NSEXPORTABLETYPES", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "mac.NSExportableTypes"}, {"acc": 25, "nme": "MAC_UTTYPECONFORMSTO", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "mac.UTTypeConformsTo"}, {"acc": 10, "nme": "pattern", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 2, "nme": "deployParams", "dsc": "Ljdk/jpackage/internal/DeployParams;"}, {"acc": 2, "nme": "pos", "dsc": "I"}, {"acc": 2, "nme": "argList", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 2, "nme": "allOptions", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/jpackage/internal/Arguments$CLIOptions;>;"}, {"acc": 2, "nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dsc": "Z"}, {"acc": 2, "nme": "hasMainClass", "dsc": "Z"}, {"acc": 2, "nme": "hasMainModule", "dsc": "Z"}, {"acc": 1, "nme": "userProvidedBuildRoot", "dsc": "Z"}, {"acc": 2, "nme": "buildRoot", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "runtimeInstaller", "dsc": "Z"}, {"acc": 2, "nme": "addLaunchers", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/jpackage/internal/AddLauncherArguments;>;"}, {"acc": 26, "nme": "argIds", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljdk/jpackage/internal/Arguments$CLIOptions;>;"}, {"acc": 26, "nme": "argShortIds", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljdk/jpackage/internal/Arguments$CLIOptions;>;"}, {"acc": 26, "nme": "instance", "dsc": "<PERSON><PERSON><PERSON>/lang/InheritableThreadLocal;", "sig": "Ljava/lang/InheritableThreadLocal<Ljdk/jpackage/internal/Arguments;>;"}]}, "classes/jdk/jpackage/internal/I18N.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/I18N", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "getString", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "(I)[<PERSON><PERSON><PERSON>/util/ResourceBundle;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "BUNDLE", "dsc": "Ljdk/jpackage/internal/I18N$MultiResourceBundle;"}]}, "classes/jdk/jpackage/internal/WixToolset$WixToolsetType.class": {"ver": 68, "acc": 16432, "nme": "jdk/jpackage/internal/WixToolset$WixToolsetType", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/jpackage/internal/WixToolset$WixToolsetType;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;)Ljdk/jpackage/internal/WixToolset$WixToolsetType;"}, {"nme": "<init>", "acc": 130, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I[Ljdk/jpackage/internal/WixTool;)V", "sig": "([Ljdk/jpackage/internal/WixTool;)V"}, {"nme": "getTools", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljdk/jpackage/internal/WixTool;>;"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/jpackage/internal/WixToolset$WixToolsetType;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "Wix4", "dsc": "Ljdk/jpackage/internal/WixToolset$WixToolsetType;"}, {"acc": 16409, "nme": "Wix3", "dsc": "Ljdk/jpackage/internal/WixToolset$WixToolsetType;"}, {"acc": 18, "nme": "tools", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljdk/jpackage/internal/WixTool;>;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/jpackage/internal/WixToolset$WixToolsetType;"}]}, "classes/jdk/jpackage/internal/WixPipeline$Builder.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/WixPipeline$Builder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "create", "acc": 0, "dsc": "(Ljdk/jpackage/internal/WixToolset;)Ljdk/jpackage/internal/WixPipeline;"}, {"nme": "setWixObjDir", "acc": 0, "dsc": "(Ljava/nio/file/Path;)Ljdk/jpackage/internal/WixPipeline$Builder;"}, {"nme": "setWorkDir", "acc": 0, "dsc": "(Ljava/nio/file/Path;)Ljdk/jpackage/internal/WixPipeline$Builder;"}, {"nme": "setWixVariables", "acc": 0, "dsc": "(Ljava/util/Map;)Ljdk/jpackage/internal/WixPipeline$Builder;", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)Ljdk/jpackage/internal/WixPipeline$Builder;"}, {"nme": "addSource", "acc": 0, "dsc": "(Ljava/nio/file/Path;Ljava/util/Map;)Ljdk/jpackage/internal/WixPipeline$Builder;", "sig": "(Ljava/nio/file/Path;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)Ljdk/jpackage/internal/WixPipeline$Builder;"}, {"nme": "addLightOptions", "acc": 128, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)Ljdk/jpackage/internal/WixPipeline$Builder;"}, {"nme": "mapLightOptions", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/function/UnaryOperator;)Ljava/util/List;", "sig": "(Ljava/util/function/UnaryOperator<Ljava/nio/file/Path;>;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "lambda$create$1", "acc": 4106, "dsc": "(Lja<PERSON>/util/function/UnaryOperator;Ljdk/jpackage/internal/WixPipeline$WixSource;)Ljdk/jpackage/internal/WixPipeline$WixSource;"}, {"nme": "lambda$create$0", "acc": 4106, "dsc": "(Ljava/nio/file/Path;)Ljava/nio/file/Path;"}], "flds": [{"acc": 2, "nme": "workDir", "dsc": "Ljava/nio/file/Path;"}, {"acc": 2, "nme": "wixObjDir", "dsc": "Ljava/nio/file/Path;"}, {"acc": 18, "nme": "wixVariables", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"acc": 18, "nme": "lightOptions", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 18, "nme": "sources", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/jpackage/internal/WixPipeline$WixSource;>;"}]}, "classes/jdk/jpackage/internal/ExecutableRebrander$UpdateResourceAction.class": {"ver": 68, "acc": 1536, "nme": "jdk/jpackage/internal/ExecutableRebrander$UpdateResourceAction", "super": "java/lang/Object", "mthds": [{"nme": "editResource", "acc": 1025, "dsc": "(J)V", "exs": ["java/io/IOException"]}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/FunctionalInterface;"}]}, "classes/jdk/jpackage/internal/util/FileUtils$RecursiveDeleter.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/util/FileUtils$RecursiveDeleter", "super": "java/nio/file/SimpleFileVisitor", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "visitFile", "acc": 1, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/attribute/BasicFileAttributes;)Ljava/nio/file/FileVisitResult;", "exs": ["java/io/IOException"]}, {"nme": "preVisitDirectory", "acc": 1, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/attribute/BasicFileAttributes;)Ljava/nio/file/FileVisitResult;", "exs": ["java/io/IOException"]}, {"nme": "postVisitDirectory", "acc": 1, "dsc": "(Ljava/nio/file/Path;Ljava/io/IOException;)Ljava/nio/file/FileVisitResult;", "exs": ["java/io/IOException"]}, {"nme": "adjustAttributes", "acc": 10, "dsc": "(Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "runActionOnPath", "acc": 2, "dsc": "(Ljdk/jpackage/internal/util/function/ThrowingConsumer;Ljava/nio/file/Path;)V", "sig": "(Ljdk/jpackage/internal/util/function/ThrowingConsumer<Ljava/nio/file/Path;>;Ljava/nio/file/Path;)V"}, {"nme": "postVisitDirectory", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Ljava/io/IOException;)Ljava/nio/file/FileVisitResult;", "exs": ["java/io/IOException"]}, {"nme": "visitFile", "acc": 4161, "dsc": "(Lja<PERSON>/lang/Object;Ljava/nio/file/attribute/BasicFileAttributes;)Ljava/nio/file/FileVisitResult;", "exs": ["java/io/IOException"]}, {"nme": "preVisitDirectory", "acc": 4161, "dsc": "(Lja<PERSON>/lang/Object;Ljava/nio/file/attribute/BasicFileAttributes;)Ljava/nio/file/FileVisitResult;", "exs": ["java/io/IOException"]}], "flds": [{"acc": 2, "nme": "ex", "dsc": "Ljava/io/IOException;"}]}, "classes/jdk/jpackage/internal/WixFragmentBuilder$WixNamespace.class": {"ver": 68, "acc": 16433, "nme": "jdk/jpackage/internal/WixFragmentBuilder$WixNamespace", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/jpackage/internal/WixFragmentBuilder$WixNamespace;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljdk/jpackage/internal/WixFragmentBuilder$WixNamespace;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/jpackage/internal/WixFragmentBuilder$WixNamespace;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "<PERSON><PERSON><PERSON>", "dsc": "Ljdk/jpackage/internal/WixFragmentBuilder$WixNamespace;"}, {"acc": 16409, "nme": "<PERSON><PERSON>", "dsc": "Ljdk/jpackage/internal/WixFragmentBuilder$WixNamespace;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/jpackage/internal/WixFragmentBuilder$WixNamespace;"}]}, "classes/jdk/jpackage/internal/util/SkipDocumentHandler.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/util/SkipDocumentHandler", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;)V"}, {"nme": "invoke", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/reflect/Method;[Ljava/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Throwable"]}], "flds": [{"acc": 18, "nme": "target", "dsc": "Ljavax/xml/stream/XMLStreamWriter;"}]}, "classes/jdk/jpackage/internal/WixUiFragmentBuilder$Dialog.class": {"ver": 68, "acc": 16432, "nme": "jdk/jpackage/internal/WixUiFragmentBuilder$Dialog", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/jpackage/internal/WixUiFragmentBuilder$Dialog;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljdk/jpackage/internal/WixUiFragmentBuilder$Dialog;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "createPair", "acc": 8, "dsc": "(Ljdk/jpackage/internal/WixUiFragmentBuilder$Dialog;Ljdk/jpackage/internal/WixUiFragmentBuilder$Dialog;Ljava/util/List;Ljava/util/List;)Ljava/util/Map;", "sig": "(Ljdk/jpackage/internal/WixUiFragmentBuilder$Dialog;Ljdk/jpackage/internal/WixUiFragmentBuilder$Dialog;Ljava/util/List<Ljdk/jpackage/internal/WixUiFragmentBuilder$PublishBuilder;>;Ljava/util/List<Ljdk/jpackage/internal/WixUiFragmentBuilder$PublishBuilder;>;)Ljava/util/Map<Ljdk/jpackage/internal/WixUiFragmentBuilder$DialogPair;Ljava/util/List<Ljdk/jpackage/internal/WixUiFragmentBuilder$Publish;>;>;"}, {"nme": "createPair", "acc": 8, "dsc": "(Ljdk/jpackage/internal/WixUiFragmentBuilder$Dialog;Ljdk/jpackage/internal/WixUiFragmentBuilder$Dialog;Ljava/util/List;)Ljava/util/Map;", "sig": "(Ljdk/jpackage/internal/WixUiFragmentBuilder$Dialog;Ljdk/jpackage/internal/WixUiFragmentBuilder$Dialog;Ljava/util/List<Ljdk/jpackage/internal/WixUiFragmentBuilder$PublishBuilder;>;)Ljava/util/Map<Ljdk/jpackage/internal/WixUiFragmentBuilder$DialogPair;Ljava/util/List<Ljdk/jpackage/internal/WixUiFragmentBuilder$Publish;>;>;"}, {"nme": "createPairsForWixUI_InstallDir", "acc": 8, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljdk/jpackage/internal/WixUiFragmentBuilder$DialogPair;Ljava/util/List<Ljdk/jpackage/internal/WixUiFragmentBuilder$Publish;>;>;"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/jpackage/internal/WixUiFragmentBuilder$Dialog;"}, {"nme": "lambda$createPair$1", "acc": 4106, "dsc": "(Ljdk/jpackage/internal/WixUiFragmentBuilder$PublishBuilder;)Ljdk/jpackage/internal/WixUiFragmentBuilder$Publish;"}, {"nme": "lambda$createPair$0", "acc": 4106, "dsc": "(Ljdk/jpackage/internal/WixUiFragmentBuilder$PublishBuilder;)Ljdk/jpackage/internal/WixUiFragmentBuilder$Publish;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "WixUI_WelcomeDlg", "dsc": "Ljdk/jpackage/internal/WixUiFragmentBuilder$Dialog;"}, {"acc": 16409, "nme": "WixUI_LicenseAgreementDlg", "dsc": "Ljdk/jpackage/internal/WixUiFragmentBuilder$Dialog;"}, {"acc": 16409, "nme": "InstallDirDlg", "dsc": "Ljdk/jpackage/internal/WixUiFragmentBuilder$Dialog;"}, {"acc": 16409, "nme": "ShortcutPromptDlg", "dsc": "Ljdk/jpackage/internal/WixUiFragmentBuilder$Dialog;"}, {"acc": 16409, "nme": "WixUI_VerifyReadyDlg", "dsc": "Ljdk/jpackage/internal/WixUiFragmentBuilder$Dialog;"}, {"acc": 18, "nme": "id", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/jpackage/internal/WixUiFragmentBuilder$Dialog;"}]}, "classes/jdk/jpackage/internal/PackageFile.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/PackageFile", "super": "java/lang/Object", "mthds": [{"nme": "getPathInAppImage", "acc": 9, "dsc": "(Ljava/nio/file/Path;)Ljava/nio/file/Path;"}, {"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "save", "acc": 0, "dsc": "(Ljdk/jpackage/internal/ApplicationLayout;)V", "exs": ["java/io/IOException"]}, {"nme": "lambda$save$0", "acc": 4106, "dsc": "(Ljava/nio/file/Path;)Ljava/nio/file/Path;"}], "flds": [{"acc": 18, "nme": "packageName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "FILENAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": ".package"}]}, "classes/jdk/jpackage/internal/FileAssociation.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/FileAssociation", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "verify", "acc": 0, "dsc": "()V"}, {"nme": "verify", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Ljdk/jpackage/internal/FileAssociation;>;)V", "exs": ["jdk/jpackage/internal/ConfigException"]}, {"nme": "fetchFrom", "acc": 8, "dsc": "(Ljava/util/Map;)Ljava/util/List;", "sig": "(Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)Ljava/util/List<Ljdk/jpackage/internal/FileAssociation;>;"}, {"nme": "lambda$fetchFrom$0", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/Map;)Ljdk/jpackage/internal/FileAssociation;"}], "flds": [{"acc": 0, "nme": "launcherPath", "dsc": "Ljava/nio/file/Path;"}, {"acc": 0, "nme": "iconPath", "dsc": "Ljava/nio/file/Path;"}, {"acc": 0, "nme": "mimeTypes", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 0, "nme": "extensions", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 0, "nme": "description", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/jpackage/internal/LauncherAsService.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/LauncherAsService", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/Map;Ljdk/jpackage/internal/OverridableResource;)V", "sig": "(Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;Ljdk/jpackage/internal/OverridableResource;)V"}, {"nme": "getResource", "acc": 4, "dsc": "()Ljdk/jpackage/internal/OverridableResource;"}, {"nme": "getName", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDescription", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "description", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "resource", "dsc": "Ljdk/jpackage/internal/OverridableResource;"}]}, "classes/jdk/jpackage/internal/WixLauncherAsService.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/WixLauncherAsService", "super": "jdk/jpackage/internal/LauncherAsService", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/util/Map;)V", "sig": "(Lja<PERSON>/lang/String;Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)V"}, {"nme": "setLauncherInstallPath", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljdk/jpackage/internal/WixLauncherAsService;"}, {"nme": "setLauncherInstallPathId", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljdk/jpackage/internal/WixLauncherAsService;"}, {"nme": "writeServiceConfig", "acc": 0, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;)V", "exs": ["javax/xml/stream/XMLStreamException", "java/io/IOException"]}, {"nme": "writeServiceInstall", "acc": 0, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;)V", "exs": ["javax/xml/stream/XMLStreamException", "java/io/IOException"]}, {"nme": "addSubstitutionDataEntry", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;)Ljdk/jpackage/internal/WixLauncherAsService;"}, {"nme": "setPublicName", "acc": 2, "dsc": "(Ljdk/jpackage/internal/OverridableResource;)Ljdk/jpackage/internal/OverridableResource;"}, {"nme": "writeResource", "acc": 2, "dsc": "(Ljdk/jpackage/internal/OverridableResource;Ljavax/xml/stream/XMLStreamWriter;)V", "exs": ["javax/xml/stream/XMLStreamException", "java/io/IOException"]}], "flds": [{"acc": 18, "nme": "serviceConfigResource", "dsc": "Ljdk/jpackage/internal/OverridableResource;"}]}, "classes/jdk/jpackage/internal/CLIHelp$1.class": {"ver": 68, "acc": 4128, "nme": "jdk/jpackage/internal/CLIHelp$1", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$jdk$internal$util$OperatingSystem", "dsc": "[I"}]}, "classes/jdk/jpackage/internal/MsiVersion.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/MsiVersion", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "of", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljdk/jpackage/internal/DottedVersion;"}], "flds": []}, "classes/jdk/jpackage/internal/WixPipeline.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/WixPipeline", "super": "java/lang/Object", "mthds": [{"nme": "build", "acc": 8, "dsc": "()Ljdk/jpackage/internal/WixPipeline$Builder;"}, {"nme": "<init>", "acc": 2, "dsc": "(Ljdk/jpackage/internal/WixToolset;Ljava/nio/file/Path;Ljava/nio/file/Path;Ljava/util/Map;Ljava/util/List;Ljava/util/List;)V", "sig": "(Ljdk/jpackage/internal/WixToolset;Ljava/nio/file/Path;Ljava/nio/file/Path;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;Ljava/util/List<Ljava/lang/String;>;Ljava/util/List<Ljdk/jpackage/internal/WixPipeline$WixSource;>;)V"}, {"nme": "buildMsi", "acc": 0, "dsc": "(Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "addWixVariblesToCommandLine", "acc": 2, "dsc": "(Ljava/util/Map;Ljava/util/List;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;Ljava/util/List<Ljava/lang/String;>;)V"}, {"nme": "buildMsiWix4", "acc": 2, "dsc": "(Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "buildMsiWix3", "acc": 2, "dsc": "(Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "compileWix3", "acc": 2, "dsc": "(Ljdk/jpackage/internal/WixPipeline$WixSource;)Ljava/nio/file/Path;", "exs": ["java/io/IOException"]}, {"nme": "execute", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/String;>;)V", "exs": ["java/io/IOException"]}, {"nme": "lambda$buildMsiWix4$1", "acc": 4106, "dsc": "(Ljdk/jpackage/internal/WixPipeline$WixSource;)Ljava/lang/String;"}, {"nme": "lambda$buildMsiWix4$0", "acc": 4106, "dsc": "(Ljdk/jpackage/internal/WixPipeline$WixSource;)Ljava/util/stream/Stream;"}, {"nme": "lambda$addWixVariblesToCommandLine$1", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/util/List;Ljava/util/Map;)V"}, {"nme": "lambda$addWixVariblesToCommandLine$5", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/util/List;)<PERSON>java/util/List;"}, {"nme": "lambda$addWixVariblesToCommandLine$4", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;"}, {"nme": "lambda$addWixVariblesToCommandLine$3", "acc": 4106, "dsc": "(Ljava/util/Map$Entry;)Ljava/util/stream/Stream;"}, {"nme": "lambda$addWixVariblesToCommandLine$2", "acc": 4106, "dsc": "(Ljava/util/Map$Entry;)Ljava/lang/String;"}, {"nme": "lambda$addWixVariblesToCommandLine$0", "acc": 4106, "dsc": "(Ljava/util/Map;Ljava/util/Map;)Ljava/util/Map;"}], "flds": [{"acc": 18, "nme": "toolset", "dsc": "Ljdk/jpackage/internal/WixToolset;"}, {"acc": 18, "nme": "wixVariables", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"acc": 18, "nme": "lightOptions", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 18, "nme": "wixObjDir", "dsc": "Ljava/nio/file/Path;"}, {"acc": 18, "nme": "workDir", "dsc": "Ljava/nio/file/Path;"}, {"acc": 18, "nme": "sources", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/jpackage/internal/WixPipeline$WixSource;>;"}]}, "classes/jdk/jpackage/internal/ValidOptions$USE.class": {"ver": 68, "acc": 16432, "nme": "jdk/jpackage/internal/ValidOptions$USE", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/jpackage/internal/ValidOptions$USE;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Ljdk/jpackage/internal/ValidOptions$USE;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/jpackage/internal/ValidOptions$USE;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "ALL", "dsc": "Ljdk/jpackage/internal/ValidOptions$USE;"}, {"acc": 16409, "nme": "LAUNCHER", "dsc": "Ljdk/jpackage/internal/ValidOptions$USE;"}, {"acc": 16409, "nme": "INSTALL", "dsc": "Ljdk/jpackage/internal/ValidOptions$USE;"}, {"acc": 16409, "nme": "SIGN", "dsc": "Ljdk/jpackage/internal/ValidOptions$USE;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/jpackage/internal/ValidOptions$USE;"}]}, "classes/jdk/jpackage/internal/util/function/ThrowingBiConsumer.class": {"ver": 68, "acc": 1537, "nme": "jdk/jpackage/internal/util/function/ThrowingBiConsumer", "super": "java/lang/Object", "mthds": [{"nme": "accept", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TT;TU;)V", "exs": ["java/lang/Throwable"]}, {"nme": "toBiConsumer", "acc": 9, "dsc": "(Ljdk/jpackage/internal/util/function/ThrowingBiConsumer;)Ljava/util/function/BiConsumer;", "sig": "<T:Ljava/lang/Object;U:Ljava/lang/Object;>(Ljdk/jpackage/internal/util/function/ThrowingBiConsumer<TT;TU;>;)Ljava/util/function/BiConsumer<TT;TU;>;"}, {"nme": "lambda$toBiConsumer$0", "acc": 4106, "dsc": "(Ljdk/jpackage/internal/util/function/ThrowingBiConsumer;Ljava/lang/Object;Ljava/lang/Object;)V"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/FunctionalInterface;"}]}, "classes/jdk/jpackage/internal/util/FileUtils$1.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/util/FileUtils$1", "super": "java/nio/file/SimpleFileVisitor", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(L<PERSON><PERSON>/util/List;Ljava/util/List;Ljava/nio/file/Path;Ljava/nio/file/Path;)V"}, {"nme": "preVisitDirectory", "acc": 1, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/attribute/BasicFileAttributes;)Ljava/nio/file/FileVisitResult;"}, {"nme": "visitFile", "acc": 1, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/attribute/BasicFileAttributes;)Ljava/nio/file/FileVisitResult;"}, {"nme": "visitFile", "acc": 4161, "dsc": "(Lja<PERSON>/lang/Object;Ljava/nio/file/attribute/BasicFileAttributes;)Ljava/nio/file/FileVisitResult;", "exs": ["java/io/IOException"]}, {"nme": "preVisitDirectory", "acc": 4161, "dsc": "(Lja<PERSON>/lang/Object;Ljava/nio/file/attribute/BasicFileAttributes;)Ljava/nio/file/FileVisitResult;", "exs": ["java/io/IOException"]}], "flds": [{"acc": 4112, "nme": "val$excludes", "dsc": "<PERSON><PERSON><PERSON>/util/List;"}, {"acc": 4112, "nme": "val$copyActions", "dsc": "<PERSON><PERSON><PERSON>/util/List;"}, {"acc": 4112, "nme": "val$dest", "dsc": "Ljava/nio/file/Path;"}, {"acc": 4112, "nme": "val$src", "dsc": "Ljava/nio/file/Path;"}]}, "classes/jdk/jpackage/internal/JLinkBundlerHelper.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/JLinkBundlerHelper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "execute", "acc": 8, "dsc": "(Ljava/util/Map;Ljava/nio/file/Path;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;Ljava/nio/file/Path;)V", "exs": ["java/io/IOException", "jdk/jpackage/internal/PackagerException"]}, {"nme": "getDefaultModules", "acc": 10, "dsc": "(L<PERSON><PERSON>/util/Collection;Ljava/util/Collection;)Ljava/util/Set;", "sig": "(Ljava/util/Collection<Ljava/nio/file/Path;>;Ljava/util/Collection<Ljava/lang/String;>;)Ljava/util/Set<Ljava/lang/String;>;"}, {"nme": "exportsAPI", "acc": 10, "dsc": "(Ljava/lang/module/ModuleDescriptor;)Z"}, {"nme": "createModuleFinder", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Ljava/lang/module/ModuleFinder;", "sig": "(Ljava/util/Collection<Ljava/nio/file/Path;>;)Ljava/lang/module/ModuleFinder;"}, {"nme": "createModuleList", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/util/Set;<PERSON><PERSON><PERSON>/util/Set;)<PERSON><PERSON><PERSON>/util/Set;", "sig": "(Ljava/util/List<Ljava/nio/file/Path;>;Ljava/util/Set<Ljava/lang/String;>;Ljava/util/Set<Ljava/lang/String;>;)Ljava/util/Set<Ljava/lang/String;>;"}, {"nme": "runJLink", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/nio/file/Path;Ljava/util/List;<PERSON><PERSON><PERSON>/util/Set;Ljava/util/Set;L<PERSON>va/util/List;)V", "sig": "(Ljava/nio/file/Path;Ljava/util/List<Ljava/nio/file/Path;>;Ljava/util/Set<Ljava/lang/String;>;Ljava/util/Set<Ljava/lang/String;>;Ljava/util/List<Ljava/lang/String;>;)V", "exs": ["jdk/jpackage/internal/PackagerException", "java/io/IOException"]}, {"nme": "getPathList", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Ljava/lang/String;", "sig": "(Ljava/util/List<Ljava/nio/file/Path;>;)Ljava/lang/String;"}, {"nme": "getStringList", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)<PERSON>ja<PERSON>/lang/String;", "sig": "(<PERSON><PERSON><PERSON>/util/Set<Ljava/lang/String;>;)Ljava/lang/String;"}, {"nme": "lambda$createModuleList$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/util/Set;)Ljava/util/Collection;"}, {"nme": "lambda$createModuleList$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Ljava/util/Collection;"}, {"nme": "lambda$createModuleFinder$0", "acc": 4106, "dsc": "(I)[Ljava/nio/file/Path;"}, {"nme": "lambda$exportsAPI$0", "acc": 4106, "dsc": "(Ljava/lang/module/ModuleDescriptor$Exports;)Z"}], "flds": [{"acc": 26, "nme": "ALL_MODULE_PATH", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "ALL-MODULE-PATH"}, {"acc": 26, "nme": "ALL_DEFAULT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "ALL-DEFAULT"}]}, "classes/jdk/jpackage/internal/ExecutableRebrander.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/ExecutableRebrander", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "rebrandInstaller", "acc": 0, "dsc": "(Ljava/util/Map;Ljava/nio/file/Path;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "rebrandLauncher", "acc": 0, "dsc": "(Ljava/util/Map;Ljava/nio/file/Path;Ljava/nio/file/Path;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;Ljava/nio/file/Path;Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "rebrandExecutable", "acc": 2, "dsc": "(Ljava/util/Map;Ljava/nio/file/Path;Ljava/util/function/Supplier;Ljava/nio/file/Path;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;Ljava/nio/file/Path;Ljava/util/function/Supplier<Ljdk/jpackage/internal/OverridableResource;>;Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "addAction", "acc": 0, "dsc": "(Ljdk/jpackage/internal/ExecutableRebrander$UpdateResourceAction;)Ljdk/jpackage/internal/ExecutableRebrander;"}, {"nme": "rebrandExecutable", "acc": 2, "dsc": "(Ljava/util/Map;Ljava/nio/file/Path;Ljdk/jpackage/internal/ExecutableRebrander$UpdateResourceAction;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;Ljava/nio/file/Path;Ljdk/jpackage/internal/ExecutableRebrander$UpdateResourceAction;)V", "exs": ["java/io/IOException"]}, {"nme": "getFixedFileVersion", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "createSubstituteData", "acc": 2, "dsc": "(Ljava/util/Map;)Ljava/util/Map;", "sig": "(Ljava/util/Map<Ljava/lang/String;-L<PERSON>va/lang/Object;>;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "rebrandProperties", "acc": 2, "dsc": "(JLjdk/jpackage/internal/OverridableResource;Ljava/util/Map;Ljava/nio/file/Path;)V", "sig": "(JLjdk/jpackage/internal/OverridableResource;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "validateValueAndPut", "acc": 10, "dsc": "(Ljava/util/Map;Ljava/lang/String;Ljdk/jpackage/internal/BundlerParamInfo;Ljava/util/Map;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;Ljava/lang/String;Ljdk/jpackage/internal/BundlerParamInfo<Ljava/lang/String;>;Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)V"}, {"nme": "iconSwapWrapper", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "versionSwapWrapper", "acc": 10, "dsc": "(J[<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "lockResource", "acc": 266, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)J"}, {"nme": "unlockResource", "acc": 266, "dsc": "(J)Z"}, {"nme": "iconSwap", "acc": 266, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "versionSwap", "acc": 266, "dsc": "(J[<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "lambda$rebrandProperties$1", "acc": 4106, "dsc": "(I)[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$rebrandProperties$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "lambda$rebrandExecutable$0", "acc": 4098, "dsc": "(Ljava/util/function/Supplier;Ljava/util/Map;Ljava/nio/file/Path;Ljava/nio/file/Path;J)V", "exs": ["java/io/IOException"]}, {"nme": "lambda$rebrandLauncher$0", "acc": 4106, "dsc": "(Ljava/util/Map;)Ljdk/jpackage/internal/OverridableResource;"}, {"nme": "lambda$rebrandInstaller$0", "acc": 4106, "dsc": "(Ljava/util/Map;)Ljdk/jpackage/internal/OverridableResource;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "I18N", "dsc": "Ljava/util/ResourceBundle;"}, {"acc": 26, "nme": "LAUNCHER_PROPERTIES_TEMPLATE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "WinLauncher.template"}, {"acc": 26, "nme": "INSTALLER_PROPERTIES_TEMPLATE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "WinInstaller.template"}, {"acc": 26, "nme": "INSTALLER_PROPERTIES_RESOURE_DIR_ID", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "WinInstaller.properties"}, {"acc": 2, "nme": "extraActions", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/jpackage/internal/ExecutableRebrander$UpdateResourceAction;>;"}]}, "classes/jdk/jpackage/internal/WinMsiBundler$1.class": {"ver": 68, "acc": 4128, "nme": "jdk/jpackage/internal/WinMsiBundler$1", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$jdk$jpackage$internal$WixToolset$WixToolsetType", "dsc": "[I"}]}, "classes/jdk/jpackage/internal/util/function/ExceptionBox.class": {"ver": 68, "acc": 33, "nme": "jdk/jpackage/internal/util/function/ExceptionBox", "super": "java/lang/RuntimeException", "mthds": [{"nme": "reth<PERSON><PERSON><PERSON><PERSON>ed", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)<PERSON>ja<PERSON>/lang/RuntimeException;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}]}, "classes/jdk/jpackage/internal/WixAppImageFragmentBuilder$2.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/WixAppImageFragmentBuilder$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/jpackage/internal/WixAppImageFragmentBuilder;Ljava/util/List;)V", "sig": "()V"}, {"nme": "copyFile", "acc": 1, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "createDirectory", "acc": 1, "dsc": "(Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 4112, "nme": "val$files", "dsc": "<PERSON><PERSON><PERSON>/util/List;"}]}, "classes/jdk/jpackage/internal/WixAppImageFragmentBuilder$ShortcutsFolder.class": {"ver": 68, "acc": 16432, "nme": "jdk/jpackage/internal/WixAppImageFragmentBuilder$ShortcutsFolder", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/jpackage/internal/WixAppImageFragmentBuilder$ShortcutsFolder;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;)Ljdk/jpackage/internal/WixAppImageFragmentBuilder$ShortcutsFolder;"}, {"nme": "<init>", "acc": 2, "dsc": "(Ljava/lang/String;ILjava/nio/file/Path;Ljdk/jpackage/internal/Arguments$CLIOptions;Ljava/lang/String;Ljava/lang/String;)V", "sig": "(Ljava/nio/file/Path;Ljdk/jpackage/internal/Arguments$CLIOptions;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 0, "dsc": "(Ljdk/jpackage/internal/WixAppImageFragmentBuilder;)Ljava/nio/file/Path;"}, {"nme": "requested", "acc": 0, "dsc": "(Ljava/util/Map;)Z", "sig": "(<PERSON><PERSON><PERSON>/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)Z"}, {"nme": "getWixVariableName", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/jpackage/internal/WixAppImageFragmentBuilder$ShortcutsFolder;"}, {"nme": "lambda$new$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)<PERSON><PERSON><PERSON>/lang/Boolean;"}, {"nme": "lambda$new$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/Boolean;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "ProgramMenu", "dsc": "Ljdk/jpackage/internal/WixAppImageFragmentBuilder$ShortcutsFolder;"}, {"acc": 16409, "nme": "Desktop", "dsc": "Ljdk/jpackage/internal/WixAppImageFragmentBuilder$ShortcutsFolder;"}, {"acc": 18, "nme": "root", "dsc": "Ljava/nio/file/Path;"}, {"acc": 18, "nme": "property", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "wixVariableName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "bundlerParam", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/lang/Boolean;>;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/jpackage/internal/WixAppImageFragmentBuilder$ShortcutsFolder;"}]}, "classes/jdk/jpackage/internal/Bundlers.class": {"ver": 68, "acc": 1537, "nme": "jdk/jpackage/internal/Bundlers", "super": "java/lang/Object", "mthds": [{"nme": "createBundlersInstance", "acc": 9, "dsc": "()Ljdk/jpackage/internal/Bundlers;"}, {"nme": "createBundlersInstance", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;)Ljdk/jpackage/internal/Bundlers;"}, {"nme": "getBundlers", "acc": 1025, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Ljdk/jpackage/internal/Bundler;>;"}, {"nme": "getBundlers", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Collection;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/Collection<Ljdk/jpackage/internal/Bundler;>;"}, {"nme": "loadBundlersFromServices", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;)V"}], "flds": []}, "classes/jdk/jpackage/internal/AddLauncherArguments.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/AddLauncherArguments", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "initLauncherMap", "acc": 2, "dsc": "()V"}, {"nme": "getOptionValue", "acc": 2, "dsc": "(Ljdk/jpackage/internal/Arguments$CLIOptions;)Ljava/lang/String;"}, {"nme": "getLauncherMap", "acc": 0, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;"}, {"nme": "merge", "acc": 136, "dsc": "(Ljava/util/Map;Ljava/util/Map;[Ljava/lang/String;)Ljava/util/Map;", "sig": "(Ljava/util/Map<Ljava/lang/String;-<PERSON><PERSON><PERSON>/lang/Object;>;Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;[Ljava/lang/String;)Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;"}, {"nme": "lambda$initLauncherMap$0", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;)Ljava/nio/file/Path;"}], "flds": [{"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "filename", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "allArgs", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"acc": 2, "nme": "bundleParams", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;"}]}, "classes/jdk/jpackage/internal/OverridableResource$2.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/OverridableResource$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/jpackage/internal/OverridableResource;Ljava/nio/file/Path;)V", "sig": "()V"}, {"nme": "publicName", "acc": 1, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "consume", "acc": 1, "dsc": "(<PERSON><PERSON>va/io/InputStream;)V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 4112, "nme": "val$dest", "dsc": "Ljava/nio/file/Path;"}]}, "classes/jdk/jpackage/internal/Enquoter.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/Enquoter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "for<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 8, "dsc": "()Ljdk/jpackage/internal/Enquoter;"}, {"nme": "forShellLiterals", "acc": 8, "dsc": "()Ljdk/jpackage/internal/Enquoter;"}, {"nme": "forShellLiterals", "acc": 8, "dsc": "(C)Ljdk/jpackage/internal/Enquoter;"}, {"nme": "applyTo", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "setQuoteChar", "acc": 0, "dsc": "(C)Ljdk/jpackage/internal/Enquoter;"}, {"nme": "setEscaper", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/function/BiConsumer;)Ljdk/jpackage/internal/Enquoter;", "sig": "(<PERSON><PERSON><PERSON>/util/function/BiConsumer<Ljava/lang/Integer;Ljava/lang/StringBuilder;>;)Ljdk/jpackage/internal/Enquoter;"}, {"nme": "setEnquotePredicate", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Predicate;)Ljdk/jpackage/internal/Enquoter;", "sig": "(L<PERSON><PERSON>/util/function/Predicate<Ljava/lang/String;>;)Ljdk/jpackage/internal/Enquoter;"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Integer;<PERSON><PERSON><PERSON>/lang/StringBuilder;)V"}, {"nme": "lambda$new$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "lambda$applyTo$2", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "lambda$applyTo$0", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/StringBuilder;<PERSON>ja<PERSON>/util/function/BiConsumer;)V"}, {"nme": "lambda$applyTo$1", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;I)V"}, {"nme": "lambda$forShellLiterals$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "beginQuoteChr", "dsc": "I"}, {"acc": 2, "nme": "endQuoteChr", "dsc": "I"}, {"acc": 2, "nme": "escaper", "dsc": "Ljava/util/function/BiConsumer;", "sig": "Ljava/util/function/BiConsumer<Ljava/lang/Integer;Ljava/lang/StringBuilder;>;"}, {"acc": 2, "nme": "needQuotes", "dsc": "<PERSON><PERSON><PERSON>/util/function/Predicate;", "sig": "Ljava/util/function/Predicate<Ljava/lang/String;>;"}, {"acc": 26, "nme": "QUOTE_IF_WHITESPACES", "dsc": "<PERSON><PERSON><PERSON>/util/function/Predicate;", "sig": "Ljava/util/function/Predicate<Ljava/lang/String;>;"}, {"acc": 26, "nme": "PREPEND_BACKSLASH", "dsc": "Ljava/util/function/BiConsumer;", "sig": "Ljava/util/function/BiConsumer<Ljava/lang/Integer;Ljava/lang/StringBuilder;>;"}]}, "classes/jdk/jpackage/internal/WinMsiBundler.class": {"ver": 68, "acc": 33, "nme": "jdk/jpackage/internal/WinMsiBundler", "super": "jdk/jpackage/internal/AbstractBundler", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getID", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getBundleType", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "supported", "acc": 1, "dsc": "(Z)Z"}, {"nme": "isDefault", "acc": 1, "dsc": "()Z"}, {"nme": "getUpgradeCode", "acc": 10, "dsc": "(Ljava/util/Map;)Ljava/util/UUID;", "sig": "(Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)Ljava/util/UUID;"}, {"nme": "getProductCode", "acc": 10, "dsc": "(Ljava/util/Map;)Ljava/util/UUID;", "sig": "(Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)Ljava/util/UUID;"}, {"nme": "createNameUUID", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/util/Map;Ljava/util/List;)Ljava/util/UUID;", "sig": "(Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;Ljava/util/List<Ljdk/jpackage/internal/StandardBundlerParam<Ljava/lang/String;>;>;)Ljava/util/UUID;"}, {"nme": "validate", "acc": 1, "dsc": "(Ljava/util/Map;)Z", "sig": "(<PERSON><PERSON><PERSON>/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)Z", "exs": ["jdk/jpackage/internal/ConfigException"]}, {"nme": "prepareProto", "acc": 2, "dsc": "(Ljava/util/Map;)V", "sig": "(L<PERSON><PERSON>/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)V", "exs": ["jdk/jpackage/internal/PackagerException", "java/io/IOException"]}, {"nme": "execute", "acc": 1, "dsc": "(Ljava/util/Map;Ljava/nio/file/Path;)Ljava/nio/file/Path;", "sig": "(Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;Ljava/nio/file/Path;)Ljava/nio/file/Path;", "exs": ["jdk/jpackage/internal/PackagerException"]}, {"nme": "getAppImageSizeKb", "acc": 2, "dsc": "(Ljava/util/Map;)J", "sig": "(<PERSON><PERSON><PERSON>/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)J", "exs": ["java/io/IOException"]}, {"nme": "prepareMainProjectFile", "acc": 2, "dsc": "(Ljava/util/Map;)Ljava/util/Map;", "sig": "(Ljava/util/Map<Ljava/lang/String;-L<PERSON>va/lang/Object;>;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;", "exs": ["java/io/IOException"]}, {"nme": "buildMSI", "acc": 2, "dsc": "(Ljava/util/Map;Ljava/util/Map;Ljava/nio/file/Path;)Ljava/nio/file/Path;", "sig": "(Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;Ljava/nio/file/Path;)Ljava/nio/file/Path;", "exs": ["java/io/IOException"]}, {"nme": "getWxlFilesFromDir", "acc": 10, "dsc": "(Ljava/util/Map;Ljdk/jpackage/internal/StandardBundlerParam;)Ljava/util/List;", "sig": "(Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;Ljdk/jpackage/internal/StandardBundlerParam<Ljava/nio/file/Path;>;)Ljava/util/List<Ljava/nio/file/Path;>;", "exs": ["java/io/IOException"]}, {"nme": "getCultureFromWxlFile", "acc": 10, "dsc": "(Lja<PERSON>/nio/file/Path;)Ljava/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "ensureByMutationFileIsRTF", "acc": 10, "dsc": "(Ljava/nio/file/Path;)V"}, {"nme": "initServiceInstallerResource", "acc": 10, "dsc": "(Ljava/util/Map;)Ljdk/jpackage/internal/OverridableResource;", "sig": "(Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)Ljdk/jpackage/internal/OverridableResource;", "exs": ["jdk/jpackage/internal/ConfigException"]}, {"nme": "cleanup", "acc": 4161, "dsc": "(Ljava/util/Map;)V"}, {"nme": "toString", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$ensureByMutationFileIsRTF$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/io/Writer;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "lambda$getWxlFilesFromDir$0", "acc": 4106, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/Path;)I"}, {"nme": "lambda$buildMSI$4", "acc": 4106, "dsc": "(Ljdk/jpackage/internal/WixPipeline$Builder;Ljava/lang/String;)V"}, {"nme": "lambda$buildMSI$3", "acc": 4106, "dsc": "(Ljava/nio/file/Path;)V"}, {"nme": "lambda$buildMSI$1", "acc": 4106, "dsc": "(Lja<PERSON>/util/List;Ljava/nio/file/Path;)Z"}, {"nme": "lambda$buildMSI$2", "acc": 4106, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/Path;)Z"}, {"nme": "lambda$buildMSI$0", "acc": 4106, "dsc": "(Ljava/nio/file/Path;Ljava/lang/String;)Ljava/nio/file/Path;"}, {"nme": "lambda$prepareMainProjectFile$2", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;Ljava/lang/String;)V"}, {"nme": "lambda$prepareMainProjectFile$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;Ljava/lang/String;)V"}, {"nme": "lambda$prepareMainProjectFile$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;Ljava/lang/String;)V"}, {"nme": "lambda$validate$0", "acc": 4098, "dsc": "(Ljdk/jpackage/internal/WixFragmentBuilder;)V"}, {"nme": "lambda$createNameUUID$0", "acc": 4106, "dsc": "(Ljava/util/Map;Ljdk/jpackage/internal/StandardBundlerParam;)Ljava/lang/String;"}, {"nme": "lambda$new$0", "acc": 4106, "dsc": "(Ljava/util/Map$Entry;)Ljdk/jpackage/internal/WixFragmentBuilder;"}, {"nme": "lambda$static$10", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$9", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$8", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$7", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$6", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$5", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$4", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)<PERSON><PERSON><PERSON>/lang/Boolean;"}, {"nme": "lambda$static$3", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/Boolean;"}, {"nme": "lambda$static$2", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/Map;)Ljava/nio/file/Path;"}, {"nme": "lambda$static$1", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/Map;)Ljava/nio/file/Path;"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "(Ljava/util/Map;)Ljava/nio/file/Path;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "MSI_IMAGE_DIR", "dsc": "Ljdk/jpackage/internal/BundlerParamInfo;", "sig": "Ljdk/jpackage/internal/BundlerParamInfo<Ljava/nio/file/Path;>;"}, {"acc": 25, "nme": "WIN_APP_IMAGE", "dsc": "Ljdk/jpackage/internal/BundlerParamInfo;", "sig": "Ljdk/jpackage/internal/BundlerParamInfo<Ljava/nio/file/Path;>;"}, {"acc": 24, "nme": "SERVICE_INSTALLER", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljdk/jpackage/internal/InstallableFile;>;"}, {"acc": 25, "nme": "MSI_SYSTEM_WIDE", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/lang/Boolean;>;"}, {"acc": 25, "nme": "PRODUCT_VERSION", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/lang/String;>;"}, {"acc": 26, "nme": "HELP_URL", "dsc": "Ljdk/jpackage/internal/BundlerParamInfo;", "sig": "Ljdk/jpackage/internal/BundlerParamInfo<Ljava/lang/String;>;"}, {"acc": 26, "nme": "UPDATE_URL", "dsc": "Ljdk/jpackage/internal/BundlerParamInfo;", "sig": "Ljdk/jpackage/internal/BundlerParamInfo<Ljava/lang/String;>;"}, {"acc": 26, "nme": "UPGRADE_UUID", "dsc": "Ljdk/jpackage/internal/BundlerParamInfo;", "sig": "Ljdk/jpackage/internal/BundlerParamInfo<Ljava/lang/String;>;"}, {"acc": 26, "nme": "INSTALLER_FILE_NAME", "dsc": "Ljdk/jpackage/internal/BundlerParamInfo;", "sig": "Ljdk/jpackage/internal/BundlerParamInfo<Ljava/lang/String;>;"}, {"acc": 2, "nme": "installerIcon", "dsc": "Ljava/nio/file/Path;"}, {"acc": 2, "nme": "wixToolset", "dsc": "Ljdk/jpackage/internal/WixToolset;"}, {"acc": 2, "nme": "appImageBundler", "dsc": "Ljdk/jpackage/internal/AppImageBundler;"}, {"acc": 18, "nme": "wixFragments", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/jpackage/internal/WixFragmentBuilder;>;"}]}, "classes/jdk/jpackage/internal/WixSourceConverter$NamespaceCollector.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/WixSourceConverter$NamespaceCollector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "invoke", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/reflect/Method;[Ljava/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Throwable"]}, {"nme": "isOnlyKnownNamespacesUsed", "acc": 0, "dsc": "()Z"}, {"nme": "getPrefixToUri", "acc": 0, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "initFromElementName", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "createValue", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Ljava/util/Map$Entry;", "sig": "(L<PERSON><PERSON>/lang/Object;)Ljava/util/Map$Entry<Ljava/lang/String;Ljava/lang/Boolean;>;"}, {"nme": "lambda$initFromElementName$1", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/Map$Entry;)Ljava/util/Map$Entry;"}, {"nme": "lambda$initFromElementName$0", "acc": 4098, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;)Ljava/util/Map$Entry;"}, {"nme": "lambda$getPrefixToUri$0", "acc": 4106, "dsc": "(Ljava/util/Map$Entry;)Ljava/lang/String;"}, {"nme": "lambda$isOnlyKnownNamespacesUsed$1", "acc": 4106, "dsc": "(Ljava/util/Map$Entry;)Z"}, {"nme": "lambda$isOnlyKnownNamespacesUsed$0", "acc": 4106, "dsc": "(Ljava/util/Map$Entry;)Z"}, {"nme": "lambda$invoke$1", "acc": 4098, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;Ljava/lang/String;)Ljava/util/Map$Entry;"}, {"nme": "lambda$invoke$0", "acc": 4098, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;Ljava/lang/String;)Ljava/util/Map$Entry;"}], "flds": [{"acc": 18, "nme": "prefixToUri", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/util/Map$Entry<Ljava/lang/String;Ljava/lang/Boolean;>;>;"}]}, "classes/jdk/jpackage/internal/util/PrettyPrintHandler.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/util/PrettyPrintHandler", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;)V"}, {"nme": "invoke", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/reflect/Method;[Ljava/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Throwable"]}, {"nme": "repeat", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": [{"acc": 18, "nme": "target", "dsc": "Ljavax/xml/stream/XMLStreamWriter;"}, {"acc": 2, "nme": "depth", "dsc": "I"}, {"acc": 18, "nme": "hasChildElement", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Integer;Ljava/lang/Boolean;>;"}, {"acc": 26, "nme": "INDENT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "  "}, {"acc": 26, "nme": "EOL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "\n"}]}, "classes/jdk/jpackage/internal/AbstractBundler.class": {"ver": 68, "acc": 1056, "nme": "jdk/jpackage/internal/AbstractBundler", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "cleanup", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(L<PERSON><PERSON>/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)V"}, {"nme": "lambda$static$1", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/Map;)Ljava/nio/file/Path;"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "(Ljava/util/Map;)Ljava/nio/file/Path;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "IMAGES_ROOT", "dsc": "Ljdk/jpackage/internal/BundlerParamInfo;", "sig": "Ljdk/jpackage/internal/BundlerParamInfo<Ljava/nio/file/Path;>;"}]}, "classes/jdk/jpackage/internal/util/function/ThrowingFunction.class": {"ver": 68, "acc": 1537, "nme": "jdk/jpackage/internal/util/function/ThrowingFunction", "super": "java/lang/Object", "mthds": [{"nme": "apply", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(TT;)TR;", "exs": ["java/lang/Throwable"]}, {"nme": "toFunction", "acc": 9, "dsc": "(Ljdk/jpackage/internal/util/function/ThrowingFunction;)Ljava/util/function/Function;", "sig": "<T:Ljava/lang/Object;R:Ljava/lang/Object;>(Ljdk/jpackage/internal/util/function/ThrowingFunction<TT;TR;>;)Ljava/util/function/Function<TT;TR;>;"}, {"nme": "lambda$toFunction$0", "acc": 4106, "dsc": "(Ljdk/jpackage/internal/util/function/ThrowingFunction;Ljava/lang/Object;)Ljava/lang/Object;"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/FunctionalInterface;"}]}, "classes/jdk/jpackage/internal/LauncherData.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/LauncherData", "super": "java/lang/Object", "mthds": [{"nme": "isModular", "acc": 0, "dsc": "()Z"}, {"nme": "qualifiedClassName", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isClassNameFromMainJar", "acc": 0, "dsc": "()Z"}, {"nme": "packageName", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "moduleName", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "modulePath", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/nio/file/Path;>;"}, {"nme": "mainJarName", "acc": 0, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "classPath", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/nio/file/Path;>;"}, {"nme": "getAppVersion", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "verifyIsModular", "acc": 2, "dsc": "(Z)V"}, {"nme": "create", "acc": 8, "dsc": "(Ljava/util/Map;)Ljdk/jpackage/internal/LauncherData;", "sig": "(Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)Ljdk/jpackage/internal/LauncherData;", "exs": ["jdk/jpackage/internal/ConfigException", "java/io/IOException"]}, {"nme": "createModular", "acc": 10, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/Map;)Ljdk/jpackage/internal/LauncherData;", "sig": "(Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)Ljdk/jpackage/internal/LauncherData;", "exs": ["jdk/jpackage/internal/ConfigException", "java/io/IOException"]}, {"nme": "createNonModular", "acc": 10, "dsc": "(Ljava/util/Map;)Ljdk/jpackage/internal/LauncherData;", "sig": "(Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)Ljdk/jpackage/internal/LauncherData;", "exs": ["jdk/jpackage/internal/ConfigException", "java/io/IOException"]}, {"nme": "initClasspath", "acc": 2, "dsc": "(Ljava/util/Map;)V", "sig": "(L<PERSON><PERSON>/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)V", "exs": ["java/io/IOException"]}, {"nme": "getMainClass", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;", "sig": "(L<PERSON><PERSON>/util/Map<Ljava/lang/String;-<PERSON><PERSON><PERSON>/lang/Object;>;)Ljava/lang/String;"}, {"nme": "getMainJarName", "acc": 10, "dsc": "(Ljava/util/Map;)Ljava/nio/file/Path;", "sig": "(Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)Ljava/nio/file/Path;", "exs": ["jdk/jpackage/internal/ConfigException"]}, {"nme": "getMainModule", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;", "sig": "(L<PERSON><PERSON>/util/Map<Ljava/lang/String;-<PERSON><PERSON><PERSON>/lang/Object;>;)Ljava/lang/String;"}, {"nme": "getStringParam", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;Ljava/lang/String;)Ljava/lang/String;", "sig": "(L<PERSON><PERSON>/util/Map<Ljava/lang/String;-L<PERSON><PERSON>/lang/Object;>;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "get<PERSON>ath<PERSON><PERSON><PERSON>", "acc": 10, "dsc": "(L<PERSON><PERSON>/lang/String;L<PERSON><PERSON>/util/function/Supplier;)Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/String;Ljava/util/function/Supplier<TT;>;)TT;", "exs": ["jdk/jpackage/internal/ConfigException"]}, {"nme": "get<PERSON>ath<PERSON><PERSON><PERSON>", "acc": 10, "dsc": "(Ljava/util/Map;Ljava/lang/String;)Ljava/nio/file/Path;", "sig": "(Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;Ljava/lang/String;)Ljava/nio/file/Path;", "exs": ["jdk/jpackage/internal/ConfigException"]}, {"nme": "getModulePath", "acc": 10, "dsc": "(Ljava/util/Map;)Ljava/util/List;", "sig": "(Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)Ljava/util/List<Ljava/nio/file/Path;>;", "exs": ["jdk/jpackage/internal/ConfigException"]}, {"nme": "getPathListParameter", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/util/Map;)Ljava/util/List;", "sig": "(Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)Ljava/util/List<Ljava/nio/file/Path;>;", "exs": ["jdk/jpackage/internal/ConfigException"]}, {"nme": "lambda$getPathListParameter$0", "acc": 4106, "dsc": "(L<PERSON><PERSON>/util/Map;Ljava/lang/String;)Ljava/util/List;"}, {"nme": "lambda$getPathListParameter$1", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;)Ljava/nio/file/Path;"}, {"nme": "lambda$getPathParam$0", "acc": 4106, "dsc": "(Ljava/util/Map;Ljava/lang/String;)Ljava/nio/file/Path;"}, {"nme": "lambda$initClasspath$2", "acc": 4106, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/Path;)Ljava/nio/file/Path;"}, {"nme": "lambda$initClasspath$1", "acc": 4106, "dsc": "(Ljava/nio/file/Path;)Z"}, {"nme": "lambda$initClasspath$0", "acc": 4106, "dsc": "(Ljava/nio/file/Path;)Z"}], "flds": [{"acc": 2, "nme": "qualifiedClassName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "jarMainClass", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "mainJarName", "dsc": "Ljava/nio/file/Path;"}, {"acc": 2, "nme": "classPath", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/nio/file/Path;>;"}, {"acc": 2, "nme": "modulePath", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/nio/file/Path;>;"}, {"acc": 2, "nme": "moduleInfo", "dsc": "Ljdk/jpackage/internal/LauncherData$ModuleInfo;"}]}, "classes/jdk/jpackage/internal/WixUiFragmentBuilder$1.class": {"ver": 68, "acc": 4128, "nme": "jdk/jpackage/internal/WixUiFragmentBuilder$1", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$jdk$jpackage$internal$WixToolset$WixToolsetType", "dsc": "[I"}]}, "classes/jdk/jpackage/internal/WinExeBundler.class": {"ver": 68, "acc": 33, "nme": "jdk/jpackage/internal/WinExeBundler", "super": "jdk/jpackage/internal/AbstractBundler", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getID", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getBundleType", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "execute", "acc": 1, "dsc": "(Ljava/util/Map;Ljava/nio/file/Path;)Ljava/nio/file/Path;", "sig": "(Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;Ljava/nio/file/Path;)Ljava/nio/file/Path;", "exs": ["jdk/jpackage/internal/PackagerException"]}, {"nme": "supported", "acc": 1, "dsc": "(Z)Z"}, {"nme": "isDefault", "acc": 1, "dsc": "()Z"}, {"nme": "validate", "acc": 1, "dsc": "(Ljava/util/Map;)Z", "sig": "(<PERSON><PERSON><PERSON>/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)Z", "exs": ["jdk/jpackage/internal/ConfigException"]}, {"nme": "bundle", "acc": 1, "dsc": "(Ljava/util/Map;Ljava/nio/file/Path;)Ljava/nio/file/Path;", "sig": "(Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;Ljava/nio/file/Path;)Ljava/nio/file/Path;", "exs": ["jdk/jpackage/internal/PackagerException"]}, {"nme": "buildEXE", "acc": 2, "dsc": "(Ljava/util/Map;Ljava/nio/file/Path;Ljava/nio/file/Path;)Ljava/nio/file/Path;", "sig": "(Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;Ljava/nio/file/Path;Ljava/nio/file/Path;)Ljava/nio/file/Path;", "exs": ["java/io/IOException"]}, {"nme": "embedMSI", "acc": 266, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "cleanup", "acc": 4161, "dsc": "(Ljava/util/Map;)V"}, {"nme": "toString", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$buildEXE$0", "acc": 4106, "dsc": "(Ljava/nio/file/Path;J)V", "exs": ["java/io/IOException"]}, {"nme": "lambda$static$1", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/Map;)Ljava/nio/file/Path;"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "(Ljava/util/Map;)Ljava/nio/file/Path;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "EXE_IMAGE_DIR", "dsc": "Ljdk/jpackage/internal/BundlerParamInfo;", "sig": "Ljdk/jpackage/internal/BundlerParamInfo<Ljava/nio/file/Path;>;"}, {"acc": 26, "nme": "EXE_WRAPPER_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "msiwrapper.exe"}, {"acc": 18, "nme": "msiBundler", "dsc": "Ljdk/jpackage/internal/WinMsiBundler;"}]}, "classes/jdk/jpackage/internal/PathGroup$TransformHandler.class": {"ver": 68, "acc": 1536, "nme": "jdk/jpackage/internal/PathGroup$TransformHandler", "super": "java/lang/Object", "mthds": [{"nme": "copyFile", "acc": 1025, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "createDirectory", "acc": 1025, "dsc": "(Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}], "flds": []}, "classes/jdk/jpackage/internal/WixAppImageFragmentBuilder$3.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/WixAppImageFragmentBuilder$3", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/jpackage/internal/WixAppImageFragmentBuilder;Ljava/util/List;)V", "sig": "()V"}, {"nme": "copyFile", "acc": 1, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "createDirectory", "acc": 1, "dsc": "(Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 4112, "nme": "val$icoFiles", "dsc": "<PERSON><PERSON><PERSON>/util/List;"}]}, "classes/jdk/jpackage/internal/CfgFile.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/CfgFile", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "initFromParams", "acc": 0, "dsc": "(Ljava/util/Map;)Ljdk/jpackage/internal/CfgFile;", "sig": "(Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)Ljdk/jpackage/internal/CfgFile;"}, {"nme": "create", "acc": 0, "dsc": "(Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "createAppCfgLayout", "acc": 2, "dsc": "()Ljdk/jpackage/internal/ApplicationLayout;"}, {"nme": "lambda$create$0", "acc": 4106, "dsc": "([Z<PERSON>java/util/Map$Entry;)Ljava/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "launcherName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "launcherData", "dsc": "Ljdk/jpackage/internal/LauncherData;"}, {"acc": 0, "nme": "arguments", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 0, "nme": "javaOptions", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 18, "nme": "appLayout", "dsc": "Ljdk/jpackage/internal/ApplicationLayout;"}, {"acc": 26, "nme": "SECTION_TAG", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "classes/jdk/jpackage/internal/OverridableResource.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/OverridableResource", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getResourceDir", "acc": 0, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "getDefaultName", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getPublicName", "acc": 0, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "getEx<PERSON>al<PERSON><PERSON>", "acc": 0, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "setSubstitutionData", "acc": 0, "dsc": "(Ljava/util/Map;)Ljdk/jpackage/internal/OverridableResource;", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)Ljdk/jpackage/internal/OverridableResource;"}, {"nme": "addSubstitutionDataEntry", "acc": 0, "dsc": "(L<PERSON><PERSON>/lang/String;Ljava/lang/String;)Ljdk/jpackage/internal/OverridableResource;"}, {"nme": "setCategory", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljdk/jpackage/internal/OverridableResource;"}, {"nme": "setResourceDir", "acc": 0, "dsc": "(Ljava/nio/file/Path;)Ljdk/jpackage/internal/OverridableResource;"}, {"nme": "setResourceDir", "acc": 0, "dsc": "(Ljava/io/File;)Ljdk/jpackage/internal/OverridableResource;"}, {"nme": "setSourceOrder", "acc": 128, "dsc": "([Ljdk/jpackage/internal/OverridableResource$Source;)Ljdk/jpackage/internal/OverridableResource;"}, {"nme": "setPublicName", "acc": 0, "dsc": "(Ljava/nio/file/Path;)Ljdk/jpackage/internal/OverridableResource;"}, {"nme": "setPublicName", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljdk/jpackage/internal/OverridableResource;"}, {"nme": "setLogPublicName", "acc": 0, "dsc": "(Ljava/nio/file/Path;)Ljdk/jpackage/internal/OverridableResource;"}, {"nme": "setLogPublicName", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljdk/jpackage/internal/OverridableResource;"}, {"nme": "setExternal", "acc": 0, "dsc": "(Ljava/nio/file/Path;)Ljdk/jpackage/internal/OverridableResource;"}, {"nme": "setExternal", "acc": 0, "dsc": "(Ljava/io/File;)Ljdk/jpackage/internal/OverridableResource;"}, {"nme": "saveToStream", "acc": 0, "dsc": "(Ljava/io/OutputStream;)Ljdk/jpackage/internal/OverridableResource$Source;", "exs": ["java/io/IOException"]}, {"nme": "saveInFolder", "acc": 0, "dsc": "(Ljava/nio/file/Path;)Ljdk/jpackage/internal/OverridableResource$Source;", "exs": ["java/io/IOException"]}, {"nme": "saveToFile", "acc": 0, "dsc": "(Ljava/nio/file/Path;)Ljdk/jpackage/internal/OverridableResource$Source;", "exs": ["java/io/IOException"]}, {"nme": "saveToFile", "acc": 0, "dsc": "(Ljava/io/File;)Ljdk/jpackage/internal/OverridableResource$Source;", "exs": ["java/io/IOException"]}, {"nme": "readDefault", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/io/InputStream;"}, {"nme": "createResource", "acc": 8, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/Map;)Ljdk/jpackage/internal/OverridableResource;", "sig": "(Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)Ljdk/jpackage/internal/OverridableResource;"}, {"nme": "sendToConsumer", "acc": 2, "dsc": "(Ljdk/jpackage/internal/OverridableResource$ResourceConsumer;)Ljdk/jpackage/internal/OverridableResource$Source;", "exs": ["java/io/IOException"]}, {"nme": "getPrintableCategory", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "useExternal", "acc": 2, "dsc": "(Ljdk/jpackage/internal/OverridableResource$ResourceConsumer;)Z", "exs": ["java/io/IOException"]}, {"nme": "useResourceDir", "acc": 2, "dsc": "(Ljdk/jpackage/internal/OverridableResource$ResourceConsumer;)Z", "exs": ["java/io/IOException"]}, {"nme": "useDefault", "acc": 2, "dsc": "(Ljdk/jpackage/internal/OverridableResource$ResourceConsumer;)Z", "exs": ["java/io/IOException"]}, {"nme": "substitute", "acc": 10, "dsc": "(Ljava/util/stream/Stream;Ljava/util/Map;)Ljava/util/stream/Stream;", "sig": "(Ljava/util/stream/Stream<Ljava/lang/String;>;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)Ljava/util/stream/Stream<Ljava/lang/String;>;"}, {"nme": "to<PERSON><PERSON>", "acc": 10, "dsc": "(Ljava/io/File;)Ljava/nio/file/Path;"}, {"nme": "processResourceStream", "acc": 2, "dsc": "(Ljava/io/InputStream;Ljdk/jpackage/internal/OverridableResource$ResourceConsumer;)V", "exs": ["java/io/IOException"]}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(Ljdk/jpackage/internal/OverridableResource$Source;)Ljdk/jpackage/internal/OverridableResource$SourceHandler;"}, {"nme": "lambda$substitute$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "lambda$useDefault$0", "acc": 4106, "dsc": "(Ljdk/jpackage/internal/OverridableResource$ResourceConsumer;)Ljava/nio/file/Path;"}, {"nme": "lambda$useResourceDir$0", "acc": 4106, "dsc": "(Ljdk/jpackage/internal/OverridableResource$ResourceConsumer;)Ljava/nio/file/Path;"}, {"nme": "lambda$setSourceOrder$0", "acc": 4098, "dsc": "(Ljdk/jpackage/internal/OverridableResource$Source;)Ljava/util/Map$Entry;"}, {"nme": "lambda$addSubstitutionDataEntry$1", "acc": 4098, "dsc": "(Ljava/util/Map;)V"}, {"nme": "lambda$addSubstitutionDataEntry$0", "acc": 4106, "dsc": "(Ljava/util/Map;Ljava/util/Map;)V"}], "flds": [{"acc": 2, "nme": "substitutionData", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"acc": 2, "nme": "category", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "resourceDir", "dsc": "Ljava/nio/file/Path;"}, {"acc": 2, "nme": "publicName", "dsc": "Ljava/nio/file/Path;"}, {"acc": 2, "nme": "logPublicName", "dsc": "Ljava/nio/file/Path;"}, {"acc": 2, "nme": "externalPath", "dsc": "Ljava/nio/file/Path;"}, {"acc": 18, "nme": "defaultName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "sources", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/util/Map$Entry<Ljdk/jpackage/internal/OverridableResource$Source;Ljdk/jpackage/internal/OverridableResource$SourceHandler;>;>;"}]}, "classes/jdk/jpackage/internal/WixSourceConverter$NamespaceCleaner.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/WixSourceConverter$NamespaceCleaner", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/util/Map;Ljavax/xml/stream/XMLStreamWriter;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;Ljavax/xml/stream/XMLStreamWriter;)V"}, {"nme": "invoke", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/reflect/Method;[Ljava/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Throwable"]}, {"nme": "lambda$new$1", "acc": 4106, "dsc": "(Ljdk/jpackage/internal/WixSourceConverter$NamespaceCleaner$Prefix;Ljdk/jpackage/internal/WixSourceConverter$NamespaceCleaner$Prefix;)Ljdk/jpackage/internal/WixSourceConverter$NamespaceCleaner$Prefix;"}, {"nme": "lambda$new$0", "acc": 4106, "dsc": "(Ljava/util/Map$Entry;)Ljdk/jpackage/internal/WixSourceConverter$NamespaceCleaner$Prefix;"}], "flds": [{"acc": 18, "nme": "uriToPrefix", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljdk/jpackage/internal/WixSourceConverter$NamespaceCleaner$Prefix;>;"}, {"acc": 18, "nme": "prefixToUri", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"acc": 18, "nme": "target", "dsc": "Ljavax/xml/stream/XMLStreamWriter;"}]}, "classes/jdk/jpackage/internal/WixSourceConverter$1.class": {"ver": 68, "acc": 4128, "nme": "jdk/jpackage/internal/WixSourceConverter$1", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$jdk$jpackage$internal$WixToolset$WixToolsetType", "dsc": "[I"}]}, "classes/jdk/jpackage/internal/IOUtils.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/IOUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "copyFile", "acc": 9, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "exists", "acc": 9, "dsc": "(Ljava/nio/file/Path;)Z"}, {"nme": "run", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "exec", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/ProcessBuilder;)V", "exs": ["java/io/IOException"]}, {"nme": "exec", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/ProcessBuilder;J)V", "exs": ["java/io/IOException"]}, {"nme": "exec", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/ProcessBuilder;Z)V", "exs": ["java/io/IOException"]}, {"nme": "exec", "acc": 8, "dsc": "(Lja<PERSON>/lang/ProcessBuilder;ZLjava/io/PrintStream;)V", "exs": ["java/io/IOException"]}, {"nme": "exec", "acc": 8, "dsc": "(Ljava/lang/ProcessBuilder;ZLjava/io/PrintStream;ZJ)V", "exs": ["java/io/IOException"]}, {"nme": "exec", "acc": 8, "dsc": "(Ljava/lang/ProcessBuilder;ZLjava/io/PrintStream;ZJZ)V", "exs": ["java/io/IOException"]}, {"nme": "getProcessOutput", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/util/List;[Lja<PERSON>/lang/String;)I", "sig": "(<PERSON><PERSON><PERSON>/util/List<Ljava/lang/String;>;[Ljava/lang/String;)I", "exs": ["java/io/IOException", "java/lang/InterruptedException"]}, {"nme": "writableOutputDir", "acc": 8, "dsc": "(Ljava/nio/file/Path;)V", "exs": ["jdk/jpackage/internal/PackagerException"]}, {"nme": "getParent", "acc": 9, "dsc": "(Ljava/nio/file/Path;)Ljava/nio/file/Path;"}, {"nme": "getFileName", "acc": 9, "dsc": "(Ljava/nio/file/Path;)Ljava/nio/file/Path;"}, {"nme": "getPID", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Process;)J"}, {"nme": "lambda$getProcessOutput$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/io/BufferedReader;<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/io/BufferedReader;)V"}, {"nme": "lambda$exec$0", "acc": 4106, "dsc": "(L<PERSON><PERSON>/util/List;Ljava/io/PrintStream;Ljava/util/stream/Stream;)V"}], "flds": []}, "classes/jdk/jpackage/internal/WixTool$ToolInfo.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/WixTool$ToolInfo", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lja<PERSON>/nio/file/Path;Ljava/lang/String;)V"}], "flds": [{"acc": 16, "nme": "path", "dsc": "Ljava/nio/file/Path;"}, {"acc": 16, "nme": "version", "dsc": "Ljdk/jpackage/internal/DottedVersion;"}]}, "classes/jdk/jpackage/internal/OverridableResource$1.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/OverridableResource$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/jpackage/internal/OverridableResource;Ljava/io/OutputStream;)V", "sig": "()V"}, {"nme": "publicName", "acc": 1, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "consume", "acc": 1, "dsc": "(<PERSON><PERSON>va/io/InputStream;)V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 4112, "nme": "val$dest", "dsc": "Ljava/io/OutputStream;"}]}, "classes/jdk/jpackage/internal/PathGroup$1.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/PathGroup$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Z)V", "sig": "()V"}, {"nme": "copyFile", "acc": 1, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "createDirectory", "acc": 1, "dsc": "(Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 4112, "nme": "val$move", "dsc": "Z"}]}, "classes/jdk/jpackage/internal/Log.class": {"ver": 68, "acc": 33, "nme": "jdk/jpackage/internal/Log", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "setPrintWriter", "acc": 9, "dsc": "(Ljava/io/PrintWriter;Ljava/io/PrintWriter;)V"}, {"nme": "flush", "acc": 9, "dsc": "()V"}, {"nme": "info", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "fatalError", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "error", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setVerbose", "acc": 9, "dsc": "()V"}, {"nme": "isVerbose", "acc": 9, "dsc": "()Z"}, {"nme": "verbose", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "verbose", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "verbose", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/util/List;IJ)V", "sig": "(Ljava/util/List<Ljava/lang/String;>;Ljava/util/List<Ljava/lang/String;>;IJ)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "instance", "dsc": "<PERSON><PERSON><PERSON>/lang/InheritableThreadLocal;", "sig": "Ljava/lang/InheritableThreadLocal<Ljdk/jpackage/internal/Log$Logger;>;"}]}, "classes/jdk/jpackage/internal/DottedVersion.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/DottedVersion", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)V"}, {"nme": "greedy", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljdk/jpackage/internal/DottedVersion;"}, {"nme": "lazy", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljdk/jpackage/internal/DottedVersion;"}, {"nme": "compareComponents", "acc": 8, "dsc": "(Ljdk/jpackage/internal/DottedVersion;Ljdk/jpackage/internal/DottedVersion;)I"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getUnprocessedSuffix", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toComponentsString", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getComponents", "acc": 0, "dsc": "()[<PERSON><PERSON><PERSON>/math/BigInteger;"}, {"nme": "lambda$new$1", "acc": 4106, "dsc": "(I)[<PERSON><PERSON><PERSON>/math/BigInteger;"}, {"nme": "lambda$new$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/math/BigInteger;"}], "flds": [{"acc": 18, "nme": "components", "dsc": "[<PERSON>java/math/BigInteger;"}, {"acc": 18, "nme": "value", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "suffix", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/jpackage/internal/PathGroup$Facade.class": {"ver": 68, "acc": 1536, "nme": "jdk/jpackage/internal/PathGroup$Facade", "super": "java/lang/Object", "mthds": [{"nme": "pathGroup", "acc": 1025, "dsc": "()Ljdk/jpackage/internal/PathGroup;"}, {"nme": "paths", "acc": 1, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Ljava/nio/file/Path;>;"}, {"nme": "roots", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/nio/file/Path;>;"}, {"nme": "sizeInBytes", "acc": 1, "dsc": "()J", "exs": ["java/io/IOException"]}, {"nme": "resolveAt", "acc": 1025, "dsc": "(Ljava/nio/file/Path;)Ljava/lang/Object;", "sig": "(Ljava/nio/file/Path;)TT;"}, {"nme": "copy", "acc": 1, "dsc": "(Ljdk/jpackage/internal/PathGroup$Facade;)V", "sig": "(Ljdk/jpackage/internal/PathGroup$Facade<TT;>;)V", "exs": ["java/io/IOException"]}, {"nme": "move", "acc": 1, "dsc": "(Ljdk/jpackage/internal/PathGroup$Facade;)V", "sig": "(Ljdk/jpackage/internal/PathGroup$Facade<TT;>;)V", "exs": ["java/io/IOException"]}, {"nme": "transform", "acc": 1, "dsc": "(Ljdk/jpackage/internal/PathGroup$Facade;Ljdk/jpackage/internal/PathGroup$TransformHandler;)V", "sig": "(Ljdk/jpackage/internal/PathGroup$Facade<TT;>;Ljdk/jpackage/internal/PathGroup$TransformHandler;)V", "exs": ["java/io/IOException"]}], "flds": []}, "classes/jdk/jpackage/internal/AppImageBundler$ParamsValidator.class": {"ver": 68, "acc": 1536, "nme": "jdk/jpackage/internal/AppImageBundler$ParamsValidator", "super": "java/lang/Object", "mthds": [{"nme": "validate", "acc": 1025, "dsc": "(Ljava/util/Map;)V", "sig": "(L<PERSON><PERSON>/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)V", "exs": ["jdk/jpackage/internal/ConfigException"]}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/FunctionalInterface;"}]}, "classes/jdk/jpackage/internal/util/function/ThrowingBiFunction.class": {"ver": 68, "acc": 1537, "nme": "jdk/jpackage/internal/util/function/ThrowingBiFunction", "super": "java/lang/Object", "mthds": [{"nme": "apply", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(TT;TU;)TR;", "exs": ["java/lang/Throwable"]}, {"nme": "toBiFunction", "acc": 9, "dsc": "(Ljdk/jpackage/internal/util/function/ThrowingBiFunction;)Ljava/util/function/BiFunction;", "sig": "<T:Ljava/lang/Object;U:Ljava/lang/Object;R:Ljava/lang/Object;>(Ljdk/jpackage/internal/util/function/ThrowingBiFunction<TT;TU;TR;>;)Ljava/util/function/BiFunction<TT;TU;TR;>;"}, {"nme": "lambda$toBiFunction$0", "acc": 4106, "dsc": "(Ljdk/jpackage/internal/util/function/ThrowingBiFunction;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/FunctionalInterface;"}]}, "classes/jdk/jpackage/internal/WixUiFragmentBuilder.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/WixUiFragmentBuilder", "super": "jdk/jpackage/internal/WixFragmentBuilder", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "initFromParams", "acc": 0, "dsc": "(Ljava/util/Map;)V", "sig": "(L<PERSON><PERSON>/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)V"}, {"nme": "configureWixPipeline", "acc": 0, "dsc": "(Ljdk/jpackage/internal/WixPipeline$Builder;)V"}, {"nme": "addFilesToConfigRoot", "acc": 0, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "getFragmentWriters", "acc": 4, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Ljdk/jpackage/internal/util/XmlConsumer;>;"}, {"nme": "addUI", "acc": 2, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;)V", "exs": ["javax/xml/stream/XMLStreamException", "java/io/IOException"]}, {"nme": "getUI", "acc": 2, "dsc": "()Ljdk/jpackage/internal/WixUiFragmentBuilder$UI;"}, {"nme": "dialogSequenceForWixUI_InstallDir", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljdk/jpackage/internal/WixUiFragmentBuilder$Dialog;>;"}, {"nme": "buildPublish", "acc": 10, "dsc": "()Ljdk/jpackage/internal/WixUiFragmentBuilder$PublishBuilder;"}, {"nme": "buildPublish", "acc": 10, "dsc": "(Ljdk/jpackage/internal/WixUiFragmentBuilder$Publish;)Ljdk/jpackage/internal/WixUiFragmentBuilder$PublishBuilder;"}, {"nme": "writePublishDialogPair", "acc": 10, "dsc": "(Ljdk/jpackage/internal/WixToolset$WixToolsetType;Ljavax/xml/stream/XMLStreamWriter;Ljdk/jpackage/internal/WixUiFragmentBuilder$Publish;Ljdk/jpackage/internal/WixUiFragmentBuilder$DialogPair;)V", "exs": ["java/io/IOException", "javax/xml/stream/XMLStreamException"]}, {"nme": "lambda$static$3", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)<PERSON><PERSON><PERSON>/lang/Boolean;"}, {"nme": "lambda$static$2", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/Boolean;"}, {"nme": "lambda$static$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)<PERSON><PERSON><PERSON>/lang/Boolean;"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/Boolean;"}, {"nme": "lambda$initFromParams$0", "acc": 4106, "dsc": "(Ljava/util/Map;Ljdk/jpackage/internal/WixAppImageFragmentBuilder$ShortcutsFolder;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "withInstallDirChooserDlg", "dsc": "Z"}, {"acc": 2, "nme": "withShortcutPromptDlg", "dsc": "Z"}, {"acc": 2, "nme": "withLicenseDlg", "dsc": "Z"}, {"acc": 2, "nme": "withCustomActionsDll", "dsc": "Z"}, {"acc": 2, "nme": "customDialogs", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/jpackage/internal/WixUiFragmentBuilder$CustomDialog;>;"}, {"acc": 26, "nme": "INSTALLDIR_CHOOSER", "dsc": "Ljdk/jpackage/internal/BundlerParamInfo;", "sig": "Ljdk/jpackage/internal/BundlerParamInfo<Ljava/lang/Boolean;>;"}, {"acc": 26, "nme": "SHORTCUT_PROMPT", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/lang/Boolean;>;"}]}, "classes/jdk/jpackage/internal/CLIHelp.class": {"ver": 68, "acc": 33, "nme": "jdk/jpackage/internal/CLIHelp", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "showHelp", "acc": 9, "dsc": "(Z)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "I18N", "dsc": "Ljava/util/ResourceBundle;"}]}, "classes/jdk/jpackage/internal/Enquoter$1.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/Enquoter$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "test", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "test", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "flds": [{"acc": 18, "nme": "pattern", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}]}, "classes/jdk/jpackage/internal/WixSourceConverter$Status.class": {"ver": 68, "acc": 16432, "nme": "jdk/jpackage/internal/WixSourceConverter$Status", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/jpackage/internal/WixSourceConverter$Status;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;)Ljdk/jpackage/internal/WixSourceConverter$Status;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/jpackage/internal/WixSourceConverter$Status;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "SavedAsIs", "dsc": "Ljdk/jpackage/internal/WixSourceConverter$Status;"}, {"acc": 16409, "nme": "SavedAsIsMalfromedXml", "dsc": "Ljdk/jpackage/internal/WixSourceConverter$Status;"}, {"acc": 16409, "nme": "Transformed", "dsc": "Ljdk/jpackage/internal/WixSourceConverter$Status;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/jpackage/internal/WixSourceConverter$Status;"}]}, "classes/jdk/jpackage/internal/AppImageBundler.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/AppImageBundler", "super": "jdk/jpackage/internal/AbstractBundler", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "getName", "acc": 17, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getID", "acc": 17, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getBundleType", "acc": 17, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "validate", "acc": 17, "dsc": "(Ljava/util/Map;)Z", "sig": "(<PERSON><PERSON><PERSON>/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)Z", "exs": ["jdk/jpackage/internal/ConfigException"]}, {"nme": "execute", "acc": 17, "dsc": "(Ljava/util/Map;Ljava/nio/file/Path;)Ljava/nio/file/Path;", "sig": "(Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;Ljava/nio/file/Path;)Ljava/nio/file/Path;", "exs": ["jdk/jpackage/internal/PackagerException"]}, {"nme": "supported", "acc": 17, "dsc": "(Z)Z"}, {"nme": "isDefault", "acc": 17, "dsc": "()Z"}, {"nme": "setDependentTask", "acc": 16, "dsc": "(Z)Ljdk/jpackage/internal/AppImageBundler;"}, {"nme": "isDependentTask", "acc": 16, "dsc": "()Z"}, {"nme": "setAppImageSupplier", "acc": 16, "dsc": "(L<PERSON><PERSON>/util/function/Function;)Ljdk/jpackage/internal/AppImageBundler;", "sig": "(Ljava/util/function/Function<Ljava/nio/file/Path;Ljdk/jpackage/internal/AbstractAppImageBuilder;>;)Ljdk/jpackage/internal/AppImageBundler;"}, {"nme": "setParamsValidator", "acc": 16, "dsc": "(Ljdk/jpackage/internal/AppImageBundler$ParamsValidator;)Ljdk/jpackage/internal/AppImageBundler;"}, {"nme": "createRoot", "acc": 2, "dsc": "(Ljava/util/Map;Ljava/nio/file/Path;)Ljava/nio/file/Path;", "sig": "(Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;Ljava/nio/file/Path;)Ljava/nio/file/Path;", "exs": ["jdk/jpackage/internal/PackagerException", "java/io/IOException"]}, {"nme": "createAppBundle", "acc": 2, "dsc": "(Ljava/util/Map;Ljava/nio/file/Path;)Ljava/nio/file/Path;", "sig": "(Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;Ljava/nio/file/Path;)Ljava/nio/file/Path;", "exs": ["jdk/jpackage/internal/PackagerException", "java/io/IOException", "jdk/jpackage/internal/ConfigException"]}], "flds": [{"acc": 2, "nme": "dependentTask", "dsc": "Z"}, {"acc": 2, "nme": "paramsValidator", "dsc": "Ljdk/jpackage/internal/AppImageBundler$ParamsValidator;"}, {"acc": 2, "nme": "appImageSupplier", "dsc": "Ljava/util/function/Function;", "sig": "Ljava/util/function/Function<Ljava/nio/file/Path;Ljdk/jpackage/internal/AbstractAppImageBuilder;>;"}]}, "classes/jdk/jpackage/internal/WixUiFragmentBuilder$PublishBuilder.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/WixUiFragmentBuilder$PublishBuilder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "<init>", "acc": 0, "dsc": "(Ljdk/jpackage/internal/WixUiFragmentBuilder$Publish;)V"}, {"nme": "control", "acc": 1, "dsc": "(L<PERSON><PERSON>/lang/String;)Ljdk/jpackage/internal/WixUiFragmentBuilder$PublishBuilder;"}, {"nme": "next", "acc": 1, "dsc": "()Ljdk/jpackage/internal/WixUiFragmentBuilder$PublishBuilder;"}, {"nme": "back", "acc": 1, "dsc": "()Ljdk/jpackage/internal/WixUiFragmentBuilder$PublishBuilder;"}, {"nme": "condition", "acc": 1, "dsc": "(L<PERSON><PERSON>/lang/String;)Ljdk/jpackage/internal/WixUiFragmentBuilder$PublishBuilder;"}, {"nme": "order", "acc": 1, "dsc": "(I)Ljdk/jpackage/internal/WixUiFragmentBuilder$PublishBuilder;"}, {"nme": "create", "acc": 0, "dsc": "()Ljdk/jpackage/internal/WixUiFragmentBuilder$Publish;"}], "flds": [{"acc": 2, "nme": "control", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "condition", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "order", "dsc": "I"}]}, "classes/jdk/jpackage/internal/WixVariables.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/WixVariables", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "defineWixVariable", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setWixVariable", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getV<PERSON>ues", "acc": 0, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}], "flds": [{"acc": 18, "nme": "values", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}]}, "classes/jdk/jpackage/internal/WixAppImageFragmentBuilder$Component.class": {"ver": 68, "acc": 16432, "nme": "jdk/jpackage/internal/WixAppImageFragmentBuilder$Component", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/jpackage/internal/WixAppImageFragmentBuilder$Component;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;)Ljdk/jpackage/internal/WixAppImageFragmentBuilder$Component;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "<init>", "acc": 2, "dsc": "(Ljava/lang/String;ILjdk/jpackage/internal/WixAppImageFragmentBuilder$Component$Config;)V", "sig": "(Ljdk/jpackage/internal/WixAppImageFragmentBuilder$Component$Config;)V"}, {"nme": "guidOf", "acc": 0, "dsc": "(Ljava/nio/file/Path;)Ljava/util/UUID;"}, {"nme": "idOf", "acc": 0, "dsc": "(Lja<PERSON>/nio/file/Path;)Ljava/lang/String;"}, {"nme": "isRegistry<PERSON>eyPath", "acc": 0, "dsc": "()Z"}, {"nme": "isFile", "acc": 0, "dsc": "()Z"}, {"nme": "startElement", "acc": 8, "dsc": "(Ljdk/jpackage/internal/WixToolset$WixToolsetType;Ljavax/xml/stream/XMLStreamWriter;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["javax/xml/stream/XMLStreamException", "java/io/IOException"]}, {"nme": "cfg", "acc": 10, "dsc": "()Ljdk/jpackage/internal/WixAppImageFragmentBuilder$Component$Config;"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/jpackage/internal/WixAppImageFragmentBuilder$Component;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "File", "dsc": "Ljdk/jpackage/internal/WixAppImageFragmentBuilder$Component;"}, {"acc": 16409, "nme": "Shortcut", "dsc": "Ljdk/jpackage/internal/WixAppImageFragmentBuilder$Component;"}, {"acc": 16409, "nme": "ProgId", "dsc": "Ljdk/jpackage/internal/WixAppImageFragmentBuilder$Component;"}, {"acc": 16409, "nme": "CreateFolder", "dsc": "Ljdk/jpackage/internal/WixAppImageFragmentBuilder$Component;"}, {"acc": 16409, "nme": "RemoveFolder", "dsc": "Ljdk/jpackage/internal/WixAppImageFragmentBuilder$Component;"}, {"acc": 18, "nme": "cfg", "dsc": "Ljdk/jpackage/internal/WixAppImageFragmentBuilder$Component$Config;"}, {"acc": 18, "nme": "id", "dsc": "Ljdk/jpackage/internal/WixAppImageFragmentBuilder$Id;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/jpackage/internal/WixAppImageFragmentBuilder$Component;"}]}, "classes/jdk/jpackage/internal/ToolValidator.class": {"ver": 68, "acc": 49, "nme": "jdk/jpackage/internal/ToolValidator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(Ljava/nio/file/Path;)V"}, {"nme": "setCommandLine", "acc": 128, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)Ljdk/jpackage/internal/ToolValidator;"}, {"nme": "setMinimalVersion", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Comparable;)Ljdk/jpackage/internal/ToolValidator;", "sig": "(Lja<PERSON>/lang/Comparable<Ljava/lang/String;>;)Ljdk/jpackage/internal/ToolValidator;"}, {"nme": "setMinimalVersion", "acc": 0, "dsc": "(Ljdk/jpackage/internal/DottedVersion;)Ljdk/jpackage/internal/ToolValidator;"}, {"nme": "setVersionParser", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Function;)Ljdk/jpackage/internal/ToolValidator;", "sig": "(Ljava/util/function/Function<Ljava/util/stream/Stream<Ljava/lang/String;>;Ljava/lang/String;>;)Ljdk/jpackage/internal/ToolValidator;"}, {"nme": "setToolNotFoundErrorHandler", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/function/BiFunction;)Ljdk/jpackage/internal/ToolValidator;", "sig": "(Lja<PERSON>/util/function/BiFunction<Ljava/lang/String;Ljava/io/IOException;Ljdk/jpackage/internal/ConfigException;>;)Ljdk/jpackage/internal/ToolValidator;"}, {"nme": "setToolOldVersionErrorHandler", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/function/BiFunction;)Ljdk/jpackage/internal/ToolValidator;", "sig": "(Lja<PERSON>/util/function/BiFunction<Ljava/lang/String;Ljava/lang/String;Ljdk/jpackage/internal/ConfigException;>;)Ljdk/jpackage/internal/ToolValidator;"}, {"nme": "validate", "acc": 0, "dsc": "()Ljdk/jpackage/internal/ConfigException;"}, {"nme": "lambda$validate$0", "acc": 4098, "dsc": "([Lja<PERSON>/lang/String;Ljava/util/concurrent/atomic/AtomicBoolean;Ljava/util/stream/Stream;)V"}, {"nme": "lambda$setMinimalVersion$0", "acc": 4106, "dsc": "(Ljdk/jpackage/internal/DottedVersion;Lja<PERSON>/lang/String;)I"}], "flds": [{"acc": 18, "nme": "toolPath", "dsc": "Ljava/nio/file/Path;"}, {"acc": 2, "nme": "args", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 2, "nme": "minimalVersion", "dsc": "<PERSON><PERSON><PERSON>/lang/Comparable;", "sig": "Ljava/lang/Comparable<Ljava/lang/String;>;"}, {"acc": 2, "nme": "versionParser", "dsc": "Ljava/util/function/Function;", "sig": "Ljava/util/function/Function<Ljava/util/stream/Stream<Ljava/lang/String;>;Ljava/lang/String;>;"}, {"acc": 2, "nme": "toolNotFoundErrorHandler", "dsc": "Lja<PERSON>/util/function/BiFunction;", "sig": "Ljava/util/function/BiFunction<Ljava/lang/String;Ljava/io/IOException;Ljdk/jpackage/internal/ConfigException;>;"}, {"acc": 2, "nme": "toolOldVersionErrorHandler", "dsc": "Lja<PERSON>/util/function/BiFunction;", "sig": "Ljava/util/function/BiFunction<Ljava/lang/String;Ljava/lang/String;Ljdk/jpackage/internal/ConfigException;>;"}]}, "classes/jdk/jpackage/internal/WindowsDefender.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/WindowsDefender", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "isThereAPotentialWindowsDefenderIssue", "acc": 24, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isDirectoryInExclusionPath", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "getUserTempDirectory", "acc": 24, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "classes/jdk/jpackage/internal/WindowsRegistry.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/WindowsRegistry", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "readDisableRealtimeMonitoring", "acc": 24, "dsc": "()Z"}, {"nme": "readExclusionsPaths", "acc": 24, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "readDwordValue", "acc": 266, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;I)I"}, {"nme": "openRegistryKey", "acc": 266, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)J"}, {"nme": "enumRegistryValue", "acc": 266, "dsc": "(JI)Ljava/lang/String;"}, {"nme": "closeRegistry<PERSON>ey", "acc": 266, "dsc": "(J)V"}, {"nme": "comparePaths", "acc": 265, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "HKEY_LOCAL_MACHINE", "dsc": "I", "val": 1}]}, "classes/jdk/jpackage/internal/Arguments$OptionCategories.class": {"ver": 68, "acc": 16432, "nme": "jdk/jpackage/internal/Arguments$OptionCategories", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/jpackage/internal/Arguments$OptionCategories;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljdk/jpackage/internal/Arguments$OptionCategories;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/jpackage/internal/Arguments$OptionCategories;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "MODULAR", "dsc": "Ljdk/jpackage/internal/Arguments$OptionCategories;"}, {"acc": 16409, "nme": "PROPERTY", "dsc": "Ljdk/jpackage/internal/Arguments$OptionCategories;"}, {"acc": 16409, "nme": "PLATFORM_MAC", "dsc": "Ljdk/jpackage/internal/Arguments$OptionCategories;"}, {"acc": 16409, "nme": "PLATFORM_WIN", "dsc": "Ljdk/jpackage/internal/Arguments$OptionCategories;"}, {"acc": 16409, "nme": "PLATFORM_LINUX", "dsc": "Ljdk/jpackage/internal/Arguments$OptionCategories;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/jpackage/internal/Arguments$OptionCategories;"}]}, "classes/jdk/jpackage/internal/AppImageFile$LauncherInfo.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/AppImageFile$LauncherInfo", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljava/util/Map;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/util/Map;)V", "sig": "(Lja<PERSON>/lang/String;Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(Lorg/w3c/dom/Node;)V"}, {"nme": "getAttribute", "acc": 2, "dsc": "(Lorg/w3c/dom/Node;Lja<PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isShortcut", "acc": 1, "dsc": "()Z"}, {"nme": "isMenu", "acc": 1, "dsc": "()Z"}, {"nme": "isService", "acc": 1, "dsc": "()Z"}], "flds": [{"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "shortcut", "dsc": "Z"}, {"acc": 18, "nme": "menu", "dsc": "Z"}, {"acc": 18, "nme": "service", "dsc": "Z"}]}, "classes/jdk/jpackage/internal/WinAppBundler.class": {"ver": 68, "acc": 33, "nme": "jdk/jpackage/internal/WinAppBundler", "super": "jdk/jpackage/internal/AppImageBundler", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "cleanup", "acc": 4161, "dsc": "(Ljava/util/Map;)V"}, {"nme": "toString", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "classes/jdk/jpackage/internal/resources/ResourceLocator.class": {"ver": 68, "acc": 33, "nme": "jdk/jpackage/internal/resources/ResourceLocator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": []}, "classes/jdk/jpackage/internal/Bundler.class": {"ver": 68, "acc": 1537, "nme": "jdk/jpackage/internal/Bundler", "super": "java/lang/Object", "mthds": [{"nme": "getName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getID", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getBundleType", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "validate", "acc": 1025, "dsc": "(Ljava/util/Map;)Z", "sig": "(<PERSON><PERSON><PERSON>/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)Z", "exs": ["jdk/jpackage/internal/ConfigException"]}, {"nme": "execute", "acc": 1025, "dsc": "(Ljava/util/Map;Ljava/nio/file/Path;)Ljava/nio/file/Path;", "sig": "(Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;Ljava/nio/file/Path;)Ljava/nio/file/Path;", "exs": ["jdk/jpackage/internal/PackagerException"]}, {"nme": "cleanup", "acc": 1025, "dsc": "(Ljava/util/Map;)V", "sig": "(L<PERSON><PERSON>/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)V"}, {"nme": "supported", "acc": 1025, "dsc": "(Z)Z"}, {"nme": "isDefault", "acc": 1025, "dsc": "()Z"}], "flds": []}, "classes/jdk/jpackage/internal/PackagerException.class": {"ver": 68, "acc": 33, "nme": "jdk/jpackage/internal/PackagerException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 129, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 129, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}, {"acc": 26, "nme": "bundle", "dsc": "Ljava/util/ResourceBundle;"}]}, "classes/jdk/jpackage/internal/AbstractAppImageBuilder.class": {"ver": 68, "acc": 1057, "nme": "jdk/jpackage/internal/AbstractAppImageBuilder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/nio/file/Path;)V"}, {"nme": "getResourceAsStream", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/io/InputStream;"}, {"nme": "prepareApplicationFiles", "acc": 1025, "dsc": "(Ljava/util/Map;)V", "sig": "(L<PERSON><PERSON>/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)V", "exs": ["java/io/IOException"]}, {"nme": "writeCfgFile", "acc": 4, "dsc": "(Ljava/util/Map;)V", "sig": "(L<PERSON><PERSON>/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)V", "exs": ["java/io/IOException"]}, {"nme": "getAppLayout", "acc": 0, "dsc": "()Ljdk/jpackage/internal/ApplicationLayout;"}, {"nme": "copyApplication", "acc": 4, "dsc": "(Ljava/util/Map;)V", "sig": "(L<PERSON><PERSON>/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)V", "exs": ["java/io/IOException"]}, {"nme": "createIconResource", "acc": 9, "dsc": "(Ljava/lang/String;Ljdk/jpackage/internal/BundlerParamInfo;Ljava/util/Map;Ljava/util/Map;)Ljdk/jpackage/internal/OverridableResource;", "sig": "(Ljava/lang/String;Ljdk/jpackage/internal/BundlerParamInfo<Ljava/nio/file/Path;>;Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)Ljdk/jpackage/internal/OverridableResource;", "exs": ["java/io/IOException"]}, {"nme": "getLauncherIconType", "acc": 10, "dsc": "(Ljava/util/Map;)Ljdk/jpackage/internal/AbstractAppImageBuilder$IconType;", "sig": "(Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)Ljdk/jpackage/internal/AbstractAppImageBuilder$IconType;"}], "flds": [{"acc": 18, "nme": "root", "dsc": "Ljava/nio/file/Path;"}, {"acc": 20, "nme": "appLayout", "dsc": "Ljdk/jpackage/internal/ApplicationLayout;"}]}, "classes/jdk/jpackage/internal/WixUiFragmentBuilder$CustomDialog.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/WixUiFragmentBuilder$CustomDialog", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/jpackage/internal/WixUiFragmentBuilder;Ljava/util/Map;Ljava/lang/String;Ljava/lang/String;)V", "sig": "(<PERSON><PERSON><PERSON>/util/Map<Ljava/lang/String;-L<PERSON><PERSON>/lang/Object;>;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "addToWixPipeline", "acc": 0, "dsc": "(Ljdk/jpackage/internal/WixPipeline$Builder;)V"}], "flds": [{"acc": 18, "nme": "wixVariables", "dsc": "Ljdk/jpackage/internal/WixVariables;"}, {"acc": 18, "nme": "wxsFileName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4112, "nme": "this$0", "dsc": "Ljdk/jpackage/internal/WixUiFragmentBuilder;"}]}, "classes/jdk/jpackage/internal/WixTool.class": {"ver": 68, "acc": 16433, "nme": "jdk/jpackage/internal/WixTool", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/jpackage/internal/WixTool;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljdk/jpackage/internal/WixTool;"}, {"nme": "<init>", "acc": 2, "dsc": "(Lja<PERSON>/lang/String;ILjava/lang/String;Ljdk/jpackage/internal/DottedVersion;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/String;Ljdk/jpackage/internal/DottedVersion;)V"}, {"nme": "createToolset", "acc": 8, "dsc": "()Ljdk/jpackage/internal/WixToolset;", "exs": ["jdk/jpackage/internal/ConfigException"]}, {"nme": "getSystemDir", "acc": 10, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;)Ljava/nio/file/Path;"}, {"nme": "getEnvVariableAsPath", "acc": 10, "dsc": "(Lja<PERSON>/lang/String;)Ljava/nio/file/Path;"}, {"nme": "findWixInstallDirs", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/nio/file/Path;>;"}, {"nme": "findWixCurrentInstallDirs", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/nio/file/Path;>;"}, {"nme": "findWix3InstallDirs", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/nio/file/Path;>;"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/jpackage/internal/WixTool;"}, {"nme": "lambda$findWix3InstallDirs$2", "acc": 4106, "dsc": "(Ljava/nio/file/Path;)Ljava/nio/file/Path;"}, {"nme": "lambda$findWix3InstallDirs$1", "acc": 4106, "dsc": "(Ljava/nio/file/PathMatcher;Ljava/nio/file/Path;)Z"}, {"nme": "lambda$findWix3InstallDirs$0", "acc": 4106, "dsc": "(Ljava/nio/file/Path;)Ljava/util/List;"}, {"nme": "lambda$findWixCurrentInstallDirs$2", "acc": 4106, "dsc": "(Ljava/nio/file/Path;)Z"}, {"nme": "lambda$findWixCurrentInstallDirs$1", "acc": 4106, "dsc": "(Ljava/nio/file/Path;)Ljava/nio/file/Path;"}, {"nme": "lambda$findWixCurrentInstallDirs$0", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;)Ljava/nio/file/Path;"}, {"nme": "lambda$getSystemDir$0", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;)Ljava/nio/file/Path;"}, {"nme": "lambda$getSystemDir$1", "acc": 4106, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "lambda$createToolset$10", "acc": 4106, "dsc": "(Ljdk/jpackage/internal/WixTool$ToolLookupResult;)Ljdk/jpackage/internal/ConfigException;"}, {"nme": "lambda$createToolset$8", "acc": 4106, "dsc": "(Ljava/nio/file/Path;)Ljava/util/stream/Stream;"}, {"nme": "lambda$createToolset$9", "acc": 4106, "dsc": "(Ljava/nio/file/Path;Ljdk/jpackage/internal/WixTool;)Ljdk/jpackage/internal/WixTool$ToolLookupResult;"}, {"nme": "lambda$createToolset$7", "acc": 4106, "dsc": "(Ljdk/jpackage/internal/WixTool;)Ljdk/jpackage/internal/WixTool$ToolLookupResult;"}, {"nme": "lambda$createToolset$5", "acc": 4106, "dsc": "(L<PERSON><PERSON>/util/function/Function;Ljava/util/List;)Ljava/util/Optional;"}, {"nme": "lambda$createToolset$6", "acc": 4106, "dsc": "(Ljava/util/Map;Ljdk/jpackage/internal/WixToolset$WixToolsetType;)Ljdk/jpackage/internal/WixToolset;"}, {"nme": "lambda$createToolset$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Ljava/util/Map;"}, {"nme": "lambda$createToolset$3", "acc": 4106, "dsc": "(Ljdk/jpackage/internal/WixTool$ToolInfo;Ljdk/jpackage/internal/WixTool$ToolInfo;)Ljdk/jpackage/internal/WixTool$ToolInfo;"}, {"nme": "lambda$createToolset$4", "acc": 4106, "dsc": "(Ljdk/jpackage/internal/WixTool$ToolInfo;)Ljava/lang/String;"}, {"nme": "lambda$createToolset$2", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Z"}, {"nme": "lambda$createToolset$1", "acc": 4106, "dsc": "(Ljdk/jpackage/internal/WixTool$ToolLookupResult;)Ljava/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "Candle3", "dsc": "Ljdk/jpackage/internal/WixTool;"}, {"acc": 16409, "nme": "Light3", "dsc": "Ljdk/jpackage/internal/WixTool;"}, {"acc": 16409, "nme": "Wix4", "dsc": "Ljdk/jpackage/internal/WixTool;"}, {"acc": 18, "nme": "toolFileName", "dsc": "Ljava/nio/file/Path;"}, {"acc": 18, "nme": "minimalVersion", "dsc": "Ljdk/jpackage/internal/DottedVersion;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/jpackage/internal/WixTool;"}]}, "classes/jdk/jpackage/internal/Arguments$CLIOptions.class": {"ver": 68, "acc": 16433, "nme": "jdk/jpackage/internal/Arguments$CLIOptions", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"nme": "<init>", "acc": 2, "dsc": "(Ljava/lang/String;ILjava/lang/String;Ljdk/jpackage/internal/Arguments$OptionCategories;)V", "sig": "(L<PERSON><PERSON>/lang/String;Ljdk/jpackage/internal/Arguments$OptionCategories;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(L<PERSON><PERSON>/lang/String;<PERSON>java/lang/String;Ljava/lang/String;Ljdk/jpackage/internal/Arguments$OptionCategories;)V", "sig": "(Lja<PERSON>/lang/String;Ljava/lang/String;Ljdk/jpackage/internal/Arguments$OptionCategories;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(Ljava/lang/String;ILjava/lang/String;Ljdk/jpackage/internal/Arguments$OptionCategories;Ljava/lang/Runnable;)V", "sig": "(Lja<PERSON>/lang/String;Ljdk/jpackage/internal/Arguments$OptionCategories;Ljava/lang/Runnable;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>java/lang/String;Ljava/lang/String;Ljdk/jpackage/internal/Arguments$OptionCategories;Ljava/lang/Runnable;)V", "sig": "(Lja<PERSON>/lang/String;Ljava/lang/String;Ljdk/jpackage/internal/Arguments$OptionCategories;Ljava/lang/Runnable;)V"}, {"nme": "context", "acc": 9, "dsc": "()Ljdk/jpackage/internal/Arguments;"}, {"nme": "getId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getIdWithPrefix", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getShortIdWithPrefix", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "execute", "acc": 0, "dsc": "()V"}, {"nme": "defaultAction", "acc": 2, "dsc": "()V"}, {"nme": "setOptionValue", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "popArg", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getArg", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "nextArg", "acc": 10, "dsc": "()V"}, {"nme": "hasNextArg", "acc": 10, "dsc": "()Z"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"nme": "lambda$static$28", "acc": 4106, "dsc": "()V"}, {"nme": "lambda$static$27", "acc": 4106, "dsc": "()V"}, {"nme": "lambda$static$26", "acc": 4106, "dsc": "()V"}, {"nme": "lambda$static$25", "acc": 4106, "dsc": "()V"}, {"nme": "lambda$static$24", "acc": 4106, "dsc": "()V"}, {"nme": "lambda$static$23", "acc": 4106, "dsc": "()V"}, {"nme": "lambda$static$22", "acc": 4106, "dsc": "()V"}, {"nme": "lambda$static$21", "acc": 4106, "dsc": "()V"}, {"nme": "lambda$static$20", "acc": 4106, "dsc": "()V"}, {"nme": "lambda$static$19", "acc": 4106, "dsc": "()V"}, {"nme": "lambda$static$18", "acc": 4106, "dsc": "()V"}, {"nme": "lambda$static$17", "acc": 4106, "dsc": "()V"}, {"nme": "lambda$static$16", "acc": 4106, "dsc": "()V"}, {"nme": "lambda$static$15", "acc": 4106, "dsc": "()V"}, {"nme": "lambda$static$14", "acc": 4106, "dsc": "()V"}, {"nme": "lambda$static$12", "acc": 4106, "dsc": "()V"}, {"nme": "lambda$static$13", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "lambda$static$10", "acc": 4106, "dsc": "()V"}, {"nme": "lambda$static$11", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "lambda$static$9", "acc": 4106, "dsc": "()V"}, {"nme": "lambda$static$8", "acc": 4106, "dsc": "()V"}, {"nme": "lambda$static$6", "acc": 4106, "dsc": "()V"}, {"nme": "lambda$static$7", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "lambda$static$5", "acc": 4106, "dsc": "()V"}, {"nme": "lambda$static$4", "acc": 4106, "dsc": "()V"}, {"nme": "lambda$static$3", "acc": 4106, "dsc": "()V"}, {"nme": "lambda$static$2", "acc": 4106, "dsc": "()V"}, {"nme": "lambda$static$1", "acc": 4106, "dsc": "()V"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "PACKAGE_TYPE", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "INPUT", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "OUTPUT", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "DESCRIPTION", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "VENDOR", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "APPCLASS", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "NAME", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "VERBOSE", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "RESOURCE_DIR", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "DMG_CONTENT", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "ARGUMENTS", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "JLINK_OPTIONS", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "ICON", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "COPYRIGHT", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "LICENSE_FILE", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "VERSION", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "RELEASE", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "ABOUT_URL", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "JAVA_OPTIONS", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "APP_CONTENT", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "FILE_ASSOCIATIONS", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "ADD_LAUNCHER", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "TEMP_ROOT", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "INSTALL_DIR", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "PREDEFINED_APP_IMAGE", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "PREDEFINED_RUNTIME_IMAGE", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "MAIN_JAR", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "MODULE", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "ADD_MODULES", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "MODULE_PATH", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "LAUNCHER_AS_SERVICE", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "MAC_SIGN", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "MAC_APP_STORE", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "MAC_CATEGORY", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "MAC_BUNDLE_NAME", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "MAC_BUNDLE_IDENTIFIER", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "MAC_BUNDLE_SIGNING_PREFIX", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "MAC_SIGNING_KEY_NAME", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "MAC_APP_IMAGE_SIGN_IDENTITY", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "MAC_INSTALLER_SIGN_IDENTITY", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "MAC_SIGNING_KEYCHAIN", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "MAC_ENTITLEMENTS", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "WIN_HELP_URL", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "WIN_UPDATE_URL", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "WIN_MENU_HINT", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "WIN_MENU_GROUP", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "WIN_SHORTCUT_HINT", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "WIN_SHORTCUT_PROMPT", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "WIN_PER_USER_INSTALLATION", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "WIN_DIR_CHOOSER", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "WIN_UPGRADE_UUID", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "WIN_CONSOLE_HINT", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "LINUX_BUNDLE_NAME", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "LINUX_DEB_MAINTAINER", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "LINUX_CATEGORY", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "LINUX_RPM_LICENSE_TYPE", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "LINUX_PACKAGE_DEPENDENCIES", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "LINUX_SHORTCUT_HINT", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 16409, "nme": "LINUX_MENU_GROUP", "dsc": "Ljdk/jpackage/internal/Arguments$CLIOptions;"}, {"acc": 18, "nme": "id", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "shortId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "category", "dsc": "Ljdk/jpackage/internal/Arguments$OptionCategories;"}, {"acc": 18, "nme": "action", "dsc": "<PERSON><PERSON><PERSON>/lang/Runnable;"}, {"acc": 10, "nme": "argContext", "dsc": "Ljdk/jpackage/internal/Arguments;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/jpackage/internal/Arguments$CLIOptions;"}]}, "classes/jdk/jpackage/internal/WixAppImageFragmentBuilder$Id.class": {"ver": 68, "acc": 16432, "nme": "jdk/jpackage/internal/WixAppImageFragmentBuilder$Id", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/jpackage/internal/WixAppImageFragmentBuilder$Id;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;)Ljdk/jpackage/internal/WixAppImageFragmentBuilder$Id;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "of", "acc": 0, "dsc": "(Lja<PERSON>/nio/file/Path;)Ljava/lang/String;"}, {"nme": "of", "acc": 10, "dsc": "(L<PERSON><PERSON>/nio/file/Path;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "of", "acc": 8, "dsc": "(Lja<PERSON>/nio/file/Path;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/jpackage/internal/WixAppImageFragmentBuilder$Id;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "File", "dsc": "Ljdk/jpackage/internal/WixAppImageFragmentBuilder$Id;"}, {"acc": 16409, "nme": "Folder", "dsc": "Ljdk/jpackage/internal/WixAppImageFragmentBuilder$Id;"}, {"acc": 16409, "nme": "Shortcut", "dsc": "Ljdk/jpackage/internal/WixAppImageFragmentBuilder$Id;"}, {"acc": 16409, "nme": "ProgId", "dsc": "Ljdk/jpackage/internal/WixAppImageFragmentBuilder$Id;"}, {"acc": 16409, "nme": "Icon", "dsc": "Ljdk/jpackage/internal/WixAppImageFragmentBuilder$Id;"}, {"acc": 16409, "nme": "CreateFolder", "dsc": "Ljdk/jpackage/internal/WixAppImageFragmentBuilder$Id;"}, {"acc": 16409, "nme": "RemoveFolder", "dsc": "Ljdk/jpackage/internal/WixAppImageFragmentBuilder$Id;"}, {"acc": 18, "nme": "prefix", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/jpackage/internal/WixAppImageFragmentBuilder$Id;"}]}, "classes/jdk/jpackage/internal/ValidOptions.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/ValidOptions", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "checkIfSupported", "acc": 8, "dsc": "(Ljdk/jpackage/internal/Arguments$CLIOptions;)Z"}, {"nme": "checkIfImageSupported", "acc": 8, "dsc": "(Ljdk/jpackage/internal/Arguments$CLIOptions;)Z"}, {"nme": "checkIfInstallerSupported", "acc": 8, "dsc": "(Ljdk/jpackage/internal/Arguments$CLIOptions;)Z"}, {"nme": "checkIfSigningSupported", "acc": 8, "dsc": "(Ljdk/jpackage/internal/Arguments$CLIOptions;)Z"}, {"nme": "put", "acc": 10, "dsc": "(Ljava/lang/String;Ljdk/jpackage/internal/ValidOptions$USE;)Ljava/util/EnumSet;", "sig": "(Ljava/lang/String;Ljdk/jpackage/internal/ValidOptions$USE;)Ljava/util/EnumSet<Ljdk/jpackage/internal/ValidOptions$USE;>;"}, {"nme": "put", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/EnumSet;)<PERSON>ja<PERSON>/util/EnumSet;", "sig": "(Ljava/lang/String;Ljava/util/EnumSet<Ljdk/jpackage/internal/ValidOptions$USE;>;)Ljava/util/EnumSet<Ljdk/jpackage/internal/ValidOptions$USE;>;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "options", "dsc": "<PERSON><PERSON><PERSON>/util/HashMap;", "sig": "Ljava/util/HashMap<Ljava/lang/String;Ljava/util/EnumSet<Ljdk/jpackage/internal/ValidOptions$USE;>;>;"}]}, "classes/jdk/jpackage/internal/DeployParams.class": {"ver": 68, "acc": 33, "nme": "jdk/jpackage/internal/DeployParams", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "expandFileset", "acc": 0, "dsc": "(Ljava/nio/file/Path;)Ljava/util/List;", "sig": "(Ljava/nio/file/Path;)Ljava/util/List<Ljava/nio/file/Path;>;", "exs": ["java/io/IOException"]}, {"nme": "validateName", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)V", "exs": ["jdk/jpackage/internal/PackagerException"]}, {"nme": "validate", "acc": 1, "dsc": "()V", "exs": ["jdk/jpackage/internal/PackagerException"]}, {"nme": "setTargetFormat", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getTargetFormat", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isTargetAppImage", "acc": 0, "dsc": "()Z"}, {"nme": "addBundleArgument", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "getBundleParams", "acc": 0, "dsc": "()Ljdk/jpackage/internal/BundleParams;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$validate$0", "acc": 4106, "dsc": "(I)[Ljava/nio/file/Path;"}, {"nme": "lambda$expandFileset$0", "acc": 4098, "dsc": "(Lja<PERSON>/util/List;Ljava/nio/file/Path;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 0, "nme": "targetFormat", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "bundlerArguments", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;"}, {"acc": 26, "nme": "multi_args", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}]}, "classes/jdk/jpackage/internal/ApplicationLayout.class": {"ver": 68, "acc": 49, "nme": "jdk/jpackage/internal/ApplicationLayout", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/util/Map;)V", "sig": "(Ljava/util/Map<Ljava/lang/Object;Ljava/nio/file/Path;>;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(Ljdk/jpackage/internal/PathGroup;)V"}, {"nme": "pathGroup", "acc": 1, "dsc": "()Ljdk/jpackage/internal/PathGroup;"}, {"nme": "resolveAt", "acc": 1, "dsc": "(Ljava/nio/file/Path;)Ljdk/jpackage/internal/ApplicationLayout;"}, {"nme": "launchersDirectory", "acc": 1, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "appDirectory", "acc": 1, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "runtimeDirectory", "acc": 1, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "runtimeHomeDirectory", "acc": 1, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "appModsDirectory", "acc": 1, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "destktopIntegrationDirectory", "acc": 1, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "contentDirectory", "acc": 1, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "linuxAppImage", "acc": 8, "dsc": "()Ljdk/jpackage/internal/ApplicationLayout;"}, {"nme": "windowsAppImage", "acc": 8, "dsc": "()Ljdk/jpackage/internal/ApplicationLayout;"}, {"nme": "macAppImage", "acc": 8, "dsc": "()Ljdk/jpackage/internal/ApplicationLayout;"}, {"nme": "platformAppImage", "acc": 9, "dsc": "()Ljdk/jpackage/internal/ApplicationLayout;"}, {"nme": "javaRuntime", "acc": 9, "dsc": "()Ljdk/jpackage/internal/ApplicationLayout;"}, {"nme": "linuxUsrTreePackageImage", "acc": 9, "dsc": "(Ljava/nio/file/Path;Ljava/lang/String;)Ljdk/jpackage/internal/ApplicationLayout;"}, {"nme": "resolveAt", "acc": 4161, "dsc": "(Ljava/nio/file/Path;)Ljava/lang/Object;"}], "flds": [{"acc": 18, "nme": "data", "dsc": "Ljdk/jpackage/internal/PathGroup;"}]}, "classes/jdk/jpackage/main/Main.class": {"ver": 68, "acc": 33, "nme": "jdk/jpackage/main/Main", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "main", "acc": 137, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/lang/Exception"]}, {"nme": "execute", "acc": 129, "dsc": "(Ljava/io/PrintWriter;Ljava/io/PrintWriter;[<PERSON>ja<PERSON>/lang/String;)I"}, {"nme": "hasHelp", "acc": 2, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "hasVersion", "acc": 2, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "I18N", "dsc": "Ljava/util/ResourceBundle;"}]}, "classes/jdk/jpackage/internal/BundlerParamInfo.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/BundlerParamInfo", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "getID", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getValueType", "acc": 0, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<TT;>;"}, {"nme": "getIsDefaultValue", "acc": 0, "dsc": "(Ljava/util/Map;)Z", "sig": "(<PERSON><PERSON><PERSON>/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)Z"}, {"nme": "getDefaultValueFunction", "acc": 0, "dsc": "()Ljava/util/function/Function;", "sig": "()Ljava/util/function/Function<Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;TT;>;"}, {"nme": "getStringConverter", "acc": 0, "dsc": "()Ljava/util/function/BiFunction;", "sig": "()Ljava/util/function/BiFunction<Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;TT;>;"}, {"nme": "fetchFrom", "acc": 16, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/Object;", "sig": "(L<PERSON><PERSON>/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)TT;"}, {"nme": "fetchFrom", "acc": 16, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;Z)Ljava/lang/Object;", "sig": "(L<PERSON><PERSON>/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;Z)TT;"}], "flds": [{"acc": 0, "nme": "id", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "valueType", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<TT;>;"}, {"acc": 0, "nme": "defaultValueFunction", "dsc": "Ljava/util/function/Function;", "sig": "Ljava/util/function/Function<Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;TT;>;"}, {"acc": 0, "nme": "stringConverter", "dsc": "Lja<PERSON>/util/function/BiFunction;", "sig": "Ljava/util/function/BiFunction<Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;TT;>;"}]}, "classes/jdk/jpackage/internal/BasicBundlers.class": {"ver": 68, "acc": 33, "nme": "jdk/jpackage/internal/BasicBundlers", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getBundlers", "acc": 1, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Ljdk/jpackage/internal/Bundler;>;"}, {"nme": "getBundlers", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Collection;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/Collection<Ljdk/jpackage/internal/Bundler;>;"}, {"nme": "loadBundlersFromServices", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;)V"}, {"nme": "lambda$getBundlers$1", "acc": 4106, "dsc": "(I)[Ljdk/jpackage/internal/Bundler;"}, {"nme": "lambda$getBundlers$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljdk/jpackage/internal/Bundler;)Z"}], "flds": [{"acc": 0, "nme": "defaultsLoaded", "dsc": "Z"}, {"acc": 18, "nme": "bundlers", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Ljdk/jpackage/internal/Bundler;>;"}]}, "classes/jdk/jpackage/internal/PathGroup.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/PathGroup", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/util/Map;)V", "sig": "(Ljava/util/Map<Ljava/lang/Object;Ljava/nio/file/Path;>;)V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Ljava/nio/file/Path;"}, {"nme": "set<PERSON>ath", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Ljava/nio/file/Path;)V"}, {"nme": "paths", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/nio/file/Path;>;"}, {"nme": "roots", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/nio/file/Path;>;"}, {"nme": "sizeInBytes", "acc": 0, "dsc": "()J", "exs": ["java/io/IOException"]}, {"nme": "resolveAt", "acc": 0, "dsc": "(Ljava/nio/file/Path;)Ljdk/jpackage/internal/PathGroup;"}, {"nme": "copy", "acc": 0, "dsc": "(Ljdk/jpackage/internal/PathGroup;)V", "exs": ["java/io/IOException"]}, {"nme": "move", "acc": 0, "dsc": "(Ljdk/jpackage/internal/PathGroup;)V", "exs": ["java/io/IOException"]}, {"nme": "transform", "acc": 0, "dsc": "(Ljdk/jpackage/internal/PathGroup;Ljdk/jpackage/internal/PathGroup$TransformHandler;)V", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 10, "dsc": "(Ljdk/jpackage/internal/PathGroup;Ljdk/jpackage/internal/PathGroup;Ljdk/jpackage/internal/PathGroup$TransformHandler;Z)V", "exs": ["java/io/IOException"]}, {"nme": "copy", "acc": 10, "dsc": "(ZLjava/util/List;Ljava/util/List;Ljdk/jpackage/internal/PathGroup$TransformHandler;)V", "sig": "(ZLjava/util/List<Ljava/util/Map$Entry<Ljava/nio/file/Path;Ljava/nio/file/Path;>;>;Ljava/util/List<Ljava/nio/file/Path;>;Ljdk/jpackage/internal/PathGroup$TransformHandler;)V", "exs": ["java/io/IOException"]}, {"nme": "normalizedPath", "acc": 10, "dsc": "(Ljava/nio/file/Path;)Ljava/util/Map$Entry;", "sig": "(Ljava/nio/file/Path;)Ljava/util/Map$Entry<Ljava/nio/file/Path;Ljava/nio/file/Path;>;"}, {"nme": "normalizedPaths", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/util/Map$Entry<Ljava/nio/file/Path;Ljava/nio/file/Path;>;>;"}, {"nme": "lambda$copy$0", "acc": 4106, "dsc": "(Ljava/util/Map;Ljava/nio/file/Path;Ljava/nio/file/Path;Ljava/nio/file/Path;)V"}, {"nme": "lambda$resolveAt$1", "acc": 4106, "dsc": "(Ljava/nio/file/Path;Ljava/util/Map$Entry;)Ljava/nio/file/Path;"}, {"nme": "lambda$resolveAt$0", "acc": 4106, "dsc": "(Lja<PERSON>/util/Map$Entry;)Ljava/lang/Object;"}, {"nme": "lambda$sizeInBytes$2", "acc": 4106, "dsc": "(Ljava/nio/file/Path;)J"}, {"nme": "lambda$sizeInBytes$1", "acc": 4106, "dsc": "(Ljava/nio/file/Path;)Z"}, {"nme": "lambda$sizeInBytes$0", "acc": 4106, "dsc": "(Ljava/nio/file/Path;)Z"}, {"nme": "lambda$roots$4", "acc": 4106, "dsc": "(Ljava/util/Map$Entry;)Ljava/nio/file/Path;"}, {"nme": "lambda$roots$2", "acc": 4106, "dsc": "(Lja<PERSON>/util/List;Ljava/util/function/BiFunction;Ljava/util/Map$Entry;)Z"}, {"nme": "lambda$roots$3", "acc": 4106, "dsc": "(Lja<PERSON>/util/function/BiFunction;Ljava/util/Map$Entry;Ljava/util/Map$Entry;)Z"}, {"nme": "lambda$roots$1", "acc": 4106, "dsc": "(Ljava/util/Map$Entry;Ljava/util/Map$Entry;)Ljava/lang/<PERSON>an;"}, {"nme": "lambda$roots$0", "acc": 4106, "dsc": "(Ljava/util/Map$Entry;Ljava/util/Map$Entry;)I"}], "flds": [{"acc": 18, "nme": "entries", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Ljava/nio/file/Path;>;"}]}, "classes/jdk/jpackage/internal/WixTool$ToolLookupResult.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/WixTool$ToolLookupResult", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/jpackage/internal/WixTool;Ljava/nio/file/Path;)V"}, {"nme": "getTool", "acc": 0, "dsc": "()Ljdk/jpackage/internal/WixTool;"}, {"nme": "getInfo", "acc": 0, "dsc": "()Ljdk/jpackage/internal/WixTool$ToolInfo;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 0, "dsc": "()Z"}, {"nme": "isVersionTooOld", "acc": 0, "dsc": "()Z"}, {"nme": "lambda$new$5", "acc": 4106, "dsc": "([Lja<PERSON>/lang/String;Lja<PERSON>/util/function/Function;Ljava/util/stream/Stream;)Ljava/lang/String;"}, {"nme": "lambda$new$4", "acc": 4106, "dsc": "(L<PERSON><PERSON>/util/stream/Stream;)Ljava/lang/String;"}, {"nme": "lambda$new$3", "acc": 4106, "dsc": "(L<PERSON><PERSON>/util/stream/Stream;)Ljava/lang/String;"}, {"nme": "lambda$new$2", "acc": 4106, "dsc": "([<PERSON><PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/String;)Ljdk/jpackage/internal/ConfigException;"}, {"nme": "lambda$new$1", "acc": 4106, "dsc": "(L<PERSON><PERSON>/lang/String;Ljava/io/IOException;)Ljdk/jpackage/internal/ConfigException;"}, {"nme": "lambda$new$0", "acc": 4106, "dsc": "(Ljdk/jpackage/internal/WixTool;Ljava/nio/file/Path;)Ljava/nio/file/Path;"}], "flds": [{"acc": 18, "nme": "tool", "dsc": "Ljdk/jpackage/internal/WixTool;"}, {"acc": 18, "nme": "info", "dsc": "Ljdk/jpackage/internal/WixTool$ToolInfo;"}, {"acc": 18, "nme": "versionTooOld", "dsc": "Z"}]}, "classes/jdk/jpackage/internal/PlatformPackage.class": {"ver": 68, "acc": 1536, "nme": "jdk/jpackage/internal/PlatformPackage", "super": "java/lang/Object", "mthds": [{"nme": "name", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "sourceRoot", "acc": 1025, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "sourceApplicationLayout", "acc": 1025, "dsc": "()Ljdk/jpackage/internal/ApplicationLayout;"}, {"nme": "installedApplicationLayout", "acc": 1025, "dsc": "()Ljdk/jpackage/internal/ApplicationLayout;"}], "flds": []}, "classes/jdk/jpackage/internal/util/function/ThrowingConsumer.class": {"ver": 68, "acc": 1537, "nme": "jdk/jpackage/internal/util/function/ThrowingConsumer", "super": "java/lang/Object", "mthds": [{"nme": "accept", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TT;)V", "exs": ["java/lang/Throwable"]}, {"nme": "toConsumer", "acc": 9, "dsc": "(Ljdk/jpackage/internal/util/function/ThrowingConsumer;)Ljava/util/function/Consumer;", "sig": "<T:Ljava/lang/Object;>(Ljdk/jpackage/internal/util/function/ThrowingConsumer<TT;>;)Ljava/util/function/Consumer<TT;>;"}, {"nme": "lambda$toConsumer$0", "acc": 4106, "dsc": "(Ljdk/jpackage/internal/util/function/ThrowingConsumer;Lja<PERSON>/lang/Object;)V"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/FunctionalInterface;"}]}, "classes/jdk/jpackage/internal/util/function/ThrowingUnaryOperator.class": {"ver": 68, "acc": 1537, "nme": "jdk/jpackage/internal/util/function/ThrowingUnaryOperator", "super": "java/lang/Object", "mthds": [{"nme": "apply", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(TT;)TT;", "exs": ["java/lang/Throwable"]}, {"nme": "toUnaryOperator", "acc": 9, "dsc": "(Ljdk/jpackage/internal/util/function/ThrowingUnaryOperator;)Ljava/util/function/UnaryOperator;", "sig": "<T:Ljava/lang/Object;>(Ljdk/jpackage/internal/util/function/ThrowingUnaryOperator<TT;>;)Ljava/util/function/UnaryOperator<TT;>;"}, {"nme": "lambda$toUnaryOperator$0", "acc": 4106, "dsc": "(Ljdk/jpackage/internal/util/function/ThrowingUnaryOperator;Ljava/lang/Object;)Ljava/lang/Object;"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/FunctionalInterface;"}]}, "classes/jdk/jpackage/internal/WixAppImageFragmentBuilder.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/WixAppImageFragmentBuilder", "super": "jdk/jpackage/internal/WixFragmentBuilder", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "initFromParams", "acc": 0, "dsc": "(Ljava/util/Map;)V", "sig": "(L<PERSON><PERSON>/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)V"}, {"nme": "addFilesToConfigRoot", "acc": 0, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "getLoggableWixFeatures", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "getFragmentWriters", "acc": 4, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Ljdk/jpackage/internal/util/XmlConsumer;>;"}, {"nme": "normalizeFileAssociation", "acc": 2, "dsc": "(Ljdk/jpackage/internal/FileAssociation;)V"}, {"nme": "addExeSuffixToPath", "acc": 10, "dsc": "(Ljava/nio/file/Path;)Ljava/nio/file/Path;"}, {"nme": "getInstalledFaIcoPath", "acc": 2, "dsc": "(Ljdk/jpackage/internal/FileAssociation;)Ljava/nio/file/Path;"}, {"nme": "initFileAssociations", "acc": 2, "dsc": "(Ljava/util/Map;)V", "sig": "(L<PERSON><PERSON>/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)V"}, {"nme": "createNameUUID", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/UUID;"}, {"nme": "createNameUUID", "acc": 10, "dsc": "(Ljava/nio/file/Path;Ljava/lang/String;)Ljava/util/UUID;"}, {"nme": "addComponentGroup", "acc": 10, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;Ljava/lang/String;Ljava/util/List;)V", "sig": "(Ljavax/xml/stream/XMLStreamWriter;Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;)V", "exs": ["javax/xml/stream/XMLStreamException", "java/io/IOException"]}, {"nme": "addComponent", "acc": 2, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;Ljava/nio/file/Path;Ljdk/jpackage/internal/WixAppImageFragmentBuilder$Component;Ljdk/jpackage/internal/util/XmlConsumer;)Ljava/lang/String;", "exs": ["javax/xml/stream/XMLStreamException", "java/io/IOException"]}, {"nme": "addFaComponentGroup", "acc": 2, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;)V", "exs": ["javax/xml/stream/XMLStreamException", "java/io/IOException"]}, {"nme": "addShortcutComponentGroup", "acc": 2, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;)V", "exs": ["javax/xml/stream/XMLStreamException", "java/io/IOException"]}, {"nme": "addShortcutComponent", "acc": 2, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;Ljava/nio/file/Path;Ljdk/jpackage/internal/WixAppImageFragmentBuilder$ShortcutsFolder;)Ljava/lang/String;", "exs": ["javax/xml/stream/XMLStreamException", "java/io/IOException"]}, {"nme": "addFaComponents", "acc": 2, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;Ljdk/jpackage/internal/FileAssociation;)Ljava/util/List;", "sig": "(Ljavax/xml/stream/XMLStreamWriter;Ljdk/jpackage/internal/FileAssociation;)Ljava/util/List<Ljava/lang/String;>;", "exs": ["javax/xml/stream/XMLStreamException", "java/io/IOException"]}, {"nme": "addRootBranch", "acc": 2, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;Ljava/nio/file/Path;)Ljava/util/List;", "sig": "(Ljavax/xml/stream/XMLStreamWriter;Ljava/nio/file/Path;)Ljava/util/List<Ljava/lang/String;>;", "exs": ["javax/xml/stream/XMLStreamException", "java/io/IOException"]}, {"nme": "startDirectoryElement", "acc": 2, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;Ljava/lang/String;Ljava/nio/file/Path;)V", "exs": ["javax/xml/stream/XMLStreamException"]}, {"nme": "addRemoveDirectoryComponent", "acc": 2, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;Ljava/nio/file/Path;)Ljava/lang/String;", "exs": ["javax/xml/stream/XMLStreamException", "java/io/IOException"]}, {"nme": "addDirectoryHierarchy", "acc": 2, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;)Ljava/util/List;", "sig": "(Ljavax/xml/stream/XMLStreamWriter;)Ljava/util/List<Ljava/lang/String;>;", "exs": ["javax/xml/stream/XMLStreamException", "java/io/IOException"]}, {"nme": "addFilesComponentGroup", "acc": 2, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;)V", "exs": ["javax/xml/stream/XMLStreamException", "java/io/IOException"]}, {"nme": "addServiceConfigs", "acc": 2, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;)Ljava/util/List;", "sig": "(Ljavax/xml/stream/XMLStreamWriter;)Ljava/util/List<Ljava/lang/String;>;", "exs": ["javax/xml/stream/XMLStreamException", "java/io/IOException"]}, {"nme": "addIcons", "acc": 2, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;)V", "exs": ["javax/xml/stream/XMLStreamException", "java/io/IOException"]}, {"nme": "addRegistry<PERSON>eyPath", "acc": 2, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;Ljava/nio/file/Path;)V", "exs": ["javax/xml/stream/XMLStreamException", "java/io/IOException"]}, {"nme": "addRegistry<PERSON>eyPath", "acc": 2, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;Ljava/nio/file/Path;Ljava/util/function/Supplier;Ljava/util/function/Supplier;)V", "sig": "(Ljavax/xml/stream/XMLStreamWriter;Ljava/nio/file/Path;Ljava/util/function/Supplier<Ljava/lang/String;>;Ljava/util/function/Supplier<Ljava/lang/String;>;)V", "exs": ["javax/xml/stream/XMLStreamException", "java/io/IOException"]}, {"nme": "addDirectoryCleaner", "acc": 2, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;Ljava/nio/file/Path;)Ljava/lang/String;", "exs": ["javax/xml/stream/XMLStreamException", "java/io/IOException"]}, {"nme": "isWithWix36Features", "acc": 2, "dsc": "()Z"}, {"nme": "toWixPath", "acc": 10, "dsc": "(Lja<PERSON>/nio/file/Path;)Ljava/lang/String;"}, {"nme": "throwInvalidPathException", "acc": 10, "dsc": "(Lja<PERSON>/nio/file/Path;)Ljava/lang/IllegalArgumentException;"}, {"nme": "lambda$static$3", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$2", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;"}, {"nme": "lambda$addDirectoryCleaner$1", "acc": 4106, "dsc": "(Lja<PERSON>/nio/file/Path;)Ljava/lang/String;"}, {"nme": "lambda$addDirectoryCleaner$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "lambda$addRegistryKeyPath$1", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$addRegistryKeyPath$0", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$addFilesComponentGroup$0", "acc": 4106, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;Ljava/nio/file/Path;Ljava/nio/file/Path;Ljavax/xml/stream/XMLStreamWriter;)V", "exs": ["java/io/IOException", "javax/xml/stream/XMLStreamException"]}, {"nme": "lambda$addDirectoryHierarchy$0", "acc": 4106, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;)V", "exs": ["java/io/IOException", "javax/xml/stream/XMLStreamException"]}, {"nme": "lambda$addRemoveDirectoryComponent$0", "acc": 4106, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;Ljavax/xml/stream/XMLStreamWriter;)V", "exs": ["java/io/IOException", "javax/xml/stream/XMLStreamException"]}, {"nme": "lambda$addFaComponents$0", "acc": 4098, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;Ljdk/jpackage/internal/FileAssociation;Ljava/lang/String;Ljavax/xml/stream/XMLStreamWriter;)V", "exs": ["java/io/IOException", "javax/xml/stream/XMLStreamException"]}, {"nme": "lambda$addShortcutComponent$0", "acc": 4106, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;Ljava/lang/String;Ljava/nio/file/Path;Ljavax/xml/stream/XMLStreamWriter;)V", "exs": ["java/io/IOException", "javax/xml/stream/XMLStreamException"]}, {"nme": "lambda$addComponent$1", "acc": 4106, "dsc": "(Ljdk/jpackage/internal/WixAppImageFragmentBuilder$ShortcutsFolder;)Ljava/lang/String;"}, {"nme": "lambda$addComponent$0", "acc": 4106, "dsc": "(Ljava/nio/file/Path;Ljdk/jpackage/internal/WixAppImageFragmentBuilder$ShortcutsFolder;)Z"}, {"nme": "lambda$initFileAssociations$2", "acc": 4098, "dsc": "(Ljdk/jpackage/internal/FileAssociation;)V"}, {"nme": "lambda$initFileAssociations$1", "acc": 4106, "dsc": "(Ljdk/jpackage/internal/FileAssociation;)Z"}, {"nme": "lambda$initFileAssociations$0", "acc": 4106, "dsc": "(Ljdk/jpackage/internal/FileAssociation;)Z"}, {"nme": "lambda$getFragmentWriters$0", "acc": 4098, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;)V", "exs": ["java/io/IOException", "javax/xml/stream/XMLStreamException"]}, {"nme": "lambda$initFromParams$2", "acc": 4098, "dsc": "(Ljava/util/Map;Ljdk/jpackage/internal/AppImageFile$LauncherInfo;)Ljdk/jpackage/internal/WixLauncherAsService;"}, {"nme": "lambda$initFromParams$1", "acc": 4106, "dsc": "(Ljava/util/Map;Ljdk/jpackage/internal/WixAppImageFragmentBuilder$ShortcutsFolder;)Z"}, {"nme": "lambda$initFromParams$0", "acc": 4106, "dsc": "(Ljava/util/Map;)Ljdk/jpackage/internal/ApplicationLayout;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "systemWide", "dsc": "Z"}, {"acc": 2, "nme": "registryKeyPath", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "installDir", "dsc": "Ljava/nio/file/Path;"}, {"acc": 2, "nme": "programMenuFolderName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "associations", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/jpackage/internal/FileAssociation;>;"}, {"acc": 2, "nme": "shortcutFolders", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljdk/jpackage/internal/WixAppImageFragmentBuilder$ShortcutsFolder;>;"}, {"acc": 2, "nme": "launchers", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/jpackage/internal/AppImageFile$LauncherInfo;>;"}, {"acc": 2, "nme": "launchersAsServices", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/jpackage/internal/WixLauncherAsService;>;"}, {"acc": 2, "nme": "serviceInstaller", "dsc": "Ljdk/jpackage/internal/InstallableFile;"}, {"acc": 2, "nme": "appImage", "dsc": "Ljdk/jpackage/internal/ApplicationLayout;"}, {"acc": 2, "nme": "installedAppImage", "dsc": "Ljdk/jpackage/internal/ApplicationLayout;"}, {"acc": 2, "nme": "removeFolderItems", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/nio/file/Path;Ljava/lang/Integer;>;"}, {"acc": 2, "nme": "defaultedMimes", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 26, "nme": "TARGETDIR", "dsc": "Ljava/nio/file/Path;"}, {"acc": 26, "nme": "INSTALLDIR", "dsc": "Ljava/nio/file/Path;"}, {"acc": 26, "nme": "ROOT_DIRS", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/nio/file/Path;>;"}, {"acc": 26, "nme": "PROGRAM_MENU_PATH", "dsc": "Ljava/nio/file/Path;"}, {"acc": 26, "nme": "DESKTOP_PATH", "dsc": "Ljava/nio/file/Path;"}, {"acc": 26, "nme": "PROGRAM_FILES", "dsc": "Ljava/nio/file/Path;"}, {"acc": 26, "nme": "LOCAL_PROGRAM_FILES", "dsc": "Ljava/nio/file/Path;"}, {"acc": 26, "nme": "SYSTEM_DIRS", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/nio/file/Path;>;"}, {"acc": 26, "nme": "KNOWN_DIRS", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/nio/file/Path;>;"}, {"acc": 26, "nme": "USER_PROFILE_DIRS", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/nio/file/Path;>;"}, {"acc": 26, "nme": "MENU_GROUP", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/lang/String;>;"}, {"acc": 26, "nme": "WINDOWS_INSTALL_DIR", "dsc": "Ljdk/jpackage/internal/BundlerParamInfo;", "sig": "Ljdk/jpackage/internal/BundlerParamInfo<Ljava/lang/String;>;"}]}, "classes/jdk/jpackage/internal/WindowsAppImageBuilder.class": {"ver": 68, "acc": 33, "nme": "jdk/jpackage/internal/WindowsAppImageBuilder", "super": "jdk/jpackage/internal/AbstractAppImageBuilder", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/nio/file/Path;)V"}, {"nme": "writeEntry", "acc": 2, "dsc": "(Ljava/io/InputStream;Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "getLauncherName", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;", "sig": "(L<PERSON><PERSON>/util/Map<Ljava/lang/String;-<PERSON><PERSON><PERSON>/lang/Object;>;)Ljava/lang/String;"}, {"nme": "getLauncherResourceName", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;", "sig": "(L<PERSON><PERSON>/util/Map<Ljava/lang/String;-<PERSON><PERSON><PERSON>/lang/Object;>;)Ljava/lang/String;"}, {"nme": "prepareApplicationFiles", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(L<PERSON><PERSON>/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)V", "exs": ["java/io/IOException"]}, {"nme": "createLauncherForEntryPoint", "acc": 2, "dsc": "(Ljava/util/Map;Ljava/util/Map;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;-L<PERSON>va/lang/Object;>;Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)V", "exs": ["java/io/IOException"]}, {"nme": "lambda$static$3", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Map;)<PERSON><PERSON><PERSON>/lang/Boolean;"}, {"nme": "lambda$static$2", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/Boolean;"}, {"nme": "lambda$static$1", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/Map;)Ljava/nio/file/Path;"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "(Ljava/util/Map;)Ljava/nio/file/Path;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "I18N", "dsc": "Ljava/util/ResourceBundle;"}, {"acc": 26, "nme": "TEMPLATE_APP_ICON", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "JavaApp.ico"}, {"acc": 25, "nme": "ICON_ICO", "dsc": "Ljdk/jpackage/internal/BundlerParamInfo;", "sig": "Ljdk/jpackage/internal/BundlerParamInfo<Ljava/nio/file/Path;>;"}, {"acc": 25, "nme": "CONSOLE_HINT", "dsc": "Ljdk/jpackage/internal/StandardBundlerParam;", "sig": "Ljdk/jpackage/internal/StandardBundlerParam<Ljava/lang/Boolean;>;"}]}, "classes/jdk/jpackage/internal/Log$Logger.class": {"ver": 68, "acc": 33, "nme": "jdk/jpackage/internal/Log$Logger", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "setVerbose", "acc": 1, "dsc": "()V"}, {"nme": "isVerbose", "acc": 1, "dsc": "()Z"}, {"nme": "setPrintWriter", "acc": 1, "dsc": "(Ljava/io/PrintWriter;Ljava/io/PrintWriter;)V"}, {"nme": "flush", "acc": 1, "dsc": "()V"}, {"nme": "info", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "fatalError", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "error", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "verbose", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "verbose", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "verbose", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/util/List;IJ)V", "sig": "(Ljava/util/List<Ljava/lang/String;>;Ljava/util/List<Ljava/lang/String;>;IJ)V"}, {"nme": "addTimestamp", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": [{"acc": 2, "nme": "verbose", "dsc": "Z"}, {"acc": 2, "nme": "out", "dsc": "Ljava/io/PrintWriter;"}, {"acc": 2, "nme": "err", "dsc": "Ljava/io/PrintWriter;"}]}, "classes/jdk/jpackage/internal/Log$1.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/Log$1", "super": "java/lang/InheritableThreadLocal", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "initialValue", "acc": 4, "dsc": "()Ljdk/jpackage/internal/Log$Logger;"}, {"nme": "initialValue", "acc": 4164, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/jpackage/internal/util/XmlUtils.class": {"ver": 68, "acc": 49, "nme": "jdk/jpackage/internal/util/XmlUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "createXml", "acc": 9, "dsc": "(Ljava/nio/file/Path;Ljdk/jpackage/internal/util/XmlConsumer;)V", "exs": ["java/io/IOException"]}, {"nme": "mergeXmls", "acc": 9, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;Ljava/util/Collection;)V", "sig": "(Ljavax/xml/stream/XMLStreamWriter;Ljava/util/Collection<Ljavax/xml/transform/Source;>;)V", "exs": ["javax/xml/stream/XMLStreamException", "java/io/IOException"]}, {"nme": "initDocumentBuilder", "acc": 9, "dsc": "()Ljavax/xml/parsers/DocumentBuilder;"}, {"nme": "initDocumentBuilderFactory", "acc": 9, "dsc": "()Ljavax/xml/parsers/DocumentBuilderFactory;"}], "flds": []}, "classes/jdk/jpackage/internal/WixFragmentBuilder$1.class": {"ver": 68, "acc": 4128, "nme": "jdk/jpackage/internal/WixFragmentBuilder$1", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$jdk$jpackage$internal$WixToolset$WixToolsetType", "dsc": "[I"}]}, "classes/jdk/jpackage/internal/WixPipeline$1.class": {"ver": 68, "acc": 4128, "nme": "jdk/jpackage/internal/WixPipeline$1", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$jdk$jpackage$internal$WixToolset$WixToolsetType", "dsc": "[I"}]}, "classes/jdk/jpackage/internal/I18N$MultiResourceBundle.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/I18N$MultiResourceBundle", "super": "java/util/ListResourceBundle", "mthds": [{"nme": "<init>", "acc": 128, "dsc": "([Ljava/util/ResourceBundle;)V"}, {"nme": "getContents", "acc": 4, "dsc": "()[[<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "lambda$new$5", "acc": 4106, "dsc": "(I)[[<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "lambda$new$4", "acc": 4106, "dsc": "(Lja<PERSON>/util/Map$Entry;)[Ljava/lang/Object;"}, {"nme": "lambda$new$3", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "lambda$new$2", "acc": 4106, "dsc": "(Ljava/util/stream/Stream;)Ljava/util/stream/Stream;"}, {"nme": "lambda$new$0", "acc": 4106, "dsc": "(Ljava/util/ResourceBundle;)Ljava/util/stream/Stream;"}, {"nme": "lambda$new$1", "acc": 4106, "dsc": "(Lja<PERSON>/util/ResourceBundle;Ljava/lang/String;)Ljava/util/Map$Entry;"}], "flds": [{"acc": 18, "nme": "contents", "dsc": "[[<PERSON><PERSON><PERSON>/lang/Object;"}]}, "classes/jdk/jpackage/internal/util/XmlConsumer.class": {"ver": 68, "acc": 1537, "nme": "jdk/jpackage/internal/util/XmlConsumer", "super": "java/lang/Object", "mthds": [{"nme": "accept", "acc": 1025, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;)V", "exs": ["java/io/IOException", "javax/xml/stream/XMLStreamException"]}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/FunctionalInterface;"}]}, "classes/jdk/jpackage/internal/WixUiFragmentBuilder$UI.class": {"ver": 68, "acc": 16432, "nme": "jdk/jpackage/internal/WixUiFragmentBuilder$UI", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/jpackage/internal/WixUiFragmentBuilder$UI;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;)Ljdk/jpackage/internal/WixUiFragmentBuilder$UI;"}, {"nme": "<init>", "acc": 2, "dsc": "(Lja<PERSON>/lang/String;<PERSON>java/lang/String;Ljava/util/function/Function;Ljava/util/function/Supplier;)V", "sig": "(Ljava/lang/String;Ljava/util/function/Function<Ljdk/jpackage/internal/WixUiFragmentBuilder;Ljava/util/List<Ljdk/jpackage/internal/WixUiFragmentBuilder$Dialog;>;>;Ljava/util/function/Supplier<Ljava/util/Map<Ljdk/jpackage/internal/WixUiFragmentBuilder$DialogPair;Ljava/util/List<Ljdk/jpackage/internal/WixUiFragmentBuilder$Publish;>;>;>;)V"}, {"nme": "write", "acc": 0, "dsc": "(Ljdk/jpackage/internal/WixToolset$WixToolsetType;Ljdk/jpackage/internal/WixUiFragmentBuilder;Ljavax/xml/stream/XMLStreamWriter;)V", "exs": ["javax/xml/stream/XMLStreamException", "java/io/IOException"]}, {"nme": "writeContents", "acc": 2, "dsc": "(Ljdk/jpackage/internal/WixToolset$WixToolsetType;Ljdk/jpackage/internal/WixUiFragmentBuilder;Ljavax/xml/stream/XMLStreamWriter;)V", "exs": ["javax/xml/stream/XMLStreamException", "java/io/IOException"]}, {"nme": "writeWix4UIRef", "acc": 10, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["javax/xml/stream/XMLStreamException", "java/io/IOException"]}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/jpackage/internal/WixUiFragmentBuilder$UI;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "InstallDir", "dsc": "Ljdk/jpackage/internal/WixUiFragmentBuilder$UI;"}, {"acc": 16409, "nme": "Minimal", "dsc": "Ljdk/jpackage/internal/WixUiFragmentBuilder$UI;"}, {"acc": 18, "nme": "wixUIRef", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "dialogIdsSupplier", "dsc": "Ljava/util/function/Function;", "sig": "Ljava/util/function/Function<Ljdk/jpackage/internal/WixUiFragmentBuilder;Ljava/util/List<Ljdk/jpackage/internal/WixUiFragmentBuilder$Dialog;>;>;"}, {"acc": 18, "nme": "dialogPairsSupplier", "dsc": "Ljava/util/function/Supplier;", "sig": "Ljava/util/function/Supplier<Ljava/util/Map<Ljdk/jpackage/internal/WixUiFragmentBuilder$DialogPair;Ljava/util/List<Ljdk/jpackage/internal/WixUiFragmentBuilder$Publish;>;>;>;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/jpackage/internal/WixUiFragmentBuilder$UI;"}]}, "classes/jdk/jpackage/internal/util/function/ThrowingRunnable.class": {"ver": 68, "acc": 1537, "nme": "jdk/jpackage/internal/util/function/ThrowingRunnable", "super": "java/lang/Object", "mthds": [{"nme": "run", "acc": 1025, "dsc": "()V", "exs": ["java/lang/Throwable"]}, {"nme": "toRunnable", "acc": 9, "dsc": "(Ljdk/jpackage/internal/util/function/ThrowingRunnable;)Ljava/lang/Runnable;"}, {"nme": "lambda$toRunnable$0", "acc": 4106, "dsc": "(Ljdk/jpackage/internal/util/function/ThrowingRunnable;)V"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/FunctionalInterface;"}]}, "classes/jdk/jpackage/internal/WixFragmentBuilder.class": {"ver": 68, "acc": 1056, "nme": "jdk/jpackage/internal/WixFragmentBuilder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "setWixVersion", "acc": 16, "dsc": "(Ljdk/jpackage/internal/DottedVersion;Ljdk/jpackage/internal/WixToolset$WixToolsetType;)V"}, {"nme": "setOutputFileName", "acc": 16, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "initFromParams", "acc": 0, "dsc": "(Ljava/util/Map;)V", "sig": "(L<PERSON><PERSON>/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)V"}, {"nme": "getLoggableWixFeatures", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "configureWixPipeline", "acc": 0, "dsc": "(Ljdk/jpackage/internal/WixPipeline$Builder;)V"}, {"nme": "addFilesToConfigRoot", "acc": 0, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "getWixType", "acc": 16, "dsc": "()Ljdk/jpackage/internal/WixToolset$WixToolsetType;"}, {"nme": "getWixVersion", "acc": 16, "dsc": "()Ljdk/jpackage/internal/DottedVersion;"}, {"nme": "getWixNamespaces", "acc": 20, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljdk/jpackage/internal/WixFragmentBuilder$WixNamespace;Ljava/lang/String;>;"}, {"nme": "is64Bit", "acc": 8, "dsc": "()Z"}, {"nme": "getConfigRoot", "acc": 20, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "getFragmentWriters", "acc": 1028, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Ljdk/jpackage/internal/util/XmlConsumer;>;"}, {"nme": "defineWixVariable", "acc": 20, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setWixVariable", "acc": 20, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "addResource", "acc": 20, "dsc": "(Ljdk/jpackage/internal/OverridableResource;Ljava/lang/String;)V"}, {"nme": "createWixSource", "acc": 2, "dsc": "(Ljava/nio/file/Path;Ljdk/jpackage/internal/util/XmlConsumer;)V", "exs": ["java/io/IOException"]}, {"nme": "lambda$createWixSource$0", "acc": 4098, "dsc": "(Ljdk/jpackage/internal/util/XmlConsumer;Ljavax/xml/stream/XMLStreamWriter;)V", "exs": ["java/io/IOException", "javax/xml/stream/XMLStreamException"]}, {"nme": "lambda$addFilesToConfigRoot$0", "acc": 4098, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;)V", "exs": ["java/io/IOException", "javax/xml/stream/XMLStreamException"]}], "flds": [{"acc": 2, "nme": "wixType", "dsc": "Ljdk/jpackage/internal/WixToolset$WixToolsetType;"}, {"acc": 2, "nme": "wixVersion", "dsc": "Ljdk/jpackage/internal/DottedVersion;"}, {"acc": 2, "nme": "wixVariables", "dsc": "Ljdk/jpackage/internal/WixVariables;"}, {"acc": 2, "nme": "additionalResources", "dsc": "Ljdk/jpackage/internal/WixSourceConverter$ResourceGroup;"}, {"acc": 2, "nme": "fragmentResource", "dsc": "Ljdk/jpackage/internal/OverridableResource;"}, {"acc": 2, "nme": "outputFileName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "configRoot", "dsc": "Ljava/nio/file/Path;"}]}, "classes/jdk/jpackage/internal/WixAppImageFragmentBuilder$Component$Config.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/WixAppImageFragmentBuilder$Component$Config", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 0, "dsc": "()Ljdk/jpackage/internal/WixAppImageFragmentBuilder$Component$Config;"}, {"nme": "file", "acc": 0, "dsc": "()Ljdk/jpackage/internal/WixAppImageFragmentBuilder$Component$Config;"}], "flds": [{"acc": 2, "nme": "isFile", "dsc": "Z"}, {"acc": 2, "nme": "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dsc": "Z"}]}, "classes/jdk/jpackage/internal/JPackageToolProvider.class": {"ver": 68, "acc": 33, "nme": "jdk/jpackage/internal/JPackageToolProvider", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "name", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "description", "acc": 1, "dsc": "()Ljava/util/Optional;", "sig": "()Ljava/util/Optional<Ljava/lang/String;>;"}, {"nme": "run", "acc": 161, "dsc": "(Ljava/io/PrintWriter;Ljava/io/PrintWriter;[<PERSON>ja<PERSON>/lang/String;)I"}], "flds": []}, "classes/jdk/jpackage/internal/OverridableResource$ResourceConsumer.class": {"ver": 68, "acc": 1536, "nme": "jdk/jpackage/internal/OverridableResource$ResourceConsumer", "super": "java/lang/Object", "mthds": [{"nme": "publicName", "acc": 1025, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "consume", "acc": 1025, "dsc": "(<PERSON><PERSON>va/io/InputStream;)V", "exs": ["java/io/IOException"]}], "flds": []}, "classes/jdk/jpackage/internal/WixSourceConverter$NamespaceCleaner$Prefix.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/WixSourceConverter$NamespaceCleaner$Prefix", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "written", "dsc": "Z"}]}, "classes/jdk/jpackage/internal/BundleParams.class": {"ver": 68, "acc": 33, "nme": "jdk/jpackage/internal/BundleParams", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;*>;)V"}, {"nme": "addAllBundleParams", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(L<PERSON><PERSON>/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;)V"}, {"nme": "getBundleParamsAsMap", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "putUnlessNull", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}], "flds": [{"acc": 20, "nme": "params", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;-Ljava/lang/Object;>;"}]}, "classes/jdk/jpackage/internal/WixPipeline$WixSource.class": {"ver": 68, "acc": 65584, "nme": "jdk/jpackage/internal/WixPipeline$WixSource", "super": "java/lang/Record", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljava/nio/file/Path;Ljava/util/Map;)V", "sig": "(Ljava/nio/file/Path;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "override<PERSON><PERSON>", "acc": 0, "dsc": "(Ljava/nio/file/Path;)Ljdk/jpackage/internal/WixPipeline$WixSource;"}, {"nme": "toString", "acc": 17, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "hashCode", "acc": 17, "dsc": "()I"}, {"nme": "equals", "acc": 17, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "path", "acc": 1, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "variables", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}], "flds": [{"acc": 18, "nme": "path", "dsc": "Ljava/nio/file/Path;"}, {"acc": 18, "nme": "variables", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}]}, "classes/jdk/jpackage/internal/InstallableFile.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/InstallableFile", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/Path;)V"}, {"nme": "installPath", "acc": 0, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "srcPath", "acc": 0, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "applyToApplicationLayouts", "acc": 0, "dsc": "(Ljdk/jpackage/internal/ApplicationLayout;Ljdk/jpackage/internal/ApplicationLayout;)V"}, {"nme": "excludeFromApplicationLayout", "acc": 0, "dsc": "(Ljdk/jpackage/internal/ApplicationLayout;)V"}], "flds": [{"acc": 18, "nme": "installPath", "dsc": "Ljava/nio/file/Path;"}, {"acc": 18, "nme": "srcPath", "dsc": "Ljava/nio/file/Path;"}]}, "classes/jdk/jpackage/internal/OverridableResource$Source.class": {"ver": 68, "acc": 16432, "nme": "jdk/jpackage/internal/OverridableResource$Source", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/jpackage/internal/OverridableResource$Source;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Ljdk/jpackage/internal/OverridableResource$Source;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/jpackage/internal/OverridableResource$Source;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "External", "dsc": "Ljdk/jpackage/internal/OverridableResource$Source;"}, {"acc": 16409, "nme": "ResourceDir", "dsc": "Ljdk/jpackage/internal/OverridableResource$Source;"}, {"acc": 16409, "nme": "DefaultResource", "dsc": "Ljdk/jpackage/internal/OverridableResource$Source;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/jpackage/internal/OverridableResource$Source;"}]}, "classes/jdk/jpackage/internal/ShortPathUtils$ShortPathException.class": {"ver": 68, "acc": 48, "nme": "jdk/jpackage/internal/ShortPathUtils$ShortPathException", "super": "java/lang/RuntimeException", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}]}, "classes/jdk/jpackage/internal/WixFragmentBuilder$WixPreprocessorEscaper.class": {"ver": 68, "acc": 32, "nme": "jdk/jpackage/internal/WixFragmentBuilder$WixPreprocessorEscaper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljavax/xml/stream/XMLStreamWriter;)V"}, {"nme": "invoke", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/reflect/Method;[Ljava/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Throwable"]}, {"nme": "escape", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;)Ljava/lang/String;"}], "flds": [{"acc": 18, "nme": "dollarPattern", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 18, "nme": "target", "dsc": "Ljavax/xml/stream/XMLStreamWriter;"}]}}}}