{"input": "target/SbMagicHook.jar", "output": "target/SbMagicHook-obfuscated.jar", "libraries": "target/libs", "exempt": ["shyrcs.extrastoragehook.SbMagicHook", "shyrcs.extrastoragehook.application.PluginBoot", "shyrcs.extrastoragehook.minecraft.CommandSbMagicHook", "shyrcs.extrastoragehook.minecraft.PlayerListener"], "phantom": ["org.bukkit.**", "net.milkbowl.vault.**", "me.hsgamer.extrastorage.**", "me.gypopo.economyshopgui.**"], "transformers": {"string_encryption": {"enabled": true, "strength": 3}, "flow_obfuscation": {"enabled": true, "strength": 2}, "outlining": {"enabled": true}, "number_obfuscation": {"enabled": true}, "reference_obfuscation": {"enabled": true}, "class_folder": {"enabled": true}}, "debug": false, "runtime_name_generation": true}