{"md5": "ef0e782af9ee48fac1156485366d7cc9", "sha2": "c7ce3cdeda3d18909368dfe5977332dfad326c6d", "sha256": "2037be378980d3ba9333e97955f3b2cde392aa124d04ca73ce2eee6657199297", "contents": {"classes": {"org/intellij/lang/annotations/JdkConstants$CalendarMonth.class": {"ver": 52, "acc": 9729, "nme": "org/intellij/lang/annotations/JdkConstants$CalendarMonth", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/intellij/lang/annotations/JdkConstants$ListSelectionMode.class": {"ver": 52, "acc": 9729, "nme": "org/intellij/lang/annotations/JdkConstants$ListSelectionMode", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/jetbrains/annotations/MustBeInvokedByOverriders.class": {"ver": 52, "acc": 9729, "nme": "org/jetbrains/annotations/MustBeInvokedByOverriders", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}]}, "org/intellij/lang/annotations/JdkConstants$TitledBorderJustification.class": {"ver": 52, "acc": 9729, "nme": "org/intellij/lang/annotations/JdkConstants$TitledBorderJustification", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/intellij/lang/annotations/Pattern.class": {"ver": 52, "acc": 9729, "nme": "org/intellij/lang/annotations/Pattern", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/intellij/lang/annotations/Language;", "vals": ["value", "RegExp"]}, {"dsc": "Lorg/jetbrains/annotations/NonNls;"}]}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "FIELD"], ["L<PERSON>va/lang/annotation/ElementType;", "PARAMETER"], ["L<PERSON>va/lang/annotation/ElementType;", "LOCAL_VARIABLE"], ["L<PERSON>va/lang/annotation/ElementType;", "ANNOTATION_TYPE"]]]}]}, "org/intellij/lang/annotations/JdkConstants$FlowLayoutAlignment.class": {"ver": 52, "acc": 9729, "nme": "org/intellij/lang/annotations/JdkConstants$FlowLayoutAlignment", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/intellij/lang/annotations/JdkConstants$TreeSelectionMode.class": {"ver": 52, "acc": 9729, "nme": "org/intellij/lang/annotations/JdkConstants$TreeSelectionMode", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/jetbrains/annotations/Async$Schedule.class": {"ver": 52, "acc": 9729, "nme": "org/jetbrains/annotations/Async$Schedule", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "CONSTRUCTOR"], ["L<PERSON>va/lang/annotation/ElementType;", "PARAMETER"]]]}]}, "org/jetbrains/annotations/Debug.class": {"ver": 52, "acc": 49, "nme": "org/jetbrains/annotations/Debug", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}], "flds": []}, "org/jetbrains/annotations/NotNullByDefault.class": {"ver": 52, "acc": 9729, "nme": "org/jetbrains/annotations/NotNullByDefault", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "PACKAGE"]]]}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/ApiStatus$Experimental;"}]}, "org/jetbrains/annotations/CheckReturnValue.class": {"ver": 52, "acc": 9729, "nme": "org/jetbrains/annotations/CheckReturnValue", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "CONSTRUCTOR"], ["L<PERSON>va/lang/annotation/ElementType;", "TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "PACKAGE"]]]}]}, "org/jetbrains/annotations/VisibleForTesting.class": {"ver": 52, "acc": 9729, "nme": "org/jetbrains/annotations/VisibleForTesting", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "CONSTRUCTOR"], ["L<PERSON>va/lang/annotation/ElementType;", "FIELD"], ["L<PERSON>va/lang/annotation/ElementType;", "TYPE"]]]}]}, "org/intellij/lang/annotations/JdkConstants$FontStyle.class": {"ver": 52, "acc": 9729, "nme": "org/intellij/lang/annotations/JdkConstants$FontStyle", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/jetbrains/annotations/BlockingExecutor.class": {"ver": 52, "acc": 9729, "nme": "org/jetbrains/annotations/BlockingExecutor", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "TYPE_USE"]]]}]}, "org/jetbrains/annotations/NonNls.class": {"ver": 52, "acc": 9729, "nme": "org/jetbrains/annotations/NonNls", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "FIELD"], ["L<PERSON>va/lang/annotation/ElementType;", "PARAMETER"], ["L<PERSON>va/lang/annotation/ElementType;", "LOCAL_VARIABLE"], ["L<PERSON>va/lang/annotation/ElementType;", "TYPE_USE"], ["L<PERSON>va/lang/annotation/ElementType;", "TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "PACKAGE"]]]}]}, "org/intellij/lang/annotations/Subst.class": {"ver": 52, "acc": 9729, "nme": "org/intellij/lang/annotations/Subst", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "FIELD"], ["L<PERSON>va/lang/annotation/ElementType;", "LOCAL_VARIABLE"], ["L<PERSON>va/lang/annotation/ElementType;", "PARAMETER"]]]}]}, "org/intellij/lang/annotations/Language.class": {"ver": 52, "acc": 9729, "nme": "org/intellij/lang/annotations/Language", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NonNls;"}]}, {"nme": "prefix", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NonNls;"}]}, {"nme": "suffix", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NonNls;"}]}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "FIELD"], ["L<PERSON>va/lang/annotation/ElementType;", "PARAMETER"], ["L<PERSON>va/lang/annotation/ElementType;", "LOCAL_VARIABLE"], ["L<PERSON>va/lang/annotation/ElementType;", "ANNOTATION_TYPE"]]]}]}, "org/intellij/lang/annotations/JdkConstants$HorizontalScrollBarPolicy.class": {"ver": 52, "acc": 9729, "nme": "org/intellij/lang/annotations/JdkConstants$HorizontalScrollBarPolicy", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/jetbrains/annotations/ApiStatus$Obsolete.class": {"ver": 52, "acc": 9729, "nme": "org/jetbrains/annotations/ApiStatus$Obsolete", "super": "java/lang/Object", "mthds": [{"nme": "since", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "ANNOTATION_TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "CONSTRUCTOR"], ["L<PERSON>va/lang/annotation/ElementType;", "FIELD"], ["L<PERSON>va/lang/annotation/ElementType;", "PACKAGE"]]]}]}, "org/jetbrains/annotations/PropertyKey.class": {"ver": 52, "acc": 9729, "nme": "org/jetbrains/annotations/PropertyKey", "super": "java/lang/Object", "mthds": [{"nme": "resourceBundle", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NonNls;"}]}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "PARAMETER"], ["L<PERSON>va/lang/annotation/ElementType;", "LOCAL_VARIABLE"], ["L<PERSON>va/lang/annotation/ElementType;", "FIELD"], ["L<PERSON>va/lang/annotation/ElementType;", "TYPE_USE"]]]}]}, "org/intellij/lang/annotations/JdkConstants$InputEventMask.class": {"ver": 52, "acc": 9729, "nme": "org/intellij/lang/annotations/JdkConstants$InputEventMask", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/intellij/lang/annotations/JdkConstants$BoxLayoutAxis.class": {"ver": 52, "acc": 9729, "nme": "org/intellij/lang/annotations/JdkConstants$BoxLayoutAxis", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/intellij/lang/annotations/JdkConstants$TabLayoutPolicy.class": {"ver": 52, "acc": 9729, "nme": "org/intellij/lang/annotations/JdkConstants$TabLayoutPolicy", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/intellij/lang/annotations/JdkConstants$PatternFlags.class": {"ver": 52, "acc": 9729, "nme": "org/intellij/lang/annotations/JdkConstants$PatternFlags", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/jetbrains/annotations/Range.class": {"ver": 52, "acc": 9729, "nme": "org/jetbrains/annotations/Range", "super": "java/lang/Object", "mthds": [{"nme": "from", "acc": 1025, "dsc": "()J"}, {"nme": "to", "acc": 1025, "dsc": "()J"}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "TYPE_USE"]]]}]}, "org/jetbrains/annotations/Debug$Renderer.class": {"ver": 52, "acc": 9729, "nme": "org/jetbrains/annotations/Debug$Renderer", "super": "java/lang/Object", "mthds": [{"nme": "text", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/intellij/lang/annotations/Language;", "vals": ["value", "JAVA", "prefix", "class Renderer{String $text(){return ", "suffix", ";}}"]}, {"dsc": "Lorg/jetbrains/annotations/NonNls;"}]}, {"nme": "childrenA<PERSON>y", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/intellij/lang/annotations/Language;", "vals": ["value", "JAVA", "prefix", "class Renderer{Object[] $childrenArray(){return ", "suffix", ";}}"]}, {"dsc": "Lorg/jetbrains/annotations/NonNls;"}]}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/intellij/lang/annotations/Language;", "vals": ["value", "JAVA", "prefix", "class Renderer{boolean $hasChildren(){return ", "suffix", ";}}"]}, {"dsc": "Lorg/jetbrains/annotations/NonNls;"}]}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "TYPE"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}]}, "org/intellij/lang/annotations/MagicConstant.class": {"ver": 52, "acc": 9729, "nme": "org/intellij/lang/annotations/MagicConstant", "super": "java/lang/Object", "mthds": [{"nme": "intValues", "acc": 1025, "dsc": "()[J"}, {"nme": "stringValues", "acc": 1025, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NonNls;"}]}, {"nme": "flags", "acc": 1025, "dsc": "()[J"}, {"nme": "valuesFromClass", "acc": 1025, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<*>;"}, {"nme": "flagsFromClass", "acc": 1025, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<*>;"}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "SOURCE"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "FIELD"], ["L<PERSON>va/lang/annotation/ElementType;", "PARAMETER"], ["L<PERSON>va/lang/annotation/ElementType;", "LOCAL_VARIABLE"], ["L<PERSON>va/lang/annotation/ElementType;", "ANNOTATION_TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "METHOD"]]]}]}, "org/jetbrains/annotations/ApiStatus$NonExtendable.class": {"ver": 52, "acc": 9729, "nme": "org/jetbrains/annotations/ApiStatus$NonExtendable", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "METHOD"]]]}]}, "org/jetbrains/annotations/ApiStatus.class": {"ver": 52, "acc": 49, "nme": "org/jetbrains/annotations/ApiStatus", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}], "flds": []}, "org/jetbrains/annotations/Contract.class": {"ver": 52, "acc": 9729, "nme": "org/jetbrains/annotations/Contract", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NonNls;"}]}, {"nme": "pure", "acc": 1025, "dsc": "()Z"}, {"nme": "mutates", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NonNls;"}]}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "CONSTRUCTOR"]]]}]}, "org/jetbrains/annotations/NonBlocking.class": {"ver": 52, "acc": 9729, "nme": "org/jetbrains/annotations/NonBlocking", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "CONSTRUCTOR"], ["L<PERSON>va/lang/annotation/ElementType;", "TYPE"]]]}]}, "org/jetbrains/annotations/ApiStatus$Experimental.class": {"ver": 52, "acc": 9729, "nme": "org/jetbrains/annotations/ApiStatus$Experimental", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "ANNOTATION_TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "CONSTRUCTOR"], ["L<PERSON>va/lang/annotation/ElementType;", "FIELD"], ["L<PERSON>va/lang/annotation/ElementType;", "PACKAGE"]]]}]}, "org/jetbrains/annotations/Nls.class": {"ver": 52, "acc": 9729, "nme": "org/jetbrains/annotations/Nls", "super": "java/lang/Object", "mthds": [{"nme": "capitalization", "acc": 1025, "dsc": "()Lorg/jetbrains/annotations/Nls$Capitalization;"}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "FIELD"], ["L<PERSON>va/lang/annotation/ElementType;", "PARAMETER"], ["L<PERSON>va/lang/annotation/ElementType;", "LOCAL_VARIABLE"], ["L<PERSON>va/lang/annotation/ElementType;", "TYPE_USE"], ["L<PERSON>va/lang/annotation/ElementType;", "TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "PACKAGE"]]]}]}, "org/jetbrains/annotations/NotNull.class": {"ver": 52, "acc": 9729, "nme": "org/jetbrains/annotations/NotNull", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "exception", "acc": 1025, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<+Ljava/lang/Exception;>;"}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "FIELD"], ["L<PERSON>va/lang/annotation/ElementType;", "PARAMETER"], ["L<PERSON>va/lang/annotation/ElementType;", "LOCAL_VARIABLE"], ["L<PERSON>va/lang/annotation/ElementType;", "TYPE_USE"]]]}]}, "org/jetbrains/annotations/ApiStatus$Internal.class": {"ver": 52, "acc": 9729, "nme": "org/jetbrains/annotations/ApiStatus$Internal", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "ANNOTATION_TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "CONSTRUCTOR"], ["L<PERSON>va/lang/annotation/ElementType;", "FIELD"], ["L<PERSON>va/lang/annotation/ElementType;", "PACKAGE"]]]}]}, "org/jetbrains/annotations/ApiStatus$AvailableSince.class": {"ver": 52, "acc": 9729, "nme": "org/jetbrains/annotations/ApiStatus$AvailableSince", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "ANNOTATION_TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "CONSTRUCTOR"], ["L<PERSON>va/lang/annotation/ElementType;", "FIELD"], ["L<PERSON>va/lang/annotation/ElementType;", "PACKAGE"]]]}]}, "org/jetbrains/annotations/ApiStatus$OverrideOnly.class": {"ver": 52, "acc": 9729, "nme": "org/jetbrains/annotations/ApiStatus$OverrideOnly", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "METHOD"]]]}]}, "org/jetbrains/annotations/Nullable.class": {"ver": 52, "acc": 9729, "nme": "org/jetbrains/annotations/Nullable", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NonNls;"}]}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "FIELD"], ["L<PERSON>va/lang/annotation/ElementType;", "PARAMETER"], ["L<PERSON>va/lang/annotation/ElementType;", "LOCAL_VARIABLE"], ["L<PERSON>va/lang/annotation/ElementType;", "TYPE_USE"]]]}]}, "org/jetbrains/annotations/Async$Execute.class": {"ver": 52, "acc": 9729, "nme": "org/jetbrains/annotations/Async$Execute", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "CONSTRUCTOR"], ["L<PERSON>va/lang/annotation/ElementType;", "PARAMETER"]]]}]}, "org/intellij/lang/annotations/JdkConstants$VerticalScrollBarPolicy.class": {"ver": 52, "acc": 9729, "nme": "org/intellij/lang/annotations/JdkConstants$VerticalScrollBarPolicy", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/intellij/lang/annotations/PrintFormat.class": {"ver": 52, "acc": 9729, "nme": "org/intellij/lang/annotations/PrintFormat", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}], "invanns": [{"dsc": "Lorg/intellij/lang/annotations/Pattern;", "vals": ["value", "(?:[^%]|%%|(?:%(?:\\d+\\$)?(?:[-#+ 0,(<]*)?(?:\\d+)?(?:\\.\\d+)?(?:[tT])?(?:[a-zA-Z%])))*"]}]}, "org/jetbrains/annotations/UnknownNullability.class": {"ver": 52, "acc": 9729, "nme": "org/jetbrains/annotations/UnknownNullability", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NonNls;"}]}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "TYPE_USE"]]]}]}, "org/intellij/lang/annotations/PrintFormatPattern.class": {"ver": 52, "acc": 32, "nme": "org/intellij/lang/annotations/PrintFormatPattern", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "ARG_INDEX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "(?:\\d+\\$)?"}, {"acc": 26, "nme": "FLAGS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "(?:[-#+ 0,(<]*)?"}, {"acc": 26, "nme": "WIDTH", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "(?:\\d+)?"}, {"acc": 26, "nme": "PRECISION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "(?:\\.\\d+)?"}, {"acc": 26, "nme": "CONVERSION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "(?:[tT])?(?:[a-zA-Z%])"}, {"acc": 26, "nme": "TEXT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "[^%]|%%"}, {"acc": 24, "nme": "PRINT_FORMAT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "(?:[^%]|%%|(?:%(?:\\d+\\$)?(?:[-#+ 0,(<]*)?(?:\\d+)?(?:\\.\\d+)?(?:[tT])?(?:[a-zA-Z%])))*"}]}, "org/intellij/lang/annotations/JdkConstants$HorizontalAlignment.class": {"ver": 52, "acc": 9729, "nme": "org/intellij/lang/annotations/JdkConstants$HorizontalAlignment", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/intellij/lang/annotations/JdkConstants$AdjustableOrientation.class": {"ver": 52, "acc": 9729, "nme": "org/intellij/lang/annotations/JdkConstants$AdjustableOrientation", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/intellij/lang/annotations/JdkConstants$TitledBorderTitlePosition.class": {"ver": 52, "acc": 9729, "nme": "org/intellij/lang/annotations/JdkConstants$TitledBorderTitlePosition", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/intellij/lang/annotations/RegExp.class": {"ver": 52, "acc": 9729, "nme": "org/intellij/lang/annotations/RegExp", "super": "java/lang/Object", "mthds": [{"nme": "prefix", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NonNls;"}]}, {"nme": "suffix", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NonNls;"}]}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "FIELD"], ["L<PERSON>va/lang/annotation/ElementType;", "PARAMETER"], ["L<PERSON>va/lang/annotation/ElementType;", "LOCAL_VARIABLE"], ["L<PERSON>va/lang/annotation/ElementType;", "ANNOTATION_TYPE"]]]}], "invanns": [{"dsc": "Lorg/intellij/lang/annotations/Language;", "vals": ["value", "RegExp"]}]}, "org/jetbrains/annotations/Blocking.class": {"ver": 52, "acc": 9729, "nme": "org/jetbrains/annotations/Blocking", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "CONSTRUCTOR"], ["L<PERSON>va/lang/annotation/ElementType;", "TYPE"]]]}]}, "org/jetbrains/annotations/NonBlockingExecutor.class": {"ver": 52, "acc": 9729, "nme": "org/jetbrains/annotations/NonBlockingExecutor", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "TYPE_USE"]]]}]}, "org/intellij/lang/annotations/Flow.class": {"ver": 52, "acc": 9729, "nme": "org/intellij/lang/annotations/Flow", "super": "java/lang/Object", "mthds": [{"nme": "source", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "sourceIsContainer", "acc": 1025, "dsc": "()Z"}, {"nme": "target", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "targetIsContainer", "acc": 1025, "dsc": "()Z"}], "flds": [{"acc": 25, "nme": "DEFAULT_SOURCE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "The method argument (if parameter was annotated) or this container (if instance method was annotated)"}, {"acc": 25, "nme": "THIS_SOURCE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "this"}, {"acc": 25, "nme": "DEFAULT_TARGET", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "This container (if the parameter was annotated) or the return value (if instance method was annotated)"}, {"acc": 25, "nme": "RETURN_METHOD_TARGET", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "The return value of this method"}, {"acc": 25, "nme": "THIS_TARGET", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "this"}], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "PARAMETER"], ["L<PERSON>va/lang/annotation/ElementType;", "METHOD"]]]}]}, "org/intellij/lang/annotations/JdkConstants$TabPlacement.class": {"ver": 52, "acc": 9729, "nme": "org/intellij/lang/annotations/JdkConstants$TabPlacement", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/jetbrains/annotations/ApiStatus$ScheduledForRemoval.class": {"ver": 52, "acc": 9729, "nme": "org/jetbrains/annotations/ApiStatus$ScheduledForRemoval", "super": "java/lang/Object", "mthds": [{"nme": "inVersion", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "ANNOTATION_TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "CONSTRUCTOR"], ["L<PERSON>va/lang/annotation/ElementType;", "FIELD"], ["L<PERSON>va/lang/annotation/ElementType;", "PACKAGE"]]]}]}, "org/jetbrains/annotations/UnmodifiableView.class": {"ver": 52, "acc": 9729, "nme": "org/jetbrains/annotations/UnmodifiableView", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "TYPE_USE"]]]}]}, "org/jetbrains/annotations/Nls$Capitalization.class": {"ver": 52, "acc": 16433, "nme": "org/jetbrains/annotations/Nls$Capitalization", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lorg/jetbrains/annotations/Nls$Capitalization;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lorg/jetbrains/annotations/Nls$Capitalization;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "NotSpecified", "dsc": "Lorg/jetbrains/annotations/Nls$Capitalization;"}, {"acc": 16409, "nme": "Title", "dsc": "Lorg/jetbrains/annotations/Nls$Capitalization;"}, {"acc": 16409, "nme": "Sentence", "dsc": "Lorg/jetbrains/annotations/Nls$Capitalization;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lorg/jetbrains/annotations/Nls$Capitalization;"}]}, "META-INF/versions/9/module-info.class": {"ver": 53, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "org/intellij/lang/annotations/Identifier.class": {"ver": 52, "acc": 9729, "nme": "org/intellij/lang/annotations/Identifier", "super": "java/lang/Object", "mthds": [], "flds": [], "invanns": [{"dsc": "Lorg/intellij/lang/annotations/Pattern;", "vals": ["value", "\\p{javaJavaIdentifierStart}\\p{javaJavaIdentifierPart}*"]}]}, "org/jetbrains/annotations/Async.class": {"ver": 52, "acc": 49, "nme": "org/jetbrains/annotations/Async", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}], "flds": []}, "org/jetbrains/annotations/TestOnly.class": {"ver": 52, "acc": 9729, "nme": "org/jetbrains/annotations/TestOnly", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "CONSTRUCTOR"], ["L<PERSON>va/lang/annotation/ElementType;", "FIELD"], ["L<PERSON>va/lang/annotation/ElementType;", "TYPE"]]]}]}, "org/jetbrains/annotations/Unmodifiable.class": {"ver": 52, "acc": 9729, "nme": "org/jetbrains/annotations/Unmodifiable", "super": "java/lang/Object", "mthds": [], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "CLASS"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "TYPE_USE"]]]}]}, "org/intellij/lang/annotations/JdkConstants$CursorType.class": {"ver": 52, "acc": 9729, "nme": "org/intellij/lang/annotations/JdkConstants$CursorType", "super": "java/lang/Object", "mthds": [], "flds": []}, "org/intellij/lang/annotations/JdkConstants.class": {"ver": 52, "acc": 131121, "nme": "org/intellij/lang/annotations/JdkConstants", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}}}}