package shyrcs.extrastoragehook.discord.commands;

import net.dv8tion.jda.api.entities.Message;
import net.dv8tion.jda.api.entities.channel.middleman.MessageChannel;
import net.dv8tion.jda.api.events.interaction.command.SlashCommandInteractionEvent;
import net.dv8tion.jda.api.events.message.MessageReceivedEvent;
import shyrcs.extrastoragehook.application.Library;
import shyrcs.extrastoragehook.executor.DiscordExecutor;

import java.util.Objects;

/**
 * Discord command để hủy liên kết tài khoản
 */
public class CommandUnlink extends DiscordExecutor {
    
    public CommandUnlink() {
        super("unlink", Library.config.getCommand("unlink"));
    }
    
    @Override
    public void onSlashCommand(SlashCommandInteractionEvent event) {
        final String authorId = Objects.requireNonNull(event.getMember()).getId();
        
        event.deferReply().queue();
        
        try {
            // <PERSON><PERSON><PERSON> tra xem user đã link chưa
            if (!Library.storage.userConnected(authorId)) {
                event.getHook().editOriginal(Library.config.getMessage("account-not-linked")).queue();
                return;
            }
            
            // Lấy thông tin trước khi unlink
            String minecraftName = Library.database.getMinecraftName(authorId);
            if (minecraftName == null) {
                minecraftName = "Unknown";
            }
            
            // Unlink tài khoản
            boolean success = Library.database.removeConnection(authorId);
            
            // Cũng xóa khỏi in-memory storage để đồng bộ
            if (success) {
                Library.storage.disconnect(authorId);
            }
            
            if (success) {
                String response = String.format(
                    "✅ **Đã hủy liên kết thành công!**\n\n" +
                    "🎮 **Tài khoản Minecraft:** `%s`\n" +
                    "🔗 **Trạng thái:** Đã hủy liên kết",
                    minecraftName
                );
                
                event.getHook().editOriginal(response).queue();
            } else {
                event.getHook().editOriginal("❌ Có lỗi xảy ra khi hủy liên kết!").queue();
            }
            
        } catch (Exception e) {
            event.getHook().editOriginal("❌ Có lỗi xảy ra khi hủy liên kết!").queue();
        }
    }
    
    @Override
    public void onChatCommand(MessageReceivedEvent event) {
        final MessageChannel channel = event.getChannel();
        final Message message = event.getMessage();
        final String authorId = Objects.requireNonNull(event.getMember()).getId();
        
        try {
            // Kiểm tra xem user đã link chưa
            if (!Library.storage.userConnected(authorId)) {
                channel.sendMessage(Library.config.getMessage("account-not-linked"))
                    .setMessageReference(message).queue();
                return;
            }
            
            // Lấy thông tin trước khi unlink
            String minecraftName = Library.database.getMinecraftName(authorId);
            if (minecraftName == null) {
                minecraftName = "Unknown";
            }
            
            // Unlink tài khoản
            boolean success = Library.database.removeConnection(authorId);
            
            // Cũng xóa khỏi in-memory storage để đồng bộ
            if (success) {
                Library.storage.disconnect(authorId);
            }
            
            if (success) {
                String response = String.format(
                    "✅ **Đã hủy liên kết thành công!**\n\n" +
                    "🎮 **Tài khoản Minecraft:** `%s`\n" +
                    "🔗 **Trạng thái:** Đã hủy liên kết",
                    minecraftName
                );
                
                channel.sendMessage(response).setMessageReference(message).queue();
            } else {
                channel.sendMessage("❌ Có lỗi xảy ra khi hủy liên kết!")
                    .setMessageReference(message).queue();
            }
            
        } catch (Exception e) {
            channel.sendMessage("❌ Có lỗi xảy ra khi hủy liên kết!")
                .setMessageReference(message).queue();
        }
    }
}
