package shyrcs.extrastoragehook.discord;

import net.dv8tion.jda.api.events.interaction.command.SlashCommandInteractionEvent;
import net.dv8tion.jda.api.hooks.ListenerAdapter;
import shyrcs.extrastoragehook.application.Library;
import shyrcs.extrastoragehook.SbMagicHook;
import java.util.UUID;
import java.util.List;

/**
 * Listener cho slash commands
 */
public class SlashListener extends ListenerAdapter {



    @Override
    public void onSlashCommandInteraction(SlashCommandInteractionEvent event) {
        String commandName = event.getName();

        // Kiểm tra channel permissions
        if (!isChannelAllowed(event)) {
            event.reply(Library.config.getMessage("not-permitted"))
                .setEphemeral(true)
                .queue();
            return;
        }

        switch (commandName) {
            case "connect":
                handleConnectCommand(event);
                break;
            case "link":
                handleLinkCommand(event);
                break;
            case "unlink":
                handleUnlinkCommand(event);
                break;
            case "storage":
                handleStorageCommand(event);
                break;
            case "sell":
                handleSellCommand(event);
                break;
            case "help":
                handleHelpCommand(event);
                break;
            default:
                event.reply("❌ Unknown command: " + commandName)
                    .setEphemeral(true)
                    .queue();
                break;
        }
    }

    /**
     * Xử lý connect command
     */
    private void handleConnectCommand(SlashCommandInteractionEvent event) {
        event.deferReply(true).queue();

        try {
            String authorId = event.getUser().getId();
            String code = generateCode();

            // Lưu vào bridge
            Library.bridge.write(code, authorId);

            // Tạo response
            String response = String.format(
                "🔗 **Mã kết nối của bạn**\n ```%s```\n\n" +
                // "🎮 **Cách sử dụng:**\n" +
                "📝 Sử dụng lệnh sau trong Minecraft: \n`/sbmagichook hook %s`\n\n" +
                "⏰ **Thời gian hết hạn:** %d giây\n",
                // "⏰ **Thời gian hết hạn:** %d giây\n" +
                // "🔒 **Lưu ý:** Mã này chỉ sử dụng được một lần!",
                code, code, Library.config.getCodeTimeout()
            );

            // Gửi response
            event.getHook().editOriginal(response).queue(
                success -> SbMagicHook.info("Đã tạo mã kết nối " + code + " cho Discord user: " + authorId),
                error -> SbMagicHook.error("Lỗi khi gửi response: " + error.getMessage())
            );

        } catch (Exception e) {
            SbMagicHook.error("Lỗi trong handleConnectCommand: " + e.getMessage());
            event.getHook().editOriginal("❌ Có lỗi xảy ra khi tạo mã kết nối!").queue();
        }
    }

    /**
     * Xử lý unlink command
     */
    private void handleUnlinkCommand(SlashCommandInteractionEvent event) {
        event.deferReply(true).queue();

        try {
            String discordId = event.getUser().getId();

            // Kiểm tra xem user đã link chưa
            if (!isUserConnected(discordId)) {
                event.getHook().editOriginal(Library.config.getMessage("account-not-linked")).queue();
                return;
            }

            // Lấy thông tin và unlink tài khoản
            String minecraftName = Library.database.getMinecraftName(discordId);
            boolean success = Library.database.removeConnection(discordId);

            // Cũng xóa khỏi in-memory storage để đồng bộ
            if (success) {
                Library.storage.disconnect(discordId);
            }

            if (success) {
                String response = String.format(
                    "✅ **Đã hủy liên kết thành công!**\n\n" +
                    "🎮 **Tài khoản Minecraft:** `%s`\n" +
                    "🔗 **Trạng thái:** Đã hủy liên kết",
                    minecraftName != null ? minecraftName : "Unknown"
                );

                event.getHook().editOriginal(response).queue();
                SbMagicHook.info("User " + discordId + " đã hủy liên kết với " + minecraftName);
            } else {
                event.getHook().editOriginal("❌ Có lỗi xảy ra khi hủy liên kết!").queue();
            }

        } catch (Exception e) {
            SbMagicHook.error("Lỗi trong handleUnlinkCommand: " + e.getMessage());
            event.getHook().editOriginal("❌ Có lỗi xảy ra khi hủy liên kết!").queue();
        }
    }

    /**
     * Xử lý link command (alias cho connect)
     */
    private void handleLinkCommand(SlashCommandInteractionEvent event) {
        handleConnectCommand(event);
    }

    /**
     * Xử lý storage command
     */
    private void handleStorageCommand(SlashCommandInteractionEvent event) {
        event.deferReply(true).queue();

        try {
            String discordId = event.getUser().getId();

            // Kiểm tra xem user đã link chưa
            if (!isUserConnected(discordId)) {
                event.getHook().editOriginal(Library.config.getMessage("account-not-linked")).queue();
                return;
            }

            UUID minecraftUuid = getMinecraftUUID(discordId);

            // Sử dụng StorageDisplayManager để tạo display với pagination
            StorageDisplayManager.StorageDisplay display = StorageDisplayManager.createStorageDisplay(minecraftUuid, 1);

            if (display.getActionRows() != null && !display.getActionRows().isEmpty()) {
                event.getHook().editOriginalEmbeds(display.getEmbed())
                    .setComponents(display.getActionRows())
                    .queue();
            } else {
                event.getHook().editOriginalEmbeds(display.getEmbed()).queue();
            }

        } catch (Exception e) {
            SbMagicHook.error("Lỗi trong handleStorageCommand: " + e.getMessage());
            event.getHook().editOriginal("❌ Có lỗi xảy ra khi lấy thông tin kho!").queue();
        }
    }

    /**
     * Xử lý sell command
     */
    private void handleSellCommand(SlashCommandInteractionEvent event) {
        event.deferReply(true).queue();

        try {
            String discordId = event.getUser().getId();

            // Kiểm tra xem user đã link chưa
            if (!isUserConnected(discordId)) {
                event.getHook().editOriginal(Library.config.getMessage("account-not-linked")).queue();
                return;
            }

            // Lấy options từ slash command
            String itemName = event.getOption("item").getAsString().toLowerCase();
            Integer amount = event.getOption("amount") != null ? event.getOption("amount").getAsInt() : null;

            UUID minecraftUuid = getMinecraftUUID(discordId);

            if (itemName.equals("all")) {
                // Sell all items
                handleSellAll(event, minecraftUuid);
                return;
            }

            // Sell specific item - TODO: implement later
            String response = "🛒 **Tính năng sell**\n\n" +
                "� **Cách sử dụng:**\n" +
                "`/sell <item> [amount]`\n\n" +
                "📝 **Ví dụ:**\n" +
                "• `/sell diamond 64` - Bán 64 kim cương\n" +
                "• `/sell iron_ingot` - Bán tất cả iron ingot\n\n" +
                "⚠️ **Lưu ý:** Tính năng đang được phát triển...";

            event.getHook().editOriginal(response).queue();

        } catch (Exception e) {
            SbMagicHook.error("Lỗi trong handleSellCommand: " + e.getMessage());
            event.getHook().editOriginal("❌ Có lỗi xảy ra khi xử lý lệnh bán!").queue();
        }
    }

    /**
     * Xử lý help command
     */
    private void handleHelpCommand(SlashCommandInteractionEvent event) {
        event.deferReply(true).queue();

        try {
            String response = "📚 **Hướng dẫn sử dụng SbMagicHook**\n\n" +
                "🔗 **Liên kết tài khoản:**\n" +
                "• `/connect` - Tạo mã để liên kết Discord với Minecraft\n" +
                "• `/unlink` - Hủy liên kết tài khoản\n\n" +
                "📦 **Quản lý kho ExtraStorage:**\n" +
                "• `/storage` - Xem chi tiết items trong kho (có phân trang)\n" +
                "• `/sell <item> [số lượng]` - Bán items cụ thể\n" +
                "• `/sell all` - Bán tất cả items có thể bán\n\n" +
                "❓ **Hỗ trợ:**\n" +
                "• `/help` - Hiển thị hướng dẫn này\n\n" +
                "💡 **Lưu ý:** Bạn cần liên kết tài khoản trước khi sử dụng các lệnh kho!\n" +
                "🔄 **Tip:** Sử dụng các nút ◀️ ▶️ để chuyển trang trong `/storage`";

            event.getHook().editOriginal(response).queue();

        } catch (Exception e) {
            SbMagicHook.error("Lỗi trong handleHelpCommand: " + e.getMessage());
            event.getHook().editOriginal("❌ Có lỗi xảy ra khi hiển thị help!").queue();
        }
    }

    /**
     * Xử lý sell all items
     */
    private void handleSellAll(SlashCommandInteractionEvent event, UUID minecraftUuid) {
        try {
            if (Library.extraStorageHook == null) {
                event.getHook().editOriginal("❌ ExtraStorage chưa được kết nối!").queue();
                return;
            }

            // Kiểm tra xem có items không
            if (!Library.extraStorageHook.hasItems(minecraftUuid)) {
                event.getHook().editOriginal("❌ Kho của bạn đang trống!").queue();
                return;
            }

            String response = "🛒 **Bán tất cả items**\n\n" +
                "⚠️ **Tính năng đang được phát triển...**\n\n" +
                "💡 **Sẽ bán tất cả items có thể bán trong kho ExtraStorage**\n" +
                "📊 **Hiển thị tổng số tiền nhận được**\n" +
                "📝 **Chi tiết từng loại item đã bán**";

            event.getHook().editOriginal(response).queue();

        } catch (Exception e) {
            SbMagicHook.error("Lỗi trong handleSellAll: " + e.getMessage());
            event.getHook().editOriginal("❌ Có lỗi xảy ra khi bán items!").queue();
        }
    }

    /**
     * Kiểm tra xem user đã kết nối chưa (kiểm tra cả database và memory)
     */
    private boolean isUserConnected(String discordId) {
        // Kiểm tra database trước
        if (Library.database.isConnected(discordId)) {
            return true;
        }

        // Fallback: kiểm tra in-memory storage
        return Library.storage.userConnected(discordId);
    }

    /**
     * Lấy Minecraft UUID (ưu tiên database, fallback memory)
     */
    private UUID getMinecraftUUID(String discordId) {
        // Thử database trước
        UUID uuid = Library.database.getMinecraftUUID(discordId);
        if (uuid != null) {
            return uuid;
        }

        // Fallback: thử in-memory storage
        return Library.storage.getMinecraftUUID(discordId);
    }

    /**
     * Kiểm tra xem channel có được phép sử dụng bot không
     */
    private boolean isChannelAllowed(SlashCommandInteractionEvent event) {
        List<String> whitelistedChannels = Library.config.getWhitelistedChannels();

        // Nếu không có whitelist thì cho phép tất cả
        if (whitelistedChannels == null || whitelistedChannels.isEmpty()) {
            return true;
        }

        String channelId = event.getChannel().getId();
        return whitelistedChannels.contains(channelId);
    }

    /**
     * Tạo mã kết nối ngẫu nhiên
     */
    private String generateCode() {
        final char[] CHAR_MAP = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789".toCharArray();
        final int CODE_LENGTH = 6;

        java.util.Random random = new java.util.Random();
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < CODE_LENGTH; i++) {
            code.append(CHAR_MAP[random.nextInt(CHAR_MAP.length)]);
        }
        return code.toString();
    }
    
}
