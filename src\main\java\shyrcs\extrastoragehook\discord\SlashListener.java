package shyrcs.extrastoragehook.discord;

import net.dv8tion.jda.api.events.interaction.command.SlashCommandInteractionEvent;
import net.dv8tion.jda.api.hooks.ListenerAdapter;
import shyrcs.extrastoragehook.application.Library;
import shyrcs.extrastoragehook.SbMagicHook;
import java.util.UUID;
import java.util.List;

/**
 * Listener cho slash commands
 */
public class SlashListener extends ListenerAdapter {



    @Override
    public void onSlashCommandInteraction(SlashCommandInteractionEvent event) {
        String commandName = event.getName();

        // Kiể<PERSON> tra channel permissions
        if (!isChannelAllowed(event)) {
            event.reply(Library.config.getMessage("not-permitted"))
                .setEphemeral(true)
                .queue();
            return;
        }

        switch (commandName) {
            case "connect":
                handleConnectCommand(event);
                break;
            case "link":
                handleConnectCommand(event); // link is alias for connect
                break;
            case "unlink":
                handleUnlinkCommand(event);
                break;
            case "storage":
                handleStorageCommand(event);
                break;
            case "sell":
                handleSellCommand(event);
                break;
            case "help":
                // Delegate to CommandHelp
                if (Library.manager.hasCommands("help")) {
                    Library.manager.getCommand("help").onSlashCommand(event);
                } else {
                    event.reply("❌ Help command not available").setEphemeral(true).queue();
                }
                break;
            default:
                event.reply("❌ Unknown command: " + commandName)
                    .setEphemeral(true)
                    .queue();
                break;
        }
    }

    /**
     * Xử lý connect command
     */
    private void handleConnectCommand(SlashCommandInteractionEvent event) {
        // Delegate to CommandConnect
        if (Library.manager.hasCommands("connect")) {
            Library.manager.getCommand("connect").onSlashCommand(event);
        } else {
            event.reply("❌ Connect command not available").setEphemeral(true).queue();
        }
    }

    /**
     * Xử lý unlink command
     */
    private void handleUnlinkCommand(SlashCommandInteractionEvent event) {
        // Delegate to CommandUnlink
        if (Library.manager.hasCommands("unlink")) {
            Library.manager.getCommand("unlink").onSlashCommand(event);
        } else {
            event.reply("❌ Unlink command not available").setEphemeral(true).queue();
        }
    }



    /**
     * Xử lý storage command
     */
    private void handleStorageCommand(SlashCommandInteractionEvent event) {
        // Delegate to CommandStorage
        if (Library.manager.hasCommands("storage")) {
            Library.manager.getCommand("storage").onSlashCommand(event);
        } else {
            event.reply("❌ Storage command not available").setEphemeral(true).queue();
        }
    }

    /**
     * Xử lý sell command
     */
    private void handleSellCommand(SlashCommandInteractionEvent event) {
        // Delegate to CommandSell
        if (Library.manager.hasCommands("sell")) {
            Library.manager.getCommand("sell").onSlashCommand(event);
        } else {
            event.reply("❌ Sell command not available").setEphemeral(true).queue();
        }
    }





    /**
     * Kiểm tra xem user đã kết nối chưa (kiểm tra cả database và memory)
     */
    private boolean isUserConnected(String discordId) {
        // Kiểm tra database trước
        if (Library.database.isConnected(discordId)) {
            return true;
        }

        // Fallback: kiểm tra in-memory storage
        return Library.storage.userConnected(discordId);
    }

    /**
     * Lấy Minecraft UUID (ưu tiên database, fallback memory)
     */
    private UUID getMinecraftUUID(String discordId) {
        // Thử database trước
        UUID uuid = Library.database.getMinecraftUUID(discordId);
        if (uuid != null) {
            return uuid;
        }

        // Fallback: thử in-memory storage
        return Library.storage.getMinecraftUUID(discordId);
    }

    /**
     * Kiểm tra xem channel có được phép sử dụng bot không
     */
    private boolean isChannelAllowed(SlashCommandInteractionEvent event) {
        List<String> whitelistedChannels = Library.config.getWhitelistedChannels();

        // Nếu không có whitelist thì cho phép tất cả
        if (whitelistedChannels == null || whitelistedChannels.isEmpty()) {
            return true;
        }

        String channelId = event.getChannel().getId();
        return whitelistedChannels.contains(channelId);
    }

    /**
     * Tạo mã kết nối ngẫu nhiên
     */
    private String generateCode() {
        final char[] CHAR_MAP = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789".toCharArray();
        final int CODE_LENGTH = 6;

        java.util.Random random = new java.util.Random();
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < CODE_LENGTH; i++) {
            code.append(CHAR_MAP[random.nextInt(CHAR_MAP.length)]);
        }
        return code.toString();
    }
    
}
